{"ast": null, "code": "import * as React from 'react';\nexport default function getExtraFooter(prefixCls, mode, renderExtraFooter) {\n  if (!renderExtraFooter) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-footer-extra\")\n  }, renderExtraFooter(mode));\n}", "map": {"version": 3, "names": ["React", "getExtraFooter", "prefixCls", "mode", "renderExtraFooter", "createElement", "className", "concat"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-picker/es/utils/getExtraFooter.js"], "sourcesContent": ["import * as React from 'react';\nexport default function getExtraFooter(prefixCls, mode, renderExtraFooter) {\n  if (!renderExtraFooter) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-footer-extra\")\n  }, renderExtraFooter(mode));\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,cAAcA,CAACC,SAAS,EAAEC,IAAI,EAAEC,iBAAiB,EAAE;EACzE,IAAI,CAACA,iBAAiB,EAAE;IACtB,OAAO,IAAI;EACb;EACA,OAAO,aAAaJ,KAAK,CAACK,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACL,SAAS,EAAE,eAAe;EACjD,CAAC,EAAEE,iBAAiB,CAACD,IAAI,CAAC,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}