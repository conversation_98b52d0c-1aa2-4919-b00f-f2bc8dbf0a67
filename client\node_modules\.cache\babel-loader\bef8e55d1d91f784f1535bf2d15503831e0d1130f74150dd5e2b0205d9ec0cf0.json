{"ast": null, "code": "import { SEARCH_MARK } from \"../hooks/useSearchOptions\";\nexport var VALUE_SPLIT = '__RC_CASCADER_SPLIT__';\nexport var SHOW_PARENT = 'SHOW_PARENT';\nexport var SHOW_CHILD = 'SHOW_CHILD';\n\n/**\n * Will convert value to string, and join with `VALUE_SPLIT`\n */\nexport function toPathKey(value) {\n  return value.join(VALUE_SPLIT);\n}\n\n/**\n * Batch convert value to string, and join with `VALUE_SPLIT`\n */\nexport function toPathKeys(value) {\n  return value.map(toPathKey);\n}\nexport function toPathValueStr(pathKey) {\n  return pathKey.split(VALUE_SPLIT);\n}\nexport function fillFieldNames(fieldNames) {\n  var _ref = fieldNames || {},\n    label = _ref.label,\n    value = _ref.value,\n    children = _ref.children;\n  var val = value || 'value';\n  return {\n    label: label || 'label',\n    value: val,\n    key: val,\n    children: children || 'children'\n  };\n}\nexport function isLeaf(option, fieldNames) {\n  var _option$isLeaf, _option$fieldNames$ch;\n  return (_option$isLeaf = option.isLeaf) !== null && _option$isLeaf !== void 0 ? _option$isLeaf : !((_option$fieldNames$ch = option[fieldNames.children]) !== null && _option$fieldNames$ch !== void 0 && _option$fieldNames$ch.length);\n}\nexport function scrollIntoParentView(element) {\n  var parent = element.parentElement;\n  if (!parent) {\n    return;\n  }\n  var elementToParent = element.offsetTop - parent.offsetTop; // offsetParent may not be parent.\n  if (elementToParent - parent.scrollTop < 0) {\n    parent.scrollTo({\n      top: elementToParent\n    });\n  } else if (elementToParent + element.offsetHeight - parent.scrollTop > parent.offsetHeight) {\n    parent.scrollTo({\n      top: elementToParent + element.offsetHeight - parent.offsetHeight\n    });\n  }\n}\nexport function getFullPathKeys(options, fieldNames) {\n  return options.map(function (item) {\n    var _item$SEARCH_MARK;\n    return (_item$SEARCH_MARK = item[SEARCH_MARK]) === null || _item$SEARCH_MARK === void 0 ? void 0 : _item$SEARCH_MARK.map(function (opt) {\n      return opt[fieldNames.value];\n    });\n  });\n}", "map": {"version": 3, "names": ["SEARCH_MARK", "VALUE_SPLIT", "SHOW_PARENT", "SHOW_CHILD", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "join", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "map", "toPathValueStr", "path<PERSON><PERSON>", "split", "fillFieldNames", "fieldNames", "_ref", "label", "children", "val", "key", "<PERSON><PERSON><PERSON><PERSON>", "option", "_option$isLeaf", "_option$fieldNames$ch", "length", "scrollIntoParentView", "element", "parent", "parentElement", "elementToParent", "offsetTop", "scrollTop", "scrollTo", "top", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options", "item", "_item$SEARCH_MARK", "opt"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-cascader/es/utils/commonUtil.js"], "sourcesContent": ["import { SEARCH_MARK } from \"../hooks/useSearchOptions\";\nexport var VALUE_SPLIT = '__RC_CASCADER_SPLIT__';\nexport var SHOW_PARENT = 'SHOW_PARENT';\nexport var SHOW_CHILD = 'SHOW_CHILD';\n\n/**\n * Will convert value to string, and join with `VALUE_SPLIT`\n */\nexport function toPathKey(value) {\n  return value.join(VALUE_SPLIT);\n}\n\n/**\n * Batch convert value to string, and join with `VALUE_SPLIT`\n */\nexport function toPathKeys(value) {\n  return value.map(toPathKey);\n}\nexport function toPathValueStr(pathKey) {\n  return pathKey.split(VALUE_SPLIT);\n}\nexport function fillFieldNames(fieldNames) {\n  var _ref = fieldNames || {},\n    label = _ref.label,\n    value = _ref.value,\n    children = _ref.children;\n  var val = value || 'value';\n  return {\n    label: label || 'label',\n    value: val,\n    key: val,\n    children: children || 'children'\n  };\n}\nexport function isLeaf(option, fieldNames) {\n  var _option$isLeaf, _option$fieldNames$ch;\n  return (_option$isLeaf = option.isLeaf) !== null && _option$isLeaf !== void 0 ? _option$isLeaf : !((_option$fieldNames$ch = option[fieldNames.children]) !== null && _option$fieldNames$ch !== void 0 && _option$fieldNames$ch.length);\n}\nexport function scrollIntoParentView(element) {\n  var parent = element.parentElement;\n  if (!parent) {\n    return;\n  }\n  var elementToParent = element.offsetTop - parent.offsetTop; // offsetParent may not be parent.\n  if (elementToParent - parent.scrollTop < 0) {\n    parent.scrollTo({\n      top: elementToParent\n    });\n  } else if (elementToParent + element.offsetHeight - parent.scrollTop > parent.offsetHeight) {\n    parent.scrollTo({\n      top: elementToParent + element.offsetHeight - parent.offsetHeight\n    });\n  }\n}\nexport function getFullPathKeys(options, fieldNames) {\n  return options.map(function (item) {\n    var _item$SEARCH_MARK;\n    return (_item$SEARCH_MARK = item[SEARCH_MARK]) === null || _item$SEARCH_MARK === void 0 ? void 0 : _item$SEARCH_MARK.map(function (opt) {\n      return opt[fieldNames.value];\n    });\n  });\n}"], "mappings": "AAAA,SAASA,WAAW,QAAQ,2BAA2B;AACvD,OAAO,IAAIC,WAAW,GAAG,uBAAuB;AAChD,OAAO,IAAIC,WAAW,GAAG,aAAa;AACtC,OAAO,IAAIC,UAAU,GAAG,YAAY;;AAEpC;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,KAAK,EAAE;EAC/B,OAAOA,KAAK,CAACC,IAAI,CAACL,WAAW,CAAC;AAChC;;AAEA;AACA;AACA;AACA,OAAO,SAASM,UAAUA,CAACF,KAAK,EAAE;EAChC,OAAOA,KAAK,CAACG,GAAG,CAACJ,SAAS,CAAC;AAC7B;AACA,OAAO,SAASK,cAAcA,CAACC,OAAO,EAAE;EACtC,OAAOA,OAAO,CAACC,KAAK,CAACV,WAAW,CAAC;AACnC;AACA,OAAO,SAASW,cAAcA,CAACC,UAAU,EAAE;EACzC,IAAIC,IAAI,GAAGD,UAAU,IAAI,CAAC,CAAC;IACzBE,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBV,KAAK,GAAGS,IAAI,CAACT,KAAK;IAClBW,QAAQ,GAAGF,IAAI,CAACE,QAAQ;EAC1B,IAAIC,GAAG,GAAGZ,KAAK,IAAI,OAAO;EAC1B,OAAO;IACLU,KAAK,EAAEA,KAAK,IAAI,OAAO;IACvBV,KAAK,EAAEY,GAAG;IACVC,GAAG,EAAED,GAAG;IACRD,QAAQ,EAAEA,QAAQ,IAAI;EACxB,CAAC;AACH;AACA,OAAO,SAASG,MAAMA,CAACC,MAAM,EAAEP,UAAU,EAAE;EACzC,IAAIQ,cAAc,EAAEC,qBAAqB;EACzC,OAAO,CAACD,cAAc,GAAGD,MAAM,CAACD,MAAM,MAAM,IAAI,IAAIE,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAG,EAAE,CAACC,qBAAqB,GAAGF,MAAM,CAACP,UAAU,CAACG,QAAQ,CAAC,MAAM,IAAI,IAAIM,qBAAqB,KAAK,KAAK,CAAC,IAAIA,qBAAqB,CAACC,MAAM,CAAC;AACxO;AACA,OAAO,SAASC,oBAAoBA,CAACC,OAAO,EAAE;EAC5C,IAAIC,MAAM,GAAGD,OAAO,CAACE,aAAa;EAClC,IAAI,CAACD,MAAM,EAAE;IACX;EACF;EACA,IAAIE,eAAe,GAAGH,OAAO,CAACI,SAAS,GAAGH,MAAM,CAACG,SAAS,CAAC,CAAC;EAC5D,IAAID,eAAe,GAAGF,MAAM,CAACI,SAAS,GAAG,CAAC,EAAE;IAC1CJ,MAAM,CAACK,QAAQ,CAAC;MACdC,GAAG,EAAEJ;IACP,CAAC,CAAC;EACJ,CAAC,MAAM,IAAIA,eAAe,GAAGH,OAAO,CAACQ,YAAY,GAAGP,MAAM,CAACI,SAAS,GAAGJ,MAAM,CAACO,YAAY,EAAE;IAC1FP,MAAM,CAACK,QAAQ,CAAC;MACdC,GAAG,EAAEJ,eAAe,GAAGH,OAAO,CAACQ,YAAY,GAAGP,MAAM,CAACO;IACvD,CAAC,CAAC;EACJ;AACF;AACA,OAAO,SAASC,eAAeA,CAACC,OAAO,EAAEtB,UAAU,EAAE;EACnD,OAAOsB,OAAO,CAAC3B,GAAG,CAAC,UAAU4B,IAAI,EAAE;IACjC,IAAIC,iBAAiB;IACrB,OAAO,CAACA,iBAAiB,GAAGD,IAAI,CAACpC,WAAW,CAAC,MAAM,IAAI,IAAIqC,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAAC7B,GAAG,CAAC,UAAU8B,GAAG,EAAE;MACtI,OAAOA,GAAG,CAACzB,UAAU,CAACR,KAAK,CAAC;IAC9B,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}