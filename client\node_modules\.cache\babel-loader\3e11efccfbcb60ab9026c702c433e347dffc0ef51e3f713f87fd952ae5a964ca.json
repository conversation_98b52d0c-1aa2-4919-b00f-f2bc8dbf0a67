{"ast": null, "code": "import { lintWarning } from \"./utils\";\nvar linter = function linter(key, value, info) {\n  switch (key) {\n    case 'marginLeft':\n    case 'marginRight':\n    case 'paddingLeft':\n    case 'paddingRight':\n    case 'left':\n    case 'right':\n    case 'borderLeft':\n    case 'borderLeftWidth':\n    case 'borderLeftStyle':\n    case 'borderLeftColor':\n    case 'borderRight':\n    case 'borderRightWidth':\n    case 'borderRightStyle':\n    case 'borderRightColor':\n    case 'borderTopLeftRadius':\n    case 'borderTopRightRadius':\n    case 'borderBottomLeftRadius':\n    case 'borderBottomRightRadius':\n      lintWarning(\"You seem to be using non-logical property '\".concat(key, \"' which is not compatible with RTL mode. Please use logical properties and values instead. For more information: https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Logical_Properties.\"), info);\n      return;\n    case 'margin':\n    case 'padding':\n    case 'borderWidth':\n    case 'borderStyle':\n      // case 'borderColor':\n      if (typeof value === 'string') {\n        var valueArr = value.split(' ').map(function (item) {\n          return item.trim();\n        });\n        if (valueArr.length === 4 && valueArr[1] !== valueArr[3]) {\n          lintWarning(\"You seem to be using '\".concat(key, \"' property with different left \").concat(key, \" and right \").concat(key, \", which is not compatible with RTL mode. Please use logical properties and values instead. For more information: https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Logical_Properties.\"), info);\n        }\n      }\n      return;\n    case 'clear':\n    case 'textAlign':\n      if (value === 'left' || value === 'right') {\n        lintWarning(\"You seem to be using non-logical value '\".concat(value, \"' of \").concat(key, \", which is not compatible with RTL mode. Please use logical properties and values instead. For more information: https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Logical_Properties.\"), info);\n      }\n      return;\n    case 'borderRadius':\n      if (typeof value === 'string') {\n        var radiusGroups = value.split('/').map(function (item) {\n          return item.trim();\n        });\n        var invalid = radiusGroups.reduce(function (result, group) {\n          if (result) {\n            return result;\n          }\n          var radiusArr = group.split(' ').map(function (item) {\n            return item.trim();\n          });\n          // borderRadius: '2px 4px'\n          if (radiusArr.length >= 2 && radiusArr[0] !== radiusArr[1]) {\n            return true;\n          }\n          // borderRadius: '4px 4px 2px'\n          if (radiusArr.length === 3 && radiusArr[1] !== radiusArr[2]) {\n            return true;\n          }\n          // borderRadius: '4px 4px 2px 4px'\n          if (radiusArr.length === 4 && radiusArr[2] !== radiusArr[3]) {\n            return true;\n          }\n          return result;\n        }, false);\n        if (invalid) {\n          lintWarning(\"You seem to be using non-logical value '\".concat(value, \"' of \").concat(key, \", which is not compatible with RTL mode. Please use logical properties and values instead. For more information: https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Logical_Properties.\"), info);\n        }\n      }\n      return;\n    default:\n  }\n};\nexport default linter;", "map": {"version": 3, "names": ["lintWarning", "linter", "key", "value", "info", "concat", "valueArr", "split", "map", "item", "trim", "length", "radiusGroups", "invalid", "reduce", "result", "group", "radiusArr"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/@ant-design/cssinjs/es/linters/logicalPropertiesLinter.js"], "sourcesContent": ["import { lintWarning } from \"./utils\";\nvar linter = function linter(key, value, info) {\n  switch (key) {\n    case 'marginLeft':\n    case 'marginRight':\n    case 'paddingLeft':\n    case 'paddingRight':\n    case 'left':\n    case 'right':\n    case 'borderLeft':\n    case 'borderLeftWidth':\n    case 'borderLeftStyle':\n    case 'borderLeftColor':\n    case 'borderRight':\n    case 'borderRightWidth':\n    case 'borderRightStyle':\n    case 'borderRightColor':\n    case 'borderTopLeftRadius':\n    case 'borderTopRightRadius':\n    case 'borderBottomLeftRadius':\n    case 'borderBottomRightRadius':\n      lintWarning(\"You seem to be using non-logical property '\".concat(key, \"' which is not compatible with RTL mode. Please use logical properties and values instead. For more information: https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Logical_Properties.\"), info);\n      return;\n    case 'margin':\n    case 'padding':\n    case 'borderWidth':\n    case 'borderStyle':\n      // case 'borderColor':\n      if (typeof value === 'string') {\n        var valueArr = value.split(' ').map(function (item) {\n          return item.trim();\n        });\n        if (valueArr.length === 4 && valueArr[1] !== valueArr[3]) {\n          lintWarning(\"You seem to be using '\".concat(key, \"' property with different left \").concat(key, \" and right \").concat(key, \", which is not compatible with RTL mode. Please use logical properties and values instead. For more information: https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Logical_Properties.\"), info);\n        }\n      }\n      return;\n    case 'clear':\n    case 'textAlign':\n      if (value === 'left' || value === 'right') {\n        lintWarning(\"You seem to be using non-logical value '\".concat(value, \"' of \").concat(key, \", which is not compatible with RTL mode. Please use logical properties and values instead. For more information: https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Logical_Properties.\"), info);\n      }\n      return;\n    case 'borderRadius':\n      if (typeof value === 'string') {\n        var radiusGroups = value.split('/').map(function (item) {\n          return item.trim();\n        });\n        var invalid = radiusGroups.reduce(function (result, group) {\n          if (result) {\n            return result;\n          }\n          var radiusArr = group.split(' ').map(function (item) {\n            return item.trim();\n          });\n          // borderRadius: '2px 4px'\n          if (radiusArr.length >= 2 && radiusArr[0] !== radiusArr[1]) {\n            return true;\n          }\n          // borderRadius: '4px 4px 2px'\n          if (radiusArr.length === 3 && radiusArr[1] !== radiusArr[2]) {\n            return true;\n          }\n          // borderRadius: '4px 4px 2px 4px'\n          if (radiusArr.length === 4 && radiusArr[2] !== radiusArr[3]) {\n            return true;\n          }\n          return result;\n        }, false);\n        if (invalid) {\n          lintWarning(\"You seem to be using non-logical value '\".concat(value, \"' of \").concat(key, \", which is not compatible with RTL mode. Please use logical properties and values instead. For more information: https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Logical_Properties.\"), info);\n        }\n      }\n      return;\n    default:\n  }\n};\nexport default linter;"], "mappings": "AAAA,SAASA,WAAW,QAAQ,SAAS;AACrC,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,GAAG,EAAEC,KAAK,EAAEC,IAAI,EAAE;EAC7C,QAAQF,GAAG;IACT,KAAK,YAAY;IACjB,KAAK,aAAa;IAClB,KAAK,aAAa;IAClB,KAAK,cAAc;IACnB,KAAK,MAAM;IACX,KAAK,OAAO;IACZ,KAAK,YAAY;IACjB,KAAK,iBAAiB;IACtB,KAAK,iBAAiB;IACtB,KAAK,iBAAiB;IACtB,KAAK,aAAa;IAClB,KAAK,kBAAkB;IACvB,KAAK,kBAAkB;IACvB,KAAK,kBAAkB;IACvB,KAAK,qBAAqB;IAC1B,KAAK,sBAAsB;IAC3B,KAAK,wBAAwB;IAC7B,KAAK,yBAAyB;MAC5BF,WAAW,CAAC,6CAA6C,CAACK,MAAM,CAACH,GAAG,EAAE,2LAA2L,CAAC,EAAEE,IAAI,CAAC;MACzQ;IACF,KAAK,QAAQ;IACb,KAAK,SAAS;IACd,KAAK,aAAa;IAClB,KAAK,aAAa;MAChB;MACA,IAAI,OAAOD,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAIG,QAAQ,GAAGH,KAAK,CAACI,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,UAAUC,IAAI,EAAE;UAClD,OAAOA,IAAI,CAACC,IAAI,CAAC,CAAC;QACpB,CAAC,CAAC;QACF,IAAIJ,QAAQ,CAACK,MAAM,KAAK,CAAC,IAAIL,QAAQ,CAAC,CAAC,CAAC,KAAKA,QAAQ,CAAC,CAAC,CAAC,EAAE;UACxDN,WAAW,CAAC,wBAAwB,CAACK,MAAM,CAACH,GAAG,EAAE,iCAAiC,CAAC,CAACG,MAAM,CAACH,GAAG,EAAE,aAAa,CAAC,CAACG,MAAM,CAACH,GAAG,EAAE,2LAA2L,CAAC,EAAEE,IAAI,CAAC;QAChU;MACF;MACA;IACF,KAAK,OAAO;IACZ,KAAK,WAAW;MACd,IAAID,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,OAAO,EAAE;QACzCH,WAAW,CAAC,0CAA0C,CAACK,MAAM,CAACF,KAAK,EAAE,OAAO,CAAC,CAACE,MAAM,CAACH,GAAG,EAAE,2LAA2L,CAAC,EAAEE,IAAI,CAAC;MAC/R;MACA;IACF,KAAK,cAAc;MACjB,IAAI,OAAOD,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAIS,YAAY,GAAGT,KAAK,CAACI,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,UAAUC,IAAI,EAAE;UACtD,OAAOA,IAAI,CAACC,IAAI,CAAC,CAAC;QACpB,CAAC,CAAC;QACF,IAAIG,OAAO,GAAGD,YAAY,CAACE,MAAM,CAAC,UAAUC,MAAM,EAAEC,KAAK,EAAE;UACzD,IAAID,MAAM,EAAE;YACV,OAAOA,MAAM;UACf;UACA,IAAIE,SAAS,GAAGD,KAAK,CAACT,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,UAAUC,IAAI,EAAE;YACnD,OAAOA,IAAI,CAACC,IAAI,CAAC,CAAC;UACpB,CAAC,CAAC;UACF;UACA,IAAIO,SAAS,CAACN,MAAM,IAAI,CAAC,IAAIM,SAAS,CAAC,CAAC,CAAC,KAAKA,SAAS,CAAC,CAAC,CAAC,EAAE;YAC1D,OAAO,IAAI;UACb;UACA;UACA,IAAIA,SAAS,CAACN,MAAM,KAAK,CAAC,IAAIM,SAAS,CAAC,CAAC,CAAC,KAAKA,SAAS,CAAC,CAAC,CAAC,EAAE;YAC3D,OAAO,IAAI;UACb;UACA;UACA,IAAIA,SAAS,CAACN,MAAM,KAAK,CAAC,IAAIM,SAAS,CAAC,CAAC,CAAC,KAAKA,SAAS,CAAC,CAAC,CAAC,EAAE;YAC3D,OAAO,IAAI;UACb;UACA,OAAOF,MAAM;QACf,CAAC,EAAE,KAAK,CAAC;QACT,IAAIF,OAAO,EAAE;UACXb,WAAW,CAAC,0CAA0C,CAACK,MAAM,CAACF,KAAK,EAAE,OAAO,CAAC,CAACE,MAAM,CAACH,GAAG,EAAE,2LAA2L,CAAC,EAAEE,IAAI,CAAC;QAC/R;MACF;MACA;IACF;EACF;AACF,CAAC;AACD,eAAeH,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}