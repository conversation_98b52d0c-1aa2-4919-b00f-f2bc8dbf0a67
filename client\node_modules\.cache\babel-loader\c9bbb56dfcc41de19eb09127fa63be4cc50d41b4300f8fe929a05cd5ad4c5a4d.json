{"ast": null, "code": "import { getAnimatableNone } from '../../render/dom/value-types/animatable-none.mjs';\nimport { isAnimatable } from './is-animatable.mjs';\nimport { isNone } from './is-none.mjs';\nfunction getKeyframes(value, valueName, target, transition) {\n  const isTargetAnimatable = isAnimatable(valueName, target);\n  let keyframes;\n  if (Array.isArray(target)) {\n    keyframes = [...target];\n  } else {\n    keyframes = [null, target];\n  }\n  const defaultOrigin = transition.from !== undefined ? transition.from : value.get();\n  let animatableTemplateValue = undefined;\n  const noneKeyframeIndexes = [];\n  for (let i = 0; i < keyframes.length; i++) {\n    /**\n     * Fill null/wildcard keyframes\n     */\n    if (keyframes[i] === null) {\n      keyframes[i] = i === 0 ? defaultOrigin : keyframes[i - 1];\n    }\n    if (isNone(keyframes[i])) {\n      noneKeyframeIndexes.push(i);\n    }\n    // TODO: Clean this conditional, it works for now\n    if (typeof keyframes[i] === \"string\" && keyframes[i] !== \"none\" && keyframes[i] !== \"0\") {\n      animatableTemplateValue = keyframes[i];\n    }\n  }\n  if (isTargetAnimatable && noneKeyframeIndexes.length && animatableTemplateValue) {\n    for (let i = 0; i < noneKeyframeIndexes.length; i++) {\n      const index = noneKeyframeIndexes[i];\n      keyframes[index] = getAnimatableNone(valueName, animatableTemplateValue);\n    }\n  }\n  return keyframes;\n}\nexport { getKeyframes };", "map": {"version": 3, "names": ["getAnimatableNone", "isAnimatable", "isNone", "getKeyframes", "value", "valueName", "target", "transition", "isTargetAnimatable", "keyframes", "Array", "isArray", "defaultOrigin", "from", "undefined", "get", "animatableTemplateValue", "noneKeyframeIndexes", "i", "length", "push", "index"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/framer-motion/dist/es/animation/utils/keyframes.mjs"], "sourcesContent": ["import { getAnimatableNone } from '../../render/dom/value-types/animatable-none.mjs';\nimport { isAnimatable } from './is-animatable.mjs';\nimport { isNone } from './is-none.mjs';\n\nfunction getKeyframes(value, valueName, target, transition) {\n    const isTargetAnimatable = isAnimatable(valueName, target);\n    let keyframes;\n    if (Array.isArray(target)) {\n        keyframes = [...target];\n    }\n    else {\n        keyframes = [null, target];\n    }\n    const defaultOrigin = transition.from !== undefined ? transition.from : value.get();\n    let animatableTemplateValue = undefined;\n    const noneKeyframeIndexes = [];\n    for (let i = 0; i < keyframes.length; i++) {\n        /**\n         * Fill null/wildcard keyframes\n         */\n        if (keyframes[i] === null) {\n            keyframes[i] = i === 0 ? defaultOrigin : keyframes[i - 1];\n        }\n        if (isNone(keyframes[i])) {\n            noneKeyframeIndexes.push(i);\n        }\n        // TODO: Clean this conditional, it works for now\n        if (typeof keyframes[i] === \"string\" &&\n            keyframes[i] !== \"none\" &&\n            keyframes[i] !== \"0\") {\n            animatableTemplateValue = keyframes[i];\n        }\n    }\n    if (isTargetAnimatable &&\n        noneKeyframeIndexes.length &&\n        animatableTemplateValue) {\n        for (let i = 0; i < noneKeyframeIndexes.length; i++) {\n            const index = noneKeyframeIndexes[i];\n            keyframes[index] = getAnimatableNone(valueName, animatableTemplateValue);\n        }\n    }\n    return keyframes;\n}\n\nexport { getKeyframes };\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,kDAAkD;AACpF,SAASC,YAAY,QAAQ,qBAAqB;AAClD,SAASC,MAAM,QAAQ,eAAe;AAEtC,SAASC,YAAYA,CAACC,KAAK,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,EAAE;EACxD,MAAMC,kBAAkB,GAAGP,YAAY,CAACI,SAAS,EAAEC,MAAM,CAAC;EAC1D,IAAIG,SAAS;EACb,IAAIC,KAAK,CAACC,OAAO,CAACL,MAAM,CAAC,EAAE;IACvBG,SAAS,GAAG,CAAC,GAAGH,MAAM,CAAC;EAC3B,CAAC,MACI;IACDG,SAAS,GAAG,CAAC,IAAI,EAAEH,MAAM,CAAC;EAC9B;EACA,MAAMM,aAAa,GAAGL,UAAU,CAACM,IAAI,KAAKC,SAAS,GAAGP,UAAU,CAACM,IAAI,GAAGT,KAAK,CAACW,GAAG,CAAC,CAAC;EACnF,IAAIC,uBAAuB,GAAGF,SAAS;EACvC,MAAMG,mBAAmB,GAAG,EAAE;EAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,SAAS,CAACU,MAAM,EAAED,CAAC,EAAE,EAAE;IACvC;AACR;AACA;IACQ,IAAIT,SAAS,CAACS,CAAC,CAAC,KAAK,IAAI,EAAE;MACvBT,SAAS,CAACS,CAAC,CAAC,GAAGA,CAAC,KAAK,CAAC,GAAGN,aAAa,GAAGH,SAAS,CAACS,CAAC,GAAG,CAAC,CAAC;IAC7D;IACA,IAAIhB,MAAM,CAACO,SAAS,CAACS,CAAC,CAAC,CAAC,EAAE;MACtBD,mBAAmB,CAACG,IAAI,CAACF,CAAC,CAAC;IAC/B;IACA;IACA,IAAI,OAAOT,SAAS,CAACS,CAAC,CAAC,KAAK,QAAQ,IAChCT,SAAS,CAACS,CAAC,CAAC,KAAK,MAAM,IACvBT,SAAS,CAACS,CAAC,CAAC,KAAK,GAAG,EAAE;MACtBF,uBAAuB,GAAGP,SAAS,CAACS,CAAC,CAAC;IAC1C;EACJ;EACA,IAAIV,kBAAkB,IAClBS,mBAAmB,CAACE,MAAM,IAC1BH,uBAAuB,EAAE;IACzB,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,mBAAmB,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MACjD,MAAMG,KAAK,GAAGJ,mBAAmB,CAACC,CAAC,CAAC;MACpCT,SAAS,CAACY,KAAK,CAAC,GAAGrB,iBAAiB,CAACK,SAAS,EAAEW,uBAAuB,CAAC;IAC5E;EACJ;EACA,OAAOP,SAAS;AACpB;AAEA,SAASN,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}