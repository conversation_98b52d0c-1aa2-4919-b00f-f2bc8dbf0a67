{"ast": null, "code": "export var EXPAND_COLUMN = {};\nexport var INTERNAL_HOOKS = 'rc-table-internal-hook';", "map": {"version": 3, "names": ["EXPAND_COLUMN", "INTERNAL_HOOKS"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-table/es/constant.js"], "sourcesContent": ["export var EXPAND_COLUMN = {};\nexport var INTERNAL_HOOKS = 'rc-table-internal-hook';"], "mappings": "AAAA,OAAO,IAAIA,aAAa,GAAG,CAAC,CAAC;AAC7B,OAAO,IAAIC,cAAc,GAAG,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}