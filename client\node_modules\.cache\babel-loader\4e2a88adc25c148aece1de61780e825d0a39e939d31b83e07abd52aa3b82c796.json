{"ast": null, "code": "import { Keyframes } from '@ant-design/cssinjs';\nimport { initFadeMotion } from '../../style/motion';\nconst uploadAnimateInlineIn = new Keyframes('uploadAnimateInlineIn', {\n  from: {\n    width: 0,\n    height: 0,\n    margin: 0,\n    padding: 0,\n    opacity: 0\n  }\n});\nconst uploadAnimateInlineOut = new Keyframes('uploadAnimateInlineOut', {\n  to: {\n    width: 0,\n    height: 0,\n    margin: 0,\n    padding: 0,\n    opacity: 0\n  }\n});\n// =========================== Motion ===========================\nconst genMotionStyle = token => {\n  const {\n    componentCls\n  } = token;\n  const inlineCls = \"\".concat(componentCls, \"-animate-inline\");\n  return [{\n    [\"\".concat(componentCls, \"-wrapper\")]: {\n      [\"\".concat(inlineCls, \"-appear, \").concat(inlineCls, \"-enter, \").concat(inlineCls, \"-leave\")]: {\n        animationDuration: token.motionDurationSlow,\n        animationTimingFunction: token.motionEaseInOutCirc,\n        animationFillMode: 'forwards'\n      },\n      [\"\".concat(inlineCls, \"-appear, \").concat(inlineCls, \"-enter\")]: {\n        animationName: uploadAnimateInlineIn\n      },\n      [\"\".concat(inlineCls, \"-leave\")]: {\n        animationName: uploadAnimateInlineOut\n      }\n    }\n  }, {\n    [\"\".concat(componentCls, \"-wrapper\")]: initFadeMotion(token)\n  }, uploadAnimateInlineIn, uploadAnimateInlineOut];\n};\nexport default genMotionStyle;", "map": {"version": 3, "names": ["Keyframes", "initFadeMotion", "uploadAnimateInlineIn", "from", "width", "height", "margin", "padding", "opacity", "uploadAnimateInlineOut", "to", "genMotionStyle", "token", "componentCls", "inlineCls", "concat", "animationDuration", "motionDurationSlow", "animationTimingFunction", "motionEaseInOutCirc", "animationFillMode", "animationName"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/upload/style/motion.js"], "sourcesContent": ["import { Keyframes } from '@ant-design/cssinjs';\nimport { initFadeMotion } from '../../style/motion';\nconst uploadAnimateInlineIn = new Keyframes('uploadAnimateInlineIn', {\n  from: {\n    width: 0,\n    height: 0,\n    margin: 0,\n    padding: 0,\n    opacity: 0\n  }\n});\nconst uploadAnimateInlineOut = new Keyframes('uploadAnimateInlineOut', {\n  to: {\n    width: 0,\n    height: 0,\n    margin: 0,\n    padding: 0,\n    opacity: 0\n  }\n});\n// =========================== Motion ===========================\nconst genMotionStyle = token => {\n  const {\n    componentCls\n  } = token;\n  const inlineCls = `${componentCls}-animate-inline`;\n  return [{\n    [`${componentCls}-wrapper`]: {\n      [`${inlineCls}-appear, ${inlineCls}-enter, ${inlineCls}-leave`]: {\n        animationDuration: token.motionDurationSlow,\n        animationTimingFunction: token.motionEaseInOutCirc,\n        animationFillMode: 'forwards'\n      },\n      [`${inlineCls}-appear, ${inlineCls}-enter`]: {\n        animationName: uploadAnimateInlineIn\n      },\n      [`${inlineCls}-leave`]: {\n        animationName: uploadAnimateInlineOut\n      }\n    }\n  }, {\n    [`${componentCls}-wrapper`]: initFadeMotion(token)\n  }, uploadAnimateInlineIn, uploadAnimateInlineOut];\n};\nexport default genMotionStyle;"], "mappings": "AAAA,SAASA,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,cAAc,QAAQ,oBAAoB;AACnD,MAAMC,qBAAqB,GAAG,IAAIF,SAAS,CAAC,uBAAuB,EAAE;EACnEG,IAAI,EAAE;IACJC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE,CAAC;IACVC,OAAO,EAAE;EACX;AACF,CAAC,CAAC;AACF,MAAMC,sBAAsB,GAAG,IAAIT,SAAS,CAAC,wBAAwB,EAAE;EACrEU,EAAE,EAAE;IACFN,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE,CAAC;IACVC,OAAO,EAAE;EACX;AACF,CAAC,CAAC;AACF;AACA,MAAMG,cAAc,GAAGC,KAAK,IAAI;EAC9B,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,MAAME,SAAS,MAAAC,MAAA,CAAMF,YAAY,oBAAiB;EAClD,OAAO,CAAC;IACN,IAAAE,MAAA,CAAIF,YAAY,gBAAa;MAC3B,IAAAE,MAAA,CAAID,SAAS,eAAAC,MAAA,CAAYD,SAAS,cAAAC,MAAA,CAAWD,SAAS,cAAW;QAC/DE,iBAAiB,EAAEJ,KAAK,CAACK,kBAAkB;QAC3CC,uBAAuB,EAAEN,KAAK,CAACO,mBAAmB;QAClDC,iBAAiB,EAAE;MACrB,CAAC;MACD,IAAAL,MAAA,CAAID,SAAS,eAAAC,MAAA,CAAYD,SAAS,cAAW;QAC3CO,aAAa,EAAEnB;MACjB,CAAC;MACD,IAAAa,MAAA,CAAID,SAAS,cAAW;QACtBO,aAAa,EAAEZ;MACjB;IACF;EACF,CAAC,EAAE;IACD,IAAAM,MAAA,CAAIF,YAAY,gBAAaZ,cAAc,CAACW,KAAK;EACnD,CAAC,EAAEV,qBAAqB,EAAEO,sBAAsB,CAAC;AACnD,CAAC;AACD,eAAeE,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}