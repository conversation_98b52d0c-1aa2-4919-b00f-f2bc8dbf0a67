{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\n\n/**\n * Similar with `useLock`, but this hook will always execute last value.\n * When set to `true`, it will keep `true` for a short time even if `false` is set.\n */\nexport default function useDelayReset() {\n  var timeout = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 10;\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    bool = _React$useState2[0],\n    setBool = _React$useState2[1];\n  var delayRef = React.useRef(null);\n  var cancelLatest = function cancelLatest() {\n    window.clearTimeout(delayRef.current);\n  };\n  React.useEffect(function () {\n    return cancelLatest;\n  }, []);\n  var delaySetBool = function delaySetBool(value, callback) {\n    cancelLatest();\n    delayRef.current = window.setTimeout(function () {\n      setBool(value);\n      if (callback) {\n        callback();\n      }\n    }, timeout);\n  };\n  return [bool, delaySetBool, cancelLatest];\n}", "map": {"version": 3, "names": ["_slicedToArray", "React", "useDelayReset", "timeout", "arguments", "length", "undefined", "_React$useState", "useState", "_React$useState2", "bool", "setBool", "delayRef", "useRef", "cancelLatest", "window", "clearTimeout", "current", "useEffect", "delaySetBool", "value", "callback", "setTimeout"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-select/es/hooks/useDelayReset.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\n\n/**\n * Similar with `useLock`, but this hook will always execute last value.\n * When set to `true`, it will keep `true` for a short time even if `false` is set.\n */\nexport default function useDelayReset() {\n  var timeout = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 10;\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    bool = _React$useState2[0],\n    setBool = _React$useState2[1];\n  var delayRef = React.useRef(null);\n  var cancelLatest = function cancelLatest() {\n    window.clearTimeout(delayRef.current);\n  };\n  React.useEffect(function () {\n    return cancelLatest;\n  }, []);\n  var delaySetBool = function delaySetBool(value, callback) {\n    cancelLatest();\n    delayRef.current = window.setTimeout(function () {\n      setBool(value);\n      if (callback) {\n        callback();\n      }\n    }, timeout);\n  };\n  return [bool, delaySetBool, cancelLatest];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;;AAE9B;AACA;AACA;AACA;AACA,eAAe,SAASC,aAAaA,CAAA,EAAG;EACtC,IAAIC,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EACpF,IAAIG,eAAe,GAAGN,KAAK,CAACO,QAAQ,CAAC,KAAK,CAAC;IACzCC,gBAAgB,GAAGT,cAAc,CAACO,eAAe,EAAE,CAAC,CAAC;IACrDG,IAAI,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC1BE,OAAO,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC/B,IAAIG,QAAQ,GAAGX,KAAK,CAACY,MAAM,CAAC,IAAI,CAAC;EACjC,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IACzCC,MAAM,CAACC,YAAY,CAACJ,QAAQ,CAACK,OAAO,CAAC;EACvC,CAAC;EACDhB,KAAK,CAACiB,SAAS,CAAC,YAAY;IAC1B,OAAOJ,YAAY;EACrB,CAAC,EAAE,EAAE,CAAC;EACN,IAAIK,YAAY,GAAG,SAASA,YAAYA,CAACC,KAAK,EAAEC,QAAQ,EAAE;IACxDP,YAAY,CAAC,CAAC;IACdF,QAAQ,CAACK,OAAO,GAAGF,MAAM,CAACO,UAAU,CAAC,YAAY;MAC/CX,OAAO,CAACS,KAAK,CAAC;MACd,IAAIC,QAAQ,EAAE;QACZA,QAAQ,CAAC,CAAC;MACZ;IACF,CAAC,EAAElB,OAAO,CAAC;EACb,CAAC;EACD,OAAO,CAACO,IAAI,EAAES,YAAY,EAAEL,YAAY,CAAC;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}