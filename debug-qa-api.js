// Debug QA API
const http = require('http');

async function debugQAAPI() {
  console.log('🔍 DEBUGGING QA API');
  console.log('='.repeat(40));

  // Test data
  const testPDFContent = `
Question 1: What is the capital of Tanzania?
The capital of Tanzania is Dodoma.

Question 2: Calculate 2 + 2
2 + 2 = 4

Question 3: Explain photosynthesis
Photosynthesis is the process by which plants make food using sunlight.

Question 4: What is the largest lake in Tanzania?
Lake Victoria is the largest lake in Tanzania.
`;

  try {
    console.log('\n1️⃣ Testing QA Health Endpoint...');
    const healthResponse = await makeRequest('GET', '/api/qa/health');
    console.log('Health Response:', healthResponse);

    console.log('\n2️⃣ Testing Question Extraction...');
    const questionResponse = await makeRequest('POST', '/api/qa/question/3', {
      pdfContent: testPDFContent,
      language: 'english'
    });
    console.log('Question Response:', questionResponse);

    console.log('\n3️⃣ Testing All Questions...');
    const allQuestionsResponse = await makeRequest('POST', '/api/qa/questions/all', {
      pdfContent: testPDFContent
    });
    console.log('All Questions Response:', allQuestionsResponse);

  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  }
}

function makeRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 5000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve(parsed);
        } catch (e) {
          resolve(responseData);
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

debugQAAPI();
