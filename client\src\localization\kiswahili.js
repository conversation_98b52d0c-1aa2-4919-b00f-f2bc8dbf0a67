// Kiswahili translations for Primary Kiswahili Medium level
export const kiswahiliTranslations = {
  // Navigation and Common UI
  navigation: {
    home: "Nyumba<PERSON>",
    quizzes: "<PERSON><PERSON><PERSON>",
    videos: "Video",
    ranking: "<PERSON><PERSON> ya Ushindi",
    forum: "<PERSON><PERSON><PERSON><PERSON>",
    profile: "<PERSON><PERSON><PERSON>",
    logout: "Ondoka",
    settings: "Mipangilio"
  },

  // Authentication
  auth: {
    login: "Ingia",
    register: "<PERSON><PERSON><PERSON><PERSON>",
    username: "<PERSON><PERSON> la Mtumiaji",
    password: "<PERSON><PERSON><PERSON><PERSON>",
    firstName: "<PERSON><PERSON>wan<PERSON>",
    lastName: "<PERSON><PERSON>wish<PERSON>",
    school: "<PERSON><PERSON>",
    level: "<PERSON><PERSON><PERSON>",
    class: "Darasa",
    phoneNumber: "<PERSON><PERSON> ya Simu",
    email: "Barua Pepe"
  },

  // Quiz System
  quiz: {
    startQuiz: "Anza Mtihani",
    submitQuiz: "<PERSON><PERSON><PERSON>ihani",
    nextQuestion: "Swali Lijalo",
    previousQuestion: "<PERSON>wal<PERSON>",
    timeRemaining: "<PERSON><PERSON>",
    score: "<PERSON><PERSON>",
    correct: "<PERSON><PERSON><PERSON>",
    incorrect: "Makosa",
    wellDone: "Hongera!",
    tryAgain: "<PERSON><PERSON><PERSON> Ten<PERSON>",
    minutes: "<PERSON><PERSON><PERSON>",
    seconds: "Se<PERSON><PERSON>"
  },

  // Subjects (Primary Level in Kiswahili)
  subjects: {
    "Hisabati": "Hisabati",
    "Sayansi na Teknolojia": "Sayansi na Teknolojia",
    "Jiografia": "Jiografia",
    "Kiswahili": "Kiswahili",
    "Maarifa ya Jamii": "Maarifa ya Jamii",
    "Kiingereza": "Kiingereza",
    "Dini": "Dini",
    "Hesabu": "Hesabu",
    "Michezo na Sanaa": "Michezo na Sanaa",
    "Afya na Mazingira": "Afya na Mazingira",
    "Uraia na Maadili": "Uraia na Maadili",
    "Kifaransa": "Kifaransa",
    "Historia ya Tanzania": "Historia ya Tanzania"
  },

  // Classes
  classes: {
    "1": "Darasa la Kwanza",
    "2": "Darasa la Pili",
    "3": "Darasa la Tatu",
    "4": "Darasa la Nne",
    "5": "Darasa la Tano",
    "6": "Darasa la Sita",
    "7": "Darasa la Saba"
  },

  // Common Messages
  messages: {
    welcome: "Karibu",
    loading: "Inapakia...",
    error: "Hitilafu",
    success: "Mafanikio",
    save: "Hifadhi",
    cancel: "Ghairi",
    delete: "Futa",
    edit: "Hariri",
    view: "Ona",
    search: "Tafuta",
    filter: "Chuja",
    refresh: "Onyesha Upya"
  },

  // Study Materials
  studyMaterials: {
    videos: "Video za Masomo",
    documents: "Hati za Masomo",
    notes: "Maelezo",
    exercises: "Mazoezi",
    download: "Pakua",
    watch: "Tazama",
    read: "Soma"
  },

  // Profile and Settings
  profile: {
    myProfile: "Wasifu Wangu",
    editProfile: "Hariri Wasifu",
    changePassword: "Badilisha Nywila",
    statistics: "Takwimu",
    achievements: "Mafanikio",
    level: "Kiwango",
    xp: "Pointi za Uzoefu",
    streak: "Mfuatano"
  },

  // Time and Date
  time: {
    today: "Leo",
    yesterday: "Jana",
    tomorrow: "Kesho",
    week: "Wiki",
    month: "Mwezi",
    year: "Mwaka",
    morning: "Asubuhi",
    afternoon: "Mchana",
    evening: "Jioni",
    night: "Usiku"
  },

  // Numbers in Kiswahili
  numbers: {
    "1": "moja",
    "2": "mbili",
    "3": "tatu",
    "4": "nne",
    "5": "tano",
    "6": "sita",
    "7": "saba",
    "8": "nane",
    "9": "tisa",
    "10": "kumi"
  },

  // Educational Terms
  education: {
    student: "Mwanafunzi",
    teacher: "Mwalimu",
    lesson: "Somo",
    homework: "Kazi ya Nyumbani",
    exam: "Mtihani",
    grade: "Daraja",
    certificate: "Cheti",
    graduation: "Kuhitimu",
    knowledge: "Maarifa",
    learning: "Kujifunza"
  },

  // Motivational Messages
  motivation: {
    keepLearning: "Endelea Kujifunza!",
    greatJob: "Kazi Nzuri!",
    almostThere: "Karibu Kufika!",
    excellent: "Bora Sana!",
    goodLuck: "Bahati Njema!",
    believeInYourself: "Jiamini Mwenyewe!",
    neverGiveUp: "Usikate Tamaa!",
    practiceMore: "Zoeza Zaidi!"
  }
};

// Helper function to get translation
export const getKiswahiliTranslation = (key, fallback = key) => {
  const keys = key.split('.');
  let translation = kiswahiliTranslations;
  
  for (const k of keys) {
    if (translation && translation[k]) {
      translation = translation[k];
    } else {
      return fallback;
    }
  }
  
  return translation || fallback;
};

// Helper function to check if user is in Kiswahili mode
export const isKiswahiliMode = (userLevel) => {
  return userLevel === 'primary_kiswahili';
};

export default kiswahiliTranslations;
