// Kiswahili translations for Primary Kiswahili Medium level
export const kiswahiliTranslations = {
  // Navigation and Common UI
  navigation: {
    home: "Nyumba<PERSON>",
    quizzes: "<PERSON><PERSON><PERSON>",
    videos: "Video",
    ranking: "<PERSON><PERSON> ya Ushindi",
    forum: "<PERSON><PERSON><PERSON><PERSON>",
    profile: "<PERSON><PERSON><PERSON>",
    logout: "Ondoka",
    settings: "Mipangilio"
  },

  // Authentication
  auth: {
    login: "Ingia",
    register: "<PERSON><PERSON><PERSON><PERSON>",
    username: "<PERSON><PERSON> la Mtumiaji",
    password: "<PERSON><PERSON><PERSON><PERSON>",
    firstName: "<PERSON><PERSON>wan<PERSON>",
    lastName: "<PERSON><PERSON>wish<PERSON>",
    school: "<PERSON><PERSON>",
    level: "<PERSON><PERSON><PERSON>",
    class: "Darasa",
    phoneNumber: "<PERSON><PERSON> ya Simu",
    email: "Barua Pepe"
  },

  // Quiz System
  quiz: {
    startQuiz: "Anza Mtihani",
    submitQuiz: "<PERSON><PERSON><PERSON>ihani",
    nextQuestion: "Swali Lijalo",
    previousQuestion: "<PERSON>wal<PERSON>",
    timeRemaining: "<PERSON><PERSON>",
    score: "<PERSON><PERSON>",
    correct: "<PERSON><PERSON><PERSON>",
    incorrect: "Makosa",
    wellDone: "Hongera!",
    tryAgain: "<PERSON><PERSON><PERSON> Ten<PERSON>",
    minutes: "<PERSON><PERSON><PERSON>",
    seconds: "Se<PERSON><PERSON>"
  },

  // Subjects (Primary Level in Kiswahili)
  subjects: {
    "Hisabati": "Hisabati",
    "Sayansi na Teknolojia": "Sayansi na Teknolojia",
    "Jiografia": "Jiografia",
    "Kiswahili": "Kiswahili",
    "Maarifa ya Jamii": "Maarifa ya Jamii",
    "Kiingereza": "Kiingereza",
    "Dini": "Dini",
    "Hesabu": "Hesabu",
    "Michezo na Sanaa": "Michezo na Sanaa",
    "Afya na Mazingira": "Afya na Mazingira",
    "Uraia na Maadili": "Uraia na Maadili",
    "Kifaransa": "Kifaransa",
    "Historia ya Tanzania": "Historia ya Tanzania"
  },

  // Classes
  classes: {
    "1": "Darasa la Kwanza",
    "2": "Darasa la Pili",
    "3": "Darasa la Tatu",
    "4": "Darasa la Nne",
    "5": "Darasa la Tano",
    "6": "Darasa la Sita",
    "7": "Darasa la Saba"
  },

  // Common Messages
  messages: {
    welcome: "Karibu",
    loading: "Inapakia...",
    error: "Hitilafu",
    success: "Mafanikio",
    save: "Hifadhi",
    cancel: "Ghairi",
    delete: "Futa",
    edit: "Hariri",
    view: "Ona",
    search: "Tafuta",
    filter: "Chuja",
    refresh: "Onyesha Upya"
  },

  // Study Materials
  studyMaterials: {
    videos: "Video za Masomo",
    documents: "Hati za Masomo",
    notes: "Maelezo",
    exercises: "Mazoezi",
    download: "Pakua",
    watch: "Tazama",
    read: "Soma"
  },

  // Profile and Settings
  profile: {
    myProfile: "Wasifu Wangu",
    editProfile: "Hariri Wasifu",
    changePassword: "Badilisha Nywila",
    statistics: "Takwimu",
    achievements: "Mafanikio",
    level: "Kiwango",
    xp: "Pointi za Uzoefu",
    streak: "Mfuatano"
  },

  // Time and Date
  time: {
    today: "Leo",
    yesterday: "Jana",
    tomorrow: "Kesho",
    week: "Wiki",
    month: "Mwezi",
    year: "Mwaka",
    morning: "Asubuhi",
    afternoon: "Mchana",
    evening: "Jioni",
    night: "Usiku"
  },

  // Numbers in Kiswahili
  numbers: {
    "1": "moja",
    "2": "mbili",
    "3": "tatu",
    "4": "nne",
    "5": "tano",
    "6": "sita",
    "7": "saba",
    "8": "nane",
    "9": "tisa",
    "10": "kumi"
  },

  // Educational Terms
  education: {
    student: "Mwanafunzi",
    teacher: "Mwalimu",
    lesson: "Somo",
    homework: "Kazi ya Nyumbani",
    exam: "Mtihani",
    grade: "Daraja",
    certificate: "Cheti",
    graduation: "Kuhitimu",
    knowledge: "Maarifa",
    learning: "Kujifunza"
  },

  // Motivational Messages
  motivation: {
    keepLearning: "Endelea Kujifunza!",
    greatJob: "Kazi Nzuri!",
    almostThere: "Karibu Kufika!",
    excellent: "Bora Sana!",
    goodLuck: "Bahati Njema!",
    believeInYourself: "Jiamini Mwenyewe!",
    neverGiveUp: "Usikate Tamaa!",
    practiceMore: "Zoeza Zaidi!"
  },

  // Hub Page
  hub: {
    welcome: "Karibu",
    dashboard: "Dashibodi",
    studySmarter: "Jifunze Kwa Akili",
    yourProgress: "Maendeleo Yako",
    recentActivity: "Shughuli za Hivi Karibuni",
    quickActions: "Vitendo vya Haraka",
    startQuiz: "Anza Mtihani",
    watchVideos: "Tazama Video",
    readNotes: "Soma Maelezo",
    viewRanking: "Ona Orodha ya Ushindi",
    totalXP: "Jumla ya Pointi",
    currentStreak: "Mfuatano wa Sasa",
    quizzesCompleted: "Mitihani Iliyokamilika",
    studyTime: "Muda wa Kusoma"
  },

  // Quiz Pages
  quizPages: {
    availableQuizzes: "Mitihani Iliyopatikana",
    selectSubject: "Chagua Somo",
    selectClass: "Chagua Darasa",
    difficulty: "Kiwango cha Ugumu",
    easy: "Rahisi",
    medium: "Wastani",
    hard: "Ngumu",
    questionsCount: "Idadi ya Maswali",
    timeLimit: "Kikomo cha Muda",
    startNow: "Anza Sasa",
    question: "Swali",
    of: "kati ya",
    chooseAnswer: "Chagua Jibu",
    nextQuestion: "Swali Lijalo",
    previousQuestion: "Swali Lililopita",
    submitQuiz: "Wasilisha Mtihani",
    timeUp: "Muda Umeisha",
    results: "Matokeo",
    yourScore: "Alama Zako",
    correctAnswers: "Majibu Sahihi",
    wrongAnswers: "Majibu Makosa",
    timeTaken: "Muda Uliotumika",
    retakeQuiz: "Rudia Mtihani",
    viewExplanation: "Ona Maelezo",
    explanation: "Maelezo"
  },

  // Video Lessons
  videoLessons: {
    videoLessons: "Masomo ya Video",
    watchLesson: "Tazama Somo",
    lessonCompleted: "Somo Limekamilika",
    duration: "Muda",
    description: "Maelezo",
    relatedVideos: "Video Zinazohusiana",
    comments: "Maoni",
    addComment: "Ongeza Maoni",
    reply: "Jibu",
    likes: "Mapendekezo",
    views: "Miwani",
    shareVideo: "Shiriki Video",
    downloadVideo: "Pakua Video",
    fullscreen: "Skrini Nzima",
    playVideo: "Cheza Video",
    pauseVideo: "Simamisha Video"
  },

  // Study Materials
  studyMaterials: {
    studyMaterials: "Vifaa vya Kusoma",
    books: "Vitabu",
    notes: "Maelezo",
    pastPapers: "Karatasi za Zamani",
    documents: "Hati",
    downloadMaterial: "Pakua Kifaa",
    viewMaterial: "Ona Kifaa",
    searchMaterials: "Tafuta Vifaa",
    filterBySubject: "Chuja kwa Somo",
    filterByClass: "Chuja kwa Darasa",
    uploadDate: "Tarehe ya Kupakia",
    fileSize: "Ukubwa wa Faili",
    fileType: "Aina ya Faili",
    preview: "Onyesho la Awali"
  },

  // Ranking Page
  ranking: {
    leaderboard: "Orodha ya Viongozi",
    myRank: "Nafasi Yangu",
    topStudents: "Wanafunzi Bora",
    thisWeek: "Wiki Hii",
    thisMonth: "Mwezi Huu",
    allTime: "Wakati Wote",
    rank: "Nafasi",
    student: "Mwanafunzi",
    points: "Pointi",
    level: "Kiwango",
    badges: "Vibeti",
    achievements: "Mafanikio",
    viewProfile: "Ona Wasifu",
    challenge: "Changamoto",
    compete: "Shindana"
  },

  // Profile Page
  profilePage: {
    myProfile: "Wasifu Wangu",
    personalInfo: "Taarifa za Kibinafsi",
    academicInfo: "Taarifa za Kitaaluma",
    statistics: "Takwimu",
    achievements: "Mafanikio",
    settings: "Mipangilio",
    editProfile: "Hariri Wasifu",
    changePhoto: "Badilisha Picha",
    updateInfo: "Sasisha Taarifa",
    saveChanges: "Hifadhi Mabadiliko",
    accountSettings: "Mipangilio ya Akaunti",
    privacySettings: "Mipangilio ya Faragha",
    notificationSettings: "Mipangilio ya Arifa",
    languageSettings: "Mipangilio ya Lugha",
    deleteAccount: "Futa Akaunti"
  },

  // Forum
  forum: {
    discussionForum: "Jukwaa la Majadiliano",
    askQuestion: "Uliza Swali",
    recentDiscussions: "Majadiliano ya Hivi Karibuni",
    popularTopics: "Mada Maarufu",
    myQuestions: "Maswali Yangu",
    myAnswers: "Majibu Yangu",
    postQuestion: "Chapisha Swali",
    answerQuestion: "Jibu Swali",
    upvote: "Piga Kura Juu",
    downvote: "Piga Kura Chini",
    bestAnswer: "Jibu Bora",
    solved: "Imetatuliwa",
    unsolved: "Haijatuliwa",
    replies: "Majibu",
    lastActivity: "Shughuli ya Mwisho"
  },

  // Subscription
  subscription: {
    choosePlan: "Chagua Mpango",
    currentPlan: "Mpango wa Sasa",
    upgradePlan: "Boresha Mpango",
    basicPlan: "Mpango wa Msingi",
    premiumPlan: "Mpango wa Juu",
    proPlan: "Mpango wa Kitaalamu",
    features: "Vipengele",
    price: "Bei",
    duration: "Muda",
    payNow: "Lipa Sasa",
    paymentMethod: "Njia ya Malipo",
    mobileMoney: "Pesa za Simu",
    bankTransfer: "Uhamisho wa Benki",
    paymentSuccess: "Malipo Yamefanikiwa",
    paymentFailed: "Malipo Yameshindwa",
    subscriptionActive: "Uanachama Unaendelea",
    subscriptionExpired: "Uanachama Umeisha",
    renewSubscription: "Sasisha Uanachama"
  },

  // Brainwave AI
  brainwaveAI: {
    brainwaveAI: "Akili ya Brainwave",
    askBrainwave: "Uliza Brainwave",
    chatWithAI: "Ongea na AI",
    aiAssistant: "Msaidizi wa AI",
    typeMessage: "Andika Ujumbe",
    sendMessage: "Tuma Ujumbe",
    aiThinking: "AI Inafikiri...",
    aiResponse: "Jibu la AI",
    clearChat: "Futa Mazungumzo",
    chatHistory: "Historia ya Mazungumzo",
    helpfulTips: "Vidokezo vya Msaada",
    askAboutSubject: "Uliza kuhusu Somo",
    explainConcept: "Eleza Dhana",
    solveProblems: "Tatua Matatizo",
    studyGuidance: "Mwongozo wa Kusoma"
  },

  // Buttons and Actions
  buttons: {
    submit: "Wasilisha",
    cancel: "Ghairi",
    save: "Hifadhi",
    edit: "Hariri",
    delete: "Futa",
    update: "Sasisha",
    create: "Unda",
    add: "Ongeza",
    remove: "Ondoa",
    confirm: "Thibitisha",
    back: "Rudi Nyuma",
    next: "Mbele",
    previous: "Nyuma",
    finish: "Maliza",
    start: "Anza",
    stop: "Simama",
    pause: "Simamisha",
    play: "Cheza",
    download: "Pakua",
    upload: "Pakia",
    share: "Shiriki",
    copy: "Nakili",
    print: "Chapisha",
    close: "Funga",
    open: "Fungua",
    expand: "Panua",
    collapse: "Kunja"
  },

  // Status Messages
  status: {
    loading: "Inapakia...",
    saving: "Inahifadhi...",
    saved: "Imehifadhiwa",
    error: "Hitilafu",
    success: "Mafanikio",
    warning: "Onyo",
    info: "Taarifa",
    completed: "Imekamilika",
    pending: "Inasubiri",
    failed: "Imeshindwa",
    cancelled: "Imeghairiwa",
    processing: "Inachakatwa",
    uploading: "Inapakia",
    downloading: "Inapakua",
    connecting: "Inaunganisha",
    connected: "Imeunganishwa",
    disconnected: "Imekatishwa"
  },

  // Form Validation
  validation: {
    required: "Hii ni lazima",
    invalidEmail: "Barua pepe si sahihi",
    passwordTooShort: "Nywila ni fupi sana",
    passwordsDoNotMatch: "Nywila hazilingani",
    invalidPhoneNumber: "Nambari ya simu si sahihi",
    fieldTooLong: "Uga ni mrefu sana",
    fieldTooShort: "Uga ni mfupi sana",
    invalidFormat: "Muundo si sahihi",
    alreadyExists: "Tayari ipo",
    notFound: "Haijapatikana",
    accessDenied: "Ufikiaji Umekataliwa",
    sessionExpired: "Kipindi Kimeisha"
  }
};

// Helper function to get translation
export const getKiswahiliTranslation = (key, fallback = key) => {
  const keys = key.split('.');
  let translation = kiswahiliTranslations;
  
  for (const k of keys) {
    if (translation && translation[k]) {
      translation = translation[k];
    } else {
      return fallback;
    }
  }
  
  return translation || fallback;
};

// Helper function to check if user is in Kiswahili mode
export const isKiswahiliMode = (userLevel) => {
  return userLevel === 'primary_kiswahili';
};

export default kiswahiliTranslations;
