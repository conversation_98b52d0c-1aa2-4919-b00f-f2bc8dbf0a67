{"ast": null, "code": "import { useRef, useEffect } from 'react';\nimport raf from \"rc-util/es/raf\";\n\n/**\n * Always trigger latest once when call multiple time\n */\nexport default (function () {\n  var idRef = useRef(0);\n  var cleanUp = function cleanUp() {\n    raf.cancel(idRef.current);\n  };\n  useEffect(function () {\n    return cleanUp;\n  }, []);\n  return function (callback) {\n    cleanUp();\n    idRef.current = raf(function () {\n      callback();\n    });\n  };\n});", "map": {"version": 3, "names": ["useRef", "useEffect", "raf", "idRef", "cleanUp", "cancel", "current", "callback"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-input-number/es/hooks/useFrame.js"], "sourcesContent": ["import { useRef, useEffect } from 'react';\nimport raf from \"rc-util/es/raf\";\n\n/**\n * Always trigger latest once when call multiple time\n */\nexport default (function () {\n  var idRef = useRef(0);\n  var cleanUp = function cleanUp() {\n    raf.cancel(idRef.current);\n  };\n  useEffect(function () {\n    return cleanUp;\n  }, []);\n  return function (callback) {\n    cleanUp();\n    idRef.current = raf(function () {\n      callback();\n    });\n  };\n});"], "mappings": "AAAA,SAASA,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACzC,OAAOC,GAAG,MAAM,gBAAgB;;AAEhC;AACA;AACA;AACA,gBAAgB,YAAY;EAC1B,IAAIC,KAAK,GAAGH,MAAM,CAAC,CAAC,CAAC;EACrB,IAAII,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;IAC/BF,GAAG,CAACG,MAAM,CAACF,KAAK,CAACG,OAAO,CAAC;EAC3B,CAAC;EACDL,SAAS,CAAC,YAAY;IACpB,OAAOG,OAAO;EAChB,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,UAAUG,QAAQ,EAAE;IACzBH,OAAO,CAAC,CAAC;IACTD,KAAK,CAACG,OAAO,GAAGJ,GAAG,CAAC,YAAY;MAC9BK,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC;EACJ,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}