// Test Server Restart and QA Service
const http = require('http');

async function testServerRestart() {
  console.log('🔄 TESTING SERVER RESTART & QA SERVICE');
  console.log('='.repeat(50));

  try {
    // Test 1: Basic Server Health
    console.log('\n1️⃣ Testing Basic Server Health...');
    const serverHealthy = await testEndpoint('/', 5000);
    console.log(`   Server: ${serverHealthy ? '✅ Running' : '❌ Not responding'}`);

    // Test 2: API Health
    console.log('\n2️⃣ Testing API Health...');
    const apiHealthy = await testEndpoint('/api/health', 5000);
    console.log(`   API: ${apiHealthy ? '✅ Working' : '❌ Not responding'}`);

    // Test 3: QA Service Health
    console.log('\n3️⃣ Testing QA Service...');
    const qaHealthy = await testEndpoint('/api/qa/health', 5000);
    console.log(`   QA Service: ${qaHealthy ? '✅ Active' : '❌ Not responding'}`);

    console.log('\n' + '='.repeat(50));
    console.log('📋 SERVER RESTART STATUS');
    console.log('='.repeat(50));

    if (serverHealthy && apiHealthy && qaHealthy) {
      console.log('🎉 SERVER RESTART SUCCESSFUL!');
      console.log('');
      console.log('✅ ALL SERVICES OPERATIONAL:');
      console.log('   ✅ Main Server: Running on port 5000');
      console.log('   ✅ API Endpoints: Responding correctly');
      console.log('   ✅ QA Service: Active and ready');
      console.log('');
      console.log('🧪 READY FOR PDF QUESTION TESTING:');
      console.log('   • Go to: http://localhost:3000/user/study-material');
      console.log('   • Open any PDF with questions');
      console.log('   • Click "Ask AI about PDF"');
      console.log('   • Type just "4" (for question 4)');
      console.log('   • Should get immediate answer!');
      console.log('');
      console.log('🎯 QA SERVICE ENDPOINTS AVAILABLE:');
      console.log('   • POST /api/qa/question/:number');
      console.log('   • POST /api/qa/questions/all');
      console.log('   • POST /api/qa/extract-text');
      console.log('   • GET /api/qa/health');
      console.log('');
      console.log('🚀 SERVER-SIDE PDF QA IS NOW ACTIVE!');
    } else {
      console.log('⚠️ SOME ISSUES DETECTED:');
      if (!serverHealthy) console.log('   ❌ Server not responding');
      if (!apiHealthy) console.log('   ❌ API endpoints not working');
      if (!qaHealthy) console.log('   ❌ QA service not active');
      console.log('');
      console.log('🔧 TROUBLESHOOTING STEPS:');
      console.log('   1. Check server logs for errors');
      console.log('   2. Verify all dependencies installed');
      console.log('   3. Check OpenAI API key configuration');
      console.log('   4. Restart server manually if needed');
    }

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.log('\n🔧 Please check:');
    console.log('   • Server is running: node server.js');
    console.log('   • No syntax errors in new files');
    console.log('   • All dependencies installed');
  }
}

// Helper function to test endpoints
async function testEndpoint(path, port) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: port,
      path: path,
      method: 'GET',
      timeout: 3000
    };

    const req = http.request(options, (res) => {
      resolve(res.statusCode === 200);
    });

    req.on('error', () => resolve(false));
    req.on('timeout', () => resolve(false));
    req.end();
  });
}

// Run the test
testServerRestart();
