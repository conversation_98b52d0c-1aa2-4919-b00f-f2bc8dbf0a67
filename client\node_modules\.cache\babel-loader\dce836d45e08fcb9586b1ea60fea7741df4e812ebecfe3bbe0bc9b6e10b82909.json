{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport Portal from '@rc-component/portal';\nimport Dialog from \"./Dialog\";\n// fix issue #10656\n/*\n * getContainer remarks\n * Custom container should not be return, because in the Portal component, it will remove the\n * return container element here, if the custom container is the only child of it's component,\n * like issue #10656, It will has a conflict with removeChild method in react-dom.\n * So here should add a child (div element) to custom container.\n * */\n\nvar DialogWrap = function DialogWrap(props) {\n  var visible = props.visible,\n    getContainer = props.getContainer,\n    forceRender = props.forceRender,\n    _props$destroyOnClose = props.destroyOnClose,\n    destroyOnClose = _props$destroyOnClose === void 0 ? false : _props$destroyOnClose,\n    _afterClose = props.afterClose;\n  var _React$useState = React.useState(visible),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    animatedVisible = _React$useState2[0],\n    setAnimatedVisible = _React$useState2[1];\n  React.useEffect(function () {\n    if (visible) {\n      setAnimatedVisible(true);\n    }\n  }, [visible]);\n\n  // // 渲染在当前 dom 里；\n  // if (getContainer === false) {\n  //   return (\n  //     <Dialog\n  //       {...props}\n  //       getOpenCount={() => 2} // 不对 body 做任何操作。。\n  //     />\n  //   );\n  // }\n\n  // Destroy on close will remove wrapped div\n  if (!forceRender && destroyOnClose && !animatedVisible) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(Portal, {\n    open: visible || forceRender || animatedVisible,\n    autoDestroy: false,\n    getContainer: getContainer,\n    autoLock: visible || animatedVisible\n  }, /*#__PURE__*/React.createElement(Dialog, _extends({}, props, {\n    destroyOnClose: destroyOnClose,\n    afterClose: function afterClose() {\n      _afterClose === null || _afterClose === void 0 ? void 0 : _afterClose();\n      setAnimatedVisible(false);\n    }\n  })));\n};\nDialogWrap.displayName = 'Dialog';\nexport default DialogWrap;", "map": {"version": 3, "names": ["_extends", "_slicedToArray", "React", "Portal", "Dialog", "DialogWrap", "props", "visible", "getContainer", "forceRender", "_props$destroyOnClose", "destroyOnClose", "_afterClose", "afterClose", "_React$useState", "useState", "_React$useState2", "animatedVisible", "setAnimatedVisible", "useEffect", "createElement", "open", "autoDestroy", "autoLock", "displayName"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-dialog/es/DialogWrap.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport Portal from '@rc-component/portal';\nimport Dialog from \"./Dialog\";\n// fix issue #10656\n/*\n * getContainer remarks\n * Custom container should not be return, because in the Portal component, it will remove the\n * return container element here, if the custom container is the only child of it's component,\n * like issue #10656, It will has a conflict with removeChild method in react-dom.\n * So here should add a child (div element) to custom container.\n * */\n\nvar DialogWrap = function DialogWrap(props) {\n  var visible = props.visible,\n    getContainer = props.getContainer,\n    forceRender = props.forceRender,\n    _props$destroyOnClose = props.destroyOnClose,\n    destroyOnClose = _props$destroyOnClose === void 0 ? false : _props$destroyOnClose,\n    _afterClose = props.afterClose;\n  var _React$useState = React.useState(visible),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    animatedVisible = _React$useState2[0],\n    setAnimatedVisible = _React$useState2[1];\n  React.useEffect(function () {\n    if (visible) {\n      setAnimatedVisible(true);\n    }\n  }, [visible]);\n\n  // // 渲染在当前 dom 里；\n  // if (getContainer === false) {\n  //   return (\n  //     <Dialog\n  //       {...props}\n  //       getOpenCount={() => 2} // 不对 body 做任何操作。。\n  //     />\n  //   );\n  // }\n\n  // Destroy on close will remove wrapped div\n  if (!forceRender && destroyOnClose && !animatedVisible) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(Portal, {\n    open: visible || forceRender || animatedVisible,\n    autoDestroy: false,\n    getContainer: getContainer,\n    autoLock: visible || animatedVisible\n  }, /*#__PURE__*/React.createElement(Dialog, _extends({}, props, {\n    destroyOnClose: destroyOnClose,\n    afterClose: function afterClose() {\n      _afterClose === null || _afterClose === void 0 ? void 0 : _afterClose();\n      setAnimatedVisible(false);\n    }\n  })));\n};\nDialogWrap.displayName = 'Dialog';\nexport default DialogWrap;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,MAAM,MAAM,UAAU;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,KAAK,EAAE;EAC1C,IAAIC,OAAO,GAAGD,KAAK,CAACC,OAAO;IACzBC,YAAY,GAAGF,KAAK,CAACE,YAAY;IACjCC,WAAW,GAAGH,KAAK,CAACG,WAAW;IAC/BC,qBAAqB,GAAGJ,KAAK,CAACK,cAAc;IAC5CA,cAAc,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,qBAAqB;IACjFE,WAAW,GAAGN,KAAK,CAACO,UAAU;EAChC,IAAIC,eAAe,GAAGZ,KAAK,CAACa,QAAQ,CAACR,OAAO,CAAC;IAC3CS,gBAAgB,GAAGf,cAAc,CAACa,eAAe,EAAE,CAAC,CAAC;IACrDG,eAAe,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACrCE,kBAAkB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC1Cd,KAAK,CAACiB,SAAS,CAAC,YAAY;IAC1B,IAAIZ,OAAO,EAAE;MACXW,kBAAkB,CAAC,IAAI,CAAC;IAC1B;EACF,CAAC,EAAE,CAACX,OAAO,CAAC,CAAC;;EAEb;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA,IAAI,CAACE,WAAW,IAAIE,cAAc,IAAI,CAACM,eAAe,EAAE;IACtD,OAAO,IAAI;EACb;EACA,OAAO,aAAaf,KAAK,CAACkB,aAAa,CAACjB,MAAM,EAAE;IAC9CkB,IAAI,EAAEd,OAAO,IAAIE,WAAW,IAAIQ,eAAe;IAC/CK,WAAW,EAAE,KAAK;IAClBd,YAAY,EAAEA,YAAY;IAC1Be,QAAQ,EAAEhB,OAAO,IAAIU;EACvB,CAAC,EAAE,aAAaf,KAAK,CAACkB,aAAa,CAAChB,MAAM,EAAEJ,QAAQ,CAAC,CAAC,CAAC,EAAEM,KAAK,EAAE;IAC9DK,cAAc,EAAEA,cAAc;IAC9BE,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;MAChCD,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC,CAAC;MACvEM,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC,CAAC,CAAC,CAAC;AACN,CAAC;AACDb,UAAU,CAACmB,WAAW,GAAG,QAAQ;AACjC,eAAenB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}