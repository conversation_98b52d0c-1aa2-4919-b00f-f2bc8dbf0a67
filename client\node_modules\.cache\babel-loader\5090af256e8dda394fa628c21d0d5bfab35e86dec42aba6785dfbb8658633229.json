{"ast": null, "code": "import { trimNumber, num2str } from '@rc-component/mini-decimal';\nexport function getDecupleSteps(step) {\n  var stepStr = typeof step === 'number' ? num2str(step) : trimNumber(step).fullStr;\n  var hasPoint = stepStr.includes('.');\n  if (!hasPoint) {\n    return step + '0';\n  }\n  return trimNumber(stepStr.replace(/(\\d)\\.(\\d)/g, '$1$2.')).fullStr;\n}", "map": {"version": 3, "names": ["trimNumber", "num2str", "getDecupleSteps", "step", "stepStr", "fullStr", "hasPoint", "includes", "replace"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-input-number/es/utils/numberUtil.js"], "sourcesContent": ["import { trimNumber, num2str } from '@rc-component/mini-decimal';\nexport function getDecupleSteps(step) {\n  var stepStr = typeof step === 'number' ? num2str(step) : trimNumber(step).fullStr;\n  var hasPoint = stepStr.includes('.');\n  if (!hasPoint) {\n    return step + '0';\n  }\n  return trimNumber(stepStr.replace(/(\\d)\\.(\\d)/g, '$1$2.')).fullStr;\n}"], "mappings": "AAAA,SAASA,UAAU,EAAEC,OAAO,QAAQ,4BAA4B;AAChE,OAAO,SAASC,eAAeA,CAACC,IAAI,EAAE;EACpC,IAAIC,OAAO,GAAG,OAAOD,IAAI,KAAK,QAAQ,GAAGF,OAAO,CAACE,IAAI,CAAC,GAAGH,UAAU,CAACG,IAAI,CAAC,CAACE,OAAO;EACjF,IAAIC,QAAQ,GAAGF,OAAO,CAACG,QAAQ,CAAC,GAAG,CAAC;EACpC,IAAI,CAACD,QAAQ,EAAE;IACb,OAAOH,IAAI,GAAG,GAAG;EACnB;EACA,OAAOH,UAAU,CAACI,OAAO,CAACI,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC,CAACH,OAAO;AACpE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}