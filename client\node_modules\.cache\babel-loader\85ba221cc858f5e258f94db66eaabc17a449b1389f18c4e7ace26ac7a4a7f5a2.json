{"ast": null, "code": "import React from \"react\";\nvar defaultProps = {\n  accessibility: true,\n  adaptiveHeight: false,\n  afterChange: null,\n  appendDots: function appendDots(dots) {\n    return /*#__PURE__*/React.createElement(\"ul\", {\n      style: {\n        display: \"block\"\n      }\n    }, dots);\n  },\n  arrows: true,\n  autoplay: false,\n  autoplaySpeed: 3000,\n  beforeChange: null,\n  centerMode: false,\n  centerPadding: \"50px\",\n  className: \"\",\n  cssEase: \"ease\",\n  customPaging: function customPaging(i) {\n    return /*#__PURE__*/React.createElement(\"button\", null, i + 1);\n  },\n  dots: false,\n  dotsClass: \"slick-dots\",\n  draggable: true,\n  easing: \"linear\",\n  edgeFriction: 0.35,\n  fade: false,\n  focusOnSelect: false,\n  infinite: true,\n  initialSlide: 0,\n  lazyLoad: null,\n  nextArrow: null,\n  onEdge: null,\n  onInit: null,\n  onLazyLoadError: null,\n  onReInit: null,\n  pauseOnDotsHover: false,\n  pauseOnFocus: false,\n  pauseOnHover: true,\n  prevArrow: null,\n  responsive: null,\n  rows: 1,\n  rtl: false,\n  slide: \"div\",\n  slidesPerRow: 1,\n  slidesToScroll: 1,\n  slidesToShow: 1,\n  speed: 500,\n  swipe: true,\n  swipeEvent: null,\n  swipeToSlide: false,\n  touchMove: true,\n  touchThreshold: 5,\n  useCSS: true,\n  useTransform: true,\n  variableWidth: false,\n  vertical: false,\n  waitForAnimate: true\n};\nexport default defaultProps;", "map": {"version": 3, "names": ["React", "defaultProps", "accessibility", "adaptiveHeight", "afterChange", "appendDots", "dots", "createElement", "style", "display", "arrows", "autoplay", "autoplaySpeed", "beforeChange", "centerMode", "centerPadding", "className", "cssEase", "customPaging", "i", "dotsClass", "draggable", "easing", "edgeFriction", "fade", "focusOnSelect", "infinite", "initialSlide", "lazyLoad", "nextArrow", "onEdge", "onInit", "onLazyLoadError", "onReInit", "pauseOnDotsHover", "pauseOnFocus", "pauseOnHover", "prevArrow", "responsive", "rows", "rtl", "slide", "slidesPerRow", "slidesToScroll", "slidesToShow", "speed", "swipe", "swipeEvent", "swipeToSlide", "touchMove", "touchThreshold", "useCSS", "useTransform", "variableWidth", "vertical", "waitForAnimate"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/@ant-design/react-slick/es/default-props.js"], "sourcesContent": ["import React from \"react\";\nvar defaultProps = {\n  accessibility: true,\n  adaptiveHeight: false,\n  afterChange: null,\n  appendDots: function appendDots(dots) {\n    return /*#__PURE__*/React.createElement(\"ul\", {\n      style: {\n        display: \"block\"\n      }\n    }, dots);\n  },\n  arrows: true,\n  autoplay: false,\n  autoplaySpeed: 3000,\n  beforeChange: null,\n  centerMode: false,\n  centerPadding: \"50px\",\n  className: \"\",\n  cssEase: \"ease\",\n  customPaging: function customPaging(i) {\n    return /*#__PURE__*/React.createElement(\"button\", null, i + 1);\n  },\n  dots: false,\n  dotsClass: \"slick-dots\",\n  draggable: true,\n  easing: \"linear\",\n  edgeFriction: 0.35,\n  fade: false,\n  focusOnSelect: false,\n  infinite: true,\n  initialSlide: 0,\n  lazyLoad: null,\n  nextArrow: null,\n  onEdge: null,\n  onInit: null,\n  onLazyLoadError: null,\n  onReInit: null,\n  pauseOnDotsHover: false,\n  pauseOnFocus: false,\n  pauseOnHover: true,\n  prevArrow: null,\n  responsive: null,\n  rows: 1,\n  rtl: false,\n  slide: \"div\",\n  slidesPerRow: 1,\n  slidesToScroll: 1,\n  slidesToShow: 1,\n  speed: 500,\n  swipe: true,\n  swipeEvent: null,\n  swipeToSlide: false,\n  touchMove: true,\n  touchThreshold: 5,\n  useCSS: true,\n  useTransform: true,\n  variableWidth: false,\n  vertical: false,\n  waitForAnimate: true\n};\nexport default defaultProps;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,IAAIC,YAAY,GAAG;EACjBC,aAAa,EAAE,IAAI;EACnBC,cAAc,EAAE,KAAK;EACrBC,WAAW,EAAE,IAAI;EACjBC,UAAU,EAAE,SAASA,UAAUA,CAACC,IAAI,EAAE;IACpC,OAAO,aAAaN,KAAK,CAACO,aAAa,CAAC,IAAI,EAAE;MAC5CC,KAAK,EAAE;QACLC,OAAO,EAAE;MACX;IACF,CAAC,EAAEH,IAAI,CAAC;EACV,CAAC;EACDI,MAAM,EAAE,IAAI;EACZC,QAAQ,EAAE,KAAK;EACfC,aAAa,EAAE,IAAI;EACnBC,YAAY,EAAE,IAAI;EAClBC,UAAU,EAAE,KAAK;EACjBC,aAAa,EAAE,MAAM;EACrBC,SAAS,EAAE,EAAE;EACbC,OAAO,EAAE,MAAM;EACfC,YAAY,EAAE,SAASA,YAAYA,CAACC,CAAC,EAAE;IACrC,OAAO,aAAanB,KAAK,CAACO,aAAa,CAAC,QAAQ,EAAE,IAAI,EAAEY,CAAC,GAAG,CAAC,CAAC;EAChE,CAAC;EACDb,IAAI,EAAE,KAAK;EACXc,SAAS,EAAE,YAAY;EACvBC,SAAS,EAAE,IAAI;EACfC,MAAM,EAAE,QAAQ;EAChBC,YAAY,EAAE,IAAI;EAClBC,IAAI,EAAE,KAAK;EACXC,aAAa,EAAE,KAAK;EACpBC,QAAQ,EAAE,IAAI;EACdC,YAAY,EAAE,CAAC;EACfC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE,IAAI;EACfC,MAAM,EAAE,IAAI;EACZC,MAAM,EAAE,IAAI;EACZC,eAAe,EAAE,IAAI;EACrBC,QAAQ,EAAE,IAAI;EACdC,gBAAgB,EAAE,KAAK;EACvBC,YAAY,EAAE,KAAK;EACnBC,YAAY,EAAE,IAAI;EAClBC,SAAS,EAAE,IAAI;EACfC,UAAU,EAAE,IAAI;EAChBC,IAAI,EAAE,CAAC;EACPC,GAAG,EAAE,KAAK;EACVC,KAAK,EAAE,KAAK;EACZC,YAAY,EAAE,CAAC;EACfC,cAAc,EAAE,CAAC;EACjBC,YAAY,EAAE,CAAC;EACfC,KAAK,EAAE,GAAG;EACVC,KAAK,EAAE,IAAI;EACXC,UAAU,EAAE,IAAI;EAChBC,YAAY,EAAE,KAAK;EACnBC,SAAS,EAAE,IAAI;EACfC,cAAc,EAAE,CAAC;EACjBC,MAAM,EAAE,IAAI;EACZC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,KAAK;EACpBC,QAAQ,EAAE,KAAK;EACfC,cAAc,EAAE;AAClB,CAAC;AACD,eAAetD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}