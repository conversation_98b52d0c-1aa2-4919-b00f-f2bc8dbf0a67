// Test script to verify Primary Kiswahili Medium level functionality
const axios = require('axios');

const API_BASE = 'http://localhost:5000/api';

// Test data for Primary Kiswahili Medium registration
const testUser = {
  firstName: "Amina",
  lastName: "<PERSON><PERSON><PERSON><PERSON>", 
  username: "amina_test_kiswahili",
  email: "<EMAIL>",
  school: "<PERSON>le ya Msingi Kibada",
  level: "primary_kiswahili",
  class: "3",
  phoneNumber: "0754123456",
  password: "test123"
};

async function testKiswahiliRegistration() {
  console.log('🧪 Testing Primary Kiswahili Medium Level Registration...\n');
  
  try {
    // Test 1: Register a user with primary_kiswahili level
    console.log('📝 Test 1: Registering user with Primary Kiswahili Medium level...');
    console.log('User data:', JSON.stringify(testUser, null, 2));
    
    const registerResponse = await axios.post(`${API_BASE}/users/register`, testUser);
    
    if (registerResponse.status === 200) {
      console.log('✅ Registration successful!');
      console.log('Response:', registerResponse.data);
    }
    
    // Test 2: Try to login with the new user
    console.log('\n🔐 Test 2: Testing login with new user...');
    const loginResponse = await axios.post(`${API_BASE}/users/login`, {
      username: testUser.username,
      password: testUser.password
    });
    
    if (loginResponse.status === 200) {
      console.log('✅ Login successful!');
      console.log('User level:', loginResponse.data.data.level);
      
      // Verify the level is correctly stored
      if (loginResponse.data.data.level === 'primary_kiswahili') {
        console.log('✅ Level verification successful: primary_kiswahili');
      } else {
        console.log('❌ Level verification failed. Expected: primary_kiswahili, Got:', loginResponse.data.data.level);
      }
    }
    
    // Test 3: Check if user can access appropriate content
    console.log('\n📚 Test 3: Testing access to Kiswahili content...');
    const token = loginResponse.data.data.token;
    
    // Test accessing quizzes (should work)
    try {
      const quizResponse = await axios.get(`${API_BASE}/exams/get-all-exams`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      console.log('✅ Quiz access successful');
    } catch (error) {
      console.log('⚠️ Quiz access test:', error.response?.data?.message || error.message);
    }
    
    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📋 Summary:');
    console.log('- ✅ Primary Kiswahili Medium level registration works');
    console.log('- ✅ Level is correctly stored in database');
    console.log('- ✅ User can login with new level');
    console.log('- ✅ Basic functionality is accessible');
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    
    if (error.response?.status === 409) {
      console.log('\n💡 Note: User might already exist. This is expected if running the test multiple times.');
      
      // Try login instead
      try {
        console.log('\n🔄 Attempting login with existing user...');
        const loginResponse = await axios.post(`${API_BASE}/users/login`, {
          username: testUser.username,
          password: testUser.password
        });
        
        if (loginResponse.status === 200) {
          console.log('✅ Login with existing user successful!');
          console.log('User level:', loginResponse.data.data.level);
        }
      } catch (loginError) {
        console.error('❌ Login test also failed:', loginError.response?.data || loginError.message);
      }
    }
  }
}

// Test server health first
async function testServerHealth() {
  try {
    console.log('🏥 Testing server health...');
    const healthResponse = await axios.get(`${API_BASE}/health`);
    console.log('✅ Server is healthy:', healthResponse.data.message);
    return true;
  } catch (error) {
    console.error('❌ Server health check failed:', error.message);
    return false;
  }
}

// Run tests
async function runTests() {
  console.log('🚀 Starting Primary Kiswahili Medium Level Tests\n');
  console.log('=' .repeat(60));
  
  const serverHealthy = await testServerHealth();
  if (!serverHealthy) {
    console.log('❌ Cannot proceed with tests - server is not responding');
    return;
  }
  
  console.log('');
  await testKiswahiliRegistration();
  
  console.log('\n' + '='.repeat(60));
  console.log('🏁 Test suite completed!');
}

// Run the tests
runTests().catch(console.error);
