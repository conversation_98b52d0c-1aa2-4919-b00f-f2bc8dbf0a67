{"ast": null, "code": "import DeleteOutlined from \"@ant-design/icons/es/icons/DeleteOutlined\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport TransButton from '../_util/transButton';\nimport Checkbox from '../checkbox';\nimport { useLocale } from '../locale';\nimport defaultLocale from '../locale/en_US';\nconst ListItem = props => {\n  const {\n    renderedText,\n    renderedEl,\n    item,\n    checked,\n    disabled,\n    prefixCls,\n    onClick,\n    onRemove,\n    showRemove\n  } = props;\n  const className = classNames(\"\".concat(prefixCls, \"-content-item\"), {\n    [\"\".concat(prefixCls, \"-content-item-disabled\")]: disabled || item.disabled,\n    [\"\".concat(prefixCls, \"-content-item-checked\")]: checked\n  });\n  let title;\n  if (typeof renderedText === 'string' || typeof renderedText === 'number') {\n    title = String(renderedText);\n  }\n  const [contextLocale] = useLocale('Transfer', defaultLocale.Transfer);\n  const liProps = {\n    className,\n    title\n  };\n  const labelNode = /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-content-item-text\")\n  }, renderedEl);\n  if (showRemove) {\n    return /*#__PURE__*/React.createElement(\"li\", Object.assign({}, liProps), labelNode, /*#__PURE__*/React.createElement(TransButton, {\n      disabled: disabled || item.disabled,\n      className: \"\".concat(prefixCls, \"-content-item-remove\"),\n      \"aria-label\": contextLocale === null || contextLocale === void 0 ? void 0 : contextLocale.remove,\n      onClick: () => {\n        onRemove === null || onRemove === void 0 ? void 0 : onRemove(item);\n      }\n    }, /*#__PURE__*/React.createElement(DeleteOutlined, null)));\n  }\n  // Default click to select\n  liProps.onClick = disabled || item.disabled ? undefined : () => onClick(item);\n  return /*#__PURE__*/React.createElement(\"li\", Object.assign({}, liProps), /*#__PURE__*/React.createElement(Checkbox, {\n    className: \"\".concat(prefixCls, \"-checkbox\"),\n    checked: checked,\n    disabled: disabled || item.disabled\n  }), labelNode);\n};\nexport default /*#__PURE__*/React.memo(ListItem);", "map": {"version": 3, "names": ["DeleteOutlined", "classNames", "React", "TransButton", "Checkbox", "useLocale", "defaultLocale", "ListItem", "props", "renderedText", "renderedEl", "item", "checked", "disabled", "prefixCls", "onClick", "onRemove", "showRemove", "className", "concat", "title", "String", "contextLocale", "Transfer", "liProps", "labelNode", "createElement", "Object", "assign", "remove", "undefined", "memo"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/transfer/ListItem.js"], "sourcesContent": ["import DeleteOutlined from \"@ant-design/icons/es/icons/DeleteOutlined\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport TransButton from '../_util/transButton';\nimport Checkbox from '../checkbox';\nimport { useLocale } from '../locale';\nimport defaultLocale from '../locale/en_US';\nconst ListItem = props => {\n  const {\n    renderedText,\n    renderedEl,\n    item,\n    checked,\n    disabled,\n    prefixCls,\n    onClick,\n    onRemove,\n    showRemove\n  } = props;\n  const className = classNames(`${prefixCls}-content-item`, {\n    [`${prefixCls}-content-item-disabled`]: disabled || item.disabled,\n    [`${prefixCls}-content-item-checked`]: checked\n  });\n  let title;\n  if (typeof renderedText === 'string' || typeof renderedText === 'number') {\n    title = String(renderedText);\n  }\n  const [contextLocale] = useLocale('Transfer', defaultLocale.Transfer);\n  const liProps = {\n    className,\n    title\n  };\n  const labelNode = /*#__PURE__*/React.createElement(\"span\", {\n    className: `${prefixCls}-content-item-text`\n  }, renderedEl);\n  if (showRemove) {\n    return /*#__PURE__*/React.createElement(\"li\", Object.assign({}, liProps), labelNode, /*#__PURE__*/React.createElement(TransButton, {\n      disabled: disabled || item.disabled,\n      className: `${prefixCls}-content-item-remove`,\n      \"aria-label\": contextLocale === null || contextLocale === void 0 ? void 0 : contextLocale.remove,\n      onClick: () => {\n        onRemove === null || onRemove === void 0 ? void 0 : onRemove(item);\n      }\n    }, /*#__PURE__*/React.createElement(DeleteOutlined, null)));\n  }\n  // Default click to select\n  liProps.onClick = disabled || item.disabled ? undefined : () => onClick(item);\n  return /*#__PURE__*/React.createElement(\"li\", Object.assign({}, liProps), /*#__PURE__*/React.createElement(Checkbox, {\n    className: `${prefixCls}-checkbox`,\n    checked: checked,\n    disabled: disabled || item.disabled\n  }), labelNode);\n};\nexport default /*#__PURE__*/React.memo(ListItem);"], "mappings": "AAAA,OAAOA,cAAc,MAAM,2CAA2C;AACtE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,OAAOC,QAAQ,MAAM,aAAa;AAClC,SAASC,SAAS,QAAQ,WAAW;AACrC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,MAAMC,QAAQ,GAAGC,KAAK,IAAI;EACxB,MAAM;IACJC,YAAY;IACZC,UAAU;IACVC,IAAI;IACJC,OAAO;IACPC,QAAQ;IACRC,SAAS;IACTC,OAAO;IACPC,QAAQ;IACRC;EACF,CAAC,GAAGT,KAAK;EACT,MAAMU,SAAS,GAAGjB,UAAU,IAAAkB,MAAA,CAAIL,SAAS,oBAAiB;IACxD,IAAAK,MAAA,CAAIL,SAAS,8BAA2BD,QAAQ,IAAIF,IAAI,CAACE,QAAQ;IACjE,IAAAM,MAAA,CAAIL,SAAS,6BAA0BF;EACzC,CAAC,CAAC;EACF,IAAIQ,KAAK;EACT,IAAI,OAAOX,YAAY,KAAK,QAAQ,IAAI,OAAOA,YAAY,KAAK,QAAQ,EAAE;IACxEW,KAAK,GAAGC,MAAM,CAACZ,YAAY,CAAC;EAC9B;EACA,MAAM,CAACa,aAAa,CAAC,GAAGjB,SAAS,CAAC,UAAU,EAAEC,aAAa,CAACiB,QAAQ,CAAC;EACrE,MAAMC,OAAO,GAAG;IACdN,SAAS;IACTE;EACF,CAAC;EACD,MAAMK,SAAS,GAAG,aAAavB,KAAK,CAACwB,aAAa,CAAC,MAAM,EAAE;IACzDR,SAAS,KAAAC,MAAA,CAAKL,SAAS;EACzB,CAAC,EAAEJ,UAAU,CAAC;EACd,IAAIO,UAAU,EAAE;IACd,OAAO,aAAaf,KAAK,CAACwB,aAAa,CAAC,IAAI,EAAEC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEJ,OAAO,CAAC,EAAEC,SAAS,EAAE,aAAavB,KAAK,CAACwB,aAAa,CAACvB,WAAW,EAAE;MACjIU,QAAQ,EAAEA,QAAQ,IAAIF,IAAI,CAACE,QAAQ;MACnCK,SAAS,KAAAC,MAAA,CAAKL,SAAS,yBAAsB;MAC7C,YAAY,EAAEQ,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACO,MAAM;MAChGd,OAAO,EAAEA,CAAA,KAAM;QACbC,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACL,IAAI,CAAC;MACpE;IACF,CAAC,EAAE,aAAaT,KAAK,CAACwB,aAAa,CAAC1B,cAAc,EAAE,IAAI,CAAC,CAAC,CAAC;EAC7D;EACA;EACAwB,OAAO,CAACT,OAAO,GAAGF,QAAQ,IAAIF,IAAI,CAACE,QAAQ,GAAGiB,SAAS,GAAG,MAAMf,OAAO,CAACJ,IAAI,CAAC;EAC7E,OAAO,aAAaT,KAAK,CAACwB,aAAa,CAAC,IAAI,EAAEC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEJ,OAAO,CAAC,EAAE,aAAatB,KAAK,CAACwB,aAAa,CAACtB,QAAQ,EAAE;IACnHc,SAAS,KAAAC,MAAA,CAAKL,SAAS,cAAW;IAClCF,OAAO,EAAEA,OAAO;IAChBC,QAAQ,EAAEA,QAAQ,IAAIF,IAAI,CAACE;EAC7B,CAAC,CAAC,EAAEY,SAAS,CAAC;AAChB,CAAC;AACD,eAAe,aAAavB,KAAK,CAAC6B,IAAI,CAACxB,QAAQ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}