const fs = require('fs');
const path = require('path');
const pdfParse = require('pdf-parse');

/**
 * Extract text from PDF file
 * @param {string} filePath - Path to the PDF file
 * @returns {Promise<string>} - Extracted text content
 */
async function extractTextFromPDF(filePath) {
  try {
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      throw new Error(`PDF file not found: ${filePath}`);
    }

    // Read the PDF file
    const dataBuffer = fs.readFileSync(filePath);
    
    // Parse the PDF and extract text
    const data = await pdfParse(dataBuffer);
    
    console.log(`📄 PDF Text Extraction Success:`, {
      file: path.basename(filePath),
      pages: data.numpages,
      textLength: data.text.length
    });
    
    return data.text;
  } catch (error) {
    console.error('❌ PDF Text Extraction Error:', error.message);
    throw new Error(`Failed to extract text from PDF: ${error.message}`);
  }
}

/**
 * Extract specific question from PDF text
 * @param {string} pdfText - Full PDF text content
 * @param {string} questionNumber - Question number to extract (e.g., "1", "2a", "3")
 * @returns {object} - Question data with text and metadata
 */
function extractQuestionFromText(pdfText, questionNumber) {
  try {
    // Multiple regex patterns to match different question formats
    const patterns = [
      // "Question 1:", "Question 2a:", etc.
      new RegExp(`Question\\s*${questionNumber}[:\\.]([\\s\\S]*?)(?=Question\\s*\\d+[a-z]?[:\\.]|$)`, 'i'),
      // "1.", "2a.", etc.
      new RegExp(`^\\s*${questionNumber}[:\\.]([\\s\\S]*?)(?=^\\s*\\d+[a-z]?[:\\.]|$)`, 'im'),
      // "1)", "2a)", etc.
      new RegExp(`^\\s*${questionNumber}\\)([\\s\\S]*?)(?=^\\s*\\d+[a-z]?\\)|$)`, 'im'),
      // "(1)", "(2a)", etc.
      new RegExp(`\\(${questionNumber}\\)([\\s\\S]*?)(?=\\(\\d+[a-z]?\\)|$)`, 'i'),
      // More flexible pattern
      new RegExp(`(?:^|\\n)\\s*${questionNumber}[\\s\\.:)]([\\s\\S]*?)(?=(?:^|\\n)\\s*\\d+[a-z]?[\\s\\.:)]|$)`, 'i')
    ];

    for (const pattern of patterns) {
      const match = pdfText.match(pattern);
      if (match && match[1]) {
        const questionText = match[1].trim();
        
        // Clean up the question text
        const cleanedText = questionText
          .replace(/\s+/g, ' ')  // Replace multiple spaces with single space
          .replace(/\n\s*\n/g, '\n')  // Remove empty lines
          .trim();

        if (cleanedText.length > 10) { // Ensure we have substantial content
          console.log(`✅ Question ${questionNumber} found:`, {
            pattern: pattern.source,
            textLength: cleanedText.length,
            preview: cleanedText.substring(0, 100) + '...'
          });

          return {
            questionNumber,
            questionText: cleanedText,
            found: true,
            extractedAt: new Date().toISOString()
          };
        }
      }
    }

    console.log(`❌ Question ${questionNumber} not found in PDF text`);
    return {
      questionNumber,
      questionText: null,
      found: false,
      error: `Question ${questionNumber} not found in the document`
    };

  } catch (error) {
    console.error(`❌ Error extracting question ${questionNumber}:`, error.message);
    return {
      questionNumber,
      questionText: null,
      found: false,
      error: `Error extracting question: ${error.message}`
    };
  }
}

/**
 * Get all questions from PDF text
 * @param {string} pdfText - Full PDF text content
 * @returns {array} - Array of all found questions
 */
function getAllQuestionsFromText(pdfText) {
  try {
    const questions = [];
    
    // Pattern to find all question numbers
    const questionPattern = /(?:Question\s*(\d+[a-z]?)|^(\d+[a-z]?)[:\.])/gim;
    let match;
    
    while ((match = questionPattern.exec(pdfText)) !== null) {
      const questionNum = match[1] || match[2];
      if (questionNum && !questions.find(q => q.number === questionNum)) {
        const questionData = extractQuestionFromText(pdfText, questionNum);
        if (questionData.found) {
          questions.push({
            number: questionNum,
            text: questionData.questionText,
            preview: questionData.questionText.substring(0, 150) + '...'
          });
        }
      }
    }

    console.log(`📋 Found ${questions.length} questions in PDF`);
    return questions;

  } catch (error) {
    console.error('❌ Error getting all questions:', error.message);
    return [];
  }
}

module.exports = {
  extractTextFromPDF,
  extractQuestionFromText,
  getAllQuestionsFromText
};
