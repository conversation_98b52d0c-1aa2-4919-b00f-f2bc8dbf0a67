{"ast": null, "code": "const genRadius = radiusBase => {\n  let radiusLG = radiusBase;\n  let radiusSM = radiusBase;\n  let radiusXS = radiusBase;\n  let radiusOuter = radiusBase;\n  // radiusLG\n  if (radiusBase < 6 && radiusBase >= 5) {\n    radiusLG = radiusBase + 1;\n  } else if (radiusBase < 16 && radiusBase >= 6) {\n    radiusLG = radiusBase + 2;\n  } else if (radiusBase >= 16) {\n    radiusLG = 16;\n  }\n  // radiusSM\n  if (radiusBase < 7 && radiusBase >= 5) {\n    radiusSM = 4;\n  } else if (radiusBase < 8 && radiusBase >= 7) {\n    radiusSM = 5;\n  } else if (radiusBase < 14 && radiusBase >= 8) {\n    radiusSM = 6;\n  } else if (radiusBase < 16 && radiusBase >= 14) {\n    radiusSM = 7;\n  } else if (radiusBase >= 16) {\n    radiusSM = 8;\n  }\n  // radiusXS\n  if (radiusBase < 6 && radiusBase >= 2) {\n    radiusXS = 1;\n  } else if (radiusBase >= 6) {\n    radiusXS = 2;\n  }\n  // radiusOuter\n  if (radiusBase > 4 && radiusBase < 8) {\n    radiusOuter = 4;\n  } else if (radiusBase >= 8) {\n    radiusOuter = 6;\n  }\n  return {\n    borderRadius: radiusBase > 16 ? 16 : radiusBase,\n    borderRadiusXS: radiusXS,\n    borderRadiusSM: radiusSM,\n    borderRadiusLG: radiusLG,\n    borderRadiusOuter: radiusOuter\n  };\n};\nexport default genRadius;", "map": {"version": 3, "names": ["genRadius", "radiusBase", "radiusLG", "radiusSM", "radiusXS", "radiusOuter", "borderRadius", "borderRadiusXS", "borderRadiusSM", "borderRadiusLG", "borderRadiusOuter"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/theme/themes/shared/genRadius.js"], "sourcesContent": ["const genRadius = radiusBase => {\n  let radiusLG = radiusBase;\n  let radiusSM = radiusBase;\n  let radiusXS = radiusBase;\n  let radiusOuter = radiusBase;\n  // radiusLG\n  if (radiusBase < 6 && radiusBase >= 5) {\n    radiusLG = radiusBase + 1;\n  } else if (radiusBase < 16 && radiusBase >= 6) {\n    radiusLG = radiusBase + 2;\n  } else if (radiusBase >= 16) {\n    radiusLG = 16;\n  }\n  // radiusSM\n  if (radiusBase < 7 && radiusBase >= 5) {\n    radiusSM = 4;\n  } else if (radiusBase < 8 && radiusBase >= 7) {\n    radiusSM = 5;\n  } else if (radiusBase < 14 && radiusBase >= 8) {\n    radiusSM = 6;\n  } else if (radiusBase < 16 && radiusBase >= 14) {\n    radiusSM = 7;\n  } else if (radiusBase >= 16) {\n    radiusSM = 8;\n  }\n  // radiusXS\n  if (radiusBase < 6 && radiusBase >= 2) {\n    radiusXS = 1;\n  } else if (radiusBase >= 6) {\n    radiusXS = 2;\n  }\n  // radiusOuter\n  if (radiusBase > 4 && radiusBase < 8) {\n    radiusOuter = 4;\n  } else if (radiusBase >= 8) {\n    radiusOuter = 6;\n  }\n  return {\n    borderRadius: radiusBase > 16 ? 16 : radiusBase,\n    borderRadiusXS: radiusXS,\n    borderRadiusSM: radiusSM,\n    borderRadiusLG: radiusLG,\n    borderRadiusOuter: radiusOuter\n  };\n};\nexport default genRadius;"], "mappings": "AAAA,MAAMA,SAAS,GAAGC,UAAU,IAAI;EAC9B,IAAIC,QAAQ,GAAGD,UAAU;EACzB,IAAIE,QAAQ,GAAGF,UAAU;EACzB,IAAIG,QAAQ,GAAGH,UAAU;EACzB,IAAII,WAAW,GAAGJ,UAAU;EAC5B;EACA,IAAIA,UAAU,GAAG,CAAC,IAAIA,UAAU,IAAI,CAAC,EAAE;IACrCC,QAAQ,GAAGD,UAAU,GAAG,CAAC;EAC3B,CAAC,MAAM,IAAIA,UAAU,GAAG,EAAE,IAAIA,UAAU,IAAI,CAAC,EAAE;IAC7CC,QAAQ,GAAGD,UAAU,GAAG,CAAC;EAC3B,CAAC,MAAM,IAAIA,UAAU,IAAI,EAAE,EAAE;IAC3BC,QAAQ,GAAG,EAAE;EACf;EACA;EACA,IAAID,UAAU,GAAG,CAAC,IAAIA,UAAU,IAAI,CAAC,EAAE;IACrCE,QAAQ,GAAG,CAAC;EACd,CAAC,MAAM,IAAIF,UAAU,GAAG,CAAC,IAAIA,UAAU,IAAI,CAAC,EAAE;IAC5CE,QAAQ,GAAG,CAAC;EACd,CAAC,MAAM,IAAIF,UAAU,GAAG,EAAE,IAAIA,UAAU,IAAI,CAAC,EAAE;IAC7CE,QAAQ,GAAG,CAAC;EACd,CAAC,MAAM,IAAIF,UAAU,GAAG,EAAE,IAAIA,UAAU,IAAI,EAAE,EAAE;IAC9CE,QAAQ,GAAG,CAAC;EACd,CAAC,MAAM,IAAIF,UAAU,IAAI,EAAE,EAAE;IAC3BE,QAAQ,GAAG,CAAC;EACd;EACA;EACA,IAAIF,UAAU,GAAG,CAAC,IAAIA,UAAU,IAAI,CAAC,EAAE;IACrCG,QAAQ,GAAG,CAAC;EACd,CAAC,MAAM,IAAIH,UAAU,IAAI,CAAC,EAAE;IAC1BG,QAAQ,GAAG,CAAC;EACd;EACA;EACA,IAAIH,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAG,CAAC,EAAE;IACpCI,WAAW,GAAG,CAAC;EACjB,CAAC,MAAM,IAAIJ,UAAU,IAAI,CAAC,EAAE;IAC1BI,WAAW,GAAG,CAAC;EACjB;EACA,OAAO;IACLC,YAAY,EAAEL,UAAU,GAAG,EAAE,GAAG,EAAE,GAAGA,UAAU;IAC/CM,cAAc,EAAEH,QAAQ;IACxBI,cAAc,EAAEL,QAAQ;IACxBM,cAAc,EAAEP,QAAQ;IACxBQ,iBAAiB,EAAEL;EACrB,CAAC;AACH,CAAC;AACD,eAAeL,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}