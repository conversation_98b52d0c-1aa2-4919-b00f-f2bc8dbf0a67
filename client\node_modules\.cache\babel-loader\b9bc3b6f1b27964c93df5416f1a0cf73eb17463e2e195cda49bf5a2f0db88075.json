{"ast": null, "code": "// =========================== Motion ===========================\nconst genRtlStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [\"\".concat(componentCls, \"-rtl\")]: {\n      direction: 'rtl'\n    }\n  };\n};\nexport default genRtlStyle;", "map": {"version": 3, "names": ["genRtlStyle", "token", "componentCls", "concat", "direction"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/upload/style/rtl.js"], "sourcesContent": ["// =========================== Motion ===========================\nconst genRtlStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [`${componentCls}-rtl`]: {\n      direction: 'rtl'\n    }\n  };\n};\nexport default genRtlStyle;"], "mappings": "AAAA;AACA,MAAMA,WAAW,GAAGC,KAAK,IAAI;EAC3B,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,OAAO;IACL,IAAAE,MAAA,CAAID,YAAY,YAAS;MACvBE,SAAS,EAAE;IACb;EACF,CAAC;AACH,CAAC;AACD,eAAeJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}