{"ast": null, "code": "const getHorizontalStyle = token => {\n  const {\n    componentCls,\n    motionDurationSlow,\n    horizontalLineHeight,\n    colorSplit,\n    lineWidth,\n    lineType,\n    itemPaddingInline\n  } = token;\n  return {\n    [\"\".concat(componentCls, \"-horizontal\")]: {\n      lineHeight: horizontalLineHeight,\n      border: 0,\n      borderBottom: \"\".concat(lineWidth, \"px \").concat(lineType, \" \").concat(colorSplit),\n      boxShadow: 'none',\n      '&::after': {\n        display: 'block',\n        clear: 'both',\n        height: 0,\n        content: '\"\\\\20\"'\n      },\n      // ======================= Item =======================\n      [\"\".concat(componentCls, \"-item, \").concat(componentCls, \"-submenu\")]: {\n        position: 'relative',\n        display: 'inline-block',\n        verticalAlign: 'bottom',\n        paddingInline: itemPaddingInline\n      },\n      [\"> \".concat(componentCls, \"-item:hover,\\n        > \").concat(componentCls, \"-item-active,\\n        > \").concat(componentCls, \"-submenu \").concat(componentCls, \"-submenu-title:hover\")]: {\n        backgroundColor: 'transparent'\n      },\n      [\"\".concat(componentCls, \"-item, \").concat(componentCls, \"-submenu-title\")]: {\n        transition: [\"border-color \".concat(motionDurationSlow), \"background \".concat(motionDurationSlow)].join(',')\n      },\n      // ===================== Sub Menu =====================\n      [\"\".concat(componentCls, \"-submenu-arrow\")]: {\n        display: 'none'\n      }\n    }\n  };\n};\nexport default getHorizontalStyle;", "map": {"version": 3, "names": ["getHorizontalStyle", "token", "componentCls", "motionDurationSlow", "horizontalLineHeight", "colorSplit", "lineWidth", "lineType", "itemPaddingInline", "concat", "lineHeight", "border", "borderBottom", "boxShadow", "display", "clear", "height", "content", "position", "verticalAlign", "paddingInline", "backgroundColor", "transition", "join"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/menu/style/horizontal.js"], "sourcesContent": ["const getHorizontalStyle = token => {\n  const {\n    componentCls,\n    motionDurationSlow,\n    horizontalLineHeight,\n    colorSplit,\n    lineWidth,\n    lineType,\n    itemPaddingInline\n  } = token;\n  return {\n    [`${componentCls}-horizontal`]: {\n      lineHeight: horizontalLineHeight,\n      border: 0,\n      borderBottom: `${lineWidth}px ${lineType} ${colorSplit}`,\n      boxShadow: 'none',\n      '&::after': {\n        display: 'block',\n        clear: 'both',\n        height: 0,\n        content: '\"\\\\20\"'\n      },\n      // ======================= Item =======================\n      [`${componentCls}-item, ${componentCls}-submenu`]: {\n        position: 'relative',\n        display: 'inline-block',\n        verticalAlign: 'bottom',\n        paddingInline: itemPaddingInline\n      },\n      [`> ${componentCls}-item:hover,\n        > ${componentCls}-item-active,\n        > ${componentCls}-submenu ${componentCls}-submenu-title:hover`]: {\n        backgroundColor: 'transparent'\n      },\n      [`${componentCls}-item, ${componentCls}-submenu-title`]: {\n        transition: [`border-color ${motionDurationSlow}`, `background ${motionDurationSlow}`].join(',')\n      },\n      // ===================== Sub Menu =====================\n      [`${componentCls}-submenu-arrow`]: {\n        display: 'none'\n      }\n    }\n  };\n};\nexport default getHorizontalStyle;"], "mappings": "AAAA,MAAMA,kBAAkB,GAAGC,KAAK,IAAI;EAClC,MAAM;IACJC,YAAY;IACZC,kBAAkB;IAClBC,oBAAoB;IACpBC,UAAU;IACVC,SAAS;IACTC,QAAQ;IACRC;EACF,CAAC,GAAGP,KAAK;EACT,OAAO;IACL,IAAAQ,MAAA,CAAIP,YAAY,mBAAgB;MAC9BQ,UAAU,EAAEN,oBAAoB;MAChCO,MAAM,EAAE,CAAC;MACTC,YAAY,KAAAH,MAAA,CAAKH,SAAS,SAAAG,MAAA,CAAMF,QAAQ,OAAAE,MAAA,CAAIJ,UAAU,CAAE;MACxDQ,SAAS,EAAE,MAAM;MACjB,UAAU,EAAE;QACVC,OAAO,EAAE,OAAO;QAChBC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,CAAC;QACTC,OAAO,EAAE;MACX,CAAC;MACD;MACA,IAAAR,MAAA,CAAIP,YAAY,aAAAO,MAAA,CAAUP,YAAY,gBAAa;QACjDgB,QAAQ,EAAE,UAAU;QACpBJ,OAAO,EAAE,cAAc;QACvBK,aAAa,EAAE,QAAQ;QACvBC,aAAa,EAAEZ;MACjB,CAAC;MACD,MAAAC,MAAA,CAAMP,YAAY,8BAAAO,MAAA,CACZP,YAAY,+BAAAO,MAAA,CACZP,YAAY,eAAAO,MAAA,CAAYP,YAAY,4BAAyB;QACjEmB,eAAe,EAAE;MACnB,CAAC;MACD,IAAAZ,MAAA,CAAIP,YAAY,aAAAO,MAAA,CAAUP,YAAY,sBAAmB;QACvDoB,UAAU,EAAE,iBAAAb,MAAA,CAAiBN,kBAAkB,iBAAAM,MAAA,CAAkBN,kBAAkB,EAAG,CAACoB,IAAI,CAAC,GAAG;MACjG,CAAC;MACD;MACA,IAAAd,MAAA,CAAIP,YAAY,sBAAmB;QACjCY,OAAO,EAAE;MACX;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAed,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}