{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\VideoLessons\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useMemo } from \"react\";\nimport \"./index.css\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getStudyMaterial } from \"../../../apicalls/study\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { primarySubjects, primaryKiswahiliSubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects.jsx\";\nimport { useLanguage } from \"../../../contexts/LanguageContext\";\n\n// Temporary fix: Use simple text/symbols instead of React Icons to avoid chunk loading issues\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst IconComponents = {\n  FaPlayCircle: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '24px'\n    },\n    children: \"\\u25B6\\uFE0F\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 23\n  }, this),\n  FaGraduationCap: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '24px'\n    },\n    children: \"\\uD83C\\uDF93\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 26\n  }, this),\n  FaTimes: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\u2715\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 18\n  }, this),\n  FaExpand: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\u26F6\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 19\n  }, this),\n  FaCompress: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\u26F6\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 21\n  }, this),\n  TbVideo: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '24px'\n    },\n    children: \"\\uD83D\\uDCF9\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 18\n  }, this),\n  TbFilter: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\uD83D\\uDD0D\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 19\n  }, this),\n  TbSortAscending: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\u2191\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 26\n  }, this),\n  TbSearch: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\uD83D\\uDD0D\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 19\n  }, this),\n  TbX: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '16px'\n    },\n    children: \"\\u2715\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 14\n  }, this),\n  TbDownload: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\u21BB\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 21\n  }, this),\n  TbAlertTriangle: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '24px',\n      color: '#ff6b6b'\n    },\n    children: \"\\u26A0\\uFE0F\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 26\n  }, this),\n  TbInfoCircle: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\u2139\\uFE0F\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 23\n  }, this)\n};\n\n// Destructure for easy use\nconst {\n  FaPlayCircle,\n  FaGraduationCap,\n  FaTimes,\n  FaExpand,\n  FaCompress,\n  TbVideo,\n  TbFilter,\n  TbSortAscending,\n  TbSearch,\n  TbX,\n  TbDownload,\n  TbAlertTriangle,\n  TbInfoCircle\n} = IconComponents;\nfunction VideoLessons() {\n  _s();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    t,\n    isKiswahili,\n    getClassName,\n    getSubjectName\n  } = useLanguage();\n  const dispatch = useDispatch();\n\n  // State management\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [selectedLevel, setSelectedLevel] = useState((user === null || user === void 0 ? void 0 : user.level) || \"primary\");\n  const [selectedClass, setSelectedClass] = useState((user === null || user === void 0 ? void 0 : user.class) || \"all\");\n  const [selectedSubject, setSelectedSubject] = useState(\"all\");\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sortBy, setSortBy] = useState(\"newest\");\n\n  // Video player state\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [showVideoIndices, setShowVideoIndices] = useState([]);\n  const [isVideoExpanded, setIsVideoExpanded] = useState(false);\n  const [videoError, setVideoError] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n\n  // Comments state - store comments per video\n  const [videoComments, setVideoComments] = useState({});\n  const [newComment, setNewComment] = useState(\"\");\n  const [replyingTo, setReplyingTo] = useState(null);\n  const [replyText, setReplyText] = useState(\"\");\n  const [showComments, setShowComments] = useState(true);\n  const [commentsExpanded, setCommentsExpanded] = useState(false);\n\n  // Get comments for current video\n  const getCurrentVideoComments = () => {\n    if (currentVideoIndex === null) return [];\n    const currentVideo = filteredAndSortedVideos[currentVideoIndex];\n    if (!currentVideo) return [];\n    return videoComments[currentVideo.id] || [];\n  };\n\n  // Set comments for current video\n  const setCurrentVideoComments = comments => {\n    if (currentVideoIndex === null) return;\n    const currentVideo = filteredAndSortedVideos[currentVideoIndex];\n    if (!currentVideo) return;\n    setVideoComments(prev => ({\n      ...prev,\n      [currentVideo.id]: comments\n    }));\n  };\n\n  // Available classes based on level\n  const availableClasses = useMemo(() => {\n    if (selectedLevel === \"primary\" || selectedLevel === \"primary_kiswahili\") return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n    if (selectedLevel === \"secondary\") return [\"1\", \"2\", \"3\", \"4\"];\n    if (selectedLevel === \"advance\") return [\"5\", \"6\"];\n    return [];\n  }, [selectedLevel]);\n\n  // Available subjects based on level\n  const availableSubjects = useMemo(() => {\n    if (selectedLevel === \"primary\") return primarySubjects;\n    if (selectedLevel === \"primary_kiswahili\") return primaryKiswahiliSubjects;\n    if (selectedLevel === \"secondary\") return secondarySubjects;\n    if (selectedLevel === \"advance\") return advanceSubjects;\n    return [];\n  }, [selectedLevel]);\n\n  // Fetch videos\n  const fetchVideos = useCallback(async () => {\n    try {\n      var _response$data;\n      setLoading(true);\n      setError(null);\n      dispatch(ShowLoading());\n      const filters = {\n        level: selectedLevel,\n        className: \"all\",\n        // Get all classes for the level\n        subject: \"all\",\n        // Get all subjects for the level\n        content: \"videos\"\n      };\n      const response = await getStudyMaterial(filters);\n      if (response !== null && response !== void 0 && (_response$data = response.data) !== null && _response$data !== void 0 && _response$data.success) {\n        setVideos(response.data.data || []);\n      } else {\n        var _response$data2;\n        setError((response === null || response === void 0 ? void 0 : (_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.message) || \"Failed to fetch videos\");\n        setVideos([]);\n      }\n    } catch (error) {\n      console.error(\"❌ Error fetching videos:\", error);\n      setError(\"Failed to load videos. Please try again.\");\n      setVideos([]);\n    } finally {\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  }, [selectedLevel, dispatch]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    let filtered = videos;\n\n    // Apply level filter\n    filtered = filtered.filter(video => video.level === selectedLevel);\n\n    // Apply class filter\n    if (selectedClass !== \"all\") {\n      filtered = filtered.filter(video => {\n        // Check both className and class fields for compatibility\n        const videoClass = video.className || video.class;\n        return videoClass === selectedClass;\n      });\n    }\n\n    // Apply subject filter\n    if (selectedSubject !== \"all\") {\n      filtered = filtered.filter(video => video.subject === selectedSubject);\n    }\n\n    // Apply search filter\n    if (searchTerm.trim()) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(video => {\n        var _video$title, _video$subject, _video$topic;\n        return ((_video$title = video.title) === null || _video$title === void 0 ? void 0 : _video$title.toLowerCase().includes(searchLower)) || ((_video$subject = video.subject) === null || _video$subject === void 0 ? void 0 : _video$subject.toLowerCase().includes(searchLower)) || ((_video$topic = video.topic) === null || _video$topic === void 0 ? void 0 : _video$topic.toLowerCase().includes(searchLower));\n      });\n    }\n\n    // Apply sorting\n    const sorted = [...filtered].sort((a, b) => {\n      switch (sortBy) {\n        case \"newest\":\n          return new Date(b.createdAt || 0) - new Date(a.createdAt || 0);\n        case \"oldest\":\n          return new Date(a.createdAt || 0) - new Date(b.createdAt || 0);\n        case \"title\":\n          return (a.title || \"\").localeCompare(b.title || \"\");\n        case \"subject\":\n          return (a.subject || \"\").localeCompare(b.subject || \"\");\n        default:\n          return 0;\n      }\n    });\n    console.log('✅ Final filtered videos:', sorted.length);\n    if (sorted.length > 0) {\n      console.log('📹 Sample filtered video:', sorted[0]);\n    }\n    return sorted;\n  }, [videos, searchTerm, sortBy, selectedLevel, selectedClass, selectedSubject]);\n\n  // Video handlers\n  const handleShowVideo = async index => {\n    const video = filteredAndSortedVideos[index];\n    setCurrentVideoIndex(index);\n    setShowVideoIndices([index]);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video !== null && video !== void 0 && video.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        video.signedVideoUrl = signedUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n  const handleHideVideo = () => {\n    setShowVideoIndices([]);\n    setCurrentVideoIndex(null);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n    if (videoRef) {\n      videoRef.pause();\n    }\n  };\n  const toggleVideoExpansion = () => {\n    setIsVideoExpanded(!isVideoExpanded);\n  };\n\n  // Get signed URL for S3 videos to ensure access\n  const getSignedVideoUrl = async videoUrl => {\n    if (!videoUrl) return videoUrl;\n\n    // For AWS S3 URLs, get signed URL from backend\n    if (videoUrl.includes('amazonaws.com') || videoUrl.includes('s3.')) {\n      try {\n        const response = await fetch(`http://localhost:5000/api/study/video-signed-url?videoUrl=${encodeURIComponent(videoUrl)}`, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json'\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        if (data.success && data.signedUrl) {\n          console.log('✅ Got signed URL for S3 video');\n          return data.signedUrl;\n        } else {\n          console.warn('⚠️ Invalid response from signed URL endpoint:', data);\n          return videoUrl;\n        }\n      } catch (error) {\n        console.error('❌ Error getting signed URL:', error);\n        return videoUrl;\n      }\n    }\n    return videoUrl;\n  };\n\n  // Get thumbnail URL\n  const getThumbnailUrl = video => {\n    if (video.thumbnail) {\n      return video.thumbnail;\n    }\n    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n      let videoId = video.videoID;\n      if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n        const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n        videoId = match ? match[1] : videoId;\n      }\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n    return '/api/placeholder/400/225';\n  };\n\n  // Effects\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.level) {\n      setSelectedLevel(user.level);\n    }\n    if (user !== null && user !== void 0 && user.class) {\n      setSelectedClass(user.class);\n    }\n  }, [user]);\n\n  // Clear search and refresh\n  const handleClearSearch = () => {\n    setSearchTerm(\"\");\n  };\n  const handleRefresh = () => {\n    // Only refresh data, don't clear filters or search\n    fetchVideos();\n  };\n  const handleClearAll = () => {\n    setSearchTerm(\"\");\n    setSelectedSubject(\"all\");\n    setSelectedClass(\"all\");\n    fetchVideos();\n  };\n\n  // Comment functions\n  const handleAddComment = () => {\n    if (newComment.trim()) {\n      var _user$name, _user$name$charAt;\n      const comment = {\n        id: Date.now(),\n        text: newComment,\n        author: (user === null || user === void 0 ? void 0 : user.name) || \"Anonymous\",\n        avatar: (user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : (_user$name$charAt = _user$name.charAt(0)) === null || _user$name$charAt === void 0 ? void 0 : _user$name$charAt.toUpperCase()) || \"A\",\n        timestamp: new Date().toISOString(),\n        replies: [],\n        likes: 0,\n        liked: false\n      };\n      const currentComments = getCurrentVideoComments();\n      setCurrentVideoComments([comment, ...currentComments]); // Add new comments at the top\n      setNewComment(\"\");\n    }\n  };\n  const handleAddReply = commentId => {\n    if (replyText.trim()) {\n      var _user$name2, _user$name2$charAt;\n      const reply = {\n        id: Date.now(),\n        text: replyText,\n        author: (user === null || user === void 0 ? void 0 : user.name) || \"Anonymous\",\n        avatar: (user === null || user === void 0 ? void 0 : (_user$name2 = user.name) === null || _user$name2 === void 0 ? void 0 : (_user$name2$charAt = _user$name2.charAt(0)) === null || _user$name2$charAt === void 0 ? void 0 : _user$name2$charAt.toUpperCase()) || \"A\",\n        timestamp: new Date().toISOString(),\n        likes: 0,\n        liked: false\n      };\n      setComments(comments.map(comment => comment.id === commentId ? {\n        ...comment,\n        replies: [...comment.replies, reply]\n      } : comment));\n      setReplyText(\"\");\n      setReplyingTo(null);\n    }\n  };\n  const handleLikeComment = (commentId, isReply = false, parentId = null) => {\n    if (isReply) {\n      setComments(comments.map(comment => comment.id === parentId ? {\n        ...comment,\n        replies: comment.replies.map(reply => reply.id === commentId ? {\n          ...reply,\n          liked: !reply.liked,\n          likes: reply.liked ? reply.likes - 1 : reply.likes + 1\n        } : reply)\n      } : comment));\n    } else {\n      setComments(comments.map(comment => comment.id === commentId ? {\n        ...comment,\n        liked: !comment.liked,\n        likes: comment.liked ? comment.likes - 1 : comment.likes + 1\n      } : comment));\n    }\n  };\n  const formatTimeAgo = timestamp => {\n    const now = new Date();\n    const time = new Date(timestamp);\n    const diffInSeconds = Math.floor((now - time) / 1000);\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;\n    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;\n    return time.toLocaleDateString();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"video-lessons-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-lessons-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-main\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"header-icon\",\n            children: /*#__PURE__*/_jsxDEV(TbVideo, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"header-text\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              children: \"Video Lessons\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Watch educational videos to enhance your learning\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"level-display\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"current-level\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"level-label\",\n              children: \"Level:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"level-value\",\n              children: selectedLevel.charAt(0).toUpperCase() + selectedLevel.slice(1)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"current-class\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"class-label\",\n              children: \"Your Class:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"class-value\",\n              children: (user === null || user === void 0 ? void 0 : user.level) === 'primary' ? `Class ${(user === null || user === void 0 ? void 0 : user.class) || 'N/A'}` : (user === null || user === void 0 ? void 0 : user.level) === 'secondary' ? `Form ${(user === null || user === void 0 ? void 0 : user.class) || 'N/A'}` : (user === null || user === void 0 ? void 0 : user.level) === 'advance' ? `Form ${(user === null || user === void 0 ? void 0 : user.class) || 'N/A'}` : 'Not Set'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 401,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 400,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-lessons-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"video-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"controls-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"control-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"control-label\",\n              children: [/*#__PURE__*/_jsxDEV(TbFilter, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 17\n              }, this), isKiswahili ? 'Chuja kwa Darasa' : 'Filter by Class']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedClass,\n              onChange: e => setSelectedClass(e.target.value),\n              className: \"control-select class-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: isKiswahili ? 'Madarasa Yote' : 'All Classes'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 17\n              }, this), availableClasses.map(cls => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: cls,\n                children: selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${cls}` : `Class ${cls}` : `Form ${cls}`\n              }, cls, false, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"control-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"control-label\",\n              children: [/*#__PURE__*/_jsxDEV(TbFilter, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 460,\n                columnNumber: 17\n              }, this), \"Subject\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedSubject,\n              onChange: e => setSelectedSubject(e.target.value),\n              className: \"control-select subject-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: \"All Subjects\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 17\n              }, this), availableSubjects.map(subject => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: subject,\n                children: subject\n              }, subject, false, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"control-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"control-label\",\n              children: [/*#__PURE__*/_jsxDEV(TbSortAscending, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 17\n              }, this), \"Sort\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: sortBy,\n              onChange: e => setSortBy(e.target.value),\n              className: \"control-select sort-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"newest\",\n                children: \"Newest First\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"oldest\",\n                children: \"Oldest First\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 489,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"title\",\n                children: \"Title A-Z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 490,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"subject\",\n                children: \"Subject A-Z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-container\",\n            children: [/*#__PURE__*/_jsxDEV(TbSearch, {\n              className: \"search-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search videos by title, subject, or topic...\",\n              value: searchTerm,\n              onChange: e => setSearchTerm(e.target.value),\n              className: \"search-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 500,\n              columnNumber: 15\n            }, this), searchTerm && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleClearSearch,\n              className: \"clear-search-btn\",\n              children: [/*#__PURE__*/_jsxDEV(TbX, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 19\n              }, this), \"Clear Search\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 498,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleRefresh,\n            className: \"refresh-btn\",\n            children: [/*#__PURE__*/_jsxDEV(TbDownload, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 15\n            }, this), \"Refresh All\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 433,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 525,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: isKiswahili ? 'Inapakia video...' : 'Loading videos...'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 526,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 524,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-state\",\n        children: [/*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n          className: \"error-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 530,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: isKiswahili ? 'Hitilafu ya Kupakia Video' : 'Error Loading Videos'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 531,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 532,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchVideos,\n          className: \"retry-btn\",\n          children: isKiswahili ? 'Jaribu Tena' : 'Try Again'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 533,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 529,\n        columnNumber: 11\n      }, this) : filteredAndSortedVideos.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"videos-grid\",\n        children: filteredAndSortedVideos.map((video, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"video-card\",\n          onClick: () => handleShowVideo(index),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-card-thumbnail\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: getThumbnailUrl(video),\n              alt: video.title,\n              className: \"thumbnail-image\",\n              onError: e => {\n                // Fallback logic for failed thumbnails\n                if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                  // For YouTube videos, try different quality thumbnails\n                  let videoId = video.videoID;\n                  if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n                    const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                    videoId = match ? match[1] : videoId;\n                  }\n                  const fallbacks = [`https://img.youtube.com/vi/${videoId}/hqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/default.jpg`, '/api/placeholder/320/180'];\n                  const currentSrc = e.target.src;\n                  const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n                  if (currentIndex < fallbacks.length - 1) {\n                    e.target.src = fallbacks[currentIndex + 1];\n                  }\n                } else {\n                  e.target.src = '/api/placeholder/320/180';\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 542,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"play-overlay\",\n              children: /*#__PURE__*/_jsxDEV(FaPlayCircle, {\n                className: \"play-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 575,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 574,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-duration\",\n              children: video.duration || \"Video\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 577,\n              columnNumber: 19\n            }, this), video.subtitles && video.subtitles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"subtitle-badge\",\n              children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 582,\n                columnNumber: 23\n              }, this), \"CC\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 541,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-card-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"video-title\",\n              children: video.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 589,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-meta\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"video-subject\",\n                children: getSubjectName(video.subject)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 591,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"video-class\",\n                children: selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}` : `Form ${video.className || video.class}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 590,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-tags\",\n              children: [video.topic && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"topic-tag\",\n                children: video.topic\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 599,\n                columnNumber: 37\n              }, this), video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"shared-tag\",\n                children: [isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from ', selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}` : `Form ${video.sharedFromClass}`]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 601,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 598,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 588,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 540,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 538,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n          className: \"empty-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 614,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 615,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 616,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"suggestion\",\n          children: isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 617,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 613,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 431,\n      columnNumber: 7\n    }, this), showVideoIndices.length > 0 && currentVideoIndex !== null && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `video-overlay ${isVideoExpanded ? 'expanded' : ''}`,\n      onClick: e => {\n        if (e.target === e.currentTarget) handleHideVideo();\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `video-modal ${isVideoExpanded ? 'expanded' : ''}`,\n        children: ((_user$name3, _user$name3$charAt) => {\n          const video = filteredAndSortedVideos[currentVideoIndex];\n          if (!video) return /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"Video not found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 630,\n            columnNumber: 34\n          }, this);\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"video-title\",\n                  children: video.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 636,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-meta\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-subject\",\n                    children: video.subject\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 638,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-class\",\n                    children: [\"Class \", video.className]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 639,\n                    columnNumber: 25\n                  }, this), video.level && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-level\",\n                    children: video.level\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 640,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 637,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 635,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-controls\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"control-btn add-comment-btn\",\n                  onClick: () => {\n                    setCommentsExpanded(!commentsExpanded);\n                    if (!commentsExpanded && !isVideoExpanded) {\n                      toggleVideoExpansion();\n                    }\n                  },\n                  title: \"Add Comment\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"btn-icon\",\n                    children: \"\\uD83D\\uDCAC\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 654,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"btn-text\",\n                    children: \"Comment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 655,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 644,\n                  columnNumber: 23\n                }, this), isVideoExpanded && commentsExpanded && /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"control-btn close-comment-btn\",\n                  onClick: () => {\n                    setCommentsExpanded(false);\n                    toggleVideoExpansion();\n                  },\n                  title: \"Close Comments\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"btn-icon\",\n                    children: \"\\u2715\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 666,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"btn-text\",\n                    children: \"Close\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 667,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 658,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"control-btn close-btn\",\n                  onClick: handleHideVideo,\n                  title: \"Close video\",\n                  children: /*#__PURE__*/_jsxDEV(FaTimes, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 675,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 670,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 643,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 634,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `video-main-layout ${isVideoExpanded ? 'expanded-layout' : 'normal-layout'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-container\",\n                children: video.videoUrl ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    padding: '15px',\n                    background: '#000',\n                    borderRadius: '8px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"video\", {\n                    ref: ref => setVideoRef(ref),\n                    controls: true,\n                    autoPlay: true,\n                    playsInline: true,\n                    preload: \"metadata\",\n                    width: \"100%\",\n                    height: \"400\",\n                    poster: getThumbnailUrl(video),\n                    style: {\n                      width: '100%',\n                      height: '400px',\n                      backgroundColor: '#000'\n                    },\n                    onError: e => {\n                      setVideoError(`Failed to load video: ${video.title}. Please try refreshing the page.`);\n                    },\n                    onCanPlay: () => {\n                      setVideoError(null);\n                    },\n                    onLoadStart: () => {\n                      console.log('🎬 Video loading started');\n                    },\n                    crossOrigin: \"anonymous\",\n                    children: [/*#__PURE__*/_jsxDEV(\"source\", {\n                      src: video.signedVideoUrl || video.videoUrl,\n                      type: \"video/mp4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 712,\n                      columnNumber: 29\n                    }, this), video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => /*#__PURE__*/_jsxDEV(\"track\", {\n                      kind: \"subtitles\",\n                      src: subtitle.url,\n                      srcLang: subtitle.language,\n                      label: subtitle.languageName,\n                      default: subtitle.isDefault || index === 0\n                    }, `${subtitle.language}-${index}`, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 716,\n                      columnNumber: 31\n                    }, this)), \"Your browser does not support the video tag.\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 686,\n                    columnNumber: 27\n                  }, this), video.subtitles && video.subtitles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"subtitle-indicator\",\n                    children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {\n                      className: \"subtitle-icon\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 732,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [\"Subtitles available in \", video.subtitles.length, \" language(s)\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 733,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 731,\n                    columnNumber: 29\n                  }, this), videoError && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"video-error-overlay\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"error-content\",\n                      children: [/*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n                        className: \"error-icon\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 741,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: videoError\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 742,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => setVideoError(null),\n                        className: \"dismiss-error-btn\",\n                        children: \"Dismiss\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 743,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 740,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 739,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 685,\n                  columnNumber: 23\n                }, this) : video.videoID ?\n                /*#__PURE__*/\n                // Fallback to YouTube embed if no videoUrl\n                _jsxDEV(\"iframe\", {\n                  src: `https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`,\n                  title: video.title,\n                  frameBorder: \"0\",\n                  allowFullScreen: true,\n                  className: \"video-iframe\",\n                  onLoad: () => console.log('✅ YouTube iframe loaded')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 752,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-error\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"error-icon\",\n                    children: \"\\u26A0\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 762,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    children: \"Video Unavailable\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 763,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: videoError || \"This video cannot be played at the moment.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 764,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"error-actions\",\n                    children: /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: video.signedVideoUrl || video.videoUrl,\n                      target: \"_blank\",\n                      rel: \"noopener noreferrer\",\n                      className: \"external-link-btn\",\n                      children: \"\\uD83D\\uDCF1 Open in New Tab\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 766,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 765,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 761,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 683,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `comments-section-below ${isVideoExpanded ? 'expanded-comments' : 'normal-comments'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"comments-count-header\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"comments-count-display\",\n                    children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 784,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [comments.length, \" \", comments.length === 1 ? 'comment' : 'comments']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 785,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 783,\n                    columnNumber: 25\n                  }, this), !isVideoExpanded || !commentsExpanded ? /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      setCommentsExpanded(true);\n                      if (!isVideoExpanded) {\n                        toggleVideoExpansion();\n                      }\n                    },\n                    className: \"view-comments-btn\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"btn-icon\",\n                      children: \"\\uD83D\\uDC41\\uFE0F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 797,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"btn-text\",\n                      children: \"View\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 798,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 788,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setCommentsExpanded(false),\n                    className: \"comments-toggle-btn\",\n                    children: \"\\u25BC Minimize\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 801,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 782,\n                  columnNumber: 23\n                }, this), isVideoExpanded && commentsExpanded && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"comments-content maximized\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"add-comment\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"comment-input-container\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"user-avatar\",\n                        children: (user === null || user === void 0 ? void 0 : (_user$name3 = user.name) === null || _user$name3 === void 0 ? void 0 : (_user$name3$charAt = _user$name3.charAt(0)) === null || _user$name3$charAt === void 0 ? void 0 : _user$name3$charAt.toUpperCase()) || \"A\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 816,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"comment-input-wrapper\",\n                        children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n                          value: newComment,\n                          onChange: e => setNewComment(e.target.value),\n                          placeholder: \"Share your thoughts about this video...\",\n                          className: \"comment-input\",\n                          rows: \"3\",\n                          autoFocus: true\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 820,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: handleAddComment,\n                          className: \"comment-submit-btn\",\n                          disabled: !newComment.trim(),\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            children: \"\\uD83D\\uDCAC\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 833,\n                            columnNumber: 37\n                          }, this), \" Post Comment\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 828,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 819,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 815,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 814,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"comments-list\",\n                    children: comments.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"no-comments\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"no-comments-icon\",\n                        children: \"\\uD83D\\uDCAC\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 843,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"No comments yet. Be the first to share your thoughts!\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 844,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 842,\n                      columnNumber: 27\n                    }, this) : comments.map(comment => {\n                      var _comment$author, _comment$author$charA, _user$name4, _user$name4$charAt;\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"comment\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"comment-main\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"comment-avatar\",\n                            children: comment.avatar || ((_comment$author = comment.author) === null || _comment$author === void 0 ? void 0 : (_comment$author$charA = _comment$author.charAt(0)) === null || _comment$author$charA === void 0 ? void 0 : _comment$author$charA.toUpperCase()) || \"A\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 850,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"comment-content\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"comment-header\",\n                              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"comment-author\",\n                                children: comment.author\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 855,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"comment-time\",\n                                children: formatTimeAgo(comment.timestamp)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 856,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 854,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"comment-text\",\n                              children: comment.text\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 860,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"comment-actions\",\n                              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                                onClick: () => handleLikeComment(comment.id),\n                                className: `like-btn ${comment.liked ? 'liked' : ''}`,\n                                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                  children: comment.liked ? '❤️' : '🤍'\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 866,\n                                  columnNumber: 39\n                                }, this), comment.likes > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"like-count\",\n                                  children: comment.likes\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 867,\n                                  columnNumber: 61\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 862,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                                onClick: () => setReplyingTo(replyingTo === comment.id ? null : comment.id),\n                                className: \"reply-btn\",\n                                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                  children: \"\\uD83D\\uDCAC\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 873,\n                                  columnNumber: 39\n                                }, this), \" Reply\"]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 869,\n                                columnNumber: 37\n                              }, this), comment.replies.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"replies-count\",\n                                children: [comment.replies.length, \" \", comment.replies.length === 1 ? 'reply' : 'replies']\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 876,\n                                columnNumber: 39\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 861,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 853,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 849,\n                          columnNumber: 31\n                        }, this), replyingTo === comment.id && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"reply-input-container\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"reply-input-wrapper\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"user-avatar small\",\n                              children: (user === null || user === void 0 ? void 0 : (_user$name4 = user.name) === null || _user$name4 === void 0 ? void 0 : (_user$name4$charAt = _user$name4.charAt(0)) === null || _user$name4$charAt === void 0 ? void 0 : _user$name4$charAt.toUpperCase()) || \"A\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 888,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"reply-input-content\",\n                              children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n                                value: replyText,\n                                onChange: e => setReplyText(e.target.value),\n                                placeholder: `Reply to ${comment.author}...`,\n                                className: \"reply-input\",\n                                rows: \"2\",\n                                autoFocus: true\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 892,\n                                columnNumber: 39\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"reply-actions\",\n                                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                                  onClick: () => handleAddReply(comment.id),\n                                  className: \"reply-submit-btn\",\n                                  disabled: !replyText.trim(),\n                                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                    children: \"\\uD83D\\uDCAC\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 906,\n                                    columnNumber: 43\n                                  }, this), \" Reply\"]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 901,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                                  onClick: () => {\n                                    setReplyingTo(null);\n                                    setReplyText(\"\");\n                                  },\n                                  className: \"reply-cancel-btn\",\n                                  children: \"Cancel\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 908,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 900,\n                                columnNumber: 39\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 891,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 887,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 886,\n                          columnNumber: 33\n                        }, this), comment.replies.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"replies\",\n                          children: comment.replies.map(reply => {\n                            var _reply$author, _reply$author$charAt;\n                            return /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"reply\",\n                              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"reply-main\",\n                                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"reply-avatar\",\n                                  children: reply.avatar || ((_reply$author = reply.author) === null || _reply$author === void 0 ? void 0 : (_reply$author$charAt = _reply$author.charAt(0)) === null || _reply$author$charAt === void 0 ? void 0 : _reply$author$charAt.toUpperCase()) || \"A\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 929,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"reply-content\",\n                                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: \"reply-header\",\n                                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                      className: \"reply-author\",\n                                      children: reply.author\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 934,\n                                      columnNumber: 45\n                                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                      className: \"reply-time\",\n                                      children: formatTimeAgo(reply.timestamp)\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 935,\n                                      columnNumber: 45\n                                    }, this)]\n                                  }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 933,\n                                    columnNumber: 43\n                                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: \"reply-text\",\n                                    children: reply.text\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 939,\n                                    columnNumber: 43\n                                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: \"reply-actions\",\n                                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                                      onClick: () => handleLikeComment(reply.id, true, comment.id),\n                                      className: `like-btn small ${reply.liked ? 'liked' : ''}`,\n                                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                        children: reply.liked ? '❤️' : '🤍'\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 945,\n                                        columnNumber: 47\n                                      }, this), reply.likes > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                                        className: \"like-count\",\n                                        children: reply.likes\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 946,\n                                        columnNumber: 67\n                                      }, this)]\n                                    }, void 0, true, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 941,\n                                      columnNumber: 45\n                                    }, this)\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 940,\n                                    columnNumber: 43\n                                  }, this)]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 932,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 928,\n                                columnNumber: 39\n                              }, this)\n                            }, reply.id, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 927,\n                              columnNumber: 37\n                            }, this);\n                          })\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 925,\n                          columnNumber: 33\n                        }, this)]\n                      }, comment.id, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 848,\n                        columnNumber: 29\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 840,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 812,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 780,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 681,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 633,\n            columnNumber: 17\n          }, this);\n        })()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 627,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 624,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 398,\n    columnNumber: 5\n  }, this);\n}\n_s(VideoLessons, \"luj95VqrGLIvpKjmxp7mTen9L9I=\", false, function () {\n  return [useSelector, useLanguage, useDispatch];\n});\n_c = VideoLessons;\nexport default VideoLessons;\nvar _c;\n$RefreshReg$(_c, \"VideoLessons\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "motion", "AnimatePresence", "getStudyMaterial", "useDispatch", "useSelector", "HideLoading", "ShowLoading", "primarySubjects", "primaryKiswahiliSubjects", "secondarySubjects", "advanceSubjects", "useLanguage", "jsxDEV", "_jsxDEV", "IconComponents", "FaPlayCircle", "style", "fontSize", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "FaGraduationCap", "FaTimes", "FaExpand", "FaCompress", "TbVideo", "Tb<PERSON><PERSON>er", "TbSortAscending", "TbSearch", "TbX", "TbDownload", "TbAlertTriangle", "color", "TbInfoCircle", "VideoLessons", "_s", "user", "state", "t", "isKiswahili", "getClassName", "getSubjectName", "dispatch", "videos", "setVideos", "loading", "setLoading", "error", "setError", "selectedLevel", "setSelectedLevel", "level", "selectedClass", "setSelectedClass", "class", "selectedSubject", "setSelectedSubject", "searchTerm", "setSearchTerm", "sortBy", "setSortBy", "currentVideoIndex", "setCurrentVideoIndex", "showVideoIndices", "setShowVideoIndices", "isVideoExpanded", "setIsVideoExpanded", "videoError", "setVideoError", "videoRef", "setVideoRef", "videoComments", "setVideoComments", "newComment", "setNewComment", "replyingTo", "setReplyingTo", "replyText", "setReplyText", "showComments", "setShowComments", "commentsExpanded", "setCommentsExpanded", "getCurrentVideoComments", "currentVideo", "filteredAndSortedVideos", "id", "setCurrentVideoComments", "comments", "prev", "availableClasses", "availableSubjects", "fetchVideos", "_response$data", "filters", "className", "subject", "content", "response", "data", "success", "_response$data2", "message", "console", "filtered", "filter", "video", "videoClass", "trim", "searchLower", "toLowerCase", "_video$title", "_video$subject", "_video$topic", "title", "includes", "topic", "sorted", "sort", "a", "b", "Date", "createdAt", "localeCompare", "log", "length", "handleShowVideo", "index", "videoUrl", "signedUrl", "getSignedVideoUrl", "signedVideoUrl", "warn", "handleHideVideo", "pause", "toggleVideoExpansion", "fetch", "encodeURIComponent", "method", "headers", "ok", "Error", "status", "json", "getThumbnailUrl", "thumbnail", "videoID", "videoId", "match", "handleClearSearch", "handleRefresh", "handleClearAll", "handleAddComment", "_user$name", "_user$name$charAt", "comment", "now", "text", "author", "name", "avatar", "char<PERSON>t", "toUpperCase", "timestamp", "toISOString", "replies", "likes", "liked", "currentComments", "handleAddReply", "commentId", "_user$name2", "_user$name2$charAt", "reply", "setComments", "map", "handleLikeComment", "isReply", "parentId", "formatTimeAgo", "time", "diffInSeconds", "Math", "floor", "toLocaleDateString", "slice", "value", "onChange", "e", "target", "cls", "type", "placeholder", "onClick", "src", "alt", "onError", "fallbacks", "currentSrc", "currentIndex", "findIndex", "url", "split", "pop", "duration", "subtitles", "sharedFromClass", "currentTarget", "_user$name3", "_user$name3$charAt", "padding", "background", "borderRadius", "ref", "controls", "autoPlay", "playsInline", "preload", "width", "height", "poster", "backgroundColor", "onCanPlay", "onLoadStart", "crossOrigin", "subtitle", "kind", "srcLang", "language", "label", "languageName", "default", "isDefault", "frameBorder", "allowFullScreen", "onLoad", "href", "rel", "rows", "autoFocus", "disabled", "_comment$author", "_comment$author$charA", "_user$name4", "_user$name4$charAt", "_reply$author", "_reply$author$charAt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/VideoLessons/index.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo } from \"react\";\nimport \"./index.css\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getStudyMaterial } from \"../../../apicalls/study\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { primarySubjects, primaryKiswahiliSubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects.jsx\";\nimport { useLanguage } from \"../../../contexts/LanguageContext\";\n\n// Temporary fix: Use simple text/symbols instead of React Icons to avoid chunk loading issues\nconst IconComponents = {\n  FaPlayCircle: () => <span style={{fontSize: '24px'}}>▶️</span>,\n  FaGraduationCap: () => <span style={{fontSize: '24px'}}>🎓</span>,\n  FaTimes: () => <span style={{fontSize: '18px'}}>✕</span>,\n  FaExpand: () => <span style={{fontSize: '18px'}}>⛶</span>,\n  FaCompress: () => <span style={{fontSize: '18px'}}>⛶</span>,\n  TbVideo: () => <span style={{fontSize: '24px'}}>📹</span>,\n  TbFilter: () => <span style={{fontSize: '18px'}}>🔍</span>,\n  TbSortAscending: () => <span style={{fontSize: '18px'}}>↑</span>,\n  TbSearch: () => <span style={{fontSize: '18px'}}>🔍</span>,\n  TbX: () => <span style={{fontSize: '16px'}}>✕</span>,\n  TbDownload: () => <span style={{fontSize: '18px'}}>↻</span>,\n  TbAlertTriangle: () => <span style={{fontSize: '24px', color: '#ff6b6b'}}>⚠️</span>,\n  TbInfoCircle: () => <span style={{fontSize: '18px'}}>ℹ️</span>\n};\n\n// Destructure for easy use\nconst {\n  FaPlayCircle,\n  FaGraduationCap,\n  FaTimes,\n  FaExpand,\n  FaCompress,\n  TbVideo,\n  TbFilter,\n  TbSortAscending,\n  TbSearch,\n  TbX,\n  TbDownload,\n  TbAlertTriangle,\n  TbInfoCircle\n} = IconComponents;\n\nfunction VideoLessons() {\n  const { user } = useSelector((state) => state.user);\n  const { t, isKiswahili, getClassName, getSubjectName } = useLanguage();\n  const dispatch = useDispatch();\n\n  // State management\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [selectedLevel, setSelectedLevel] = useState(user?.level || \"primary\");\n  const [selectedClass, setSelectedClass] = useState(user?.class || \"all\");\n  const [selectedSubject, setSelectedSubject] = useState(\"all\");\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sortBy, setSortBy] = useState(\"newest\");\n\n  // Video player state\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [showVideoIndices, setShowVideoIndices] = useState([]);\n  const [isVideoExpanded, setIsVideoExpanded] = useState(false);\n  const [videoError, setVideoError] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n\n  // Comments state - store comments per video\n  const [videoComments, setVideoComments] = useState({});\n  const [newComment, setNewComment] = useState(\"\");\n  const [replyingTo, setReplyingTo] = useState(null);\n  const [replyText, setReplyText] = useState(\"\");\n  const [showComments, setShowComments] = useState(true);\n  const [commentsExpanded, setCommentsExpanded] = useState(false);\n\n  // Get comments for current video\n  const getCurrentVideoComments = () => {\n    if (currentVideoIndex === null) return [];\n    const currentVideo = filteredAndSortedVideos[currentVideoIndex];\n    if (!currentVideo) return [];\n    return videoComments[currentVideo.id] || [];\n  };\n\n  // Set comments for current video\n  const setCurrentVideoComments = (comments) => {\n    if (currentVideoIndex === null) return;\n    const currentVideo = filteredAndSortedVideos[currentVideoIndex];\n    if (!currentVideo) return;\n    setVideoComments(prev => ({\n      ...prev,\n      [currentVideo.id]: comments\n    }));\n  };\n\n  // Available classes based on level\n  const availableClasses = useMemo(() => {\n    if (selectedLevel === \"primary\" || selectedLevel === \"primary_kiswahili\") return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n    if (selectedLevel === \"secondary\") return [\"1\", \"2\", \"3\", \"4\"];\n    if (selectedLevel === \"advance\") return [\"5\", \"6\"];\n    return [];\n  }, [selectedLevel]);\n\n  // Available subjects based on level\n  const availableSubjects = useMemo(() => {\n    if (selectedLevel === \"primary\") return primarySubjects;\n    if (selectedLevel === \"primary_kiswahili\") return primaryKiswahiliSubjects;\n    if (selectedLevel === \"secondary\") return secondarySubjects;\n    if (selectedLevel === \"advance\") return advanceSubjects;\n    return [];\n  }, [selectedLevel]);\n\n  // Fetch videos\n  const fetchVideos = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      dispatch(ShowLoading());\n\n      const filters = {\n        level: selectedLevel,\n        className: \"all\", // Get all classes for the level\n        subject: \"all\", // Get all subjects for the level\n        content: \"videos\"\n      };\n\n      const response = await getStudyMaterial(filters);\n\n      if (response?.data?.success) {\n        setVideos(response.data.data || []);\n      } else {\n        setError(response?.data?.message || \"Failed to fetch videos\");\n        setVideos([]);\n      }\n    } catch (error) {\n      console.error(\"❌ Error fetching videos:\", error);\n      setError(\"Failed to load videos. Please try again.\");\n      setVideos([]);\n    } finally {\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  }, [selectedLevel, dispatch]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n\n\n    let filtered = videos;\n\n    // Apply level filter\n    filtered = filtered.filter(video => video.level === selectedLevel);\n\n    // Apply class filter\n    if (selectedClass !== \"all\") {\n      filtered = filtered.filter(video => {\n        // Check both className and class fields for compatibility\n        const videoClass = video.className || video.class;\n        return videoClass === selectedClass;\n      });\n    }\n\n    // Apply subject filter\n    if (selectedSubject !== \"all\") {\n      filtered = filtered.filter(video => video.subject === selectedSubject);\n    }\n\n    // Apply search filter\n    if (searchTerm.trim()) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(video =>\n        video.title?.toLowerCase().includes(searchLower) ||\n        video.subject?.toLowerCase().includes(searchLower) ||\n        video.topic?.toLowerCase().includes(searchLower)\n      );\n    }\n\n    // Apply sorting\n    const sorted = [...filtered].sort((a, b) => {\n      switch (sortBy) {\n        case \"newest\":\n          return new Date(b.createdAt || 0) - new Date(a.createdAt || 0);\n        case \"oldest\":\n          return new Date(a.createdAt || 0) - new Date(b.createdAt || 0);\n        case \"title\":\n          return (a.title || \"\").localeCompare(b.title || \"\");\n        case \"subject\":\n          return (a.subject || \"\").localeCompare(b.subject || \"\");\n        default:\n          return 0;\n      }\n    });\n\n    console.log('✅ Final filtered videos:', sorted.length);\n    if (sorted.length > 0) {\n      console.log('📹 Sample filtered video:', sorted[0]);\n    }\n\n    return sorted;\n  }, [videos, searchTerm, sortBy, selectedLevel, selectedClass, selectedSubject]);\n\n  // Video handlers\n  const handleShowVideo = async (index) => {\n    const video = filteredAndSortedVideos[index];\n\n    setCurrentVideoIndex(index);\n    setShowVideoIndices([index]);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video?.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        video.signedVideoUrl = signedUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  const handleHideVideo = () => {\n    setShowVideoIndices([]);\n    setCurrentVideoIndex(null);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n    if (videoRef) {\n      videoRef.pause();\n    }\n  };\n\n  const toggleVideoExpansion = () => {\n    setIsVideoExpanded(!isVideoExpanded);\n  };\n\n  // Get signed URL for S3 videos to ensure access\n  const getSignedVideoUrl = async (videoUrl) => {\n    if (!videoUrl) return videoUrl;\n\n    // For AWS S3 URLs, get signed URL from backend\n    if (videoUrl.includes('amazonaws.com') || videoUrl.includes('s3.')) {\n      try {\n        const response = await fetch(`http://localhost:5000/api/study/video-signed-url?videoUrl=${encodeURIComponent(videoUrl)}`, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json',\n          }\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const data = await response.json();\n\n        if (data.success && data.signedUrl) {\n          console.log('✅ Got signed URL for S3 video');\n          return data.signedUrl;\n        } else {\n          console.warn('⚠️ Invalid response from signed URL endpoint:', data);\n          return videoUrl;\n        }\n      } catch (error) {\n        console.error('❌ Error getting signed URL:', error);\n        return videoUrl;\n      }\n    }\n\n    return videoUrl;\n  };\n\n  // Get thumbnail URL\n  const getThumbnailUrl = (video) => {\n    if (video.thumbnail) {\n      return video.thumbnail;\n    }\n    \n    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n      let videoId = video.videoID;\n      if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n        const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n        videoId = match ? match[1] : videoId;\n      }\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n    \n    return '/api/placeholder/400/225';\n  };\n\n  // Effects\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n\n  useEffect(() => {\n    if (user?.level) {\n      setSelectedLevel(user.level);\n    }\n    if (user?.class) {\n      setSelectedClass(user.class);\n    }\n  }, [user]);\n\n  // Clear search and refresh\n  const handleClearSearch = () => {\n    setSearchTerm(\"\");\n  };\n\n  const handleRefresh = () => {\n    // Only refresh data, don't clear filters or search\n    fetchVideos();\n  };\n\n  const handleClearAll = () => {\n    setSearchTerm(\"\");\n    setSelectedSubject(\"all\");\n    setSelectedClass(\"all\");\n    fetchVideos();\n  };\n\n  // Comment functions\n  const handleAddComment = () => {\n    if (newComment.trim()) {\n      const comment = {\n        id: Date.now(),\n        text: newComment,\n        author: user?.name || \"Anonymous\",\n        avatar: user?.name?.charAt(0)?.toUpperCase() || \"A\",\n        timestamp: new Date().toISOString(),\n        replies: [],\n        likes: 0,\n        liked: false\n      };\n      const currentComments = getCurrentVideoComments();\n      setCurrentVideoComments([comment, ...currentComments]); // Add new comments at the top\n      setNewComment(\"\");\n    }\n  };\n\n\n\n  const handleAddReply = (commentId) => {\n    if (replyText.trim()) {\n      const reply = {\n        id: Date.now(),\n        text: replyText,\n        author: user?.name || \"Anonymous\",\n        avatar: user?.name?.charAt(0)?.toUpperCase() || \"A\",\n        timestamp: new Date().toISOString(),\n        likes: 0,\n        liked: false\n      };\n\n      setComments(comments.map(comment =>\n        comment.id === commentId\n          ? { ...comment, replies: [...comment.replies, reply] }\n          : comment\n      ));\n      setReplyText(\"\");\n      setReplyingTo(null);\n    }\n  };\n\n  const handleLikeComment = (commentId, isReply = false, parentId = null) => {\n    if (isReply) {\n      setComments(comments.map(comment =>\n        comment.id === parentId\n          ? {\n              ...comment,\n              replies: comment.replies.map(reply =>\n                reply.id === commentId\n                  ? { ...reply, liked: !reply.liked, likes: reply.liked ? reply.likes - 1 : reply.likes + 1 }\n                  : reply\n              )\n            }\n          : comment\n      ));\n    } else {\n      setComments(comments.map(comment =>\n        comment.id === commentId\n          ? { ...comment, liked: !comment.liked, likes: comment.liked ? comment.likes - 1 : comment.likes + 1 }\n          : comment\n      ));\n    }\n  };\n\n  const formatTimeAgo = (timestamp) => {\n    const now = new Date();\n    const time = new Date(timestamp);\n    const diffInSeconds = Math.floor((now - time) / 1000);\n\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;\n    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;\n    return time.toLocaleDateString();\n  };\n\n  return (\n    <div className=\"video-lessons-container\">\n      {/* Enhanced Header with Level Display */}\n      <div className=\"video-lessons-header\">\n        <div className=\"header-content\">\n          <div className=\"header-main\">\n            <div className=\"header-icon\">\n              <TbVideo />\n            </div>\n            <div className=\"header-text\">\n              <h1>Video Lessons</h1>\n              <p>Watch educational videos to enhance your learning</p>\n            </div>\n          </div>\n\n          {/* Level and Class Display */}\n          <div className=\"level-display\">\n            <div className=\"current-level\">\n              <span className=\"level-label\">Level:</span>\n              <span className=\"level-value\">{selectedLevel.charAt(0).toUpperCase() + selectedLevel.slice(1)}</span>\n            </div>\n            <div className=\"current-class\">\n              <span className=\"class-label\">Your Class:</span>\n              <span className=\"class-value\">\n                {user?.level === 'primary' ? `Class ${user?.class || 'N/A'}` :\n                 user?.level === 'secondary' ? `Form ${user?.class || 'N/A'}` :\n                 user?.level === 'advance' ? `Form ${user?.class || 'N/A'}` :\n                 'Not Set'}\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"video-lessons-content\">\n        {/* Enhanced Filters and Controls */}\n        <div className=\"video-controls\">\n          <div className=\"controls-row\">\n            {/* Class Filter */}\n            <div className=\"control-group\">\n              <label className=\"control-label\">\n                <TbFilter />\n                {isKiswahili ? 'Chuja kwa Darasa' : 'Filter by Class'}\n              </label>\n              <select\n                value={selectedClass}\n                onChange={(e) => setSelectedClass(e.target.value)}\n                className=\"control-select class-select\"\n              >\n                <option value=\"all\">{isKiswahili ? 'Madarasa Yote' : 'All Classes'}</option>\n                {availableClasses.map((cls) => (\n                  <option key={cls} value={cls}>\n                    {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ?\n                      (isKiswahili ? `Darasa la ${cls}` : `Class ${cls}`) :\n                      `Form ${cls}`}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Subject Filter */}\n            <div className=\"control-group\">\n              <label className=\"control-label\">\n                <TbFilter />\n                Subject\n              </label>\n              <select\n                value={selectedSubject}\n                onChange={(e) => setSelectedSubject(e.target.value)}\n                className=\"control-select subject-select\"\n              >\n                <option value=\"all\">All Subjects</option>\n                {availableSubjects.map((subject) => (\n                  <option key={subject} value={subject}>\n                    {subject}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Sort */}\n            <div className=\"control-group\">\n              <label className=\"control-label\">\n                <TbSortAscending />\n                Sort\n              </label>\n              <select\n                value={sortBy}\n                onChange={(e) => setSortBy(e.target.value)}\n                className=\"control-select sort-select\"\n              >\n                <option value=\"newest\">Newest First</option>\n                <option value=\"oldest\">Oldest First</option>\n                <option value=\"title\">Title A-Z</option>\n                <option value=\"subject\">Subject A-Z</option>\n              </select>\n            </div>\n          </div>\n\n          {/* Search Row */}\n          <div className=\"search-row\">\n            <div className=\"search-container\">\n              <TbSearch className=\"search-icon\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search videos by title, subject, or topic...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"search-input\"\n              />\n              {searchTerm && (\n                <button onClick={handleClearSearch} className=\"clear-search-btn\">\n                  <TbX />\n                  Clear Search\n                </button>\n              )}\n            </div>\n\n            <button onClick={handleRefresh} className=\"refresh-btn\">\n              <TbDownload />\n              Refresh All\n            </button>\n          </div>\n        </div>\n\n        {/* Loading State */}\n        {loading ? (\n          <div className=\"loading-state\">\n            <div className=\"loading-spinner\"></div>\n            <p>{isKiswahili ? 'Inapakia video...' : 'Loading videos...'}</p>\n          </div>\n        ) : error ? (\n          <div className=\"error-state\">\n            <TbAlertTriangle className=\"error-icon\" />\n            <h3>{isKiswahili ? 'Hitilafu ya Kupakia Video' : 'Error Loading Videos'}</h3>\n            <p>{error}</p>\n            <button onClick={fetchVideos} className=\"retry-btn\">\n              {isKiswahili ? 'Jaribu Tena' : 'Try Again'}\n            </button>\n          </div>\n        ) : filteredAndSortedVideos.length > 0 ? (\n          <div className=\"videos-grid\">\n            {filteredAndSortedVideos.map((video, index) => (\n              <div key={index} className=\"video-card\" onClick={() => handleShowVideo(index)}>\n                <div className=\"video-card-thumbnail\">\n                  <img\n                    src={getThumbnailUrl(video)}\n                    alt={video.title}\n                    className=\"thumbnail-image\"\n                    onError={(e) => {\n                      // Fallback logic for failed thumbnails\n                      if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                        // For YouTube videos, try different quality thumbnails\n                        let videoId = video.videoID;\n                        if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n                          const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                          videoId = match ? match[1] : videoId;\n                        }\n\n                        const fallbacks = [\n                          `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,\n                          `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`,\n                          `https://img.youtube.com/vi/${videoId}/default.jpg`,\n                          '/api/placeholder/320/180'\n                        ];\n\n                        const currentSrc = e.target.src;\n                        const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n\n                        if (currentIndex < fallbacks.length - 1) {\n                          e.target.src = fallbacks[currentIndex + 1];\n                        }\n                      } else {\n                        e.target.src = '/api/placeholder/320/180';\n                      }\n                    }}\n                  />\n                  <div className=\"play-overlay\">\n                    <FaPlayCircle className=\"play-icon\" />\n                  </div>\n                  <div className=\"video-duration\">\n                    {video.duration || \"Video\"}\n                  </div>\n                  {video.subtitles && video.subtitles.length > 0 && (\n                    <div className=\"subtitle-badge\">\n                      <TbInfoCircle />\n                      CC\n                    </div>\n                  )}\n                </div>\n\n                <div className=\"video-card-content\">\n                  <h3 className=\"video-title\">{video.title}</h3>\n                  <div className=\"video-meta\">\n                    <span className=\"video-subject\">{getSubjectName(video.subject)}</span>\n                    <span className=\"video-class\">\n                      {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ?\n                        (isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}`) :\n                        `Form ${video.className || video.class}`}\n                    </span>\n                  </div>\n                  <div className=\"video-tags\">\n                    {video.topic && <span className=\"topic-tag\">{video.topic}</span>}\n                    {video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && (\n                      <span className=\"shared-tag\">\n                        {isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from '}{selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ?\n                          (isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}`) :\n                          `Form ${video.sharedFromClass}`}\n                      </span>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"empty-state\">\n            <FaGraduationCap className=\"empty-icon\" />\n            <h3>{isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'}</h3>\n            <p>{isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'}</p>\n            <p className=\"suggestion\">{isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'}</p>\n          </div>\n        )}\n      </div>\n\n      {/* Enhanced Video Display */}\n      {showVideoIndices.length > 0 && currentVideoIndex !== null && (\n        <div className={`video-overlay ${isVideoExpanded ? 'expanded' : ''}`} onClick={(e) => {\n          if (e.target === e.currentTarget) handleHideVideo();\n        }}>\n          <div className={`video-modal ${isVideoExpanded ? 'expanded' : ''}`}>\n            {(() => {\n              const video = filteredAndSortedVideos[currentVideoIndex];\n              if (!video) return <div>Video not found</div>;\n\n              return (\n                <div className=\"video-content\">\n                  <div className=\"video-header\">\n                    <div className=\"video-info\">\n                      <h3 className=\"video-title\">{video.title}</h3>\n                      <div className=\"video-meta\">\n                        <span className=\"video-subject\">{video.subject}</span>\n                        <span className=\"video-class\">Class {video.className}</span>\n                        {video.level && <span className=\"video-level\">{video.level}</span>}\n                      </div>\n                    </div>\n                    <div className=\"video-controls\">\n                      <button\n                        className=\"control-btn add-comment-btn\"\n                        onClick={() => {\n                          setCommentsExpanded(!commentsExpanded);\n                          if (!commentsExpanded && !isVideoExpanded) {\n                            toggleVideoExpansion();\n                          }\n                        }}\n                        title=\"Add Comment\"\n                      >\n                        <span className=\"btn-icon\">💬</span>\n                        <span className=\"btn-text\">Comment</span>\n                      </button>\n                      {(isVideoExpanded && commentsExpanded) && (\n                        <button\n                          className=\"control-btn close-comment-btn\"\n                          onClick={() => {\n                            setCommentsExpanded(false);\n                            toggleVideoExpansion();\n                          }}\n                          title=\"Close Comments\"\n                        >\n                          <span className=\"btn-icon\">✕</span>\n                          <span className=\"btn-text\">Close</span>\n                        </button>\n                      )}\n                      <button\n                        className=\"control-btn close-btn\"\n                        onClick={handleHideVideo}\n                        title=\"Close video\"\n                      >\n                        <FaTimes />\n                      </button>\n                    </div>\n                  </div>\n\n                  {/* Video and Comments Layout */}\n                  <div className={`video-main-layout ${isVideoExpanded ? 'expanded-layout' : 'normal-layout'}`}>\n                    {/* Video Container */}\n                    <div className=\"video-container\">\n                    {video.videoUrl ? (\n                      <div style={{ padding: '15px', background: '#000', borderRadius: '8px' }}>\n                          <video\n                            ref={(ref) => setVideoRef(ref)}\n                            controls\n                            autoPlay\n                            playsInline\n                            preload=\"metadata\"\n                            width=\"100%\"\n                            height=\"400\"\n                            poster={getThumbnailUrl(video)}\n                            style={{\n                              width: '100%',\n                              height: '400px',\n                              backgroundColor: '#000'\n                            }}\n                            onError={(e) => {\n                              setVideoError(`Failed to load video: ${video.title}. Please try refreshing the page.`);\n                            }}\n                            onCanPlay={() => {\n                              setVideoError(null);\n                            }}\n                            onLoadStart={() => {\n                              console.log('🎬 Video loading started');\n                            }}\n                            crossOrigin=\"anonymous\"\n                          >\n                            {/* Use signed URL if available, otherwise use original URL */}\n                            <source src={video.signedVideoUrl || video.videoUrl} type=\"video/mp4\" />\n\n                            {/* Add subtitle tracks if available */}\n                            {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => (\n                              <track\n                                key={`${subtitle.language}-${index}`}\n                                kind=\"subtitles\"\n                                src={subtitle.url}\n                                srcLang={subtitle.language}\n                                label={subtitle.languageName}\n                                default={subtitle.isDefault || index === 0}\n                              />\n                            ))}\n\n                            Your browser does not support the video tag.\n                          </video>\n\n                          {/* Subtitle indicator */}\n                          {video.subtitles && video.subtitles.length > 0 && (\n                            <div className=\"subtitle-indicator\">\n                              <TbInfoCircle className=\"subtitle-icon\" />\n                              <span>Subtitles available in {video.subtitles.length} language(s)</span>\n                            </div>\n                          )}\n\n                          {/* Video error display */}\n                          {videoError && (\n                            <div className=\"video-error-overlay\">\n                              <div className=\"error-content\">\n                                <TbAlertTriangle className=\"error-icon\" />\n                                <p>{videoError}</p>\n                                <button onClick={() => setVideoError(null)} className=\"dismiss-error-btn\">\n                                  Dismiss\n                                </button>\n                              </div>\n                            </div>\n                          )}\n                        </div>\n                    ) : video.videoID ? (\n                      // Fallback to YouTube embed if no videoUrl\n                      <iframe\n                        src={`https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`}\n                        title={video.title}\n                        frameBorder=\"0\"\n                        allowFullScreen\n                        className=\"video-iframe\"\n                        onLoad={() => console.log('✅ YouTube iframe loaded')}\n                      ></iframe>\n                    ) : (\n                      <div className=\"video-error\">\n                        <div className=\"error-icon\">⚠️</div>\n                        <h3>Video Unavailable</h3>\n                        <p>{videoError || \"This video cannot be played at the moment.\"}</p>\n                        <div className=\"error-actions\">\n                          <a\n                            href={video.signedVideoUrl || video.videoUrl}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"external-link-btn\"\n                          >\n                            📱 Open in New Tab\n                          </a>\n                        </div>\n                      </div>\n                    )}\n                    </div>\n\n                    {/* Comments Section - Always visible */}\n                    <div className={`comments-section-below ${isVideoExpanded ? 'expanded-comments' : 'normal-comments'}`}>\n                      {/* Comments Count - Always visible at top */}\n                      <div className=\"comments-count-header\">\n                        <div className=\"comments-count-display\">\n                          <TbInfoCircle />\n                          <span>{comments.length} {comments.length === 1 ? 'comment' : 'comments'}</span>\n                        </div>\n                        {!isVideoExpanded || !commentsExpanded ? (\n                          <button\n                            onClick={() => {\n                              setCommentsExpanded(true);\n                              if (!isVideoExpanded) {\n                                toggleVideoExpansion();\n                              }\n                            }}\n                            className=\"view-comments-btn\"\n                          >\n                            <span className=\"btn-icon\">👁️</span>\n                            <span className=\"btn-text\">View</span>\n                          </button>\n                        ) : (\n                          <button\n                            onClick={() => setCommentsExpanded(false)}\n                            className=\"comments-toggle-btn\"\n                          >\n                            ▼ Minimize\n                          </button>\n                        )}\n                      </div>\n\n                      {/* Comments Content - Show when expanded */}\n                      {(isVideoExpanded && commentsExpanded) && (\n                        <div className=\"comments-content maximized\">\n                            {/* Add Comment */}\n                            <div className=\"add-comment\">\n                              <div className=\"comment-input-container\">\n                                <div className=\"user-avatar\">\n                                  {user?.name?.charAt(0)?.toUpperCase() || \"A\"}\n                                </div>\n                                <div className=\"comment-input-wrapper\">\n                                  <textarea\n                                    value={newComment}\n                                    onChange={(e) => setNewComment(e.target.value)}\n                                    placeholder=\"Share your thoughts about this video...\"\n                                    className=\"comment-input\"\n                                    rows=\"3\"\n                                    autoFocus\n                                  />\n                                  <button\n                                    onClick={handleAddComment}\n                                    className=\"comment-submit-btn\"\n                                    disabled={!newComment.trim()}\n                                  >\n                                    <span>💬</span> Post Comment\n                                  </button>\n                                </div>\n                              </div>\n                            </div>\n\n                      {/* Comments List */}\n                      <div className=\"comments-list\">\n                        {comments.length === 0 ? (\n                          <div className=\"no-comments\">\n                            <div className=\"no-comments-icon\">💬</div>\n                            <p>No comments yet. Be the first to share your thoughts!</p>\n                          </div>\n                        ) : (\n                          comments.map((comment) => (\n                            <div key={comment.id} className=\"comment\">\n                              <div className=\"comment-main\">\n                                <div className=\"comment-avatar\">\n                                  {comment.avatar || comment.author?.charAt(0)?.toUpperCase() || \"A\"}\n                                </div>\n                                <div className=\"comment-content\">\n                                  <div className=\"comment-header\">\n                                    <span className=\"comment-author\">{comment.author}</span>\n                                    <span className=\"comment-time\">\n                                      {formatTimeAgo(comment.timestamp)}\n                                    </span>\n                                  </div>\n                                  <div className=\"comment-text\">{comment.text}</div>\n                                  <div className=\"comment-actions\">\n                                    <button\n                                      onClick={() => handleLikeComment(comment.id)}\n                                      className={`like-btn ${comment.liked ? 'liked' : ''}`}\n                                    >\n                                      <span>{comment.liked ? '❤️' : '🤍'}</span>\n                                      {comment.likes > 0 && <span className=\"like-count\">{comment.likes}</span>}\n                                    </button>\n                                    <button\n                                      onClick={() => setReplyingTo(replyingTo === comment.id ? null : comment.id)}\n                                      className=\"reply-btn\"\n                                    >\n                                      <span>💬</span> Reply\n                                    </button>\n                                    {comment.replies.length > 0 && (\n                                      <span className=\"replies-count\">\n                                        {comment.replies.length} {comment.replies.length === 1 ? 'reply' : 'replies'}\n                                      </span>\n                                    )}\n                                  </div>\n                                </div>\n                              </div>\n\n                              {/* Reply Input */}\n                              {replyingTo === comment.id && (\n                                <div className=\"reply-input-container\">\n                                  <div className=\"reply-input-wrapper\">\n                                    <div className=\"user-avatar small\">\n                                      {user?.name?.charAt(0)?.toUpperCase() || \"A\"}\n                                    </div>\n                                    <div className=\"reply-input-content\">\n                                      <textarea\n                                        value={replyText}\n                                        onChange={(e) => setReplyText(e.target.value)}\n                                        placeholder={`Reply to ${comment.author}...`}\n                                        className=\"reply-input\"\n                                        rows=\"2\"\n                                        autoFocus\n                                      />\n                                      <div className=\"reply-actions\">\n                                        <button\n                                          onClick={() => handleAddReply(comment.id)}\n                                          className=\"reply-submit-btn\"\n                                          disabled={!replyText.trim()}\n                                        >\n                                          <span>💬</span> Reply\n                                        </button>\n                                        <button\n                                          onClick={() => {\n                                            setReplyingTo(null);\n                                            setReplyText(\"\");\n                                          }}\n                                          className=\"reply-cancel-btn\"\n                                        >\n                                          Cancel\n                                        </button>\n                                      </div>\n                                    </div>\n                                  </div>\n                                </div>\n                              )}\n\n                              {/* Replies */}\n                              {comment.replies.length > 0 && (\n                                <div className=\"replies\">\n                                  {comment.replies.map((reply) => (\n                                    <div key={reply.id} className=\"reply\">\n                                      <div className=\"reply-main\">\n                                        <div className=\"reply-avatar\">\n                                          {reply.avatar || reply.author?.charAt(0)?.toUpperCase() || \"A\"}\n                                        </div>\n                                        <div className=\"reply-content\">\n                                          <div className=\"reply-header\">\n                                            <span className=\"reply-author\">{reply.author}</span>\n                                            <span className=\"reply-time\">\n                                              {formatTimeAgo(reply.timestamp)}\n                                            </span>\n                                          </div>\n                                          <div className=\"reply-text\">{reply.text}</div>\n                                          <div className=\"reply-actions\">\n                                            <button\n                                              onClick={() => handleLikeComment(reply.id, true, comment.id)}\n                                              className={`like-btn small ${reply.liked ? 'liked' : ''}`}\n                                            >\n                                              <span>{reply.liked ? '❤️' : '🤍'}</span>\n                                              {reply.likes > 0 && <span className=\"like-count\">{reply.likes}</span>}\n                                            </button>\n                                          </div>\n                                        </div>\n                                      </div>\n                                    </div>\n                                  ))}\n                                </div>\n                              )}\n                            </div>\n                          ))\n                        )}\n                      </div>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                  {/* End of video-main-layout */}\n                </div>\n              );\n            })()}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport default VideoLessons;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACxE,OAAO,aAAa;AACpB,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,eAAe,EAAEC,wBAAwB,EAAEC,iBAAiB,EAAEC,eAAe,QAAQ,4BAA4B;AAC1H,SAASC,WAAW,QAAQ,mCAAmC;;AAE/D;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,cAAc,GAAG;EACrBC,YAAY,EAAEA,CAAA,kBAAMF,OAAA;IAAMG,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAC9DC,eAAe,EAAEA,CAAA,kBAAMV,OAAA;IAAMG,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACjEE,OAAO,EAAEA,CAAA,kBAAMX,OAAA;IAAMG,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACxDG,QAAQ,EAAEA,CAAA,kBAAMZ,OAAA;IAAMG,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACzDI,UAAU,EAAEA,CAAA,kBAAMb,OAAA;IAAMG,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAC3DK,OAAO,EAAEA,CAAA,kBAAMd,OAAA;IAAMG,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACzDM,QAAQ,EAAEA,CAAA,kBAAMf,OAAA;IAAMG,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAC1DO,eAAe,EAAEA,CAAA,kBAAMhB,OAAA;IAAMG,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAChEQ,QAAQ,EAAEA,CAAA,kBAAMjB,OAAA;IAAMG,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAC1DS,GAAG,EAAEA,CAAA,kBAAMlB,OAAA;IAAMG,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACpDU,UAAU,EAAEA,CAAA,kBAAMnB,OAAA;IAAMG,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAC3DW,eAAe,EAAEA,CAAA,kBAAMpB,OAAA;IAAMG,KAAK,EAAE;MAACC,QAAQ,EAAE,MAAM;MAAEiB,KAAK,EAAE;IAAS,CAAE;IAAAhB,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACnFa,YAAY,EAAEA,CAAA,kBAAMtB,OAAA;IAAMG,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM;AAC/D,CAAC;;AAED;AACA,MAAM;EACJP,YAAY;EACZQ,eAAe;EACfC,OAAO;EACPC,QAAQ;EACRC,UAAU;EACVC,OAAO;EACPC,QAAQ;EACRC,eAAe;EACfC,QAAQ;EACRC,GAAG;EACHC,UAAU;EACVC,eAAe;EACfE;AACF,CAAC,GAAGrB,cAAc;AAElB,SAASsB,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM;IAAEC;EAAK,CAAC,GAAGlC,WAAW,CAAEmC,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE,CAAC;IAAEC,WAAW;IAAEC,YAAY;IAAEC;EAAe,CAAC,GAAGhC,WAAW,CAAC,CAAC;EACtE,MAAMiC,QAAQ,GAAGzC,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAAC0C,MAAM,EAAEC,SAAS,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACmD,OAAO,EAAEC,UAAU,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqD,KAAK,EAAEC,QAAQ,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACuD,aAAa,EAAEC,gBAAgB,CAAC,GAAGxD,QAAQ,CAAC,CAAA0C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,KAAK,KAAI,SAAS,CAAC;EAC5E,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG3D,QAAQ,CAAC,CAAA0C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,KAAK,KAAI,KAAK,CAAC;EACxE,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC+D,UAAU,EAAEC,aAAa,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiE,MAAM,EAAEC,SAAS,CAAC,GAAGlE,QAAQ,CAAC,QAAQ,CAAC;;EAE9C;EACA,MAAM,CAACmE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpE,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACqE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtE,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACuE,eAAe,EAAEC,kBAAkB,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACyE,UAAU,EAAEC,aAAa,CAAC,GAAG1E,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC2E,QAAQ,EAAEC,WAAW,CAAC,GAAG5E,QAAQ,CAAC,IAAI,CAAC;;EAE9C;EACA,MAAM,CAAC6E,aAAa,EAAEC,gBAAgB,CAAC,GAAG9E,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAAC+E,UAAU,EAAEC,aAAa,CAAC,GAAGhF,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiF,UAAU,EAAEC,aAAa,CAAC,GAAGlF,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACmF,SAAS,EAAEC,YAAY,CAAC,GAAGpF,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACqF,YAAY,EAAEC,eAAe,CAAC,GAAGtF,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACuF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxF,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAMyF,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAItB,iBAAiB,KAAK,IAAI,EAAE,OAAO,EAAE;IACzC,MAAMuB,YAAY,GAAGC,uBAAuB,CAACxB,iBAAiB,CAAC;IAC/D,IAAI,CAACuB,YAAY,EAAE,OAAO,EAAE;IAC5B,OAAOb,aAAa,CAACa,YAAY,CAACE,EAAE,CAAC,IAAI,EAAE;EAC7C,CAAC;;EAED;EACA,MAAMC,uBAAuB,GAAIC,QAAQ,IAAK;IAC5C,IAAI3B,iBAAiB,KAAK,IAAI,EAAE;IAChC,MAAMuB,YAAY,GAAGC,uBAAuB,CAACxB,iBAAiB,CAAC;IAC/D,IAAI,CAACuB,YAAY,EAAE;IACnBZ,gBAAgB,CAACiB,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP,CAACL,YAAY,CAACE,EAAE,GAAGE;IACrB,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAME,gBAAgB,GAAG7F,OAAO,CAAC,MAAM;IACrC,IAAIoD,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IACpH,IAAIA,aAAa,KAAK,WAAW,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAC9D,IAAIA,aAAa,KAAK,SAAS,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;IAClD,OAAO,EAAE;EACX,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAM0C,iBAAiB,GAAG9F,OAAO,CAAC,MAAM;IACtC,IAAIoD,aAAa,KAAK,SAAS,EAAE,OAAO5C,eAAe;IACvD,IAAI4C,aAAa,KAAK,mBAAmB,EAAE,OAAO3C,wBAAwB;IAC1E,IAAI2C,aAAa,KAAK,WAAW,EAAE,OAAO1C,iBAAiB;IAC3D,IAAI0C,aAAa,KAAK,SAAS,EAAE,OAAOzC,eAAe;IACvD,OAAO,EAAE;EACX,CAAC,EAAE,CAACyC,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAM2C,WAAW,GAAGhG,WAAW,CAAC,YAAY;IAC1C,IAAI;MAAA,IAAAiG,cAAA;MACF/C,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MACdN,QAAQ,CAACtC,WAAW,CAAC,CAAC,CAAC;MAEvB,MAAM0F,OAAO,GAAG;QACd3C,KAAK,EAAEF,aAAa;QACpB8C,SAAS,EAAE,KAAK;QAAE;QAClBC,OAAO,EAAE,KAAK;QAAE;QAChBC,OAAO,EAAE;MACX,CAAC;MAED,MAAMC,QAAQ,GAAG,MAAMlG,gBAAgB,CAAC8F,OAAO,CAAC;MAEhD,IAAII,QAAQ,aAARA,QAAQ,gBAAAL,cAAA,GAARK,QAAQ,CAAEC,IAAI,cAAAN,cAAA,eAAdA,cAAA,CAAgBO,OAAO,EAAE;QAC3BxD,SAAS,CAACsD,QAAQ,CAACC,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;MACrC,CAAC,MAAM;QAAA,IAAAE,eAAA;QACLrD,QAAQ,CAAC,CAAAkD,QAAQ,aAARA,QAAQ,wBAAAG,eAAA,GAARH,QAAQ,CAAEC,IAAI,cAAAE,eAAA,uBAAdA,eAAA,CAAgBC,OAAO,KAAI,wBAAwB,CAAC;QAC7D1D,SAAS,CAAC,EAAE,CAAC;MACf;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdwD,OAAO,CAACxD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDC,QAAQ,CAAC,0CAA0C,CAAC;MACpDJ,SAAS,CAAC,EAAE,CAAC;IACf,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;MACjBJ,QAAQ,CAACvC,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAAC8C,aAAa,EAAEP,QAAQ,CAAC,CAAC;;EAE7B;EACA,MAAM2C,uBAAuB,GAAGxF,OAAO,CAAC,MAAM;IAG5C,IAAI2G,QAAQ,GAAG7D,MAAM;;IAErB;IACA6D,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACvD,KAAK,KAAKF,aAAa,CAAC;;IAElE;IACA,IAAIG,aAAa,KAAK,KAAK,EAAE;MAC3BoD,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,KAAK,IAAI;QAClC;QACA,MAAMC,UAAU,GAAGD,KAAK,CAACX,SAAS,IAAIW,KAAK,CAACpD,KAAK;QACjD,OAAOqD,UAAU,KAAKvD,aAAa;MACrC,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIG,eAAe,KAAK,KAAK,EAAE;MAC7BiD,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACV,OAAO,KAAKzC,eAAe,CAAC;IACxE;;IAEA;IACA,IAAIE,UAAU,CAACmD,IAAI,CAAC,CAAC,EAAE;MACrB,MAAMC,WAAW,GAAGpD,UAAU,CAACqD,WAAW,CAAC,CAAC;MAC5CN,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,KAAK;QAAA,IAAAK,YAAA,EAAAC,cAAA,EAAAC,YAAA;QAAA,OAC9B,EAAAF,YAAA,GAAAL,KAAK,CAACQ,KAAK,cAAAH,YAAA,uBAAXA,YAAA,CAAaD,WAAW,CAAC,CAAC,CAACK,QAAQ,CAACN,WAAW,CAAC,OAAAG,cAAA,GAChDN,KAAK,CAACV,OAAO,cAAAgB,cAAA,uBAAbA,cAAA,CAAeF,WAAW,CAAC,CAAC,CAACK,QAAQ,CAACN,WAAW,CAAC,OAAAI,YAAA,GAClDP,KAAK,CAACU,KAAK,cAAAH,YAAA,uBAAXA,YAAA,CAAaH,WAAW,CAAC,CAAC,CAACK,QAAQ,CAACN,WAAW,CAAC;MAAA,CAClD,CAAC;IACH;;IAEA;IACA,MAAMQ,MAAM,GAAG,CAAC,GAAGb,QAAQ,CAAC,CAACc,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC1C,QAAQ7D,MAAM;QACZ,KAAK,QAAQ;UACX,OAAO,IAAI8D,IAAI,CAACD,CAAC,CAACE,SAAS,IAAI,CAAC,CAAC,GAAG,IAAID,IAAI,CAACF,CAAC,CAACG,SAAS,IAAI,CAAC,CAAC;QAChE,KAAK,QAAQ;UACX,OAAO,IAAID,IAAI,CAACF,CAAC,CAACG,SAAS,IAAI,CAAC,CAAC,GAAG,IAAID,IAAI,CAACD,CAAC,CAACE,SAAS,IAAI,CAAC,CAAC;QAChE,KAAK,OAAO;UACV,OAAO,CAACH,CAAC,CAACL,KAAK,IAAI,EAAE,EAAES,aAAa,CAACH,CAAC,CAACN,KAAK,IAAI,EAAE,CAAC;QACrD,KAAK,SAAS;UACZ,OAAO,CAACK,CAAC,CAACvB,OAAO,IAAI,EAAE,EAAE2B,aAAa,CAACH,CAAC,CAACxB,OAAO,IAAI,EAAE,CAAC;QACzD;UACE,OAAO,CAAC;MACZ;IACF,CAAC,CAAC;IAEFO,OAAO,CAACqB,GAAG,CAAC,0BAA0B,EAAEP,MAAM,CAACQ,MAAM,CAAC;IACtD,IAAIR,MAAM,CAACQ,MAAM,GAAG,CAAC,EAAE;MACrBtB,OAAO,CAACqB,GAAG,CAAC,2BAA2B,EAAEP,MAAM,CAAC,CAAC,CAAC,CAAC;IACrD;IAEA,OAAOA,MAAM;EACf,CAAC,EAAE,CAAC1E,MAAM,EAAEc,UAAU,EAAEE,MAAM,EAAEV,aAAa,EAAEG,aAAa,EAAEG,eAAe,CAAC,CAAC;;EAE/E;EACA,MAAMuE,eAAe,GAAG,MAAOC,KAAK,IAAK;IACvC,MAAMrB,KAAK,GAAGrB,uBAAuB,CAAC0C,KAAK,CAAC;IAE5CjE,oBAAoB,CAACiE,KAAK,CAAC;IAC3B/D,mBAAmB,CAAC,CAAC+D,KAAK,CAAC,CAAC;IAC5B7D,kBAAkB,CAAC,KAAK,CAAC;IACzBE,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACA,IAAIsC,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEsB,QAAQ,KAAKtB,KAAK,CAACsB,QAAQ,CAACb,QAAQ,CAAC,eAAe,CAAC,IAAIT,KAAK,CAACsB,QAAQ,CAACb,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MACnG,IAAI;QACF,MAAMc,SAAS,GAAG,MAAMC,iBAAiB,CAACxB,KAAK,CAACsB,QAAQ,CAAC;QACzDtB,KAAK,CAACyB,cAAc,GAAGF,SAAS;MAClC,CAAC,CAAC,OAAOlF,KAAK,EAAE;QACdwD,OAAO,CAAC6B,IAAI,CAAC,8CAA8C,CAAC;QAC5D1B,KAAK,CAACyB,cAAc,GAAGzB,KAAK,CAACsB,QAAQ;MACvC;IACF;EACF,CAAC;EAED,MAAMK,eAAe,GAAGA,CAAA,KAAM;IAC5BrE,mBAAmB,CAAC,EAAE,CAAC;IACvBF,oBAAoB,CAAC,IAAI,CAAC;IAC1BI,kBAAkB,CAAC,KAAK,CAAC;IACzBE,aAAa,CAAC,IAAI,CAAC;IACnB,IAAIC,QAAQ,EAAE;MACZA,QAAQ,CAACiE,KAAK,CAAC,CAAC;IAClB;EACF,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjCrE,kBAAkB,CAAC,CAACD,eAAe,CAAC;EACtC,CAAC;;EAED;EACA,MAAMiE,iBAAiB,GAAG,MAAOF,QAAQ,IAAK;IAC5C,IAAI,CAACA,QAAQ,EAAE,OAAOA,QAAQ;;IAE9B;IACA,IAAIA,QAAQ,CAACb,QAAQ,CAAC,eAAe,CAAC,IAAIa,QAAQ,CAACb,QAAQ,CAAC,KAAK,CAAC,EAAE;MAClE,IAAI;QACF,MAAMjB,QAAQ,GAAG,MAAMsC,KAAK,CAAE,6DAA4DC,kBAAkB,CAACT,QAAQ,CAAE,EAAC,EAAE;UACxHU,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAI,CAACzC,QAAQ,CAAC0C,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAE,uBAAsB3C,QAAQ,CAAC4C,MAAO,EAAC,CAAC;QAC3D;QAEA,MAAM3C,IAAI,GAAG,MAAMD,QAAQ,CAAC6C,IAAI,CAAC,CAAC;QAElC,IAAI5C,IAAI,CAACC,OAAO,IAAID,IAAI,CAAC8B,SAAS,EAAE;UAClC1B,OAAO,CAACqB,GAAG,CAAC,+BAA+B,CAAC;UAC5C,OAAOzB,IAAI,CAAC8B,SAAS;QACvB,CAAC,MAAM;UACL1B,OAAO,CAAC6B,IAAI,CAAC,+CAA+C,EAAEjC,IAAI,CAAC;UACnE,OAAO6B,QAAQ;QACjB;MACF,CAAC,CAAC,OAAOjF,KAAK,EAAE;QACdwD,OAAO,CAACxD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,OAAOiF,QAAQ;MACjB;IACF;IAEA,OAAOA,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMgB,eAAe,GAAItC,KAAK,IAAK;IACjC,IAAIA,KAAK,CAACuC,SAAS,EAAE;MACnB,OAAOvC,KAAK,CAACuC,SAAS;IACxB;IAEA,IAAIvC,KAAK,CAACwC,OAAO,IAAI,CAACxC,KAAK,CAACwC,OAAO,CAAC/B,QAAQ,CAAC,eAAe,CAAC,EAAE;MAC7D,IAAIgC,OAAO,GAAGzC,KAAK,CAACwC,OAAO;MAC3B,IAAIC,OAAO,CAAChC,QAAQ,CAAC,aAAa,CAAC,IAAIgC,OAAO,CAAChC,QAAQ,CAAC,UAAU,CAAC,EAAE;QACnE,MAAMiC,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;QACjFD,OAAO,GAAGC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,OAAO;MACtC;MACA,OAAQ,8BAA6BA,OAAQ,oBAAmB;IAClE;IAEA,OAAO,0BAA0B;EACnC,CAAC;;EAED;EACAxJ,SAAS,CAAC,MAAM;IACdiG,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;EAEjBjG,SAAS,CAAC,MAAM;IACd,IAAIyC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEe,KAAK,EAAE;MACfD,gBAAgB,CAACd,IAAI,CAACe,KAAK,CAAC;IAC9B;IACA,IAAIf,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkB,KAAK,EAAE;MACfD,gBAAgB,CAACjB,IAAI,CAACkB,KAAK,CAAC;IAC9B;EACF,CAAC,EAAE,CAAClB,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMiH,iBAAiB,GAAGA,CAAA,KAAM;IAC9B3F,aAAa,CAAC,EAAE,CAAC;EACnB,CAAC;EAED,MAAM4F,aAAa,GAAGA,CAAA,KAAM;IAC1B;IACA1D,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAM2D,cAAc,GAAGA,CAAA,KAAM;IAC3B7F,aAAa,CAAC,EAAE,CAAC;IACjBF,kBAAkB,CAAC,KAAK,CAAC;IACzBH,gBAAgB,CAAC,KAAK,CAAC;IACvBuC,WAAW,CAAC,CAAC;EACf,CAAC;;EAED;EACA,MAAM4D,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI/E,UAAU,CAACmC,IAAI,CAAC,CAAC,EAAE;MAAA,IAAA6C,UAAA,EAAAC,iBAAA;MACrB,MAAMC,OAAO,GAAG;QACdrE,EAAE,EAAEmC,IAAI,CAACmC,GAAG,CAAC,CAAC;QACdC,IAAI,EAAEpF,UAAU;QAChBqF,MAAM,EAAE,CAAA1H,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2H,IAAI,KAAI,WAAW;QACjCC,MAAM,EAAE,CAAA5H,IAAI,aAAJA,IAAI,wBAAAqH,UAAA,GAAJrH,IAAI,CAAE2H,IAAI,cAAAN,UAAA,wBAAAC,iBAAA,GAAVD,UAAA,CAAYQ,MAAM,CAAC,CAAC,CAAC,cAAAP,iBAAA,uBAArBA,iBAAA,CAAuBQ,WAAW,CAAC,CAAC,KAAI,GAAG;QACnDC,SAAS,EAAE,IAAI1C,IAAI,CAAC,CAAC,CAAC2C,WAAW,CAAC,CAAC;QACnCC,OAAO,EAAE,EAAE;QACXC,KAAK,EAAE,CAAC;QACRC,KAAK,EAAE;MACT,CAAC;MACD,MAAMC,eAAe,GAAGrF,uBAAuB,CAAC,CAAC;MACjDI,uBAAuB,CAAC,CAACoE,OAAO,EAAE,GAAGa,eAAe,CAAC,CAAC,CAAC,CAAC;MACxD9F,aAAa,CAAC,EAAE,CAAC;IACnB;EACF,CAAC;EAID,MAAM+F,cAAc,GAAIC,SAAS,IAAK;IACpC,IAAI7F,SAAS,CAAC+B,IAAI,CAAC,CAAC,EAAE;MAAA,IAAA+D,WAAA,EAAAC,kBAAA;MACpB,MAAMC,KAAK,GAAG;QACZvF,EAAE,EAAEmC,IAAI,CAACmC,GAAG,CAAC,CAAC;QACdC,IAAI,EAAEhF,SAAS;QACfiF,MAAM,EAAE,CAAA1H,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2H,IAAI,KAAI,WAAW;QACjCC,MAAM,EAAE,CAAA5H,IAAI,aAAJA,IAAI,wBAAAuI,WAAA,GAAJvI,IAAI,CAAE2H,IAAI,cAAAY,WAAA,wBAAAC,kBAAA,GAAVD,WAAA,CAAYV,MAAM,CAAC,CAAC,CAAC,cAAAW,kBAAA,uBAArBA,kBAAA,CAAuBV,WAAW,CAAC,CAAC,KAAI,GAAG;QACnDC,SAAS,EAAE,IAAI1C,IAAI,CAAC,CAAC,CAAC2C,WAAW,CAAC,CAAC;QACnCE,KAAK,EAAE,CAAC;QACRC,KAAK,EAAE;MACT,CAAC;MAEDO,WAAW,CAACtF,QAAQ,CAACuF,GAAG,CAACpB,OAAO,IAC9BA,OAAO,CAACrE,EAAE,KAAKoF,SAAS,GACpB;QAAE,GAAGf,OAAO;QAAEU,OAAO,EAAE,CAAC,GAAGV,OAAO,CAACU,OAAO,EAAEQ,KAAK;MAAE,CAAC,GACpDlB,OACN,CAAC,CAAC;MACF7E,YAAY,CAAC,EAAE,CAAC;MAChBF,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;EAED,MAAMoG,iBAAiB,GAAGA,CAACN,SAAS,EAAEO,OAAO,GAAG,KAAK,EAAEC,QAAQ,GAAG,IAAI,KAAK;IACzE,IAAID,OAAO,EAAE;MACXH,WAAW,CAACtF,QAAQ,CAACuF,GAAG,CAACpB,OAAO,IAC9BA,OAAO,CAACrE,EAAE,KAAK4F,QAAQ,GACnB;QACE,GAAGvB,OAAO;QACVU,OAAO,EAAEV,OAAO,CAACU,OAAO,CAACU,GAAG,CAACF,KAAK,IAChCA,KAAK,CAACvF,EAAE,KAAKoF,SAAS,GAClB;UAAE,GAAGG,KAAK;UAAEN,KAAK,EAAE,CAACM,KAAK,CAACN,KAAK;UAAED,KAAK,EAAEO,KAAK,CAACN,KAAK,GAAGM,KAAK,CAACP,KAAK,GAAG,CAAC,GAAGO,KAAK,CAACP,KAAK,GAAG;QAAE,CAAC,GACzFO,KACN;MACF,CAAC,GACDlB,OACN,CAAC,CAAC;IACJ,CAAC,MAAM;MACLmB,WAAW,CAACtF,QAAQ,CAACuF,GAAG,CAACpB,OAAO,IAC9BA,OAAO,CAACrE,EAAE,KAAKoF,SAAS,GACpB;QAAE,GAAGf,OAAO;QAAEY,KAAK,EAAE,CAACZ,OAAO,CAACY,KAAK;QAAED,KAAK,EAAEX,OAAO,CAACY,KAAK,GAAGZ,OAAO,CAACW,KAAK,GAAG,CAAC,GAAGX,OAAO,CAACW,KAAK,GAAG;MAAE,CAAC,GACnGX,OACN,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMwB,aAAa,GAAIhB,SAAS,IAAK;IACnC,MAAMP,GAAG,GAAG,IAAInC,IAAI,CAAC,CAAC;IACtB,MAAM2D,IAAI,GAAG,IAAI3D,IAAI,CAAC0C,SAAS,CAAC;IAChC,MAAMkB,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAAC3B,GAAG,GAAGwB,IAAI,IAAI,IAAI,CAAC;IAErD,IAAIC,aAAa,GAAG,EAAE,EAAE,OAAO,UAAU;IACzC,IAAIA,aAAa,GAAG,IAAI,EAAE,OAAQ,GAAEC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAE,OAAM;IACzE,IAAIA,aAAa,GAAG,KAAK,EAAE,OAAQ,GAAEC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,IAAI,CAAE,OAAM;IAC5E,IAAIA,aAAa,GAAG,MAAM,EAAE,OAAQ,GAAEC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,KAAK,CAAE,OAAM;IAC9E,OAAOD,IAAI,CAACI,kBAAkB,CAAC,CAAC;EAClC,CAAC;EAED,oBACE7K,OAAA;IAAKoF,SAAS,EAAC,yBAAyB;IAAA/E,QAAA,gBAEtCL,OAAA;MAAKoF,SAAS,EAAC,sBAAsB;MAAA/E,QAAA,eACnCL,OAAA;QAAKoF,SAAS,EAAC,gBAAgB;QAAA/E,QAAA,gBAC7BL,OAAA;UAAKoF,SAAS,EAAC,aAAa;UAAA/E,QAAA,gBAC1BL,OAAA;YAAKoF,SAAS,EAAC,aAAa;YAAA/E,QAAA,eAC1BL,OAAA,CAACc,OAAO;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACNT,OAAA;YAAKoF,SAAS,EAAC,aAAa;YAAA/E,QAAA,gBAC1BL,OAAA;cAAAK,QAAA,EAAI;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtBT,OAAA;cAAAK,QAAA,EAAG;YAAiD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNT,OAAA;UAAKoF,SAAS,EAAC,eAAe;UAAA/E,QAAA,gBAC5BL,OAAA;YAAKoF,SAAS,EAAC,eAAe;YAAA/E,QAAA,gBAC5BL,OAAA;cAAMoF,SAAS,EAAC,aAAa;cAAA/E,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3CT,OAAA;cAAMoF,SAAS,EAAC,aAAa;cAAA/E,QAAA,EAAEiC,aAAa,CAACgH,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGjH,aAAa,CAACwI,KAAK,CAAC,CAAC;YAAC;cAAAxK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClG,CAAC,eACNT,OAAA;YAAKoF,SAAS,EAAC,eAAe;YAAA/E,QAAA,gBAC5BL,OAAA;cAAMoF,SAAS,EAAC,aAAa;cAAA/E,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChDT,OAAA;cAAMoF,SAAS,EAAC,aAAa;cAAA/E,QAAA,EAC1B,CAAAoB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,KAAK,MAAK,SAAS,GAAI,SAAQ,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,KAAK,KAAI,KAAM,EAAC,GAC3D,CAAAlB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,KAAK,MAAK,WAAW,GAAI,QAAO,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,KAAK,KAAI,KAAM,EAAC,GAC5D,CAAAlB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,KAAK,MAAK,SAAS,GAAI,QAAO,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,KAAK,KAAI,KAAM,EAAC,GAC1D;YAAS;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENT,OAAA;MAAKoF,SAAS,EAAC,uBAAuB;MAAA/E,QAAA,gBAEpCL,OAAA;QAAKoF,SAAS,EAAC,gBAAgB;QAAA/E,QAAA,gBAC7BL,OAAA;UAAKoF,SAAS,EAAC,cAAc;UAAA/E,QAAA,gBAE3BL,OAAA;YAAKoF,SAAS,EAAC,eAAe;YAAA/E,QAAA,gBAC5BL,OAAA;cAAOoF,SAAS,EAAC,eAAe;cAAA/E,QAAA,gBAC9BL,OAAA,CAACe,QAAQ;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACXmB,WAAW,GAAG,kBAAkB,GAAG,iBAAiB;YAAA;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACRT,OAAA;cACE+K,KAAK,EAAEtI,aAAc;cACrBuI,QAAQ,EAAGC,CAAC,IAAKvI,gBAAgB,CAACuI,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAClD3F,SAAS,EAAC,6BAA6B;cAAA/E,QAAA,gBAEvCL,OAAA;gBAAQ+K,KAAK,EAAC,KAAK;gBAAA1K,QAAA,EAAEuB,WAAW,GAAG,eAAe,GAAG;cAAa;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,EAC3EsE,gBAAgB,CAACqF,GAAG,CAAEe,GAAG,iBACxBnL,OAAA;gBAAkB+K,KAAK,EAAEI,GAAI;gBAAA9K,QAAA,EAC1BiC,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GAClEV,WAAW,GAAI,aAAYuJ,GAAI,EAAC,GAAI,SAAQA,GAAI,EAAC,GACjD,QAAOA,GAAI;cAAC,GAHJA,GAAG;gBAAA7K,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIR,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNT,OAAA;YAAKoF,SAAS,EAAC,eAAe;YAAA/E,QAAA,gBAC5BL,OAAA;cAAOoF,SAAS,EAAC,eAAe;cAAA/E,QAAA,gBAC9BL,OAAA,CAACe,QAAQ;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,WAEd;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRT,OAAA;cACE+K,KAAK,EAAEnI,eAAgB;cACvBoI,QAAQ,EAAGC,CAAC,IAAKpI,kBAAkB,CAACoI,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cACpD3F,SAAS,EAAC,+BAA+B;cAAA/E,QAAA,gBAEzCL,OAAA;gBAAQ+K,KAAK,EAAC,KAAK;gBAAA1K,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACxCuE,iBAAiB,CAACoF,GAAG,CAAE/E,OAAO,iBAC7BrF,OAAA;gBAAsB+K,KAAK,EAAE1F,OAAQ;gBAAAhF,QAAA,EAClCgF;cAAO,GADGA,OAAO;gBAAA/E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEZ,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNT,OAAA;YAAKoF,SAAS,EAAC,eAAe;YAAA/E,QAAA,gBAC5BL,OAAA;cAAOoF,SAAS,EAAC,eAAe;cAAA/E,QAAA,gBAC9BL,OAAA,CAACgB,eAAe;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,QAErB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRT,OAAA;cACE+K,KAAK,EAAE/H,MAAO;cACdgI,QAAQ,EAAGC,CAAC,IAAKhI,SAAS,CAACgI,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC3C3F,SAAS,EAAC,4BAA4B;cAAA/E,QAAA,gBAEtCL,OAAA;gBAAQ+K,KAAK,EAAC,QAAQ;gBAAA1K,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CT,OAAA;gBAAQ+K,KAAK,EAAC,QAAQ;gBAAA1K,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CT,OAAA;gBAAQ+K,KAAK,EAAC,OAAO;gBAAA1K,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCT,OAAA;gBAAQ+K,KAAK,EAAC,SAAS;gBAAA1K,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNT,OAAA;UAAKoF,SAAS,EAAC,YAAY;UAAA/E,QAAA,gBACzBL,OAAA;YAAKoF,SAAS,EAAC,kBAAkB;YAAA/E,QAAA,gBAC/BL,OAAA,CAACiB,QAAQ;cAACmE,SAAS,EAAC;YAAa;cAAA9E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpCT,OAAA;cACEoL,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,8CAA8C;cAC1DN,KAAK,EAAEjI,UAAW;cAClBkI,QAAQ,EAAGC,CAAC,IAAKlI,aAAa,CAACkI,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC/C3F,SAAS,EAAC;YAAc;cAAA9E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,EACDqC,UAAU,iBACT9C,OAAA;cAAQsL,OAAO,EAAE5C,iBAAkB;cAACtD,SAAS,EAAC,kBAAkB;cAAA/E,QAAA,gBAC9DL,OAAA,CAACkB,GAAG;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAET;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENT,OAAA;YAAQsL,OAAO,EAAE3C,aAAc;YAACvD,SAAS,EAAC,aAAa;YAAA/E,QAAA,gBACrDL,OAAA,CAACmB,UAAU;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEhB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLyB,OAAO,gBACNlC,OAAA;QAAKoF,SAAS,EAAC,eAAe;QAAA/E,QAAA,gBAC5BL,OAAA;UAAKoF,SAAS,EAAC;QAAiB;UAAA9E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvCT,OAAA;UAAAK,QAAA,EAAIuB,WAAW,GAAG,mBAAmB,GAAG;QAAmB;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,GACJ2B,KAAK,gBACPpC,OAAA;QAAKoF,SAAS,EAAC,aAAa;QAAA/E,QAAA,gBAC1BL,OAAA,CAACoB,eAAe;UAACgE,SAAS,EAAC;QAAY;UAAA9E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1CT,OAAA;UAAAK,QAAA,EAAKuB,WAAW,GAAG,2BAA2B,GAAG;QAAsB;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7ET,OAAA;UAAAK,QAAA,EAAI+B;QAAK;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdT,OAAA;UAAQsL,OAAO,EAAErG,WAAY;UAACG,SAAS,EAAC,WAAW;UAAA/E,QAAA,EAChDuB,WAAW,GAAG,aAAa,GAAG;QAAW;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,GACJiE,uBAAuB,CAACwC,MAAM,GAAG,CAAC,gBACpClH,OAAA;QAAKoF,SAAS,EAAC,aAAa;QAAA/E,QAAA,EACzBqE,uBAAuB,CAAC0F,GAAG,CAAC,CAACrE,KAAK,EAAEqB,KAAK,kBACxCpH,OAAA;UAAiBoF,SAAS,EAAC,YAAY;UAACkG,OAAO,EAAEA,CAAA,KAAMnE,eAAe,CAACC,KAAK,CAAE;UAAA/G,QAAA,gBAC5EL,OAAA;YAAKoF,SAAS,EAAC,sBAAsB;YAAA/E,QAAA,gBACnCL,OAAA;cACEuL,GAAG,EAAElD,eAAe,CAACtC,KAAK,CAAE;cAC5ByF,GAAG,EAAEzF,KAAK,CAACQ,KAAM;cACjBnB,SAAS,EAAC,iBAAiB;cAC3BqG,OAAO,EAAGR,CAAC,IAAK;gBACd;gBACA,IAAIlF,KAAK,CAACwC,OAAO,IAAI,CAACxC,KAAK,CAACwC,OAAO,CAAC/B,QAAQ,CAAC,eAAe,CAAC,EAAE;kBAC7D;kBACA,IAAIgC,OAAO,GAAGzC,KAAK,CAACwC,OAAO;kBAC3B,IAAIC,OAAO,CAAChC,QAAQ,CAAC,aAAa,CAAC,IAAIgC,OAAO,CAAChC,QAAQ,CAAC,UAAU,CAAC,EAAE;oBACnE,MAAMiC,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;oBACjFD,OAAO,GAAGC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,OAAO;kBACtC;kBAEA,MAAMkD,SAAS,GAAG,CACf,8BAA6BlD,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,cAAa,EACnD,0BAA0B,CAC3B;kBAED,MAAMmD,UAAU,GAAGV,CAAC,CAACC,MAAM,CAACK,GAAG;kBAC/B,MAAMK,YAAY,GAAGF,SAAS,CAACG,SAAS,CAACC,GAAG,IAAIH,UAAU,CAACnF,QAAQ,CAACsF,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC;kBAE1F,IAAIJ,YAAY,GAAGF,SAAS,CAACxE,MAAM,GAAG,CAAC,EAAE;oBACvC+D,CAAC,CAACC,MAAM,CAACK,GAAG,GAAGG,SAAS,CAACE,YAAY,GAAG,CAAC,CAAC;kBAC5C;gBACF,CAAC,MAAM;kBACLX,CAAC,CAACC,MAAM,CAACK,GAAG,GAAG,0BAA0B;gBAC3C;cACF;YAAE;cAAAjL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFT,OAAA;cAAKoF,SAAS,EAAC,cAAc;cAAA/E,QAAA,eAC3BL,OAAA,CAACE,YAAY;gBAACkF,SAAS,EAAC;cAAW;gBAAA9E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACNT,OAAA;cAAKoF,SAAS,EAAC,gBAAgB;cAAA/E,QAAA,EAC5B0F,KAAK,CAACkG,QAAQ,IAAI;YAAO;cAAA3L,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,EACLsF,KAAK,CAACmG,SAAS,IAAInG,KAAK,CAACmG,SAAS,CAAChF,MAAM,GAAG,CAAC,iBAC5ClH,OAAA;cAAKoF,SAAS,EAAC,gBAAgB;cAAA/E,QAAA,gBAC7BL,OAAA,CAACsB,YAAY;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,MAElB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENT,OAAA;YAAKoF,SAAS,EAAC,oBAAoB;YAAA/E,QAAA,gBACjCL,OAAA;cAAIoF,SAAS,EAAC,aAAa;cAAA/E,QAAA,EAAE0F,KAAK,CAACQ;YAAK;cAAAjG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9CT,OAAA;cAAKoF,SAAS,EAAC,YAAY;cAAA/E,QAAA,gBACzBL,OAAA;gBAAMoF,SAAS,EAAC,eAAe;gBAAA/E,QAAA,EAAEyB,cAAc,CAACiE,KAAK,CAACV,OAAO;cAAC;gBAAA/E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtET,OAAA;gBAAMoF,SAAS,EAAC,aAAa;gBAAA/E,QAAA,EAC1BiC,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GAClEV,WAAW,GAAI,aAAYmE,KAAK,CAACX,SAAS,IAAIW,KAAK,CAACpD,KAAM,EAAC,GAAI,SAAQoD,KAAK,CAACX,SAAS,IAAIW,KAAK,CAACpD,KAAM,EAAC,GACvG,QAAOoD,KAAK,CAACX,SAAS,IAAIW,KAAK,CAACpD,KAAM;cAAC;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNT,OAAA;cAAKoF,SAAS,EAAC,YAAY;cAAA/E,QAAA,GACxB0F,KAAK,CAACU,KAAK,iBAAIzG,OAAA;gBAAMoF,SAAS,EAAC,WAAW;gBAAA/E,QAAA,EAAE0F,KAAK,CAACU;cAAK;gBAAAnG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAC/DsF,KAAK,CAACoG,eAAe,IAAIpG,KAAK,CAACoG,eAAe,MAAMpG,KAAK,CAACX,SAAS,IAAIW,KAAK,CAACpD,KAAK,CAAC,iBAClF3C,OAAA;gBAAMoF,SAAS,EAAC,YAAY;gBAAA/E,QAAA,GACzBuB,WAAW,GAAG,qBAAqB,GAAG,cAAc,EAAEU,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,GACxHV,WAAW,GAAI,aAAYmE,KAAK,CAACoG,eAAgB,EAAC,GAAI,SAAQpG,KAAK,CAACoG,eAAgB,EAAC,GACrF,QAAOpG,KAAK,CAACoG,eAAgB,EAAC;cAAA;gBAAA7L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GApEE2G,KAAK;UAAA9G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqEV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAENT,OAAA;QAAKoF,SAAS,EAAC,aAAa;QAAA/E,QAAA,gBAC1BL,OAAA,CAACU,eAAe;UAAC0E,SAAS,EAAC;QAAY;UAAA9E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1CT,OAAA;UAAAK,QAAA,EAAKuB,WAAW,GAAG,6BAA6B,GAAG;QAAiB;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1ET,OAAA;UAAAK,QAAA,EAAIuB,WAAW,GAAG,kEAAkE,GAAG;QAA4D;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxJT,OAAA;UAAGoF,SAAS,EAAC,YAAY;UAAA/E,QAAA,EAAEuB,WAAW,GAAG,yCAAyC,GAAG;QAA6C;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpI,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL2C,gBAAgB,CAAC8D,MAAM,GAAG,CAAC,IAAIhE,iBAAiB,KAAK,IAAI,iBACxDlD,OAAA;MAAKoF,SAAS,EAAG,iBAAgB9B,eAAe,GAAG,UAAU,GAAG,EAAG,EAAE;MAACgI,OAAO,EAAGL,CAAC,IAAK;QACpF,IAAIA,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACmB,aAAa,EAAE1E,eAAe,CAAC,CAAC;MACrD,CAAE;MAAArH,QAAA,eACAL,OAAA;QAAKoF,SAAS,EAAG,eAAc9B,eAAe,GAAG,UAAU,GAAG,EAAG,EAAE;QAAAjD,QAAA,EAChE,CAAC,CAAAgM,WAAA,EAAAC,kBAAA,KAAM;UACN,MAAMvG,KAAK,GAAGrB,uBAAuB,CAACxB,iBAAiB,CAAC;UACxD,IAAI,CAAC6C,KAAK,EAAE,oBAAO/F,OAAA;YAAAK,QAAA,EAAK;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;UAE7C,oBACET,OAAA;YAAKoF,SAAS,EAAC,eAAe;YAAA/E,QAAA,gBAC5BL,OAAA;cAAKoF,SAAS,EAAC,cAAc;cAAA/E,QAAA,gBAC3BL,OAAA;gBAAKoF,SAAS,EAAC,YAAY;gBAAA/E,QAAA,gBACzBL,OAAA;kBAAIoF,SAAS,EAAC,aAAa;kBAAA/E,QAAA,EAAE0F,KAAK,CAACQ;gBAAK;kBAAAjG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9CT,OAAA;kBAAKoF,SAAS,EAAC,YAAY;kBAAA/E,QAAA,gBACzBL,OAAA;oBAAMoF,SAAS,EAAC,eAAe;oBAAA/E,QAAA,EAAE0F,KAAK,CAACV;kBAAO;oBAAA/E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtDT,OAAA;oBAAMoF,SAAS,EAAC,aAAa;oBAAA/E,QAAA,GAAC,QAAM,EAAC0F,KAAK,CAACX,SAAS;kBAAA;oBAAA9E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAC3DsF,KAAK,CAACvD,KAAK,iBAAIxC,OAAA;oBAAMoF,SAAS,EAAC,aAAa;oBAAA/E,QAAA,EAAE0F,KAAK,CAACvD;kBAAK;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNT,OAAA;gBAAKoF,SAAS,EAAC,gBAAgB;gBAAA/E,QAAA,gBAC7BL,OAAA;kBACEoF,SAAS,EAAC,6BAA6B;kBACvCkG,OAAO,EAAEA,CAAA,KAAM;oBACb/G,mBAAmB,CAAC,CAACD,gBAAgB,CAAC;oBACtC,IAAI,CAACA,gBAAgB,IAAI,CAAChB,eAAe,EAAE;sBACzCsE,oBAAoB,CAAC,CAAC;oBACxB;kBACF,CAAE;kBACFrB,KAAK,EAAC,aAAa;kBAAAlG,QAAA,gBAEnBL,OAAA;oBAAMoF,SAAS,EAAC,UAAU;oBAAA/E,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpCT,OAAA;oBAAMoF,SAAS,EAAC,UAAU;oBAAA/E,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,EACP6C,eAAe,IAAIgB,gBAAgB,iBACnCtE,OAAA;kBACEoF,SAAS,EAAC,+BAA+B;kBACzCkG,OAAO,EAAEA,CAAA,KAAM;oBACb/G,mBAAmB,CAAC,KAAK,CAAC;oBAC1BqD,oBAAoB,CAAC,CAAC;kBACxB,CAAE;kBACFrB,KAAK,EAAC,gBAAgB;kBAAAlG,QAAA,gBAEtBL,OAAA;oBAAMoF,SAAS,EAAC,UAAU;oBAAA/E,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnCT,OAAA;oBAAMoF,SAAS,EAAC,UAAU;oBAAA/E,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CACT,eACDT,OAAA;kBACEoF,SAAS,EAAC,uBAAuB;kBACjCkG,OAAO,EAAE5D,eAAgB;kBACzBnB,KAAK,EAAC,aAAa;kBAAAlG,QAAA,eAEnBL,OAAA,CAACW,OAAO;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNT,OAAA;cAAKoF,SAAS,EAAG,qBAAoB9B,eAAe,GAAG,iBAAiB,GAAG,eAAgB,EAAE;cAAAjD,QAAA,gBAE3FL,OAAA;gBAAKoF,SAAS,EAAC,iBAAiB;gBAAA/E,QAAA,EAC/B0F,KAAK,CAACsB,QAAQ,gBACbrH,OAAA;kBAAKG,KAAK,EAAE;oBAAEoM,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,MAAM;oBAAEC,YAAY,EAAE;kBAAM,CAAE;kBAAApM,QAAA,gBACrEL,OAAA;oBACE0M,GAAG,EAAGA,GAAG,IAAK/I,WAAW,CAAC+I,GAAG,CAAE;oBAC/BC,QAAQ;oBACRC,QAAQ;oBACRC,WAAW;oBACXC,OAAO,EAAC,UAAU;oBAClBC,KAAK,EAAC,MAAM;oBACZC,MAAM,EAAC,KAAK;oBACZC,MAAM,EAAE5E,eAAe,CAACtC,KAAK,CAAE;oBAC/B5F,KAAK,EAAE;sBACL4M,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,OAAO;sBACfE,eAAe,EAAE;oBACnB,CAAE;oBACFzB,OAAO,EAAGR,CAAC,IAAK;sBACdxH,aAAa,CAAE,yBAAwBsC,KAAK,CAACQ,KAAM,mCAAkC,CAAC;oBACxF,CAAE;oBACF4G,SAAS,EAAEA,CAAA,KAAM;sBACf1J,aAAa,CAAC,IAAI,CAAC;oBACrB,CAAE;oBACF2J,WAAW,EAAEA,CAAA,KAAM;sBACjBxH,OAAO,CAACqB,GAAG,CAAC,0BAA0B,CAAC;oBACzC,CAAE;oBACFoG,WAAW,EAAC,WAAW;oBAAAhN,QAAA,gBAGvBL,OAAA;sBAAQuL,GAAG,EAAExF,KAAK,CAACyB,cAAc,IAAIzB,KAAK,CAACsB,QAAS;sBAAC+D,IAAI,EAAC;oBAAW;sBAAA9K,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EAGvEsF,KAAK,CAACmG,SAAS,IAAInG,KAAK,CAACmG,SAAS,CAAChF,MAAM,GAAG,CAAC,IAAInB,KAAK,CAACmG,SAAS,CAAC9B,GAAG,CAAC,CAACkD,QAAQ,EAAElG,KAAK,kBACpFpH,OAAA;sBAEEuN,IAAI,EAAC,WAAW;sBAChBhC,GAAG,EAAE+B,QAAQ,CAACxB,GAAI;sBAClB0B,OAAO,EAAEF,QAAQ,CAACG,QAAS;sBAC3BC,KAAK,EAAEJ,QAAQ,CAACK,YAAa;sBAC7BC,OAAO,EAAEN,QAAQ,CAACO,SAAS,IAAIzG,KAAK,KAAK;oBAAE,GALrC,GAAEkG,QAAQ,CAACG,QAAS,IAAGrG,KAAM,EAAC;sBAAA9G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAMrC,CACF,CAAC,EAAC,8CAGL;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAGPsF,KAAK,CAACmG,SAAS,IAAInG,KAAK,CAACmG,SAAS,CAAChF,MAAM,GAAG,CAAC,iBAC5ClH,OAAA;oBAAKoF,SAAS,EAAC,oBAAoB;oBAAA/E,QAAA,gBACjCL,OAAA,CAACsB,YAAY;sBAAC8D,SAAS,EAAC;oBAAe;sBAAA9E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC1CT,OAAA;sBAAAK,QAAA,GAAM,yBAAuB,EAAC0F,KAAK,CAACmG,SAAS,CAAChF,MAAM,EAAC,cAAY;oBAAA;sBAAA5G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrE,CACN,EAGA+C,UAAU,iBACTxD,OAAA;oBAAKoF,SAAS,EAAC,qBAAqB;oBAAA/E,QAAA,eAClCL,OAAA;sBAAKoF,SAAS,EAAC,eAAe;sBAAA/E,QAAA,gBAC5BL,OAAA,CAACoB,eAAe;wBAACgE,SAAS,EAAC;sBAAY;wBAAA9E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC1CT,OAAA;wBAAAK,QAAA,EAAImD;sBAAU;wBAAAlD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACnBT,OAAA;wBAAQsL,OAAO,EAAEA,CAAA,KAAM7H,aAAa,CAAC,IAAI,CAAE;wBAAC2B,SAAS,EAAC,mBAAmB;wBAAA/E,QAAA,EAAC;sBAE1E;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,GACNsF,KAAK,CAACwC,OAAO;gBAAA;gBACf;gBACAvI,OAAA;kBACEuL,GAAG,EAAG,iCAAgCxF,KAAK,CAACwC,OAAQ,mBAAmB;kBACvEhC,KAAK,EAAER,KAAK,CAACQ,KAAM;kBACnBuH,WAAW,EAAC,GAAG;kBACfC,eAAe;kBACf3I,SAAS,EAAC,cAAc;kBACxB4I,MAAM,EAAEA,CAAA,KAAMpI,OAAO,CAACqB,GAAG,CAAC,yBAAyB;gBAAE;kBAAA3G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,gBAEVT,OAAA;kBAAKoF,SAAS,EAAC,aAAa;kBAAA/E,QAAA,gBAC1BL,OAAA;oBAAKoF,SAAS,EAAC,YAAY;oBAAA/E,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpCT,OAAA;oBAAAK,QAAA,EAAI;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1BT,OAAA;oBAAAK,QAAA,EAAImD,UAAU,IAAI;kBAA4C;oBAAAlD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnET,OAAA;oBAAKoF,SAAS,EAAC,eAAe;oBAAA/E,QAAA,eAC5BL,OAAA;sBACEiO,IAAI,EAAElI,KAAK,CAACyB,cAAc,IAAIzB,KAAK,CAACsB,QAAS;sBAC7C6D,MAAM,EAAC,QAAQ;sBACfgD,GAAG,EAAC,qBAAqB;sBACzB9I,SAAS,EAAC,mBAAmB;sBAAA/E,QAAA,EAC9B;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eAGNT,OAAA;gBAAKoF,SAAS,EAAG,0BAAyB9B,eAAe,GAAG,mBAAmB,GAAG,iBAAkB,EAAE;gBAAAjD,QAAA,gBAEpGL,OAAA;kBAAKoF,SAAS,EAAC,uBAAuB;kBAAA/E,QAAA,gBACpCL,OAAA;oBAAKoF,SAAS,EAAC,wBAAwB;oBAAA/E,QAAA,gBACrCL,OAAA,CAACsB,YAAY;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAChBT,OAAA;sBAAAK,QAAA,GAAOwE,QAAQ,CAACqC,MAAM,EAAC,GAAC,EAACrC,QAAQ,CAACqC,MAAM,KAAK,CAAC,GAAG,SAAS,GAAG,UAAU;oBAAA;sBAAA5G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5E,CAAC,EACL,CAAC6C,eAAe,IAAI,CAACgB,gBAAgB,gBACpCtE,OAAA;oBACEsL,OAAO,EAAEA,CAAA,KAAM;sBACb/G,mBAAmB,CAAC,IAAI,CAAC;sBACzB,IAAI,CAACjB,eAAe,EAAE;wBACpBsE,oBAAoB,CAAC,CAAC;sBACxB;oBACF,CAAE;oBACFxC,SAAS,EAAC,mBAAmB;oBAAA/E,QAAA,gBAE7BL,OAAA;sBAAMoF,SAAS,EAAC,UAAU;sBAAA/E,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACrCT,OAAA;sBAAMoF,SAAS,EAAC,UAAU;sBAAA/E,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC,gBAETT,OAAA;oBACEsL,OAAO,EAAEA,CAAA,KAAM/G,mBAAmB,CAAC,KAAK,CAAE;oBAC1Ca,SAAS,EAAC,qBAAqB;oBAAA/E,QAAA,EAChC;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,EAGJ6C,eAAe,IAAIgB,gBAAgB,iBACnCtE,OAAA;kBAAKoF,SAAS,EAAC,4BAA4B;kBAAA/E,QAAA,gBAEvCL,OAAA;oBAAKoF,SAAS,EAAC,aAAa;oBAAA/E,QAAA,eAC1BL,OAAA;sBAAKoF,SAAS,EAAC,yBAAyB;sBAAA/E,QAAA,gBACtCL,OAAA;wBAAKoF,SAAS,EAAC,aAAa;wBAAA/E,QAAA,EACzB,CAAAoB,IAAI,aAAJA,IAAI,wBAAA4K,WAAA,GAAJ5K,IAAI,CAAE2H,IAAI,cAAAiD,WAAA,wBAAAC,kBAAA,GAAVD,WAAA,CAAY/C,MAAM,CAAC,CAAC,CAAC,cAAAgD,kBAAA,uBAArBA,kBAAA,CAAuB/C,WAAW,CAAC,CAAC,KAAI;sBAAG;wBAAAjJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzC,CAAC,eACNT,OAAA;wBAAKoF,SAAS,EAAC,uBAAuB;wBAAA/E,QAAA,gBACpCL,OAAA;0BACE+K,KAAK,EAAEjH,UAAW;0BAClBkH,QAAQ,EAAGC,CAAC,IAAKlH,aAAa,CAACkH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;0BAC/CM,WAAW,EAAC,yCAAyC;0BACrDjG,SAAS,EAAC,eAAe;0BACzB+I,IAAI,EAAC,GAAG;0BACRC,SAAS;wBAAA;0BAAA9N,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACV,CAAC,eACFT,OAAA;0BACEsL,OAAO,EAAEzC,gBAAiB;0BAC1BzD,SAAS,EAAC,oBAAoB;0BAC9BiJ,QAAQ,EAAE,CAACvK,UAAU,CAACmC,IAAI,CAAC,CAAE;0BAAA5F,QAAA,gBAE7BL,OAAA;4BAAAK,QAAA,EAAM;0BAAE;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,iBACjB;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGZT,OAAA;oBAAKoF,SAAS,EAAC,eAAe;oBAAA/E,QAAA,EAC3BwE,QAAQ,CAACqC,MAAM,KAAK,CAAC,gBACpBlH,OAAA;sBAAKoF,SAAS,EAAC,aAAa;sBAAA/E,QAAA,gBAC1BL,OAAA;wBAAKoF,SAAS,EAAC,kBAAkB;wBAAA/E,QAAA,EAAC;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC1CT,OAAA;wBAAAK,QAAA,EAAG;sBAAqD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzD,CAAC,GAENoE,QAAQ,CAACuF,GAAG,CAAEpB,OAAO;sBAAA,IAAAsF,eAAA,EAAAC,qBAAA,EAAAC,WAAA,EAAAC,kBAAA;sBAAA,oBACnBzO,OAAA;wBAAsBoF,SAAS,EAAC,SAAS;wBAAA/E,QAAA,gBACvCL,OAAA;0BAAKoF,SAAS,EAAC,cAAc;0BAAA/E,QAAA,gBAC3BL,OAAA;4BAAKoF,SAAS,EAAC,gBAAgB;4BAAA/E,QAAA,EAC5B2I,OAAO,CAACK,MAAM,MAAAiF,eAAA,GAAItF,OAAO,CAACG,MAAM,cAAAmF,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBhF,MAAM,CAAC,CAAC,CAAC,cAAAiF,qBAAA,uBAAzBA,qBAAA,CAA2BhF,WAAW,CAAC,CAAC,KAAI;0BAAG;4BAAAjJ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC/D,CAAC,eACNT,OAAA;4BAAKoF,SAAS,EAAC,iBAAiB;4BAAA/E,QAAA,gBAC9BL,OAAA;8BAAKoF,SAAS,EAAC,gBAAgB;8BAAA/E,QAAA,gBAC7BL,OAAA;gCAAMoF,SAAS,EAAC,gBAAgB;gCAAA/E,QAAA,EAAE2I,OAAO,CAACG;8BAAM;gCAAA7I,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC,eACxDT,OAAA;gCAAMoF,SAAS,EAAC,cAAc;gCAAA/E,QAAA,EAC3BmK,aAAa,CAACxB,OAAO,CAACQ,SAAS;8BAAC;gCAAAlJ,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC7B,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACJ,CAAC,eACNT,OAAA;8BAAKoF,SAAS,EAAC,cAAc;8BAAA/E,QAAA,EAAE2I,OAAO,CAACE;4BAAI;8BAAA5I,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,eAClDT,OAAA;8BAAKoF,SAAS,EAAC,iBAAiB;8BAAA/E,QAAA,gBAC9BL,OAAA;gCACEsL,OAAO,EAAEA,CAAA,KAAMjB,iBAAiB,CAACrB,OAAO,CAACrE,EAAE,CAAE;gCAC7CS,SAAS,EAAG,YAAW4D,OAAO,CAACY,KAAK,GAAG,OAAO,GAAG,EAAG,EAAE;gCAAAvJ,QAAA,gBAEtDL,OAAA;kCAAAK,QAAA,EAAO2I,OAAO,CAACY,KAAK,GAAG,IAAI,GAAG;gCAAI;kCAAAtJ,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO,CAAC,EACzCuI,OAAO,CAACW,KAAK,GAAG,CAAC,iBAAI3J,OAAA;kCAAMoF,SAAS,EAAC,YAAY;kCAAA/E,QAAA,EAAE2I,OAAO,CAACW;gCAAK;kCAAArJ,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACnE,CAAC,eACTT,OAAA;gCACEsL,OAAO,EAAEA,CAAA,KAAMrH,aAAa,CAACD,UAAU,KAAKgF,OAAO,CAACrE,EAAE,GAAG,IAAI,GAAGqE,OAAO,CAACrE,EAAE,CAAE;gCAC5ES,SAAS,EAAC,WAAW;gCAAA/E,QAAA,gBAErBL,OAAA;kCAAAK,QAAA,EAAM;gCAAE;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAM,CAAC,UACjB;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,EACRuI,OAAO,CAACU,OAAO,CAACxC,MAAM,GAAG,CAAC,iBACzBlH,OAAA;gCAAMoF,SAAS,EAAC,eAAe;gCAAA/E,QAAA,GAC5B2I,OAAO,CAACU,OAAO,CAACxC,MAAM,EAAC,GAAC,EAAC8B,OAAO,CAACU,OAAO,CAACxC,MAAM,KAAK,CAAC,GAAG,OAAO,GAAG,SAAS;8BAAA;gCAAA5G,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACxE,CACP;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACE,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,EAGLuD,UAAU,KAAKgF,OAAO,CAACrE,EAAE,iBACxB3E,OAAA;0BAAKoF,SAAS,EAAC,uBAAuB;0BAAA/E,QAAA,eACpCL,OAAA;4BAAKoF,SAAS,EAAC,qBAAqB;4BAAA/E,QAAA,gBAClCL,OAAA;8BAAKoF,SAAS,EAAC,mBAAmB;8BAAA/E,QAAA,EAC/B,CAAAoB,IAAI,aAAJA,IAAI,wBAAA+M,WAAA,GAAJ/M,IAAI,CAAE2H,IAAI,cAAAoF,WAAA,wBAAAC,kBAAA,GAAVD,WAAA,CAAYlF,MAAM,CAAC,CAAC,CAAC,cAAAmF,kBAAA,uBAArBA,kBAAA,CAAuBlF,WAAW,CAAC,CAAC,KAAI;4BAAG;8BAAAjJ,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACzC,CAAC,eACNT,OAAA;8BAAKoF,SAAS,EAAC,qBAAqB;8BAAA/E,QAAA,gBAClCL,OAAA;gCACE+K,KAAK,EAAE7G,SAAU;gCACjB8G,QAAQ,EAAGC,CAAC,IAAK9G,YAAY,CAAC8G,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gCAC9CM,WAAW,EAAG,YAAWrC,OAAO,CAACG,MAAO,KAAK;gCAC7C/D,SAAS,EAAC,aAAa;gCACvB+I,IAAI,EAAC,GAAG;gCACRC,SAAS;8BAAA;gCAAA9N,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACV,CAAC,eACFT,OAAA;gCAAKoF,SAAS,EAAC,eAAe;gCAAA/E,QAAA,gBAC5BL,OAAA;kCACEsL,OAAO,EAAEA,CAAA,KAAMxB,cAAc,CAACd,OAAO,CAACrE,EAAE,CAAE;kCAC1CS,SAAS,EAAC,kBAAkB;kCAC5BiJ,QAAQ,EAAE,CAACnK,SAAS,CAAC+B,IAAI,CAAC,CAAE;kCAAA5F,QAAA,gBAE5BL,OAAA;oCAAAK,QAAA,EAAM;kCAAE;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAM,CAAC,UACjB;gCAAA;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAQ,CAAC,eACTT,OAAA;kCACEsL,OAAO,EAAEA,CAAA,KAAM;oCACbrH,aAAa,CAAC,IAAI,CAAC;oCACnBE,YAAY,CAAC,EAAE,CAAC;kCAClB,CAAE;kCACFiB,SAAS,EAAC,kBAAkB;kCAAA/E,QAAA,EAC7B;gCAED;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAQ,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACN,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CACN,EAGAuI,OAAO,CAACU,OAAO,CAACxC,MAAM,GAAG,CAAC,iBACzBlH,OAAA;0BAAKoF,SAAS,EAAC,SAAS;0BAAA/E,QAAA,EACrB2I,OAAO,CAACU,OAAO,CAACU,GAAG,CAAEF,KAAK;4BAAA,IAAAwE,aAAA,EAAAC,oBAAA;4BAAA,oBACzB3O,OAAA;8BAAoBoF,SAAS,EAAC,OAAO;8BAAA/E,QAAA,eACnCL,OAAA;gCAAKoF,SAAS,EAAC,YAAY;gCAAA/E,QAAA,gBACzBL,OAAA;kCAAKoF,SAAS,EAAC,cAAc;kCAAA/E,QAAA,EAC1B6J,KAAK,CAACb,MAAM,MAAAqF,aAAA,GAAIxE,KAAK,CAACf,MAAM,cAAAuF,aAAA,wBAAAC,oBAAA,GAAZD,aAAA,CAAcpF,MAAM,CAAC,CAAC,CAAC,cAAAqF,oBAAA,uBAAvBA,oBAAA,CAAyBpF,WAAW,CAAC,CAAC,KAAI;gCAAG;kCAAAjJ,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC3D,CAAC,eACNT,OAAA;kCAAKoF,SAAS,EAAC,eAAe;kCAAA/E,QAAA,gBAC5BL,OAAA;oCAAKoF,SAAS,EAAC,cAAc;oCAAA/E,QAAA,gBAC3BL,OAAA;sCAAMoF,SAAS,EAAC,cAAc;sCAAA/E,QAAA,EAAE6J,KAAK,CAACf;oCAAM;sCAAA7I,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAO,CAAC,eACpDT,OAAA;sCAAMoF,SAAS,EAAC,YAAY;sCAAA/E,QAAA,EACzBmK,aAAa,CAACN,KAAK,CAACV,SAAS;oCAAC;sCAAAlJ,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAC3B,CAAC;kCAAA;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACJ,CAAC,eACNT,OAAA;oCAAKoF,SAAS,EAAC,YAAY;oCAAA/E,QAAA,EAAE6J,KAAK,CAAChB;kCAAI;oCAAA5I,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAM,CAAC,eAC9CT,OAAA;oCAAKoF,SAAS,EAAC,eAAe;oCAAA/E,QAAA,eAC5BL,OAAA;sCACEsL,OAAO,EAAEA,CAAA,KAAMjB,iBAAiB,CAACH,KAAK,CAACvF,EAAE,EAAE,IAAI,EAAEqE,OAAO,CAACrE,EAAE,CAAE;sCAC7DS,SAAS,EAAG,kBAAiB8E,KAAK,CAACN,KAAK,GAAG,OAAO,GAAG,EAAG,EAAE;sCAAAvJ,QAAA,gBAE1DL,OAAA;wCAAAK,QAAA,EAAO6J,KAAK,CAACN,KAAK,GAAG,IAAI,GAAG;sCAAI;wCAAAtJ,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OAAO,CAAC,EACvCyJ,KAAK,CAACP,KAAK,GAAG,CAAC,iBAAI3J,OAAA;wCAAMoF,SAAS,EAAC,YAAY;wCAAA/E,QAAA,EAAE6J,KAAK,CAACP;sCAAK;wCAAArJ,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OAAO,CAAC;oCAAA;sCAAAH,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAC/D;kCAAC;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACN,CAAC;gCAAA;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACH,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACH;4BAAC,GAvBEyJ,KAAK,CAACvF,EAAE;8BAAArE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAwBb,CAAC;0BAAA,CACP;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CACN;sBAAA,GA1GOuI,OAAO,CAACrE,EAAE;wBAAArE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OA2Gf,CAAC;oBAAA,CACP;kBACF;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEH,CAAC;QAEV,CAAC,EAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACe,EAAA,CAh6BQD,YAAY;EAAA,QACFhC,WAAW,EAC6BO,WAAW,EACnDR,WAAW;AAAA;AAAAsP,EAAA,GAHrBrN,YAAY;AAk6BrB,eAAeA,YAAY;AAAC,IAAAqN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}