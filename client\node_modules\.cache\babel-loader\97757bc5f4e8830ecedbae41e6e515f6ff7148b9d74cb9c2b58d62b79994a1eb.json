{"ast": null, "code": "/**\n * Webpack has bug for import loop, which is not the same behavior as ES module.\n * When util.js imports the TreeNode for tree generate will cause treeContextTypes be empty.\n */\nimport * as React from 'react';\nexport var TreeContext = /*#__PURE__*/React.createContext(null);", "map": {"version": 3, "names": ["React", "TreeContext", "createContext"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-tree/es/contextTypes.js"], "sourcesContent": ["/**\n * Webpack has bug for import loop, which is not the same behavior as ES module.\n * When util.js imports the TreeNode for tree generate will cause treeContextTypes be empty.\n */\nimport * as React from 'react';\nexport var TreeContext = /*#__PURE__*/React.createContext(null);"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,IAAIC,WAAW,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}