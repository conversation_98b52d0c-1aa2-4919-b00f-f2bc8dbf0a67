{"ast": null, "code": "import useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport React, { useMemo } from 'react';\nimport Select from '../../select';\nimport { ColorFormat } from '../interface';\nimport ColorAlphaInput from './ColorAlphaInput';\nimport ColorHexInput from './ColorHexInput';\nimport ColorHsbInput from './ColorHsbInput';\nimport ColorRgbInput from './ColorRgbInput';\nconst selectOptions = [ColorFormat.hex, ColorFormat.hsb, ColorFormat.rgb].map(format => ({\n  value: format,\n  label: format.toLocaleUpperCase()\n}));\nconst ColorInput = props => {\n  const {\n    prefixCls,\n    format,\n    value,\n    disabledAlpha,\n    onFormatChange,\n    onChange\n  } = props;\n  const [colorFormat, setColorFormat] = useMergedState(ColorFormat.hex, {\n    value: format,\n    onChange: onFormatChange\n  });\n  const colorInputPrefixCls = \"\".concat(prefixCls, \"-input\");\n  const handleFormatChange = newFormat => {\n    setColorFormat(newFormat);\n  };\n  const steppersNode = useMemo(() => {\n    const inputProps = {\n      value,\n      prefixCls,\n      onChange\n    };\n    switch (colorFormat) {\n      case ColorFormat.hsb:\n        return /*#__PURE__*/React.createElement(ColorHsbInput, Object.assign({}, inputProps));\n      case ColorFormat.rgb:\n        return /*#__PURE__*/React.createElement(ColorRgbInput, Object.assign({}, inputProps));\n      case ColorFormat.hex:\n      default:\n        return /*#__PURE__*/React.createElement(ColorHexInput, Object.assign({}, inputProps));\n    }\n  }, [colorFormat, prefixCls, value, onChange]);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(colorInputPrefixCls, \"-container\")\n  }, /*#__PURE__*/React.createElement(Select, {\n    value: colorFormat,\n    bordered: false,\n    getPopupContainer: current => current,\n    popupMatchSelectWidth: 68,\n    placement: \"bottomRight\",\n    onChange: handleFormatChange,\n    className: \"\".concat(prefixCls, \"-format-select\"),\n    size: \"small\",\n    options: selectOptions\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: colorInputPrefixCls\n  }, steppersNode), !disabledAlpha && /*#__PURE__*/React.createElement(ColorAlphaInput, {\n    prefixCls: prefixCls,\n    value: value,\n    onChange: onChange\n  }));\n};\nexport default ColorInput;", "map": {"version": 3, "names": ["useMergedState", "React", "useMemo", "Select", "ColorFormat", "ColorAlphaInput", "ColorHexInput", "ColorHsbInput", "ColorRgbInput", "selectOptions", "hex", "hsb", "rgb", "map", "format", "value", "label", "toLocaleUpperCase", "ColorInput", "props", "prefixCls", "disabledAlpha", "onFormatChange", "onChange", "colorFormat", "setColorFormat", "colorInputPrefixCls", "concat", "handleFormatChange", "newFormat", "steppersNode", "inputProps", "createElement", "Object", "assign", "className", "bordered", "getPopupContainer", "current", "popupMatchSelectWidth", "placement", "size", "options"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/color-picker/components/ColorInput.js"], "sourcesContent": ["import useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport React, { useMemo } from 'react';\nimport Select from '../../select';\nimport { ColorFormat } from '../interface';\nimport ColorAlphaInput from './ColorAlphaInput';\nimport ColorHexInput from './ColorHexInput';\nimport ColorHsbInput from './ColorHsbInput';\nimport ColorRgbInput from './ColorRgbInput';\nconst selectOptions = [ColorFormat.hex, ColorFormat.hsb, ColorFormat.rgb].map(format => ({\n  value: format,\n  label: format.toLocaleUpperCase()\n}));\nconst ColorInput = props => {\n  const {\n    prefixCls,\n    format,\n    value,\n    disabledAlpha,\n    onFormatChange,\n    onChange\n  } = props;\n  const [colorFormat, setColorFormat] = useMergedState(ColorFormat.hex, {\n    value: format,\n    onChange: onFormatChange\n  });\n  const colorInputPrefixCls = `${prefixCls}-input`;\n  const handleFormatChange = newFormat => {\n    setColorFormat(newFormat);\n  };\n  const steppersNode = useMemo(() => {\n    const inputProps = {\n      value,\n      prefixCls,\n      onChange\n    };\n    switch (colorFormat) {\n      case ColorFormat.hsb:\n        return /*#__PURE__*/React.createElement(ColorHsbInput, Object.assign({}, inputProps));\n      case ColorFormat.rgb:\n        return /*#__PURE__*/React.createElement(ColorRgbInput, Object.assign({}, inputProps));\n      case ColorFormat.hex:\n      default:\n        return /*#__PURE__*/React.createElement(ColorHexInput, Object.assign({}, inputProps));\n    }\n  }, [colorFormat, prefixCls, value, onChange]);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: `${colorInputPrefixCls}-container`\n  }, /*#__PURE__*/React.createElement(Select, {\n    value: colorFormat,\n    bordered: false,\n    getPopupContainer: current => current,\n    popupMatchSelectWidth: 68,\n    placement: \"bottomRight\",\n    onChange: handleFormatChange,\n    className: `${prefixCls}-format-select`,\n    size: \"small\",\n    options: selectOptions\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: colorInputPrefixCls\n  }, steppersNode), !disabledAlpha && /*#__PURE__*/React.createElement(ColorAlphaInput, {\n    prefixCls: prefixCls,\n    value: value,\n    onChange: onChange\n  }));\n};\nexport default ColorInput;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,KAAK,IAAIC,OAAO,QAAQ,OAAO;AACtC,OAAOC,MAAM,MAAM,cAAc;AACjC,SAASC,WAAW,QAAQ,cAAc;AAC1C,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,MAAMC,aAAa,GAAG,CAACL,WAAW,CAACM,GAAG,EAAEN,WAAW,CAACO,GAAG,EAAEP,WAAW,CAACQ,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,KAAK;EACvFC,KAAK,EAAED,MAAM;EACbE,KAAK,EAAEF,MAAM,CAACG,iBAAiB,CAAC;AAClC,CAAC,CAAC,CAAC;AACH,MAAMC,UAAU,GAAGC,KAAK,IAAI;EAC1B,MAAM;IACJC,SAAS;IACTN,MAAM;IACNC,KAAK;IACLM,aAAa;IACbC,cAAc;IACdC;EACF,CAAC,GAAGJ,KAAK;EACT,MAAM,CAACK,WAAW,EAAEC,cAAc,CAAC,GAAGzB,cAAc,CAACI,WAAW,CAACM,GAAG,EAAE;IACpEK,KAAK,EAAED,MAAM;IACbS,QAAQ,EAAED;EACZ,CAAC,CAAC;EACF,MAAMI,mBAAmB,MAAAC,MAAA,CAAMP,SAAS,WAAQ;EAChD,MAAMQ,kBAAkB,GAAGC,SAAS,IAAI;IACtCJ,cAAc,CAACI,SAAS,CAAC;EAC3B,CAAC;EACD,MAAMC,YAAY,GAAG5B,OAAO,CAAC,MAAM;IACjC,MAAM6B,UAAU,GAAG;MACjBhB,KAAK;MACLK,SAAS;MACTG;IACF,CAAC;IACD,QAAQC,WAAW;MACjB,KAAKpB,WAAW,CAACO,GAAG;QAClB,OAAO,aAAaV,KAAK,CAAC+B,aAAa,CAACzB,aAAa,EAAE0B,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,UAAU,CAAC,CAAC;MACvF,KAAK3B,WAAW,CAACQ,GAAG;QAClB,OAAO,aAAaX,KAAK,CAAC+B,aAAa,CAACxB,aAAa,EAAEyB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,UAAU,CAAC,CAAC;MACvF,KAAK3B,WAAW,CAACM,GAAG;MACpB;QACE,OAAO,aAAaT,KAAK,CAAC+B,aAAa,CAAC1B,aAAa,EAAE2B,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,UAAU,CAAC,CAAC;IACzF;EACF,CAAC,EAAE,CAACP,WAAW,EAAEJ,SAAS,EAAEL,KAAK,EAAEQ,QAAQ,CAAC,CAAC;EAC7C,OAAO,aAAatB,KAAK,CAAC+B,aAAa,CAAC,KAAK,EAAE;IAC7CG,SAAS,KAAAR,MAAA,CAAKD,mBAAmB;EACnC,CAAC,EAAE,aAAazB,KAAK,CAAC+B,aAAa,CAAC7B,MAAM,EAAE;IAC1CY,KAAK,EAAES,WAAW;IAClBY,QAAQ,EAAE,KAAK;IACfC,iBAAiB,EAAEC,OAAO,IAAIA,OAAO;IACrCC,qBAAqB,EAAE,EAAE;IACzBC,SAAS,EAAE,aAAa;IACxBjB,QAAQ,EAAEK,kBAAkB;IAC5BO,SAAS,KAAAR,MAAA,CAAKP,SAAS,mBAAgB;IACvCqB,IAAI,EAAE,OAAO;IACbC,OAAO,EAAEjC;EACX,CAAC,CAAC,EAAE,aAAaR,KAAK,CAAC+B,aAAa,CAAC,KAAK,EAAE;IAC1CG,SAAS,EAAET;EACb,CAAC,EAAEI,YAAY,CAAC,EAAE,CAACT,aAAa,IAAI,aAAapB,KAAK,CAAC+B,aAAa,CAAC3B,eAAe,EAAE;IACpFe,SAAS,EAAEA,SAAS;IACpBL,KAAK,EAAEA,KAAK;IACZQ,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC;AACL,CAAC;AACD,eAAeL,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}