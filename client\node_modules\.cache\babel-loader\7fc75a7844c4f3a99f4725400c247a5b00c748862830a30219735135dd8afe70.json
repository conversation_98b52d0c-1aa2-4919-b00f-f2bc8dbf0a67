{"ast": null, "code": "const genDraggerStyle = token => {\n  const {\n    componentCls,\n    iconCls\n  } = token;\n  return {\n    [\"\".concat(componentCls, \"-wrapper\")]: {\n      [\"\".concat(componentCls, \"-drag\")]: {\n        position: 'relative',\n        width: '100%',\n        height: '100%',\n        textAlign: 'center',\n        background: token.colorFillAlter,\n        border: \"\".concat(token.lineWidth, \"px dashed \").concat(token.colorBorder),\n        borderRadius: token.borderRadiusLG,\n        cursor: 'pointer',\n        transition: \"border-color \".concat(token.motionDurationSlow),\n        [componentCls]: {\n          padding: \"\".concat(token.padding, \"px 0\")\n        },\n        [\"\".concat(componentCls, \"-btn\")]: {\n          display: 'table',\n          width: '100%',\n          height: '100%',\n          outline: 'none'\n        },\n        [\"\".concat(componentCls, \"-drag-container\")]: {\n          display: 'table-cell',\n          verticalAlign: 'middle'\n        },\n        [\"&:not(\".concat(componentCls, \"-disabled):hover\")]: {\n          borderColor: token.colorPrimaryHover\n        },\n        [\"p\".concat(componentCls, \"-drag-icon\")]: {\n          marginBottom: token.margin,\n          [iconCls]: {\n            color: token.colorPrimary,\n            fontSize: token.uploadThumbnailSize\n          }\n        },\n        [\"p\".concat(componentCls, \"-text\")]: {\n          margin: \"0 0 \".concat(token.marginXXS, \"px\"),\n          color: token.colorTextHeading,\n          fontSize: token.fontSizeLG\n        },\n        [\"p\".concat(componentCls, \"-hint\")]: {\n          color: token.colorTextDescription,\n          fontSize: token.fontSize\n        },\n        // ===================== Disabled =====================\n        [\"&\".concat(componentCls, \"-disabled\")]: {\n          cursor: 'not-allowed',\n          [\"p\".concat(componentCls, \"-drag-icon \").concat(iconCls, \",\\n            p\").concat(componentCls, \"-text,\\n            p\").concat(componentCls, \"-hint\\n          \")]: {\n            color: token.colorTextDisabled\n          }\n        }\n      }\n    }\n  };\n};\nexport default genDraggerStyle;", "map": {"version": 3, "names": ["genDraggerStyle", "token", "componentCls", "iconCls", "concat", "position", "width", "height", "textAlign", "background", "colorFillAlter", "border", "lineWidth", "colorBorder", "borderRadius", "borderRadiusLG", "cursor", "transition", "motionDurationSlow", "padding", "display", "outline", "verticalAlign", "borderColor", "colorPrimaryHover", "marginBottom", "margin", "color", "colorPrimary", "fontSize", "uploadThumbnailSize", "marginXXS", "colorTextHeading", "fontSizeLG", "colorTextDescription", "colorTextDisabled"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/upload/style/dragger.js"], "sourcesContent": ["const genDraggerStyle = token => {\n  const {\n    componentCls,\n    iconCls\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-drag`]: {\n        position: 'relative',\n        width: '100%',\n        height: '100%',\n        textAlign: 'center',\n        background: token.colorFillAlter,\n        border: `${token.lineWidth}px dashed ${token.colorBorder}`,\n        borderRadius: token.borderRadiusLG,\n        cursor: 'pointer',\n        transition: `border-color ${token.motionDurationSlow}`,\n        [componentCls]: {\n          padding: `${token.padding}px 0`\n        },\n        [`${componentCls}-btn`]: {\n          display: 'table',\n          width: '100%',\n          height: '100%',\n          outline: 'none'\n        },\n        [`${componentCls}-drag-container`]: {\n          display: 'table-cell',\n          verticalAlign: 'middle'\n        },\n        [`&:not(${componentCls}-disabled):hover`]: {\n          borderColor: token.colorPrimaryHover\n        },\n        [`p${componentCls}-drag-icon`]: {\n          marginBottom: token.margin,\n          [iconCls]: {\n            color: token.colorPrimary,\n            fontSize: token.uploadThumbnailSize\n          }\n        },\n        [`p${componentCls}-text`]: {\n          margin: `0 0 ${token.marginXXS}px`,\n          color: token.colorTextHeading,\n          fontSize: token.fontSizeLG\n        },\n        [`p${componentCls}-hint`]: {\n          color: token.colorTextDescription,\n          fontSize: token.fontSize\n        },\n        // ===================== Disabled =====================\n        [`&${componentCls}-disabled`]: {\n          cursor: 'not-allowed',\n          [`p${componentCls}-drag-icon ${iconCls},\n            p${componentCls}-text,\n            p${componentCls}-hint\n          `]: {\n            color: token.colorTextDisabled\n          }\n        }\n      }\n    }\n  };\n};\nexport default genDraggerStyle;"], "mappings": "AAAA,MAAMA,eAAe,GAAGC,KAAK,IAAI;EAC/B,MAAM;IACJC,YAAY;IACZC;EACF,CAAC,GAAGF,KAAK;EACT,OAAO;IACL,IAAAG,MAAA,CAAIF,YAAY,gBAAa;MAC3B,IAAAE,MAAA,CAAIF,YAAY,aAAU;QACxBG,QAAQ,EAAE,UAAU;QACpBC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdC,SAAS,EAAE,QAAQ;QACnBC,UAAU,EAAER,KAAK,CAACS,cAAc;QAChCC,MAAM,KAAAP,MAAA,CAAKH,KAAK,CAACW,SAAS,gBAAAR,MAAA,CAAaH,KAAK,CAACY,WAAW,CAAE;QAC1DC,YAAY,EAAEb,KAAK,CAACc,cAAc;QAClCC,MAAM,EAAE,SAAS;QACjBC,UAAU,kBAAAb,MAAA,CAAkBH,KAAK,CAACiB,kBAAkB,CAAE;QACtD,CAAChB,YAAY,GAAG;UACdiB,OAAO,KAAAf,MAAA,CAAKH,KAAK,CAACkB,OAAO;QAC3B,CAAC;QACD,IAAAf,MAAA,CAAIF,YAAY,YAAS;UACvBkB,OAAO,EAAE,OAAO;UAChBd,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdc,OAAO,EAAE;QACX,CAAC;QACD,IAAAjB,MAAA,CAAIF,YAAY,uBAAoB;UAClCkB,OAAO,EAAE,YAAY;UACrBE,aAAa,EAAE;QACjB,CAAC;QACD,UAAAlB,MAAA,CAAUF,YAAY,wBAAqB;UACzCqB,WAAW,EAAEtB,KAAK,CAACuB;QACrB,CAAC;QACD,KAAApB,MAAA,CAAKF,YAAY,kBAAe;UAC9BuB,YAAY,EAAExB,KAAK,CAACyB,MAAM;UAC1B,CAACvB,OAAO,GAAG;YACTwB,KAAK,EAAE1B,KAAK,CAAC2B,YAAY;YACzBC,QAAQ,EAAE5B,KAAK,CAAC6B;UAClB;QACF,CAAC;QACD,KAAA1B,MAAA,CAAKF,YAAY,aAAU;UACzBwB,MAAM,SAAAtB,MAAA,CAASH,KAAK,CAAC8B,SAAS,OAAI;UAClCJ,KAAK,EAAE1B,KAAK,CAAC+B,gBAAgB;UAC7BH,QAAQ,EAAE5B,KAAK,CAACgC;QAClB,CAAC;QACD,KAAA7B,MAAA,CAAKF,YAAY,aAAU;UACzByB,KAAK,EAAE1B,KAAK,CAACiC,oBAAoB;UACjCL,QAAQ,EAAE5B,KAAK,CAAC4B;QAClB,CAAC;QACD;QACA,KAAAzB,MAAA,CAAKF,YAAY,iBAAc;UAC7Bc,MAAM,EAAE,aAAa;UACrB,KAAAZ,MAAA,CAAKF,YAAY,iBAAAE,MAAA,CAAcD,OAAO,sBAAAC,MAAA,CACjCF,YAAY,2BAAAE,MAAA,CACZF,YAAY,yBACb;YACFyB,KAAK,EAAE1B,KAAK,CAACkC;UACf;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAenC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}