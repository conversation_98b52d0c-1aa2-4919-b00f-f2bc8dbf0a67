{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport { fillLegacyProps } from \"../utils/legacyUtil\";\nexport default (function (treeData, searchValue, _ref) {\n  var treeNodeFilterProp = _ref.treeNodeFilterProp,\n    filterTreeNode = _ref.filterTreeNode,\n    fieldNames = _ref.fieldNames;\n  var fieldChildren = fieldNames.children;\n  return React.useMemo(function () {\n    if (!searchValue || filterTreeNode === false) {\n      return treeData;\n    }\n    var filterOptionFunc;\n    if (typeof filterTreeNode === 'function') {\n      filterOptionFunc = filterTreeNode;\n    } else {\n      var upperStr = searchValue.toUpperCase();\n      filterOptionFunc = function filterOptionFunc(_, dataNode) {\n        var value = dataNode[treeNodeFilterProp];\n        return String(value).toUpperCase().includes(upperStr);\n      };\n    }\n    function dig(list) {\n      var keepAll = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      return list.reduce(function (total, dataNode) {\n        var children = dataNode[fieldChildren];\n        var match = keepAll || filterOptionFunc(searchValue, fillLegacyProps(dataNode));\n        var childList = dig(children || [], match);\n        if (match || childList.length) {\n          total.push(_objectSpread(_objectSpread({}, dataNode), {}, _defineProperty({\n            isLeaf: undefined\n          }, fieldChildren, childList)));\n        }\n        return total;\n      }, []);\n    }\n    return dig(treeData);\n  }, [treeData, searchValue, fieldChildren, treeNodeFilterProp, filterTreeNode]);\n});", "map": {"version": 3, "names": ["_defineProperty", "_objectSpread", "React", "fillLegacyProps", "treeData", "searchValue", "_ref", "treeNodeFilterProp", "filterTreeNode", "fieldNames", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "useMemo", "filterOptionFunc", "upperStr", "toUpperCase", "_", "dataNode", "value", "String", "includes", "dig", "list", "keepAll", "arguments", "length", "undefined", "reduce", "total", "match", "childList", "push", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-tree-select/es/hooks/useFilterTreeData.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport { fillLegacyProps } from \"../utils/legacyUtil\";\nexport default (function (treeData, searchValue, _ref) {\n  var treeNodeFilterProp = _ref.treeNodeFilterProp,\n    filterTreeNode = _ref.filterTreeNode,\n    fieldNames = _ref.fieldNames;\n  var fieldChildren = fieldNames.children;\n  return React.useMemo(function () {\n    if (!searchValue || filterTreeNode === false) {\n      return treeData;\n    }\n    var filterOptionFunc;\n    if (typeof filterTreeNode === 'function') {\n      filterOptionFunc = filterTreeNode;\n    } else {\n      var upperStr = searchValue.toUpperCase();\n      filterOptionFunc = function filterOptionFunc(_, dataNode) {\n        var value = dataNode[treeNodeFilterProp];\n        return String(value).toUpperCase().includes(upperStr);\n      };\n    }\n    function dig(list) {\n      var keepAll = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      return list.reduce(function (total, dataNode) {\n        var children = dataNode[fieldChildren];\n        var match = keepAll || filterOptionFunc(searchValue, fillLegacyProps(dataNode));\n        var childList = dig(children || [], match);\n        if (match || childList.length) {\n          total.push(_objectSpread(_objectSpread({}, dataNode), {}, _defineProperty({\n            isLeaf: undefined\n          }, fieldChildren, childList)));\n        }\n        return total;\n      }, []);\n    }\n    return dig(treeData);\n  }, [treeData, searchValue, fieldChildren, treeNodeFilterProp, filterTreeNode]);\n});"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,eAAe,QAAQ,qBAAqB;AACrD,gBAAgB,UAAUC,QAAQ,EAAEC,WAAW,EAAEC,IAAI,EAAE;EACrD,IAAIC,kBAAkB,GAAGD,IAAI,CAACC,kBAAkB;IAC9CC,cAAc,GAAGF,IAAI,CAACE,cAAc;IACpCC,UAAU,GAAGH,IAAI,CAACG,UAAU;EAC9B,IAAIC,aAAa,GAAGD,UAAU,CAACE,QAAQ;EACvC,OAAOT,KAAK,CAACU,OAAO,CAAC,YAAY;IAC/B,IAAI,CAACP,WAAW,IAAIG,cAAc,KAAK,KAAK,EAAE;MAC5C,OAAOJ,QAAQ;IACjB;IACA,IAAIS,gBAAgB;IACpB,IAAI,OAAOL,cAAc,KAAK,UAAU,EAAE;MACxCK,gBAAgB,GAAGL,cAAc;IACnC,CAAC,MAAM;MACL,IAAIM,QAAQ,GAAGT,WAAW,CAACU,WAAW,CAAC,CAAC;MACxCF,gBAAgB,GAAG,SAASA,gBAAgBA,CAACG,CAAC,EAAEC,QAAQ,EAAE;QACxD,IAAIC,KAAK,GAAGD,QAAQ,CAACV,kBAAkB,CAAC;QACxC,OAAOY,MAAM,CAACD,KAAK,CAAC,CAACH,WAAW,CAAC,CAAC,CAACK,QAAQ,CAACN,QAAQ,CAAC;MACvD,CAAC;IACH;IACA,SAASO,GAAGA,CAACC,IAAI,EAAE;MACjB,IAAIC,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;MACvF,OAAOF,IAAI,CAACK,MAAM,CAAC,UAAUC,KAAK,EAAEX,QAAQ,EAAE;QAC5C,IAAIN,QAAQ,GAAGM,QAAQ,CAACP,aAAa,CAAC;QACtC,IAAImB,KAAK,GAAGN,OAAO,IAAIV,gBAAgB,CAACR,WAAW,EAAEF,eAAe,CAACc,QAAQ,CAAC,CAAC;QAC/E,IAAIa,SAAS,GAAGT,GAAG,CAACV,QAAQ,IAAI,EAAE,EAAEkB,KAAK,CAAC;QAC1C,IAAIA,KAAK,IAAIC,SAAS,CAACL,MAAM,EAAE;UAC7BG,KAAK,CAACG,IAAI,CAAC9B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgB,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAEjB,eAAe,CAAC;YACxEgC,MAAM,EAAEN;UACV,CAAC,EAAEhB,aAAa,EAAEoB,SAAS,CAAC,CAAC,CAAC;QAChC;QACA,OAAOF,KAAK;MACd,CAAC,EAAE,EAAE,CAAC;IACR;IACA,OAAOP,GAAG,CAACjB,QAAQ,CAAC;EACtB,CAAC,EAAE,CAACA,QAAQ,EAAEC,WAAW,EAAEK,aAAa,EAAEH,kBAAkB,EAAEC,cAAc,CAAC,CAAC;AAChF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}