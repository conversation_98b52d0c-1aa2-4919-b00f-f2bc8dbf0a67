{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { Suspense, lazy } from \"react\";\nimport \"./stylesheets/theme.css\";\nimport \"./stylesheets/alignments.css\";\nimport \"./stylesheets/textelements.css\";\nimport \"./stylesheets/form-elements.css\";\nimport \"./stylesheets/custom-components.css\";\nimport \"./stylesheets/layout.css\";\nimport \"./styles/modern.css\";\nimport \"./styles/animations.css\";\nimport { BrowserRouter, Routes, Route } from \"react-router-dom\";\nimport ProtectedRoute from \"./components/ProtectedRoute\";\nimport Loader from \"./components/Loader\";\nimport { useSelector } from \"react-redux\";\nimport { ThemeProvider } from \"./contexts/ThemeContext\";\nimport { LanguageProvider } from \"./contexts/LanguageContext\";\nimport { ErrorBoundary } from \"./components/modern\";\nimport AdminProtectedRoute from \"./components/AdminProtectedRoute\";\n\n// Immediate load components (critical for initial render)\nimport Login from \"./pages/common/Login\";\nimport Register from \"./pages/common/Register\";\nimport Home from \"./pages/common/Home\";\n\n// Lazy load components for better performance\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Quiz = /*#__PURE__*/lazy(_c = () => import(\"./pages/user/Quiz\"));\n_c2 = Quiz;\nconst QuizPlay = /*#__PURE__*/lazy(_c3 = () => import(\"./pages/user/Quiz/QuizPlay\"));\n_c4 = QuizPlay;\nconst QuizResult = /*#__PURE__*/lazy(_c5 = () => import(\"./pages/user/Quiz/QuizResult\"));\n_c6 = QuizResult;\nconst Exams = /*#__PURE__*/lazy(_c7 = () => import(\"./pages/admin/Exams\"));\n_c8 = Exams;\nconst AddEditExam = /*#__PURE__*/lazy(_c9 = () => import(\"./pages/admin/Exams/AddEditExam\"));\n_c10 = AddEditExam;\nconst Users = /*#__PURE__*/lazy(_c11 = () => import(\"./pages/admin/Users\"));\n_c12 = Users;\nconst AdminDashboard = /*#__PURE__*/lazy(_c13 = () => import(\"./pages/admin/Dashboard\"));\n_c14 = AdminDashboard;\nconst TrialPage = /*#__PURE__*/lazy(_c15 = () => import(\"./pages/trial/TrialPage\"));\n_c16 = TrialPage;\nconst WriteExam = /*#__PURE__*/lazy(_c17 = () => import(\"./pages/user/WriteExam\"));\n_c18 = WriteExam;\nconst UserReports = /*#__PURE__*/lazy(_c19 = () => import(\"./pages/user/UserReports\"));\n_c20 = UserReports;\nconst AdminReports = /*#__PURE__*/lazy(_c21 = () => import(\"./pages/admin/AdminReports\"));\n_c22 = AdminReports;\nconst StudyMaterial = /*#__PURE__*/lazy(_c23 = () => import(\"./pages/user/StudyMaterial\"));\n_c24 = StudyMaterial;\nconst VideoLessons = /*#__PURE__*/lazy(_c25 = () => import(\"./pages/user/VideoLessons\"));\n_c26 = VideoLessons;\nconst Skills = /*#__PURE__*/lazy(_c27 = () => import(\"./pages/user/Skills\"));\n_c28 = Skills;\nconst Ranking = /*#__PURE__*/lazy(_c29 = () => import(\"./pages/user/Ranking\"));\n_c30 = Ranking;\nconst RankingErrorBoundary = /*#__PURE__*/lazy(_c31 = () => import(\"./components/RankingErrorBoundary\"));\n_c32 = RankingErrorBoundary;\nconst Profile = /*#__PURE__*/lazy(_c33 = () => import(\"./pages/common/Profile\"));\n_c34 = Profile;\nconst AboutUs = /*#__PURE__*/lazy(_c35 = () => import(\"./pages/user/AboutUs\"));\n_c36 = AboutUs;\nconst Forum = /*#__PURE__*/lazy(_c37 = () => import(\"./pages/common/Forum\"));\n_c38 = Forum;\nconst Test = /*#__PURE__*/lazy(_c39 = () => import(\"./pages/user/Test\"));\n_c40 = Test;\nconst Subscription = /*#__PURE__*/lazy(_c41 = () => import(\"./pages/user/Subscription\"));\n_c42 = Subscription;\nconst Hub = /*#__PURE__*/lazy(_c43 = () => import(\"./pages/user/Hub\"));\n_c44 = Hub;\nconst AdminStudyMaterials = /*#__PURE__*/lazy(_c45 = () => import(\"./pages/admin/StudyMaterials\"));\n_c46 = AdminStudyMaterials;\nconst AdminSkills = /*#__PURE__*/lazy(_c47 = () => import(\"./pages/admin/Skills\"));\n_c48 = AdminSkills;\nconst AdminNotifications = /*#__PURE__*/lazy(_c49 = () => import(\"./pages/admin/Notifications/AdminNotifications\"));\n_c50 = AdminNotifications;\nconst AdminForum = /*#__PURE__*/lazy(_c51 = () => import(\"./pages/admin/Forum\"));\n_c52 = AdminForum;\nconst DebugAuth = /*#__PURE__*/lazy(_c53 = () => import(\"./components/DebugAuth\"));\n_c54 = DebugAuth;\nconst RankingDemo = /*#__PURE__*/lazy(_c55 = () => import(\"./components/modern/RankingDemo\"));\n\n// Global error handler for CSS style errors and null reference errors\n_c56 = RankingDemo;\nwindow.addEventListener('error', event => {\n  if (event.message && (event.message.includes('Indexed property setter is not supported') || event.message.includes('Cannot read properties of null') || event.message.includes('Cannot read property \\'style\\''))) {\n    console.warn('DOM/Style Error caught and handled:', event.message);\n    event.preventDefault();\n    return false;\n  }\n});\n\n// Handle unhandled promise rejections that might be related to style errors\nwindow.addEventListener('unhandledrejection', event => {\n  if (event.reason && event.reason.message && (event.reason.message.includes('Indexed property setter is not supported') || event.reason.message.includes('Cannot read properties of null') || event.reason.message.includes('Cannot read property \\'style\\''))) {\n    console.warn('DOM/Style Promise Rejection caught and handled:', event.reason.message);\n    event.preventDefault();\n  }\n});\n// Fast loading component for lazy routes\nconst FastLoader = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"flex items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n  children: /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-center\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 rounded-full h-12 w-12 border-t-2 border-blue-300 mx-auto animate-pulse\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-gray-600 font-medium\",\n      children: \"Loading page...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-gray-400 text-sm mt-2\",\n      children: \"Please wait a moment\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 82,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 81,\n  columnNumber: 3\n}, this);\n_c57 = FastLoader;\nfunction App() {\n  _s();\n  const {\n    loading\n  } = useSelector(state => state.loader);\n  return /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n    children: /*#__PURE__*/_jsxDEV(ThemeProvider, {\n      children: /*#__PURE__*/_jsxDEV(LanguageProvider, {\n        children: [loading && /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 23\n        }, this), /*#__PURE__*/_jsxDEV(BrowserRouter, {\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/login\",\n              element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/register\",\n              element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 44\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 36\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/ranking-demo\",\n              element: /*#__PURE__*/_jsxDEV(RankingDemo, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/trial\",\n              element: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 110,\n                  columnNumber: 33\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(TrialPage, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 111,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 13\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/test\",\n              element: /*#__PURE__*/_jsxDEV(Suspense, {\n                fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 33\n                }, this),\n                children: /*#__PURE__*/_jsxDEV(Test, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 117,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 13\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/forum\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Suspense, {\n                  fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 124,\n                    columnNumber: 37\n                  }, this),\n                  children: /*#__PURE__*/_jsxDEV(Forum, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 125,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 124,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/profile\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Suspense, {\n                  fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 136,\n                    columnNumber: 37\n                  }, this),\n                  children: /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 137,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/user/profile\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Suspense, {\n                  fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 146,\n                    columnNumber: 37\n                  }, this),\n                  children: /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 147,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/subscription\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Suspense, {\n                  fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 157,\n                    columnNumber: 37\n                  }, this),\n                  children: /*#__PURE__*/_jsxDEV(Subscription, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 158,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/user/subscription\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Suspense, {\n                  fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 167,\n                    columnNumber: 37\n                  }, this),\n                  children: /*#__PURE__*/_jsxDEV(Subscription, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 168,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/user/hub\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Suspense, {\n                  fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 178,\n                    columnNumber: 37\n                  }, this),\n                  children: /*#__PURE__*/_jsxDEV(Hub, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 179,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/user/quiz\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Suspense, {\n                  fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 188,\n                    columnNumber: 37\n                  }, this),\n                  children: /*#__PURE__*/_jsxDEV(Quiz, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 189,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/user/write-exam/:id\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Suspense, {\n                  fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 37\n                  }, this),\n                  children: /*#__PURE__*/_jsxDEV(WriteExam, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 200,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/quiz/:id/result\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Suspense, {\n                  fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 209,\n                    columnNumber: 37\n                  }, this),\n                  children: /*#__PURE__*/_jsxDEV(QuizResult, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 210,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/quiz/:id/play\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(QuizPlay, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/user/reports\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Suspense, {\n                  fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 231,\n                    columnNumber: 37\n                  }, this),\n                  children: /*#__PURE__*/_jsxDEV(UserReports, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 232,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/user/study-material\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Suspense, {\n                  fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 37\n                  }, this),\n                  children: /*#__PURE__*/_jsxDEV(StudyMaterial, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/user/video-lessons\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Suspense, {\n                  fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 37\n                  }, this),\n                  children: /*#__PURE__*/_jsxDEV(VideoLessons, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 252,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/user/skills\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Suspense, {\n                  fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 37\n                  }, this),\n                  children: /*#__PURE__*/_jsxDEV(Skills, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 262,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/user/ranking\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Suspense, {\n                  fallback: /*#__PURE__*/_jsxDEV(FastLoader, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 271,\n                    columnNumber: 37\n                  }, this),\n                  children: /*#__PURE__*/_jsxDEV(RankingErrorBoundary, {\n                    children: /*#__PURE__*/_jsxDEV(Ranking, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 273,\n                      columnNumber: 21\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 272,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/user/about-us\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AboutUs, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/admin/dashboard\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 294,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/admin/users\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(Users, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 304,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/admin/exams\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(Exams, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/admin/exams/add\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(AddEditExam, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 324,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/admin/exams/edit/:id\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(AddEditExam, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 335,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/admin/study-materials\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(AdminStudyMaterials, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 346,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/admin/skills\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(AdminSkills, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 356,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/admin/reports\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(AdminReports, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 367,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/admin/notifications\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(AdminNotifications, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 378,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/admin/forum\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(AdminForum, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 389,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 388,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/admin/debug\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(AdminProtectedRoute, {\n                  children: /*#__PURE__*/_jsxDEV(DebugAuth, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 400,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 11\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 9\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 96,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"qPH7E8ZBRBZG+ChS5bGqdjAK4Pc=\", false, function () {\n  return [useSelector];\n});\n_c58 = App;\nexport default App;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28, _c29, _c30, _c31, _c32, _c33, _c34, _c35, _c36, _c37, _c38, _c39, _c40, _c41, _c42, _c43, _c44, _c45, _c46, _c47, _c48, _c49, _c50, _c51, _c52, _c53, _c54, _c55, _c56, _c57, _c58;\n$RefreshReg$(_c, \"Quiz$lazy\");\n$RefreshReg$(_c2, \"Quiz\");\n$RefreshReg$(_c3, \"QuizPlay$lazy\");\n$RefreshReg$(_c4, \"QuizPlay\");\n$RefreshReg$(_c5, \"QuizResult$lazy\");\n$RefreshReg$(_c6, \"QuizResult\");\n$RefreshReg$(_c7, \"Exams$lazy\");\n$RefreshReg$(_c8, \"Exams\");\n$RefreshReg$(_c9, \"AddEditExam$lazy\");\n$RefreshReg$(_c10, \"AddEditExam\");\n$RefreshReg$(_c11, \"Users$lazy\");\n$RefreshReg$(_c12, \"Users\");\n$RefreshReg$(_c13, \"AdminDashboard$lazy\");\n$RefreshReg$(_c14, \"AdminDashboard\");\n$RefreshReg$(_c15, \"TrialPage$lazy\");\n$RefreshReg$(_c16, \"TrialPage\");\n$RefreshReg$(_c17, \"WriteExam$lazy\");\n$RefreshReg$(_c18, \"WriteExam\");\n$RefreshReg$(_c19, \"UserReports$lazy\");\n$RefreshReg$(_c20, \"UserReports\");\n$RefreshReg$(_c21, \"AdminReports$lazy\");\n$RefreshReg$(_c22, \"AdminReports\");\n$RefreshReg$(_c23, \"StudyMaterial$lazy\");\n$RefreshReg$(_c24, \"StudyMaterial\");\n$RefreshReg$(_c25, \"VideoLessons$lazy\");\n$RefreshReg$(_c26, \"VideoLessons\");\n$RefreshReg$(_c27, \"Skills$lazy\");\n$RefreshReg$(_c28, \"Skills\");\n$RefreshReg$(_c29, \"Ranking$lazy\");\n$RefreshReg$(_c30, \"Ranking\");\n$RefreshReg$(_c31, \"RankingErrorBoundary$lazy\");\n$RefreshReg$(_c32, \"RankingErrorBoundary\");\n$RefreshReg$(_c33, \"Profile$lazy\");\n$RefreshReg$(_c34, \"Profile\");\n$RefreshReg$(_c35, \"AboutUs$lazy\");\n$RefreshReg$(_c36, \"AboutUs\");\n$RefreshReg$(_c37, \"Forum$lazy\");\n$RefreshReg$(_c38, \"Forum\");\n$RefreshReg$(_c39, \"Test$lazy\");\n$RefreshReg$(_c40, \"Test\");\n$RefreshReg$(_c41, \"Subscription$lazy\");\n$RefreshReg$(_c42, \"Subscription\");\n$RefreshReg$(_c43, \"Hub$lazy\");\n$RefreshReg$(_c44, \"Hub\");\n$RefreshReg$(_c45, \"AdminStudyMaterials$lazy\");\n$RefreshReg$(_c46, \"AdminStudyMaterials\");\n$RefreshReg$(_c47, \"AdminSkills$lazy\");\n$RefreshReg$(_c48, \"AdminSkills\");\n$RefreshReg$(_c49, \"AdminNotifications$lazy\");\n$RefreshReg$(_c50, \"AdminNotifications\");\n$RefreshReg$(_c51, \"AdminForum$lazy\");\n$RefreshReg$(_c52, \"AdminForum\");\n$RefreshReg$(_c53, \"DebugAuth$lazy\");\n$RefreshReg$(_c54, \"DebugAuth\");\n$RefreshReg$(_c55, \"RankingDemo$lazy\");\n$RefreshReg$(_c56, \"RankingDemo\");\n$RefreshReg$(_c57, \"FastLoader\");\n$RefreshReg$(_c58, \"App\");", "map": {"version": 3, "names": ["React", "Suspense", "lazy", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "ProtectedRoute", "Loader", "useSelector", "ThemeProvider", "LanguageProvider", "Error<PERSON>ou<PERSON><PERSON>", "AdminProtectedRoute", "<PERSON><PERSON>", "Register", "Home", "jsxDEV", "_jsxDEV", "Quiz", "_c", "_c2", "QuizPlay", "_c3", "_c4", "QuizResult", "_c5", "_c6", "<PERSON><PERSON>", "_c7", "_c8", "AddEditExam", "_c9", "_c10", "Users", "_c11", "_c12", "AdminDashboard", "_c13", "_c14", "TrialPage", "_c15", "_c16", "WriteExam", "_c17", "_c18", "UserReports", "_c19", "_c20", "AdminReports", "_c21", "_c22", "StudyMaterial", "_c23", "_c24", "VideoLessons", "_c25", "_c26", "Skills", "_c27", "_c28", "Ranking", "_c29", "_c30", "RankingError<PERSON><PERSON><PERSON>ry", "_c31", "_c32", "Profile", "_c33", "_c34", "AboutUs", "_c35", "_c36", "Forum", "_c37", "_c38", "Test", "_c39", "_c40", "Subscription", "_c41", "_c42", "<PERSON><PERSON>", "_c43", "_c44", "AdminStudyMaterials", "_c45", "_c46", "AdminSkills", "_c47", "_c48", "AdminNotifications", "_c49", "_c50", "AdminForum", "_c51", "_c52", "DebugAuth", "_c53", "_c54", "RankingDemo", "_c55", "_c56", "window", "addEventListener", "event", "message", "includes", "console", "warn", "preventDefault", "reason", "FastLoader", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c57", "App", "_s", "loading", "state", "loader", "path", "element", "fallback", "_c58", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/App.js"], "sourcesContent": ["import React, { Suspense, lazy } from \"react\";\r\nimport \"./stylesheets/theme.css\";\r\nimport \"./stylesheets/alignments.css\";\r\nimport \"./stylesheets/textelements.css\";\r\nimport \"./stylesheets/form-elements.css\";\r\nimport \"./stylesheets/custom-components.css\";\r\nimport \"./stylesheets/layout.css\";\r\nimport \"./styles/modern.css\";\r\nimport \"./styles/animations.css\";\r\nimport { BrowserRouter, Routes, Route } from \"react-router-dom\";\r\nimport ProtectedRoute from \"./components/ProtectedRoute\";\r\nimport Loader from \"./components/Loader\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { ThemeProvider } from \"./contexts/ThemeContext\";\r\nimport { LanguageProvider } from \"./contexts/LanguageContext\";\r\nimport { ErrorBoundary } from \"./components/modern\";\r\nimport AdminProtectedRoute from \"./components/AdminProtectedRoute\";\r\n\r\n// Immediate load components (critical for initial render)\r\nimport Login from \"./pages/common/Login\";\r\nimport Register from \"./pages/common/Register\";\r\nimport Home from \"./pages/common/Home\";\r\n\r\n// Lazy load components for better performance\r\nconst Quiz = lazy(() => import(\"./pages/user/Quiz\"));\r\nconst QuizPlay = lazy(() => import(\"./pages/user/Quiz/QuizPlay\"));\r\nconst QuizResult = lazy(() => import(\"./pages/user/Quiz/QuizResult\"));\r\nconst Exams = lazy(() => import(\"./pages/admin/Exams\"));\r\nconst AddEditExam = lazy(() => import(\"./pages/admin/Exams/AddEditExam\"));\r\nconst Users = lazy(() => import(\"./pages/admin/Users\"));\r\nconst AdminDashboard = lazy(() => import(\"./pages/admin/Dashboard\"));\r\nconst TrialPage = lazy(() => import(\"./pages/trial/TrialPage\"));\r\nconst WriteExam = lazy(() => import(\"./pages/user/WriteExam\"));\r\nconst UserReports = lazy(() => import(\"./pages/user/UserReports\"));\r\nconst AdminReports = lazy(() => import(\"./pages/admin/AdminReports\"));\r\nconst StudyMaterial = lazy(() => import(\"./pages/user/StudyMaterial\"));\r\nconst VideoLessons = lazy(() => import(\"./pages/user/VideoLessons\"));\r\nconst Skills = lazy(() => import(\"./pages/user/Skills\"));\r\nconst Ranking = lazy(() => import(\"./pages/user/Ranking\"));\r\nconst RankingErrorBoundary = lazy(() => import(\"./components/RankingErrorBoundary\"));\r\nconst Profile = lazy(() => import(\"./pages/common/Profile\"));\r\nconst AboutUs = lazy(() => import(\"./pages/user/AboutUs\"));\r\nconst Forum = lazy(() => import(\"./pages/common/Forum\"));\r\nconst Test = lazy(() => import(\"./pages/user/Test\"));\r\nconst Subscription = lazy(() => import(\"./pages/user/Subscription\"));\r\n\r\nconst Hub = lazy(() => import(\"./pages/user/Hub\"));\r\nconst AdminStudyMaterials = lazy(() => import(\"./pages/admin/StudyMaterials\"));\r\nconst AdminSkills = lazy(() => import(\"./pages/admin/Skills\"));\r\nconst AdminNotifications = lazy(() => import(\"./pages/admin/Notifications/AdminNotifications\"));\r\nconst AdminForum = lazy(() => import(\"./pages/admin/Forum\"));\r\nconst DebugAuth = lazy(() => import(\"./components/DebugAuth\"));\r\nconst RankingDemo = lazy(() => import(\"./components/modern/RankingDemo\"));\r\n\r\n// Global error handler for CSS style errors and null reference errors\r\nwindow.addEventListener('error', (event) => {\r\n  if (event.message && (\r\n    event.message.includes('Indexed property setter is not supported') ||\r\n    event.message.includes('Cannot read properties of null') ||\r\n    event.message.includes('Cannot read property \\'style\\'')\r\n  )) {\r\n    console.warn('DOM/Style Error caught and handled:', event.message);\r\n    event.preventDefault();\r\n    return false;\r\n  }\r\n});\r\n\r\n// Handle unhandled promise rejections that might be related to style errors\r\nwindow.addEventListener('unhandledrejection', (event) => {\r\n  if (event.reason && event.reason.message && (\r\n    event.reason.message.includes('Indexed property setter is not supported') ||\r\n    event.reason.message.includes('Cannot read properties of null') ||\r\n    event.reason.message.includes('Cannot read property \\'style\\'')\r\n  )) {\r\n    console.warn('DOM/Style Promise Rejection caught and handled:', event.reason.message);\r\n    event.preventDefault();\r\n  }\r\n});\r\n// Fast loading component for lazy routes\r\nconst FastLoader = () => (\r\n  <div className=\"flex items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\r\n    <div className=\"text-center\">\r\n      <div className=\"relative\">\r\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\r\n        <div className=\"absolute inset-0 rounded-full h-12 w-12 border-t-2 border-blue-300 mx-auto animate-pulse\"></div>\r\n      </div>\r\n      <p className=\"text-gray-600 font-medium\">Loading page...</p>\r\n      <p className=\"text-gray-400 text-sm mt-2\">Please wait a moment</p>\r\n    </div>\r\n  </div>\r\n);\r\n\r\nfunction App() {\r\n  const { loading } = useSelector((state) => state.loader);\r\n  return (\r\n    <ErrorBoundary>\r\n      <ThemeProvider>\r\n        <LanguageProvider>\r\n          {loading && <Loader />}\r\n        <BrowserRouter>\r\n        <Routes>\r\n          {/* Common Routes */}\r\n          <Route path=\"/login\" element={<Login />} />\r\n          <Route path=\"/register\" element={<Register />} />\r\n          <Route path=\"/\" element={<Home />} />\r\n          <Route path=\"/ranking-demo\" element={<RankingDemo />} />\r\n\r\n          {/* Trial Route (No authentication required) */}\r\n          <Route path=\"/trial\" element={\r\n            <Suspense fallback={<FastLoader />}>\r\n              <TrialPage />\r\n            </Suspense>\r\n          } />\r\n\r\n          <Route path=\"/test\" element={\r\n            <Suspense fallback={<FastLoader />}>\r\n              <Test />\r\n            </Suspense>\r\n          } />\r\n          <Route\r\n            path=\"/forum\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Forum />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          {/* User Routes */}\r\n          <Route\r\n            path=\"/profile\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Profile />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/profile\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Profile />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/subscription\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Subscription />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/subscription\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Subscription />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/user/hub\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Hub />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/quiz\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Quiz />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/user/write-exam/:id\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <WriteExam />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/quiz/:id/result\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <QuizResult />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          {/* New Quiz Routes */}\r\n\r\n          <Route\r\n            path=\"/quiz/:id/play\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <QuizPlay />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/user/reports\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <UserReports />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/study-material\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <StudyMaterial />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/video-lessons\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <VideoLessons />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/skills\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <Skills />\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/ranking\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Suspense fallback={<FastLoader />}>\r\n                  <RankingErrorBoundary>\r\n                    <Ranking />\r\n                  </RankingErrorBoundary>\r\n                </Suspense>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/user/about-us\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AboutUs />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          {/* Admin Routes */}\r\n          <Route\r\n            path=\"/admin/dashboard\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminDashboard />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/admin/users\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <Users />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/admin/exams\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <Exams />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/admin/exams/add\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AddEditExam />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/exams/edit/:id\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AddEditExam />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/study-materials\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminStudyMaterials />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/admin/skills\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminSkills />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/reports\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminReports />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/notifications\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminNotifications />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/forum\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <AdminForum />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          <Route\r\n            path=\"/admin/debug\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <AdminProtectedRoute>\r\n                  <DebugAuth />\r\n                </AdminProtectedRoute>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n        </Routes>\r\n      </BrowserRouter>\r\n        </LanguageProvider>\r\n    </ThemeProvider>\r\n    </ErrorBoundary>\r\n  );\r\n}\r\n\r\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,IAAI,QAAQ,OAAO;AAC7C,OAAO,yBAAyB;AAChC,OAAO,8BAA8B;AACrC,OAAO,gCAAgC;AACvC,OAAO,iCAAiC;AACxC,OAAO,qCAAqC;AAC5C,OAAO,0BAA0B;AACjC,OAAO,qBAAqB;AAC5B,OAAO,yBAAyB;AAChC,SAASC,aAAa,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AAC/D,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,gBAAgB,QAAQ,4BAA4B;AAC7D,SAASC,aAAa,QAAQ,qBAAqB;AACnD,OAAOC,mBAAmB,MAAM,kCAAkC;;AAElE;AACA,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAOC,QAAQ,MAAM,yBAAyB;AAC9C,OAAOC,IAAI,MAAM,qBAAqB;;AAEtC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,IAAI,gBAAGhB,IAAI,CAAAiB,EAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,GAAA,GAA/CF,IAAI;AACV,MAAMG,QAAQ,gBAAGnB,IAAI,CAAAoB,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC;AAACC,GAAA,GAA5DF,QAAQ;AACd,MAAMG,UAAU,gBAAGtB,IAAI,CAAAuB,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC;AAACC,GAAA,GAAhEF,UAAU;AAChB,MAAMG,KAAK,gBAAGzB,IAAI,CAAA0B,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC;AAACC,GAAA,GAAlDF,KAAK;AACX,MAAMG,WAAW,gBAAG5B,IAAI,CAAA6B,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC,CAAC;AAACC,IAAA,GAApEF,WAAW;AACjB,MAAMG,KAAK,gBAAG/B,IAAI,CAAAgC,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC;AAACC,IAAA,GAAlDF,KAAK;AACX,MAAMG,cAAc,gBAAGlC,IAAI,CAAAmC,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC;AAACC,IAAA,GAA/DF,cAAc;AACpB,MAAMG,SAAS,gBAAGrC,IAAI,CAAAsC,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC;AAACC,IAAA,GAA1DF,SAAS;AACf,MAAMG,SAAS,gBAAGxC,IAAI,CAAAyC,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAAC;AAACC,IAAA,GAAzDF,SAAS;AACf,MAAMG,WAAW,gBAAG3C,IAAI,CAAA4C,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC,CAAC;AAACC,IAAA,GAA7DF,WAAW;AACjB,MAAMG,YAAY,gBAAG9C,IAAI,CAAA+C,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC;AAACC,IAAA,GAAhEF,YAAY;AAClB,MAAMG,aAAa,gBAAGjD,IAAI,CAAAkD,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC;AAACC,IAAA,GAAjEF,aAAa;AACnB,MAAMG,YAAY,gBAAGpD,IAAI,CAAAqD,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC,CAAC;AAACC,IAAA,GAA/DF,YAAY;AAClB,MAAMG,MAAM,gBAAGvD,IAAI,CAAAwD,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC;AAACC,IAAA,GAAnDF,MAAM;AACZ,MAAMG,OAAO,gBAAG1D,IAAI,CAAA2D,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAACC,IAAA,GAArDF,OAAO;AACb,MAAMG,oBAAoB,gBAAG7D,IAAI,CAAA8D,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC,CAAC;AAACC,IAAA,GAA/EF,oBAAoB;AAC1B,MAAMG,OAAO,gBAAGhE,IAAI,CAAAiE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAAC;AAACC,IAAA,GAAvDF,OAAO;AACb,MAAMG,OAAO,gBAAGnE,IAAI,CAAAoE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAACC,IAAA,GAArDF,OAAO;AACb,MAAMG,KAAK,gBAAGtE,IAAI,CAAAuE,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAACC,IAAA,GAAnDF,KAAK;AACX,MAAMG,IAAI,gBAAGzE,IAAI,CAAA0E,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,IAAA,GAA/CF,IAAI;AACV,MAAMG,YAAY,gBAAG5E,IAAI,CAAA6E,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC,CAAC;AAACC,IAAA,GAA/DF,YAAY;AAElB,MAAMG,GAAG,gBAAG/E,IAAI,CAAAgF,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,kBAAkB,CAAC,CAAC;AAACC,IAAA,GAA7CF,GAAG;AACT,MAAMG,mBAAmB,gBAAGlF,IAAI,CAAAmF,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC;AAACC,IAAA,GAAzEF,mBAAmB;AACzB,MAAMG,WAAW,gBAAGrF,IAAI,CAAAsF,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAACC,IAAA,GAAzDF,WAAW;AACjB,MAAMG,kBAAkB,gBAAGxF,IAAI,CAAAyF,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,gDAAgD,CAAC,CAAC;AAACC,IAAA,GAA1FF,kBAAkB;AACxB,MAAMG,UAAU,gBAAG3F,IAAI,CAAA4F,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC;AAACC,IAAA,GAAvDF,UAAU;AAChB,MAAMG,SAAS,gBAAG9F,IAAI,CAAA+F,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAAC;AAACC,IAAA,GAAzDF,SAAS;AACf,MAAMG,WAAW,gBAAGjG,IAAI,CAAAkG,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC,CAAC;;AAEzE;AAAAC,IAAA,GAFMF,WAAW;AAGjBG,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;EAC1C,IAAIA,KAAK,CAACC,OAAO,KACfD,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,0CAA0C,CAAC,IAClEF,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,gCAAgC,CAAC,IACxDF,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,gCAAgC,CAAC,CACzD,EAAE;IACDC,OAAO,CAACC,IAAI,CAAC,qCAAqC,EAAEJ,KAAK,CAACC,OAAO,CAAC;IAClED,KAAK,CAACK,cAAc,CAAC,CAAC;IACtB,OAAO,KAAK;EACd;AACF,CAAC,CAAC;;AAEF;AACAP,MAAM,CAACC,gBAAgB,CAAC,oBAAoB,EAAGC,KAAK,IAAK;EACvD,IAAIA,KAAK,CAACM,MAAM,IAAIN,KAAK,CAACM,MAAM,CAACL,OAAO,KACtCD,KAAK,CAACM,MAAM,CAACL,OAAO,CAACC,QAAQ,CAAC,0CAA0C,CAAC,IACzEF,KAAK,CAACM,MAAM,CAACL,OAAO,CAACC,QAAQ,CAAC,gCAAgC,CAAC,IAC/DF,KAAK,CAACM,MAAM,CAACL,OAAO,CAACC,QAAQ,CAAC,gCAAgC,CAAC,CAChE,EAAE;IACDC,OAAO,CAACC,IAAI,CAAC,iDAAiD,EAAEJ,KAAK,CAACM,MAAM,CAACL,OAAO,CAAC;IACrFD,KAAK,CAACK,cAAc,CAAC,CAAC;EACxB;AACF,CAAC,CAAC;AACF;AACA,MAAME,UAAU,GAAGA,CAAA,kBACjB9F,OAAA;EAAK+F,SAAS,EAAC,4FAA4F;EAAAC,QAAA,eACzGhG,OAAA;IAAK+F,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1BhG,OAAA;MAAK+F,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACvBhG,OAAA;QAAK+F,SAAS,EAAC;MAA6E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACnGpG,OAAA;QAAK+F,SAAS,EAAC;MAA0F;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7G,CAAC,eACNpG,OAAA;MAAG+F,SAAS,EAAC,2BAA2B;MAAAC,QAAA,EAAC;IAAe;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAC5DpG,OAAA;MAAG+F,SAAS,EAAC,4BAA4B;MAAAC,QAAA,EAAC;IAAoB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC/D;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACC,IAAA,GAXIP,UAAU;AAahB,SAASQ,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM;IAAEC;EAAQ,CAAC,GAAGjH,WAAW,CAAEkH,KAAK,IAAKA,KAAK,CAACC,MAAM,CAAC;EACxD,oBACE1G,OAAA,CAACN,aAAa;IAAAsG,QAAA,eACZhG,OAAA,CAACR,aAAa;MAAAwG,QAAA,eACZhG,OAAA,CAACP,gBAAgB;QAAAuG,QAAA,GACdQ,OAAO,iBAAIxG,OAAA,CAACV,MAAM;UAAA2G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxBpG,OAAA,CAACd,aAAa;UAAA8G,QAAA,eACdhG,OAAA,CAACb,MAAM;YAAA6G,QAAA,gBAELhG,OAAA,CAACZ,KAAK;cAACuH,IAAI,EAAC,QAAQ;cAACC,OAAO,eAAE5G,OAAA,CAACJ,KAAK;gBAAAqG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3CpG,OAAA,CAACZ,KAAK;cAACuH,IAAI,EAAC,WAAW;cAACC,OAAO,eAAE5G,OAAA,CAACH,QAAQ;gBAAAoG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjDpG,OAAA,CAACZ,KAAK;cAACuH,IAAI,EAAC,GAAG;cAACC,OAAO,eAAE5G,OAAA,CAACF,IAAI;gBAAAmG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrCpG,OAAA,CAACZ,KAAK;cAACuH,IAAI,EAAC,eAAe;cAACC,OAAO,eAAE5G,OAAA,CAACkF,WAAW;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAGxDpG,OAAA,CAACZ,KAAK;cAACuH,IAAI,EAAC,QAAQ;cAACC,OAAO,eAC1B5G,OAAA,CAAChB,QAAQ;gBAAC6H,QAAQ,eAAE7G,OAAA,CAAC8F,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjChG,OAAA,CAACsB,SAAS;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YACX;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEJpG,OAAA,CAACZ,KAAK;cAACuH,IAAI,EAAC,OAAO;cAACC,OAAO,eACzB5G,OAAA,CAAChB,QAAQ;gBAAC6H,QAAQ,eAAE7G,OAAA,CAAC8F,UAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAJ,QAAA,eACjChG,OAAA,CAAC0D,IAAI;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YACX;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACJpG,OAAA,CAACZ,KAAK;cACJuH,IAAI,EAAC,QAAQ;cACbC,OAAO,eACL5G,OAAA,CAACX,cAAc;gBAAA2G,QAAA,eACbhG,OAAA,CAAChB,QAAQ;kBAAC6H,QAAQ,eAAE7G,OAAA,CAAC8F,UAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,eACjChG,OAAA,CAACuD,KAAK;oBAAA0C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGFpG,OAAA,CAACZ,KAAK;cACJuH,IAAI,EAAC,UAAU;cACfC,OAAO,eACL5G,OAAA,CAACX,cAAc;gBAAA2G,QAAA,eACbhG,OAAA,CAAChB,QAAQ;kBAAC6H,QAAQ,eAAE7G,OAAA,CAAC8F,UAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,eACjChG,OAAA,CAACiD,OAAO;oBAAAgD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACFpG,OAAA,CAACZ,KAAK;cACJuH,IAAI,EAAC,eAAe;cACpBC,OAAO,eACL5G,OAAA,CAACX,cAAc;gBAAA2G,QAAA,eACbhG,OAAA,CAAChB,QAAQ;kBAAC6H,QAAQ,eAAE7G,OAAA,CAAC8F,UAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,eACjChG,OAAA,CAACiD,OAAO;oBAAAgD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEFpG,OAAA,CAACZ,KAAK;cACJuH,IAAI,EAAC,eAAe;cACpBC,OAAO,eACL5G,OAAA,CAACX,cAAc;gBAAA2G,QAAA,eACbhG,OAAA,CAAChB,QAAQ;kBAAC6H,QAAQ,eAAE7G,OAAA,CAAC8F,UAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,eACjChG,OAAA,CAAC6D,YAAY;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACFpG,OAAA,CAACZ,KAAK;cACJuH,IAAI,EAAC,oBAAoB;cACzBC,OAAO,eACL5G,OAAA,CAACX,cAAc;gBAAA2G,QAAA,eACbhG,OAAA,CAAChB,QAAQ;kBAAC6H,QAAQ,eAAE7G,OAAA,CAAC8F,UAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,eACjChG,OAAA,CAAC6D,YAAY;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEFpG,OAAA,CAACZ,KAAK;cACJuH,IAAI,EAAC,WAAW;cAChBC,OAAO,eACL5G,OAAA,CAACX,cAAc;gBAAA2G,QAAA,eACbhG,OAAA,CAAChB,QAAQ;kBAAC6H,QAAQ,eAAE7G,OAAA,CAAC8F,UAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,eACjChG,OAAA,CAACgE,GAAG;oBAAAiC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACFpG,OAAA,CAACZ,KAAK;cACJuH,IAAI,EAAC,YAAY;cACjBC,OAAO,eACL5G,OAAA,CAACX,cAAc;gBAAA2G,QAAA,eACbhG,OAAA,CAAChB,QAAQ;kBAAC6H,QAAQ,eAAE7G,OAAA,CAAC8F,UAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,eACjChG,OAAA,CAACC,IAAI;oBAAAgG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEFpG,OAAA,CAACZ,KAAK;cACJuH,IAAI,EAAC,sBAAsB;cAC3BC,OAAO,eACL5G,OAAA,CAACX,cAAc;gBAAA2G,QAAA,eACbhG,OAAA,CAAChB,QAAQ;kBAAC6H,QAAQ,eAAE7G,OAAA,CAAC8F,UAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,eACjChG,OAAA,CAACyB,SAAS;oBAAAwE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACFpG,OAAA,CAACZ,KAAK;cACJuH,IAAI,EAAC,kBAAkB;cACvBC,OAAO,eACL5G,OAAA,CAACX,cAAc;gBAAA2G,QAAA,eACbhG,OAAA,CAAChB,QAAQ;kBAAC6H,QAAQ,eAAE7G,OAAA,CAAC8F,UAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,eACjChG,OAAA,CAACO,UAAU;oBAAA0F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAIFpG,OAAA,CAACZ,KAAK;cACJuH,IAAI,EAAC,gBAAgB;cACrBC,OAAO,eACL5G,OAAA,CAACX,cAAc;gBAAA2G,QAAA,eACbhG,OAAA,CAACI,QAAQ;kBAAA6F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEFpG,OAAA,CAACZ,KAAK;cACJuH,IAAI,EAAC,eAAe;cACpBC,OAAO,eACL5G,OAAA,CAACX,cAAc;gBAAA2G,QAAA,eACbhG,OAAA,CAAChB,QAAQ;kBAAC6H,QAAQ,eAAE7G,OAAA,CAAC8F,UAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,eACjChG,OAAA,CAAC4B,WAAW;oBAAAqE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACFpG,OAAA,CAACZ,KAAK;cACJuH,IAAI,EAAC,sBAAsB;cAC3BC,OAAO,eACL5G,OAAA,CAACX,cAAc;gBAAA2G,QAAA,eACbhG,OAAA,CAAChB,QAAQ;kBAAC6H,QAAQ,eAAE7G,OAAA,CAAC8F,UAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,eACjChG,OAAA,CAACkC,aAAa;oBAAA+D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACFpG,OAAA,CAACZ,KAAK;cACJuH,IAAI,EAAC,qBAAqB;cAC1BC,OAAO,eACL5G,OAAA,CAACX,cAAc;gBAAA2G,QAAA,eACbhG,OAAA,CAAChB,QAAQ;kBAAC6H,QAAQ,eAAE7G,OAAA,CAAC8F,UAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,eACjChG,OAAA,CAACqC,YAAY;oBAAA4D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACFpG,OAAA,CAACZ,KAAK;cACJuH,IAAI,EAAC,cAAc;cACnBC,OAAO,eACL5G,OAAA,CAACX,cAAc;gBAAA2G,QAAA,eACbhG,OAAA,CAAChB,QAAQ;kBAAC6H,QAAQ,eAAE7G,OAAA,CAAC8F,UAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,eACjChG,OAAA,CAACwC,MAAM;oBAAAyD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACFpG,OAAA,CAACZ,KAAK;cACJuH,IAAI,EAAC,eAAe;cACpBC,OAAO,eACL5G,OAAA,CAACX,cAAc;gBAAA2G,QAAA,eACbhG,OAAA,CAAChB,QAAQ;kBAAC6H,QAAQ,eAAE7G,OAAA,CAAC8F,UAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,eACjChG,OAAA,CAAC8C,oBAAoB;oBAAAkD,QAAA,eACnBhG,OAAA,CAAC2C,OAAO;sBAAAsD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACFpG,OAAA,CAACZ,KAAK;cACJuH,IAAI,EAAC,gBAAgB;cACrBC,OAAO,eACL5G,OAAA,CAACX,cAAc;gBAAA2G,QAAA,eACbhG,OAAA,CAACoD,OAAO;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGFpG,OAAA,CAACZ,KAAK;cACJuH,IAAI,EAAC,kBAAkB;cACvBC,OAAO,eACL5G,OAAA,CAACX,cAAc;gBAAA2G,QAAA,eACbhG,OAAA,CAACL,mBAAmB;kBAAAqG,QAAA,eAClBhG,OAAA,CAACmB,cAAc;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACFpG,OAAA,CAACZ,KAAK;cACJuH,IAAI,EAAC,cAAc;cACnBC,OAAO,eACL5G,OAAA,CAACX,cAAc;gBAAA2G,QAAA,eACbhG,OAAA,CAACL,mBAAmB;kBAAAqG,QAAA,eAClBhG,OAAA,CAACgB,KAAK;oBAAAiF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACU;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACFpG,OAAA,CAACZ,KAAK;cACJuH,IAAI,EAAC,cAAc;cACnBC,OAAO,eACL5G,OAAA,CAACX,cAAc;gBAAA2G,QAAA,eACbhG,OAAA,CAACL,mBAAmB;kBAAAqG,QAAA,eAClBhG,OAAA,CAACU,KAAK;oBAAAuF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACU;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACFpG,OAAA,CAACZ,KAAK;cACJuH,IAAI,EAAC,kBAAkB;cACvBC,OAAO,eACL5G,OAAA,CAACX,cAAc;gBAAA2G,QAAA,eACbhG,OAAA,CAACL,mBAAmB;kBAAAqG,QAAA,eAClBhG,OAAA,CAACa,WAAW;oBAAAoF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEFpG,OAAA,CAACZ,KAAK;cACJuH,IAAI,EAAC,uBAAuB;cAC5BC,OAAO,eACL5G,OAAA,CAACX,cAAc;gBAAA2G,QAAA,eACbhG,OAAA,CAACL,mBAAmB;kBAAAqG,QAAA,eAClBhG,OAAA,CAACa,WAAW;oBAAAoF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEFpG,OAAA,CAACZ,KAAK;cACJuH,IAAI,EAAC,wBAAwB;cAC7BC,OAAO,eACL5G,OAAA,CAACX,cAAc;gBAAA2G,QAAA,eACbhG,OAAA,CAACL,mBAAmB;kBAAAqG,QAAA,eAClBhG,OAAA,CAACmE,mBAAmB;oBAAA8B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACFpG,OAAA,CAACZ,KAAK;cACJuH,IAAI,EAAC,eAAe;cACpBC,OAAO,eACL5G,OAAA,CAACX,cAAc;gBAAA2G,QAAA,eACbhG,OAAA,CAACL,mBAAmB;kBAAAqG,QAAA,eAClBhG,OAAA,CAACsE,WAAW;oBAAA2B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEFpG,OAAA,CAACZ,KAAK;cACJuH,IAAI,EAAC,gBAAgB;cACrBC,OAAO,eACL5G,OAAA,CAACX,cAAc;gBAAA2G,QAAA,eACbhG,OAAA,CAACL,mBAAmB;kBAAAqG,QAAA,eAClBhG,OAAA,CAAC+B,YAAY;oBAAAkE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEFpG,OAAA,CAACZ,KAAK;cACJuH,IAAI,EAAC,sBAAsB;cAC3BC,OAAO,eACL5G,OAAA,CAACX,cAAc;gBAAA2G,QAAA,eACbhG,OAAA,CAACL,mBAAmB;kBAAAqG,QAAA,eAClBhG,OAAA,CAACyE,kBAAkB;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEFpG,OAAA,CAACZ,KAAK;cACJuH,IAAI,EAAC,cAAc;cACnBC,OAAO,eACL5G,OAAA,CAACX,cAAc;gBAAA2G,QAAA,eACbhG,OAAA,CAACL,mBAAmB;kBAAAqG,QAAA,eAClBhG,OAAA,CAAC4E,UAAU;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAEFpG,OAAA,CAACZ,KAAK;cACJuH,IAAI,EAAC,cAAc;cACnBC,OAAO,eACL5G,OAAA,CAACX,cAAc;gBAAA2G,QAAA,eACbhG,OAAA,CAACL,mBAAmB;kBAAAqG,QAAA,eAClBhG,OAAA,CAAC+E,SAAS;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEpB;AAACG,EAAA,CA9TQD,GAAG;EAAA,QACU/G,WAAW;AAAA;AAAAuH,IAAA,GADxBR,GAAG;AAgUZ,eAAeA,GAAG;AAAC,IAAApG,EAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAiB,IAAA,EAAAS,IAAA;AAAAC,YAAA,CAAA7G,EAAA;AAAA6G,YAAA,CAAA5G,GAAA;AAAA4G,YAAA,CAAA1G,GAAA;AAAA0G,YAAA,CAAAzG,GAAA;AAAAyG,YAAA,CAAAvG,GAAA;AAAAuG,YAAA,CAAAtG,GAAA;AAAAsG,YAAA,CAAApG,GAAA;AAAAoG,YAAA,CAAAnG,GAAA;AAAAmG,YAAA,CAAAjG,GAAA;AAAAiG,YAAA,CAAAhG,IAAA;AAAAgG,YAAA,CAAA9F,IAAA;AAAA8F,YAAA,CAAA7F,IAAA;AAAA6F,YAAA,CAAA3F,IAAA;AAAA2F,YAAA,CAAA1F,IAAA;AAAA0F,YAAA,CAAAxF,IAAA;AAAAwF,YAAA,CAAAvF,IAAA;AAAAuF,YAAA,CAAArF,IAAA;AAAAqF,YAAA,CAAApF,IAAA;AAAAoF,YAAA,CAAAlF,IAAA;AAAAkF,YAAA,CAAAjF,IAAA;AAAAiF,YAAA,CAAA/E,IAAA;AAAA+E,YAAA,CAAA9E,IAAA;AAAA8E,YAAA,CAAA5E,IAAA;AAAA4E,YAAA,CAAA3E,IAAA;AAAA2E,YAAA,CAAAzE,IAAA;AAAAyE,YAAA,CAAAxE,IAAA;AAAAwE,YAAA,CAAAtE,IAAA;AAAAsE,YAAA,CAAArE,IAAA;AAAAqE,YAAA,CAAAnE,IAAA;AAAAmE,YAAA,CAAAlE,IAAA;AAAAkE,YAAA,CAAAhE,IAAA;AAAAgE,YAAA,CAAA/D,IAAA;AAAA+D,YAAA,CAAA7D,IAAA;AAAA6D,YAAA,CAAA5D,IAAA;AAAA4D,YAAA,CAAA1D,IAAA;AAAA0D,YAAA,CAAAzD,IAAA;AAAAyD,YAAA,CAAAvD,IAAA;AAAAuD,YAAA,CAAAtD,IAAA;AAAAsD,YAAA,CAAApD,IAAA;AAAAoD,YAAA,CAAAnD,IAAA;AAAAmD,YAAA,CAAAjD,IAAA;AAAAiD,YAAA,CAAAhD,IAAA;AAAAgD,YAAA,CAAA9C,IAAA;AAAA8C,YAAA,CAAA7C,IAAA;AAAA6C,YAAA,CAAA3C,IAAA;AAAA2C,YAAA,CAAA1C,IAAA;AAAA0C,YAAA,CAAAxC,IAAA;AAAAwC,YAAA,CAAAvC,IAAA;AAAAuC,YAAA,CAAArC,IAAA;AAAAqC,YAAA,CAAApC,IAAA;AAAAoC,YAAA,CAAAlC,IAAA;AAAAkC,YAAA,CAAAjC,IAAA;AAAAiC,YAAA,CAAA/B,IAAA;AAAA+B,YAAA,CAAA9B,IAAA;AAAA8B,YAAA,CAAA5B,IAAA;AAAA4B,YAAA,CAAA3B,IAAA;AAAA2B,YAAA,CAAAV,IAAA;AAAAU,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}