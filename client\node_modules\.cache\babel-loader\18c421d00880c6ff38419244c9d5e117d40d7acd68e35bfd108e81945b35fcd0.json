{"ast": null, "code": "import * as React from 'react';\nimport { renderColumnTitle } from '../util';\nfunction fillTitle(columns, columnTitleProps) {\n  return columns.map(column => {\n    const cloneColumn = Object.assign({}, column);\n    cloneColumn.title = renderColumnTitle(column.title, columnTitleProps);\n    if ('children' in cloneColumn) {\n      cloneColumn.children = fillTitle(cloneColumn.children, columnTitleProps);\n    }\n    return cloneColumn;\n  });\n}\nexport default function useTitleColumns(columnTitleProps) {\n  const filledColumns = React.useCallback(columns => fillTitle(columns, columnTitleProps), [columnTitleProps]);\n  return [filledColumns];\n}", "map": {"version": 3, "names": ["React", "renderColumnTitle", "fill<PERSON>itle", "columns", "columnTitleProps", "map", "column", "cloneColumn", "Object", "assign", "title", "children", "useTitleColumns", "filledColumns", "useCallback"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/table/hooks/useTitleColumns.js"], "sourcesContent": ["import * as React from 'react';\nimport { renderColumnTitle } from '../util';\nfunction fillTitle(columns, columnTitleProps) {\n  return columns.map(column => {\n    const cloneColumn = Object.assign({}, column);\n    cloneColumn.title = renderColumnTitle(column.title, columnTitleProps);\n    if ('children' in cloneColumn) {\n      cloneColumn.children = fillTitle(cloneColumn.children, columnTitleProps);\n    }\n    return cloneColumn;\n  });\n}\nexport default function useTitleColumns(columnTitleProps) {\n  const filledColumns = React.useCallback(columns => fillTitle(columns, columnTitleProps), [columnTitleProps]);\n  return [filledColumns];\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,iBAAiB,QAAQ,SAAS;AAC3C,SAASC,SAASA,CAACC,OAAO,EAAEC,gBAAgB,EAAE;EAC5C,OAAOD,OAAO,CAACE,GAAG,CAACC,MAAM,IAAI;IAC3B,MAAMC,WAAW,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,MAAM,CAAC;IAC7CC,WAAW,CAACG,KAAK,GAAGT,iBAAiB,CAACK,MAAM,CAACI,KAAK,EAAEN,gBAAgB,CAAC;IACrE,IAAI,UAAU,IAAIG,WAAW,EAAE;MAC7BA,WAAW,CAACI,QAAQ,GAAGT,SAAS,CAACK,WAAW,CAACI,QAAQ,EAAEP,gBAAgB,CAAC;IAC1E;IACA,OAAOG,WAAW;EACpB,CAAC,CAAC;AACJ;AACA,eAAe,SAASK,eAAeA,CAACR,gBAAgB,EAAE;EACxD,MAAMS,aAAa,GAAGb,KAAK,CAACc,WAAW,CAACX,OAAO,IAAID,SAAS,CAACC,OAAO,EAAEC,gBAAgB,CAAC,EAAE,CAACA,gBAAgB,CAAC,CAAC;EAC5G,OAAO,CAACS,aAAa,CAAC;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}