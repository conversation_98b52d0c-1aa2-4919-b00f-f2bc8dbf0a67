{"ast": null, "code": "import { TinyColor } from '@ctrl/tinycolor';\nfunction isStableColor(color) {\n  return color >= 0 && color <= 255;\n}\nfunction getAlphaColor(frontColor, backgroundColor) {\n  const {\n    r: fR,\n    g: fG,\n    b: fB,\n    a: originAlpha\n  } = new TinyColor(frontColor).toRgb();\n  if (originAlpha < 1) {\n    return frontColor;\n  }\n  const {\n    r: bR,\n    g: bG,\n    b: bB\n  } = new TinyColor(backgroundColor).toRgb();\n  for (let fA = 0.01; fA <= 1; fA += 0.01) {\n    const r = Math.round((fR - bR * (1 - fA)) / fA);\n    const g = Math.round((fG - bG * (1 - fA)) / fA);\n    const b = Math.round((fB - bB * (1 - fA)) / fA);\n    if (isStableColor(r) && isStableColor(g) && isStableColor(b)) {\n      return new TinyColor({\n        r,\n        g,\n        b,\n        a: Math.round(fA * 100) / 100\n      }).toRgbString();\n    }\n  }\n  // fallback\n  /* istanbul ignore next */\n  return new TinyColor({\n    r: fR,\n    g: fG,\n    b: fB,\n    a: 1\n  }).toRgbString();\n}\nexport default getAlphaColor;", "map": {"version": 3, "names": ["TinyColor", "isStableColor", "color", "getAlphaColor", "frontColor", "backgroundColor", "r", "fR", "g", "fG", "b", "fB", "a", "originAlpha", "toRgb", "bR", "bG", "bB", "fA", "Math", "round", "toRgbString"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/theme/util/getAlphaColor.js"], "sourcesContent": ["import { TinyColor } from '@ctrl/tinycolor';\nfunction isStableColor(color) {\n  return color >= 0 && color <= 255;\n}\nfunction getAlphaColor(frontColor, backgroundColor) {\n  const {\n    r: fR,\n    g: fG,\n    b: fB,\n    a: originAlpha\n  } = new TinyColor(frontColor).toRgb();\n  if (originAlpha < 1) {\n    return frontColor;\n  }\n  const {\n    r: bR,\n    g: bG,\n    b: bB\n  } = new TinyColor(backgroundColor).toRgb();\n  for (let fA = 0.01; fA <= 1; fA += 0.01) {\n    const r = Math.round((fR - bR * (1 - fA)) / fA);\n    const g = Math.round((fG - bG * (1 - fA)) / fA);\n    const b = Math.round((fB - bB * (1 - fA)) / fA);\n    if (isStableColor(r) && isStableColor(g) && isStableColor(b)) {\n      return new TinyColor({\n        r,\n        g,\n        b,\n        a: Math.round(fA * 100) / 100\n      }).toRgbString();\n    }\n  }\n  // fallback\n  /* istanbul ignore next */\n  return new TinyColor({\n    r: fR,\n    g: fG,\n    b: fB,\n    a: 1\n  }).toRgbString();\n}\nexport default getAlphaColor;"], "mappings": "AAAA,SAASA,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC5B,OAAOA,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAI,GAAG;AACnC;AACA,SAASC,aAAaA,CAACC,UAAU,EAAEC,eAAe,EAAE;EAClD,MAAM;IACJC,CAAC,EAAEC,EAAE;IACLC,CAAC,EAAEC,EAAE;IACLC,CAAC,EAAEC,EAAE;IACLC,CAAC,EAAEC;EACL,CAAC,GAAG,IAAIb,SAAS,CAACI,UAAU,CAAC,CAACU,KAAK,CAAC,CAAC;EACrC,IAAID,WAAW,GAAG,CAAC,EAAE;IACnB,OAAOT,UAAU;EACnB;EACA,MAAM;IACJE,CAAC,EAAES,EAAE;IACLP,CAAC,EAAEQ,EAAE;IACLN,CAAC,EAAEO;EACL,CAAC,GAAG,IAAIjB,SAAS,CAACK,eAAe,CAAC,CAACS,KAAK,CAAC,CAAC;EAC1C,KAAK,IAAII,EAAE,GAAG,IAAI,EAAEA,EAAE,IAAI,CAAC,EAAEA,EAAE,IAAI,IAAI,EAAE;IACvC,MAAMZ,CAAC,GAAGa,IAAI,CAACC,KAAK,CAAC,CAACb,EAAE,GAAGQ,EAAE,IAAI,CAAC,GAAGG,EAAE,CAAC,IAAIA,EAAE,CAAC;IAC/C,MAAMV,CAAC,GAAGW,IAAI,CAACC,KAAK,CAAC,CAACX,EAAE,GAAGO,EAAE,IAAI,CAAC,GAAGE,EAAE,CAAC,IAAIA,EAAE,CAAC;IAC/C,MAAMR,CAAC,GAAGS,IAAI,CAACC,KAAK,CAAC,CAACT,EAAE,GAAGM,EAAE,IAAI,CAAC,GAAGC,EAAE,CAAC,IAAIA,EAAE,CAAC;IAC/C,IAAIjB,aAAa,CAACK,CAAC,CAAC,IAAIL,aAAa,CAACO,CAAC,CAAC,IAAIP,aAAa,CAACS,CAAC,CAAC,EAAE;MAC5D,OAAO,IAAIV,SAAS,CAAC;QACnBM,CAAC;QACDE,CAAC;QACDE,CAAC;QACDE,CAAC,EAAEO,IAAI,CAACC,KAAK,CAACF,EAAE,GAAG,GAAG,CAAC,GAAG;MAC5B,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC;IAClB;EACF;EACA;EACA;EACA,OAAO,IAAIrB,SAAS,CAAC;IACnBM,CAAC,EAAEC,EAAE;IACLC,CAAC,EAAEC,EAAE;IACLC,CAAC,EAAEC,EAAE;IACLC,CAAC,EAAE;EACL,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC;AAClB;AACA,eAAelB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}