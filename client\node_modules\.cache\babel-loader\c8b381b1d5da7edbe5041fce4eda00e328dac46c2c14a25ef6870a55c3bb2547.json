{"ast": null, "code": "import warning from \"rc-util/es/warning\";\nfunction warningProps(props) {\n  var onPopupVisibleChange = props.onPopupVisibleChange,\n    popupVisible = props.popupVisible,\n    popupClassName = props.popupClassName,\n    popupPlacement = props.popupPlacement;\n  warning(!onPopupVisibleChange, '`onPopupVisibleChange` is deprecated. Please use `onDropdownVisibleChange` instead.');\n  warning(popupVisible === undefined, '`popupVisible` is deprecated. Please use `open` instead.');\n  warning(popupClassName === undefined, '`popupClassName` is deprecated. Please use `dropdownClassName` instead.');\n  warning(popupPlacement === undefined, '`popupPlacement` is deprecated. Please use `placement` instead.');\n}\n\n// value in Cascader options should not be null\nexport function warningNullOptions(options, fieldNames) {\n  if (options) {\n    var recursiveOptions = function recursiveOptions(optionsList) {\n      for (var i = 0; i < optionsList.length; i++) {\n        var option = optionsList[i];\n        if (option[fieldNames === null || fieldNames === void 0 ? void 0 : fieldNames.value] === null) {\n          warning(false, '`value` in Cascader options should not be `null`.');\n          return true;\n        }\n        if (Array.isArray(option[fieldNames === null || fieldNames === void 0 ? void 0 : fieldNames.children]) && recursiveOptions(option[fieldNames === null || fieldNames === void 0 ? void 0 : fieldNames.children])) {\n          return true;\n        }\n      }\n    };\n    recursiveOptions(options);\n  }\n}\nexport default warningProps;", "map": {"version": 3, "names": ["warning", "warningProps", "props", "onPopupVisibleChange", "popupVisible", "popupClassName", "popupPlacement", "undefined", "warningNullOptions", "options", "fieldNames", "recursiveOptions", "optionsList", "i", "length", "option", "value", "Array", "isArray", "children"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-cascader/es/utils/warningPropsUtil.js"], "sourcesContent": ["import warning from \"rc-util/es/warning\";\nfunction warningProps(props) {\n  var onPopupVisibleChange = props.onPopupVisibleChange,\n    popupVisible = props.popupVisible,\n    popupClassName = props.popupClassName,\n    popupPlacement = props.popupPlacement;\n  warning(!onPopupVisibleChange, '`onPopupVisibleChange` is deprecated. Please use `onDropdownVisibleChange` instead.');\n  warning(popupVisible === undefined, '`popupVisible` is deprecated. Please use `open` instead.');\n  warning(popupClassName === undefined, '`popupClassName` is deprecated. Please use `dropdownClassName` instead.');\n  warning(popupPlacement === undefined, '`popupPlacement` is deprecated. Please use `placement` instead.');\n}\n\n// value in Cascader options should not be null\nexport function warningNullOptions(options, fieldNames) {\n  if (options) {\n    var recursiveOptions = function recursiveOptions(optionsList) {\n      for (var i = 0; i < optionsList.length; i++) {\n        var option = optionsList[i];\n        if (option[fieldNames === null || fieldNames === void 0 ? void 0 : fieldNames.value] === null) {\n          warning(false, '`value` in Cascader options should not be `null`.');\n          return true;\n        }\n        if (Array.isArray(option[fieldNames === null || fieldNames === void 0 ? void 0 : fieldNames.children]) && recursiveOptions(option[fieldNames === null || fieldNames === void 0 ? void 0 : fieldNames.children])) {\n          return true;\n        }\n      }\n    };\n    recursiveOptions(options);\n  }\n}\nexport default warningProps;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,oBAAoB;AACxC,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B,IAAIC,oBAAoB,GAAGD,KAAK,CAACC,oBAAoB;IACnDC,YAAY,GAAGF,KAAK,CAACE,YAAY;IACjCC,cAAc,GAAGH,KAAK,CAACG,cAAc;IACrCC,cAAc,GAAGJ,KAAK,CAACI,cAAc;EACvCN,OAAO,CAAC,CAACG,oBAAoB,EAAE,qFAAqF,CAAC;EACrHH,OAAO,CAACI,YAAY,KAAKG,SAAS,EAAE,0DAA0D,CAAC;EAC/FP,OAAO,CAACK,cAAc,KAAKE,SAAS,EAAE,yEAAyE,CAAC;EAChHP,OAAO,CAACM,cAAc,KAAKC,SAAS,EAAE,iEAAiE,CAAC;AAC1G;;AAEA;AACA,OAAO,SAASC,kBAAkBA,CAACC,OAAO,EAAEC,UAAU,EAAE;EACtD,IAAID,OAAO,EAAE;IACX,IAAIE,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,WAAW,EAAE;MAC5D,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,WAAW,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;QAC3C,IAAIE,MAAM,GAAGH,WAAW,CAACC,CAAC,CAAC;QAC3B,IAAIE,MAAM,CAACL,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACM,KAAK,CAAC,KAAK,IAAI,EAAE;UAC7FhB,OAAO,CAAC,KAAK,EAAE,mDAAmD,CAAC;UACnE,OAAO,IAAI;QACb;QACA,IAAIiB,KAAK,CAACC,OAAO,CAACH,MAAM,CAACL,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACS,QAAQ,CAAC,CAAC,IAAIR,gBAAgB,CAACI,MAAM,CAACL,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACS,QAAQ,CAAC,CAAC,EAAE;UAC/M,OAAO,IAAI;QACb;MACF;IACF,CAAC;IACDR,gBAAgB,CAACF,OAAO,CAAC;EAC3B;AACF;AACA,eAAeR,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}