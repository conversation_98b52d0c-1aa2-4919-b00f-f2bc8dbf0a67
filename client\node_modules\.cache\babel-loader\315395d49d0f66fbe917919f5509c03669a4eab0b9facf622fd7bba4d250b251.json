{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _toArray from \"@babel/runtime/helpers/esm/toArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport warning from \"rc-util/es/warning\";\nfunction getKey(data, index) {\n  var key = data.key;\n  var value;\n  if ('value' in data) {\n    value = data.value;\n  }\n  if (key !== null && key !== undefined) {\n    return key;\n  }\n  if (value !== undefined) {\n    return value;\n  }\n  return \"rc-index-key-\".concat(index);\n}\nexport function fillFieldNames(fieldNames, childrenAsData) {\n  var _ref = fieldNames || {},\n    label = _ref.label,\n    value = _ref.value,\n    options = _ref.options,\n    groupLabel = _ref.groupLabel;\n  var mergedLabel = label || (childrenAsData ? 'children' : 'label');\n  return {\n    label: mergedLabel,\n    value: value || 'value',\n    options: options || 'options',\n    groupLabel: groupLabel || mergedLabel\n  };\n}\n\n/**\n * Flat options into flatten list.\n * We use `optionOnly` here is aim to avoid user use nested option group.\n * Here is simply set `key` to the index if not provided.\n */\nexport function flattenOptions(options) {\n  var _ref2 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n    fieldNames = _ref2.fieldNames,\n    childrenAsData = _ref2.childrenAsData;\n  var flattenList = [];\n  var _fillFieldNames = fillFieldNames(fieldNames, false),\n    fieldLabel = _fillFieldNames.label,\n    fieldValue = _fillFieldNames.value,\n    fieldOptions = _fillFieldNames.options,\n    groupLabel = _fillFieldNames.groupLabel;\n  function dig(list, isGroupOption) {\n    list.forEach(function (data) {\n      if (isGroupOption || !(fieldOptions in data)) {\n        var value = data[fieldValue];\n\n        // Option\n        flattenList.push({\n          key: getKey(data, flattenList.length),\n          groupOption: isGroupOption,\n          data: data,\n          label: data[fieldLabel],\n          value: value\n        });\n      } else {\n        var grpLabel = data[groupLabel];\n        if (grpLabel === undefined && childrenAsData) {\n          grpLabel = data.label;\n        }\n\n        // Option Group\n        flattenList.push({\n          key: getKey(data, flattenList.length),\n          group: true,\n          data: data,\n          label: grpLabel\n        });\n        dig(data[fieldOptions], true);\n      }\n    });\n  }\n  dig(options, false);\n  return flattenList;\n}\n\n/**\n * Inject `props` into `option` for legacy usage\n */\nexport function injectPropsWithOption(option) {\n  var newOption = _objectSpread({}, option);\n  if (!('props' in newOption)) {\n    Object.defineProperty(newOption, 'props', {\n      get: function get() {\n        warning(false, 'Return type is option instead of Option instance. Please read value directly instead of reading from `props`.');\n        return newOption;\n      }\n    });\n  }\n  return newOption;\n}\nexport function getSeparatedContent(text, tokens) {\n  if (!tokens || !tokens.length) {\n    return null;\n  }\n  var match = false;\n  function separate(str, _ref3) {\n    var _ref4 = _toArray(_ref3),\n      token = _ref4[0],\n      restTokens = _ref4.slice(1);\n    if (!token) {\n      return [str];\n    }\n    var list = str.split(token);\n    match = match || list.length > 1;\n    return list.reduce(function (prevList, unitStr) {\n      return [].concat(_toConsumableArray(prevList), _toConsumableArray(separate(unitStr, restTokens)));\n    }, []).filter(function (unit) {\n      return unit;\n    });\n  }\n  var list = separate(text, tokens);\n  return match ? list : null;\n}", "map": {"version": 3, "names": ["_toConsumableArray", "_toArray", "_objectSpread", "warning", "<PERSON><PERSON><PERSON>", "data", "index", "key", "value", "undefined", "concat", "fillFieldNames", "fieldNames", "childrenAsData", "_ref", "label", "options", "groupLabel", "mergedLabel", "flattenOptions", "_ref2", "arguments", "length", "flattenList", "_fillField<PERSON><PERSON>s", "<PERSON><PERSON><PERSON><PERSON>", "fieldValue", "fieldOptions", "dig", "list", "isGroupOption", "for<PERSON>ach", "push", "groupOption", "grpLabel", "group", "injectPropsWithOption", "option", "newOption", "Object", "defineProperty", "get", "getSeparatedContent", "text", "tokens", "match", "separate", "str", "_ref3", "_ref4", "token", "restTokens", "slice", "split", "reduce", "prevList", "unitStr", "filter", "unit"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-select/es/utils/valueUtil.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _toArray from \"@babel/runtime/helpers/esm/toArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport warning from \"rc-util/es/warning\";\nfunction getKey(data, index) {\n  var key = data.key;\n  var value;\n  if ('value' in data) {\n    value = data.value;\n  }\n  if (key !== null && key !== undefined) {\n    return key;\n  }\n  if (value !== undefined) {\n    return value;\n  }\n  return \"rc-index-key-\".concat(index);\n}\nexport function fillFieldNames(fieldNames, childrenAsData) {\n  var _ref = fieldNames || {},\n    label = _ref.label,\n    value = _ref.value,\n    options = _ref.options,\n    groupLabel = _ref.groupLabel;\n  var mergedLabel = label || (childrenAsData ? 'children' : 'label');\n  return {\n    label: mergedLabel,\n    value: value || 'value',\n    options: options || 'options',\n    groupLabel: groupLabel || mergedLabel\n  };\n}\n\n/**\n * Flat options into flatten list.\n * We use `optionOnly` here is aim to avoid user use nested option group.\n * Here is simply set `key` to the index if not provided.\n */\nexport function flattenOptions(options) {\n  var _ref2 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n    fieldNames = _ref2.fieldNames,\n    childrenAsData = _ref2.childrenAsData;\n  var flattenList = [];\n  var _fillFieldNames = fillFieldNames(fieldNames, false),\n    fieldLabel = _fillFieldNames.label,\n    fieldValue = _fillFieldNames.value,\n    fieldOptions = _fillFieldNames.options,\n    groupLabel = _fillFieldNames.groupLabel;\n  function dig(list, isGroupOption) {\n    list.forEach(function (data) {\n      if (isGroupOption || !(fieldOptions in data)) {\n        var value = data[fieldValue];\n\n        // Option\n        flattenList.push({\n          key: getKey(data, flattenList.length),\n          groupOption: isGroupOption,\n          data: data,\n          label: data[fieldLabel],\n          value: value\n        });\n      } else {\n        var grpLabel = data[groupLabel];\n        if (grpLabel === undefined && childrenAsData) {\n          grpLabel = data.label;\n        }\n\n        // Option Group\n        flattenList.push({\n          key: getKey(data, flattenList.length),\n          group: true,\n          data: data,\n          label: grpLabel\n        });\n        dig(data[fieldOptions], true);\n      }\n    });\n  }\n  dig(options, false);\n  return flattenList;\n}\n\n/**\n * Inject `props` into `option` for legacy usage\n */\nexport function injectPropsWithOption(option) {\n  var newOption = _objectSpread({}, option);\n  if (!('props' in newOption)) {\n    Object.defineProperty(newOption, 'props', {\n      get: function get() {\n        warning(false, 'Return type is option instead of Option instance. Please read value directly instead of reading from `props`.');\n        return newOption;\n      }\n    });\n  }\n  return newOption;\n}\nexport function getSeparatedContent(text, tokens) {\n  if (!tokens || !tokens.length) {\n    return null;\n  }\n  var match = false;\n  function separate(str, _ref3) {\n    var _ref4 = _toArray(_ref3),\n      token = _ref4[0],\n      restTokens = _ref4.slice(1);\n    if (!token) {\n      return [str];\n    }\n    var list = str.split(token);\n    match = match || list.length > 1;\n    return list.reduce(function (prevList, unitStr) {\n      return [].concat(_toConsumableArray(prevList), _toConsumableArray(separate(unitStr, restTokens)));\n    }, []).filter(function (unit) {\n      return unit;\n    });\n  }\n  var list = separate(text, tokens);\n  return match ? list : null;\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,OAAO,MAAM,oBAAoB;AACxC,SAASC,MAAMA,CAACC,IAAI,EAAEC,KAAK,EAAE;EAC3B,IAAIC,GAAG,GAAGF,IAAI,CAACE,GAAG;EAClB,IAAIC,KAAK;EACT,IAAI,OAAO,IAAIH,IAAI,EAAE;IACnBG,KAAK,GAAGH,IAAI,CAACG,KAAK;EACpB;EACA,IAAID,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,EAAE;IACrC,OAAOF,GAAG;EACZ;EACA,IAAIC,KAAK,KAAKC,SAAS,EAAE;IACvB,OAAOD,KAAK;EACd;EACA,OAAO,eAAe,CAACE,MAAM,CAACJ,KAAK,CAAC;AACtC;AACA,OAAO,SAASK,cAAcA,CAACC,UAAU,EAAEC,cAAc,EAAE;EACzD,IAAIC,IAAI,GAAGF,UAAU,IAAI,CAAC,CAAC;IACzBG,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBP,KAAK,GAAGM,IAAI,CAACN,KAAK;IAClBQ,OAAO,GAAGF,IAAI,CAACE,OAAO;IACtBC,UAAU,GAAGH,IAAI,CAACG,UAAU;EAC9B,IAAIC,WAAW,GAAGH,KAAK,KAAKF,cAAc,GAAG,UAAU,GAAG,OAAO,CAAC;EAClE,OAAO;IACLE,KAAK,EAAEG,WAAW;IAClBV,KAAK,EAAEA,KAAK,IAAI,OAAO;IACvBQ,OAAO,EAAEA,OAAO,IAAI,SAAS;IAC7BC,UAAU,EAAEA,UAAU,IAAIC;EAC5B,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACH,OAAO,EAAE;EACtC,IAAII,KAAK,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKZ,SAAS,GAAGY,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAChFT,UAAU,GAAGQ,KAAK,CAACR,UAAU;IAC7BC,cAAc,GAAGO,KAAK,CAACP,cAAc;EACvC,IAAIU,WAAW,GAAG,EAAE;EACpB,IAAIC,eAAe,GAAGb,cAAc,CAACC,UAAU,EAAE,KAAK,CAAC;IACrDa,UAAU,GAAGD,eAAe,CAACT,KAAK;IAClCW,UAAU,GAAGF,eAAe,CAAChB,KAAK;IAClCmB,YAAY,GAAGH,eAAe,CAACR,OAAO;IACtCC,UAAU,GAAGO,eAAe,CAACP,UAAU;EACzC,SAASW,GAAGA,CAACC,IAAI,EAAEC,aAAa,EAAE;IAChCD,IAAI,CAACE,OAAO,CAAC,UAAU1B,IAAI,EAAE;MAC3B,IAAIyB,aAAa,IAAI,EAAEH,YAAY,IAAItB,IAAI,CAAC,EAAE;QAC5C,IAAIG,KAAK,GAAGH,IAAI,CAACqB,UAAU,CAAC;;QAE5B;QACAH,WAAW,CAACS,IAAI,CAAC;UACfzB,GAAG,EAAEH,MAAM,CAACC,IAAI,EAAEkB,WAAW,CAACD,MAAM,CAAC;UACrCW,WAAW,EAAEH,aAAa;UAC1BzB,IAAI,EAAEA,IAAI;UACVU,KAAK,EAAEV,IAAI,CAACoB,UAAU,CAAC;UACvBjB,KAAK,EAAEA;QACT,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAI0B,QAAQ,GAAG7B,IAAI,CAACY,UAAU,CAAC;QAC/B,IAAIiB,QAAQ,KAAKzB,SAAS,IAAII,cAAc,EAAE;UAC5CqB,QAAQ,GAAG7B,IAAI,CAACU,KAAK;QACvB;;QAEA;QACAQ,WAAW,CAACS,IAAI,CAAC;UACfzB,GAAG,EAAEH,MAAM,CAACC,IAAI,EAAEkB,WAAW,CAACD,MAAM,CAAC;UACrCa,KAAK,EAAE,IAAI;UACX9B,IAAI,EAAEA,IAAI;UACVU,KAAK,EAAEmB;QACT,CAAC,CAAC;QACFN,GAAG,CAACvB,IAAI,CAACsB,YAAY,CAAC,EAAE,IAAI,CAAC;MAC/B;IACF,CAAC,CAAC;EACJ;EACAC,GAAG,CAACZ,OAAO,EAAE,KAAK,CAAC;EACnB,OAAOO,WAAW;AACpB;;AAEA;AACA;AACA;AACA,OAAO,SAASa,qBAAqBA,CAACC,MAAM,EAAE;EAC5C,IAAIC,SAAS,GAAGpC,aAAa,CAAC,CAAC,CAAC,EAAEmC,MAAM,CAAC;EACzC,IAAI,EAAE,OAAO,IAAIC,SAAS,CAAC,EAAE;IAC3BC,MAAM,CAACC,cAAc,CAACF,SAAS,EAAE,OAAO,EAAE;MACxCG,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;QAClBtC,OAAO,CAAC,KAAK,EAAE,+GAA+G,CAAC;QAC/H,OAAOmC,SAAS;MAClB;IACF,CAAC,CAAC;EACJ;EACA,OAAOA,SAAS;AAClB;AACA,OAAO,SAASI,mBAAmBA,CAACC,IAAI,EAAEC,MAAM,EAAE;EAChD,IAAI,CAACA,MAAM,IAAI,CAACA,MAAM,CAACtB,MAAM,EAAE;IAC7B,OAAO,IAAI;EACb;EACA,IAAIuB,KAAK,GAAG,KAAK;EACjB,SAASC,QAAQA,CAACC,GAAG,EAAEC,KAAK,EAAE;IAC5B,IAAIC,KAAK,GAAGhD,QAAQ,CAAC+C,KAAK,CAAC;MACzBE,KAAK,GAAGD,KAAK,CAAC,CAAC,CAAC;MAChBE,UAAU,GAAGF,KAAK,CAACG,KAAK,CAAC,CAAC,CAAC;IAC7B,IAAI,CAACF,KAAK,EAAE;MACV,OAAO,CAACH,GAAG,CAAC;IACd;IACA,IAAIlB,IAAI,GAAGkB,GAAG,CAACM,KAAK,CAACH,KAAK,CAAC;IAC3BL,KAAK,GAAGA,KAAK,IAAIhB,IAAI,CAACP,MAAM,GAAG,CAAC;IAChC,OAAOO,IAAI,CAACyB,MAAM,CAAC,UAAUC,QAAQ,EAAEC,OAAO,EAAE;MAC9C,OAAO,EAAE,CAAC9C,MAAM,CAACV,kBAAkB,CAACuD,QAAQ,CAAC,EAAEvD,kBAAkB,CAAC8C,QAAQ,CAACU,OAAO,EAAEL,UAAU,CAAC,CAAC,CAAC;IACnG,CAAC,EAAE,EAAE,CAAC,CAACM,MAAM,CAAC,UAAUC,IAAI,EAAE;MAC5B,OAAOA,IAAI;IACb,CAAC,CAAC;EACJ;EACA,IAAI7B,IAAI,GAAGiB,QAAQ,CAACH,IAAI,EAAEC,MAAM,CAAC;EACjC,OAAOC,KAAK,GAAGhB,IAAI,GAAG,IAAI;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}