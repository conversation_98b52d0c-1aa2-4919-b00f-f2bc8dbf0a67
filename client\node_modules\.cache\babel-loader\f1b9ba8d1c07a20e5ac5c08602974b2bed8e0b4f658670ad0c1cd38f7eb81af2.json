{"ast": null, "code": "function getScroll(w) {\n  var ret = w.pageXOffset;\n  var method = 'scrollLeft';\n  if (typeof ret !== 'number') {\n    var d = w.document;\n    // ie6,7,8 standard mode\n    ret = d.documentElement[method];\n    if (typeof ret !== 'number') {\n      // quirks mode\n      ret = d.body[method];\n    }\n  }\n  return ret;\n}\nfunction getClientPosition(elem) {\n  var x;\n  var y;\n  var doc = elem.ownerDocument;\n  var body = doc.body;\n  var docElem = doc && doc.documentElement;\n  var box = elem.getBoundingClientRect();\n  x = box.left;\n  y = box.top;\n  x -= docElem.clientLeft || body.clientLeft || 0;\n  y -= docElem.clientTop || body.clientTop || 0;\n  return {\n    left: x,\n    top: y\n  };\n}\nexport function getOffsetLeft(el) {\n  var pos = getClientPosition(el);\n  var doc = el.ownerDocument;\n  // Only IE use `parentWindow`\n  var w = doc.defaultView || doc.parentWindow;\n  pos.left += getScroll(w);\n  return pos.left;\n}", "map": {"version": 3, "names": ["getScroll", "w", "ret", "pageXOffset", "method", "d", "document", "documentElement", "body", "getClientPosition", "elem", "x", "y", "doc", "ownerDocument", "doc<PERSON><PERSON>", "box", "getBoundingClientRect", "left", "top", "clientLeft", "clientTop", "getOffsetLeft", "el", "pos", "defaultView", "parentWindow"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-rate/es/util.js"], "sourcesContent": ["function getScroll(w) {\n  var ret = w.pageXOffset;\n  var method = 'scrollLeft';\n  if (typeof ret !== 'number') {\n    var d = w.document;\n    // ie6,7,8 standard mode\n    ret = d.documentElement[method];\n    if (typeof ret !== 'number') {\n      // quirks mode\n      ret = d.body[method];\n    }\n  }\n  return ret;\n}\nfunction getClientPosition(elem) {\n  var x;\n  var y;\n  var doc = elem.ownerDocument;\n  var body = doc.body;\n  var docElem = doc && doc.documentElement;\n  var box = elem.getBoundingClientRect();\n  x = box.left;\n  y = box.top;\n  x -= docElem.clientLeft || body.clientLeft || 0;\n  y -= docElem.clientTop || body.clientTop || 0;\n  return {\n    left: x,\n    top: y\n  };\n}\nexport function getOffsetLeft(el) {\n  var pos = getClientPosition(el);\n  var doc = el.ownerDocument;\n  // Only IE use `parentWindow`\n  var w = doc.defaultView || doc.parentWindow;\n  pos.left += getScroll(w);\n  return pos.left;\n}"], "mappings": "AAAA,SAASA,SAASA,CAACC,CAAC,EAAE;EACpB,IAAIC,GAAG,GAAGD,CAAC,CAACE,WAAW;EACvB,IAAIC,MAAM,GAAG,YAAY;EACzB,IAAI,OAAOF,GAAG,KAAK,QAAQ,EAAE;IAC3B,IAAIG,CAAC,GAAGJ,CAAC,CAACK,QAAQ;IAClB;IACAJ,GAAG,GAAGG,CAAC,CAACE,eAAe,CAACH,MAAM,CAAC;IAC/B,IAAI,OAAOF,GAAG,KAAK,QAAQ,EAAE;MAC3B;MACAA,GAAG,GAAGG,CAAC,CAACG,IAAI,CAACJ,MAAM,CAAC;IACtB;EACF;EACA,OAAOF,GAAG;AACZ;AACA,SAASO,iBAAiBA,CAACC,IAAI,EAAE;EAC/B,IAAIC,CAAC;EACL,IAAIC,CAAC;EACL,IAAIC,GAAG,GAAGH,IAAI,CAACI,aAAa;EAC5B,IAAIN,IAAI,GAAGK,GAAG,CAACL,IAAI;EACnB,IAAIO,OAAO,GAAGF,GAAG,IAAIA,GAAG,CAACN,eAAe;EACxC,IAAIS,GAAG,GAAGN,IAAI,CAACO,qBAAqB,CAAC,CAAC;EACtCN,CAAC,GAAGK,GAAG,CAACE,IAAI;EACZN,CAAC,GAAGI,GAAG,CAACG,GAAG;EACXR,CAAC,IAAII,OAAO,CAACK,UAAU,IAAIZ,IAAI,CAACY,UAAU,IAAI,CAAC;EAC/CR,CAAC,IAAIG,OAAO,CAACM,SAAS,IAAIb,IAAI,CAACa,SAAS,IAAI,CAAC;EAC7C,OAAO;IACLH,IAAI,EAAEP,CAAC;IACPQ,GAAG,EAAEP;EACP,CAAC;AACH;AACA,OAAO,SAASU,aAAaA,CAACC,EAAE,EAAE;EAChC,IAAIC,GAAG,GAAGf,iBAAiB,CAACc,EAAE,CAAC;EAC/B,IAAIV,GAAG,GAAGU,EAAE,CAACT,aAAa;EAC1B;EACA,IAAIb,CAAC,GAAGY,GAAG,CAACY,WAAW,IAAIZ,GAAG,CAACa,YAAY;EAC3CF,GAAG,CAACN,IAAI,IAAIlB,SAAS,CAACC,CAAC,CAAC;EACxB,OAAOuB,GAAG,CAACN,IAAI;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}