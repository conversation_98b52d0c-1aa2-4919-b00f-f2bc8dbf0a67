import React, { useState, useEffect, useCallback, useMemo } from "react";
import "./index.css";
import { motion, AnimatePresence } from "framer-motion";
import { getStudyMaterial } from "../../../apicalls/study";
import { useDispatch, useSelector } from "react-redux";
import { HideLoading, ShowLoading } from "../../../redux/loaderSlice";
import { primarySubjects, primaryKiswahiliSubjects, secondarySubjects, advanceSubjects } from "../../../data/Subjects.jsx";
import { useLanguage } from "../../../contexts/LanguageContext";
import { getVideoCommentAIResponse } from "../../../apicalls/aiResponse";

// Temporary fix: Use simple text/symbols instead of React Icons to avoid chunk loading issues
const IconComponents = {
  FaPlayCircle: () => <span style={{fontSize: '24px'}}>▶️</span>,
  FaGraduationCap: () => <span style={{fontSize: '24px'}}>🎓</span>,
  FaTimes: () => <span style={{fontSize: '18px'}}>✕</span>,
  FaExpand: () => <span style={{fontSize: '18px'}}>⛶</span>,
  FaCompress: () => <span style={{fontSize: '18px'}}>⛶</span>,
  TbVideo: () => <span style={{fontSize: '24px'}}>📹</span>,
  TbFilter: () => <span style={{fontSize: '18px'}}>🔍</span>,
  TbSortAscending: () => <span style={{fontSize: '18px'}}>↑</span>,
  TbSearch: () => <span style={{fontSize: '18px'}}>🔍</span>,
  TbX: () => <span style={{fontSize: '16px'}}>✕</span>,
  TbDownload: () => <span style={{fontSize: '18px'}}>↻</span>,
  TbAlertTriangle: () => <span style={{fontSize: '24px', color: '#ff6b6b'}}>⚠️</span>,
  TbInfoCircle: () => <span style={{fontSize: '18px'}}>ℹ️</span>
};

// Destructure for easy use
const {
  FaPlayCircle,
  FaGraduationCap,
  FaTimes,
  FaExpand,
  FaCompress,
  TbVideo,
  TbFilter,
  TbSortAscending,
  TbSearch,
  TbX,
  TbDownload,
  TbAlertTriangle,
  TbInfoCircle
} = IconComponents;

function VideoLessons() {
  const { user } = useSelector((state) => state.user);
  const { t, isKiswahili, getClassName, getSubjectName } = useLanguage();
  const dispatch = useDispatch();

  // State management
  const [videos, setVideos] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedLevel, setSelectedLevel] = useState(user?.level || "primary");
  const [selectedClass, setSelectedClass] = useState(user?.class || "all");
  const [selectedSubject, setSelectedSubject] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("newest");

  // Video player state
  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);
  const [showVideoIndices, setShowVideoIndices] = useState([]);
  const [isVideoExpanded, setIsVideoExpanded] = useState(false);
  const [videoError, setVideoError] = useState(null);
  const [videoRef, setVideoRef] = useState(null);

  // Comments state
  const [comments, setComments] = useState([]);
  const [newComment, setNewComment] = useState("");
  const [replyingTo, setReplyingTo] = useState(null);
  const [replyText, setReplyText] = useState("");
  const [showComments, setShowComments] = useState(true);
  const [commentsExpanded, setCommentsExpanded] = useState(false);

  // Available classes based on level
  const availableClasses = useMemo(() => {
    if (selectedLevel === "primary" || selectedLevel === "primary_kiswahili") return ["1", "2", "3", "4", "5", "6", "7"];
    if (selectedLevel === "secondary") return ["1", "2", "3", "4"];
    if (selectedLevel === "advance") return ["5", "6"];
    return [];
  }, [selectedLevel]);

  // Available subjects based on level
  const availableSubjects = useMemo(() => {
    if (selectedLevel === "primary") return primarySubjects;
    if (selectedLevel === "primary_kiswahili") return primaryKiswahiliSubjects;
    if (selectedLevel === "secondary") return secondarySubjects;
    if (selectedLevel === "advance") return advanceSubjects;
    return [];
  }, [selectedLevel]);

  // Fetch videos
  const fetchVideos = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      dispatch(ShowLoading());

      const filters = {
        level: selectedLevel,
        className: "all", // Get all classes for the level
        subject: "all", // Get all subjects for the level
        content: "videos"
      };

      const response = await getStudyMaterial(filters);

      if (response?.data?.success) {
        setVideos(response.data.data || []);
      } else {
        setError(response?.data?.message || "Failed to fetch videos");
        setVideos([]);
      }
    } catch (error) {
      console.error("❌ Error fetching videos:", error);
      setError("Failed to load videos. Please try again.");
      setVideos([]);
    } finally {
      setLoading(false);
      dispatch(HideLoading());
    }
  }, [selectedLevel, dispatch]);

  // Filter and sort videos
  const filteredAndSortedVideos = useMemo(() => {


    let filtered = videos;

    // Apply level filter
    filtered = filtered.filter(video => video.level === selectedLevel);

    // Apply class filter
    if (selectedClass !== "all") {
      filtered = filtered.filter(video => {
        // Check both className and class fields for compatibility
        const videoClass = video.className || video.class;
        return videoClass === selectedClass;
      });
    }

    // Apply subject filter
    if (selectedSubject !== "all") {
      filtered = filtered.filter(video => video.subject === selectedSubject);
    }

    // Apply search filter
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(video =>
        video.title?.toLowerCase().includes(searchLower) ||
        video.subject?.toLowerCase().includes(searchLower) ||
        video.topic?.toLowerCase().includes(searchLower)
      );
    }

    // Apply sorting
    const sorted = [...filtered].sort((a, b) => {
      switch (sortBy) {
        case "newest":
          return new Date(b.createdAt || 0) - new Date(a.createdAt || 0);
        case "oldest":
          return new Date(a.createdAt || 0) - new Date(b.createdAt || 0);
        case "title":
          return (a.title || "").localeCompare(b.title || "");
        case "subject":
          return (a.subject || "").localeCompare(b.subject || "");
        default:
          return 0;
      }
    });

    console.log('✅ Final filtered videos:', sorted.length);
    if (sorted.length > 0) {
      console.log('📹 Sample filtered video:', sorted[0]);
    }

    return sorted;
  }, [videos, searchTerm, sortBy, selectedLevel, selectedClass, selectedSubject]);

  // Video handlers
  const handleShowVideo = async (index) => {
    const video = filteredAndSortedVideos[index];

    setCurrentVideoIndex(index);
    setShowVideoIndices([index]);
    setIsVideoExpanded(false);
    setVideoError(null);

    // Get signed URL for S3 videos if needed
    if (video?.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {
      try {
        const signedUrl = await getSignedVideoUrl(video.videoUrl);
        video.signedVideoUrl = signedUrl;
      } catch (error) {
        console.warn('Failed to get signed URL, using original URL');
        video.signedVideoUrl = video.videoUrl;
      }
    }
  };

  const handleHideVideo = () => {
    setShowVideoIndices([]);
    setCurrentVideoIndex(null);
    setIsVideoExpanded(false);
    setVideoError(null);
    if (videoRef) {
      videoRef.pause();
    }
  };

  const toggleVideoExpansion = () => {
    setIsVideoExpanded(!isVideoExpanded);
  };

  // Get signed URL for S3 videos to ensure access
  const getSignedVideoUrl = async (videoUrl) => {
    if (!videoUrl) return videoUrl;

    // For AWS S3 URLs, get signed URL from backend
    if (videoUrl.includes('amazonaws.com') || videoUrl.includes('s3.')) {
      try {
        const response = await fetch(`http://localhost:5000/api/study/video-signed-url?videoUrl=${encodeURIComponent(videoUrl)}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.success && data.signedUrl) {
          console.log('✅ Got signed URL for S3 video');
          return data.signedUrl;
        } else {
          console.warn('⚠️ Invalid response from signed URL endpoint:', data);
          return videoUrl;
        }
      } catch (error) {
        console.error('❌ Error getting signed URL:', error);
        return videoUrl;
      }
    }

    return videoUrl;
  };

  // Get thumbnail URL
  const getThumbnailUrl = (video) => {
    if (video.thumbnail) {
      return video.thumbnail;
    }
    
    if (video.videoID && !video.videoID.includes('amazonaws.com')) {
      let videoId = video.videoID;
      if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {
        const match = videoId.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);
        videoId = match ? match[1] : videoId;
      }
      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
    }
    
    return '/api/placeholder/400/225';
  };

  // Effects
  useEffect(() => {
    fetchVideos();
  }, [fetchVideos]);

  useEffect(() => {
    if (user?.level) {
      setSelectedLevel(user.level);
    }
    if (user?.class) {
      setSelectedClass(user.class);
    }
  }, [user]);

  // Clear search and refresh
  const handleClearSearch = () => {
    setSearchTerm("");
  };

  const handleRefresh = () => {
    // Only refresh data, don't clear filters or search
    fetchVideos();
  };

  const handleClearAll = () => {
    setSearchTerm("");
    setSelectedSubject("all");
    setSelectedClass("all");
    fetchVideos();
  };

  // Comment functions
  const handleAddComment = async () => {
    if (newComment.trim()) {
      const comment = {
        id: Date.now(),
        text: newComment,
        author: user?.name || "Anonymous",
        avatar: user?.name?.charAt(0)?.toUpperCase() || "A",
        timestamp: new Date().toISOString(),
        replies: [],
        likes: 0,
        liked: false
      };
      setComments([comment, ...comments]); // Add new comments at the top
      setNewComment("");

      // Generate automatic AI response
      await generateVideoCommentAIResponse(comment);
    }
  };

  // Generate automatic AI response for video comments
  const generateVideoCommentAIResponse = async (userComment) => {
    try {
      // Get current video from the filtered list
      const currentVideo = currentVideoIndex !== null ? filteredAndSortedVideos[currentVideoIndex] : null;

      if (!currentVideo) {
        console.log('No current video found for AI response');
        return;
      }

      console.log('Generating AI response for video comment:', {
        videoTitle: currentVideo.title,
        commentText: userComment.text
      });

      const aiResponseData = {
        commentContent: userComment.text,
        videoTitle: currentVideo.title || 'Video Lesson',
        subject: currentVideo.subject || 'General',
        userLevel: user?.level,
        language: isKiswahili ? 'kiswahili' : 'english'
      };

      const aiResponse = await getVideoCommentAIResponse(aiResponseData);
      console.log('AI Response received:', aiResponse);

      if (aiResponse.success) {
        // Add AI response as a reply to the comment
        const aiReply = {
          id: Date.now() + 1,
          text: aiResponse.data.response,
          author: "Brainwave AI",
          avatar: "🤖",
          timestamp: new Date().toISOString(),
          likes: 0,
          liked: false,
          isAI: true
        };

        console.log('Adding AI reply to comment:', aiReply);

        // Add AI reply to the user's comment
        setComments(prevComments =>
          prevComments.map(comment =>
            comment.id === userComment.id
              ? { ...comment, replies: [aiReply, ...comment.replies] }
              : comment
          )
        );
      } else {
        console.error('AI Response failed:', aiResponse.message);
      }
    } catch (error) {
      console.error('Video Comment AI Response Error:', error);
      // Don't show error to user as this is automatic
    }
  };

  const handleAddReply = (commentId) => {
    if (replyText.trim()) {
      const reply = {
        id: Date.now(),
        text: replyText,
        author: user?.name || "Anonymous",
        avatar: user?.name?.charAt(0)?.toUpperCase() || "A",
        timestamp: new Date().toISOString(),
        likes: 0,
        liked: false
      };

      setComments(comments.map(comment =>
        comment.id === commentId
          ? { ...comment, replies: [...comment.replies, reply] }
          : comment
      ));
      setReplyText("");
      setReplyingTo(null);
    }
  };

  const handleLikeComment = (commentId, isReply = false, parentId = null) => {
    if (isReply) {
      setComments(comments.map(comment =>
        comment.id === parentId
          ? {
              ...comment,
              replies: comment.replies.map(reply =>
                reply.id === commentId
                  ? { ...reply, liked: !reply.liked, likes: reply.liked ? reply.likes - 1 : reply.likes + 1 }
                  : reply
              )
            }
          : comment
      ));
    } else {
      setComments(comments.map(comment =>
        comment.id === commentId
          ? { ...comment, liked: !comment.liked, likes: comment.liked ? comment.likes - 1 : comment.likes + 1 }
          : comment
      ));
    }
  };

  const formatTimeAgo = (timestamp) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInSeconds = Math.floor((now - time) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;
    return time.toLocaleDateString();
  };

  return (
    <div className="video-lessons-container">
      {/* Enhanced Header with Level Display */}
      <div className="video-lessons-header">
        <div className="header-content">
          <div className="header-main">
            <div className="header-icon">
              <TbVideo />
            </div>
            <div className="header-text">
              <h1>Video Lessons</h1>
              <p>Watch educational videos to enhance your learning</p>
            </div>
          </div>

          {/* Level and Class Display */}
          <div className="level-display">
            <div className="current-level">
              <span className="level-label">Level:</span>
              <span className="level-value">{selectedLevel.charAt(0).toUpperCase() + selectedLevel.slice(1)}</span>
            </div>
            <div className="current-class">
              <span className="class-label">Your Class:</span>
              <span className="class-value">
                {user?.level === 'primary' ? `Class ${user?.class || 'N/A'}` :
                 user?.level === 'secondary' ? `Form ${user?.class || 'N/A'}` :
                 user?.level === 'advance' ? `Form ${user?.class || 'N/A'}` :
                 'Not Set'}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="video-lessons-content">
        {/* Enhanced Filters and Controls */}
        <div className="video-controls">
          <div className="controls-row">
            {/* Class Filter */}
            <div className="control-group">
              <label className="control-label">
                <TbFilter />
                {isKiswahili ? 'Chuja kwa Darasa' : 'Filter by Class'}
              </label>
              <select
                value={selectedClass}
                onChange={(e) => setSelectedClass(e.target.value)}
                className="control-select class-select"
              >
                <option value="all">{isKiswahili ? 'Madarasa Yote' : 'All Classes'}</option>
                {availableClasses.map((cls) => (
                  <option key={cls} value={cls}>
                    {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ?
                      (isKiswahili ? `Darasa la ${cls}` : `Class ${cls}`) :
                      `Form ${cls}`}
                  </option>
                ))}
              </select>
            </div>

            {/* Subject Filter */}
            <div className="control-group">
              <label className="control-label">
                <TbFilter />
                Subject
              </label>
              <select
                value={selectedSubject}
                onChange={(e) => setSelectedSubject(e.target.value)}
                className="control-select subject-select"
              >
                <option value="all">All Subjects</option>
                {availableSubjects.map((subject) => (
                  <option key={subject} value={subject}>
                    {subject}
                  </option>
                ))}
              </select>
            </div>

            {/* Sort */}
            <div className="control-group">
              <label className="control-label">
                <TbSortAscending />
                Sort
              </label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="control-select sort-select"
              >
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="title">Title A-Z</option>
                <option value="subject">Subject A-Z</option>
              </select>
            </div>
          </div>

          {/* Search Row */}
          <div className="search-row">
            <div className="search-container">
              <TbSearch className="search-icon" />
              <input
                type="text"
                placeholder="Search videos by title, subject, or topic..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="search-input"
              />
              {searchTerm && (
                <button onClick={handleClearSearch} className="clear-search-btn">
                  <TbX />
                  Clear Search
                </button>
              )}
            </div>

            <button onClick={handleRefresh} className="refresh-btn">
              <TbDownload />
              Refresh All
            </button>
          </div>
        </div>

        {/* Loading State */}
        {loading ? (
          <div className="loading-state">
            <div className="loading-spinner"></div>
            <p>{isKiswahili ? 'Inapakia video...' : 'Loading videos...'}</p>
          </div>
        ) : error ? (
          <div className="error-state">
            <TbAlertTriangle className="error-icon" />
            <h3>{isKiswahili ? 'Hitilafu ya Kupakia Video' : 'Error Loading Videos'}</h3>
            <p>{error}</p>
            <button onClick={fetchVideos} className="retry-btn">
              {isKiswahili ? 'Jaribu Tena' : 'Try Again'}
            </button>
          </div>
        ) : filteredAndSortedVideos.length > 0 ? (
          <div className="videos-grid">
            {filteredAndSortedVideos.map((video, index) => (
              <div key={index} className="video-card" onClick={() => handleShowVideo(index)}>
                <div className="video-card-thumbnail">
                  <img
                    src={getThumbnailUrl(video)}
                    alt={video.title}
                    className="thumbnail-image"
                    onError={(e) => {
                      // Fallback logic for failed thumbnails
                      if (video.videoID && !video.videoID.includes('amazonaws.com')) {
                        // For YouTube videos, try different quality thumbnails
                        let videoId = video.videoID;
                        if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {
                          const match = videoId.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);
                          videoId = match ? match[1] : videoId;
                        }

                        const fallbacks = [
                          `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,
                          `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`,
                          `https://img.youtube.com/vi/${videoId}/default.jpg`,
                          '/api/placeholder/320/180'
                        ];

                        const currentSrc = e.target.src;
                        const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));

                        if (currentIndex < fallbacks.length - 1) {
                          e.target.src = fallbacks[currentIndex + 1];
                        }
                      } else {
                        e.target.src = '/api/placeholder/320/180';
                      }
                    }}
                  />
                  <div className="play-overlay">
                    <FaPlayCircle className="play-icon" />
                  </div>
                  <div className="video-duration">
                    {video.duration || "Video"}
                  </div>
                  {video.subtitles && video.subtitles.length > 0 && (
                    <div className="subtitle-badge">
                      <TbInfoCircle />
                      CC
                    </div>
                  )}
                </div>

                <div className="video-card-content">
                  <h3 className="video-title">{video.title}</h3>
                  <div className="video-meta">
                    <span className="video-subject">{getSubjectName(video.subject)}</span>
                    <span className="video-class">
                      {selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ?
                        (isKiswahili ? `Darasa la ${video.className || video.class}` : `Class ${video.className || video.class}`) :
                        `Form ${video.className || video.class}`}
                    </span>
                  </div>
                  <div className="video-tags">
                    {video.topic && <span className="topic-tag">{video.topic}</span>}
                    {video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && (
                      <span className="shared-tag">
                        {isKiswahili ? 'Kushirikiwa kutoka ' : 'Shared from '}{selectedLevel === 'primary' || selectedLevel === 'primary_kiswahili' ?
                          (isKiswahili ? `Darasa la ${video.sharedFromClass}` : `Class ${video.sharedFromClass}`) :
                          `Form ${video.sharedFromClass}`}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="empty-state">
            <FaGraduationCap className="empty-icon" />
            <h3>{isKiswahili ? 'Hakuna Video Zilizopatikana' : 'No Videos Found'}</h3>
            <p>{isKiswahili ? 'Hakuna masomo ya video yanayopatikana kwa uchaguzi wako wa sasa.' : 'No video lessons are available for your current selection.'}</p>
            <p className="suggestion">{isKiswahili ? 'Jaribu kuchagua darasa au somo tofauti.' : 'Try selecting a different class or subject.'}</p>
          </div>
        )}
      </div>

      {/* Enhanced Video Display */}
      {showVideoIndices.length > 0 && currentVideoIndex !== null && (
        <div className={`video-overlay ${isVideoExpanded ? 'expanded' : ''}`} onClick={(e) => {
          if (e.target === e.currentTarget) handleHideVideo();
        }}>
          <div className={`video-modal ${isVideoExpanded ? 'expanded' : ''}`}>
            {(() => {
              const video = filteredAndSortedVideos[currentVideoIndex];
              if (!video) return <div>Video not found</div>;

              return (
                <div className="video-content">
                  <div className="video-header">
                    <div className="video-info">
                      <h3 className="video-title">{video.title}</h3>
                      <div className="video-meta">
                        <span className="video-subject">{video.subject}</span>
                        <span className="video-class">Class {video.className}</span>
                        {video.level && <span className="video-level">{video.level}</span>}
                      </div>
                    </div>
                    <div className="video-controls">
                      <button
                        className="control-btn add-comment-btn"
                        onClick={() => {
                          setCommentsExpanded(!commentsExpanded);
                          if (!commentsExpanded && !isVideoExpanded) {
                            toggleVideoExpansion();
                          }
                        }}
                        title="Add Comment"
                      >
                        <span className="btn-icon">💬</span>
                        <span className="btn-text">Comment</span>
                      </button>
                      {(isVideoExpanded && commentsExpanded) && (
                        <button
                          className="control-btn close-comment-btn"
                          onClick={() => {
                            setCommentsExpanded(false);
                            toggleVideoExpansion();
                          }}
                          title="Close Comments"
                        >
                          <span className="btn-icon">✕</span>
                          <span className="btn-text">Close</span>
                        </button>
                      )}
                      <button
                        className="control-btn close-btn"
                        onClick={handleHideVideo}
                        title="Close video"
                      >
                        <FaTimes />
                      </button>
                    </div>
                  </div>

                  {/* Video and Comments Layout */}
                  <div className={`video-main-layout ${isVideoExpanded ? 'expanded-layout' : 'normal-layout'}`}>
                    {/* Video Container */}
                    <div className="video-container">
                    {video.videoUrl ? (
                      <div style={{ padding: '15px', background: '#000', borderRadius: '8px' }}>
                          <video
                            ref={(ref) => setVideoRef(ref)}
                            controls
                            autoPlay
                            playsInline
                            preload="metadata"
                            width="100%"
                            height="400"
                            poster={getThumbnailUrl(video)}
                            style={{
                              width: '100%',
                              height: '400px',
                              backgroundColor: '#000'
                            }}
                            onError={(e) => {
                              setVideoError(`Failed to load video: ${video.title}. Please try refreshing the page.`);
                            }}
                            onCanPlay={() => {
                              setVideoError(null);
                            }}
                            onLoadStart={() => {
                              console.log('🎬 Video loading started');
                            }}
                            crossOrigin="anonymous"
                          >
                            {/* Use signed URL if available, otherwise use original URL */}
                            <source src={video.signedVideoUrl || video.videoUrl} type="video/mp4" />

                            {/* Add subtitle tracks if available */}
                            {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => (
                              <track
                                key={`${subtitle.language}-${index}`}
                                kind="subtitles"
                                src={subtitle.url}
                                srcLang={subtitle.language}
                                label={subtitle.languageName}
                                default={subtitle.isDefault || index === 0}
                              />
                            ))}

                            Your browser does not support the video tag.
                          </video>

                          {/* Subtitle indicator */}
                          {video.subtitles && video.subtitles.length > 0 && (
                            <div className="subtitle-indicator">
                              <TbInfoCircle className="subtitle-icon" />
                              <span>Subtitles available in {video.subtitles.length} language(s)</span>
                            </div>
                          )}

                          {/* Video error display */}
                          {videoError && (
                            <div className="video-error-overlay">
                              <div className="error-content">
                                <TbAlertTriangle className="error-icon" />
                                <p>{videoError}</p>
                                <button onClick={() => setVideoError(null)} className="dismiss-error-btn">
                                  Dismiss
                                </button>
                              </div>
                            </div>
                          )}
                        </div>
                    ) : video.videoID ? (
                      // Fallback to YouTube embed if no videoUrl
                      <iframe
                        src={`https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`}
                        title={video.title}
                        frameBorder="0"
                        allowFullScreen
                        className="video-iframe"
                        onLoad={() => console.log('✅ YouTube iframe loaded')}
                      ></iframe>
                    ) : (
                      <div className="video-error">
                        <div className="error-icon">⚠️</div>
                        <h3>Video Unavailable</h3>
                        <p>{videoError || "This video cannot be played at the moment."}</p>
                        <div className="error-actions">
                          <a
                            href={video.signedVideoUrl || video.videoUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="external-link-btn"
                          >
                            📱 Open in New Tab
                          </a>
                        </div>
                      </div>
                    )}
                    </div>

                    {/* Comments Section - Always visible */}
                    <div className={`comments-section-below ${isVideoExpanded ? 'expanded-comments' : 'normal-comments'}`}>
                      {/* Comments Count - Always visible at top */}
                      <div className="comments-count-header">
                        <div className="comments-count-display">
                          <TbInfoCircle />
                          <span>{comments.length} {comments.length === 1 ? 'comment' : 'comments'}</span>
                        </div>
                        {!isVideoExpanded || !commentsExpanded ? (
                          <button
                            onClick={() => {
                              setCommentsExpanded(true);
                              if (!isVideoExpanded) {
                                toggleVideoExpansion();
                              }
                            }}
                            className="view-comments-btn"
                          >
                            <span className="btn-icon">👁️</span>
                            <span className="btn-text">View</span>
                          </button>
                        ) : (
                          <button
                            onClick={() => setCommentsExpanded(false)}
                            className="comments-toggle-btn"
                          >
                            ▼ Minimize
                          </button>
                        )}
                      </div>

                      {/* Comments Content - Show when expanded */}
                      {(isVideoExpanded && commentsExpanded) && (
                        <div className="comments-content maximized">
                            {/* Add Comment */}
                            <div className="add-comment">
                              <div className="comment-input-container">
                                <div className="user-avatar">
                                  {user?.name?.charAt(0)?.toUpperCase() || "A"}
                                </div>
                                <div className="comment-input-wrapper">
                                  <textarea
                                    value={newComment}
                                    onChange={(e) => setNewComment(e.target.value)}
                                    placeholder="Share your thoughts about this video..."
                                    className="comment-input"
                                    rows="3"
                                    autoFocus
                                  />
                                  <button
                                    onClick={handleAddComment}
                                    className="comment-submit-btn"
                                    disabled={!newComment.trim()}
                                  >
                                    <span>💬</span> Post Comment
                                  </button>
                                </div>
                              </div>
                            </div>

                      {/* Comments List */}
                      <div className="comments-list">
                        {comments.length === 0 ? (
                          <div className="no-comments">
                            <div className="no-comments-icon">💬</div>
                            <p>No comments yet. Be the first to share your thoughts!</p>
                          </div>
                        ) : (
                          comments.map((comment) => (
                            <div key={comment.id} className="comment">
                              <div className="comment-main">
                                <div className="comment-avatar">
                                  {comment.avatar || comment.author?.charAt(0)?.toUpperCase() || "A"}
                                </div>
                                <div className="comment-content">
                                  <div className="comment-header">
                                    <span className="comment-author">{comment.author}</span>
                                    <span className="comment-time">
                                      {formatTimeAgo(comment.timestamp)}
                                    </span>
                                  </div>
                                  <div className="comment-text">{comment.text}</div>
                                  <div className="comment-actions">
                                    <button
                                      onClick={() => handleLikeComment(comment.id)}
                                      className={`like-btn ${comment.liked ? 'liked' : ''}`}
                                    >
                                      <span>{comment.liked ? '❤️' : '🤍'}</span>
                                      {comment.likes > 0 && <span className="like-count">{comment.likes}</span>}
                                    </button>
                                    <button
                                      onClick={() => setReplyingTo(replyingTo === comment.id ? null : comment.id)}
                                      className="reply-btn"
                                    >
                                      <span>💬</span> Reply
                                    </button>
                                    {comment.replies.length > 0 && (
                                      <span className="replies-count">
                                        {comment.replies.length} {comment.replies.length === 1 ? 'reply' : 'replies'}
                                      </span>
                                    )}
                                  </div>
                                </div>
                              </div>

                              {/* Reply Input */}
                              {replyingTo === comment.id && (
                                <div className="reply-input-container">
                                  <div className="reply-input-wrapper">
                                    <div className="user-avatar small">
                                      {user?.name?.charAt(0)?.toUpperCase() || "A"}
                                    </div>
                                    <div className="reply-input-content">
                                      <textarea
                                        value={replyText}
                                        onChange={(e) => setReplyText(e.target.value)}
                                        placeholder={`Reply to ${comment.author}...`}
                                        className="reply-input"
                                        rows="2"
                                        autoFocus
                                      />
                                      <div className="reply-actions">
                                        <button
                                          onClick={() => handleAddReply(comment.id)}
                                          className="reply-submit-btn"
                                          disabled={!replyText.trim()}
                                        >
                                          <span>💬</span> Reply
                                        </button>
                                        <button
                                          onClick={() => {
                                            setReplyingTo(null);
                                            setReplyText("");
                                          }}
                                          className="reply-cancel-btn"
                                        >
                                          Cancel
                                        </button>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              )}

                              {/* Replies */}
                              {comment.replies.length > 0 && (
                                <div className="replies">
                                  {comment.replies.map((reply) => (
                                    <div key={reply.id} className="reply">
                                      <div className="reply-main">
                                        <div className="reply-avatar">
                                          {reply.avatar || reply.author?.charAt(0)?.toUpperCase() || "A"}
                                        </div>
                                        <div className="reply-content">
                                          <div className="reply-header">
                                            <span className="reply-author">{reply.author}</span>
                                            <span className="reply-time">
                                              {formatTimeAgo(reply.timestamp)}
                                            </span>
                                          </div>
                                          <div className="reply-text">{reply.text}</div>
                                          <div className="reply-actions">
                                            <button
                                              onClick={() => handleLikeComment(reply.id, true, comment.id)}
                                              className={`like-btn small ${reply.liked ? 'liked' : ''}`}
                                            >
                                              <span>{reply.liked ? '❤️' : '🤍'}</span>
                                              {reply.likes > 0 && <span className="like-count">{reply.likes}</span>}
                                            </button>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>
                          ))
                        )}
                      </div>
                        </div>
                      )}
                    </div>
                  </div>
                  {/* End of video-main-layout */}
                </div>
              );
            })()}
          </div>
        </div>
      )}
    </div>
  );
}

export default VideoLessons;
