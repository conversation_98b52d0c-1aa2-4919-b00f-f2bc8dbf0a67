{"ast": null, "code": "import * as React from 'react';\nexport var PreviewGroupContext = /*#__PURE__*/React.createContext(null);", "map": {"version": 3, "names": ["React", "PreviewGroupContext", "createContext"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-image/es/context.js"], "sourcesContent": ["import * as React from 'react';\nexport var PreviewGroupContext = /*#__PURE__*/React.createContext(null);"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,IAAIC,mBAAmB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}