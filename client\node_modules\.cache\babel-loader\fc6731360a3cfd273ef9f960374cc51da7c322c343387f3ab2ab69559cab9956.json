{"ast": null, "code": "// Kiswahili translations for Primary Kiswahili Medium level\nexport const kiswahiliTranslations = {\n  // Navigation and Common UI\n  navigation: {\n    home: \"Nyumba<PERSON>\",\n    quizzes: \"<PERSON><PERSON><PERSON>\",\n    videos: \"Video\",\n    ranking: \"<PERSON><PERSON> ya Ushindi\",\n    forum: \"<PERSON><PERSON><PERSON><PERSON>\",\n    profile: \"<PERSON><PERSON><PERSON>\",\n    logout: \"Ondoka\",\n    settings: \"Mipangilio\"\n  },\n  // Authentication\n  auth: {\n    login: \"Ingia\",\n    register: \"<PERSON><PERSON><PERSON><PERSON>\",\n    username: \"<PERSON><PERSON> la Mtumiaji\",\n    password: \"<PERSON><PERSON><PERSON><PERSON>\",\n    firstName: \"<PERSON><PERSON>wan<PERSON>\",\n    lastName: \"<PERSON><PERSON>wish<PERSON>\",\n    school: \"<PERSON><PERSON>\",\n    level: \"<PERSON><PERSON><PERSON>\",\n    class: \"Darasa\",\n    phoneNumber: \"<PERSON><PERSON> ya Simu\",\n    email: \"Barua Pepe\"\n  },\n  // Quiz System\n  quiz: {\n    startQuiz: \"Anza Mtihani\",\n    submitQuiz: \"<PERSON><PERSON><PERSON>ihani\",\n    nextQuestion: \"Swali Lijalo\",\n    previousQuestion: \"<PERSON>wal<PERSON>\",\n    timeRemaining: \"<PERSON><PERSON>\",\n    score: \"<PERSON><PERSON>\",\n    correct: \"<PERSON><PERSON><PERSON>\",\n    incorrect: \"Makosa\",\n    wellDone: \"Hongera!\",\n    tryAgain: \"<PERSON><PERSON><PERSON> Ten<PERSON>\",\n    minutes: \"<PERSON><PERSON><PERSON>\",\n    seconds: \"Se<PERSON><PERSON>\"\n  },\n  // Subjects (Primary Level in Kiswahili)\n  subjects: {\n    \"Hisabati\": \"Hisabati\",\n    \"Sayansi na Teknolojia\": \"Sayansi na Teknolojia\",\n    \"Jiografia\": \"Jiografia\",\n    \"Kiswahili\": \"Kiswahili\",\n    \"Maarifa ya Jamii\": \"Maarifa ya Jamii\",\n    \"Kiingereza\": \"Kiingereza\",\n    \"Dini\": \"Dini\",\n    \"Hesabu\": \"Hesabu\",\n    \"Michezo na Sanaa\": \"Michezo na Sanaa\",\n    \"Afya na Mazingira\": \"Afya na Mazingira\",\n    \"Uraia na Maadili\": \"Uraia na Maadili\",\n    \"Kifaransa\": \"Kifaransa\",\n    \"Historia ya Tanzania\": \"Historia ya Tanzania\"\n  },\n  // Classes\n  classes: {\n    \"1\": \"Darasa la Kwanza\",\n    \"2\": \"Darasa la Pili\",\n    \"3\": \"Darasa la Tatu\",\n    \"4\": \"Darasa la Nne\",\n    \"5\": \"Darasa la Tano\",\n    \"6\": \"Darasa la Sita\",\n    \"7\": \"Darasa la Saba\"\n  },\n  // Common Messages\n  messages: {\n    welcome: \"Karibu\",\n    loading: \"Inapakia...\",\n    error: \"Hitilafu\",\n    success: \"Mafanikio\",\n    save: \"Hifadhi\",\n    cancel: \"Ghairi\",\n    delete: \"Futa\",\n    edit: \"Hariri\",\n    view: \"Ona\",\n    search: \"Tafuta\",\n    filter: \"Chuja\",\n    refresh: \"Onyesha Upya\"\n  },\n  // Study Materials\n  studyMaterials: {\n    videos: \"Video za Masomo\",\n    documents: \"Hati za Masomo\",\n    notes: \"Maelezo\",\n    exercises: \"Mazoezi\",\n    download: \"Pakua\",\n    watch: \"Tazama\",\n    read: \"Soma\"\n  },\n  // Profile and Settings\n  profile: {\n    myProfile: \"Wasifu Wangu\",\n    editProfile: \"Hariri Wasifu\",\n    changePassword: \"Badilisha Nywila\",\n    statistics: \"Takwimu\",\n    achievements: \"Mafanikio\",\n    level: \"Kiwango\",\n    xp: \"Pointi za Uzoefu\",\n    streak: \"Mfuatano\"\n  },\n  // Time and Date\n  time: {\n    today: \"Leo\",\n    yesterday: \"Jana\",\n    tomorrow: \"Kesho\",\n    week: \"Wiki\",\n    month: \"Mwezi\",\n    year: \"Mwaka\",\n    morning: \"Asubuhi\",\n    afternoon: \"Mchana\",\n    evening: \"Jioni\",\n    night: \"Usiku\"\n  },\n  // Numbers in Kiswahili\n  numbers: {\n    \"1\": \"moja\",\n    \"2\": \"mbili\",\n    \"3\": \"tatu\",\n    \"4\": \"nne\",\n    \"5\": \"tano\",\n    \"6\": \"sita\",\n    \"7\": \"saba\",\n    \"8\": \"nane\",\n    \"9\": \"tisa\",\n    \"10\": \"kumi\"\n  },\n  // Educational Terms\n  education: {\n    student: \"Mwanafunzi\",\n    teacher: \"Mwalimu\",\n    lesson: \"Somo\",\n    homework: \"Kazi ya Nyumbani\",\n    exam: \"Mtihani\",\n    grade: \"Daraja\",\n    certificate: \"Cheti\",\n    graduation: \"Kuhitimu\",\n    knowledge: \"Maarifa\",\n    learning: \"Kujifunza\"\n  },\n  // Motivational Messages\n  motivation: {\n    keepLearning: \"Endelea Kujifunza!\",\n    greatJob: \"Kazi Nzuri!\",\n    almostThere: \"Karibu Kufika!\",\n    excellent: \"Bora Sana!\",\n    goodLuck: \"Bahati Njema!\",\n    believeInYourself: \"Jiamini Mwenyewe!\",\n    neverGiveUp: \"Usikate Tamaa!\",\n    practiceMore: \"Zoeza Zaidi!\"\n  }\n};\n\n// Helper function to get translation\nexport const getKiswahiliTranslation = (key, fallback = key) => {\n  const keys = key.split('.');\n  let translation = kiswahiliTranslations;\n  for (const k of keys) {\n    if (translation && translation[k]) {\n      translation = translation[k];\n    } else {\n      return fallback;\n    }\n  }\n  return translation || fallback;\n};\n\n// Helper function to check if user is in Kiswahili mode\nexport const isKiswahiliMode = userLevel => {\n  return userLevel === 'primary_kiswahili';\n};\nexport default kiswahiliTranslations;", "map": {"version": 3, "names": ["kiswahiliTranslations", "navigation", "home", "quizzes", "videos", "ranking", "forum", "profile", "logout", "settings", "auth", "login", "register", "username", "password", "firstName", "lastName", "school", "level", "class", "phoneNumber", "email", "quiz", "startQuiz", "submitQuiz", "nextQuestion", "previousQuestion", "timeRemaining", "score", "correct", "incorrect", "wellDone", "try<PERSON><PERSON>n", "minutes", "seconds", "subjects", "classes", "messages", "welcome", "loading", "error", "success", "save", "cancel", "delete", "edit", "view", "search", "filter", "refresh", "studyMaterials", "documents", "notes", "exercises", "download", "watch", "read", "myProfile", "editProfile", "changePassword", "statistics", "achievements", "xp", "streak", "time", "today", "yesterday", "tomorrow", "week", "month", "year", "morning", "afternoon", "evening", "night", "numbers", "education", "student", "teacher", "lesson", "homework", "exam", "grade", "certificate", "graduation", "knowledge", "learning", "motivation", "keepLearning", "<PERSON><PERSON><PERSON>", "almostThere", "excellent", "goodLuck", "believeInYourself", "neverGiveUp", "practiceMore", "getKiswahiliTranslation", "key", "fallback", "keys", "split", "translation", "k", "isKiswahiliMode", "userLevel"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/localization/kiswahili.js"], "sourcesContent": ["// Kiswahili translations for Primary Kiswahili Medium level\nexport const kiswahiliTranslations = {\n  // Navigation and Common UI\n  navigation: {\n    home: \"Nyumba<PERSON>\",\n    quizzes: \"<PERSON><PERSON><PERSON>\",\n    videos: \"Video\",\n    ranking: \"<PERSON><PERSON> ya Ushindi\",\n    forum: \"<PERSON><PERSON><PERSON><PERSON>\",\n    profile: \"<PERSON><PERSON><PERSON>\",\n    logout: \"Ondoka\",\n    settings: \"Mipangilio\"\n  },\n\n  // Authentication\n  auth: {\n    login: \"Ingia\",\n    register: \"<PERSON><PERSON><PERSON><PERSON>\",\n    username: \"<PERSON><PERSON> la Mtumiaji\",\n    password: \"<PERSON><PERSON><PERSON><PERSON>\",\n    firstName: \"<PERSON><PERSON>wan<PERSON>\",\n    lastName: \"<PERSON><PERSON>wish<PERSON>\",\n    school: \"<PERSON><PERSON>\",\n    level: \"<PERSON><PERSON><PERSON>\",\n    class: \"Darasa\",\n    phoneNumber: \"<PERSON><PERSON> ya Simu\",\n    email: \"Barua Pepe\"\n  },\n\n  // Quiz System\n  quiz: {\n    startQuiz: \"Anza Mtihani\",\n    submitQuiz: \"<PERSON><PERSON><PERSON>ihani\",\n    nextQuestion: \"Swali Lijalo\",\n    previousQuestion: \"<PERSON>wal<PERSON>\",\n    timeRemaining: \"<PERSON><PERSON>\",\n    score: \"<PERSON><PERSON>\",\n    correct: \"<PERSON><PERSON><PERSON>\",\n    incorrect: \"Makosa\",\n    wellDone: \"Hongera!\",\n    tryAgain: \"<PERSON><PERSON><PERSON> Ten<PERSON>\",\n    minutes: \"<PERSON><PERSON><PERSON>\",\n    seconds: \"Se<PERSON><PERSON>\"\n  },\n\n  // Subjects (Primary Level in Kiswahili)\n  subjects: {\n    \"Hisabati\": \"Hisabati\",\n    \"Sayansi na Teknolojia\": \"Sayansi na Teknolojia\",\n    \"Jiografia\": \"Jiografia\",\n    \"Kiswahili\": \"Kiswahili\",\n    \"Maarifa ya Jamii\": \"Maarifa ya Jamii\",\n    \"Kiingereza\": \"Kiingereza\",\n    \"Dini\": \"Dini\",\n    \"Hesabu\": \"Hesabu\",\n    \"Michezo na Sanaa\": \"Michezo na Sanaa\",\n    \"Afya na Mazingira\": \"Afya na Mazingira\",\n    \"Uraia na Maadili\": \"Uraia na Maadili\",\n    \"Kifaransa\": \"Kifaransa\",\n    \"Historia ya Tanzania\": \"Historia ya Tanzania\"\n  },\n\n  // Classes\n  classes: {\n    \"1\": \"Darasa la Kwanza\",\n    \"2\": \"Darasa la Pili\",\n    \"3\": \"Darasa la Tatu\",\n    \"4\": \"Darasa la Nne\",\n    \"5\": \"Darasa la Tano\",\n    \"6\": \"Darasa la Sita\",\n    \"7\": \"Darasa la Saba\"\n  },\n\n  // Common Messages\n  messages: {\n    welcome: \"Karibu\",\n    loading: \"Inapakia...\",\n    error: \"Hitilafu\",\n    success: \"Mafanikio\",\n    save: \"Hifadhi\",\n    cancel: \"Ghairi\",\n    delete: \"Futa\",\n    edit: \"Hariri\",\n    view: \"Ona\",\n    search: \"Tafuta\",\n    filter: \"Chuja\",\n    refresh: \"Onyesha Upya\"\n  },\n\n  // Study Materials\n  studyMaterials: {\n    videos: \"Video za Masomo\",\n    documents: \"Hati za Masomo\",\n    notes: \"Maelezo\",\n    exercises: \"Mazoezi\",\n    download: \"Pakua\",\n    watch: \"Tazama\",\n    read: \"Soma\"\n  },\n\n  // Profile and Settings\n  profile: {\n    myProfile: \"Wasifu Wangu\",\n    editProfile: \"Hariri Wasifu\",\n    changePassword: \"Badilisha Nywila\",\n    statistics: \"Takwimu\",\n    achievements: \"Mafanikio\",\n    level: \"Kiwango\",\n    xp: \"Pointi za Uzoefu\",\n    streak: \"Mfuatano\"\n  },\n\n  // Time and Date\n  time: {\n    today: \"Leo\",\n    yesterday: \"Jana\",\n    tomorrow: \"Kesho\",\n    week: \"Wiki\",\n    month: \"Mwezi\",\n    year: \"Mwaka\",\n    morning: \"Asubuhi\",\n    afternoon: \"Mchana\",\n    evening: \"Jioni\",\n    night: \"Usiku\"\n  },\n\n  // Numbers in Kiswahili\n  numbers: {\n    \"1\": \"moja\",\n    \"2\": \"mbili\",\n    \"3\": \"tatu\",\n    \"4\": \"nne\",\n    \"5\": \"tano\",\n    \"6\": \"sita\",\n    \"7\": \"saba\",\n    \"8\": \"nane\",\n    \"9\": \"tisa\",\n    \"10\": \"kumi\"\n  },\n\n  // Educational Terms\n  education: {\n    student: \"Mwanafunzi\",\n    teacher: \"Mwalimu\",\n    lesson: \"Somo\",\n    homework: \"Kazi ya Nyumbani\",\n    exam: \"Mtihani\",\n    grade: \"Daraja\",\n    certificate: \"Cheti\",\n    graduation: \"Kuhitimu\",\n    knowledge: \"Maarifa\",\n    learning: \"Kujifunza\"\n  },\n\n  // Motivational Messages\n  motivation: {\n    keepLearning: \"Endelea Kujifunza!\",\n    greatJob: \"Kazi Nzuri!\",\n    almostThere: \"Karibu Kufika!\",\n    excellent: \"Bora Sana!\",\n    goodLuck: \"Bahati Njema!\",\n    believeInYourself: \"Jiamini Mwenyewe!\",\n    neverGiveUp: \"Usikate Tamaa!\",\n    practiceMore: \"Zoeza Zaidi!\"\n  }\n};\n\n// Helper function to get translation\nexport const getKiswahiliTranslation = (key, fallback = key) => {\n  const keys = key.split('.');\n  let translation = kiswahiliTranslations;\n  \n  for (const k of keys) {\n    if (translation && translation[k]) {\n      translation = translation[k];\n    } else {\n      return fallback;\n    }\n  }\n  \n  return translation || fallback;\n};\n\n// Helper function to check if user is in Kiswahili mode\nexport const isKiswahiliMode = (userLevel) => {\n  return userLevel === 'primary_kiswahili';\n};\n\nexport default kiswahiliTranslations;\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,qBAAqB,GAAG;EACnC;EACAC,UAAU,EAAE;IACVC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,UAAU;IACnBC,MAAM,EAAE,OAAO;IACfC,OAAO,EAAE,mBAAmB;IAC5BC,KAAK,EAAE,QAAQ;IACfC,OAAO,EAAE,QAAQ;IACjBC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE;EACZ,CAAC;EAED;EACAC,IAAI,EAAE;IACJC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,kBAAkB;IAC5BC,QAAQ,EAAE,QAAQ;IAClBC,SAAS,EAAE,gBAAgB;IAC3BC,QAAQ,EAAE,gBAAgB;IAC1BC,MAAM,EAAE,OAAO;IACfC,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE,iBAAiB;IAC9BC,KAAK,EAAE;EACT,CAAC;EAED;EACAC,IAAI,EAAE;IACJC,SAAS,EAAE,cAAc;IACzBC,UAAU,EAAE,mBAAmB;IAC/BC,YAAY,EAAE,cAAc;IAC5BC,gBAAgB,EAAE,kBAAkB;IACpCC,aAAa,EAAE,eAAe;IAC9BC,KAAK,EAAE,OAAO;IACdC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,aAAa;IACvBC,OAAO,EAAE,QAAQ;IACjBC,OAAO,EAAE;EACX,CAAC;EAED;EACAC,QAAQ,EAAE;IACR,UAAU,EAAE,UAAU;IACtB,uBAAuB,EAAE,uBAAuB;IAChD,WAAW,EAAE,WAAW;IACxB,WAAW,EAAE,WAAW;IACxB,kBAAkB,EAAE,kBAAkB;IACtC,YAAY,EAAE,YAAY;IAC1B,MAAM,EAAE,MAAM;IACd,QAAQ,EAAE,QAAQ;IAClB,kBAAkB,EAAE,kBAAkB;IACtC,mBAAmB,EAAE,mBAAmB;IACxC,kBAAkB,EAAE,kBAAkB;IACtC,WAAW,EAAE,WAAW;IACxB,sBAAsB,EAAE;EAC1B,CAAC;EAED;EACAC,OAAO,EAAE;IACP,GAAG,EAAE,kBAAkB;IACvB,GAAG,EAAE,gBAAgB;IACrB,GAAG,EAAE,gBAAgB;IACrB,GAAG,EAAE,eAAe;IACpB,GAAG,EAAE,gBAAgB;IACrB,GAAG,EAAE,gBAAgB;IACrB,GAAG,EAAE;EACP,CAAC;EAED;EACAC,QAAQ,EAAE;IACRC,OAAO,EAAE,QAAQ;IACjBC,OAAO,EAAE,aAAa;IACtBC,KAAK,EAAE,UAAU;IACjBC,OAAO,EAAE,WAAW;IACpBC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,KAAK;IACXC,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,OAAO;IACfC,OAAO,EAAE;EACX,CAAC;EAED;EACAC,cAAc,EAAE;IACd9C,MAAM,EAAE,iBAAiB;IACzB+C,SAAS,EAAE,gBAAgB;IAC3BC,KAAK,EAAE,SAAS;IAChBC,SAAS,EAAE,SAAS;IACpBC,QAAQ,EAAE,OAAO;IACjBC,KAAK,EAAE,QAAQ;IACfC,IAAI,EAAE;EACR,CAAC;EAED;EACAjD,OAAO,EAAE;IACPkD,SAAS,EAAE,cAAc;IACzBC,WAAW,EAAE,eAAe;IAC5BC,cAAc,EAAE,kBAAkB;IAClCC,UAAU,EAAE,SAAS;IACrBC,YAAY,EAAE,WAAW;IACzB3C,KAAK,EAAE,SAAS;IAChB4C,EAAE,EAAE,kBAAkB;IACtBC,MAAM,EAAE;EACV,CAAC;EAED;EACAC,IAAI,EAAE;IACJC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,MAAM;IACjBC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,OAAO;IACdC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EAED;EACAC,OAAO,EAAE;IACP,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,OAAO;IACZ,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,KAAK;IACV,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,IAAI,EAAE;EACR,CAAC;EAED;EACAC,SAAS,EAAE;IACTC,OAAO,EAAE,YAAY;IACrBC,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE,kBAAkB;IAC5BC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE,OAAO;IACpBC,UAAU,EAAE,UAAU;IACtBC,SAAS,EAAE,SAAS;IACpBC,QAAQ,EAAE;EACZ,CAAC;EAED;EACAC,UAAU,EAAE;IACVC,YAAY,EAAE,oBAAoB;IAClCC,QAAQ,EAAE,aAAa;IACvBC,WAAW,EAAE,gBAAgB;IAC7BC,SAAS,EAAE,YAAY;IACvBC,QAAQ,EAAE,eAAe;IACzBC,iBAAiB,EAAE,mBAAmB;IACtCC,WAAW,EAAE,gBAAgB;IAC7BC,YAAY,EAAE;EAChB;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,uBAAuB,GAAGA,CAACC,GAAG,EAAEC,QAAQ,GAAGD,GAAG,KAAK;EAC9D,MAAME,IAAI,GAAGF,GAAG,CAACG,KAAK,CAAC,GAAG,CAAC;EAC3B,IAAIC,WAAW,GAAGrG,qBAAqB;EAEvC,KAAK,MAAMsG,CAAC,IAAIH,IAAI,EAAE;IACpB,IAAIE,WAAW,IAAIA,WAAW,CAACC,CAAC,CAAC,EAAE;MACjCD,WAAW,GAAGA,WAAW,CAACC,CAAC,CAAC;IAC9B,CAAC,MAAM;MACL,OAAOJ,QAAQ;IACjB;EACF;EAEA,OAAOG,WAAW,IAAIH,QAAQ;AAChC,CAAC;;AAED;AACA,OAAO,MAAMK,eAAe,GAAIC,SAAS,IAAK;EAC5C,OAAOA,SAAS,KAAK,mBAAmB;AAC1C,CAAC;AAED,eAAexG,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}