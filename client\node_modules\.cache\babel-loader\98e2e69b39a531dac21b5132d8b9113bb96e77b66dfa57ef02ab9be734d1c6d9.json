{"ast": null, "code": "import * as React from 'react';\n\n/**\n * Same as `React.useCallback` but always return a memoized function\n * but redirect to real function.\n */\nexport default function useRefFunc(callback) {\n  var funcRef = React.useRef();\n  funcRef.current = callback;\n  var cacheFn = React.useCallback(function () {\n    return funcRef.current.apply(funcRef, arguments);\n  }, []);\n  return cacheFn;\n}", "map": {"version": 3, "names": ["React", "useRefFunc", "callback", "funcRef", "useRef", "current", "cacheFn", "useCallback", "apply", "arguments"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-tree-select/es/hooks/useRefFunc.js"], "sourcesContent": ["import * as React from 'react';\n\n/**\n * Same as `React.useCallback` but always return a memoized function\n * but redirect to real function.\n */\nexport default function useRefFunc(callback) {\n  var funcRef = React.useRef();\n  funcRef.current = callback;\n  var cacheFn = React.useCallback(function () {\n    return funcRef.current.apply(funcRef, arguments);\n  }, []);\n  return cacheFn;\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;;AAE9B;AACA;AACA;AACA;AACA,eAAe,SAASC,UAAUA,CAACC,QAAQ,EAAE;EAC3C,IAAIC,OAAO,GAAGH,KAAK,CAACI,MAAM,CAAC,CAAC;EAC5BD,OAAO,CAACE,OAAO,GAAGH,QAAQ;EAC1B,IAAII,OAAO,GAAGN,KAAK,CAACO,WAAW,CAAC,YAAY;IAC1C,OAAOJ,OAAO,CAACE,OAAO,CAACG,KAAK,CAACL,OAAO,EAAEM,SAAS,CAAC;EAClD,CAAC,EAAE,EAAE,CAAC;EACN,OAAOH,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}