{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\StudyMaterial\\\\PDFModal.js\",\n  _s = $RefreshSig$();\nimport { useEffect, useRef, useState } from \"react\";\nimport * as pdfjsLib from \"pdfjs-dist\";\nimport ReactModal from \"react-modal\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nReactModal.setAppElement('#root');\npdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@2.15.349/build/pdf.worker.min.js`;\n\n// Add CSS for spinner animation\nconst spinnerStyle = `\n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n`;\n\n// Inject the CSS\nif (typeof document !== 'undefined') {\n  const style = document.createElement('style');\n  style.textContent = spinnerStyle;\n  document.head.appendChild(style);\n}\nconst PDFModal = ({\n  modalIsOpen,\n  closeModal,\n  documentUrl\n}) => {\n  _s();\n  const [pages, setPages] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [loadingProgress, setLoadingProgress] = useState(0);\n  const [totalPages, setTotalPages] = useState(0);\n  const [selectedText, setSelectedText] = useState('');\n  const [showCopyButton, setShowCopyButton] = useState(false);\n  const canvasRefs = useRef([]);\n  const textLayerRefs = useRef([]);\n  const containerRef = useRef(null);\n  const renderingRefs = useRef({}); // Track rendering state per page\n\n  // Handle text selection\n  const handleTextSelection = () => {\n    const selection = window.getSelection();\n    const text = selection.toString().trim();\n    if (text) {\n      setSelectedText(text);\n      setShowCopyButton(true);\n    } else {\n      setSelectedText('');\n      setShowCopyButton(false);\n    }\n  };\n\n  // Copy selected text to clipboard\n  const copyToClipboard = async () => {\n    if (selectedText) {\n      try {\n        await navigator.clipboard.writeText(selectedText);\n        alert('Text copied to clipboard!');\n        setShowCopyButton(false);\n        window.getSelection().removeAllRanges();\n      } catch (err) {\n        // Fallback for older browsers\n        const textArea = document.createElement('textarea');\n        textArea.value = selectedText;\n        document.body.appendChild(textArea);\n        textArea.select();\n        document.execCommand('copy');\n        document.body.removeChild(textArea);\n        alert('Text copied to clipboard!');\n        setShowCopyButton(false);\n        window.getSelection().removeAllRanges();\n      }\n    }\n  };\n  const renderPDF = async url => {\n    try {\n      setIsLoading(true);\n      setLoadingProgress(0);\n      const pdf = await pdfjsLib.getDocument(url).promise;\n      console.log(\"PDF loaded\");\n      setTotalPages(pdf.numPages);\n\n      // Load pages progressively (first 3 pages immediately, then lazy load others)\n      const initialPagesToLoad = Math.min(3, pdf.numPages);\n      const pagesData = [];\n      for (let i = 1; i <= initialPagesToLoad; i++) {\n        const page = await pdf.getPage(i);\n        pagesData.push(page);\n        setLoadingProgress(i / pdf.numPages * 100);\n      }\n      setPages(pagesData);\n      setIsLoading(false);\n\n      // Load remaining pages in background\n      if (pdf.numPages > initialPagesToLoad) {\n        loadRemainingPages(pdf, initialPagesToLoad + 1, pagesData);\n      }\n    } catch (error) {\n      console.error(\"Error loading PDF:\", error);\n      setIsLoading(false);\n    }\n  };\n  const loadRemainingPages = async (pdf, startPage, existingPages) => {\n    const updatedPages = [...existingPages];\n    for (let i = startPage; i <= pdf.numPages; i++) {\n      try {\n        const page = await pdf.getPage(i);\n        updatedPages.push(page);\n        setPages([...updatedPages]);\n        setLoadingProgress(i / pdf.numPages * 100);\n\n        // Small delay to prevent blocking the UI\n        await new Promise(resolve => setTimeout(resolve, 50));\n      } catch (error) {\n        console.error(`Error loading page ${i}:`, error);\n      }\n    }\n  };\n  const renderPage = async (page, index) => {\n    const canvas = canvasRefs.current[index];\n    const textLayer = textLayerRefs.current[index];\n    if (!canvas || renderingRefs.current[index] || !containerRef.current) return;\n    try {\n      renderingRefs.current[index] = true;\n      const viewport = page.getViewport({\n        scale: 1.0\n      });\n      const containerWidth = containerRef.current.clientWidth;\n      const scale = containerWidth / viewport.width;\n      const scaledViewport = page.getViewport({\n        scale\n      });\n      const context = canvas.getContext(\"2d\");\n      canvas.height = scaledViewport.height;\n      canvas.width = scaledViewport.width;\n      canvas.style.width = '100%';\n      canvas.style.height = 'auto';\n      const renderContext = {\n        canvasContext: context,\n        viewport: scaledViewport\n      };\n      await page.render(renderContext).promise;\n\n      // Render text layer for selection\n      if (textLayer) {\n        textLayer.innerHTML = '';\n        textLayer.style.width = canvas.style.width;\n        textLayer.style.height = canvas.style.height;\n        textLayer.style.position = 'absolute';\n        textLayer.style.top = '0';\n        textLayer.style.left = '0';\n        textLayer.style.pointerEvents = 'auto';\n        textLayer.style.userSelect = 'text';\n        try {\n          const textContent = await page.getTextContent();\n          const textLayerDiv = textLayer;\n\n          // Simple text rendering for selection\n          textContent.items.forEach((item, itemIndex) => {\n            const textSpan = document.createElement('span');\n            textSpan.textContent = item.str;\n            textSpan.style.position = 'absolute';\n            textSpan.style.fontSize = `${item.height * scale}px`;\n            textSpan.style.left = `${item.transform[4] * scale}px`;\n            textSpan.style.top = `${scaledViewport.height - item.transform[5] * scale - item.height * scale}px`;\n            textSpan.style.fontFamily = item.fontName || 'sans-serif';\n            textSpan.style.color = 'transparent';\n            textSpan.style.userSelect = 'text';\n            textLayerDiv.appendChild(textSpan);\n          });\n        } catch (textError) {\n          console.warn(`Could not render text layer for page ${index + 1}:`, textError);\n        }\n      }\n      console.log(`Page ${index + 1} rendered`);\n    } catch (error) {\n      console.error(`Error rendering page ${index + 1}:`, error);\n    } finally {\n      renderingRefs.current[index] = false;\n    }\n  };\n  useEffect(() => {\n    if (modalIsOpen && documentUrl) {\n      setPages([]);\n      setTotalPages(0);\n      setLoadingProgress(0);\n      canvasRefs.current = [];\n      renderingRefs.current = {};\n      renderPDF(documentUrl);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [modalIsOpen, documentUrl]);\n\n  // Effect to render pages when they're loaded\n  useEffect(() => {\n    if (pages.length > 0 && containerRef.current) {\n      pages.forEach((page, index) => {\n        renderPage(page, index);\n      });\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [pages]);\n\n  // Re-render pages when window is resized\n  useEffect(() => {\n    const handleResize = () => {\n      if (pages.length > 0) {\n        pages.forEach((page, index) => {\n          renderPage(page, index);\n        });\n      }\n    };\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, [pages]);\n  return /*#__PURE__*/_jsxDEV(ReactModal, {\n    isOpen: modalIsOpen,\n    onRequestClose: closeModal,\n    contentLabel: \"Document Preview\",\n    style: {\n      overlay: {\n        backgroundColor: 'rgba(0, 0, 0, 0.75)'\n      },\n      content: {\n        top: '50%',\n        left: '50%',\n        right: 'auto',\n        bottom: 'auto',\n        marginRight: '-50%',\n        transform: 'translate(-50%, -50%)',\n        width: '70%',\n        height: '90%',\n        padding: '20px',\n        borderRadius: '10px',\n        overflow: 'hidden'\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: closeModal,\n      style: {\n        position: \"absolute\",\n        top: \"10px\",\n        right: \"10px\",\n        background: \"transparent\",\n        border: \"none\",\n        fontSize: \"20px\",\n        cursor: \"pointer\",\n        zIndex: 1\n      },\n      children: \"X\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        height: '100%',\n        overflow: 'auto',\n        padding: '10px',\n        scrollbarWidth: 'thin'\n      },\n      children: [isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          justifyContent: 'center',\n          height: '200px',\n          color: '#666'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '50px',\n            height: '50px',\n            border: '3px solid #f3f3f3',\n            borderTop: '3px solid #3498db',\n            borderRadius: '50%',\n            animation: 'spin 1s linear infinite',\n            marginBottom: '20px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading PDF...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '200px',\n            height: '6px',\n            backgroundColor: '#f3f3f3',\n            borderRadius: '3px',\n            overflow: 'hidden',\n            marginTop: '10px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: `${loadingProgress}%`,\n              height: '100%',\n              backgroundColor: '#3498db',\n              transition: 'width 0.3s ease'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          style: {\n            marginTop: '5px'\n          },\n          children: [Math.round(loadingProgress), \"% loaded\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 11\n      }, this), pages.map((page, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '10px',\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"canvas\", {\n          ref: element => {\n            canvasRefs.current[index] = element;\n          },\n          style: {\n            maxWidth: '100%',\n            height: 'auto',\n            border: '1px solid black'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 13\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 11\n      }, this)), totalPages > pages.length && !isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '20px',\n          color: '#666',\n          fontStyle: 'italic'\n        },\n        children: [\"Loading remaining pages... (\", pages.length, \"/\", totalPages, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 228,\n    columnNumber: 5\n  }, this);\n};\n_s(PDFModal, \"bdem5RaQChnFhqrMTxHShA5srpA=\");\n_c = PDFModal;\nexport default PDFModal;\nvar _c;\n$RefreshReg$(_c, \"PDFModal\");", "map": {"version": 3, "names": ["useEffect", "useRef", "useState", "pdfjsLib", "ReactModal", "jsxDEV", "_jsxDEV", "setAppElement", "GlobalWorkerOptions", "workerSrc", "spinnerStyle", "document", "style", "createElement", "textContent", "head", "append<PERSON><PERSON><PERSON>", "PDFModal", "modalIsOpen", "closeModal", "documentUrl", "_s", "pages", "setPages", "isLoading", "setIsLoading", "loadingProgress", "setLoadingProgress", "totalPages", "setTotalPages", "selectedText", "setSelectedText", "showCopyButton", "setShowCopyButton", "canvasRefs", "textLayerRefs", "containerRef", "renderingRefs", "handleTextSelection", "selection", "window", "getSelection", "text", "toString", "trim", "copyToClipboard", "navigator", "clipboard", "writeText", "alert", "removeAllRanges", "err", "textArea", "value", "body", "select", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "renderPDF", "url", "pdf", "getDocument", "promise", "console", "log", "numPages", "initialPagesToLoad", "Math", "min", "pagesData", "i", "page", "getPage", "push", "loadRemainingPages", "error", "startPage", "existingPages", "updatedPages", "Promise", "resolve", "setTimeout", "renderPage", "index", "canvas", "current", "textLayer", "viewport", "getViewport", "scale", "containerWidth", "clientWidth", "width", "scaledViewport", "context", "getContext", "height", "renderContext", "canvasContext", "render", "innerHTML", "position", "top", "left", "pointerEvents", "userSelect", "getTextContent", "textLayerDiv", "items", "for<PERSON>ach", "item", "itemIndex", "textSpan", "str", "fontSize", "transform", "fontFamily", "fontName", "color", "textError", "warn", "length", "handleResize", "addEventListener", "removeEventListener", "isOpen", "onRequestClose", "contentLabel", "overlay", "backgroundColor", "content", "right", "bottom", "marginRight", "padding", "borderRadius", "overflow", "children", "onClick", "background", "border", "cursor", "zIndex", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "scrollbarWidth", "display", "flexDirection", "alignItems", "justifyContent", "borderTop", "animation", "marginBottom", "marginTop", "transition", "round", "map", "element", "max<PERSON><PERSON><PERSON>", "textAlign", "fontStyle", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/StudyMaterial/PDFModal.js"], "sourcesContent": ["import { useEffect, useRef, useState } from \"react\";\r\nimport * as pdfjsLib from \"pdfjs-dist\";\r\nimport ReactModal from \"react-modal\";\r\nReactModal.setAppElement('#root');\r\n\r\npdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@2.15.349/build/pdf.worker.min.js`;\r\n\r\n// Add CSS for spinner animation\r\nconst spinnerStyle = `\r\n  @keyframes spin {\r\n    0% { transform: rotate(0deg); }\r\n    100% { transform: rotate(360deg); }\r\n  }\r\n`;\r\n\r\n// Inject the CSS\r\nif (typeof document !== 'undefined') {\r\n  const style = document.createElement('style');\r\n  style.textContent = spinnerStyle;\r\n  document.head.appendChild(style);\r\n}\r\n\r\nconst PDFModal = ({ modalIsOpen, closeModal, documentUrl }) => {\r\n  const [pages, setPages] = useState([]);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [loadingProgress, setLoadingProgress] = useState(0);\r\n  const [totalPages, setTotalPages] = useState(0);\r\n  const [selectedText, setSelectedText] = useState('');\r\n  const [showCopyButton, setShowCopyButton] = useState(false);\r\n  const canvasRefs = useRef([]);\r\n  const textLayerRefs = useRef([]);\r\n  const containerRef = useRef(null);\r\n  const renderingRefs = useRef({}); // Track rendering state per page\r\n\r\n  // Handle text selection\r\n  const handleTextSelection = () => {\r\n    const selection = window.getSelection();\r\n    const text = selection.toString().trim();\r\n\r\n    if (text) {\r\n      setSelectedText(text);\r\n      setShowCopyButton(true);\r\n    } else {\r\n      setSelectedText('');\r\n      setShowCopyButton(false);\r\n    }\r\n  };\r\n\r\n  // Copy selected text to clipboard\r\n  const copyToClipboard = async () => {\r\n    if (selectedText) {\r\n      try {\r\n        await navigator.clipboard.writeText(selectedText);\r\n        alert('Text copied to clipboard!');\r\n        setShowCopyButton(false);\r\n        window.getSelection().removeAllRanges();\r\n      } catch (err) {\r\n        // Fallback for older browsers\r\n        const textArea = document.createElement('textarea');\r\n        textArea.value = selectedText;\r\n        document.body.appendChild(textArea);\r\n        textArea.select();\r\n        document.execCommand('copy');\r\n        document.body.removeChild(textArea);\r\n        alert('Text copied to clipboard!');\r\n        setShowCopyButton(false);\r\n        window.getSelection().removeAllRanges();\r\n      }\r\n    }\r\n  };\r\n\r\n  const renderPDF = async (url) => {\r\n    try {\r\n      setIsLoading(true);\r\n      setLoadingProgress(0);\r\n\r\n      const pdf = await pdfjsLib.getDocument(url).promise;\r\n      console.log(\"PDF loaded\");\r\n\r\n      setTotalPages(pdf.numPages);\r\n\r\n      // Load pages progressively (first 3 pages immediately, then lazy load others)\r\n      const initialPagesToLoad = Math.min(3, pdf.numPages);\r\n      const pagesData = [];\r\n\r\n      for (let i = 1; i <= initialPagesToLoad; i++) {\r\n        const page = await pdf.getPage(i);\r\n        pagesData.push(page);\r\n        setLoadingProgress((i / pdf.numPages) * 100);\r\n      }\r\n\r\n      setPages(pagesData);\r\n      setIsLoading(false);\r\n\r\n      // Load remaining pages in background\r\n      if (pdf.numPages > initialPagesToLoad) {\r\n        loadRemainingPages(pdf, initialPagesToLoad + 1, pagesData);\r\n      }\r\n\r\n    } catch (error) {\r\n      console.error(\"Error loading PDF:\", error);\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const loadRemainingPages = async (pdf, startPage, existingPages) => {\r\n    const updatedPages = [...existingPages];\r\n\r\n    for (let i = startPage; i <= pdf.numPages; i++) {\r\n      try {\r\n        const page = await pdf.getPage(i);\r\n        updatedPages.push(page);\r\n        setPages([...updatedPages]);\r\n        setLoadingProgress((i / pdf.numPages) * 100);\r\n\r\n        // Small delay to prevent blocking the UI\r\n        await new Promise(resolve => setTimeout(resolve, 50));\r\n      } catch (error) {\r\n        console.error(`Error loading page ${i}:`, error);\r\n      }\r\n    }\r\n  };\r\n\r\n  const renderPage = async (page, index) => {\r\n    const canvas = canvasRefs.current[index];\r\n    const textLayer = textLayerRefs.current[index];\r\n    if (!canvas || renderingRefs.current[index] || !containerRef.current) return;\r\n\r\n    try {\r\n      renderingRefs.current[index] = true;\r\n\r\n      const viewport = page.getViewport({ scale: 1.0 });\r\n      const containerWidth = containerRef.current.clientWidth;\r\n      const scale = containerWidth / viewport.width;\r\n      const scaledViewport = page.getViewport({ scale });\r\n\r\n      const context = canvas.getContext(\"2d\");\r\n      canvas.height = scaledViewport.height;\r\n      canvas.width = scaledViewport.width;\r\n      canvas.style.width = '100%';\r\n      canvas.style.height = 'auto';\r\n\r\n      const renderContext = {\r\n        canvasContext: context,\r\n        viewport: scaledViewport,\r\n      };\r\n\r\n      await page.render(renderContext).promise;\r\n\r\n      // Render text layer for selection\r\n      if (textLayer) {\r\n        textLayer.innerHTML = '';\r\n        textLayer.style.width = canvas.style.width;\r\n        textLayer.style.height = canvas.style.height;\r\n        textLayer.style.position = 'absolute';\r\n        textLayer.style.top = '0';\r\n        textLayer.style.left = '0';\r\n        textLayer.style.pointerEvents = 'auto';\r\n        textLayer.style.userSelect = 'text';\r\n\r\n        try {\r\n          const textContent = await page.getTextContent();\r\n          const textLayerDiv = textLayer;\r\n\r\n          // Simple text rendering for selection\r\n          textContent.items.forEach((item, itemIndex) => {\r\n            const textSpan = document.createElement('span');\r\n            textSpan.textContent = item.str;\r\n            textSpan.style.position = 'absolute';\r\n            textSpan.style.fontSize = `${item.height * scale}px`;\r\n            textSpan.style.left = `${item.transform[4] * scale}px`;\r\n            textSpan.style.top = `${scaledViewport.height - item.transform[5] * scale - item.height * scale}px`;\r\n            textSpan.style.fontFamily = item.fontName || 'sans-serif';\r\n            textSpan.style.color = 'transparent';\r\n            textSpan.style.userSelect = 'text';\r\n            textLayerDiv.appendChild(textSpan);\r\n          });\r\n        } catch (textError) {\r\n          console.warn(`Could not render text layer for page ${index + 1}:`, textError);\r\n        }\r\n      }\r\n\r\n      console.log(`Page ${index + 1} rendered`);\r\n    } catch (error) {\r\n      console.error(`Error rendering page ${index + 1}:`, error);\r\n    } finally {\r\n      renderingRefs.current[index] = false;\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (modalIsOpen && documentUrl) {\r\n      setPages([]);\r\n      setTotalPages(0);\r\n      setLoadingProgress(0);\r\n      canvasRefs.current = [];\r\n      renderingRefs.current = {};\r\n      renderPDF(documentUrl);\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [modalIsOpen, documentUrl]);\r\n\r\n  // Effect to render pages when they're loaded\r\n  useEffect(() => {\r\n    if (pages.length > 0 && containerRef.current) {\r\n      pages.forEach((page, index) => {\r\n        renderPage(page, index);\r\n      });\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [pages]);\r\n\r\n  // Re-render pages when window is resized\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      if (pages.length > 0) {\r\n        pages.forEach((page, index) => {\r\n          renderPage(page, index);\r\n        });\r\n      }\r\n    };\r\n\r\n    window.addEventListener('resize', handleResize);\r\n    return () => window.removeEventListener('resize', handleResize);\r\n  }, [pages]);\r\n\r\n  return (\r\n    <ReactModal\r\n      isOpen={modalIsOpen}\r\n      onRequestClose={closeModal}\r\n      contentLabel=\"Document Preview\"\r\n      style={{\r\n        overlay: {\r\n          backgroundColor: 'rgba(0, 0, 0, 0.75)'\r\n        },\r\n        content: {\r\n          top: '50%',\r\n          left: '50%',\r\n          right: 'auto',\r\n          bottom: 'auto',\r\n          marginRight: '-50%',\r\n          transform: 'translate(-50%, -50%)',\r\n          width: '70%',\r\n          height: '90%',\r\n          padding: '20px',\r\n          borderRadius: '10px',\r\n          overflow: 'hidden',\r\n        },\r\n      }}\r\n    >\r\n      <button\r\n        onClick={closeModal}\r\n        style={{\r\n          position: \"absolute\",\r\n          top: \"10px\",\r\n          right: \"10px\",\r\n          background: \"transparent\",\r\n          border: \"none\",\r\n          fontSize: \"20px\",\r\n          cursor: \"pointer\",\r\n          zIndex: 1,\r\n        }}\r\n      >\r\n        X\r\n      </button>\r\n\r\n      <div\r\n        ref={containerRef}\r\n        style={{\r\n          height: '100%',\r\n          overflow: 'auto',\r\n          padding: '10px',\r\n          scrollbarWidth: 'thin'\r\n        }}\r\n      >\r\n        {isLoading && (\r\n          <div style={{\r\n            display: 'flex',\r\n            flexDirection: 'column',\r\n            alignItems: 'center',\r\n            justifyContent: 'center',\r\n            height: '200px',\r\n            color: '#666'\r\n          }}>\r\n            <div style={{\r\n              width: '50px',\r\n              height: '50px',\r\n              border: '3px solid #f3f3f3',\r\n              borderTop: '3px solid #3498db',\r\n              borderRadius: '50%',\r\n              animation: 'spin 1s linear infinite',\r\n              marginBottom: '20px'\r\n            }}></div>\r\n            <p>Loading PDF...</p>\r\n            <div style={{\r\n              width: '200px',\r\n              height: '6px',\r\n              backgroundColor: '#f3f3f3',\r\n              borderRadius: '3px',\r\n              overflow: 'hidden',\r\n              marginTop: '10px'\r\n            }}>\r\n              <div style={{\r\n                width: `${loadingProgress}%`,\r\n                height: '100%',\r\n                backgroundColor: '#3498db',\r\n                transition: 'width 0.3s ease'\r\n              }}></div>\r\n            </div>\r\n            <small style={{ marginTop: '5px' }}>\r\n              {Math.round(loadingProgress)}% loaded\r\n            </small>\r\n          </div>\r\n        )}\r\n\r\n        {pages.map((page, index) => (\r\n          <div\r\n            key={index}\r\n            style={{\r\n              marginBottom: '10px',\r\n              display: 'flex',\r\n              flexDirection: 'column',\r\n              alignItems: 'center'\r\n            }}\r\n          >\r\n            <canvas\r\n              ref={element => {\r\n                canvasRefs.current[index] = element;\r\n              }}\r\n              style={{\r\n                maxWidth: '100%',\r\n                height: 'auto',\r\n                border: '1px solid black'\r\n              }}\r\n            />\r\n          </div>\r\n        ))}\r\n\r\n        {totalPages > pages.length && !isLoading && (\r\n          <div style={{\r\n            textAlign: 'center',\r\n            padding: '20px',\r\n            color: '#666',\r\n            fontStyle: 'italic'\r\n          }}>\r\n            Loading remaining pages... ({pages.length}/{totalPages})\r\n          </div>\r\n        )}\r\n      </div>\r\n    </ReactModal>\r\n  );\r\n};\r\n\r\nexport default PDFModal;"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,OAAO,KAAKC,QAAQ,MAAM,YAAY;AACtC,OAAOC,UAAU,MAAM,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AACrCF,UAAU,CAACG,aAAa,CAAC,OAAO,CAAC;AAEjCJ,QAAQ,CAACK,mBAAmB,CAACC,SAAS,GAAI,+DAA8D;;AAExG;AACA,MAAMC,YAAY,GAAI;AACtB;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;EACnC,MAAMC,KAAK,GAAGD,QAAQ,CAACE,aAAa,CAAC,OAAO,CAAC;EAC7CD,KAAK,CAACE,WAAW,GAAGJ,YAAY;EAChCC,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACJ,KAAK,CAAC;AAClC;AAEA,MAAMK,QAAQ,GAAGA,CAAC;EAAEC,WAAW;EAAEC,UAAU;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EAC7D,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACsB,SAAS,EAAEC,YAAY,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACwB,eAAe,EAAEC,kBAAkB,CAAC,GAAGzB,QAAQ,CAAC,CAAC,CAAC;EACzD,MAAM,CAAC0B,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC8B,cAAc,EAAEC,iBAAiB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAMgC,UAAU,GAAGjC,MAAM,CAAC,EAAE,CAAC;EAC7B,MAAMkC,aAAa,GAAGlC,MAAM,CAAC,EAAE,CAAC;EAChC,MAAMmC,YAAY,GAAGnC,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMoC,aAAa,GAAGpC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAElC;EACA,MAAMqC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,SAAS,GAAGC,MAAM,CAACC,YAAY,CAAC,CAAC;IACvC,MAAMC,IAAI,GAAGH,SAAS,CAACI,QAAQ,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;IAExC,IAAIF,IAAI,EAAE;MACRX,eAAe,CAACW,IAAI,CAAC;MACrBT,iBAAiB,CAAC,IAAI,CAAC;IACzB,CAAC,MAAM;MACLF,eAAe,CAAC,EAAE,CAAC;MACnBE,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;;EAED;EACA,MAAMY,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAIf,YAAY,EAAE;MAChB,IAAI;QACF,MAAMgB,SAAS,CAACC,SAAS,CAACC,SAAS,CAAClB,YAAY,CAAC;QACjDmB,KAAK,CAAC,2BAA2B,CAAC;QAClChB,iBAAiB,CAAC,KAAK,CAAC;QACxBO,MAAM,CAACC,YAAY,CAAC,CAAC,CAACS,eAAe,CAAC,CAAC;MACzC,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZ;QACA,MAAMC,QAAQ,GAAGzC,QAAQ,CAACE,aAAa,CAAC,UAAU,CAAC;QACnDuC,QAAQ,CAACC,KAAK,GAAGvB,YAAY;QAC7BnB,QAAQ,CAAC2C,IAAI,CAACtC,WAAW,CAACoC,QAAQ,CAAC;QACnCA,QAAQ,CAACG,MAAM,CAAC,CAAC;QACjB5C,QAAQ,CAAC6C,WAAW,CAAC,MAAM,CAAC;QAC5B7C,QAAQ,CAAC2C,IAAI,CAACG,WAAW,CAACL,QAAQ,CAAC;QACnCH,KAAK,CAAC,2BAA2B,CAAC;QAClChB,iBAAiB,CAAC,KAAK,CAAC;QACxBO,MAAM,CAACC,YAAY,CAAC,CAAC,CAACS,eAAe,CAAC,CAAC;MACzC;IACF;EACF,CAAC;EAED,MAAMQ,SAAS,GAAG,MAAOC,GAAG,IAAK;IAC/B,IAAI;MACFlC,YAAY,CAAC,IAAI,CAAC;MAClBE,kBAAkB,CAAC,CAAC,CAAC;MAErB,MAAMiC,GAAG,GAAG,MAAMzD,QAAQ,CAAC0D,WAAW,CAACF,GAAG,CAAC,CAACG,OAAO;MACnDC,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;MAEzBnC,aAAa,CAAC+B,GAAG,CAACK,QAAQ,CAAC;;MAE3B;MACA,MAAMC,kBAAkB,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAER,GAAG,CAACK,QAAQ,CAAC;MACpD,MAAMI,SAAS,GAAG,EAAE;MAEpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIJ,kBAAkB,EAAEI,CAAC,EAAE,EAAE;QAC5C,MAAMC,IAAI,GAAG,MAAMX,GAAG,CAACY,OAAO,CAACF,CAAC,CAAC;QACjCD,SAAS,CAACI,IAAI,CAACF,IAAI,CAAC;QACpB5C,kBAAkB,CAAE2C,CAAC,GAAGV,GAAG,CAACK,QAAQ,GAAI,GAAG,CAAC;MAC9C;MAEA1C,QAAQ,CAAC8C,SAAS,CAAC;MACnB5C,YAAY,CAAC,KAAK,CAAC;;MAEnB;MACA,IAAImC,GAAG,CAACK,QAAQ,GAAGC,kBAAkB,EAAE;QACrCQ,kBAAkB,CAACd,GAAG,EAAEM,kBAAkB,GAAG,CAAC,EAAEG,SAAS,CAAC;MAC5D;IAEF,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1ClD,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMiD,kBAAkB,GAAG,MAAAA,CAAOd,GAAG,EAAEgB,SAAS,EAAEC,aAAa,KAAK;IAClE,MAAMC,YAAY,GAAG,CAAC,GAAGD,aAAa,CAAC;IAEvC,KAAK,IAAIP,CAAC,GAAGM,SAAS,EAAEN,CAAC,IAAIV,GAAG,CAACK,QAAQ,EAAEK,CAAC,EAAE,EAAE;MAC9C,IAAI;QACF,MAAMC,IAAI,GAAG,MAAMX,GAAG,CAACY,OAAO,CAACF,CAAC,CAAC;QACjCQ,YAAY,CAACL,IAAI,CAACF,IAAI,CAAC;QACvBhD,QAAQ,CAAC,CAAC,GAAGuD,YAAY,CAAC,CAAC;QAC3BnD,kBAAkB,CAAE2C,CAAC,GAAGV,GAAG,CAACK,QAAQ,GAAI,GAAG,CAAC;;QAE5C;QACA,MAAM,IAAIc,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,EAAE,CAAC,CAAC;MACvD,CAAC,CAAC,OAAOL,KAAK,EAAE;QACdZ,OAAO,CAACY,KAAK,CAAE,sBAAqBL,CAAE,GAAE,EAAEK,KAAK,CAAC;MAClD;IACF;EACF,CAAC;EAED,MAAMO,UAAU,GAAG,MAAAA,CAAOX,IAAI,EAAEY,KAAK,KAAK;IACxC,MAAMC,MAAM,GAAGlD,UAAU,CAACmD,OAAO,CAACF,KAAK,CAAC;IACxC,MAAMG,SAAS,GAAGnD,aAAa,CAACkD,OAAO,CAACF,KAAK,CAAC;IAC9C,IAAI,CAACC,MAAM,IAAI/C,aAAa,CAACgD,OAAO,CAACF,KAAK,CAAC,IAAI,CAAC/C,YAAY,CAACiD,OAAO,EAAE;IAEtE,IAAI;MACFhD,aAAa,CAACgD,OAAO,CAACF,KAAK,CAAC,GAAG,IAAI;MAEnC,MAAMI,QAAQ,GAAGhB,IAAI,CAACiB,WAAW,CAAC;QAAEC,KAAK,EAAE;MAAI,CAAC,CAAC;MACjD,MAAMC,cAAc,GAAGtD,YAAY,CAACiD,OAAO,CAACM,WAAW;MACvD,MAAMF,KAAK,GAAGC,cAAc,GAAGH,QAAQ,CAACK,KAAK;MAC7C,MAAMC,cAAc,GAAGtB,IAAI,CAACiB,WAAW,CAAC;QAAEC;MAAM,CAAC,CAAC;MAElD,MAAMK,OAAO,GAAGV,MAAM,CAACW,UAAU,CAAC,IAAI,CAAC;MACvCX,MAAM,CAACY,MAAM,GAAGH,cAAc,CAACG,MAAM;MACrCZ,MAAM,CAACQ,KAAK,GAAGC,cAAc,CAACD,KAAK;MACnCR,MAAM,CAACxE,KAAK,CAACgF,KAAK,GAAG,MAAM;MAC3BR,MAAM,CAACxE,KAAK,CAACoF,MAAM,GAAG,MAAM;MAE5B,MAAMC,aAAa,GAAG;QACpBC,aAAa,EAAEJ,OAAO;QACtBP,QAAQ,EAAEM;MACZ,CAAC;MAED,MAAMtB,IAAI,CAAC4B,MAAM,CAACF,aAAa,CAAC,CAACnC,OAAO;;MAExC;MACA,IAAIwB,SAAS,EAAE;QACbA,SAAS,CAACc,SAAS,GAAG,EAAE;QACxBd,SAAS,CAAC1E,KAAK,CAACgF,KAAK,GAAGR,MAAM,CAACxE,KAAK,CAACgF,KAAK;QAC1CN,SAAS,CAAC1E,KAAK,CAACoF,MAAM,GAAGZ,MAAM,CAACxE,KAAK,CAACoF,MAAM;QAC5CV,SAAS,CAAC1E,KAAK,CAACyF,QAAQ,GAAG,UAAU;QACrCf,SAAS,CAAC1E,KAAK,CAAC0F,GAAG,GAAG,GAAG;QACzBhB,SAAS,CAAC1E,KAAK,CAAC2F,IAAI,GAAG,GAAG;QAC1BjB,SAAS,CAAC1E,KAAK,CAAC4F,aAAa,GAAG,MAAM;QACtClB,SAAS,CAAC1E,KAAK,CAAC6F,UAAU,GAAG,MAAM;QAEnC,IAAI;UACF,MAAM3F,WAAW,GAAG,MAAMyD,IAAI,CAACmC,cAAc,CAAC,CAAC;UAC/C,MAAMC,YAAY,GAAGrB,SAAS;;UAE9B;UACAxE,WAAW,CAAC8F,KAAK,CAACC,OAAO,CAAC,CAACC,IAAI,EAAEC,SAAS,KAAK;YAC7C,MAAMC,QAAQ,GAAGrG,QAAQ,CAACE,aAAa,CAAC,MAAM,CAAC;YAC/CmG,QAAQ,CAAClG,WAAW,GAAGgG,IAAI,CAACG,GAAG;YAC/BD,QAAQ,CAACpG,KAAK,CAACyF,QAAQ,GAAG,UAAU;YACpCW,QAAQ,CAACpG,KAAK,CAACsG,QAAQ,GAAI,GAAEJ,IAAI,CAACd,MAAM,GAAGP,KAAM,IAAG;YACpDuB,QAAQ,CAACpG,KAAK,CAAC2F,IAAI,GAAI,GAAEO,IAAI,CAACK,SAAS,CAAC,CAAC,CAAC,GAAG1B,KAAM,IAAG;YACtDuB,QAAQ,CAACpG,KAAK,CAAC0F,GAAG,GAAI,GAAET,cAAc,CAACG,MAAM,GAAGc,IAAI,CAACK,SAAS,CAAC,CAAC,CAAC,GAAG1B,KAAK,GAAGqB,IAAI,CAACd,MAAM,GAAGP,KAAM,IAAG;YACnGuB,QAAQ,CAACpG,KAAK,CAACwG,UAAU,GAAGN,IAAI,CAACO,QAAQ,IAAI,YAAY;YACzDL,QAAQ,CAACpG,KAAK,CAAC0G,KAAK,GAAG,aAAa;YACpCN,QAAQ,CAACpG,KAAK,CAAC6F,UAAU,GAAG,MAAM;YAClCE,YAAY,CAAC3F,WAAW,CAACgG,QAAQ,CAAC;UACpC,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOO,SAAS,EAAE;UAClBxD,OAAO,CAACyD,IAAI,CAAE,wCAAuCrC,KAAK,GAAG,CAAE,GAAE,EAAEoC,SAAS,CAAC;QAC/E;MACF;MAEAxD,OAAO,CAACC,GAAG,CAAE,QAAOmB,KAAK,GAAG,CAAE,WAAU,CAAC;IAC3C,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAE,wBAAuBQ,KAAK,GAAG,CAAE,GAAE,EAAER,KAAK,CAAC;IAC5D,CAAC,SAAS;MACRtC,aAAa,CAACgD,OAAO,CAACF,KAAK,CAAC,GAAG,KAAK;IACtC;EACF,CAAC;EAEDnF,SAAS,CAAC,MAAM;IACd,IAAIkB,WAAW,IAAIE,WAAW,EAAE;MAC9BG,QAAQ,CAAC,EAAE,CAAC;MACZM,aAAa,CAAC,CAAC,CAAC;MAChBF,kBAAkB,CAAC,CAAC,CAAC;MACrBO,UAAU,CAACmD,OAAO,GAAG,EAAE;MACvBhD,aAAa,CAACgD,OAAO,GAAG,CAAC,CAAC;MAC1B3B,SAAS,CAACtC,WAAW,CAAC;IACxB;IACA;EACF,CAAC,EAAE,CAACF,WAAW,EAAEE,WAAW,CAAC,CAAC;;EAE9B;EACApB,SAAS,CAAC,MAAM;IACd,IAAIsB,KAAK,CAACmG,MAAM,GAAG,CAAC,IAAIrF,YAAY,CAACiD,OAAO,EAAE;MAC5C/D,KAAK,CAACuF,OAAO,CAAC,CAACtC,IAAI,EAAEY,KAAK,KAAK;QAC7BD,UAAU,CAACX,IAAI,EAAEY,KAAK,CAAC;MACzB,CAAC,CAAC;IACJ;IACA;EACF,CAAC,EAAE,CAAC7D,KAAK,CAAC,CAAC;;EAEX;EACAtB,SAAS,CAAC,MAAM;IACd,MAAM0H,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAIpG,KAAK,CAACmG,MAAM,GAAG,CAAC,EAAE;QACpBnG,KAAK,CAACuF,OAAO,CAAC,CAACtC,IAAI,EAAEY,KAAK,KAAK;UAC7BD,UAAU,CAACX,IAAI,EAAEY,KAAK,CAAC;QACzB,CAAC,CAAC;MACJ;IACF,CAAC;IAED3C,MAAM,CAACmF,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAMlF,MAAM,CAACoF,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EACjE,CAAC,EAAE,CAACpG,KAAK,CAAC,CAAC;EAEX,oBACEhB,OAAA,CAACF,UAAU;IACTyH,MAAM,EAAE3G,WAAY;IACpB4G,cAAc,EAAE3G,UAAW;IAC3B4G,YAAY,EAAC,kBAAkB;IAC/BnH,KAAK,EAAE;MACLoH,OAAO,EAAE;QACPC,eAAe,EAAE;MACnB,CAAC;MACDC,OAAO,EAAE;QACP5B,GAAG,EAAE,KAAK;QACVC,IAAI,EAAE,KAAK;QACX4B,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdC,WAAW,EAAE,MAAM;QACnBlB,SAAS,EAAE,uBAAuB;QAClCvB,KAAK,EAAE,KAAK;QACZI,MAAM,EAAE,KAAK;QACbsC,OAAO,EAAE,MAAM;QACfC,YAAY,EAAE,MAAM;QACpBC,QAAQ,EAAE;MACZ;IACF,CAAE;IAAAC,QAAA,gBAEFnI,OAAA;MACEoI,OAAO,EAAEvH,UAAW;MACpBP,KAAK,EAAE;QACLyF,QAAQ,EAAE,UAAU;QACpBC,GAAG,EAAE,MAAM;QACX6B,KAAK,EAAE,MAAM;QACbQ,UAAU,EAAE,aAAa;QACzBC,MAAM,EAAE,MAAM;QACd1B,QAAQ,EAAE,MAAM;QAChB2B,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE;MACV,CAAE;MAAAL,QAAA,EACH;IAED;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAET5I,OAAA;MACE6I,GAAG,EAAE/G,YAAa;MAClBxB,KAAK,EAAE;QACLoF,MAAM,EAAE,MAAM;QACdwC,QAAQ,EAAE,MAAM;QAChBF,OAAO,EAAE,MAAM;QACfc,cAAc,EAAE;MAClB,CAAE;MAAAX,QAAA,GAEDjH,SAAS,iBACRlB,OAAA;QAAKM,KAAK,EAAE;UACVyI,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBxD,MAAM,EAAE,OAAO;UACfsB,KAAK,EAAE;QACT,CAAE;QAAAmB,QAAA,gBACAnI,OAAA;UAAKM,KAAK,EAAE;YACVgF,KAAK,EAAE,MAAM;YACbI,MAAM,EAAE,MAAM;YACd4C,MAAM,EAAE,mBAAmB;YAC3Ba,SAAS,EAAE,mBAAmB;YAC9BlB,YAAY,EAAE,KAAK;YACnBmB,SAAS,EAAE,yBAAyB;YACpCC,YAAY,EAAE;UAChB;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACT5I,OAAA;UAAAmI,QAAA,EAAG;QAAc;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACrB5I,OAAA;UAAKM,KAAK,EAAE;YACVgF,KAAK,EAAE,OAAO;YACdI,MAAM,EAAE,KAAK;YACbiC,eAAe,EAAE,SAAS;YAC1BM,YAAY,EAAE,KAAK;YACnBC,QAAQ,EAAE,QAAQ;YAClBoB,SAAS,EAAE;UACb,CAAE;UAAAnB,QAAA,eACAnI,OAAA;YAAKM,KAAK,EAAE;cACVgF,KAAK,EAAG,GAAElE,eAAgB,GAAE;cAC5BsE,MAAM,EAAE,MAAM;cACdiC,eAAe,EAAE,SAAS;cAC1B4B,UAAU,EAAE;YACd;UAAE;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN5I,OAAA;UAAOM,KAAK,EAAE;YAAEgJ,SAAS,EAAE;UAAM,CAAE;UAAAnB,QAAA,GAChCtE,IAAI,CAAC2F,KAAK,CAACpI,eAAe,CAAC,EAAC,UAC/B;QAAA;UAAAqH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACN,EAEA5H,KAAK,CAACyI,GAAG,CAAC,CAACxF,IAAI,EAAEY,KAAK,kBACrB7E,OAAA;QAEEM,KAAK,EAAE;UACL+I,YAAY,EAAE,MAAM;UACpBN,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBC,UAAU,EAAE;QACd,CAAE;QAAAd,QAAA,eAEFnI,OAAA;UACE6I,GAAG,EAAEa,OAAO,IAAI;YACd9H,UAAU,CAACmD,OAAO,CAACF,KAAK,CAAC,GAAG6E,OAAO;UACrC,CAAE;UACFpJ,KAAK,EAAE;YACLqJ,QAAQ,EAAE,MAAM;YAChBjE,MAAM,EAAE,MAAM;YACd4C,MAAM,EAAE;UACV;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAjBG/D,KAAK;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAkBP,CACN,CAAC,EAEDtH,UAAU,GAAGN,KAAK,CAACmG,MAAM,IAAI,CAACjG,SAAS,iBACtClB,OAAA;QAAKM,KAAK,EAAE;UACVsJ,SAAS,EAAE,QAAQ;UACnB5B,OAAO,EAAE,MAAM;UACfhB,KAAK,EAAE,MAAM;UACb6C,SAAS,EAAE;QACb,CAAE;QAAA1B,QAAA,GAAC,8BAC2B,EAACnH,KAAK,CAACmG,MAAM,EAAC,GAAC,EAAC7F,UAAU,EAAC,GACzD;MAAA;QAAAmH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEjB,CAAC;AAAC7H,EAAA,CAzUIJ,QAAQ;AAAAmJ,EAAA,GAARnJ,QAAQ;AA2Ud,eAAeA,QAAQ;AAAC,IAAAmJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}