// Test login for Primary Kiswahili Medium user
const http = require('http');

const loginData = JSON.stringify({
  username: "amina_kiswahili_test",
  password: "test123"
});

const options = {
  hostname: 'localhost',
  port: 5000,
  path: '/api/users/login',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(loginData)
  }
};

console.log('🔐 Testing Primary Kiswahili Medium User Login...');

const req = http.request(options, (res) => {
  console.log(`\n📊 Status Code: ${res.statusCode}`);

  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    console.log('\n📝 Response:');
    try {
      const response = JSON.parse(data);
      
      if (res.statusCode === 200) {
        console.log('✅ Login successful!');
        console.log('👤 User Info:');
        console.log(`   Name: ${response.data.firstName} ${response.data.lastName}`);
        console.log(`   Username: ${response.data.username}`);
        console.log(`   Level: ${response.data.level}`);
        console.log(`   Class: ${response.data.class}`);
        console.log(`   School: ${response.data.school}`);
        
        // Verify the level
        if (response.data.level === 'primary_kiswahili') {
          console.log('\n🎉 SUCCESS: Level verification passed!');
          console.log('✅ Primary Kiswahili Medium level is correctly stored and retrieved');
        } else {
          console.log('\n❌ Level verification failed!');
          console.log(`Expected: primary_kiswahili, Got: ${response.data.level}`);
        }
      } else {
        console.log('❌ Login failed');
        console.log(JSON.stringify(response, null, 2));
      }
    } catch (error) {
      console.log('Raw response:', data);
      console.error('Parse error:', error.message);
    }
  });
});

req.on('error', (error) => {
  console.error('❌ Request failed:', error.message);
});

req.write(loginData);
req.end();
