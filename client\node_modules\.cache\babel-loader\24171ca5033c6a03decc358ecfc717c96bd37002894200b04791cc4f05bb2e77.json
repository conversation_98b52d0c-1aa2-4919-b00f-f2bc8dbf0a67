{"ast": null, "code": "import { axiosInstance } from \"./index\";\n\n// Public API calls (no authentication required)\n\n// Get all skills with filtering\nexport const getAllSkills = async (params = {}) => {\n  try {\n    const response = await axiosInstance.get(\"/api/skills\", {\n      params\n    });\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Get skill by ID\nexport const getSkillById = async id => {\n  try {\n    const response = await axiosInstance.get(`/api/skills/${id}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Get skills by level\nexport const getSkillsByLevel = async (level, limit = 20) => {\n  try {\n    const response = await axiosInstance.get(`/api/skills/level/${level}`, {\n      params: {\n        limit\n      }\n    });\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Get featured skills\nexport const getFeaturedSkills = async (limit = 10) => {\n  try {\n    const response = await axiosInstance.get(\"/api/skills/featured/list\", {\n      params: {\n        limit\n      }\n    });\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Search skills\nexport const searchSkills = async (query, filters = {}) => {\n  try {\n    const response = await axiosInstance.post(\"/api/skills/search\", {\n      query,\n      filters\n    });\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Authenticated API calls\n\n// Mark skill as completed\nexport const markSkillCompleted = async id => {\n  try {\n    const response = await axiosInstance.post(`/api/skills/${id}/complete`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Admin API calls\n\n// Create new skill\nexport const createSkill = async skillData => {\n  try {\n    const response = await axiosInstance.post(\"/api/skills/admin/create\", skillData);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Update skill\nexport const updateSkill = async (id, skillData) => {\n  try {\n    const response = await axiosInstance.put(`/api/skills/admin/${id}`, skillData);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Delete skill\nexport const deleteSkill = async id => {\n  try {\n    const response = await axiosInstance.delete(`/api/skills/admin/${id}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Get all skills for admin (includes inactive)\nexport const getAllSkillsAdmin = async (params = {}) => {\n  try {\n    const response = await axiosInstance.get(\"/api/skills/admin/all\", {\n      params\n    });\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};", "map": {"version": 3, "names": ["axiosInstance", "getAllSkills", "params", "response", "get", "data", "error", "getSkillById", "id", "getSkillsByLevel", "level", "limit", "getFeaturedSkills", "searchSkills", "query", "filters", "post", "mark<PERSON><PERSON>Completed", "createSkill", "skillData", "updateSkill", "put", "deleteSkill", "delete", "getAllSkillsAdmin"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/apicalls/skills.js"], "sourcesContent": ["import { axiosInstance } from \"./index\";\n\n// Public API calls (no authentication required)\n\n// Get all skills with filtering\nexport const getAllSkills = async (params = {}) => {\n  try {\n    const response = await axiosInstance.get(\"/api/skills\", { params });\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Get skill by ID\nexport const getSkillById = async (id) => {\n  try {\n    const response = await axiosInstance.get(`/api/skills/${id}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Get skills by level\nexport const getSkillsByLevel = async (level, limit = 20) => {\n  try {\n    const response = await axiosInstance.get(`/api/skills/level/${level}`, {\n      params: { limit }\n    });\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Get featured skills\nexport const getFeaturedSkills = async (limit = 10) => {\n  try {\n    const response = await axiosInstance.get(\"/api/skills/featured/list\", {\n      params: { limit }\n    });\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Search skills\nexport const searchSkills = async (query, filters = {}) => {\n  try {\n    const response = await axiosInstance.post(\"/api/skills/search\", {\n      query,\n      filters\n    });\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Authenticated API calls\n\n// Mark skill as completed\nexport const markSkillCompleted = async (id) => {\n  try {\n    const response = await axiosInstance.post(`/api/skills/${id}/complete`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Admin API calls\n\n// Create new skill\nexport const createSkill = async (skillData) => {\n  try {\n    const response = await axiosInstance.post(\"/api/skills/admin/create\", skillData);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Update skill\nexport const updateSkill = async (id, skillData) => {\n  try {\n    const response = await axiosInstance.put(`/api/skills/admin/${id}`, skillData);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Delete skill\nexport const deleteSkill = async (id) => {\n  try {\n    const response = await axiosInstance.delete(`/api/skills/admin/${id}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// Get all skills for admin (includes inactive)\nexport const getAllSkillsAdmin = async (params = {}) => {\n  try {\n    const response = await axiosInstance.get(\"/api/skills/admin/all\", { params });\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,SAAS;;AAEvC;;AAEA;AACA,OAAO,MAAMC,YAAY,GAAG,MAAAA,CAAOC,MAAM,GAAG,CAAC,CAAC,KAAK;EACjD,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMH,aAAa,CAACI,GAAG,CAAC,aAAa,EAAE;MAAEF;IAAO,CAAC,CAAC;IACnE,OAAOC,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC5B;AACF,CAAC;;AAED;AACA,OAAO,MAAME,YAAY,GAAG,MAAOC,EAAE,IAAK;EACxC,IAAI;IACF,MAAML,QAAQ,GAAG,MAAMH,aAAa,CAACI,GAAG,CAAE,eAAcI,EAAG,EAAC,CAAC;IAC7D,OAAOL,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC5B;AACF,CAAC;;AAED;AACA,OAAO,MAAMI,gBAAgB,GAAG,MAAAA,CAAOC,KAAK,EAAEC,KAAK,GAAG,EAAE,KAAK;EAC3D,IAAI;IACF,MAAMR,QAAQ,GAAG,MAAMH,aAAa,CAACI,GAAG,CAAE,qBAAoBM,KAAM,EAAC,EAAE;MACrER,MAAM,EAAE;QAAES;MAAM;IAClB,CAAC,CAAC;IACF,OAAOR,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC5B;AACF,CAAC;;AAED;AACA,OAAO,MAAMO,iBAAiB,GAAG,MAAAA,CAAOD,KAAK,GAAG,EAAE,KAAK;EACrD,IAAI;IACF,MAAMR,QAAQ,GAAG,MAAMH,aAAa,CAACI,GAAG,CAAC,2BAA2B,EAAE;MACpEF,MAAM,EAAE;QAAES;MAAM;IAClB,CAAC,CAAC;IACF,OAAOR,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC5B;AACF,CAAC;;AAED;AACA,OAAO,MAAMQ,YAAY,GAAG,MAAAA,CAAOC,KAAK,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;EACzD,IAAI;IACF,MAAMZ,QAAQ,GAAG,MAAMH,aAAa,CAACgB,IAAI,CAAC,oBAAoB,EAAE;MAC9DF,KAAK;MACLC;IACF,CAAC,CAAC;IACF,OAAOZ,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC5B;AACF,CAAC;;AAED;;AAEA;AACA,OAAO,MAAMY,kBAAkB,GAAG,MAAOT,EAAE,IAAK;EAC9C,IAAI;IACF,MAAML,QAAQ,GAAG,MAAMH,aAAa,CAACgB,IAAI,CAAE,eAAcR,EAAG,WAAU,CAAC;IACvE,OAAOL,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC5B;AACF,CAAC;;AAED;;AAEA;AACA,OAAO,MAAMa,WAAW,GAAG,MAAOC,SAAS,IAAK;EAC9C,IAAI;IACF,MAAMhB,QAAQ,GAAG,MAAMH,aAAa,CAACgB,IAAI,CAAC,0BAA0B,EAAEG,SAAS,CAAC;IAChF,OAAOhB,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC5B;AACF,CAAC;;AAED;AACA,OAAO,MAAMe,WAAW,GAAG,MAAAA,CAAOZ,EAAE,EAAEW,SAAS,KAAK;EAClD,IAAI;IACF,MAAMhB,QAAQ,GAAG,MAAMH,aAAa,CAACqB,GAAG,CAAE,qBAAoBb,EAAG,EAAC,EAAEW,SAAS,CAAC;IAC9E,OAAOhB,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC5B;AACF,CAAC;;AAED;AACA,OAAO,MAAMiB,WAAW,GAAG,MAAOd,EAAE,IAAK;EACvC,IAAI;IACF,MAAML,QAAQ,GAAG,MAAMH,aAAa,CAACuB,MAAM,CAAE,qBAAoBf,EAAG,EAAC,CAAC;IACtE,OAAOL,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC5B;AACF,CAAC;;AAED;AACA,OAAO,MAAMmB,iBAAiB,GAAG,MAAAA,CAAOtB,MAAM,GAAG,CAAC,CAAC,KAAK;EACtD,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMH,aAAa,CAACI,GAAG,CAAC,uBAAuB,EAAE;MAAEF;IAAO,CAAC,CAAC;IAC7E,OAAOC,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC5B;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}