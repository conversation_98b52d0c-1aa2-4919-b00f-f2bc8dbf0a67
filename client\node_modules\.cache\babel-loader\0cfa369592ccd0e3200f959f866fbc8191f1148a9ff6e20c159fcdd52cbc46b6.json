{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\StudyMaterial\\\\PDFModal.js\",\n  _s = $RefreshSig$();\nimport { useEffect, useRef, useState } from \"react\";\nimport * as pdfjsLib from \"pdfjs-dist\";\nimport ReactModal from \"react-modal\";\nimport FloatingBrainwaveAI from \"../../../components/FloatingBrainwaveAI\";\nimport { useLanguage } from \"../../../contexts/LanguageContext\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nReactModal.setAppElement('#root');\npdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@2.15.349/build/pdf.worker.min.js`;\n\n// Add CSS for spinner animation\nconst spinnerStyle = `\n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n`;\n\n// Inject the CSS\nif (typeof document !== 'undefined') {\n  const style = document.createElement('style');\n  style.textContent = spinnerStyle;\n  document.head.appendChild(style);\n}\nconst PDFModal = ({\n  modalIsOpen,\n  closeModal,\n  documentUrl\n}) => {\n  _s();\n  const {\n    isKiswahili\n  } = useLanguage();\n  const [pages, setPages] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [loadingProgress, setLoadingProgress] = useState(0);\n  const [totalPages, setTotalPages] = useState(0);\n  const [selectedText, setSelectedText] = useState('');\n  const [showCopyButton, setShowCopyButton] = useState(false);\n  const [pdfContext, setPdfContext] = useState(null);\n  const [extractedText, setExtractedText] = useState('');\n  const [showAIButton, setShowAIButton] = useState(false);\n  const [showBrainwaveAI, setShowBrainwaveAI] = useState(false);\n  const canvasRefs = useRef([]);\n  const textLayerRefs = useRef([]);\n  const containerRef = useRef(null);\n  const renderingRefs = useRef({}); // Track rendering state per page\n\n  // Handle text selection\n  const handleTextSelection = () => {\n    const selection = window.getSelection();\n    const text = selection.toString().trim();\n    if (text) {\n      setSelectedText(text);\n      setShowCopyButton(true);\n    } else {\n      setSelectedText('');\n      setShowCopyButton(false);\n    }\n  };\n\n  // Copy selected text to clipboard\n  const copyToClipboard = async () => {\n    if (selectedText) {\n      try {\n        await navigator.clipboard.writeText(selectedText);\n        alert('Text copied to clipboard!');\n        setShowCopyButton(false);\n        window.getSelection().removeAllRanges();\n      } catch (err) {\n        // Fallback for older browsers\n        const textArea = document.createElement('textarea');\n        textArea.value = selectedText;\n        document.body.appendChild(textArea);\n        textArea.select();\n        document.execCommand('copy');\n        document.body.removeChild(textArea);\n        alert('Text copied to clipboard!');\n        setShowCopyButton(false);\n        window.getSelection().removeAllRanges();\n      }\n    }\n  };\n  const renderPDF = async url => {\n    try {\n      setIsLoading(true);\n      setLoadingProgress(0);\n      const pdf = await pdfjsLib.getDocument(url).promise;\n      console.log(\"PDF loaded\");\n      setTotalPages(pdf.numPages);\n\n      // Load pages progressively (first 3 pages immediately, then lazy load others)\n      const initialPagesToLoad = Math.min(3, pdf.numPages);\n      const pagesData = [];\n      for (let i = 1; i <= initialPagesToLoad; i++) {\n        const page = await pdf.getPage(i);\n        pagesData.push(page);\n        setLoadingProgress(i / pdf.numPages * 100);\n      }\n      setPages(pagesData);\n      setIsLoading(false);\n\n      // Extract text from first few pages for AI context\n      await extractPDFText(pdf);\n\n      // Create PDF context for AI\n      const fileName = url.split('/').pop() || 'PDF Document';\n      setPdfContext({\n        title: fileName,\n        subject: 'Study Material',\n        totalPages: pdf.numPages,\n        extractedText: extractedText,\n        url: url\n      });\n      setShowAIButton(true);\n\n      // Load remaining pages in background\n      if (pdf.numPages > initialPagesToLoad) {\n        loadRemainingPages(pdf, initialPagesToLoad + 1, pagesData);\n      }\n    } catch (error) {\n      console.error(\"Error loading PDF:\", error);\n      setIsLoading(false);\n    }\n  };\n  const loadRemainingPages = async (pdf, startPage, existingPages) => {\n    const updatedPages = [...existingPages];\n    for (let i = startPage; i <= pdf.numPages; i++) {\n      try {\n        const page = await pdf.getPage(i);\n        updatedPages.push(page);\n        setPages([...updatedPages]);\n        setLoadingProgress(i / pdf.numPages * 100);\n\n        // Small delay to prevent blocking the UI\n        await new Promise(resolve => setTimeout(resolve, 50));\n      } catch (error) {\n        console.error(`Error loading page ${i}:`, error);\n      }\n    }\n  };\n\n  // Extract text from PDF for AI context\n  const extractPDFText = async pdf => {\n    try {\n      let fullText = '';\n      const maxPagesToExtract = Math.min(5, pdf.numPages); // Extract from first 5 pages\n\n      for (let i = 1; i <= maxPagesToExtract; i++) {\n        const page = await pdf.getPage(i);\n        const textContent = await page.getTextContent();\n        const pageText = textContent.items.map(item => item.str).join(' ');\n        fullText += `Page ${i}: ${pageText}\\n\\n`;\n      }\n      setExtractedText(fullText);\n      return fullText;\n    } catch (error) {\n      console.warn('Could not extract text from PDF:', error);\n      return '';\n    }\n  };\n  const renderPage = async (page, index) => {\n    const canvas = canvasRefs.current[index];\n    const textLayer = textLayerRefs.current[index];\n    if (!canvas || renderingRefs.current[index] || !containerRef.current) return;\n    try {\n      renderingRefs.current[index] = true;\n      const viewport = page.getViewport({\n        scale: 1.0\n      });\n      const containerWidth = containerRef.current.clientWidth;\n      const scale = containerWidth / viewport.width;\n      const scaledViewport = page.getViewport({\n        scale\n      });\n      const context = canvas.getContext(\"2d\");\n      canvas.height = scaledViewport.height;\n      canvas.width = scaledViewport.width;\n      canvas.style.width = '100%';\n      canvas.style.height = 'auto';\n      const renderContext = {\n        canvasContext: context,\n        viewport: scaledViewport\n      };\n      await page.render(renderContext).promise;\n\n      // Render text layer for selection\n      if (textLayer) {\n        textLayer.innerHTML = '';\n        textLayer.style.width = canvas.style.width;\n        textLayer.style.height = canvas.style.height;\n        textLayer.style.position = 'absolute';\n        textLayer.style.top = '0';\n        textLayer.style.left = '0';\n        textLayer.style.pointerEvents = 'auto';\n        textLayer.style.userSelect = 'text';\n        try {\n          const textContent = await page.getTextContent();\n          const textLayerDiv = textLayer;\n\n          // Simple text rendering for selection\n          textContent.items.forEach((item, itemIndex) => {\n            const textSpan = document.createElement('span');\n            textSpan.textContent = item.str;\n            textSpan.style.position = 'absolute';\n            textSpan.style.fontSize = `${item.height * scale}px`;\n            textSpan.style.left = `${item.transform[4] * scale}px`;\n            textSpan.style.top = `${scaledViewport.height - item.transform[5] * scale - item.height * scale}px`;\n            textSpan.style.fontFamily = item.fontName || 'sans-serif';\n            textSpan.style.color = 'transparent';\n            textSpan.style.userSelect = 'text';\n            textLayerDiv.appendChild(textSpan);\n          });\n        } catch (textError) {\n          console.warn(`Could not render text layer for page ${index + 1}:`, textError);\n        }\n      }\n      console.log(`Page ${index + 1} rendered`);\n    } catch (error) {\n      console.error(`Error rendering page ${index + 1}:`, error);\n    } finally {\n      renderingRefs.current[index] = false;\n    }\n  };\n  useEffect(() => {\n    if (modalIsOpen && documentUrl) {\n      setPages([]);\n      setTotalPages(0);\n      setLoadingProgress(0);\n      setSelectedText('');\n      setShowCopyButton(false);\n      canvasRefs.current = [];\n      textLayerRefs.current = [];\n      renderingRefs.current = {};\n      renderPDF(documentUrl);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [modalIsOpen, documentUrl]);\n\n  // Add event listener for text selection\n  useEffect(() => {\n    if (modalIsOpen) {\n      document.addEventListener('selectionchange', handleTextSelection);\n\n      // Listen for custom event to open Brainwave AI\n      const handleOpenBrainwaveAI = event => {\n        setShowBrainwaveAI(true);\n      };\n      window.addEventListener('openBrainwaveAI', handleOpenBrainwaveAI);\n      return () => {\n        document.removeEventListener('selectionchange', handleTextSelection);\n        window.removeEventListener('openBrainwaveAI', handleOpenBrainwaveAI);\n      };\n    }\n  }, [modalIsOpen]);\n\n  // Effect to render pages when they're loaded\n  useEffect(() => {\n    if (pages.length > 0 && containerRef.current) {\n      pages.forEach((page, index) => {\n        renderPage(page, index);\n      });\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [pages]);\n\n  // Re-render pages when window is resized\n  useEffect(() => {\n    const handleResize = () => {\n      if (pages.length > 0) {\n        pages.forEach((page, index) => {\n          renderPage(page, index);\n        });\n      }\n    };\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, [pages]);\n  return /*#__PURE__*/_jsxDEV(ReactModal, {\n    isOpen: modalIsOpen,\n    onRequestClose: closeModal,\n    contentLabel: \"Document Preview\",\n    style: {\n      overlay: {\n        backgroundColor: 'rgba(0, 0, 0, 0.75)'\n      },\n      content: {\n        top: '50%',\n        left: '50%',\n        right: 'auto',\n        bottom: 'auto',\n        marginRight: '-50%',\n        transform: 'translate(-50%, -50%)',\n        width: '70%',\n        height: '90%',\n        padding: '20px',\n        borderRadius: '10px',\n        overflow: 'hidden'\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: closeModal,\n      style: {\n        position: \"absolute\",\n        top: \"10px\",\n        right: \"10px\",\n        background: \"transparent\",\n        border: \"none\",\n        fontSize: \"20px\",\n        cursor: \"pointer\",\n        zIndex: 1\n      },\n      children: \"X\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 316,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        height: '100%',\n        overflow: 'auto',\n        padding: '10px',\n        scrollbarWidth: 'thin'\n      },\n      children: [isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          justifyContent: 'center',\n          height: '200px',\n          color: '#666'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '50px',\n            height: '50px',\n            border: '3px solid #f3f3f3',\n            borderTop: '3px solid #3498db',\n            borderRadius: '50%',\n            animation: 'spin 1s linear infinite',\n            marginBottom: '20px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading PDF...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '200px',\n            height: '6px',\n            backgroundColor: '#f3f3f3',\n            borderRadius: '3px',\n            overflow: 'hidden',\n            marginTop: '10px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: `${loadingProgress}%`,\n              height: '100%',\n              backgroundColor: '#3498db',\n              transition: 'width 0.3s ease'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          style: {\n            marginTop: '5px'\n          },\n          children: [Math.round(loadingProgress), \"% loaded\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 342,\n        columnNumber: 11\n      }, this), showCopyButton && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'fixed',\n          top: '20px',\n          right: '20px',\n          zIndex: 1000,\n          background: '#007BFF',\n          color: 'white',\n          padding: '10px 15px',\n          borderRadius: '5px',\n          cursor: 'pointer',\n          boxShadow: '0 2px 10px rgba(0,0,0,0.3)',\n          fontSize: '14px',\n          fontWeight: 'bold'\n        },\n        onClick: copyToClipboard,\n        children: [\"\\uD83D\\uDCCB \", isKiswahili ? 'Nakili Maandishi' : 'Copy Selected Text']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 11\n      }, this), showAIButton && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'fixed',\n          bottom: '20px',\n          right: '20px',\n          zIndex: 1000,\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n          color: 'white',\n          padding: '12px 18px',\n          borderRadius: '25px',\n          cursor: 'pointer',\n          boxShadow: '0 4px 15px rgba(102, 126, 234, 0.4)',\n          fontSize: '14px',\n          fontWeight: 'bold',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '8px',\n          transition: 'all 0.3s ease'\n        },\n        onClick: () => {\n          // This will be handled by the FloatingBrainwaveAI component\n          const event = new CustomEvent('openBrainwaveAI', {\n            detail: {\n              pdfContext,\n              selectedText\n            }\n          });\n          window.dispatchEvent(event);\n        },\n        onMouseEnter: e => {\n          e.target.style.transform = 'translateY(-2px)';\n          e.target.style.boxShadow = '0 6px 20px rgba(102, 126, 234, 0.6)';\n        },\n        onMouseLeave: e => {\n          e.target.style.transform = 'translateY(0)';\n          e.target.style.boxShadow = '0 4px 15px rgba(102, 126, 234, 0.4)';\n        },\n        children: [\"\\uD83E\\uDD16 \", isKiswahili ? 'Uliza AI kuhusu PDF' : 'Ask AI about PDF']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 403,\n        columnNumber: 11\n      }, this), pages.map((page, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '10px',\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          position: 'relative'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'relative',\n            display: 'inline-block'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"canvas\", {\n            ref: element => {\n              canvasRefs.current[index] = element;\n            },\n            style: {\n              maxWidth: '100%',\n              height: 'auto',\n              border: '1px solid black',\n              display: 'block'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: element => {\n              textLayerRefs.current[index] = element;\n            },\n            style: {\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              width: '100%',\n              height: '100%',\n              pointerEvents: 'auto',\n              userSelect: 'text',\n              cursor: 'text'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 451,\n          columnNumber: 13\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 441,\n        columnNumber: 11\n      }, this)), totalPages > pages.length && !isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '20px',\n          color: '#666',\n          fontStyle: 'italic'\n        },\n        children: [\"Loading remaining pages... (\", pages.length, \"/\", totalPages, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 483,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 293,\n    columnNumber: 5\n  }, this);\n};\n_s(PDFModal, \"qgC7JFZCfEo4oK9bcC0/m2Renug=\", false, function () {\n  return [useLanguage];\n});\n_c = PDFModal;\nexport default PDFModal;\nvar _c;\n$RefreshReg$(_c, \"PDFModal\");", "map": {"version": 3, "names": ["useEffect", "useRef", "useState", "pdfjsLib", "ReactModal", "FloatingBrainwaveAI", "useLanguage", "jsxDEV", "_jsxDEV", "setAppElement", "GlobalWorkerOptions", "workerSrc", "spinnerStyle", "document", "style", "createElement", "textContent", "head", "append<PERSON><PERSON><PERSON>", "PDFModal", "modalIsOpen", "closeModal", "documentUrl", "_s", "isKiswahili", "pages", "setPages", "isLoading", "setIsLoading", "loadingProgress", "setLoadingProgress", "totalPages", "setTotalPages", "selectedText", "setSelectedText", "showCopyButton", "setShowCopyButton", "pdfContext", "setPdfContext", "extractedText", "setExtractedText", "showAIButton", "setShowAIButton", "showBrainwaveAI", "setShowBrainwaveAI", "canvasRefs", "textLayerRefs", "containerRef", "renderingRefs", "handleTextSelection", "selection", "window", "getSelection", "text", "toString", "trim", "copyToClipboard", "navigator", "clipboard", "writeText", "alert", "removeAllRanges", "err", "textArea", "value", "body", "select", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "renderPDF", "url", "pdf", "getDocument", "promise", "console", "log", "numPages", "initialPagesToLoad", "Math", "min", "pagesData", "i", "page", "getPage", "push", "extractPDFText", "fileName", "split", "pop", "title", "subject", "loadRemainingPages", "error", "startPage", "existingPages", "updatedPages", "Promise", "resolve", "setTimeout", "fullText", "maxPagesToExtract", "getTextContent", "pageText", "items", "map", "item", "str", "join", "warn", "renderPage", "index", "canvas", "current", "textLayer", "viewport", "getViewport", "scale", "containerWidth", "clientWidth", "width", "scaledViewport", "context", "getContext", "height", "renderContext", "canvasContext", "render", "innerHTML", "position", "top", "left", "pointerEvents", "userSelect", "textLayerDiv", "for<PERSON>ach", "itemIndex", "textSpan", "fontSize", "transform", "fontFamily", "fontName", "color", "textError", "addEventListener", "handleOpenBrainwaveAI", "event", "removeEventListener", "length", "handleResize", "isOpen", "onRequestClose", "contentLabel", "overlay", "backgroundColor", "content", "right", "bottom", "marginRight", "padding", "borderRadius", "overflow", "children", "onClick", "background", "border", "cursor", "zIndex", "_jsxFileName", "lineNumber", "columnNumber", "ref", "scrollbarWidth", "display", "flexDirection", "alignItems", "justifyContent", "borderTop", "animation", "marginBottom", "marginTop", "transition", "round", "boxShadow", "fontWeight", "gap", "CustomEvent", "detail", "dispatchEvent", "onMouseEnter", "e", "target", "onMouseLeave", "element", "max<PERSON><PERSON><PERSON>", "textAlign", "fontStyle", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/StudyMaterial/PDFModal.js"], "sourcesContent": ["import { useEffect, useRef, useState } from \"react\";\r\nimport * as pdfjsLib from \"pdfjs-dist\";\r\nimport ReactModal from \"react-modal\";\r\nimport FloatingBrainwaveAI from \"../../../components/FloatingBrainwaveAI\";\r\nimport { useLanguage } from \"../../../contexts/LanguageContext\";\r\nReactModal.setAppElement('#root');\r\n\r\npdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@2.15.349/build/pdf.worker.min.js`;\r\n\r\n// Add CSS for spinner animation\r\nconst spinnerStyle = `\r\n  @keyframes spin {\r\n    0% { transform: rotate(0deg); }\r\n    100% { transform: rotate(360deg); }\r\n  }\r\n`;\r\n\r\n// Inject the CSS\r\nif (typeof document !== 'undefined') {\r\n  const style = document.createElement('style');\r\n  style.textContent = spinnerStyle;\r\n  document.head.appendChild(style);\r\n}\r\n\r\nconst PDFModal = ({ modalIsOpen, closeModal, documentUrl }) => {\r\n  const { isKiswahili } = useLanguage();\r\n  const [pages, setPages] = useState([]);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [loadingProgress, setLoadingProgress] = useState(0);\r\n  const [totalPages, setTotalPages] = useState(0);\r\n  const [selectedText, setSelectedText] = useState('');\r\n  const [showCopyButton, setShowCopyButton] = useState(false);\r\n  const [pdfContext, setPdfContext] = useState(null);\r\n  const [extractedText, setExtractedText] = useState('');\r\n  const [showAIButton, setShowAIButton] = useState(false);\r\n  const [showBrainwaveAI, setShowBrainwaveAI] = useState(false);\r\n  const canvasRefs = useRef([]);\r\n  const textLayerRefs = useRef([]);\r\n  const containerRef = useRef(null);\r\n  const renderingRefs = useRef({}); // Track rendering state per page\r\n\r\n  // Handle text selection\r\n  const handleTextSelection = () => {\r\n    const selection = window.getSelection();\r\n    const text = selection.toString().trim();\r\n\r\n    if (text) {\r\n      setSelectedText(text);\r\n      setShowCopyButton(true);\r\n    } else {\r\n      setSelectedText('');\r\n      setShowCopyButton(false);\r\n    }\r\n  };\r\n\r\n  // Copy selected text to clipboard\r\n  const copyToClipboard = async () => {\r\n    if (selectedText) {\r\n      try {\r\n        await navigator.clipboard.writeText(selectedText);\r\n        alert('Text copied to clipboard!');\r\n        setShowCopyButton(false);\r\n        window.getSelection().removeAllRanges();\r\n      } catch (err) {\r\n        // Fallback for older browsers\r\n        const textArea = document.createElement('textarea');\r\n        textArea.value = selectedText;\r\n        document.body.appendChild(textArea);\r\n        textArea.select();\r\n        document.execCommand('copy');\r\n        document.body.removeChild(textArea);\r\n        alert('Text copied to clipboard!');\r\n        setShowCopyButton(false);\r\n        window.getSelection().removeAllRanges();\r\n      }\r\n    }\r\n  };\r\n\r\n  const renderPDF = async (url) => {\r\n    try {\r\n      setIsLoading(true);\r\n      setLoadingProgress(0);\r\n\r\n      const pdf = await pdfjsLib.getDocument(url).promise;\r\n      console.log(\"PDF loaded\");\r\n\r\n      setTotalPages(pdf.numPages);\r\n\r\n      // Load pages progressively (first 3 pages immediately, then lazy load others)\r\n      const initialPagesToLoad = Math.min(3, pdf.numPages);\r\n      const pagesData = [];\r\n\r\n      for (let i = 1; i <= initialPagesToLoad; i++) {\r\n        const page = await pdf.getPage(i);\r\n        pagesData.push(page);\r\n        setLoadingProgress((i / pdf.numPages) * 100);\r\n      }\r\n\r\n      setPages(pagesData);\r\n      setIsLoading(false);\r\n\r\n      // Extract text from first few pages for AI context\r\n      await extractPDFText(pdf);\r\n\r\n      // Create PDF context for AI\r\n      const fileName = url.split('/').pop() || 'PDF Document';\r\n      setPdfContext({\r\n        title: fileName,\r\n        subject: 'Study Material',\r\n        totalPages: pdf.numPages,\r\n        extractedText: extractedText,\r\n        url: url\r\n      });\r\n\r\n      setShowAIButton(true);\r\n\r\n      // Load remaining pages in background\r\n      if (pdf.numPages > initialPagesToLoad) {\r\n        loadRemainingPages(pdf, initialPagesToLoad + 1, pagesData);\r\n      }\r\n\r\n    } catch (error) {\r\n      console.error(\"Error loading PDF:\", error);\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const loadRemainingPages = async (pdf, startPage, existingPages) => {\r\n    const updatedPages = [...existingPages];\r\n\r\n    for (let i = startPage; i <= pdf.numPages; i++) {\r\n      try {\r\n        const page = await pdf.getPage(i);\r\n        updatedPages.push(page);\r\n        setPages([...updatedPages]);\r\n        setLoadingProgress((i / pdf.numPages) * 100);\r\n\r\n        // Small delay to prevent blocking the UI\r\n        await new Promise(resolve => setTimeout(resolve, 50));\r\n      } catch (error) {\r\n        console.error(`Error loading page ${i}:`, error);\r\n      }\r\n    }\r\n  };\r\n\r\n  // Extract text from PDF for AI context\r\n  const extractPDFText = async (pdf) => {\r\n    try {\r\n      let fullText = '';\r\n      const maxPagesToExtract = Math.min(5, pdf.numPages); // Extract from first 5 pages\r\n\r\n      for (let i = 1; i <= maxPagesToExtract; i++) {\r\n        const page = await pdf.getPage(i);\r\n        const textContent = await page.getTextContent();\r\n        const pageText = textContent.items.map(item => item.str).join(' ');\r\n        fullText += `Page ${i}: ${pageText}\\n\\n`;\r\n      }\r\n\r\n      setExtractedText(fullText);\r\n      return fullText;\r\n    } catch (error) {\r\n      console.warn('Could not extract text from PDF:', error);\r\n      return '';\r\n    }\r\n  };\r\n\r\n  const renderPage = async (page, index) => {\r\n    const canvas = canvasRefs.current[index];\r\n    const textLayer = textLayerRefs.current[index];\r\n    if (!canvas || renderingRefs.current[index] || !containerRef.current) return;\r\n\r\n    try {\r\n      renderingRefs.current[index] = true;\r\n\r\n      const viewport = page.getViewport({ scale: 1.0 });\r\n      const containerWidth = containerRef.current.clientWidth;\r\n      const scale = containerWidth / viewport.width;\r\n      const scaledViewport = page.getViewport({ scale });\r\n\r\n      const context = canvas.getContext(\"2d\");\r\n      canvas.height = scaledViewport.height;\r\n      canvas.width = scaledViewport.width;\r\n      canvas.style.width = '100%';\r\n      canvas.style.height = 'auto';\r\n\r\n      const renderContext = {\r\n        canvasContext: context,\r\n        viewport: scaledViewport,\r\n      };\r\n\r\n      await page.render(renderContext).promise;\r\n\r\n      // Render text layer for selection\r\n      if (textLayer) {\r\n        textLayer.innerHTML = '';\r\n        textLayer.style.width = canvas.style.width;\r\n        textLayer.style.height = canvas.style.height;\r\n        textLayer.style.position = 'absolute';\r\n        textLayer.style.top = '0';\r\n        textLayer.style.left = '0';\r\n        textLayer.style.pointerEvents = 'auto';\r\n        textLayer.style.userSelect = 'text';\r\n\r\n        try {\r\n          const textContent = await page.getTextContent();\r\n          const textLayerDiv = textLayer;\r\n\r\n          // Simple text rendering for selection\r\n          textContent.items.forEach((item, itemIndex) => {\r\n            const textSpan = document.createElement('span');\r\n            textSpan.textContent = item.str;\r\n            textSpan.style.position = 'absolute';\r\n            textSpan.style.fontSize = `${item.height * scale}px`;\r\n            textSpan.style.left = `${item.transform[4] * scale}px`;\r\n            textSpan.style.top = `${scaledViewport.height - item.transform[5] * scale - item.height * scale}px`;\r\n            textSpan.style.fontFamily = item.fontName || 'sans-serif';\r\n            textSpan.style.color = 'transparent';\r\n            textSpan.style.userSelect = 'text';\r\n            textLayerDiv.appendChild(textSpan);\r\n          });\r\n        } catch (textError) {\r\n          console.warn(`Could not render text layer for page ${index + 1}:`, textError);\r\n        }\r\n      }\r\n\r\n      console.log(`Page ${index + 1} rendered`);\r\n    } catch (error) {\r\n      console.error(`Error rendering page ${index + 1}:`, error);\r\n    } finally {\r\n      renderingRefs.current[index] = false;\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (modalIsOpen && documentUrl) {\r\n      setPages([]);\r\n      setTotalPages(0);\r\n      setLoadingProgress(0);\r\n      setSelectedText('');\r\n      setShowCopyButton(false);\r\n      canvasRefs.current = [];\r\n      textLayerRefs.current = [];\r\n      renderingRefs.current = {};\r\n      renderPDF(documentUrl);\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [modalIsOpen, documentUrl]);\r\n\r\n  // Add event listener for text selection\r\n  useEffect(() => {\r\n    if (modalIsOpen) {\r\n      document.addEventListener('selectionchange', handleTextSelection);\r\n\r\n      // Listen for custom event to open Brainwave AI\r\n      const handleOpenBrainwaveAI = (event) => {\r\n        setShowBrainwaveAI(true);\r\n      };\r\n\r\n      window.addEventListener('openBrainwaveAI', handleOpenBrainwaveAI);\r\n\r\n      return () => {\r\n        document.removeEventListener('selectionchange', handleTextSelection);\r\n        window.removeEventListener('openBrainwaveAI', handleOpenBrainwaveAI);\r\n      };\r\n    }\r\n  }, [modalIsOpen]);\r\n\r\n  // Effect to render pages when they're loaded\r\n  useEffect(() => {\r\n    if (pages.length > 0 && containerRef.current) {\r\n      pages.forEach((page, index) => {\r\n        renderPage(page, index);\r\n      });\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [pages]);\r\n\r\n  // Re-render pages when window is resized\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      if (pages.length > 0) {\r\n        pages.forEach((page, index) => {\r\n          renderPage(page, index);\r\n        });\r\n      }\r\n    };\r\n\r\n    window.addEventListener('resize', handleResize);\r\n    return () => window.removeEventListener('resize', handleResize);\r\n  }, [pages]);\r\n\r\n  return (\r\n    <ReactModal\r\n      isOpen={modalIsOpen}\r\n      onRequestClose={closeModal}\r\n      contentLabel=\"Document Preview\"\r\n      style={{\r\n        overlay: {\r\n          backgroundColor: 'rgba(0, 0, 0, 0.75)'\r\n        },\r\n        content: {\r\n          top: '50%',\r\n          left: '50%',\r\n          right: 'auto',\r\n          bottom: 'auto',\r\n          marginRight: '-50%',\r\n          transform: 'translate(-50%, -50%)',\r\n          width: '70%',\r\n          height: '90%',\r\n          padding: '20px',\r\n          borderRadius: '10px',\r\n          overflow: 'hidden',\r\n        },\r\n      }}\r\n    >\r\n      <button\r\n        onClick={closeModal}\r\n        style={{\r\n          position: \"absolute\",\r\n          top: \"10px\",\r\n          right: \"10px\",\r\n          background: \"transparent\",\r\n          border: \"none\",\r\n          fontSize: \"20px\",\r\n          cursor: \"pointer\",\r\n          zIndex: 1,\r\n        }}\r\n      >\r\n        X\r\n      </button>\r\n\r\n      <div\r\n        ref={containerRef}\r\n        style={{\r\n          height: '100%',\r\n          overflow: 'auto',\r\n          padding: '10px',\r\n          scrollbarWidth: 'thin'\r\n        }}\r\n      >\r\n        {isLoading && (\r\n          <div style={{\r\n            display: 'flex',\r\n            flexDirection: 'column',\r\n            alignItems: 'center',\r\n            justifyContent: 'center',\r\n            height: '200px',\r\n            color: '#666'\r\n          }}>\r\n            <div style={{\r\n              width: '50px',\r\n              height: '50px',\r\n              border: '3px solid #f3f3f3',\r\n              borderTop: '3px solid #3498db',\r\n              borderRadius: '50%',\r\n              animation: 'spin 1s linear infinite',\r\n              marginBottom: '20px'\r\n            }}></div>\r\n            <p>Loading PDF...</p>\r\n            <div style={{\r\n              width: '200px',\r\n              height: '6px',\r\n              backgroundColor: '#f3f3f3',\r\n              borderRadius: '3px',\r\n              overflow: 'hidden',\r\n              marginTop: '10px'\r\n            }}>\r\n              <div style={{\r\n                width: `${loadingProgress}%`,\r\n                height: '100%',\r\n                backgroundColor: '#3498db',\r\n                transition: 'width 0.3s ease'\r\n              }}></div>\r\n            </div>\r\n            <small style={{ marginTop: '5px' }}>\r\n              {Math.round(loadingProgress)}% loaded\r\n            </small>\r\n          </div>\r\n        )}\r\n\r\n        {/* Copy Button */}\r\n        {showCopyButton && (\r\n          <div style={{\r\n            position: 'fixed',\r\n            top: '20px',\r\n            right: '20px',\r\n            zIndex: 1000,\r\n            background: '#007BFF',\r\n            color: 'white',\r\n            padding: '10px 15px',\r\n            borderRadius: '5px',\r\n            cursor: 'pointer',\r\n            boxShadow: '0 2px 10px rgba(0,0,0,0.3)',\r\n            fontSize: '14px',\r\n            fontWeight: 'bold'\r\n          }} onClick={copyToClipboard}>\r\n            📋 {isKiswahili ? 'Nakili Maandishi' : 'Copy Selected Text'}\r\n          </div>\r\n        )}\r\n\r\n        {/* Quick Ask AI Button */}\r\n        {showAIButton && (\r\n          <div style={{\r\n            position: 'fixed',\r\n            bottom: '20px',\r\n            right: '20px',\r\n            zIndex: 1000,\r\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n            color: 'white',\r\n            padding: '12px 18px',\r\n            borderRadius: '25px',\r\n            cursor: 'pointer',\r\n            boxShadow: '0 4px 15px rgba(102, 126, 234, 0.4)',\r\n            fontSize: '14px',\r\n            fontWeight: 'bold',\r\n            display: 'flex',\r\n            alignItems: 'center',\r\n            gap: '8px',\r\n            transition: 'all 0.3s ease'\r\n          }}\r\n          onClick={() => {\r\n            // This will be handled by the FloatingBrainwaveAI component\r\n            const event = new CustomEvent('openBrainwaveAI', {\r\n              detail: { pdfContext, selectedText }\r\n            });\r\n            window.dispatchEvent(event);\r\n          }}\r\n          onMouseEnter={(e) => {\r\n            e.target.style.transform = 'translateY(-2px)';\r\n            e.target.style.boxShadow = '0 6px 20px rgba(102, 126, 234, 0.6)';\r\n          }}\r\n          onMouseLeave={(e) => {\r\n            e.target.style.transform = 'translateY(0)';\r\n            e.target.style.boxShadow = '0 4px 15px rgba(102, 126, 234, 0.4)';\r\n          }}>\r\n            🤖 {isKiswahili ? 'Uliza AI kuhusu PDF' : 'Ask AI about PDF'}\r\n          </div>\r\n        )}\r\n\r\n        {pages.map((page, index) => (\r\n          <div\r\n            key={index}\r\n            style={{\r\n              marginBottom: '10px',\r\n              display: 'flex',\r\n              flexDirection: 'column',\r\n              alignItems: 'center',\r\n              position: 'relative'\r\n            }}\r\n          >\r\n            <div style={{ position: 'relative', display: 'inline-block' }}>\r\n              <canvas\r\n                ref={element => {\r\n                  canvasRefs.current[index] = element;\r\n                }}\r\n                style={{\r\n                  maxWidth: '100%',\r\n                  height: 'auto',\r\n                  border: '1px solid black',\r\n                  display: 'block'\r\n                }}\r\n              />\r\n              <div\r\n                ref={element => {\r\n                  textLayerRefs.current[index] = element;\r\n                }}\r\n                style={{\r\n                  position: 'absolute',\r\n                  top: 0,\r\n                  left: 0,\r\n                  width: '100%',\r\n                  height: '100%',\r\n                  pointerEvents: 'auto',\r\n                  userSelect: 'text',\r\n                  cursor: 'text'\r\n                }}\r\n              />\r\n            </div>\r\n          </div>\r\n        ))}\r\n\r\n        {totalPages > pages.length && !isLoading && (\r\n          <div style={{\r\n            textAlign: 'center',\r\n            padding: '20px',\r\n            color: '#666',\r\n            fontStyle: 'italic'\r\n          }}>\r\n            Loading remaining pages... ({pages.length}/{totalPages})\r\n          </div>\r\n        )}\r\n      </div>\r\n    </ReactModal>\r\n  );\r\n};\r\n\r\nexport default PDFModal;"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,OAAO,KAAKC,QAAQ,MAAM,YAAY;AACtC,OAAOC,UAAU,MAAM,aAAa;AACpC,OAAOC,mBAAmB,MAAM,yCAAyC;AACzE,SAASC,WAAW,QAAQ,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAChEJ,UAAU,CAACK,aAAa,CAAC,OAAO,CAAC;AAEjCN,QAAQ,CAACO,mBAAmB,CAACC,SAAS,GAAI,+DAA8D;;AAExG;AACA,MAAMC,YAAY,GAAI;AACtB;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;EACnC,MAAMC,KAAK,GAAGD,QAAQ,CAACE,aAAa,CAAC,OAAO,CAAC;EAC7CD,KAAK,CAACE,WAAW,GAAGJ,YAAY;EAChCC,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACJ,KAAK,CAAC;AAClC;AAEA,MAAMK,QAAQ,GAAGA,CAAC;EAAEC,WAAW;EAAEC,UAAU;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EAC7D,MAAM;IAAEC;EAAY,CAAC,GAAGlB,WAAW,CAAC,CAAC;EACrC,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC2B,eAAe,EAAEC,kBAAkB,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC;EACzD,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiC,cAAc,EAAEC,iBAAiB,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACqC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACuC,YAAY,EAAEC,eAAe,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACyC,eAAe,EAAEC,kBAAkB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM2C,UAAU,GAAG5C,MAAM,CAAC,EAAE,CAAC;EAC7B,MAAM6C,aAAa,GAAG7C,MAAM,CAAC,EAAE,CAAC;EAChC,MAAM8C,YAAY,GAAG9C,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM+C,aAAa,GAAG/C,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAElC;EACA,MAAMgD,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,SAAS,GAAGC,MAAM,CAACC,YAAY,CAAC,CAAC;IACvC,MAAMC,IAAI,GAAGH,SAAS,CAACI,QAAQ,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;IAExC,IAAIF,IAAI,EAAE;MACRnB,eAAe,CAACmB,IAAI,CAAC;MACrBjB,iBAAiB,CAAC,IAAI,CAAC;IACzB,CAAC,MAAM;MACLF,eAAe,CAAC,EAAE,CAAC;MACnBE,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;;EAED;EACA,MAAMoB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAIvB,YAAY,EAAE;MAChB,IAAI;QACF,MAAMwB,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC1B,YAAY,CAAC;QACjD2B,KAAK,CAAC,2BAA2B,CAAC;QAClCxB,iBAAiB,CAAC,KAAK,CAAC;QACxBe,MAAM,CAACC,YAAY,CAAC,CAAC,CAACS,eAAe,CAAC,CAAC;MACzC,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZ;QACA,MAAMC,QAAQ,GAAGlD,QAAQ,CAACE,aAAa,CAAC,UAAU,CAAC;QACnDgD,QAAQ,CAACC,KAAK,GAAG/B,YAAY;QAC7BpB,QAAQ,CAACoD,IAAI,CAAC/C,WAAW,CAAC6C,QAAQ,CAAC;QACnCA,QAAQ,CAACG,MAAM,CAAC,CAAC;QACjBrD,QAAQ,CAACsD,WAAW,CAAC,MAAM,CAAC;QAC5BtD,QAAQ,CAACoD,IAAI,CAACG,WAAW,CAACL,QAAQ,CAAC;QACnCH,KAAK,CAAC,2BAA2B,CAAC;QAClCxB,iBAAiB,CAAC,KAAK,CAAC;QACxBe,MAAM,CAACC,YAAY,CAAC,CAAC,CAACS,eAAe,CAAC,CAAC;MACzC;IACF;EACF,CAAC;EAED,MAAMQ,SAAS,GAAG,MAAOC,GAAG,IAAK;IAC/B,IAAI;MACF1C,YAAY,CAAC,IAAI,CAAC;MAClBE,kBAAkB,CAAC,CAAC,CAAC;MAErB,MAAMyC,GAAG,GAAG,MAAMpE,QAAQ,CAACqE,WAAW,CAACF,GAAG,CAAC,CAACG,OAAO;MACnDC,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;MAEzB3C,aAAa,CAACuC,GAAG,CAACK,QAAQ,CAAC;;MAE3B;MACA,MAAMC,kBAAkB,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAER,GAAG,CAACK,QAAQ,CAAC;MACpD,MAAMI,SAAS,GAAG,EAAE;MAEpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIJ,kBAAkB,EAAEI,CAAC,EAAE,EAAE;QAC5C,MAAMC,IAAI,GAAG,MAAMX,GAAG,CAACY,OAAO,CAACF,CAAC,CAAC;QACjCD,SAAS,CAACI,IAAI,CAACF,IAAI,CAAC;QACpBpD,kBAAkB,CAAEmD,CAAC,GAAGV,GAAG,CAACK,QAAQ,GAAI,GAAG,CAAC;MAC9C;MAEAlD,QAAQ,CAACsD,SAAS,CAAC;MACnBpD,YAAY,CAAC,KAAK,CAAC;;MAEnB;MACA,MAAMyD,cAAc,CAACd,GAAG,CAAC;;MAEzB;MACA,MAAMe,QAAQ,GAAGhB,GAAG,CAACiB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,IAAI,cAAc;MACvDlD,aAAa,CAAC;QACZmD,KAAK,EAAEH,QAAQ;QACfI,OAAO,EAAE,gBAAgB;QACzB3D,UAAU,EAAEwC,GAAG,CAACK,QAAQ;QACxBrC,aAAa,EAAEA,aAAa;QAC5B+B,GAAG,EAAEA;MACP,CAAC,CAAC;MAEF5B,eAAe,CAAC,IAAI,CAAC;;MAErB;MACA,IAAI6B,GAAG,CAACK,QAAQ,GAAGC,kBAAkB,EAAE;QACrCc,kBAAkB,CAACpB,GAAG,EAAEM,kBAAkB,GAAG,CAAC,EAAEG,SAAS,CAAC;MAC5D;IAEF,CAAC,CAAC,OAAOY,KAAK,EAAE;MACdlB,OAAO,CAACkB,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1ChE,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM+D,kBAAkB,GAAG,MAAAA,CAAOpB,GAAG,EAAEsB,SAAS,EAAEC,aAAa,KAAK;IAClE,MAAMC,YAAY,GAAG,CAAC,GAAGD,aAAa,CAAC;IAEvC,KAAK,IAAIb,CAAC,GAAGY,SAAS,EAAEZ,CAAC,IAAIV,GAAG,CAACK,QAAQ,EAAEK,CAAC,EAAE,EAAE;MAC9C,IAAI;QACF,MAAMC,IAAI,GAAG,MAAMX,GAAG,CAACY,OAAO,CAACF,CAAC,CAAC;QACjCc,YAAY,CAACX,IAAI,CAACF,IAAI,CAAC;QACvBxD,QAAQ,CAAC,CAAC,GAAGqE,YAAY,CAAC,CAAC;QAC3BjE,kBAAkB,CAAEmD,CAAC,GAAGV,GAAG,CAACK,QAAQ,GAAI,GAAG,CAAC;;QAE5C;QACA,MAAM,IAAIoB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,EAAE,CAAC,CAAC;MACvD,CAAC,CAAC,OAAOL,KAAK,EAAE;QACdlB,OAAO,CAACkB,KAAK,CAAE,sBAAqBX,CAAE,GAAE,EAAEW,KAAK,CAAC;MAClD;IACF;EACF,CAAC;;EAED;EACA,MAAMP,cAAc,GAAG,MAAOd,GAAG,IAAK;IACpC,IAAI;MACF,IAAI4B,QAAQ,GAAG,EAAE;MACjB,MAAMC,iBAAiB,GAAGtB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAER,GAAG,CAACK,QAAQ,CAAC,CAAC,CAAC;;MAErD,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAImB,iBAAiB,EAAEnB,CAAC,EAAE,EAAE;QAC3C,MAAMC,IAAI,GAAG,MAAMX,GAAG,CAACY,OAAO,CAACF,CAAC,CAAC;QACjC,MAAMjE,WAAW,GAAG,MAAMkE,IAAI,CAACmB,cAAc,CAAC,CAAC;QAC/C,MAAMC,QAAQ,GAAGtF,WAAW,CAACuF,KAAK,CAACC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;QAClER,QAAQ,IAAK,QAAOlB,CAAE,KAAIqB,QAAS,MAAK;MAC1C;MAEA9D,gBAAgB,CAAC2D,QAAQ,CAAC;MAC1B,OAAOA,QAAQ;IACjB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdlB,OAAO,CAACkC,IAAI,CAAC,kCAAkC,EAAEhB,KAAK,CAAC;MACvD,OAAO,EAAE;IACX;EACF,CAAC;EAED,MAAMiB,UAAU,GAAG,MAAAA,CAAO3B,IAAI,EAAE4B,KAAK,KAAK;IACxC,MAAMC,MAAM,GAAGlE,UAAU,CAACmE,OAAO,CAACF,KAAK,CAAC;IACxC,MAAMG,SAAS,GAAGnE,aAAa,CAACkE,OAAO,CAACF,KAAK,CAAC;IAC9C,IAAI,CAACC,MAAM,IAAI/D,aAAa,CAACgE,OAAO,CAACF,KAAK,CAAC,IAAI,CAAC/D,YAAY,CAACiE,OAAO,EAAE;IAEtE,IAAI;MACFhE,aAAa,CAACgE,OAAO,CAACF,KAAK,CAAC,GAAG,IAAI;MAEnC,MAAMI,QAAQ,GAAGhC,IAAI,CAACiC,WAAW,CAAC;QAAEC,KAAK,EAAE;MAAI,CAAC,CAAC;MACjD,MAAMC,cAAc,GAAGtE,YAAY,CAACiE,OAAO,CAACM,WAAW;MACvD,MAAMF,KAAK,GAAGC,cAAc,GAAGH,QAAQ,CAACK,KAAK;MAC7C,MAAMC,cAAc,GAAGtC,IAAI,CAACiC,WAAW,CAAC;QAAEC;MAAM,CAAC,CAAC;MAElD,MAAMK,OAAO,GAAGV,MAAM,CAACW,UAAU,CAAC,IAAI,CAAC;MACvCX,MAAM,CAACY,MAAM,GAAGH,cAAc,CAACG,MAAM;MACrCZ,MAAM,CAACQ,KAAK,GAAGC,cAAc,CAACD,KAAK;MACnCR,MAAM,CAACjG,KAAK,CAACyG,KAAK,GAAG,MAAM;MAC3BR,MAAM,CAACjG,KAAK,CAAC6G,MAAM,GAAG,MAAM;MAE5B,MAAMC,aAAa,GAAG;QACpBC,aAAa,EAAEJ,OAAO;QACtBP,QAAQ,EAAEM;MACZ,CAAC;MAED,MAAMtC,IAAI,CAAC4C,MAAM,CAACF,aAAa,CAAC,CAACnD,OAAO;;MAExC;MACA,IAAIwC,SAAS,EAAE;QACbA,SAAS,CAACc,SAAS,GAAG,EAAE;QACxBd,SAAS,CAACnG,KAAK,CAACyG,KAAK,GAAGR,MAAM,CAACjG,KAAK,CAACyG,KAAK;QAC1CN,SAAS,CAACnG,KAAK,CAAC6G,MAAM,GAAGZ,MAAM,CAACjG,KAAK,CAAC6G,MAAM;QAC5CV,SAAS,CAACnG,KAAK,CAACkH,QAAQ,GAAG,UAAU;QACrCf,SAAS,CAACnG,KAAK,CAACmH,GAAG,GAAG,GAAG;QACzBhB,SAAS,CAACnG,KAAK,CAACoH,IAAI,GAAG,GAAG;QAC1BjB,SAAS,CAACnG,KAAK,CAACqH,aAAa,GAAG,MAAM;QACtClB,SAAS,CAACnG,KAAK,CAACsH,UAAU,GAAG,MAAM;QAEnC,IAAI;UACF,MAAMpH,WAAW,GAAG,MAAMkE,IAAI,CAACmB,cAAc,CAAC,CAAC;UAC/C,MAAMgC,YAAY,GAAGpB,SAAS;;UAE9B;UACAjG,WAAW,CAACuF,KAAK,CAAC+B,OAAO,CAAC,CAAC7B,IAAI,EAAE8B,SAAS,KAAK;YAC7C,MAAMC,QAAQ,GAAG3H,QAAQ,CAACE,aAAa,CAAC,MAAM,CAAC;YAC/CyH,QAAQ,CAACxH,WAAW,GAAGyF,IAAI,CAACC,GAAG;YAC/B8B,QAAQ,CAAC1H,KAAK,CAACkH,QAAQ,GAAG,UAAU;YACpCQ,QAAQ,CAAC1H,KAAK,CAAC2H,QAAQ,GAAI,GAAEhC,IAAI,CAACkB,MAAM,GAAGP,KAAM,IAAG;YACpDoB,QAAQ,CAAC1H,KAAK,CAACoH,IAAI,GAAI,GAAEzB,IAAI,CAACiC,SAAS,CAAC,CAAC,CAAC,GAAGtB,KAAM,IAAG;YACtDoB,QAAQ,CAAC1H,KAAK,CAACmH,GAAG,GAAI,GAAET,cAAc,CAACG,MAAM,GAAGlB,IAAI,CAACiC,SAAS,CAAC,CAAC,CAAC,GAAGtB,KAAK,GAAGX,IAAI,CAACkB,MAAM,GAAGP,KAAM,IAAG;YACnGoB,QAAQ,CAAC1H,KAAK,CAAC6H,UAAU,GAAGlC,IAAI,CAACmC,QAAQ,IAAI,YAAY;YACzDJ,QAAQ,CAAC1H,KAAK,CAAC+H,KAAK,GAAG,aAAa;YACpCL,QAAQ,CAAC1H,KAAK,CAACsH,UAAU,GAAG,MAAM;YAClCC,YAAY,CAACnH,WAAW,CAACsH,QAAQ,CAAC;UACpC,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOM,SAAS,EAAE;UAClBpE,OAAO,CAACkC,IAAI,CAAE,wCAAuCE,KAAK,GAAG,CAAE,GAAE,EAAEgC,SAAS,CAAC;QAC/E;MACF;MAEApE,OAAO,CAACC,GAAG,CAAE,QAAOmC,KAAK,GAAG,CAAE,WAAU,CAAC;IAC3C,CAAC,CAAC,OAAOlB,KAAK,EAAE;MACdlB,OAAO,CAACkB,KAAK,CAAE,wBAAuBkB,KAAK,GAAG,CAAE,GAAE,EAAElB,KAAK,CAAC;IAC5D,CAAC,SAAS;MACR5C,aAAa,CAACgE,OAAO,CAACF,KAAK,CAAC,GAAG,KAAK;IACtC;EACF,CAAC;EAED9G,SAAS,CAAC,MAAM;IACd,IAAIoB,WAAW,IAAIE,WAAW,EAAE;MAC9BI,QAAQ,CAAC,EAAE,CAAC;MACZM,aAAa,CAAC,CAAC,CAAC;MAChBF,kBAAkB,CAAC,CAAC,CAAC;MACrBI,eAAe,CAAC,EAAE,CAAC;MACnBE,iBAAiB,CAAC,KAAK,CAAC;MACxBS,UAAU,CAACmE,OAAO,GAAG,EAAE;MACvBlE,aAAa,CAACkE,OAAO,GAAG,EAAE;MAC1BhE,aAAa,CAACgE,OAAO,GAAG,CAAC,CAAC;MAC1B3C,SAAS,CAAC/C,WAAW,CAAC;IACxB;IACA;EACF,CAAC,EAAE,CAACF,WAAW,EAAEE,WAAW,CAAC,CAAC;;EAE9B;EACAtB,SAAS,CAAC,MAAM;IACd,IAAIoB,WAAW,EAAE;MACfP,QAAQ,CAACkI,gBAAgB,CAAC,iBAAiB,EAAE9F,mBAAmB,CAAC;;MAEjE;MACA,MAAM+F,qBAAqB,GAAIC,KAAK,IAAK;QACvCrG,kBAAkB,CAAC,IAAI,CAAC;MAC1B,CAAC;MAEDO,MAAM,CAAC4F,gBAAgB,CAAC,iBAAiB,EAAEC,qBAAqB,CAAC;MAEjE,OAAO,MAAM;QACXnI,QAAQ,CAACqI,mBAAmB,CAAC,iBAAiB,EAAEjG,mBAAmB,CAAC;QACpEE,MAAM,CAAC+F,mBAAmB,CAAC,iBAAiB,EAAEF,qBAAqB,CAAC;MACtE,CAAC;IACH;EACF,CAAC,EAAE,CAAC5H,WAAW,CAAC,CAAC;;EAEjB;EACApB,SAAS,CAAC,MAAM;IACd,IAAIyB,KAAK,CAAC0H,MAAM,GAAG,CAAC,IAAIpG,YAAY,CAACiE,OAAO,EAAE;MAC5CvF,KAAK,CAAC6G,OAAO,CAAC,CAACpD,IAAI,EAAE4B,KAAK,KAAK;QAC7BD,UAAU,CAAC3B,IAAI,EAAE4B,KAAK,CAAC;MACzB,CAAC,CAAC;IACJ;IACA;EACF,CAAC,EAAE,CAACrF,KAAK,CAAC,CAAC;;EAEX;EACAzB,SAAS,CAAC,MAAM;IACd,MAAMoJ,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAI3H,KAAK,CAAC0H,MAAM,GAAG,CAAC,EAAE;QACpB1H,KAAK,CAAC6G,OAAO,CAAC,CAACpD,IAAI,EAAE4B,KAAK,KAAK;UAC7BD,UAAU,CAAC3B,IAAI,EAAE4B,KAAK,CAAC;QACzB,CAAC,CAAC;MACJ;IACF,CAAC;IAED3D,MAAM,CAAC4F,gBAAgB,CAAC,QAAQ,EAAEK,YAAY,CAAC;IAC/C,OAAO,MAAMjG,MAAM,CAAC+F,mBAAmB,CAAC,QAAQ,EAAEE,YAAY,CAAC;EACjE,CAAC,EAAE,CAAC3H,KAAK,CAAC,CAAC;EAEX,oBACEjB,OAAA,CAACJ,UAAU;IACTiJ,MAAM,EAAEjI,WAAY;IACpBkI,cAAc,EAAEjI,UAAW;IAC3BkI,YAAY,EAAC,kBAAkB;IAC/BzI,KAAK,EAAE;MACL0I,OAAO,EAAE;QACPC,eAAe,EAAE;MACnB,CAAC;MACDC,OAAO,EAAE;QACPzB,GAAG,EAAE,KAAK;QACVC,IAAI,EAAE,KAAK;QACXyB,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdC,WAAW,EAAE,MAAM;QACnBnB,SAAS,EAAE,uBAAuB;QAClCnB,KAAK,EAAE,KAAK;QACZI,MAAM,EAAE,KAAK;QACbmC,OAAO,EAAE,MAAM;QACfC,YAAY,EAAE,MAAM;QACpBC,QAAQ,EAAE;MACZ;IACF,CAAE;IAAAC,QAAA,gBAEFzJ,OAAA;MACE0J,OAAO,EAAE7I,UAAW;MACpBP,KAAK,EAAE;QACLkH,QAAQ,EAAE,UAAU;QACpBC,GAAG,EAAE,MAAM;QACX0B,KAAK,EAAE,MAAM;QACbQ,UAAU,EAAE,aAAa;QACzBC,MAAM,EAAE,MAAM;QACd3B,QAAQ,EAAE,MAAM;QAChB4B,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE;MACV,CAAE;MAAAL,QAAA,EACH;IAED;MAAA3E,QAAA,EAAAiF,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAETjK,OAAA;MACEkK,GAAG,EAAE3H,YAAa;MAClBjC,KAAK,EAAE;QACL6G,MAAM,EAAE,MAAM;QACdqC,QAAQ,EAAE,MAAM;QAChBF,OAAO,EAAE,MAAM;QACfa,cAAc,EAAE;MAClB,CAAE;MAAAV,QAAA,GAEDtI,SAAS,iBACRnB,OAAA;QAAKM,KAAK,EAAE;UACV8J,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBpD,MAAM,EAAE,OAAO;UACfkB,KAAK,EAAE;QACT,CAAE;QAAAoB,QAAA,gBACAzJ,OAAA;UAAKM,KAAK,EAAE;YACVyG,KAAK,EAAE,MAAM;YACbI,MAAM,EAAE,MAAM;YACdyC,MAAM,EAAE,mBAAmB;YAC3BY,SAAS,EAAE,mBAAmB;YAC9BjB,YAAY,EAAE,KAAK;YACnBkB,SAAS,EAAE,yBAAyB;YACpCC,YAAY,EAAE;UAChB;QAAE;UAAA5F,QAAA,EAAAiF,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACTjK,OAAA;UAAAyJ,QAAA,EAAG;QAAc;UAAA3E,QAAA,EAAAiF,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACrBjK,OAAA;UAAKM,KAAK,EAAE;YACVyG,KAAK,EAAE,OAAO;YACdI,MAAM,EAAE,KAAK;YACb8B,eAAe,EAAE,SAAS;YAC1BM,YAAY,EAAE,KAAK;YACnBC,QAAQ,EAAE,QAAQ;YAClBmB,SAAS,EAAE;UACb,CAAE;UAAAlB,QAAA,eACAzJ,OAAA;YAAKM,KAAK,EAAE;cACVyG,KAAK,EAAG,GAAE1F,eAAgB,GAAE;cAC5B8F,MAAM,EAAE,MAAM;cACd8B,eAAe,EAAE,SAAS;cAC1B2B,UAAU,EAAE;YACd;UAAE;YAAA9F,QAAA,EAAAiF,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAnF,QAAA,EAAAiF,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNjK,OAAA;UAAOM,KAAK,EAAE;YAAEqK,SAAS,EAAE;UAAM,CAAE;UAAAlB,QAAA,GAChCnF,IAAI,CAACuG,KAAK,CAACxJ,eAAe,CAAC,EAAC,UAC/B;QAAA;UAAAyD,QAAA,EAAAiF,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAnF,QAAA,EAAAiF,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACN,EAGAtI,cAAc,iBACb3B,OAAA;QAAKM,KAAK,EAAE;UACVkH,QAAQ,EAAE,OAAO;UACjBC,GAAG,EAAE,MAAM;UACX0B,KAAK,EAAE,MAAM;UACbW,MAAM,EAAE,IAAI;UACZH,UAAU,EAAE,SAAS;UACrBtB,KAAK,EAAE,OAAO;UACdiB,OAAO,EAAE,WAAW;UACpBC,YAAY,EAAE,KAAK;UACnBM,MAAM,EAAE,SAAS;UACjBiB,SAAS,EAAE,4BAA4B;UACvC7C,QAAQ,EAAE,MAAM;UAChB8C,UAAU,EAAE;QACd,CAAE;QAACrB,OAAO,EAAE1G,eAAgB;QAAAyG,QAAA,GAAC,eACxB,EAACzI,WAAW,GAAG,kBAAkB,GAAG,oBAAoB;MAAA;QAAA8D,QAAA,EAAAiF,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CACN,EAGAhI,YAAY,iBACXjC,OAAA;QAAKM,KAAK,EAAE;UACVkH,QAAQ,EAAE,OAAO;UACjB4B,MAAM,EAAE,MAAM;UACdD,KAAK,EAAE,MAAM;UACbW,MAAM,EAAE,IAAI;UACZH,UAAU,EAAE,mDAAmD;UAC/DtB,KAAK,EAAE,OAAO;UACdiB,OAAO,EAAE,WAAW;UACpBC,YAAY,EAAE,MAAM;UACpBM,MAAM,EAAE,SAAS;UACjBiB,SAAS,EAAE,qCAAqC;UAChD7C,QAAQ,EAAE,MAAM;UAChB8C,UAAU,EAAE,MAAM;UAClBX,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBU,GAAG,EAAE,KAAK;UACVJ,UAAU,EAAE;QACd,CAAE;QACFlB,OAAO,EAAEA,CAAA,KAAM;UACb;UACA,MAAMjB,KAAK,GAAG,IAAIwC,WAAW,CAAC,iBAAiB,EAAE;YAC/CC,MAAM,EAAE;cAAErJ,UAAU;cAAEJ;YAAa;UACrC,CAAC,CAAC;UACFkB,MAAM,CAACwI,aAAa,CAAC1C,KAAK,CAAC;QAC7B,CAAE;QACF2C,YAAY,EAAGC,CAAC,IAAK;UACnBA,CAAC,CAACC,MAAM,CAAChL,KAAK,CAAC4H,SAAS,GAAG,kBAAkB;UAC7CmD,CAAC,CAACC,MAAM,CAAChL,KAAK,CAACwK,SAAS,GAAG,qCAAqC;QAClE,CAAE;QACFS,YAAY,EAAGF,CAAC,IAAK;UACnBA,CAAC,CAACC,MAAM,CAAChL,KAAK,CAAC4H,SAAS,GAAG,eAAe;UAC1CmD,CAAC,CAACC,MAAM,CAAChL,KAAK,CAACwK,SAAS,GAAG,qCAAqC;QAClE,CAAE;QAAArB,QAAA,GAAC,eACE,EAACzI,WAAW,GAAG,qBAAqB,GAAG,kBAAkB;MAAA;QAAA8D,QAAA,EAAAiF,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CACN,EAEAhJ,KAAK,CAAC+E,GAAG,CAAC,CAACtB,IAAI,EAAE4B,KAAK,kBACrBtG,OAAA;QAEEM,KAAK,EAAE;UACLoK,YAAY,EAAE,MAAM;UACpBN,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBC,UAAU,EAAE,QAAQ;UACpB9C,QAAQ,EAAE;QACZ,CAAE;QAAAiC,QAAA,eAEFzJ,OAAA;UAAKM,KAAK,EAAE;YAAEkH,QAAQ,EAAE,UAAU;YAAE4C,OAAO,EAAE;UAAe,CAAE;UAAAX,QAAA,gBAC5DzJ,OAAA;YACEkK,GAAG,EAAEsB,OAAO,IAAI;cACdnJ,UAAU,CAACmE,OAAO,CAACF,KAAK,CAAC,GAAGkF,OAAO;YACrC,CAAE;YACFlL,KAAK,EAAE;cACLmL,QAAQ,EAAE,MAAM;cAChBtE,MAAM,EAAE,MAAM;cACdyC,MAAM,EAAE,iBAAiB;cACzBQ,OAAO,EAAE;YACX;UAAE;YAAAtF,QAAA,EAAAiF,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACFjK,OAAA;YACEkK,GAAG,EAAEsB,OAAO,IAAI;cACdlJ,aAAa,CAACkE,OAAO,CAACF,KAAK,CAAC,GAAGkF,OAAO;YACxC,CAAE;YACFlL,KAAK,EAAE;cACLkH,QAAQ,EAAE,UAAU;cACpBC,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPX,KAAK,EAAE,MAAM;cACbI,MAAM,EAAE,MAAM;cACdQ,aAAa,EAAE,MAAM;cACrBC,UAAU,EAAE,MAAM;cAClBiC,MAAM,EAAE;YACV;UAAE;YAAA/E,QAAA,EAAAiF,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAnF,QAAA,EAAAiF,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC,GApCD3D,KAAK;QAAAxB,QAAA,EAAAiF,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqCP,CACN,CAAC,EAED1I,UAAU,GAAGN,KAAK,CAAC0H,MAAM,IAAI,CAACxH,SAAS,iBACtCnB,OAAA;QAAKM,KAAK,EAAE;UACVoL,SAAS,EAAE,QAAQ;UACnBpC,OAAO,EAAE,MAAM;UACfjB,KAAK,EAAE,MAAM;UACbsD,SAAS,EAAE;QACb,CAAE;QAAAlC,QAAA,GAAC,8BAC2B,EAACxI,KAAK,CAAC0H,MAAM,EAAC,GAAC,EAACpH,UAAU,EAAC,GACzD;MAAA;QAAAuD,QAAA,EAAAiF,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN;IAAA;MAAAnF,QAAA,EAAAiF,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAnF,QAAA,EAAAiF,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEjB,CAAC;AAAClJ,EAAA,CAtdIJ,QAAQ;EAAA,QACYb,WAAW;AAAA;AAAA8L,EAAA,GAD/BjL,QAAQ;AAwdd,eAAeA,QAAQ;AAAC,IAAAiL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}