{"ast": null, "code": "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n/**\n * Wrap of sub component which need use as Button capacity (like Icon component).\n *\n * This helps accessibility reader to tread as a interactive button to operation.\n */\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nconst inlineStyle = {\n  border: 0,\n  background: 'transparent',\n  padding: 0,\n  lineHeight: 'inherit',\n  display: 'inline-block'\n};\nconst TransButton = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const onKeyDown = event => {\n    const {\n      keyCode\n    } = event;\n    if (keyCode === KeyCode.ENTER) {\n      event.preventDefault();\n    }\n  };\n  const onKeyUp = event => {\n    const {\n      keyCode\n    } = event;\n    const {\n      onClick\n    } = props;\n    if (keyCode === KeyCode.ENTER && onClick) {\n      onClick();\n    }\n  };\n  const {\n      style,\n      noStyle,\n      disabled\n    } = props,\n    restProps = __rest(props, [\"style\", \"noStyle\", \"disabled\"]);\n  let mergedStyle = {};\n  if (!noStyle) {\n    mergedStyle = Object.assign({}, inlineStyle);\n  }\n  if (disabled) {\n    mergedStyle.pointerEvents = 'none';\n  }\n  mergedStyle = Object.assign(Object.assign({}, mergedStyle), style);\n  return /*#__PURE__*/React.createElement(\"div\", Object.assign({\n    role: \"button\",\n    tabIndex: 0,\n    ref: ref\n  }, restProps, {\n    onKeyDown: onKeyDown,\n    onKeyUp: onKeyUp,\n    style: mergedStyle\n  }));\n});\nexport default TransButton;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "KeyCode", "React", "inlineStyle", "border", "background", "padding", "lineHeight", "display", "TransButton", "forwardRef", "props", "ref", "onKeyDown", "event", "keyCode", "ENTER", "preventDefault", "onKeyUp", "onClick", "style", "noStyle", "disabled", "restProps", "mergedStyle", "assign", "pointerEvents", "createElement", "role", "tabIndex"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/_util/transButton.js"], "sourcesContent": ["var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\n/**\n * Wrap of sub component which need use as Button capacity (like Icon component).\n *\n * This helps accessibility reader to tread as a interactive button to operation.\n */\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nconst inlineStyle = {\n  border: 0,\n  background: 'transparent',\n  padding: 0,\n  lineHeight: 'inherit',\n  display: 'inline-block'\n};\nconst TransButton = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const onKeyDown = event => {\n    const {\n      keyCode\n    } = event;\n    if (keyCode === KeyCode.ENTER) {\n      event.preventDefault();\n    }\n  };\n  const onKeyUp = event => {\n    const {\n      keyCode\n    } = event;\n    const {\n      onClick\n    } = props;\n    if (keyCode === KeyCode.ENTER && onClick) {\n      onClick();\n    }\n  };\n  const {\n      style,\n      noStyle,\n      disabled\n    } = props,\n    restProps = __rest(props, [\"style\", \"noStyle\", \"disabled\"]);\n  let mergedStyle = {};\n  if (!noStyle) {\n    mergedStyle = Object.assign({}, inlineStyle);\n  }\n  if (disabled) {\n    mergedStyle.pointerEvents = 'none';\n  }\n  mergedStyle = Object.assign(Object.assign({}, mergedStyle), style);\n  return /*#__PURE__*/React.createElement(\"div\", Object.assign({\n    role: \"button\",\n    tabIndex: 0,\n    ref: ref\n  }, restProps, {\n    onKeyDown: onKeyDown,\n    onKeyUp: onKeyUp,\n    style: mergedStyle\n  }));\n});\nexport default TransButton;"], "mappings": "AAAA,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,OAAOW,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,MAAMC,WAAW,GAAG;EAClBC,MAAM,EAAE,CAAC;EACTC,UAAU,EAAE,aAAa;EACzBC,OAAO,EAAE,CAAC;EACVC,UAAU,EAAE,SAAS;EACrBC,OAAO,EAAE;AACX,CAAC;AACD,MAAMC,WAAW,GAAG,aAAaP,KAAK,CAACQ,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EAChE,MAAMC,SAAS,GAAGC,KAAK,IAAI;IACzB,MAAM;MACJC;IACF,CAAC,GAAGD,KAAK;IACT,IAAIC,OAAO,KAAKd,OAAO,CAACe,KAAK,EAAE;MAC7BF,KAAK,CAACG,cAAc,CAAC,CAAC;IACxB;EACF,CAAC;EACD,MAAMC,OAAO,GAAGJ,KAAK,IAAI;IACvB,MAAM;MACJC;IACF,CAAC,GAAGD,KAAK;IACT,MAAM;MACJK;IACF,CAAC,GAAGR,KAAK;IACT,IAAII,OAAO,KAAKd,OAAO,CAACe,KAAK,IAAIG,OAAO,EAAE;MACxCA,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EACD,MAAM;MACFC,KAAK;MACLC,OAAO;MACPC;IACF,CAAC,GAAGX,KAAK;IACTY,SAAS,GAAGpC,MAAM,CAACwB,KAAK,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;EAC7D,IAAIa,WAAW,GAAG,CAAC,CAAC;EACpB,IAAI,CAACH,OAAO,EAAE;IACZG,WAAW,GAAGhC,MAAM,CAACiC,MAAM,CAAC,CAAC,CAAC,EAAEtB,WAAW,CAAC;EAC9C;EACA,IAAImB,QAAQ,EAAE;IACZE,WAAW,CAACE,aAAa,GAAG,MAAM;EACpC;EACAF,WAAW,GAAGhC,MAAM,CAACiC,MAAM,CAACjC,MAAM,CAACiC,MAAM,CAAC,CAAC,CAAC,EAAED,WAAW,CAAC,EAAEJ,KAAK,CAAC;EAClE,OAAO,aAAalB,KAAK,CAACyB,aAAa,CAAC,KAAK,EAAEnC,MAAM,CAACiC,MAAM,CAAC;IAC3DG,IAAI,EAAE,QAAQ;IACdC,QAAQ,EAAE,CAAC;IACXjB,GAAG,EAAEA;EACP,CAAC,EAAEW,SAAS,EAAE;IACZV,SAAS,EAAEA,SAAS;IACpBK,OAAO,EAAEA,OAAO;IAChBE,KAAK,EAAEI;EACT,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,eAAef,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}