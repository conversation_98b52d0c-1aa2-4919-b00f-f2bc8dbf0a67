{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nvar _excluded = [\"defaultValue\", \"value\", \"onFocus\", \"onBlur\", \"onChange\", \"allowClear\", \"maxLength\", \"onCompositionStart\", \"onCompositionEnd\", \"suffix\", \"prefixCls\", \"classes\", \"showCount\", \"className\", \"style\", \"disabled\", \"hidden\", \"classNames\", \"styles\", \"onResize\"];\nimport clsx from 'classnames';\nimport { BaseInput } from 'rc-input';\nimport { fixControlledValue, resolveOnChange } from \"rc-input/es/utils/commonUtils\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport React, { useEffect, useImperativeHandle, useRef } from 'react';\nimport ResizableTextArea from \"./ResizableTextArea\";\nfunction fixEmojiLength(value, maxLength) {\n  return _toConsumableArray(value || '').slice(0, maxLength).join('');\n}\nfunction setTriggerValue(isCursorInEnd, preValue, triggerValue, maxLength) {\n  var newTriggerValue = triggerValue;\n  if (isCursorInEnd) {\n    // 光标在尾部，直接截断\n    newTriggerValue = fixEmojiLength(triggerValue, maxLength);\n  } else if (_toConsumableArray(preValue || '').length < triggerValue.length && _toConsumableArray(triggerValue || '').length > maxLength) {\n    // 光标在中间，如果最后的值超过最大值，则采用原先的值\n    newTriggerValue = preValue;\n  }\n  return newTriggerValue;\n}\nvar TextArea = /*#__PURE__*/React.forwardRef(function (_ref, ref) {\n  var _clsx;\n  var defaultValue = _ref.defaultValue,\n    customValue = _ref.value,\n    onFocus = _ref.onFocus,\n    onBlur = _ref.onBlur,\n    onChange = _ref.onChange,\n    allowClear = _ref.allowClear,\n    maxLength = _ref.maxLength,\n    onCompositionStart = _ref.onCompositionStart,\n    onCompositionEnd = _ref.onCompositionEnd,\n    suffix = _ref.suffix,\n    _ref$prefixCls = _ref.prefixCls,\n    prefixCls = _ref$prefixCls === void 0 ? 'rc-textarea' : _ref$prefixCls,\n    classes = _ref.classes,\n    showCount = _ref.showCount,\n    className = _ref.className,\n    style = _ref.style,\n    disabled = _ref.disabled,\n    hidden = _ref.hidden,\n    classNames = _ref.classNames,\n    styles = _ref.styles,\n    onResize = _ref.onResize,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var _useMergedState = useMergedState(defaultValue, {\n      value: customValue,\n      defaultValue: defaultValue\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    value = _useMergedState2[0],\n    setValue = _useMergedState2[1];\n  var resizableTextAreaRef = useRef(null);\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    focused = _React$useState2[0],\n    setFocused = _React$useState2[1];\n  var _React$useState3 = React.useState(false),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    compositing = _React$useState4[0],\n    setCompositing = _React$useState4[1];\n  var oldCompositionValueRef = React.useRef();\n  var oldSelectionStartRef = React.useRef(0);\n  var _React$useState5 = React.useState(null),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    textareaResized = _React$useState6[0],\n    setTextareaResized = _React$useState6[1];\n  var focus = function focus() {\n    var _resizableTextAreaRef;\n    (_resizableTextAreaRef = resizableTextAreaRef.current) === null || _resizableTextAreaRef === void 0 ? void 0 : _resizableTextAreaRef.textArea.focus();\n  };\n  useImperativeHandle(ref, function () {\n    return {\n      resizableTextArea: resizableTextAreaRef.current,\n      focus: focus,\n      blur: function blur() {\n        var _resizableTextAreaRef2;\n        (_resizableTextAreaRef2 = resizableTextAreaRef.current) === null || _resizableTextAreaRef2 === void 0 ? void 0 : _resizableTextAreaRef2.textArea.blur();\n      }\n    };\n  });\n  useEffect(function () {\n    setFocused(function (prev) {\n      return !disabled && prev;\n    });\n  }, [disabled]);\n\n  // =========================== Value Update ===========================\n  // Max length value\n  var hasMaxLength = Number(maxLength) > 0;\n  var onInternalCompositionStart = function onInternalCompositionStart(e) {\n    setCompositing(true);\n    // 拼音输入前保存一份旧值\n    oldCompositionValueRef.current = value;\n    // 保存旧的光标位置\n    oldSelectionStartRef.current = e.currentTarget.selectionStart;\n    onCompositionStart === null || onCompositionStart === void 0 ? void 0 : onCompositionStart(e);\n  };\n  var onInternalCompositionEnd = function onInternalCompositionEnd(e) {\n    setCompositing(false);\n    var triggerValue = e.currentTarget.value;\n    if (hasMaxLength) {\n      var _oldCompositionValueR;\n      var isCursorInEnd = oldSelectionStartRef.current >= maxLength + 1 || oldSelectionStartRef.current === ((_oldCompositionValueR = oldCompositionValueRef.current) === null || _oldCompositionValueR === void 0 ? void 0 : _oldCompositionValueR.length);\n      triggerValue = setTriggerValue(isCursorInEnd, oldCompositionValueRef.current, triggerValue, maxLength);\n    }\n    // Patch composition onChange when value changed\n    if (triggerValue !== value) {\n      setValue(triggerValue);\n      resolveOnChange(e.currentTarget, e, onChange, triggerValue);\n    }\n    onCompositionEnd === null || onCompositionEnd === void 0 ? void 0 : onCompositionEnd(e);\n  };\n  var handleChange = function handleChange(e) {\n    var triggerValue = e.target.value;\n    if (!compositing && hasMaxLength) {\n      // 1. 复制粘贴超过maxlength的情况 2.未超过maxlength的情况\n      var isCursorInEnd = e.target.selectionStart >= maxLength + 1 || e.target.selectionStart === triggerValue.length || !e.target.selectionStart;\n      triggerValue = setTriggerValue(isCursorInEnd, value, triggerValue, maxLength);\n    }\n    setValue(triggerValue);\n    resolveOnChange(e.currentTarget, e, onChange, triggerValue);\n  };\n  var handleKeyDown = function handleKeyDown(e) {\n    var onPressEnter = rest.onPressEnter,\n      onKeyDown = rest.onKeyDown;\n    if (e.key === 'Enter' && onPressEnter) {\n      onPressEnter(e);\n    }\n    onKeyDown === null || onKeyDown === void 0 ? void 0 : onKeyDown(e);\n  };\n  var handleFocus = function handleFocus(e) {\n    setFocused(true);\n    onFocus === null || onFocus === void 0 ? void 0 : onFocus(e);\n  };\n  var handleBlur = function handleBlur(e) {\n    setFocused(false);\n    onBlur === null || onBlur === void 0 ? void 0 : onBlur(e);\n  };\n\n  // ============================== Reset ===============================\n  var handleReset = function handleReset(e) {\n    var _resizableTextAreaRef3;\n    setValue('');\n    focus();\n    resolveOnChange((_resizableTextAreaRef3 = resizableTextAreaRef.current) === null || _resizableTextAreaRef3 === void 0 ? void 0 : _resizableTextAreaRef3.textArea, e, onChange);\n  };\n  var val = fixControlledValue(value);\n  if (!compositing && hasMaxLength && (customValue === null || customValue === undefined)) {\n    // fix #27612 将value转为数组进行截取，解决 '😂'.length === 2 等emoji表情导致的截取乱码的问题\n    val = fixEmojiLength(val, maxLength);\n  }\n  var suffixNode = suffix;\n  var dataCount;\n  if (showCount) {\n    var valueLength = _toConsumableArray(val).length;\n    if (_typeof(showCount) === 'object') {\n      dataCount = showCount.formatter({\n        value: val,\n        count: valueLength,\n        maxLength: maxLength\n      });\n    } else {\n      dataCount = \"\".concat(valueLength).concat(hasMaxLength ? \" / \".concat(maxLength) : '');\n    }\n    suffixNode = /*#__PURE__*/React.createElement(React.Fragment, null, suffixNode, /*#__PURE__*/React.createElement(\"span\", {\n      className: clsx(\"\".concat(prefixCls, \"-data-count\"), classNames === null || classNames === void 0 ? void 0 : classNames.count),\n      style: styles === null || styles === void 0 ? void 0 : styles.count\n    }, dataCount));\n  }\n  var handleResize = function handleResize(size) {\n    var _resizableTextAreaRef4;\n    onResize === null || onResize === void 0 ? void 0 : onResize(size);\n    if ((_resizableTextAreaRef4 = resizableTextAreaRef.current) !== null && _resizableTextAreaRef4 !== void 0 && _resizableTextAreaRef4.textArea.style.height) {\n      setTextareaResized(true);\n    }\n  };\n  var isPureTextArea = !rest.autoSize && !showCount && !allowClear;\n  var textarea = /*#__PURE__*/React.createElement(BaseInput, {\n    value: val,\n    allowClear: allowClear,\n    handleReset: handleReset,\n    suffix: suffixNode,\n    prefixCls: prefixCls,\n    classes: {\n      affixWrapper: clsx(classes === null || classes === void 0 ? void 0 : classes.affixWrapper, (_clsx = {}, _defineProperty(_clsx, \"\".concat(prefixCls, \"-show-count\"), showCount), _defineProperty(_clsx, \"\".concat(prefixCls, \"-textarea-allow-clear\"), allowClear), _clsx))\n    },\n    disabled: disabled,\n    focused: focused,\n    className: className,\n    style: _objectSpread(_objectSpread({}, style), textareaResized && !isPureTextArea ? {\n      height: 'auto'\n    } : {}),\n    dataAttrs: {\n      affixWrapper: {\n        'data-count': typeof dataCount === 'string' ? dataCount : undefined\n      }\n    },\n    hidden: hidden,\n    inputElement: /*#__PURE__*/React.createElement(ResizableTextArea, _extends({}, rest, {\n      onKeyDown: handleKeyDown,\n      onChange: handleChange,\n      onFocus: handleFocus,\n      onBlur: handleBlur,\n      onCompositionStart: onInternalCompositionStart,\n      onCompositionEnd: onInternalCompositionEnd,\n      className: classNames === null || classNames === void 0 ? void 0 : classNames.textarea,\n      style: _objectSpread(_objectSpread({}, styles === null || styles === void 0 ? void 0 : styles.textarea), {}, {\n        resize: style === null || style === void 0 ? void 0 : style.resize\n      }),\n      disabled: disabled,\n      prefixCls: prefixCls,\n      onResize: handleResize,\n      ref: resizableTextAreaRef\n    }))\n  });\n  return textarea;\n});\nexport default TextArea;", "map": {"version": 3, "names": ["_extends", "_objectSpread", "_defineProperty", "_typeof", "_slicedToArray", "_objectWithoutProperties", "_toConsumableArray", "_excluded", "clsx", "BaseInput", "fixControlledValue", "resolveOnChange", "useMergedState", "React", "useEffect", "useImperativeHandle", "useRef", "ResizableTextArea", "fixEmojiLength", "value", "max<PERSON><PERSON><PERSON>", "slice", "join", "setTriggerValue", "isCursorInEnd", "preValue", "triggerValue", "newTriggerValue", "length", "TextArea", "forwardRef", "_ref", "ref", "_clsx", "defaultValue", "customValue", "onFocus", "onBlur", "onChange", "allowClear", "onCompositionStart", "onCompositionEnd", "suffix", "_ref$prefixCls", "prefixCls", "classes", "showCount", "className", "style", "disabled", "hidden", "classNames", "styles", "onResize", "rest", "_useMergedState", "_useMergedState2", "setValue", "resizableTextAreaRef", "_React$useState", "useState", "_React$useState2", "focused", "setFocused", "_React$useState3", "_React$useState4", "compositing", "setCompositing", "oldCompositionValueRef", "oldSelectionStartRef", "_React$useState5", "_React$useState6", "textareaResized", "setTextareaResized", "focus", "_resizableTextAreaRef", "current", "textArea", "resizableTextArea", "blur", "_resizableTextAreaRef2", "prev", "hasMaxLength", "Number", "onInternalCompositionStart", "e", "currentTarget", "selectionStart", "onInternalCompositionEnd", "_oldCompositionValueR", "handleChange", "target", "handleKeyDown", "onPressEnter", "onKeyDown", "key", "handleFocus", "handleBlur", "handleReset", "_resizableTextAreaRef3", "val", "undefined", "suffixNode", "dataCount", "valueLength", "formatter", "count", "concat", "createElement", "Fragment", "handleResize", "size", "_resizableTextAreaRef4", "height", "isPureTextArea", "autoSize", "textarea", "affixWrapper", "dataAttrs", "inputElement", "resize"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-textarea/es/TextArea.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nvar _excluded = [\"defaultValue\", \"value\", \"onFocus\", \"onBlur\", \"onChange\", \"allowClear\", \"maxLength\", \"onCompositionStart\", \"onCompositionEnd\", \"suffix\", \"prefixCls\", \"classes\", \"showCount\", \"className\", \"style\", \"disabled\", \"hidden\", \"classNames\", \"styles\", \"onResize\"];\nimport clsx from 'classnames';\nimport { BaseInput } from 'rc-input';\nimport { fixControlledValue, resolveOnChange } from \"rc-input/es/utils/commonUtils\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport React, { useEffect, useImperativeHandle, useRef } from 'react';\nimport ResizableTextArea from \"./ResizableTextArea\";\nfunction fixEmojiLength(value, maxLength) {\n  return _toConsumableArray(value || '').slice(0, maxLength).join('');\n}\nfunction setTriggerValue(isCursorInEnd, preValue, triggerValue, maxLength) {\n  var newTriggerValue = triggerValue;\n  if (isCursorInEnd) {\n    // 光标在尾部，直接截断\n    newTriggerValue = fixEmojiLength(triggerValue, maxLength);\n  } else if (_toConsumableArray(preValue || '').length < triggerValue.length && _toConsumableArray(triggerValue || '').length > maxLength) {\n    // 光标在中间，如果最后的值超过最大值，则采用原先的值\n    newTriggerValue = preValue;\n  }\n  return newTriggerValue;\n}\nvar TextArea = /*#__PURE__*/React.forwardRef(function (_ref, ref) {\n  var _clsx;\n  var defaultValue = _ref.defaultValue,\n    customValue = _ref.value,\n    onFocus = _ref.onFocus,\n    onBlur = _ref.onBlur,\n    onChange = _ref.onChange,\n    allowClear = _ref.allowClear,\n    maxLength = _ref.maxLength,\n    onCompositionStart = _ref.onCompositionStart,\n    onCompositionEnd = _ref.onCompositionEnd,\n    suffix = _ref.suffix,\n    _ref$prefixCls = _ref.prefixCls,\n    prefixCls = _ref$prefixCls === void 0 ? 'rc-textarea' : _ref$prefixCls,\n    classes = _ref.classes,\n    showCount = _ref.showCount,\n    className = _ref.className,\n    style = _ref.style,\n    disabled = _ref.disabled,\n    hidden = _ref.hidden,\n    classNames = _ref.classNames,\n    styles = _ref.styles,\n    onResize = _ref.onResize,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var _useMergedState = useMergedState(defaultValue, {\n      value: customValue,\n      defaultValue: defaultValue\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    value = _useMergedState2[0],\n    setValue = _useMergedState2[1];\n  var resizableTextAreaRef = useRef(null);\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    focused = _React$useState2[0],\n    setFocused = _React$useState2[1];\n  var _React$useState3 = React.useState(false),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    compositing = _React$useState4[0],\n    setCompositing = _React$useState4[1];\n  var oldCompositionValueRef = React.useRef();\n  var oldSelectionStartRef = React.useRef(0);\n  var _React$useState5 = React.useState(null),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    textareaResized = _React$useState6[0],\n    setTextareaResized = _React$useState6[1];\n  var focus = function focus() {\n    var _resizableTextAreaRef;\n    (_resizableTextAreaRef = resizableTextAreaRef.current) === null || _resizableTextAreaRef === void 0 ? void 0 : _resizableTextAreaRef.textArea.focus();\n  };\n  useImperativeHandle(ref, function () {\n    return {\n      resizableTextArea: resizableTextAreaRef.current,\n      focus: focus,\n      blur: function blur() {\n        var _resizableTextAreaRef2;\n        (_resizableTextAreaRef2 = resizableTextAreaRef.current) === null || _resizableTextAreaRef2 === void 0 ? void 0 : _resizableTextAreaRef2.textArea.blur();\n      }\n    };\n  });\n  useEffect(function () {\n    setFocused(function (prev) {\n      return !disabled && prev;\n    });\n  }, [disabled]);\n\n  // =========================== Value Update ===========================\n  // Max length value\n  var hasMaxLength = Number(maxLength) > 0;\n  var onInternalCompositionStart = function onInternalCompositionStart(e) {\n    setCompositing(true);\n    // 拼音输入前保存一份旧值\n    oldCompositionValueRef.current = value;\n    // 保存旧的光标位置\n    oldSelectionStartRef.current = e.currentTarget.selectionStart;\n    onCompositionStart === null || onCompositionStart === void 0 ? void 0 : onCompositionStart(e);\n  };\n  var onInternalCompositionEnd = function onInternalCompositionEnd(e) {\n    setCompositing(false);\n    var triggerValue = e.currentTarget.value;\n    if (hasMaxLength) {\n      var _oldCompositionValueR;\n      var isCursorInEnd = oldSelectionStartRef.current >= maxLength + 1 || oldSelectionStartRef.current === ((_oldCompositionValueR = oldCompositionValueRef.current) === null || _oldCompositionValueR === void 0 ? void 0 : _oldCompositionValueR.length);\n      triggerValue = setTriggerValue(isCursorInEnd, oldCompositionValueRef.current, triggerValue, maxLength);\n    }\n    // Patch composition onChange when value changed\n    if (triggerValue !== value) {\n      setValue(triggerValue);\n      resolveOnChange(e.currentTarget, e, onChange, triggerValue);\n    }\n    onCompositionEnd === null || onCompositionEnd === void 0 ? void 0 : onCompositionEnd(e);\n  };\n  var handleChange = function handleChange(e) {\n    var triggerValue = e.target.value;\n    if (!compositing && hasMaxLength) {\n      // 1. 复制粘贴超过maxlength的情况 2.未超过maxlength的情况\n      var isCursorInEnd = e.target.selectionStart >= maxLength + 1 || e.target.selectionStart === triggerValue.length || !e.target.selectionStart;\n      triggerValue = setTriggerValue(isCursorInEnd, value, triggerValue, maxLength);\n    }\n    setValue(triggerValue);\n    resolveOnChange(e.currentTarget, e, onChange, triggerValue);\n  };\n  var handleKeyDown = function handleKeyDown(e) {\n    var onPressEnter = rest.onPressEnter,\n      onKeyDown = rest.onKeyDown;\n    if (e.key === 'Enter' && onPressEnter) {\n      onPressEnter(e);\n    }\n    onKeyDown === null || onKeyDown === void 0 ? void 0 : onKeyDown(e);\n  };\n  var handleFocus = function handleFocus(e) {\n    setFocused(true);\n    onFocus === null || onFocus === void 0 ? void 0 : onFocus(e);\n  };\n  var handleBlur = function handleBlur(e) {\n    setFocused(false);\n    onBlur === null || onBlur === void 0 ? void 0 : onBlur(e);\n  };\n\n  // ============================== Reset ===============================\n  var handleReset = function handleReset(e) {\n    var _resizableTextAreaRef3;\n    setValue('');\n    focus();\n    resolveOnChange((_resizableTextAreaRef3 = resizableTextAreaRef.current) === null || _resizableTextAreaRef3 === void 0 ? void 0 : _resizableTextAreaRef3.textArea, e, onChange);\n  };\n  var val = fixControlledValue(value);\n  if (!compositing && hasMaxLength && (customValue === null || customValue === undefined)) {\n    // fix #27612 将value转为数组进行截取，解决 '😂'.length === 2 等emoji表情导致的截取乱码的问题\n    val = fixEmojiLength(val, maxLength);\n  }\n  var suffixNode = suffix;\n  var dataCount;\n  if (showCount) {\n    var valueLength = _toConsumableArray(val).length;\n    if (_typeof(showCount) === 'object') {\n      dataCount = showCount.formatter({\n        value: val,\n        count: valueLength,\n        maxLength: maxLength\n      });\n    } else {\n      dataCount = \"\".concat(valueLength).concat(hasMaxLength ? \" / \".concat(maxLength) : '');\n    }\n    suffixNode = /*#__PURE__*/React.createElement(React.Fragment, null, suffixNode, /*#__PURE__*/React.createElement(\"span\", {\n      className: clsx(\"\".concat(prefixCls, \"-data-count\"), classNames === null || classNames === void 0 ? void 0 : classNames.count),\n      style: styles === null || styles === void 0 ? void 0 : styles.count\n    }, dataCount));\n  }\n  var handleResize = function handleResize(size) {\n    var _resizableTextAreaRef4;\n    onResize === null || onResize === void 0 ? void 0 : onResize(size);\n    if ((_resizableTextAreaRef4 = resizableTextAreaRef.current) !== null && _resizableTextAreaRef4 !== void 0 && _resizableTextAreaRef4.textArea.style.height) {\n      setTextareaResized(true);\n    }\n  };\n  var isPureTextArea = !rest.autoSize && !showCount && !allowClear;\n  var textarea = /*#__PURE__*/React.createElement(BaseInput, {\n    value: val,\n    allowClear: allowClear,\n    handleReset: handleReset,\n    suffix: suffixNode,\n    prefixCls: prefixCls,\n    classes: {\n      affixWrapper: clsx(classes === null || classes === void 0 ? void 0 : classes.affixWrapper, (_clsx = {}, _defineProperty(_clsx, \"\".concat(prefixCls, \"-show-count\"), showCount), _defineProperty(_clsx, \"\".concat(prefixCls, \"-textarea-allow-clear\"), allowClear), _clsx))\n    },\n    disabled: disabled,\n    focused: focused,\n    className: className,\n    style: _objectSpread(_objectSpread({}, style), textareaResized && !isPureTextArea ? {\n      height: 'auto'\n    } : {}),\n    dataAttrs: {\n      affixWrapper: {\n        'data-count': typeof dataCount === 'string' ? dataCount : undefined\n      }\n    },\n    hidden: hidden,\n    inputElement: /*#__PURE__*/React.createElement(ResizableTextArea, _extends({}, rest, {\n      onKeyDown: handleKeyDown,\n      onChange: handleChange,\n      onFocus: handleFocus,\n      onBlur: handleBlur,\n      onCompositionStart: onInternalCompositionStart,\n      onCompositionEnd: onInternalCompositionEnd,\n      className: classNames === null || classNames === void 0 ? void 0 : classNames.textarea,\n      style: _objectSpread(_objectSpread({}, styles === null || styles === void 0 ? void 0 : styles.textarea), {}, {\n        resize: style === null || style === void 0 ? void 0 : style.resize\n      }),\n      disabled: disabled,\n      prefixCls: prefixCls,\n      onResize: handleResize,\n      ref: resizableTextAreaRef\n    }))\n  });\n  return textarea;\n});\nexport default TextArea;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,IAAIC,SAAS,GAAG,CAAC,cAAc,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,WAAW,EAAE,oBAAoB,EAAE,kBAAkB,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAE,UAAU,CAAC;AAC9Q,OAAOC,IAAI,MAAM,YAAY;AAC7B,SAASC,SAAS,QAAQ,UAAU;AACpC,SAASC,kBAAkB,EAAEC,eAAe,QAAQ,+BAA+B;AACnF,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,KAAK,IAAIC,SAAS,EAAEC,mBAAmB,EAAEC,MAAM,QAAQ,OAAO;AACrE,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,cAAcA,CAACC,KAAK,EAAEC,SAAS,EAAE;EACxC,OAAOd,kBAAkB,CAACa,KAAK,IAAI,EAAE,CAAC,CAACE,KAAK,CAAC,CAAC,EAAED,SAAS,CAAC,CAACE,IAAI,CAAC,EAAE,CAAC;AACrE;AACA,SAASC,eAAeA,CAACC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,EAAEN,SAAS,EAAE;EACzE,IAAIO,eAAe,GAAGD,YAAY;EAClC,IAAIF,aAAa,EAAE;IACjB;IACAG,eAAe,GAAGT,cAAc,CAACQ,YAAY,EAAEN,SAAS,CAAC;EAC3D,CAAC,MAAM,IAAId,kBAAkB,CAACmB,QAAQ,IAAI,EAAE,CAAC,CAACG,MAAM,GAAGF,YAAY,CAACE,MAAM,IAAItB,kBAAkB,CAACoB,YAAY,IAAI,EAAE,CAAC,CAACE,MAAM,GAAGR,SAAS,EAAE;IACvI;IACAO,eAAe,GAAGF,QAAQ;EAC5B;EACA,OAAOE,eAAe;AACxB;AACA,IAAIE,QAAQ,GAAG,aAAahB,KAAK,CAACiB,UAAU,CAAC,UAAUC,IAAI,EAAEC,GAAG,EAAE;EAChE,IAAIC,KAAK;EACT,IAAIC,YAAY,GAAGH,IAAI,CAACG,YAAY;IAClCC,WAAW,GAAGJ,IAAI,CAACZ,KAAK;IACxBiB,OAAO,GAAGL,IAAI,CAACK,OAAO;IACtBC,MAAM,GAAGN,IAAI,CAACM,MAAM;IACpBC,QAAQ,GAAGP,IAAI,CAACO,QAAQ;IACxBC,UAAU,GAAGR,IAAI,CAACQ,UAAU;IAC5BnB,SAAS,GAAGW,IAAI,CAACX,SAAS;IAC1BoB,kBAAkB,GAAGT,IAAI,CAACS,kBAAkB;IAC5CC,gBAAgB,GAAGV,IAAI,CAACU,gBAAgB;IACxCC,MAAM,GAAGX,IAAI,CAACW,MAAM;IACpBC,cAAc,GAAGZ,IAAI,CAACa,SAAS;IAC/BA,SAAS,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,aAAa,GAAGA,cAAc;IACtEE,OAAO,GAAGd,IAAI,CAACc,OAAO;IACtBC,SAAS,GAAGf,IAAI,CAACe,SAAS;IAC1BC,SAAS,GAAGhB,IAAI,CAACgB,SAAS;IAC1BC,KAAK,GAAGjB,IAAI,CAACiB,KAAK;IAClBC,QAAQ,GAAGlB,IAAI,CAACkB,QAAQ;IACxBC,MAAM,GAAGnB,IAAI,CAACmB,MAAM;IACpBC,UAAU,GAAGpB,IAAI,CAACoB,UAAU;IAC5BC,MAAM,GAAGrB,IAAI,CAACqB,MAAM;IACpBC,QAAQ,GAAGtB,IAAI,CAACsB,QAAQ;IACxBC,IAAI,GAAGjD,wBAAwB,CAAC0B,IAAI,EAAExB,SAAS,CAAC;EAClD,IAAIgD,eAAe,GAAG3C,cAAc,CAACsB,YAAY,EAAE;MAC/Cf,KAAK,EAAEgB,WAAW;MAClBD,YAAY,EAAEA;IAChB,CAAC,CAAC;IACFsB,gBAAgB,GAAGpD,cAAc,CAACmD,eAAe,EAAE,CAAC,CAAC;IACrDpC,KAAK,GAAGqC,gBAAgB,CAAC,CAAC,CAAC;IAC3BC,QAAQ,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EAChC,IAAIE,oBAAoB,GAAG1C,MAAM,CAAC,IAAI,CAAC;EACvC,IAAI2C,eAAe,GAAG9C,KAAK,CAAC+C,QAAQ,CAAC,KAAK,CAAC;IACzCC,gBAAgB,GAAGzD,cAAc,CAACuD,eAAe,EAAE,CAAC,CAAC;IACrDG,OAAO,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC7BE,UAAU,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAClC,IAAIG,gBAAgB,GAAGnD,KAAK,CAAC+C,QAAQ,CAAC,KAAK,CAAC;IAC1CK,gBAAgB,GAAG7D,cAAc,CAAC4D,gBAAgB,EAAE,CAAC,CAAC;IACtDE,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACtC,IAAIG,sBAAsB,GAAGvD,KAAK,CAACG,MAAM,CAAC,CAAC;EAC3C,IAAIqD,oBAAoB,GAAGxD,KAAK,CAACG,MAAM,CAAC,CAAC,CAAC;EAC1C,IAAIsD,gBAAgB,GAAGzD,KAAK,CAAC+C,QAAQ,CAAC,IAAI,CAAC;IACzCW,gBAAgB,GAAGnE,cAAc,CAACkE,gBAAgB,EAAE,CAAC,CAAC;IACtDE,eAAe,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACrCE,kBAAkB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC1C,IAAIG,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;IAC3B,IAAIC,qBAAqB;IACzB,CAACA,qBAAqB,GAAGjB,oBAAoB,CAACkB,OAAO,MAAM,IAAI,IAAID,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACE,QAAQ,CAACH,KAAK,CAAC,CAAC;EACvJ,CAAC;EACD3D,mBAAmB,CAACiB,GAAG,EAAE,YAAY;IACnC,OAAO;MACL8C,iBAAiB,EAAEpB,oBAAoB,CAACkB,OAAO;MAC/CF,KAAK,EAAEA,KAAK;MACZK,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;QACpB,IAAIC,sBAAsB;QAC1B,CAACA,sBAAsB,GAAGtB,oBAAoB,CAACkB,OAAO,MAAM,IAAI,IAAII,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACH,QAAQ,CAACE,IAAI,CAAC,CAAC;MACzJ;IACF,CAAC;EACH,CAAC,CAAC;EACFjE,SAAS,CAAC,YAAY;IACpBiD,UAAU,CAAC,UAAUkB,IAAI,EAAE;MACzB,OAAO,CAAChC,QAAQ,IAAIgC,IAAI;IAC1B,CAAC,CAAC;EACJ,CAAC,EAAE,CAAChC,QAAQ,CAAC,CAAC;;EAEd;EACA;EACA,IAAIiC,YAAY,GAAGC,MAAM,CAAC/D,SAAS,CAAC,GAAG,CAAC;EACxC,IAAIgE,0BAA0B,GAAG,SAASA,0BAA0BA,CAACC,CAAC,EAAE;IACtElB,cAAc,CAAC,IAAI,CAAC;IACpB;IACAC,sBAAsB,CAACQ,OAAO,GAAGzD,KAAK;IACtC;IACAkD,oBAAoB,CAACO,OAAO,GAAGS,CAAC,CAACC,aAAa,CAACC,cAAc;IAC7D/C,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAAC6C,CAAC,CAAC;EAC/F,CAAC;EACD,IAAIG,wBAAwB,GAAG,SAASA,wBAAwBA,CAACH,CAAC,EAAE;IAClElB,cAAc,CAAC,KAAK,CAAC;IACrB,IAAIzC,YAAY,GAAG2D,CAAC,CAACC,aAAa,CAACnE,KAAK;IACxC,IAAI+D,YAAY,EAAE;MAChB,IAAIO,qBAAqB;MACzB,IAAIjE,aAAa,GAAG6C,oBAAoB,CAACO,OAAO,IAAIxD,SAAS,GAAG,CAAC,IAAIiD,oBAAoB,CAACO,OAAO,MAAM,CAACa,qBAAqB,GAAGrB,sBAAsB,CAACQ,OAAO,MAAM,IAAI,IAAIa,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAAC7D,MAAM,CAAC;MACrPF,YAAY,GAAGH,eAAe,CAACC,aAAa,EAAE4C,sBAAsB,CAACQ,OAAO,EAAElD,YAAY,EAAEN,SAAS,CAAC;IACxG;IACA;IACA,IAAIM,YAAY,KAAKP,KAAK,EAAE;MAC1BsC,QAAQ,CAAC/B,YAAY,CAAC;MACtBf,eAAe,CAAC0E,CAAC,CAACC,aAAa,EAAED,CAAC,EAAE/C,QAAQ,EAAEZ,YAAY,CAAC;IAC7D;IACAe,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAAC4C,CAAC,CAAC;EACzF,CAAC;EACD,IAAIK,YAAY,GAAG,SAASA,YAAYA,CAACL,CAAC,EAAE;IAC1C,IAAI3D,YAAY,GAAG2D,CAAC,CAACM,MAAM,CAACxE,KAAK;IACjC,IAAI,CAAC+C,WAAW,IAAIgB,YAAY,EAAE;MAChC;MACA,IAAI1D,aAAa,GAAG6D,CAAC,CAACM,MAAM,CAACJ,cAAc,IAAInE,SAAS,GAAG,CAAC,IAAIiE,CAAC,CAACM,MAAM,CAACJ,cAAc,KAAK7D,YAAY,CAACE,MAAM,IAAI,CAACyD,CAAC,CAACM,MAAM,CAACJ,cAAc;MAC3I7D,YAAY,GAAGH,eAAe,CAACC,aAAa,EAAEL,KAAK,EAAEO,YAAY,EAAEN,SAAS,CAAC;IAC/E;IACAqC,QAAQ,CAAC/B,YAAY,CAAC;IACtBf,eAAe,CAAC0E,CAAC,CAACC,aAAa,EAAED,CAAC,EAAE/C,QAAQ,EAAEZ,YAAY,CAAC;EAC7D,CAAC;EACD,IAAIkE,aAAa,GAAG,SAASA,aAAaA,CAACP,CAAC,EAAE;IAC5C,IAAIQ,YAAY,GAAGvC,IAAI,CAACuC,YAAY;MAClCC,SAAS,GAAGxC,IAAI,CAACwC,SAAS;IAC5B,IAAIT,CAAC,CAACU,GAAG,KAAK,OAAO,IAAIF,YAAY,EAAE;MACrCA,YAAY,CAACR,CAAC,CAAC;IACjB;IACAS,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACT,CAAC,CAAC;EACpE,CAAC;EACD,IAAIW,WAAW,GAAG,SAASA,WAAWA,CAACX,CAAC,EAAE;IACxCtB,UAAU,CAAC,IAAI,CAAC;IAChB3B,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACiD,CAAC,CAAC;EAC9D,CAAC;EACD,IAAIY,UAAU,GAAG,SAASA,UAAUA,CAACZ,CAAC,EAAE;IACtCtB,UAAU,CAAC,KAAK,CAAC;IACjB1B,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACgD,CAAC,CAAC;EAC3D,CAAC;;EAED;EACA,IAAIa,WAAW,GAAG,SAASA,WAAWA,CAACb,CAAC,EAAE;IACxC,IAAIc,sBAAsB;IAC1B1C,QAAQ,CAAC,EAAE,CAAC;IACZiB,KAAK,CAAC,CAAC;IACP/D,eAAe,CAAC,CAACwF,sBAAsB,GAAGzC,oBAAoB,CAACkB,OAAO,MAAM,IAAI,IAAIuB,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACtB,QAAQ,EAAEQ,CAAC,EAAE/C,QAAQ,CAAC;EAChL,CAAC;EACD,IAAI8D,GAAG,GAAG1F,kBAAkB,CAACS,KAAK,CAAC;EACnC,IAAI,CAAC+C,WAAW,IAAIgB,YAAY,KAAK/C,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAKkE,SAAS,CAAC,EAAE;IACvF;IACAD,GAAG,GAAGlF,cAAc,CAACkF,GAAG,EAAEhF,SAAS,CAAC;EACtC;EACA,IAAIkF,UAAU,GAAG5D,MAAM;EACvB,IAAI6D,SAAS;EACb,IAAIzD,SAAS,EAAE;IACb,IAAI0D,WAAW,GAAGlG,kBAAkB,CAAC8F,GAAG,CAAC,CAACxE,MAAM;IAChD,IAAIzB,OAAO,CAAC2C,SAAS,CAAC,KAAK,QAAQ,EAAE;MACnCyD,SAAS,GAAGzD,SAAS,CAAC2D,SAAS,CAAC;QAC9BtF,KAAK,EAAEiF,GAAG;QACVM,KAAK,EAAEF,WAAW;QAClBpF,SAAS,EAAEA;MACb,CAAC,CAAC;IACJ,CAAC,MAAM;MACLmF,SAAS,GAAG,EAAE,CAACI,MAAM,CAACH,WAAW,CAAC,CAACG,MAAM,CAACzB,YAAY,GAAG,KAAK,CAACyB,MAAM,CAACvF,SAAS,CAAC,GAAG,EAAE,CAAC;IACxF;IACAkF,UAAU,GAAG,aAAazF,KAAK,CAAC+F,aAAa,CAAC/F,KAAK,CAACgG,QAAQ,EAAE,IAAI,EAAEP,UAAU,EAAE,aAAazF,KAAK,CAAC+F,aAAa,CAAC,MAAM,EAAE;MACvH7D,SAAS,EAAEvC,IAAI,CAAC,EAAE,CAACmG,MAAM,CAAC/D,SAAS,EAAE,aAAa,CAAC,EAAEO,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACuD,KAAK,CAAC;MAC9H1D,KAAK,EAAEI,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACsD;IAChE,CAAC,EAAEH,SAAS,CAAC,CAAC;EAChB;EACA,IAAIO,YAAY,GAAG,SAASA,YAAYA,CAACC,IAAI,EAAE;IAC7C,IAAIC,sBAAsB;IAC1B3D,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC0D,IAAI,CAAC;IAClE,IAAI,CAACC,sBAAsB,GAAGtD,oBAAoB,CAACkB,OAAO,MAAM,IAAI,IAAIoC,sBAAsB,KAAK,KAAK,CAAC,IAAIA,sBAAsB,CAACnC,QAAQ,CAAC7B,KAAK,CAACiE,MAAM,EAAE;MACzJxC,kBAAkB,CAAC,IAAI,CAAC;IAC1B;EACF,CAAC;EACD,IAAIyC,cAAc,GAAG,CAAC5D,IAAI,CAAC6D,QAAQ,IAAI,CAACrE,SAAS,IAAI,CAACP,UAAU;EAChE,IAAI6E,QAAQ,GAAG,aAAavG,KAAK,CAAC+F,aAAa,CAACnG,SAAS,EAAE;IACzDU,KAAK,EAAEiF,GAAG;IACV7D,UAAU,EAAEA,UAAU;IACtB2D,WAAW,EAAEA,WAAW;IACxBxD,MAAM,EAAE4D,UAAU;IAClB1D,SAAS,EAAEA,SAAS;IACpBC,OAAO,EAAE;MACPwE,YAAY,EAAE7G,IAAI,CAACqC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACwE,YAAY,GAAGpF,KAAK,GAAG,CAAC,CAAC,EAAE/B,eAAe,CAAC+B,KAAK,EAAE,EAAE,CAAC0E,MAAM,CAAC/D,SAAS,EAAE,aAAa,CAAC,EAAEE,SAAS,CAAC,EAAE5C,eAAe,CAAC+B,KAAK,EAAE,EAAE,CAAC0E,MAAM,CAAC/D,SAAS,EAAE,uBAAuB,CAAC,EAAEL,UAAU,CAAC,EAAEN,KAAK,CAAC;IAC3Q,CAAC;IACDgB,QAAQ,EAAEA,QAAQ;IAClBa,OAAO,EAAEA,OAAO;IAChBf,SAAS,EAAEA,SAAS;IACpBC,KAAK,EAAE/C,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+C,KAAK,CAAC,EAAEwB,eAAe,IAAI,CAAC0C,cAAc,GAAG;MAClFD,MAAM,EAAE;IACV,CAAC,GAAG,CAAC,CAAC,CAAC;IACPK,SAAS,EAAE;MACTD,YAAY,EAAE;QACZ,YAAY,EAAE,OAAOd,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAGF;MAC5D;IACF,CAAC;IACDnD,MAAM,EAAEA,MAAM;IACdqE,YAAY,EAAE,aAAa1G,KAAK,CAAC+F,aAAa,CAAC3F,iBAAiB,EAAEjB,QAAQ,CAAC,CAAC,CAAC,EAAEsD,IAAI,EAAE;MACnFwC,SAAS,EAAEF,aAAa;MACxBtD,QAAQ,EAAEoD,YAAY;MACtBtD,OAAO,EAAE4D,WAAW;MACpB3D,MAAM,EAAE4D,UAAU;MAClBzD,kBAAkB,EAAE4C,0BAA0B;MAC9C3C,gBAAgB,EAAE+C,wBAAwB;MAC1CzC,SAAS,EAAEI,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACiE,QAAQ;MACtFpE,KAAK,EAAE/C,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmD,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACgE,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE;QAC3GI,MAAM,EAAExE,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACwE;MAC9D,CAAC,CAAC;MACFvE,QAAQ,EAAEA,QAAQ;MAClBL,SAAS,EAAEA,SAAS;MACpBS,QAAQ,EAAEyD,YAAY;MACtB9E,GAAG,EAAE0B;IACP,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAO0D,QAAQ;AACjB,CAAC,CAAC;AACF,eAAevF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}