{"ast": null, "code": "/* istanbul ignore next */\n/**\n * This is a syntactic sugar for `columns` prop.\n * So HOC will not work on this.\n */ // eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction Column(_) {\n  return null;\n}\nexport default Column;", "map": {"version": 3, "names": ["Column", "_"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-table/es/sugar/Column.js"], "sourcesContent": ["/* istanbul ignore next */\n/**\n * This is a syntactic sugar for `columns` prop.\n * So HOC will not work on this.\n */ // eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction Column(_) {\n  return null;\n}\nexport default Column;"], "mappings": "AAAA;AACA;AACA;AACA;AACA,GAHA,CAGI;AACJ,SAASA,MAAMA,CAACC,CAAC,EAAE;EACjB,OAAO,IAAI;AACb;AACA,eAAeD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}