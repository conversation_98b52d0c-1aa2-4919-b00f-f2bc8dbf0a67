{"ast": null, "code": "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport QuestionCircleOutlined from \"@ant-design/icons/es/icons/QuestionCircleOutlined\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport Col from '../grid/col';\nimport defaultLocale from '../locale/en_US';\nimport { useLocale } from '../locale';\nimport Tooltip from '../tooltip';\nimport { FormContext } from './context';\nfunction toTooltipProps(tooltip) {\n  if (!tooltip) {\n    return null;\n  }\n  if (typeof tooltip === 'object' && ! /*#__PURE__*/React.isValidElement(tooltip)) {\n    return tooltip;\n  }\n  return {\n    title: tooltip\n  };\n}\nconst FormItemLabel = _ref => {\n  let {\n    prefixCls,\n    label,\n    htmlFor,\n    labelCol,\n    labelAlign,\n    colon,\n    required,\n    requiredMark,\n    tooltip\n  } = _ref;\n  var _a;\n  const [formLocale] = useLocale('Form');\n  const {\n    vertical,\n    labelAlign: contextLabelAlign,\n    labelCol: contextLabelCol,\n    labelWrap,\n    colon: contextColon\n  } = React.useContext(FormContext);\n  if (!label) {\n    return null;\n  }\n  const mergedLabelCol = labelCol || contextLabelCol || {};\n  const mergedLabelAlign = labelAlign || contextLabelAlign;\n  const labelClsBasic = \"\".concat(prefixCls, \"-item-label\");\n  const labelColClassName = classNames(labelClsBasic, mergedLabelAlign === 'left' && \"\".concat(labelClsBasic, \"-left\"), mergedLabelCol.className, {\n    [\"\".concat(labelClsBasic, \"-wrap\")]: !!labelWrap\n  });\n  let labelChildren = label;\n  // Keep label is original where there should have no colon\n  const computedColon = colon === true || contextColon !== false && colon !== false;\n  const haveColon = computedColon && !vertical;\n  // Remove duplicated user input colon\n  if (haveColon && typeof label === 'string' && label.trim() !== '') {\n    labelChildren = label.replace(/[:|：]\\s*$/, '');\n  }\n  // Tooltip\n  const tooltipProps = toTooltipProps(tooltip);\n  if (tooltipProps) {\n    const {\n        icon = /*#__PURE__*/React.createElement(QuestionCircleOutlined, null)\n      } = tooltipProps,\n      restTooltipProps = __rest(tooltipProps, [\"icon\"]);\n    const tooltipNode = /*#__PURE__*/React.createElement(Tooltip, Object.assign({}, restTooltipProps), /*#__PURE__*/React.cloneElement(icon, {\n      className: \"\".concat(prefixCls, \"-item-tooltip\"),\n      title: ''\n    }));\n    labelChildren = /*#__PURE__*/React.createElement(React.Fragment, null, labelChildren, tooltipNode);\n  }\n  if (requiredMark === 'optional' && !required) {\n    labelChildren = /*#__PURE__*/React.createElement(React.Fragment, null, labelChildren, /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-item-optional\"),\n      title: \"\"\n    }, (formLocale === null || formLocale === void 0 ? void 0 : formLocale.optional) || ((_a = defaultLocale.Form) === null || _a === void 0 ? void 0 : _a.optional)));\n  }\n  const labelClassName = classNames({\n    [\"\".concat(prefixCls, \"-item-required\")]: required,\n    [\"\".concat(prefixCls, \"-item-required-mark-optional\")]: requiredMark === 'optional',\n    [\"\".concat(prefixCls, \"-item-no-colon\")]: !computedColon\n  });\n  return /*#__PURE__*/React.createElement(Col, Object.assign({}, mergedLabelCol, {\n    className: labelColClassName\n  }), /*#__PURE__*/React.createElement(\"label\", {\n    htmlFor: htmlFor,\n    className: labelClassName,\n    title: typeof label === 'string' ? label : ''\n  }, labelChildren));\n};\nexport default FormItemLabel;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "QuestionCircleOutlined", "classNames", "React", "Col", "defaultLocale", "useLocale", "<PERSON><PERSON><PERSON>", "FormContext", "toTooltipProps", "tooltip", "isValidElement", "title", "FormItemLabel", "_ref", "prefixCls", "label", "htmlFor", "labelCol", "labelAlign", "colon", "required", "requiredMark", "_a", "formLocale", "vertical", "contextLabelAlign", "contextLabelCol", "labelWrap", "contextColon", "useContext", "mergedLabelCol", "mergedLabelAlign", "labelClsBasic", "concat", "labelColClassName", "className", "labelChildren", "computedColon", "haveColon", "trim", "replace", "tooltipProps", "icon", "createElement", "restTooltipProps", "tooltipNode", "assign", "cloneElement", "Fragment", "optional", "Form", "labelClassName"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/form/FormItemLabel.js"], "sourcesContent": ["var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport QuestionCircleOutlined from \"@ant-design/icons/es/icons/QuestionCircleOutlined\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport Col from '../grid/col';\nimport defaultLocale from '../locale/en_US';\nimport { useLocale } from '../locale';\nimport Tooltip from '../tooltip';\nimport { FormContext } from './context';\nfunction toTooltipProps(tooltip) {\n  if (!tooltip) {\n    return null;\n  }\n  if (typeof tooltip === 'object' && ! /*#__PURE__*/React.isValidElement(tooltip)) {\n    return tooltip;\n  }\n  return {\n    title: tooltip\n  };\n}\nconst FormItemLabel = _ref => {\n  let {\n    prefixCls,\n    label,\n    htmlFor,\n    labelCol,\n    labelAlign,\n    colon,\n    required,\n    requiredMark,\n    tooltip\n  } = _ref;\n  var _a;\n  const [formLocale] = useLocale('Form');\n  const {\n    vertical,\n    labelAlign: contextLabelAlign,\n    labelCol: contextLabelCol,\n    labelWrap,\n    colon: contextColon\n  } = React.useContext(FormContext);\n  if (!label) {\n    return null;\n  }\n  const mergedLabelCol = labelCol || contextLabelCol || {};\n  const mergedLabelAlign = labelAlign || contextLabelAlign;\n  const labelClsBasic = `${prefixCls}-item-label`;\n  const labelColClassName = classNames(labelClsBasic, mergedLabelAlign === 'left' && `${labelClsBasic}-left`, mergedLabelCol.className, {\n    [`${labelClsBasic}-wrap`]: !!labelWrap\n  });\n  let labelChildren = label;\n  // Keep label is original where there should have no colon\n  const computedColon = colon === true || contextColon !== false && colon !== false;\n  const haveColon = computedColon && !vertical;\n  // Remove duplicated user input colon\n  if (haveColon && typeof label === 'string' && label.trim() !== '') {\n    labelChildren = label.replace(/[:|：]\\s*$/, '');\n  }\n  // Tooltip\n  const tooltipProps = toTooltipProps(tooltip);\n  if (tooltipProps) {\n    const {\n        icon = /*#__PURE__*/React.createElement(QuestionCircleOutlined, null)\n      } = tooltipProps,\n      restTooltipProps = __rest(tooltipProps, [\"icon\"]);\n    const tooltipNode = /*#__PURE__*/React.createElement(Tooltip, Object.assign({}, restTooltipProps), /*#__PURE__*/React.cloneElement(icon, {\n      className: `${prefixCls}-item-tooltip`,\n      title: ''\n    }));\n    labelChildren = /*#__PURE__*/React.createElement(React.Fragment, null, labelChildren, tooltipNode);\n  }\n  if (requiredMark === 'optional' && !required) {\n    labelChildren = /*#__PURE__*/React.createElement(React.Fragment, null, labelChildren, /*#__PURE__*/React.createElement(\"span\", {\n      className: `${prefixCls}-item-optional`,\n      title: \"\"\n    }, (formLocale === null || formLocale === void 0 ? void 0 : formLocale.optional) || ((_a = defaultLocale.Form) === null || _a === void 0 ? void 0 : _a.optional)));\n  }\n  const labelClassName = classNames({\n    [`${prefixCls}-item-required`]: required,\n    [`${prefixCls}-item-required-mark-optional`]: requiredMark === 'optional',\n    [`${prefixCls}-item-no-colon`]: !computedColon\n  });\n  return /*#__PURE__*/React.createElement(Col, Object.assign({}, mergedLabelCol, {\n    className: labelColClassName\n  }), /*#__PURE__*/React.createElement(\"label\", {\n    htmlFor: htmlFor,\n    className: labelClassName,\n    title: typeof label === 'string' ? label : ''\n  }, labelChildren));\n};\nexport default FormItemLabel;"], "mappings": "AAAA,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAOW,sBAAsB,MAAM,mDAAmD;AACtF,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,GAAG,MAAM,aAAa;AAC7B,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,SAAS,QAAQ,WAAW;AACrC,OAAOC,OAAO,MAAM,YAAY;AAChC,SAASC,WAAW,QAAQ,WAAW;AACvC,SAASC,cAAcA,CAACC,OAAO,EAAE;EAC/B,IAAI,CAACA,OAAO,EAAE;IACZ,OAAO,IAAI;EACb;EACA,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAI,EAAE,aAAaP,KAAK,CAACQ,cAAc,CAACD,OAAO,CAAC,EAAE;IAC/E,OAAOA,OAAO;EAChB;EACA,OAAO;IACLE,KAAK,EAAEF;EACT,CAAC;AACH;AACA,MAAMG,aAAa,GAAGC,IAAI,IAAI;EAC5B,IAAI;IACFC,SAAS;IACTC,KAAK;IACLC,OAAO;IACPC,QAAQ;IACRC,UAAU;IACVC,KAAK;IACLC,QAAQ;IACRC,YAAY;IACZZ;EACF,CAAC,GAAGI,IAAI;EACR,IAAIS,EAAE;EACN,MAAM,CAACC,UAAU,CAAC,GAAGlB,SAAS,CAAC,MAAM,CAAC;EACtC,MAAM;IACJmB,QAAQ;IACRN,UAAU,EAAEO,iBAAiB;IAC7BR,QAAQ,EAAES,eAAe;IACzBC,SAAS;IACTR,KAAK,EAAES;EACT,CAAC,GAAG1B,KAAK,CAAC2B,UAAU,CAACtB,WAAW,CAAC;EACjC,IAAI,CAACQ,KAAK,EAAE;IACV,OAAO,IAAI;EACb;EACA,MAAMe,cAAc,GAAGb,QAAQ,IAAIS,eAAe,IAAI,CAAC,CAAC;EACxD,MAAMK,gBAAgB,GAAGb,UAAU,IAAIO,iBAAiB;EACxD,MAAMO,aAAa,MAAAC,MAAA,CAAMnB,SAAS,gBAAa;EAC/C,MAAMoB,iBAAiB,GAAGjC,UAAU,CAAC+B,aAAa,EAAED,gBAAgB,KAAK,MAAM,OAAAE,MAAA,CAAOD,aAAa,UAAO,EAAEF,cAAc,CAACK,SAAS,EAAE;IACpI,IAAAF,MAAA,CAAID,aAAa,aAAU,CAAC,CAACL;EAC/B,CAAC,CAAC;EACF,IAAIS,aAAa,GAAGrB,KAAK;EACzB;EACA,MAAMsB,aAAa,GAAGlB,KAAK,KAAK,IAAI,IAAIS,YAAY,KAAK,KAAK,IAAIT,KAAK,KAAK,KAAK;EACjF,MAAMmB,SAAS,GAAGD,aAAa,IAAI,CAACb,QAAQ;EAC5C;EACA,IAAIc,SAAS,IAAI,OAAOvB,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACwB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IACjEH,aAAa,GAAGrB,KAAK,CAACyB,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;EAChD;EACA;EACA,MAAMC,YAAY,GAAGjC,cAAc,CAACC,OAAO,CAAC;EAC5C,IAAIgC,YAAY,EAAE;IAChB,MAAM;QACFC,IAAI,GAAG,aAAaxC,KAAK,CAACyC,aAAa,CAAC3C,sBAAsB,EAAE,IAAI;MACtE,CAAC,GAAGyC,YAAY;MAChBG,gBAAgB,GAAG1D,MAAM,CAACuD,YAAY,EAAE,CAAC,MAAM,CAAC,CAAC;IACnD,MAAMI,WAAW,GAAG,aAAa3C,KAAK,CAACyC,aAAa,CAACrC,OAAO,EAAEf,MAAM,CAACuD,MAAM,CAAC,CAAC,CAAC,EAAEF,gBAAgB,CAAC,EAAE,aAAa1C,KAAK,CAAC6C,YAAY,CAACL,IAAI,EAAE;MACvIP,SAAS,KAAAF,MAAA,CAAKnB,SAAS,kBAAe;MACtCH,KAAK,EAAE;IACT,CAAC,CAAC,CAAC;IACHyB,aAAa,GAAG,aAAalC,KAAK,CAACyC,aAAa,CAACzC,KAAK,CAAC8C,QAAQ,EAAE,IAAI,EAAEZ,aAAa,EAAES,WAAW,CAAC;EACpG;EACA,IAAIxB,YAAY,KAAK,UAAU,IAAI,CAACD,QAAQ,EAAE;IAC5CgB,aAAa,GAAG,aAAalC,KAAK,CAACyC,aAAa,CAACzC,KAAK,CAAC8C,QAAQ,EAAE,IAAI,EAAEZ,aAAa,EAAE,aAAalC,KAAK,CAACyC,aAAa,CAAC,MAAM,EAAE;MAC7HR,SAAS,KAAAF,MAAA,CAAKnB,SAAS,mBAAgB;MACvCH,KAAK,EAAE;IACT,CAAC,EAAE,CAACY,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC0B,QAAQ,MAAM,CAAC3B,EAAE,GAAGlB,aAAa,CAAC8C,IAAI,MAAM,IAAI,IAAI5B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC2B,QAAQ,CAAC,CAAC,CAAC;EACpK;EACA,MAAME,cAAc,GAAGlD,UAAU,CAAC;IAChC,IAAAgC,MAAA,CAAInB,SAAS,sBAAmBM,QAAQ;IACxC,IAAAa,MAAA,CAAInB,SAAS,oCAAiCO,YAAY,KAAK,UAAU;IACzE,IAAAY,MAAA,CAAInB,SAAS,sBAAmB,CAACuB;EACnC,CAAC,CAAC;EACF,OAAO,aAAanC,KAAK,CAACyC,aAAa,CAACxC,GAAG,EAAEZ,MAAM,CAACuD,MAAM,CAAC,CAAC,CAAC,EAAEhB,cAAc,EAAE;IAC7EK,SAAS,EAAED;EACb,CAAC,CAAC,EAAE,aAAahC,KAAK,CAACyC,aAAa,CAAC,OAAO,EAAE;IAC5C3B,OAAO,EAAEA,OAAO;IAChBmB,SAAS,EAAEgB,cAAc;IACzBxC,KAAK,EAAE,OAAOI,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAG;EAC7C,CAAC,EAAEqB,aAAa,CAAC,CAAC;AACpB,CAAC;AACD,eAAexB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}