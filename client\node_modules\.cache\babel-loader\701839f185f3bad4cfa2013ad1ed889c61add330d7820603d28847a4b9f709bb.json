{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\admin\\\\Skills\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSelector } from 'react-redux';\nimport { message, Modal, Button, Table, Space, Tag, Input, Select, Form } from 'antd';\nimport { FaPlus, FaEdit, FaTrash, FaEye, FaVideo, FaStar, FaSearch, FaFilter, FaUpload } from 'react-icons/fa';\nimport { getAllSkillsAdmin, createSkill, updateSkill, deleteSkill } from '../../../apicalls/skills';\nimport './Skills.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst {\n  TextArea\n} = Input;\nconst AdminSkills = () => {\n  _s();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const [skills, setSkills] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingSkill, setEditingSkill] = useState(null);\n  const [form] = Form.useForm();\n  const [videoFile, setVideoFile] = useState(null);\n  const [uploading, setUploading] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [levelFilter, setLevelFilter] = useState('all');\n\n  // Skill levels\n  const skillLevels = [{\n    value: 'beginner',\n    label: 'Beginner',\n    color: 'green'\n  }, {\n    value: 'amateur',\n    label: 'Amateur',\n    color: 'blue'\n  }, {\n    value: 'professional',\n    label: 'Professional',\n    color: 'orange'\n  }, {\n    value: 'expert',\n    label: 'Expert',\n    color: 'red'\n  }];\n\n  // Target audiences\n  const targetAudiences = [{\n    value: 'all',\n    label: 'All Levels'\n  }, {\n    value: 'primary',\n    label: 'Primary'\n  }, {\n    value: 'primary_kiswahili',\n    label: 'Primary Kiswahili'\n  }, {\n    value: 'secondary',\n    label: 'Secondary'\n  }, {\n    value: 'advance',\n    label: 'Advanced'\n  }];\n  useEffect(() => {\n    fetchSkills();\n  }, []);\n  const fetchSkills = async () => {\n    setLoading(true);\n    try {\n      const response = await getAllSkillsAdmin({\n        page: 1,\n        limit: 100,\n        sortBy: 'createdAt',\n        sortOrder: 'desc'\n      });\n      if (response.success) {\n        setSkills(response.data);\n      } else {\n        message.error('Failed to fetch skills');\n      }\n    } catch (error) {\n      message.error('Error fetching skills');\n      console.error('Error:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleVideoUpload = async file => {\n    if (!file) return null;\n    setUploading(true);\n    try {\n      // For now, we'll just use the file name as a placeholder\n      // In a real implementation, you would upload to your preferred storage service\n      message.info('Video file selected. Please provide a video URL for now.');\n      return null;\n    } catch (error) {\n      message.error('Error handling video file');\n      console.error('Upload error:', error);\n      return null;\n    } finally {\n      setUploading(false);\n    }\n  };\n  const handleSubmit = async values => {\n    try {\n      let videoUrl = values.videoUrl;\n\n      // Upload video if file is selected\n      if (videoFile) {\n        videoUrl = await handleVideoUpload(videoFile);\n        if (!videoUrl) return;\n      }\n      const skillData = {\n        ...values,\n        videoUrl,\n        createdBy: user._id,\n        userId: user._id\n      };\n      let response;\n      if (editingSkill) {\n        response = await updateSkill(editingSkill._id, skillData);\n      } else {\n        response = await createSkill(skillData);\n      }\n      if (response.success) {\n        message.success(`Skill ${editingSkill ? 'updated' : 'created'} successfully`);\n        setModalVisible(false);\n        setEditingSkill(null);\n        setVideoFile(null);\n        form.resetFields();\n        fetchSkills();\n      } else {\n        message.error(response.message || 'Operation failed');\n      }\n    } catch (error) {\n      message.error('Error saving skill');\n      console.error('Error:', error);\n    }\n  };\n  const handleEdit = skill => {\n    var _skill$tags;\n    setEditingSkill(skill);\n    form.setFieldsValue({\n      title: skill.title,\n      description: skill.description,\n      level: skill.level,\n      category: skill.category,\n      subject: skill.subject,\n      targetAudience: skill.targetAudience,\n      videoUrl: skill.videoUrl,\n      duration: skill.duration,\n      estimatedTime: skill.estimatedTime,\n      difficulty: skill.difficulty,\n      isActive: skill.isActive,\n      isFeatured: skill.isFeatured,\n      tags: (_skill$tags = skill.tags) === null || _skill$tags === void 0 ? void 0 : _skill$tags.join(', ')\n    });\n    setModalVisible(true);\n  };\n  const handleDelete = async skillId => {\n    Modal.confirm({\n      title: 'Delete Skill',\n      content: 'Are you sure you want to delete this skill? This action cannot be undone.',\n      okText: 'Delete',\n      okType: 'danger',\n      cancelText: 'Cancel',\n      onOk: async () => {\n        try {\n          const response = await deleteSkill(skillId);\n          if (response.success) {\n            message.success('Skill deleted successfully');\n            fetchSkills();\n          } else {\n            message.error('Failed to delete skill');\n          }\n        } catch (error) {\n          message.error('Error deleting skill');\n          console.error('Error:', error);\n        }\n      }\n    });\n  };\n  const openCreateModal = () => {\n    setEditingSkill(null);\n    setVideoFile(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  // Filter skills based on search and level\n  const filteredSkills = skills.filter(skill => {\n    var _skill$description;\n    const matchesSearch = skill.title.toLowerCase().includes(searchTerm.toLowerCase()) || ((_skill$description = skill.description) === null || _skill$description === void 0 ? void 0 : _skill$description.toLowerCase().includes(searchTerm.toLowerCase())) || skill.category.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesLevel = levelFilter === 'all' || skill.level === levelFilter;\n    return matchesSearch && matchesLevel;\n  });\n  const columns = [{\n    title: 'Title',\n    dataIndex: 'title',\n    key: 'title',\n    width: 200,\n    render: (text, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"font-semibold\",\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-xs text-gray-500\",\n        children: record.category\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: 'Level',\n    dataIndex: 'level',\n    key: 'level',\n    width: 100,\n    render: level => {\n      const levelConfig = skillLevels.find(l => l.value === level);\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: (levelConfig === null || levelConfig === void 0 ? void 0 : levelConfig.color) || 'default',\n        children: (levelConfig === null || levelConfig === void 0 ? void 0 : levelConfig.label) || level\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    title: 'Target Audience',\n    dataIndex: 'targetAudience',\n    key: 'targetAudience',\n    width: 120,\n    render: audience => {\n      const audienceConfig = targetAudiences.find(a => a.value === audience);\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: \"blue\",\n        children: (audienceConfig === null || audienceConfig === void 0 ? void 0 : audienceConfig.label) || audience\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 16\n      }, this);\n    }\n  }, {\n    title: 'Status',\n    key: 'status',\n    width: 100,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col gap-1\",\n      children: [/*#__PURE__*/_jsxDEV(Tag, {\n        color: record.isActive ? 'green' : 'red',\n        children: record.isActive ? 'Active' : 'Inactive'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 11\n      }, this), record.isFeatured && /*#__PURE__*/_jsxDEV(Tag, {\n        color: \"gold\",\n        children: \"Featured\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 33\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 237,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: 'Video',\n    key: 'video',\n    width: 80,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center\",\n      children: record.videoUrl ? /*#__PURE__*/_jsxDEV(FaVideo, {\n        className: \"text-green-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-gray-400\",\n        children: \"No video\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: 'Views',\n    dataIndex: 'viewCount',\n    key: 'viewCount',\n    width: 80,\n    render: count => count || 0\n  }, {\n    title: 'Actions',\n    key: 'actions',\n    width: 150,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(FaEdit, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleEdit(record),\n        children: \"Edit\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        danger: true,\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(FaTrash, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleDelete(record._id),\n        children: \"Delete\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"admin-skills-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"skills-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"page-title\",\n          children: [/*#__PURE__*/_jsxDEV(FaVideo, {\n            className: \"title-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 13\n          }, this), \"Skills Management\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"page-description\",\n          children: \"Manage skill videos and learning content for students\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        size: \"large\",\n        icon: /*#__PURE__*/_jsxDEV(FaPlus, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 17\n        }, this),\n        onClick: openCreateModal,\n        className: \"create-btn\",\n        children: \"Add New Skill\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"skills-filters\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-group\",\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          placeholder: \"Search skills...\",\n          prefix: /*#__PURE__*/_jsxDEV(FaSearch, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 21\n          }, this),\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          className: \"search-input\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-group\",\n        children: /*#__PURE__*/_jsxDEV(Select, {\n          value: levelFilter,\n          onChange: setLevelFilter,\n          className: \"level-filter\",\n          placeholder: \"Filter by level\",\n          children: [/*#__PURE__*/_jsxDEV(Option, {\n            value: \"all\",\n            children: \"All Levels\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this), skillLevels.map(level => /*#__PURE__*/_jsxDEV(Option, {\n            value: level.value,\n            children: level.label\n          }, level.value, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"skills-table-container\",\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: filteredSkills,\n        rowKey: \"_id\",\n        loading: loading,\n        pagination: {\n          pageSize: 10,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} skills`\n        },\n        scroll: {\n          x: 1000\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 347,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingSkill ? 'Edit Skill' : 'Create New Skill',\n      open: modalVisible,\n      onCancel: () => {\n        setModalVisible(false);\n        setEditingSkill(null);\n        setVideoFile(null);\n        form.resetFields();\n      },\n      footer: null,\n      width: 800,\n      className: \"skill-modal\",\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSubmit,\n        className: \"skill-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"title\",\n            label: \"Skill Title\",\n            rules: [{\n              required: true,\n              message: 'Please enter skill title'\n            }],\n            className: \"form-item-half\",\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"Enter skill title\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"level\",\n            label: \"Skill Level\",\n            rules: [{\n              required: true,\n              message: 'Please select skill level'\n            }],\n            className: \"form-item-half\",\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"Select skill level\",\n              children: skillLevels.map(level => /*#__PURE__*/_jsxDEV(Option, {\n                value: level.value,\n                children: level.label\n              }, level.value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"Description\",\n          rules: [{\n            required: true,\n            message: 'Please enter description'\n          }],\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 3,\n            placeholder: \"Enter skill description\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"category\",\n            label: \"Category\",\n            rules: [{\n              required: true,\n              message: 'Please enter category'\n            }],\n            className: \"form-item-half\",\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"e.g., Programming, Design, etc.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"subject\",\n            label: \"Subject\",\n            className: \"form-item-half\",\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"Related subject\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"targetAudience\",\n            label: \"Target Audience\",\n            rules: [{\n              required: true,\n              message: 'Please select target audience'\n            }],\n            className: \"form-item-half\",\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"Select target audience\",\n              children: targetAudiences.map(audience => /*#__PURE__*/_jsxDEV(Option, {\n                value: audience.value,\n                children: audience.label\n              }, audience.value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"difficulty\",\n            label: \"Difficulty (1-5)\",\n            className: \"form-item-half\",\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"Select difficulty\",\n              children: [1, 2, 3, 4, 5].map(num => /*#__PURE__*/_jsxDEV(Option, {\n                value: num,\n                children: [num, \" \", '⭐'.repeat(num)]\n              }, num, true, {\n                fileName: _jsxFileName,\n                lineNumber: 460,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"video-upload-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Video Content\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"videoUrl\",\n            label: \"Video URL (Optional - if you have a direct link)\",\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"https://example.com/video.mp4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"file-upload-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"upload-label\",\n              children: [/*#__PURE__*/_jsxDEV(FaUpload, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 17\n              }, this), \" Upload Video File\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"file\",\n              accept: \"video/*\",\n              onChange: e => setVideoFile(e.target.files[0]),\n              className: \"file-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 15\n            }, this), videoFile && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"file-info\",\n              children: [\"Selected: \", videoFile.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 469,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"duration\",\n            label: \"Video Duration\",\n            className: \"form-item-half\",\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"e.g., 10:30\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 498,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"estimatedTime\",\n            label: \"Estimated Learning Time\",\n            className: \"form-item-half\",\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"e.g., 30 minutes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"tags\",\n          label: \"Tags (comma separated)\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"programming, javascript, tutorial\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 519,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 515,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"isActive\",\n            label: \"Status\",\n            className: \"form-item-half\",\n            initialValue: true,\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              children: [/*#__PURE__*/_jsxDEV(Option, {\n                value: true,\n                children: \"Active\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: false,\n                children: \"Inactive\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 531,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"isFeatured\",\n            label: \"Featured\",\n            className: \"form-item-half\",\n            initialValue: false,\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              children: [/*#__PURE__*/_jsxDEV(Option, {\n                value: true,\n                children: \"Yes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 542,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: false,\n                children: \"No\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 535,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 522,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-actions\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"default\",\n            onClick: () => {\n              setModalVisible(false);\n              setEditingSkill(null);\n              setVideoFile(null);\n              form.resetFields();\n            },\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            htmlType: \"submit\",\n            loading: uploading,\n            icon: editingSkill ? /*#__PURE__*/_jsxDEV(FaEdit, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 564,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(FaPlus, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 564,\n              columnNumber: 49\n            }, this),\n            children: uploading ? 'Uploading...' : editingSkill ? 'Update Skill' : 'Create Skill'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 548,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 365,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 295,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminSkills, \"tS0jM5Or80rQ/LRY0Ceax1/t7Js=\", false, function () {\n  return [useSelector, Form.useForm];\n});\n_c = AdminSkills;\nexport default AdminSkills;\nvar _c;\n$RefreshReg$(_c, \"AdminSkills\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSelector", "message", "Modal", "<PERSON><PERSON>", "Table", "Space", "Tag", "Input", "Select", "Form", "FaPlus", "FaEdit", "FaTrash", "FaEye", "FaVideo", "FaStar", "FaSearch", "FaFilter", "FaUpload", "getAllSkillsAdmin", "createSkill", "updateSkill", "deleteSkill", "jsxDEV", "_jsxDEV", "Option", "TextArea", "AdminSkills", "_s", "user", "state", "skills", "setSkills", "loading", "setLoading", "modalVisible", "setModalVisible", "editingSkill", "setEditingSkill", "form", "useForm", "videoFile", "setVideoFile", "uploading", "setUploading", "searchTerm", "setSearchTerm", "levelFilter", "setLevelFilter", "skillLevels", "value", "label", "color", "targetAudiences", "fetchSkills", "response", "page", "limit", "sortBy", "sortOrder", "success", "data", "error", "console", "handleVideoUpload", "file", "info", "handleSubmit", "values", "videoUrl", "skillData", "created<PERSON>y", "_id", "userId", "resetFields", "handleEdit", "skill", "_skill$tags", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title", "description", "level", "category", "subject", "targetAudience", "duration", "estimatedTime", "difficulty", "isActive", "isFeatured", "tags", "join", "handleDelete", "skillId", "confirm", "content", "okText", "okType", "cancelText", "onOk", "openCreateModal", "filteredSkills", "filter", "_skill$description", "matchesSearch", "toLowerCase", "includes", "matchesLevel", "columns", "dataIndex", "key", "width", "render", "text", "record", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "levelConfig", "find", "l", "audience", "audienceConfig", "a", "_", "count", "type", "size", "icon", "onClick", "danger", "placeholder", "prefix", "onChange", "e", "target", "map", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "total", "range", "scroll", "x", "open", "onCancel", "footer", "layout", "onFinish", "<PERSON><PERSON>", "name", "rules", "required", "rows", "num", "repeat", "accept", "files", "initialValue", "htmlType", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/admin/Skills/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSelector } from 'react-redux';\nimport { message, Modal, Button, Table, Space, Tag, Input, Select, Form } from 'antd';\nimport { \n  FaPlus, \n  FaEdit, \n  FaTrash, \n  FaEye, \n  FaVideo, \n  FaStar,\n  FaSearch,\n  FaFilter,\n  FaUpload\n} from 'react-icons/fa';\nimport { getAllSkillsAdmin, createSkill, updateSkill, deleteSkill } from '../../../apicalls/skills';\nimport './Skills.css';\n\nconst { Option } = Select;\nconst { TextArea } = Input;\n\nconst AdminSkills = () => {\n  const { user } = useSelector((state) => state.user);\n  const [skills, setSkills] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingSkill, setEditingSkill] = useState(null);\n  const [form] = Form.useForm();\n  const [videoFile, setVideoFile] = useState(null);\n  const [uploading, setUploading] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [levelFilter, setLevelFilter] = useState('all');\n\n  // Skill levels\n  const skillLevels = [\n    { value: 'beginner', label: 'Beginner', color: 'green' },\n    { value: 'amateur', label: 'Amateur', color: 'blue' },\n    { value: 'professional', label: 'Professional', color: 'orange' },\n    { value: 'expert', label: 'Expert', color: 'red' }\n  ];\n\n  // Target audiences\n  const targetAudiences = [\n    { value: 'all', label: 'All Levels' },\n    { value: 'primary', label: 'Primary' },\n    { value: 'primary_kiswahili', label: 'Primary Kiswahili' },\n    { value: 'secondary', label: 'Secondary' },\n    { value: 'advance', label: 'Advanced' }\n  ];\n\n  useEffect(() => {\n    fetchSkills();\n  }, []);\n\n  const fetchSkills = async () => {\n    setLoading(true);\n    try {\n      const response = await getAllSkillsAdmin({\n        page: 1,\n        limit: 100,\n        sortBy: 'createdAt',\n        sortOrder: 'desc'\n      });\n      \n      if (response.success) {\n        setSkills(response.data);\n      } else {\n        message.error('Failed to fetch skills');\n      }\n    } catch (error) {\n      message.error('Error fetching skills');\n      console.error('Error:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleVideoUpload = async (file) => {\n    if (!file) return null;\n\n    setUploading(true);\n    try {\n      // For now, we'll just use the file name as a placeholder\n      // In a real implementation, you would upload to your preferred storage service\n      message.info('Video file selected. Please provide a video URL for now.');\n      return null;\n    } catch (error) {\n      message.error('Error handling video file');\n      console.error('Upload error:', error);\n      return null;\n    } finally {\n      setUploading(false);\n    }\n  };\n\n  const handleSubmit = async (values) => {\n    try {\n      let videoUrl = values.videoUrl;\n      \n      // Upload video if file is selected\n      if (videoFile) {\n        videoUrl = await handleVideoUpload(videoFile);\n        if (!videoUrl) return;\n      }\n\n      const skillData = {\n        ...values,\n        videoUrl,\n        createdBy: user._id,\n        userId: user._id\n      };\n\n      let response;\n      if (editingSkill) {\n        response = await updateSkill(editingSkill._id, skillData);\n      } else {\n        response = await createSkill(skillData);\n      }\n\n      if (response.success) {\n        message.success(`Skill ${editingSkill ? 'updated' : 'created'} successfully`);\n        setModalVisible(false);\n        setEditingSkill(null);\n        setVideoFile(null);\n        form.resetFields();\n        fetchSkills();\n      } else {\n        message.error(response.message || 'Operation failed');\n      }\n    } catch (error) {\n      message.error('Error saving skill');\n      console.error('Error:', error);\n    }\n  };\n\n  const handleEdit = (skill) => {\n    setEditingSkill(skill);\n    form.setFieldsValue({\n      title: skill.title,\n      description: skill.description,\n      level: skill.level,\n      category: skill.category,\n      subject: skill.subject,\n      targetAudience: skill.targetAudience,\n      videoUrl: skill.videoUrl,\n      duration: skill.duration,\n      estimatedTime: skill.estimatedTime,\n      difficulty: skill.difficulty,\n      isActive: skill.isActive,\n      isFeatured: skill.isFeatured,\n      tags: skill.tags?.join(', ')\n    });\n    setModalVisible(true);\n  };\n\n  const handleDelete = async (skillId) => {\n    Modal.confirm({\n      title: 'Delete Skill',\n      content: 'Are you sure you want to delete this skill? This action cannot be undone.',\n      okText: 'Delete',\n      okType: 'danger',\n      cancelText: 'Cancel',\n      onOk: async () => {\n        try {\n          const response = await deleteSkill(skillId);\n          if (response.success) {\n            message.success('Skill deleted successfully');\n            fetchSkills();\n          } else {\n            message.error('Failed to delete skill');\n          }\n        } catch (error) {\n          message.error('Error deleting skill');\n          console.error('Error:', error);\n        }\n      }\n    });\n  };\n\n  const openCreateModal = () => {\n    setEditingSkill(null);\n    setVideoFile(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  // Filter skills based on search and level\n  const filteredSkills = skills.filter(skill => {\n    const matchesSearch = skill.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         skill.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         skill.category.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesLevel = levelFilter === 'all' || skill.level === levelFilter;\n    return matchesSearch && matchesLevel;\n  });\n\n  const columns = [\n    {\n      title: 'Title',\n      dataIndex: 'title',\n      key: 'title',\n      width: 200,\n      render: (text, record) => (\n        <div>\n          <div className=\"font-semibold\">{text}</div>\n          <div className=\"text-xs text-gray-500\">{record.category}</div>\n        </div>\n      )\n    },\n    {\n      title: 'Level',\n      dataIndex: 'level',\n      key: 'level',\n      width: 100,\n      render: (level) => {\n        const levelConfig = skillLevels.find(l => l.value === level);\n        return (\n          <Tag color={levelConfig?.color || 'default'}>\n            {levelConfig?.label || level}\n          </Tag>\n        );\n      }\n    },\n    {\n      title: 'Target Audience',\n      dataIndex: 'targetAudience',\n      key: 'targetAudience',\n      width: 120,\n      render: (audience) => {\n        const audienceConfig = targetAudiences.find(a => a.value === audience);\n        return <Tag color=\"blue\">{audienceConfig?.label || audience}</Tag>;\n      }\n    },\n    {\n      title: 'Status',\n      key: 'status',\n      width: 100,\n      render: (_, record) => (\n        <div className=\"flex flex-col gap-1\">\n          <Tag color={record.isActive ? 'green' : 'red'}>\n            {record.isActive ? 'Active' : 'Inactive'}\n          </Tag>\n          {record.isFeatured && <Tag color=\"gold\">Featured</Tag>}\n        </div>\n      )\n    },\n    {\n      title: 'Video',\n      key: 'video',\n      width: 80,\n      render: (_, record) => (\n        <div className=\"text-center\">\n          {record.videoUrl ? (\n            <FaVideo className=\"text-green-500\" />\n          ) : (\n            <span className=\"text-gray-400\">No video</span>\n          )}\n        </div>\n      )\n    },\n    {\n      title: 'Views',\n      dataIndex: 'viewCount',\n      key: 'viewCount',\n      width: 80,\n      render: (count) => count || 0\n    },\n    {\n      title: 'Actions',\n      key: 'actions',\n      width: 150,\n      render: (_, record) => (\n        <Space>\n          <Button\n            type=\"primary\"\n            size=\"small\"\n            icon={<FaEdit />}\n            onClick={() => handleEdit(record)}\n          >\n            Edit\n          </Button>\n          <Button\n            type=\"primary\"\n            danger\n            size=\"small\"\n            icon={<FaTrash />}\n            onClick={() => handleDelete(record._id)}\n          >\n            Delete\n          </Button>\n        </Space>\n      )\n    }\n  ];\n\n  return (\n    <div className=\"admin-skills-container\">\n      <div className=\"skills-header\">\n        <div className=\"header-content\">\n          <h1 className=\"page-title\">\n            <FaVideo className=\"title-icon\" />\n            Skills Management\n          </h1>\n          <p className=\"page-description\">\n            Manage skill videos and learning content for students\n          </p>\n        </div>\n        \n        <Button\n          type=\"primary\"\n          size=\"large\"\n          icon={<FaPlus />}\n          onClick={openCreateModal}\n          className=\"create-btn\"\n        >\n          Add New Skill\n        </Button>\n      </div>\n\n      {/* Filters */}\n      <div className=\"skills-filters\">\n        <div className=\"filter-group\">\n          <Input\n            placeholder=\"Search skills...\"\n            prefix={<FaSearch />}\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"search-input\"\n          />\n        </div>\n        <div className=\"filter-group\">\n          <Select\n            value={levelFilter}\n            onChange={setLevelFilter}\n            className=\"level-filter\"\n            placeholder=\"Filter by level\"\n          >\n            <Option value=\"all\">All Levels</Option>\n            {skillLevels.map(level => (\n              <Option key={level.value} value={level.value}>\n                {level.label}\n              </Option>\n            ))}\n          </Select>\n        </div>\n      </div>\n\n      {/* Skills Table */}\n      <div className=\"skills-table-container\">\n        <Table\n          columns={columns}\n          dataSource={filteredSkills}\n          rowKey=\"_id\"\n          loading={loading}\n          pagination={{\n            pageSize: 10,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) => \n              `${range[0]}-${range[1]} of ${total} skills`\n          }}\n          scroll={{ x: 1000 }}\n        />\n      </div>\n\n      {/* Create/Edit Modal */}\n      <Modal\n        title={editingSkill ? 'Edit Skill' : 'Create New Skill'}\n        open={modalVisible}\n        onCancel={() => {\n          setModalVisible(false);\n          setEditingSkill(null);\n          setVideoFile(null);\n          form.resetFields();\n        }}\n        footer={null}\n        width={800}\n        className=\"skill-modal\"\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSubmit}\n          className=\"skill-form\"\n        >\n          <div className=\"form-row\">\n            <Form.Item\n              name=\"title\"\n              label=\"Skill Title\"\n              rules={[{ required: true, message: 'Please enter skill title' }]}\n              className=\"form-item-half\"\n            >\n              <Input placeholder=\"Enter skill title\" />\n            </Form.Item>\n\n            <Form.Item\n              name=\"level\"\n              label=\"Skill Level\"\n              rules={[{ required: true, message: 'Please select skill level' }]}\n              className=\"form-item-half\"\n            >\n              <Select placeholder=\"Select skill level\">\n                {skillLevels.map(level => (\n                  <Option key={level.value} value={level.value}>\n                    {level.label}\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </div>\n\n          <Form.Item\n            name=\"description\"\n            label=\"Description\"\n            rules={[{ required: true, message: 'Please enter description' }]}\n          >\n            <TextArea rows={3} placeholder=\"Enter skill description\" />\n          </Form.Item>\n\n          <div className=\"form-row\">\n            <Form.Item\n              name=\"category\"\n              label=\"Category\"\n              rules={[{ required: true, message: 'Please enter category' }]}\n              className=\"form-item-half\"\n            >\n              <Input placeholder=\"e.g., Programming, Design, etc.\" />\n            </Form.Item>\n\n            <Form.Item\n              name=\"subject\"\n              label=\"Subject\"\n              className=\"form-item-half\"\n            >\n              <Input placeholder=\"Related subject\" />\n            </Form.Item>\n          </div>\n\n          <div className=\"form-row\">\n            <Form.Item\n              name=\"targetAudience\"\n              label=\"Target Audience\"\n              rules={[{ required: true, message: 'Please select target audience' }]}\n              className=\"form-item-half\"\n            >\n              <Select placeholder=\"Select target audience\">\n                {targetAudiences.map(audience => (\n                  <Option key={audience.value} value={audience.value}>\n                    {audience.label}\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n\n            <Form.Item\n              name=\"difficulty\"\n              label=\"Difficulty (1-5)\"\n              className=\"form-item-half\"\n            >\n              <Select placeholder=\"Select difficulty\">\n                {[1, 2, 3, 4, 5].map(num => (\n                  <Option key={num} value={num}>\n                    {num} {'⭐'.repeat(num)}\n                  </Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </div>\n\n          {/* Video Upload Section */}\n          <div className=\"video-upload-section\">\n            <h4>Video Content</h4>\n            \n            <Form.Item\n              name=\"videoUrl\"\n              label=\"Video URL (Optional - if you have a direct link)\"\n            >\n              <Input placeholder=\"https://example.com/video.mp4\" />\n            </Form.Item>\n\n            <div className=\"file-upload-section\">\n              <label className=\"upload-label\">\n                <FaUpload /> Upload Video File\n              </label>\n              <input\n                type=\"file\"\n                accept=\"video/*\"\n                onChange={(e) => setVideoFile(e.target.files[0])}\n                className=\"file-input\"\n              />\n              {videoFile && (\n                <div className=\"file-info\">\n                  Selected: {videoFile.name}\n                </div>\n              )}\n            </div>\n          </div>\n\n          <div className=\"form-row\">\n            <Form.Item\n              name=\"duration\"\n              label=\"Video Duration\"\n              className=\"form-item-half\"\n            >\n              <Input placeholder=\"e.g., 10:30\" />\n            </Form.Item>\n\n            <Form.Item\n              name=\"estimatedTime\"\n              label=\"Estimated Learning Time\"\n              className=\"form-item-half\"\n            >\n              <Input placeholder=\"e.g., 30 minutes\" />\n            </Form.Item>\n          </div>\n\n          <Form.Item\n            name=\"tags\"\n            label=\"Tags (comma separated)\"\n          >\n            <Input placeholder=\"programming, javascript, tutorial\" />\n          </Form.Item>\n\n          <div className=\"form-row\">\n            <Form.Item\n              name=\"isActive\"\n              label=\"Status\"\n              className=\"form-item-half\"\n              initialValue={true}\n            >\n              <Select>\n                <Option value={true}>Active</Option>\n                <Option value={false}>Inactive</Option>\n              </Select>\n            </Form.Item>\n\n            <Form.Item\n              name=\"isFeatured\"\n              label=\"Featured\"\n              className=\"form-item-half\"\n              initialValue={false}\n            >\n              <Select>\n                <Option value={true}>Yes</Option>\n                <Option value={false}>No</Option>\n              </Select>\n            </Form.Item>\n          </div>\n\n          <div className=\"form-actions\">\n            <Button\n              type=\"default\"\n              onClick={() => {\n                setModalVisible(false);\n                setEditingSkill(null);\n                setVideoFile(null);\n                form.resetFields();\n              }}\n            >\n              Cancel\n            </Button>\n            <Button\n              type=\"primary\"\n              htmlType=\"submit\"\n              loading={uploading}\n              icon={editingSkill ? <FaEdit /> : <FaPlus />}\n            >\n              {uploading ? 'Uploading...' : (editingSkill ? 'Update Skill' : 'Create Skill')}\n            </Button>\n          </div>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default AdminSkills;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,OAAO,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,GAAG,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,QAAQ,MAAM;AACrF,SACEC,MAAM,EACNC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,QAAQ,EACRC,QAAQ,QACH,gBAAgB;AACvB,SAASC,iBAAiB,EAAEC,WAAW,EAAEC,WAAW,EAAEC,WAAW,QAAQ,0BAA0B;AACnG,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAM;EAAEC;AAAO,CAAC,GAAGjB,MAAM;AACzB,MAAM;EAAEkB;AAAS,CAAC,GAAGnB,KAAK;AAE1B,MAAMoB,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC;EAAK,CAAC,GAAG7B,WAAW,CAAE8B,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM,CAACE,MAAM,EAAEC,SAAS,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACmC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqC,YAAY,EAAEC,eAAe,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACuC,YAAY,EAAEC,eAAe,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACyC,IAAI,CAAC,GAAG9B,IAAI,CAAC+B,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC6C,SAAS,EAAEC,YAAY,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC+C,UAAU,EAAEC,aAAa,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiD,WAAW,EAAEC,cAAc,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACA,MAAMmD,WAAW,GAAG,CAClB;IAAEC,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAQ,CAAC,EACxD;IAAEF,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAO,CAAC,EACrD;IAAEF,KAAK,EAAE,cAAc;IAAEC,KAAK,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAS,CAAC,EACjE;IAAEF,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAM,CAAC,CACnD;;EAED;EACA,MAAMC,eAAe,GAAG,CACtB;IAAEH,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAa,CAAC,EACrC;IAAED,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,EACtC;IAAED,KAAK,EAAE,mBAAmB;IAAEC,KAAK,EAAE;EAAoB,CAAC,EAC1D;IAAED,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAY,CAAC,EAC1C;IAAED,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAW,CAAC,CACxC;EAEDpD,SAAS,CAAC,MAAM;IACduD,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9BpB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMqB,QAAQ,GAAG,MAAMpC,iBAAiB,CAAC;QACvCqC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,GAAG;QACVC,MAAM,EAAE,WAAW;QACnBC,SAAS,EAAE;MACb,CAAC,CAAC;MAEF,IAAIJ,QAAQ,CAACK,OAAO,EAAE;QACpB5B,SAAS,CAACuB,QAAQ,CAACM,IAAI,CAAC;MAC1B,CAAC,MAAM;QACL5D,OAAO,CAAC6D,KAAK,CAAC,wBAAwB,CAAC;MACzC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd7D,OAAO,CAAC6D,KAAK,CAAC,uBAAuB,CAAC;MACtCC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;IAChC,CAAC,SAAS;MACR5B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM8B,iBAAiB,GAAG,MAAOC,IAAI,IAAK;IACxC,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI;IAEtBrB,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF;MACA;MACA3C,OAAO,CAACiE,IAAI,CAAC,0DAA0D,CAAC;MACxE,OAAO,IAAI;IACb,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACd7D,OAAO,CAAC6D,KAAK,CAAC,2BAA2B,CAAC;MAC1CC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC,OAAO,IAAI;IACb,CAAC,SAAS;MACRlB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMuB,YAAY,GAAG,MAAOC,MAAM,IAAK;IACrC,IAAI;MACF,IAAIC,QAAQ,GAAGD,MAAM,CAACC,QAAQ;;MAE9B;MACA,IAAI5B,SAAS,EAAE;QACb4B,QAAQ,GAAG,MAAML,iBAAiB,CAACvB,SAAS,CAAC;QAC7C,IAAI,CAAC4B,QAAQ,EAAE;MACjB;MAEA,MAAMC,SAAS,GAAG;QAChB,GAAGF,MAAM;QACTC,QAAQ;QACRE,SAAS,EAAE1C,IAAI,CAAC2C,GAAG;QACnBC,MAAM,EAAE5C,IAAI,CAAC2C;MACf,CAAC;MAED,IAAIjB,QAAQ;MACZ,IAAIlB,YAAY,EAAE;QAChBkB,QAAQ,GAAG,MAAMlC,WAAW,CAACgB,YAAY,CAACmC,GAAG,EAAEF,SAAS,CAAC;MAC3D,CAAC,MAAM;QACLf,QAAQ,GAAG,MAAMnC,WAAW,CAACkD,SAAS,CAAC;MACzC;MAEA,IAAIf,QAAQ,CAACK,OAAO,EAAE;QACpB3D,OAAO,CAAC2D,OAAO,CAAE,SAAQvB,YAAY,GAAG,SAAS,GAAG,SAAU,eAAc,CAAC;QAC7ED,eAAe,CAAC,KAAK,CAAC;QACtBE,eAAe,CAAC,IAAI,CAAC;QACrBI,YAAY,CAAC,IAAI,CAAC;QAClBH,IAAI,CAACmC,WAAW,CAAC,CAAC;QAClBpB,WAAW,CAAC,CAAC;MACf,CAAC,MAAM;QACLrD,OAAO,CAAC6D,KAAK,CAACP,QAAQ,CAACtD,OAAO,IAAI,kBAAkB,CAAC;MACvD;IACF,CAAC,CAAC,OAAO6D,KAAK,EAAE;MACd7D,OAAO,CAAC6D,KAAK,CAAC,oBAAoB,CAAC;MACnCC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;IAChC;EACF,CAAC;EAED,MAAMa,UAAU,GAAIC,KAAK,IAAK;IAAA,IAAAC,WAAA;IAC5BvC,eAAe,CAACsC,KAAK,CAAC;IACtBrC,IAAI,CAACuC,cAAc,CAAC;MAClBC,KAAK,EAAEH,KAAK,CAACG,KAAK;MAClBC,WAAW,EAAEJ,KAAK,CAACI,WAAW;MAC9BC,KAAK,EAAEL,KAAK,CAACK,KAAK;MAClBC,QAAQ,EAAEN,KAAK,CAACM,QAAQ;MACxBC,OAAO,EAAEP,KAAK,CAACO,OAAO;MACtBC,cAAc,EAAER,KAAK,CAACQ,cAAc;MACpCf,QAAQ,EAAEO,KAAK,CAACP,QAAQ;MACxBgB,QAAQ,EAAET,KAAK,CAACS,QAAQ;MACxBC,aAAa,EAAEV,KAAK,CAACU,aAAa;MAClCC,UAAU,EAAEX,KAAK,CAACW,UAAU;MAC5BC,QAAQ,EAAEZ,KAAK,CAACY,QAAQ;MACxBC,UAAU,EAAEb,KAAK,CAACa,UAAU;MAC5BC,IAAI,GAAAb,WAAA,GAAED,KAAK,CAACc,IAAI,cAAAb,WAAA,uBAAVA,WAAA,CAAYc,IAAI,CAAC,IAAI;IAC7B,CAAC,CAAC;IACFvD,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMwD,YAAY,GAAG,MAAOC,OAAO,IAAK;IACtC3F,KAAK,CAAC4F,OAAO,CAAC;MACZf,KAAK,EAAE,cAAc;MACrBgB,OAAO,EAAE,2EAA2E;MACpFC,MAAM,EAAE,QAAQ;MAChBC,MAAM,EAAE,QAAQ;MAChBC,UAAU,EAAE,QAAQ;MACpBC,IAAI,EAAE,MAAAA,CAAA,KAAY;QAChB,IAAI;UACF,MAAM5C,QAAQ,GAAG,MAAMjC,WAAW,CAACuE,OAAO,CAAC;UAC3C,IAAItC,QAAQ,CAACK,OAAO,EAAE;YACpB3D,OAAO,CAAC2D,OAAO,CAAC,4BAA4B,CAAC;YAC7CN,WAAW,CAAC,CAAC;UACf,CAAC,MAAM;YACLrD,OAAO,CAAC6D,KAAK,CAAC,wBAAwB,CAAC;UACzC;QACF,CAAC,CAAC,OAAOA,KAAK,EAAE;UACd7D,OAAO,CAAC6D,KAAK,CAAC,sBAAsB,CAAC;UACrCC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;QAChC;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMsC,eAAe,GAAGA,CAAA,KAAM;IAC5B9D,eAAe,CAAC,IAAI,CAAC;IACrBI,YAAY,CAAC,IAAI,CAAC;IAClBH,IAAI,CAACmC,WAAW,CAAC,CAAC;IAClBtC,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMiE,cAAc,GAAGtE,MAAM,CAACuE,MAAM,CAAC1B,KAAK,IAAI;IAAA,IAAA2B,kBAAA;IAC5C,MAAMC,aAAa,GAAG5B,KAAK,CAACG,KAAK,CAAC0B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7D,UAAU,CAAC4D,WAAW,CAAC,CAAC,CAAC,MAAAF,kBAAA,GAC7D3B,KAAK,CAACI,WAAW,cAAAuB,kBAAA,uBAAjBA,kBAAA,CAAmBE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7D,UAAU,CAAC4D,WAAW,CAAC,CAAC,CAAC,KACnE7B,KAAK,CAACM,QAAQ,CAACuB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7D,UAAU,CAAC4D,WAAW,CAAC,CAAC,CAAC;IACpF,MAAME,YAAY,GAAG5D,WAAW,KAAK,KAAK,IAAI6B,KAAK,CAACK,KAAK,KAAKlC,WAAW;IACzE,OAAOyD,aAAa,IAAIG,YAAY;EACtC,CAAC,CAAC;EAEF,MAAMC,OAAO,GAAG,CACd;IACE7B,KAAK,EAAE,OAAO;IACd8B,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,kBACnB1F,OAAA;MAAA2F,QAAA,gBACE3F,OAAA;QAAK4F,SAAS,EAAC,eAAe;QAAAD,QAAA,EAAEF;MAAI;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC3ChG,OAAA;QAAK4F,SAAS,EAAC,uBAAuB;QAAAD,QAAA,EAAED,MAAM,CAAChC;MAAQ;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3D;EAET,CAAC,EACD;IACEzC,KAAK,EAAE,OAAO;IACd8B,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAG/B,KAAK,IAAK;MACjB,MAAMwC,WAAW,GAAGxE,WAAW,CAACyE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzE,KAAK,KAAK+B,KAAK,CAAC;MAC5D,oBACEzD,OAAA,CAAClB,GAAG;QAAC8C,KAAK,EAAE,CAAAqE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAErE,KAAK,KAAI,SAAU;QAAA+D,QAAA,EACzC,CAAAM,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEtE,KAAK,KAAI8B;MAAK;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC;IAEV;EACF,CAAC,EACD;IACEzC,KAAK,EAAE,iBAAiB;IACxB8B,SAAS,EAAE,gBAAgB;IAC3BC,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGY,QAAQ,IAAK;MACpB,MAAMC,cAAc,GAAGxE,eAAe,CAACqE,IAAI,CAACI,CAAC,IAAIA,CAAC,CAAC5E,KAAK,KAAK0E,QAAQ,CAAC;MACtE,oBAAOpG,OAAA,CAAClB,GAAG;QAAC8C,KAAK,EAAC,MAAM;QAAA+D,QAAA,EAAE,CAAAU,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE1E,KAAK,KAAIyE;MAAQ;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IACpE;EACF,CAAC,EACD;IACEzC,KAAK,EAAE,QAAQ;IACf+B,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACe,CAAC,EAAEb,MAAM,kBAChB1F,OAAA;MAAK4F,SAAS,EAAC,qBAAqB;MAAAD,QAAA,gBAClC3F,OAAA,CAAClB,GAAG;QAAC8C,KAAK,EAAE8D,MAAM,CAAC1B,QAAQ,GAAG,OAAO,GAAG,KAAM;QAAA2B,QAAA,EAC3CD,MAAM,CAAC1B,QAAQ,GAAG,QAAQ,GAAG;MAAU;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC,EACLN,MAAM,CAACzB,UAAU,iBAAIjE,OAAA,CAAClB,GAAG;QAAC8C,KAAK,EAAC,MAAM;QAAA+D,QAAA,EAAC;MAAQ;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD;EAET,CAAC,EACD;IACEzC,KAAK,EAAE,OAAO;IACd+B,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAEA,CAACe,CAAC,EAAEb,MAAM,kBAChB1F,OAAA;MAAK4F,SAAS,EAAC,aAAa;MAAAD,QAAA,EACzBD,MAAM,CAAC7C,QAAQ,gBACd7C,OAAA,CAACV,OAAO;QAACsG,SAAS,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAEtChG,OAAA;QAAM4F,SAAS,EAAC,eAAe;QAAAD,QAAA,EAAC;MAAQ;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAC/C;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAET,CAAC,EACD;IACEzC,KAAK,EAAE,OAAO;IACd8B,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAGgB,KAAK,IAAKA,KAAK,IAAI;EAC9B,CAAC,EACD;IACEjD,KAAK,EAAE,SAAS;IAChB+B,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACe,CAAC,EAAEb,MAAM,kBAChB1F,OAAA,CAACnB,KAAK;MAAA8G,QAAA,gBACJ3F,OAAA,CAACrB,MAAM;QACL8H,IAAI,EAAC,SAAS;QACdC,IAAI,EAAC,OAAO;QACZC,IAAI,eAAE3G,OAAA,CAACb,MAAM;UAAA0G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACjBY,OAAO,EAAEA,CAAA,KAAMzD,UAAU,CAACuC,MAAM,CAAE;QAAAC,QAAA,EACnC;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACThG,OAAA,CAACrB,MAAM;QACL8H,IAAI,EAAC,SAAS;QACdI,MAAM;QACNH,IAAI,EAAC,OAAO;QACZC,IAAI,eAAE3G,OAAA,CAACZ,OAAO;UAAAyG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAClBY,OAAO,EAAEA,CAAA,KAAMxC,YAAY,CAACsB,MAAM,CAAC1C,GAAG,CAAE;QAAA2C,QAAA,EACzC;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAEX,CAAC,CACF;EAED,oBACEhG,OAAA;IAAK4F,SAAS,EAAC,wBAAwB;IAAAD,QAAA,gBACrC3F,OAAA;MAAK4F,SAAS,EAAC,eAAe;MAAAD,QAAA,gBAC5B3F,OAAA;QAAK4F,SAAS,EAAC,gBAAgB;QAAAD,QAAA,gBAC7B3F,OAAA;UAAI4F,SAAS,EAAC,YAAY;UAAAD,QAAA,gBACxB3F,OAAA,CAACV,OAAO;YAACsG,SAAS,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,qBAEpC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLhG,OAAA;UAAG4F,SAAS,EAAC,kBAAkB;UAAAD,QAAA,EAAC;QAEhC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENhG,OAAA,CAACrB,MAAM;QACL8H,IAAI,EAAC,SAAS;QACdC,IAAI,EAAC,OAAO;QACZC,IAAI,eAAE3G,OAAA,CAACd,MAAM;UAAA2G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACjBY,OAAO,EAAEhC,eAAgB;QACzBgB,SAAS,EAAC,YAAY;QAAAD,QAAA,EACvB;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNhG,OAAA;MAAK4F,SAAS,EAAC,gBAAgB;MAAAD,QAAA,gBAC7B3F,OAAA;QAAK4F,SAAS,EAAC,cAAc;QAAAD,QAAA,eAC3B3F,OAAA,CAACjB,KAAK;UACJ+H,WAAW,EAAC,kBAAkB;UAC9BC,MAAM,eAAE/G,OAAA,CAACR,QAAQ;YAAAqG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACrBtE,KAAK,EAAEL,UAAW;UAClB2F,QAAQ,EAAGC,CAAC,IAAK3F,aAAa,CAAC2F,CAAC,CAACC,MAAM,CAACxF,KAAK,CAAE;UAC/CkE,SAAS,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNhG,OAAA;QAAK4F,SAAS,EAAC,cAAc;QAAAD,QAAA,eAC3B3F,OAAA,CAAChB,MAAM;UACL0C,KAAK,EAAEH,WAAY;UACnByF,QAAQ,EAAExF,cAAe;UACzBoE,SAAS,EAAC,cAAc;UACxBkB,WAAW,EAAC,iBAAiB;UAAAnB,QAAA,gBAE7B3F,OAAA,CAACC,MAAM;YAACyB,KAAK,EAAC,KAAK;YAAAiE,QAAA,EAAC;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACtCvE,WAAW,CAAC0F,GAAG,CAAC1D,KAAK,iBACpBzD,OAAA,CAACC,MAAM;YAAmByB,KAAK,EAAE+B,KAAK,CAAC/B,KAAM;YAAAiE,QAAA,EAC1ClC,KAAK,CAAC9B;UAAK,GADD8B,KAAK,CAAC/B,KAAK;YAAAmE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEhB,CACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhG,OAAA;MAAK4F,SAAS,EAAC,wBAAwB;MAAAD,QAAA,eACrC3F,OAAA,CAACpB,KAAK;QACJwG,OAAO,EAAEA,OAAQ;QACjBgC,UAAU,EAAEvC,cAAe;QAC3BwC,MAAM,EAAC,KAAK;QACZ5G,OAAO,EAAEA,OAAQ;QACjB6G,UAAU,EAAE;UACVC,QAAQ,EAAE,EAAE;UACZC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAACC,KAAK,EAAEC,KAAK,KACrB,GAAEA,KAAK,CAAC,CAAC,CAAE,IAAGA,KAAK,CAAC,CAAC,CAAE,OAAMD,KAAM;QACxC,CAAE;QACFE,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK;MAAE;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNhG,OAAA,CAACtB,KAAK;MACJ6E,KAAK,EAAE1C,YAAY,GAAG,YAAY,GAAG,kBAAmB;MACxDkH,IAAI,EAAEpH,YAAa;MACnBqH,QAAQ,EAAEA,CAAA,KAAM;QACdpH,eAAe,CAAC,KAAK,CAAC;QACtBE,eAAe,CAAC,IAAI,CAAC;QACrBI,YAAY,CAAC,IAAI,CAAC;QAClBH,IAAI,CAACmC,WAAW,CAAC,CAAC;MACpB,CAAE;MACF+E,MAAM,EAAE,IAAK;MACb1C,KAAK,EAAE,GAAI;MACXK,SAAS,EAAC,aAAa;MAAAD,QAAA,eAEvB3F,OAAA,CAACf,IAAI;QACH8B,IAAI,EAAEA,IAAK;QACXmH,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAExF,YAAa;QACvBiD,SAAS,EAAC,YAAY;QAAAD,QAAA,gBAEtB3F,OAAA;UAAK4F,SAAS,EAAC,UAAU;UAAAD,QAAA,gBACvB3F,OAAA,CAACf,IAAI,CAACmJ,IAAI;YACRC,IAAI,EAAC,OAAO;YACZ1G,KAAK,EAAC,aAAa;YACnB2G,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAE9J,OAAO,EAAE;YAA2B,CAAC,CAAE;YACjEmH,SAAS,EAAC,gBAAgB;YAAAD,QAAA,eAE1B3F,OAAA,CAACjB,KAAK;cAAC+H,WAAW,EAAC;YAAmB;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eAEZhG,OAAA,CAACf,IAAI,CAACmJ,IAAI;YACRC,IAAI,EAAC,OAAO;YACZ1G,KAAK,EAAC,aAAa;YACnB2G,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAE9J,OAAO,EAAE;YAA4B,CAAC,CAAE;YAClEmH,SAAS,EAAC,gBAAgB;YAAAD,QAAA,eAE1B3F,OAAA,CAAChB,MAAM;cAAC8H,WAAW,EAAC,oBAAoB;cAAAnB,QAAA,EACrClE,WAAW,CAAC0F,GAAG,CAAC1D,KAAK,iBACpBzD,OAAA,CAACC,MAAM;gBAAmByB,KAAK,EAAE+B,KAAK,CAAC/B,KAAM;gBAAAiE,QAAA,EAC1ClC,KAAK,CAAC9B;cAAK,GADD8B,KAAK,CAAC/B,KAAK;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEhB,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENhG,OAAA,CAACf,IAAI,CAACmJ,IAAI;UACRC,IAAI,EAAC,aAAa;UAClB1G,KAAK,EAAC,aAAa;UACnB2G,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE9J,OAAO,EAAE;UAA2B,CAAC,CAAE;UAAAkH,QAAA,eAEjE3F,OAAA,CAACE,QAAQ;YAACsI,IAAI,EAAE,CAAE;YAAC1B,WAAW,EAAC;UAAyB;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eAEZhG,OAAA;UAAK4F,SAAS,EAAC,UAAU;UAAAD,QAAA,gBACvB3F,OAAA,CAACf,IAAI,CAACmJ,IAAI;YACRC,IAAI,EAAC,UAAU;YACf1G,KAAK,EAAC,UAAU;YAChB2G,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAE9J,OAAO,EAAE;YAAwB,CAAC,CAAE;YAC9DmH,SAAS,EAAC,gBAAgB;YAAAD,QAAA,eAE1B3F,OAAA,CAACjB,KAAK;cAAC+H,WAAW,EAAC;YAAiC;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eAEZhG,OAAA,CAACf,IAAI,CAACmJ,IAAI;YACRC,IAAI,EAAC,SAAS;YACd1G,KAAK,EAAC,SAAS;YACfiE,SAAS,EAAC,gBAAgB;YAAAD,QAAA,eAE1B3F,OAAA,CAACjB,KAAK;cAAC+H,WAAW,EAAC;YAAiB;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENhG,OAAA;UAAK4F,SAAS,EAAC,UAAU;UAAAD,QAAA,gBACvB3F,OAAA,CAACf,IAAI,CAACmJ,IAAI;YACRC,IAAI,EAAC,gBAAgB;YACrB1G,KAAK,EAAC,iBAAiB;YACvB2G,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAE9J,OAAO,EAAE;YAAgC,CAAC,CAAE;YACtEmH,SAAS,EAAC,gBAAgB;YAAAD,QAAA,eAE1B3F,OAAA,CAAChB,MAAM;cAAC8H,WAAW,EAAC,wBAAwB;cAAAnB,QAAA,EACzC9D,eAAe,CAACsF,GAAG,CAACf,QAAQ,iBAC3BpG,OAAA,CAACC,MAAM;gBAAsByB,KAAK,EAAE0E,QAAQ,CAAC1E,KAAM;gBAAAiE,QAAA,EAChDS,QAAQ,CAACzE;cAAK,GADJyE,QAAQ,CAAC1E,KAAK;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEnB,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAEZhG,OAAA,CAACf,IAAI,CAACmJ,IAAI;YACRC,IAAI,EAAC,YAAY;YACjB1G,KAAK,EAAC,kBAAkB;YACxBiE,SAAS,EAAC,gBAAgB;YAAAD,QAAA,eAE1B3F,OAAA,CAAChB,MAAM;cAAC8H,WAAW,EAAC,mBAAmB;cAAAnB,QAAA,EACpC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACwB,GAAG,CAACsB,GAAG,iBACtBzI,OAAA,CAACC,MAAM;gBAAWyB,KAAK,EAAE+G,GAAI;gBAAA9C,QAAA,GAC1B8C,GAAG,EAAC,GAAC,EAAC,GAAG,CAACC,MAAM,CAACD,GAAG,CAAC;cAAA,GADXA,GAAG;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAER,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAGNhG,OAAA;UAAK4F,SAAS,EAAC,sBAAsB;UAAAD,QAAA,gBACnC3F,OAAA;YAAA2F,QAAA,EAAI;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEtBhG,OAAA,CAACf,IAAI,CAACmJ,IAAI;YACRC,IAAI,EAAC,UAAU;YACf1G,KAAK,EAAC,kDAAkD;YAAAgE,QAAA,eAExD3F,OAAA,CAACjB,KAAK;cAAC+H,WAAW,EAAC;YAA+B;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eAEZhG,OAAA;YAAK4F,SAAS,EAAC,qBAAqB;YAAAD,QAAA,gBAClC3F,OAAA;cAAO4F,SAAS,EAAC,cAAc;cAAAD,QAAA,gBAC7B3F,OAAA,CAACN,QAAQ;gBAAAmG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,sBACd;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhG,OAAA;cACEyG,IAAI,EAAC,MAAM;cACXkC,MAAM,EAAC,SAAS;cAChB3B,QAAQ,EAAGC,CAAC,IAAK/F,YAAY,CAAC+F,CAAC,CAACC,MAAM,CAAC0B,KAAK,CAAC,CAAC,CAAC,CAAE;cACjDhD,SAAS,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,EACD/E,SAAS,iBACRjB,OAAA;cAAK4F,SAAS,EAAC,WAAW;cAAAD,QAAA,GAAC,YACf,EAAC1E,SAAS,CAACoH,IAAI;YAAA;cAAAxC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhG,OAAA;UAAK4F,SAAS,EAAC,UAAU;UAAAD,QAAA,gBACvB3F,OAAA,CAACf,IAAI,CAACmJ,IAAI;YACRC,IAAI,EAAC,UAAU;YACf1G,KAAK,EAAC,gBAAgB;YACtBiE,SAAS,EAAC,gBAAgB;YAAAD,QAAA,eAE1B3F,OAAA,CAACjB,KAAK;cAAC+H,WAAW,EAAC;YAAa;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,eAEZhG,OAAA,CAACf,IAAI,CAACmJ,IAAI;YACRC,IAAI,EAAC,eAAe;YACpB1G,KAAK,EAAC,yBAAyB;YAC/BiE,SAAS,EAAC,gBAAgB;YAAAD,QAAA,eAE1B3F,OAAA,CAACjB,KAAK;cAAC+H,WAAW,EAAC;YAAkB;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENhG,OAAA,CAACf,IAAI,CAACmJ,IAAI;UACRC,IAAI,EAAC,MAAM;UACX1G,KAAK,EAAC,wBAAwB;UAAAgE,QAAA,eAE9B3F,OAAA,CAACjB,KAAK;YAAC+H,WAAW,EAAC;UAAmC;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eAEZhG,OAAA;UAAK4F,SAAS,EAAC,UAAU;UAAAD,QAAA,gBACvB3F,OAAA,CAACf,IAAI,CAACmJ,IAAI;YACRC,IAAI,EAAC,UAAU;YACf1G,KAAK,EAAC,QAAQ;YACdiE,SAAS,EAAC,gBAAgB;YAC1BiD,YAAY,EAAE,IAAK;YAAAlD,QAAA,eAEnB3F,OAAA,CAAChB,MAAM;cAAA2G,QAAA,gBACL3F,OAAA,CAACC,MAAM;gBAACyB,KAAK,EAAE,IAAK;gBAAAiE,QAAA,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpChG,OAAA,CAACC,MAAM;gBAACyB,KAAK,EAAE,KAAM;gBAAAiE,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAEZhG,OAAA,CAACf,IAAI,CAACmJ,IAAI;YACRC,IAAI,EAAC,YAAY;YACjB1G,KAAK,EAAC,UAAU;YAChBiE,SAAS,EAAC,gBAAgB;YAC1BiD,YAAY,EAAE,KAAM;YAAAlD,QAAA,eAEpB3F,OAAA,CAAChB,MAAM;cAAA2G,QAAA,gBACL3F,OAAA,CAACC,MAAM;gBAACyB,KAAK,EAAE,IAAK;gBAAAiE,QAAA,EAAC;cAAG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACjChG,OAAA,CAACC,MAAM;gBAACyB,KAAK,EAAE,KAAM;gBAAAiE,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENhG,OAAA;UAAK4F,SAAS,EAAC,cAAc;UAAAD,QAAA,gBAC3B3F,OAAA,CAACrB,MAAM;YACL8H,IAAI,EAAC,SAAS;YACdG,OAAO,EAAEA,CAAA,KAAM;cACbhG,eAAe,CAAC,KAAK,CAAC;cACtBE,eAAe,CAAC,IAAI,CAAC;cACrBI,YAAY,CAAC,IAAI,CAAC;cAClBH,IAAI,CAACmC,WAAW,CAAC,CAAC;YACpB,CAAE;YAAAyC,QAAA,EACH;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThG,OAAA,CAACrB,MAAM;YACL8H,IAAI,EAAC,SAAS;YACdqC,QAAQ,EAAC,QAAQ;YACjBrI,OAAO,EAAEU,SAAU;YACnBwF,IAAI,EAAE9F,YAAY,gBAAGb,OAAA,CAACb,MAAM;cAAA0G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGhG,OAAA,CAACd,MAAM;cAAA2G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAL,QAAA,EAE5CxE,SAAS,GAAG,cAAc,GAAIN,YAAY,GAAG,cAAc,GAAG;UAAe;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC5F,EAAA,CAxiBID,WAAW;EAAA,QACE3B,WAAW,EAKbS,IAAI,CAAC+B,OAAO;AAAA;AAAA+H,EAAA,GANvB5I,WAAW;AA0iBjB,eAAeA,WAAW;AAAC,IAAA4I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}