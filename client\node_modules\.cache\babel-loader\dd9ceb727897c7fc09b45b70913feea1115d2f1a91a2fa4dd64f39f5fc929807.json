{"ast": null, "code": "const genCollapseMotion = token => ({\n  [token.componentCls]: {\n    // For common/openAnimation\n    [\"\".concat(token.antCls, \"-motion-collapse-legacy\")]: {\n      overflow: 'hidden',\n      '&-active': {\n        transition: \"height \".concat(token.motionDurationMid, \" \").concat(token.motionEaseInOut, \",\\n        opacity \").concat(token.motionDurationMid, \" \").concat(token.motionEaseInOut, \" !important\")\n      }\n    },\n    [\"\".concat(token.antCls, \"-motion-collapse\")]: {\n      overflow: 'hidden',\n      transition: \"height \".concat(token.motionDurationMid, \" \").concat(token.motionEaseInOut, \",\\n        opacity \").concat(token.motionDurationMid, \" \").concat(token.motionEaseInOut, \" !important\")\n    }\n  }\n});\nexport default genCollapseMotion;", "map": {"version": 3, "names": ["genCollapseMotion", "token", "componentCls", "concat", "antCls", "overflow", "transition", "motionDurationMid", "motionEaseInOut"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/style/motion/collapse.js"], "sourcesContent": ["const genCollapseMotion = token => ({\n  [token.componentCls]: {\n    // For common/openAnimation\n    [`${token.antCls}-motion-collapse-legacy`]: {\n      overflow: 'hidden',\n      '&-active': {\n        transition: `height ${token.motionDurationMid} ${token.motionEaseInOut},\n        opacity ${token.motionDurationMid} ${token.motionEaseInOut} !important`\n      }\n    },\n    [`${token.antCls}-motion-collapse`]: {\n      overflow: 'hidden',\n      transition: `height ${token.motionDurationMid} ${token.motionEaseInOut},\n        opacity ${token.motionDurationMid} ${token.motionEaseInOut} !important`\n    }\n  }\n});\nexport default genCollapseMotion;"], "mappings": "AAAA,MAAMA,iBAAiB,GAAGC,KAAK,KAAK;EAClC,CAACA,KAAK,CAACC,YAAY,GAAG;IACpB;IACA,IAAAC,MAAA,CAAIF,KAAK,CAACG,MAAM,+BAA4B;MAC1CC,QAAQ,EAAE,QAAQ;MAClB,UAAU,EAAE;QACVC,UAAU,YAAAH,MAAA,CAAYF,KAAK,CAACM,iBAAiB,OAAAJ,MAAA,CAAIF,KAAK,CAACO,eAAe,yBAAAL,MAAA,CAC5DF,KAAK,CAACM,iBAAiB,OAAAJ,MAAA,CAAIF,KAAK,CAACO,eAAe;MAC5D;IACF,CAAC;IACD,IAAAL,MAAA,CAAIF,KAAK,CAACG,MAAM,wBAAqB;MACnCC,QAAQ,EAAE,QAAQ;MAClBC,UAAU,YAAAH,MAAA,CAAYF,KAAK,CAACM,iBAAiB,OAAAJ,MAAA,CAAIF,KAAK,CAACO,eAAe,yBAAAL,MAAA,CAC1DF,KAAK,CAACM,iBAAiB,OAAAJ,MAAA,CAAIF,KAAK,CAACO,eAAe;IAC9D;EACF;AACF,CAAC,CAAC;AACF,eAAeR,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}