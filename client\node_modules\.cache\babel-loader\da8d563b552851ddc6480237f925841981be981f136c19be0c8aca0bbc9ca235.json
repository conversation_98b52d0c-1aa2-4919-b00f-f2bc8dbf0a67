{"ast": null, "code": "var autoAdjustOverflowTopBottom = {\n  shiftX: 64,\n  adjustY: 1\n};\nvar autoAdjustOverflowLeftRight = {\n  adjustX: 1,\n  shiftY: true\n};\nvar targetOffset = [0, 0];\nexport var placements = {\n  left: {\n    points: ['cr', 'cl'],\n    overflow: autoAdjustOverflowLeftRight,\n    offset: [-4, 0],\n    targetOffset: targetOffset\n  },\n  right: {\n    points: ['cl', 'cr'],\n    overflow: autoAdjustOverflowLeftRight,\n    offset: [4, 0],\n    targetOffset: targetOffset\n  },\n  top: {\n    points: ['bc', 'tc'],\n    overflow: autoAdjustOverflowTopBottom,\n    offset: [0, -4],\n    targetOffset: targetOffset\n  },\n  bottom: {\n    points: ['tc', 'bc'],\n    overflow: autoAdjustOverflowTopBottom,\n    offset: [0, 4],\n    targetOffset: targetOffset\n  },\n  topLeft: {\n    points: ['bl', 'tl'],\n    overflow: autoAdjustOverflowTopBottom,\n    offset: [0, -4],\n    targetOffset: targetOffset\n  },\n  leftTop: {\n    points: ['tr', 'tl'],\n    overflow: autoAdjustOverflowLeftRight,\n    offset: [-4, 0],\n    targetOffset: targetOffset\n  },\n  topRight: {\n    points: ['br', 'tr'],\n    overflow: autoAdjustOverflowTopBottom,\n    offset: [0, -4],\n    targetOffset: targetOffset\n  },\n  rightTop: {\n    points: ['tl', 'tr'],\n    overflow: autoAdjustOverflowLeftRight,\n    offset: [4, 0],\n    targetOffset: targetOffset\n  },\n  bottomRight: {\n    points: ['tr', 'br'],\n    overflow: autoAdjustOverflowTopBottom,\n    offset: [0, 4],\n    targetOffset: targetOffset\n  },\n  rightBottom: {\n    points: ['bl', 'br'],\n    overflow: autoAdjustOverflowLeftRight,\n    offset: [4, 0],\n    targetOffset: targetOffset\n  },\n  bottomLeft: {\n    points: ['tl', 'bl'],\n    overflow: autoAdjustOverflowTopBottom,\n    offset: [0, 4],\n    targetOffset: targetOffset\n  },\n  leftBottom: {\n    points: ['br', 'bl'],\n    overflow: autoAdjustOverflowLeftRight,\n    offset: [-4, 0],\n    targetOffset: targetOffset\n  }\n};\nexport default placements;", "map": {"version": 3, "names": ["autoAdjustOverflowTopBottom", "shiftX", "adjustY", "autoAdjustOverflowLeftRight", "adjustX", "shiftY", "targetOffset", "placements", "left", "points", "overflow", "offset", "right", "top", "bottom", "topLeft", "leftTop", "topRight", "rightTop", "bottomRight", "rightBottom", "bottomLeft", "leftBottom"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-tooltip/es/placements.js"], "sourcesContent": ["var autoAdjustOverflowTopBottom = {\n  shiftX: 64,\n  adjustY: 1\n};\nvar autoAdjustOverflowLeftRight = {\n  adjustX: 1,\n  shiftY: true\n};\nvar targetOffset = [0, 0];\nexport var placements = {\n  left: {\n    points: ['cr', 'cl'],\n    overflow: autoAdjustOverflowLeftRight,\n    offset: [-4, 0],\n    targetOffset: targetOffset\n  },\n  right: {\n    points: ['cl', 'cr'],\n    overflow: autoAdjustOverflowLeftRight,\n    offset: [4, 0],\n    targetOffset: targetOffset\n  },\n  top: {\n    points: ['bc', 'tc'],\n    overflow: autoAdjustOverflowTopBottom,\n    offset: [0, -4],\n    targetOffset: targetOffset\n  },\n  bottom: {\n    points: ['tc', 'bc'],\n    overflow: autoAdjustOverflowTopBottom,\n    offset: [0, 4],\n    targetOffset: targetOffset\n  },\n  topLeft: {\n    points: ['bl', 'tl'],\n    overflow: autoAdjustOverflowTopBottom,\n    offset: [0, -4],\n    targetOffset: targetOffset\n  },\n  leftTop: {\n    points: ['tr', 'tl'],\n    overflow: autoAdjustOverflowLeftRight,\n    offset: [-4, 0],\n    targetOffset: targetOffset\n  },\n  topRight: {\n    points: ['br', 'tr'],\n    overflow: autoAdjustOverflowTopBottom,\n    offset: [0, -4],\n    targetOffset: targetOffset\n  },\n  rightTop: {\n    points: ['tl', 'tr'],\n    overflow: autoAdjustOverflowLeftRight,\n    offset: [4, 0],\n    targetOffset: targetOffset\n  },\n  bottomRight: {\n    points: ['tr', 'br'],\n    overflow: autoAdjustOverflowTopBottom,\n    offset: [0, 4],\n    targetOffset: targetOffset\n  },\n  rightBottom: {\n    points: ['bl', 'br'],\n    overflow: autoAdjustOverflowLeftRight,\n    offset: [4, 0],\n    targetOffset: targetOffset\n  },\n  bottomLeft: {\n    points: ['tl', 'bl'],\n    overflow: autoAdjustOverflowTopBottom,\n    offset: [0, 4],\n    targetOffset: targetOffset\n  },\n  leftBottom: {\n    points: ['br', 'bl'],\n    overflow: autoAdjustOverflowLeftRight,\n    offset: [-4, 0],\n    targetOffset: targetOffset\n  }\n};\nexport default placements;"], "mappings": "AAAA,IAAIA,2BAA2B,GAAG;EAChCC,MAAM,EAAE,EAAE;EACVC,OAAO,EAAE;AACX,CAAC;AACD,IAAIC,2BAA2B,GAAG;EAChCC,OAAO,EAAE,CAAC;EACVC,MAAM,EAAE;AACV,CAAC;AACD,IAAIC,YAAY,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;AACzB,OAAO,IAAIC,UAAU,GAAG;EACtBC,IAAI,EAAE;IACJC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEP,2BAA2B;IACrCQ,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACfL,YAAY,EAAEA;EAChB,CAAC;EACDM,KAAK,EAAE;IACLH,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEP,2BAA2B;IACrCQ,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACdL,YAAY,EAAEA;EAChB,CAAC;EACDO,GAAG,EAAE;IACHJ,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEV,2BAA2B;IACrCW,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACfL,YAAY,EAAEA;EAChB,CAAC;EACDQ,MAAM,EAAE;IACNL,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEV,2BAA2B;IACrCW,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACdL,YAAY,EAAEA;EAChB,CAAC;EACDS,OAAO,EAAE;IACPN,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEV,2BAA2B;IACrCW,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACfL,YAAY,EAAEA;EAChB,CAAC;EACDU,OAAO,EAAE;IACPP,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEP,2BAA2B;IACrCQ,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACfL,YAAY,EAAEA;EAChB,CAAC;EACDW,QAAQ,EAAE;IACRR,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEV,2BAA2B;IACrCW,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACfL,YAAY,EAAEA;EAChB,CAAC;EACDY,QAAQ,EAAE;IACRT,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEP,2BAA2B;IACrCQ,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACdL,YAAY,EAAEA;EAChB,CAAC;EACDa,WAAW,EAAE;IACXV,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEV,2BAA2B;IACrCW,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACdL,YAAY,EAAEA;EAChB,CAAC;EACDc,WAAW,EAAE;IACXX,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEP,2BAA2B;IACrCQ,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACdL,YAAY,EAAEA;EAChB,CAAC;EACDe,UAAU,EAAE;IACVZ,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEV,2BAA2B;IACrCW,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACdL,YAAY,EAAEA;EAChB,CAAC;EACDgB,UAAU,EAAE;IACVb,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACpBC,QAAQ,EAAEP,2BAA2B;IACrCQ,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACfL,YAAY,EAAEA;EAChB;AACF,CAAC;AACD,eAAeC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}