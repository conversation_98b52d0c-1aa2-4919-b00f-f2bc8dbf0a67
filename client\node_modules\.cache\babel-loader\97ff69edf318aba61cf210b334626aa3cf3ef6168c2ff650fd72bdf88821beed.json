{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.resetState = resetState;\nexports.log = log;\nexports.assertNodeList = assertNodeList;\nexports.setElement = setElement;\nexports.validateElement = validateElement;\nexports.hide = hide;\nexports.show = show;\nexports.documentNotReadyOrSSRTesting = documentNotReadyOrSSRTesting;\nvar _warning = require(\"warning\");\nvar _warning2 = _interopRequireDefault(_warning);\nvar _safeHTMLElement = require(\"./safeHTMLElement\");\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar globalElement = null;\n\n/* eslint-disable no-console */\n/* istanbul ignore next */\nfunction resetState() {\n  if (globalElement) {\n    if (globalElement.removeAttribute) {\n      globalElement.removeAttribute(\"aria-hidden\");\n    } else if (globalElement.length != null) {\n      globalElement.forEach(function (element) {\n        return element.removeAttribute(\"aria-hidden\");\n      });\n    } else {\n      document.querySelectorAll(globalElement).forEach(function (element) {\n        return element.removeAttribute(\"aria-hidden\");\n      });\n    }\n  }\n  globalElement = null;\n}\n\n/* istanbul ignore next */\nfunction log() {\n  if (process.env.NODE_ENV !== \"production\") {\n    var check = globalElement || {};\n    console.log(\"ariaAppHider ----------\");\n    console.log(check.nodeName, check.className, check.id);\n    console.log(\"end ariaAppHider ----------\");\n  }\n}\n/* eslint-enable no-console */\n\nfunction assertNodeList(nodeList, selector) {\n  if (!nodeList || !nodeList.length) {\n    throw new Error(\"react-modal: No elements were found for selector \" + selector + \".\");\n  }\n}\nfunction setElement(element) {\n  var useElement = element;\n  if (typeof useElement === \"string\" && _safeHTMLElement.canUseDOM) {\n    var el = document.querySelectorAll(useElement);\n    assertNodeList(el, useElement);\n    useElement = el;\n  }\n  globalElement = useElement || globalElement;\n  return globalElement;\n}\nfunction validateElement(appElement) {\n  var el = appElement || globalElement;\n  if (el) {\n    return Array.isArray(el) || el instanceof HTMLCollection || el instanceof NodeList ? el : [el];\n  } else {\n    (0, _warning2.default)(false, [\"react-modal: App element is not defined.\", \"Please use `Modal.setAppElement(el)` or set `appElement={el}`.\", \"This is needed so screen readers don't see main content\", \"when modal is opened. It is not recommended, but you can opt-out\", \"by setting `ariaHideApp={false}`.\"].join(\" \"));\n    return [];\n  }\n}\nfunction hide(appElement) {\n  var _iteratorNormalCompletion = true;\n  var _didIteratorError = false;\n  var _iteratorError = undefined;\n  try {\n    for (var _iterator = validateElement(appElement)[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true) {\n      var el = _step.value;\n      el.setAttribute(\"aria-hidden\", \"true\");\n    }\n  } catch (err) {\n    _didIteratorError = true;\n    _iteratorError = err;\n  } finally {\n    try {\n      if (!_iteratorNormalCompletion && _iterator.return) {\n        _iterator.return();\n      }\n    } finally {\n      if (_didIteratorError) {\n        throw _iteratorError;\n      }\n    }\n  }\n}\nfunction show(appElement) {\n  var _iteratorNormalCompletion2 = true;\n  var _didIteratorError2 = false;\n  var _iteratorError2 = undefined;\n  try {\n    for (var _iterator2 = validateElement(appElement)[Symbol.iterator](), _step2; !(_iteratorNormalCompletion2 = (_step2 = _iterator2.next()).done); _iteratorNormalCompletion2 = true) {\n      var el = _step2.value;\n      el.removeAttribute(\"aria-hidden\");\n    }\n  } catch (err) {\n    _didIteratorError2 = true;\n    _iteratorError2 = err;\n  } finally {\n    try {\n      if (!_iteratorNormalCompletion2 && _iterator2.return) {\n        _iterator2.return();\n      }\n    } finally {\n      if (_didIteratorError2) {\n        throw _iteratorError2;\n      }\n    }\n  }\n}\nfunction documentNotReadyOrSSRTesting() {\n  globalElement = null;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "resetState", "log", "assertNodeList", "setElement", "validateElement", "hide", "show", "documentNotReadyOrSSRTesting", "_warning", "require", "_warning2", "_interopRequireDefault", "_safeHTMLElement", "obj", "__esModule", "default", "globalElement", "removeAttribute", "length", "for<PERSON>ach", "element", "document", "querySelectorAll", "process", "env", "NODE_ENV", "check", "console", "nodeName", "className", "id", "nodeList", "selector", "Error", "useElement", "canUseDOM", "el", "appElement", "Array", "isArray", "HTMLCollection", "NodeList", "join", "_iteratorNormalCompletion", "_didIteratorError", "_iteratorError", "undefined", "_iterator", "Symbol", "iterator", "_step", "next", "done", "setAttribute", "err", "return", "_iteratorNormalCompletion2", "_didIteratorError2", "_iteratorError2", "_iterator2", "_step2"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/react-modal/lib/helpers/ariaAppHider.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.resetState = resetState;\nexports.log = log;\nexports.assertNodeList = assertNodeList;\nexports.setElement = setElement;\nexports.validateElement = validateElement;\nexports.hide = hide;\nexports.show = show;\nexports.documentNotReadyOrSSRTesting = documentNotReadyOrSSRTesting;\n\nvar _warning = require(\"warning\");\n\nvar _warning2 = _interopRequireDefault(_warning);\n\nvar _safeHTMLElement = require(\"./safeHTMLElement\");\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar globalElement = null;\n\n/* eslint-disable no-console */\n/* istanbul ignore next */\nfunction resetState() {\n  if (globalElement) {\n    if (globalElement.removeAttribute) {\n      globalElement.removeAttribute(\"aria-hidden\");\n    } else if (globalElement.length != null) {\n      globalElement.forEach(function (element) {\n        return element.removeAttribute(\"aria-hidden\");\n      });\n    } else {\n      document.querySelectorAll(globalElement).forEach(function (element) {\n        return element.removeAttribute(\"aria-hidden\");\n      });\n    }\n  }\n  globalElement = null;\n}\n\n/* istanbul ignore next */\nfunction log() {\n  if (process.env.NODE_ENV !== \"production\") {\n    var check = globalElement || {};\n    console.log(\"ariaAppHider ----------\");\n    console.log(check.nodeName, check.className, check.id);\n    console.log(\"end ariaAppHider ----------\");\n  }\n}\n/* eslint-enable no-console */\n\nfunction assertNodeList(nodeList, selector) {\n  if (!nodeList || !nodeList.length) {\n    throw new Error(\"react-modal: No elements were found for selector \" + selector + \".\");\n  }\n}\n\nfunction setElement(element) {\n  var useElement = element;\n  if (typeof useElement === \"string\" && _safeHTMLElement.canUseDOM) {\n    var el = document.querySelectorAll(useElement);\n    assertNodeList(el, useElement);\n    useElement = el;\n  }\n  globalElement = useElement || globalElement;\n  return globalElement;\n}\n\nfunction validateElement(appElement) {\n  var el = appElement || globalElement;\n  if (el) {\n    return Array.isArray(el) || el instanceof HTMLCollection || el instanceof NodeList ? el : [el];\n  } else {\n    (0, _warning2.default)(false, [\"react-modal: App element is not defined.\", \"Please use `Modal.setAppElement(el)` or set `appElement={el}`.\", \"This is needed so screen readers don't see main content\", \"when modal is opened. It is not recommended, but you can opt-out\", \"by setting `ariaHideApp={false}`.\"].join(\" \"));\n\n    return [];\n  }\n}\n\nfunction hide(appElement) {\n  var _iteratorNormalCompletion = true;\n  var _didIteratorError = false;\n  var _iteratorError = undefined;\n\n  try {\n    for (var _iterator = validateElement(appElement)[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true) {\n      var el = _step.value;\n\n      el.setAttribute(\"aria-hidden\", \"true\");\n    }\n  } catch (err) {\n    _didIteratorError = true;\n    _iteratorError = err;\n  } finally {\n    try {\n      if (!_iteratorNormalCompletion && _iterator.return) {\n        _iterator.return();\n      }\n    } finally {\n      if (_didIteratorError) {\n        throw _iteratorError;\n      }\n    }\n  }\n}\n\nfunction show(appElement) {\n  var _iteratorNormalCompletion2 = true;\n  var _didIteratorError2 = false;\n  var _iteratorError2 = undefined;\n\n  try {\n    for (var _iterator2 = validateElement(appElement)[Symbol.iterator](), _step2; !(_iteratorNormalCompletion2 = (_step2 = _iterator2.next()).done); _iteratorNormalCompletion2 = true) {\n      var el = _step2.value;\n\n      el.removeAttribute(\"aria-hidden\");\n    }\n  } catch (err) {\n    _didIteratorError2 = true;\n    _iteratorError2 = err;\n  } finally {\n    try {\n      if (!_iteratorNormalCompletion2 && _iterator2.return) {\n        _iterator2.return();\n      }\n    } finally {\n      if (_didIteratorError2) {\n        throw _iteratorError2;\n      }\n    }\n  }\n}\n\nfunction documentNotReadyOrSSRTesting() {\n  globalElement = null;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,UAAU,GAAGA,UAAU;AAC/BF,OAAO,CAACG,GAAG,GAAGA,GAAG;AACjBH,OAAO,CAACI,cAAc,GAAGA,cAAc;AACvCJ,OAAO,CAACK,UAAU,GAAGA,UAAU;AAC/BL,OAAO,CAACM,eAAe,GAAGA,eAAe;AACzCN,OAAO,CAACO,IAAI,GAAGA,IAAI;AACnBP,OAAO,CAACQ,IAAI,GAAGA,IAAI;AACnBR,OAAO,CAACS,4BAA4B,GAAGA,4BAA4B;AAEnE,IAAIC,QAAQ,GAAGC,OAAO,CAAC,SAAS,CAAC;AAEjC,IAAIC,SAAS,GAAGC,sBAAsB,CAACH,QAAQ,CAAC;AAEhD,IAAII,gBAAgB,GAAGH,OAAO,CAAC,mBAAmB,CAAC;AAEnD,SAASE,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEE,OAAO,EAAEF;EAAI,CAAC;AAAE;AAE9F,IAAIG,aAAa,GAAG,IAAI;;AAExB;AACA;AACA,SAAShB,UAAUA,CAAA,EAAG;EACpB,IAAIgB,aAAa,EAAE;IACjB,IAAIA,aAAa,CAACC,eAAe,EAAE;MACjCD,aAAa,CAACC,eAAe,CAAC,aAAa,CAAC;IAC9C,CAAC,MAAM,IAAID,aAAa,CAACE,MAAM,IAAI,IAAI,EAAE;MACvCF,aAAa,CAACG,OAAO,CAAC,UAAUC,OAAO,EAAE;QACvC,OAAOA,OAAO,CAACH,eAAe,CAAC,aAAa,CAAC;MAC/C,CAAC,CAAC;IACJ,CAAC,MAAM;MACLI,QAAQ,CAACC,gBAAgB,CAACN,aAAa,CAAC,CAACG,OAAO,CAAC,UAAUC,OAAO,EAAE;QAClE,OAAOA,OAAO,CAACH,eAAe,CAAC,aAAa,CAAC;MAC/C,CAAC,CAAC;IACJ;EACF;EACAD,aAAa,GAAG,IAAI;AACtB;;AAEA;AACA,SAASf,GAAGA,CAAA,EAAG;EACb,IAAIsB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIC,KAAK,GAAGV,aAAa,IAAI,CAAC,CAAC;IAC/BW,OAAO,CAAC1B,GAAG,CAAC,yBAAyB,CAAC;IACtC0B,OAAO,CAAC1B,GAAG,CAACyB,KAAK,CAACE,QAAQ,EAAEF,KAAK,CAACG,SAAS,EAAEH,KAAK,CAACI,EAAE,CAAC;IACtDH,OAAO,CAAC1B,GAAG,CAAC,6BAA6B,CAAC;EAC5C;AACF;AACA;;AAEA,SAASC,cAAcA,CAAC6B,QAAQ,EAAEC,QAAQ,EAAE;EAC1C,IAAI,CAACD,QAAQ,IAAI,CAACA,QAAQ,CAACb,MAAM,EAAE;IACjC,MAAM,IAAIe,KAAK,CAAC,mDAAmD,GAAGD,QAAQ,GAAG,GAAG,CAAC;EACvF;AACF;AAEA,SAAS7B,UAAUA,CAACiB,OAAO,EAAE;EAC3B,IAAIc,UAAU,GAAGd,OAAO;EACxB,IAAI,OAAOc,UAAU,KAAK,QAAQ,IAAItB,gBAAgB,CAACuB,SAAS,EAAE;IAChE,IAAIC,EAAE,GAAGf,QAAQ,CAACC,gBAAgB,CAACY,UAAU,CAAC;IAC9ChC,cAAc,CAACkC,EAAE,EAAEF,UAAU,CAAC;IAC9BA,UAAU,GAAGE,EAAE;EACjB;EACApB,aAAa,GAAGkB,UAAU,IAAIlB,aAAa;EAC3C,OAAOA,aAAa;AACtB;AAEA,SAASZ,eAAeA,CAACiC,UAAU,EAAE;EACnC,IAAID,EAAE,GAAGC,UAAU,IAAIrB,aAAa;EACpC,IAAIoB,EAAE,EAAE;IACN,OAAOE,KAAK,CAACC,OAAO,CAACH,EAAE,CAAC,IAAIA,EAAE,YAAYI,cAAc,IAAIJ,EAAE,YAAYK,QAAQ,GAAGL,EAAE,GAAG,CAACA,EAAE,CAAC;EAChG,CAAC,MAAM;IACL,CAAC,CAAC,EAAE1B,SAAS,CAACK,OAAO,EAAE,KAAK,EAAE,CAAC,0CAA0C,EAAE,gEAAgE,EAAE,yDAAyD,EAAE,kEAAkE,EAAE,mCAAmC,CAAC,CAAC2B,IAAI,CAAC,GAAG,CAAC,CAAC;IAE3T,OAAO,EAAE;EACX;AACF;AAEA,SAASrC,IAAIA,CAACgC,UAAU,EAAE;EACxB,IAAIM,yBAAyB,GAAG,IAAI;EACpC,IAAIC,iBAAiB,GAAG,KAAK;EAC7B,IAAIC,cAAc,GAAGC,SAAS;EAE9B,IAAI;IACF,KAAK,IAAIC,SAAS,GAAG3C,eAAe,CAACiC,UAAU,CAAC,CAACW,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC,EAAEC,KAAK,EAAE,EAAEP,yBAAyB,GAAG,CAACO,KAAK,GAAGH,SAAS,CAACI,IAAI,CAAC,CAAC,EAAEC,IAAI,CAAC,EAAET,yBAAyB,GAAG,IAAI,EAAE;MAC5K,IAAIP,EAAE,GAAGc,KAAK,CAACnD,KAAK;MAEpBqC,EAAE,CAACiB,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IACxC;EACF,CAAC,CAAC,OAAOC,GAAG,EAAE;IACZV,iBAAiB,GAAG,IAAI;IACxBC,cAAc,GAAGS,GAAG;EACtB,CAAC,SAAS;IACR,IAAI;MACF,IAAI,CAACX,yBAAyB,IAAII,SAAS,CAACQ,MAAM,EAAE;QAClDR,SAAS,CAACQ,MAAM,CAAC,CAAC;MACpB;IACF,CAAC,SAAS;MACR,IAAIX,iBAAiB,EAAE;QACrB,MAAMC,cAAc;MACtB;IACF;EACF;AACF;AAEA,SAASvC,IAAIA,CAAC+B,UAAU,EAAE;EACxB,IAAImB,0BAA0B,GAAG,IAAI;EACrC,IAAIC,kBAAkB,GAAG,KAAK;EAC9B,IAAIC,eAAe,GAAGZ,SAAS;EAE/B,IAAI;IACF,KAAK,IAAIa,UAAU,GAAGvD,eAAe,CAACiC,UAAU,CAAC,CAACW,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC,EAAEW,MAAM,EAAE,EAAEJ,0BAA0B,GAAG,CAACI,MAAM,GAAGD,UAAU,CAACR,IAAI,CAAC,CAAC,EAAEC,IAAI,CAAC,EAAEI,0BAA0B,GAAG,IAAI,EAAE;MAClL,IAAIpB,EAAE,GAAGwB,MAAM,CAAC7D,KAAK;MAErBqC,EAAE,CAACnB,eAAe,CAAC,aAAa,CAAC;IACnC;EACF,CAAC,CAAC,OAAOqC,GAAG,EAAE;IACZG,kBAAkB,GAAG,IAAI;IACzBC,eAAe,GAAGJ,GAAG;EACvB,CAAC,SAAS;IACR,IAAI;MACF,IAAI,CAACE,0BAA0B,IAAIG,UAAU,CAACJ,MAAM,EAAE;QACpDI,UAAU,CAACJ,MAAM,CAAC,CAAC;MACrB;IACF,CAAC,SAAS;MACR,IAAIE,kBAAkB,EAAE;QACtB,MAAMC,eAAe;MACvB;IACF;EACF;AACF;AAEA,SAASnD,4BAA4BA,CAAA,EAAG;EACtCS,aAAa,GAAG,IAAI;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}