{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\n// ================================== Cache ==================================\n\nexport function sameDerivativeOption(left, right) {\n  if (left.length !== right.length) {\n    return false;\n  }\n  for (var i = 0; i < left.length; i++) {\n    if (left[i] !== right[i]) {\n      return false;\n    }\n  }\n  return true;\n}\nvar ThemeCache = /*#__PURE__*/function () {\n  function ThemeCache() {\n    _classCallCheck(this, ThemeCache);\n    _defineProperty(this, \"cache\", void 0);\n    _defineProperty(this, \"keys\", void 0);\n    _defineProperty(this, \"cacheCallTimes\", void 0);\n    this.cache = new Map();\n    this.keys = [];\n    this.cacheCallTimes = 0;\n  }\n  _createClass(ThemeCache, [{\n    key: \"size\",\n    value: function size() {\n      return this.keys.length;\n    }\n  }, {\n    key: \"internalGet\",\n    value: function internalGet(derivativeOption) {\n      var _cache2, _cache3;\n      var updateCallTimes = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      var cache = {\n        map: this.cache\n      };\n      derivativeOption.forEach(function (derivative) {\n        if (!cache) {\n          cache = undefined;\n        } else {\n          var _cache, _cache$map;\n          cache = (_cache = cache) === null || _cache === void 0 ? void 0 : (_cache$map = _cache.map) === null || _cache$map === void 0 ? void 0 : _cache$map.get(derivative);\n        }\n      });\n      if ((_cache2 = cache) !== null && _cache2 !== void 0 && _cache2.value && updateCallTimes) {\n        cache.value[1] = this.cacheCallTimes++;\n      }\n      return (_cache3 = cache) === null || _cache3 === void 0 ? void 0 : _cache3.value;\n    }\n  }, {\n    key: \"get\",\n    value: function get(derivativeOption) {\n      var _this$internalGet;\n      return (_this$internalGet = this.internalGet(derivativeOption, true)) === null || _this$internalGet === void 0 ? void 0 : _this$internalGet[0];\n    }\n  }, {\n    key: \"has\",\n    value: function has(derivativeOption) {\n      return !!this.internalGet(derivativeOption);\n    }\n  }, {\n    key: \"set\",\n    value: function set(derivativeOption, value) {\n      var _this = this;\n      // New cache\n      if (!this.has(derivativeOption)) {\n        if (this.size() + 1 > ThemeCache.MAX_CACHE_SIZE + ThemeCache.MAX_CACHE_OFFSET) {\n          var _this$keys$reduce = this.keys.reduce(function (result, key) {\n              var _result = _slicedToArray(result, 2),\n                callTimes = _result[1];\n              if (_this.internalGet(key)[1] < callTimes) {\n                return [key, _this.internalGet(key)[1]];\n              }\n              return result;\n            }, [this.keys[0], this.cacheCallTimes]),\n            _this$keys$reduce2 = _slicedToArray(_this$keys$reduce, 1),\n            targetKey = _this$keys$reduce2[0];\n          this.delete(targetKey);\n        }\n        this.keys.push(derivativeOption);\n      }\n      var cache = this.cache;\n      derivativeOption.forEach(function (derivative, index) {\n        if (index === derivativeOption.length - 1) {\n          cache.set(derivative, {\n            value: [value, _this.cacheCallTimes++]\n          });\n        } else {\n          var cacheValue = cache.get(derivative);\n          if (!cacheValue) {\n            cache.set(derivative, {\n              map: new Map()\n            });\n          } else if (!cacheValue.map) {\n            cacheValue.map = new Map();\n          }\n          cache = cache.get(derivative).map;\n        }\n      });\n    }\n  }, {\n    key: \"deleteByPath\",\n    value: function deleteByPath(currentCache, derivatives) {\n      var cache = currentCache.get(derivatives[0]);\n      if (derivatives.length === 1) {\n        var _cache$value;\n        if (!cache.map) {\n          currentCache.delete(derivatives[0]);\n        } else {\n          currentCache.set(derivatives[0], {\n            map: cache.map\n          });\n        }\n        return (_cache$value = cache.value) === null || _cache$value === void 0 ? void 0 : _cache$value[0];\n      }\n      var result = this.deleteByPath(cache.map, derivatives.slice(1));\n      if ((!cache.map || cache.map.size === 0) && !cache.value) {\n        currentCache.delete(derivatives[0]);\n      }\n      return result;\n    }\n  }, {\n    key: \"delete\",\n    value: function _delete(derivativeOption) {\n      // If cache exists\n      if (this.has(derivativeOption)) {\n        this.keys = this.keys.filter(function (item) {\n          return !sameDerivativeOption(item, derivativeOption);\n        });\n        return this.deleteByPath(this.cache, derivativeOption);\n      }\n      return undefined;\n    }\n  }]);\n  return ThemeCache;\n}();\n_defineProperty(ThemeCache, \"MAX_CACHE_SIZE\", 20);\n_defineProperty(ThemeCache, \"MAX_CACHE_OFFSET\", 5);\nexport { ThemeCache as default };", "map": {"version": 3, "names": ["_slicedToArray", "_classCallCheck", "_createClass", "_defineProperty", "sameDerivativeOption", "left", "right", "length", "i", "ThemeCache", "cache", "Map", "keys", "cacheCallTimes", "key", "value", "size", "internalGet", "derivativeOption", "_cache2", "_cache3", "updateCallTimes", "arguments", "undefined", "map", "for<PERSON>ach", "derivative", "_cache", "_cache$map", "get", "_this$internalGet", "has", "set", "_this", "MAX_CACHE_SIZE", "MAX_CACHE_OFFSET", "_this$keys$reduce", "reduce", "result", "_result", "callTimes", "_this$keys$reduce2", "<PERSON><PERSON><PERSON>", "delete", "push", "index", "cacheValue", "deleteByPath", "currentCache", "derivatives", "_cache$value", "slice", "_delete", "filter", "item", "default"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/@ant-design/cssinjs/es/theme/ThemeCache.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\n// ================================== Cache ==================================\n\nexport function sameDerivativeOption(left, right) {\n  if (left.length !== right.length) {\n    return false;\n  }\n  for (var i = 0; i < left.length; i++) {\n    if (left[i] !== right[i]) {\n      return false;\n    }\n  }\n  return true;\n}\nvar ThemeCache = /*#__PURE__*/function () {\n  function ThemeCache() {\n    _classCallCheck(this, ThemeCache);\n    _defineProperty(this, \"cache\", void 0);\n    _defineProperty(this, \"keys\", void 0);\n    _defineProperty(this, \"cacheCallTimes\", void 0);\n    this.cache = new Map();\n    this.keys = [];\n    this.cacheCallTimes = 0;\n  }\n  _createClass(ThemeCache, [{\n    key: \"size\",\n    value: function size() {\n      return this.keys.length;\n    }\n  }, {\n    key: \"internalGet\",\n    value: function internalGet(derivativeOption) {\n      var _cache2, _cache3;\n      var updateCallTimes = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      var cache = {\n        map: this.cache\n      };\n      derivativeOption.forEach(function (derivative) {\n        if (!cache) {\n          cache = undefined;\n        } else {\n          var _cache, _cache$map;\n          cache = (_cache = cache) === null || _cache === void 0 ? void 0 : (_cache$map = _cache.map) === null || _cache$map === void 0 ? void 0 : _cache$map.get(derivative);\n        }\n      });\n      if ((_cache2 = cache) !== null && _cache2 !== void 0 && _cache2.value && updateCallTimes) {\n        cache.value[1] = this.cacheCallTimes++;\n      }\n      return (_cache3 = cache) === null || _cache3 === void 0 ? void 0 : _cache3.value;\n    }\n  }, {\n    key: \"get\",\n    value: function get(derivativeOption) {\n      var _this$internalGet;\n      return (_this$internalGet = this.internalGet(derivativeOption, true)) === null || _this$internalGet === void 0 ? void 0 : _this$internalGet[0];\n    }\n  }, {\n    key: \"has\",\n    value: function has(derivativeOption) {\n      return !!this.internalGet(derivativeOption);\n    }\n  }, {\n    key: \"set\",\n    value: function set(derivativeOption, value) {\n      var _this = this;\n      // New cache\n      if (!this.has(derivativeOption)) {\n        if (this.size() + 1 > ThemeCache.MAX_CACHE_SIZE + ThemeCache.MAX_CACHE_OFFSET) {\n          var _this$keys$reduce = this.keys.reduce(function (result, key) {\n              var _result = _slicedToArray(result, 2),\n                callTimes = _result[1];\n              if (_this.internalGet(key)[1] < callTimes) {\n                return [key, _this.internalGet(key)[1]];\n              }\n              return result;\n            }, [this.keys[0], this.cacheCallTimes]),\n            _this$keys$reduce2 = _slicedToArray(_this$keys$reduce, 1),\n            targetKey = _this$keys$reduce2[0];\n          this.delete(targetKey);\n        }\n        this.keys.push(derivativeOption);\n      }\n      var cache = this.cache;\n      derivativeOption.forEach(function (derivative, index) {\n        if (index === derivativeOption.length - 1) {\n          cache.set(derivative, {\n            value: [value, _this.cacheCallTimes++]\n          });\n        } else {\n          var cacheValue = cache.get(derivative);\n          if (!cacheValue) {\n            cache.set(derivative, {\n              map: new Map()\n            });\n          } else if (!cacheValue.map) {\n            cacheValue.map = new Map();\n          }\n          cache = cache.get(derivative).map;\n        }\n      });\n    }\n  }, {\n    key: \"deleteByPath\",\n    value: function deleteByPath(currentCache, derivatives) {\n      var cache = currentCache.get(derivatives[0]);\n      if (derivatives.length === 1) {\n        var _cache$value;\n        if (!cache.map) {\n          currentCache.delete(derivatives[0]);\n        } else {\n          currentCache.set(derivatives[0], {\n            map: cache.map\n          });\n        }\n        return (_cache$value = cache.value) === null || _cache$value === void 0 ? void 0 : _cache$value[0];\n      }\n      var result = this.deleteByPath(cache.map, derivatives.slice(1));\n      if ((!cache.map || cache.map.size === 0) && !cache.value) {\n        currentCache.delete(derivatives[0]);\n      }\n      return result;\n    }\n  }, {\n    key: \"delete\",\n    value: function _delete(derivativeOption) {\n      // If cache exists\n      if (this.has(derivativeOption)) {\n        this.keys = this.keys.filter(function (item) {\n          return !sameDerivativeOption(item, derivativeOption);\n        });\n        return this.deleteByPath(this.cache, derivativeOption);\n      }\n      return undefined;\n    }\n  }]);\n  return ThemeCache;\n}();\n_defineProperty(ThemeCache, \"MAX_CACHE_SIZE\", 20);\n_defineProperty(ThemeCache, \"MAX_CACHE_OFFSET\", 5);\nexport { ThemeCache as default };"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,eAAe,MAAM,2CAA2C;AACvE;;AAEA,OAAO,SAASC,oBAAoBA,CAACC,IAAI,EAAEC,KAAK,EAAE;EAChD,IAAID,IAAI,CAACE,MAAM,KAAKD,KAAK,CAACC,MAAM,EAAE;IAChC,OAAO,KAAK;EACd;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,CAACE,MAAM,EAAEC,CAAC,EAAE,EAAE;IACpC,IAAIH,IAAI,CAACG,CAAC,CAAC,KAAKF,KAAK,CAACE,CAAC,CAAC,EAAE;MACxB,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb;AACA,IAAIC,UAAU,GAAG,aAAa,YAAY;EACxC,SAASA,UAAUA,CAAA,EAAG;IACpBR,eAAe,CAAC,IAAI,EAAEQ,UAAU,CAAC;IACjCN,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IACtCA,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IACrCA,eAAe,CAAC,IAAI,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAC;IAC/C,IAAI,CAACO,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtB,IAAI,CAACC,IAAI,GAAG,EAAE;IACd,IAAI,CAACC,cAAc,GAAG,CAAC;EACzB;EACAX,YAAY,CAACO,UAAU,EAAE,CAAC;IACxBK,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,SAASC,IAAIA,CAAA,EAAG;MACrB,OAAO,IAAI,CAACJ,IAAI,CAACL,MAAM;IACzB;EACF,CAAC,EAAE;IACDO,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,SAASE,WAAWA,CAACC,gBAAgB,EAAE;MAC5C,IAAIC,OAAO,EAAEC,OAAO;MACpB,IAAIC,eAAe,GAAGC,SAAS,CAACf,MAAM,GAAG,CAAC,IAAIe,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;MAC/F,IAAIZ,KAAK,GAAG;QACVc,GAAG,EAAE,IAAI,CAACd;MACZ,CAAC;MACDQ,gBAAgB,CAACO,OAAO,CAAC,UAAUC,UAAU,EAAE;QAC7C,IAAI,CAAChB,KAAK,EAAE;UACVA,KAAK,GAAGa,SAAS;QACnB,CAAC,MAAM;UACL,IAAII,MAAM,EAAEC,UAAU;UACtBlB,KAAK,GAAG,CAACiB,MAAM,GAAGjB,KAAK,MAAM,IAAI,IAAIiB,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,UAAU,GAAGD,MAAM,CAACH,GAAG,MAAM,IAAI,IAAII,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACC,GAAG,CAACH,UAAU,CAAC;QACrK;MACF,CAAC,CAAC;MACF,IAAI,CAACP,OAAO,GAAGT,KAAK,MAAM,IAAI,IAAIS,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACJ,KAAK,IAAIM,eAAe,EAAE;QACxFX,KAAK,CAACK,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAACF,cAAc,EAAE;MACxC;MACA,OAAO,CAACO,OAAO,GAAGV,KAAK,MAAM,IAAI,IAAIU,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACL,KAAK;IAClF;EACF,CAAC,EAAE;IACDD,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASc,GAAGA,CAACX,gBAAgB,EAAE;MACpC,IAAIY,iBAAiB;MACrB,OAAO,CAACA,iBAAiB,GAAG,IAAI,CAACb,WAAW,CAACC,gBAAgB,EAAE,IAAI,CAAC,MAAM,IAAI,IAAIY,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAAC,CAAC,CAAC;IAChJ;EACF,CAAC,EAAE;IACDhB,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASgB,GAAGA,CAACb,gBAAgB,EAAE;MACpC,OAAO,CAAC,CAAC,IAAI,CAACD,WAAW,CAACC,gBAAgB,CAAC;IAC7C;EACF,CAAC,EAAE;IACDJ,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASiB,GAAGA,CAACd,gBAAgB,EAAEH,KAAK,EAAE;MAC3C,IAAIkB,KAAK,GAAG,IAAI;MAChB;MACA,IAAI,CAAC,IAAI,CAACF,GAAG,CAACb,gBAAgB,CAAC,EAAE;QAC/B,IAAI,IAAI,CAACF,IAAI,CAAC,CAAC,GAAG,CAAC,GAAGP,UAAU,CAACyB,cAAc,GAAGzB,UAAU,CAAC0B,gBAAgB,EAAE;UAC7E,IAAIC,iBAAiB,GAAG,IAAI,CAACxB,IAAI,CAACyB,MAAM,CAAC,UAAUC,MAAM,EAAExB,GAAG,EAAE;cAC5D,IAAIyB,OAAO,GAAGvC,cAAc,CAACsC,MAAM,EAAE,CAAC,CAAC;gBACrCE,SAAS,GAAGD,OAAO,CAAC,CAAC,CAAC;cACxB,IAAIN,KAAK,CAAChB,WAAW,CAACH,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG0B,SAAS,EAAE;gBACzC,OAAO,CAAC1B,GAAG,EAAEmB,KAAK,CAAChB,WAAW,CAACH,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;cACzC;cACA,OAAOwB,MAAM;YACf,CAAC,EAAE,CAAC,IAAI,CAAC1B,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAACC,cAAc,CAAC,CAAC;YACvC4B,kBAAkB,GAAGzC,cAAc,CAACoC,iBAAiB,EAAE,CAAC,CAAC;YACzDM,SAAS,GAAGD,kBAAkB,CAAC,CAAC,CAAC;UACnC,IAAI,CAACE,MAAM,CAACD,SAAS,CAAC;QACxB;QACA,IAAI,CAAC9B,IAAI,CAACgC,IAAI,CAAC1B,gBAAgB,CAAC;MAClC;MACA,IAAIR,KAAK,GAAG,IAAI,CAACA,KAAK;MACtBQ,gBAAgB,CAACO,OAAO,CAAC,UAAUC,UAAU,EAAEmB,KAAK,EAAE;QACpD,IAAIA,KAAK,KAAK3B,gBAAgB,CAACX,MAAM,GAAG,CAAC,EAAE;UACzCG,KAAK,CAACsB,GAAG,CAACN,UAAU,EAAE;YACpBX,KAAK,EAAE,CAACA,KAAK,EAAEkB,KAAK,CAACpB,cAAc,EAAE;UACvC,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,IAAIiC,UAAU,GAAGpC,KAAK,CAACmB,GAAG,CAACH,UAAU,CAAC;UACtC,IAAI,CAACoB,UAAU,EAAE;YACfpC,KAAK,CAACsB,GAAG,CAACN,UAAU,EAAE;cACpBF,GAAG,EAAE,IAAIb,GAAG,CAAC;YACf,CAAC,CAAC;UACJ,CAAC,MAAM,IAAI,CAACmC,UAAU,CAACtB,GAAG,EAAE;YAC1BsB,UAAU,CAACtB,GAAG,GAAG,IAAIb,GAAG,CAAC,CAAC;UAC5B;UACAD,KAAK,GAAGA,KAAK,CAACmB,GAAG,CAACH,UAAU,CAAC,CAACF,GAAG;QACnC;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDV,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE,SAASgC,YAAYA,CAACC,YAAY,EAAEC,WAAW,EAAE;MACtD,IAAIvC,KAAK,GAAGsC,YAAY,CAACnB,GAAG,CAACoB,WAAW,CAAC,CAAC,CAAC,CAAC;MAC5C,IAAIA,WAAW,CAAC1C,MAAM,KAAK,CAAC,EAAE;QAC5B,IAAI2C,YAAY;QAChB,IAAI,CAACxC,KAAK,CAACc,GAAG,EAAE;UACdwB,YAAY,CAACL,MAAM,CAACM,WAAW,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,MAAM;UACLD,YAAY,CAAChB,GAAG,CAACiB,WAAW,CAAC,CAAC,CAAC,EAAE;YAC/BzB,GAAG,EAAEd,KAAK,CAACc;UACb,CAAC,CAAC;QACJ;QACA,OAAO,CAAC0B,YAAY,GAAGxC,KAAK,CAACK,KAAK,MAAM,IAAI,IAAImC,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC,CAAC,CAAC;MACpG;MACA,IAAIZ,MAAM,GAAG,IAAI,CAACS,YAAY,CAACrC,KAAK,CAACc,GAAG,EAAEyB,WAAW,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC;MAC/D,IAAI,CAAC,CAACzC,KAAK,CAACc,GAAG,IAAId,KAAK,CAACc,GAAG,CAACR,IAAI,KAAK,CAAC,KAAK,CAACN,KAAK,CAACK,KAAK,EAAE;QACxDiC,YAAY,CAACL,MAAM,CAACM,WAAW,CAAC,CAAC,CAAC,CAAC;MACrC;MACA,OAAOX,MAAM;IACf;EACF,CAAC,EAAE;IACDxB,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASqC,OAAOA,CAAClC,gBAAgB,EAAE;MACxC;MACA,IAAI,IAAI,CAACa,GAAG,CAACb,gBAAgB,CAAC,EAAE;QAC9B,IAAI,CAACN,IAAI,GAAG,IAAI,CAACA,IAAI,CAACyC,MAAM,CAAC,UAAUC,IAAI,EAAE;UAC3C,OAAO,CAAClD,oBAAoB,CAACkD,IAAI,EAAEpC,gBAAgB,CAAC;QACtD,CAAC,CAAC;QACF,OAAO,IAAI,CAAC6B,YAAY,CAAC,IAAI,CAACrC,KAAK,EAAEQ,gBAAgB,CAAC;MACxD;MACA,OAAOK,SAAS;IAClB;EACF,CAAC,CAAC,CAAC;EACH,OAAOd,UAAU;AACnB,CAAC,CAAC,CAAC;AACHN,eAAe,CAACM,UAAU,EAAE,gBAAgB,EAAE,EAAE,CAAC;AACjDN,eAAe,CAACM,UAAU,EAAE,kBAAkB,EAAE,CAAC,CAAC;AAClD,SAASA,UAAU,IAAI8C,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}