{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\StudyMaterial\\\\PDFModal.js\",\n  _s = $RefreshSig$();\nimport { useEffect, useRef, useState } from \"react\";\nimport * as pdfjsLib from \"pdfjs-dist\";\nimport ReactModal from \"react-modal\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nReactModal.setAppElement('#root');\npdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@2.15.349/build/pdf.worker.min.js`;\n\n// Add CSS for spinner animation\nconst spinnerStyle = `\n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n`;\n\n// Inject the CSS\nif (typeof document !== 'undefined') {\n  const style = document.createElement('style');\n  style.textContent = spinnerStyle;\n  document.head.appendChild(style);\n}\nconst PDFModal = ({\n  modalIsOpen,\n  closeModal,\n  documentUrl\n}) => {\n  _s();\n  const [pages, setPages] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [loadingProgress, setLoadingProgress] = useState(0);\n  const [totalPages, setTotalPages] = useState(0);\n  const [selectedText, setSelectedText] = useState('');\n  const [showCopyButton, setShowCopyButton] = useState(false);\n  const canvasRefs = useRef([]);\n  const textLayerRefs = useRef([]);\n  const containerRef = useRef(null);\n  const renderingRefs = useRef({}); // Track rendering state per page\n\n  // Handle text selection\n  const handleTextSelection = () => {\n    const selection = window.getSelection();\n    const text = selection.toString().trim();\n    if (text) {\n      setSelectedText(text);\n      setShowCopyButton(true);\n    } else {\n      setSelectedText('');\n      setShowCopyButton(false);\n    }\n  };\n\n  // Copy selected text to clipboard\n  const copyToClipboard = async () => {\n    if (selectedText) {\n      try {\n        await navigator.clipboard.writeText(selectedText);\n        alert('Text copied to clipboard!');\n        setShowCopyButton(false);\n        window.getSelection().removeAllRanges();\n      } catch (err) {\n        // Fallback for older browsers\n        const textArea = document.createElement('textarea');\n        textArea.value = selectedText;\n        document.body.appendChild(textArea);\n        textArea.select();\n        document.execCommand('copy');\n        document.body.removeChild(textArea);\n        alert('Text copied to clipboard!');\n        setShowCopyButton(false);\n        window.getSelection().removeAllRanges();\n      }\n    }\n  };\n  const renderPDF = async url => {\n    try {\n      setIsLoading(true);\n      setLoadingProgress(0);\n      const pdf = await pdfjsLib.getDocument(url).promise;\n      console.log(\"PDF loaded\");\n      setTotalPages(pdf.numPages);\n\n      // Load pages progressively (first 3 pages immediately, then lazy load others)\n      const initialPagesToLoad = Math.min(3, pdf.numPages);\n      const pagesData = [];\n      for (let i = 1; i <= initialPagesToLoad; i++) {\n        const page = await pdf.getPage(i);\n        pagesData.push(page);\n        setLoadingProgress(i / pdf.numPages * 100);\n      }\n      setPages(pagesData);\n      setIsLoading(false);\n\n      // Load remaining pages in background\n      if (pdf.numPages > initialPagesToLoad) {\n        loadRemainingPages(pdf, initialPagesToLoad + 1, pagesData);\n      }\n    } catch (error) {\n      console.error(\"Error loading PDF:\", error);\n      setIsLoading(false);\n    }\n  };\n  const loadRemainingPages = async (pdf, startPage, existingPages) => {\n    const updatedPages = [...existingPages];\n    for (let i = startPage; i <= pdf.numPages; i++) {\n      try {\n        const page = await pdf.getPage(i);\n        updatedPages.push(page);\n        setPages([...updatedPages]);\n        setLoadingProgress(i / pdf.numPages * 100);\n\n        // Small delay to prevent blocking the UI\n        await new Promise(resolve => setTimeout(resolve, 50));\n      } catch (error) {\n        console.error(`Error loading page ${i}:`, error);\n      }\n    }\n  };\n  const renderPage = async (page, index) => {\n    const canvas = canvasRefs.current[index];\n    if (!canvas || renderingRefs.current[index] || !containerRef.current) return;\n    try {\n      renderingRefs.current[index] = true;\n      const viewport = page.getViewport({\n        scale: 1.0\n      });\n      const containerWidth = containerRef.current.clientWidth;\n      const scale = containerWidth / viewport.width;\n      const scaledViewport = page.getViewport({\n        scale\n      });\n      const context = canvas.getContext(\"2d\");\n      canvas.height = scaledViewport.height;\n      canvas.width = scaledViewport.width;\n      canvas.style.width = '100%';\n      canvas.style.height = 'auto';\n      const renderContext = {\n        canvasContext: context,\n        viewport: scaledViewport\n      };\n      await page.render(renderContext).promise;\n      console.log(`Page ${index + 1} rendered`);\n    } catch (error) {\n      console.error(`Error rendering page ${index + 1}:`, error);\n    } finally {\n      renderingRefs.current[index] = false;\n    }\n  };\n  useEffect(() => {\n    if (modalIsOpen && documentUrl) {\n      setPages([]);\n      setTotalPages(0);\n      setLoadingProgress(0);\n      canvasRefs.current = [];\n      renderingRefs.current = {};\n      renderPDF(documentUrl);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [modalIsOpen, documentUrl]);\n\n  // Effect to render pages when they're loaded\n  useEffect(() => {\n    if (pages.length > 0 && containerRef.current) {\n      pages.forEach((page, index) => {\n        renderPage(page, index);\n      });\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [pages]);\n\n  // Re-render pages when window is resized\n  useEffect(() => {\n    const handleResize = () => {\n      if (pages.length > 0) {\n        pages.forEach((page, index) => {\n          renderPage(page, index);\n        });\n      }\n    };\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, [pages]);\n  return /*#__PURE__*/_jsxDEV(ReactModal, {\n    isOpen: modalIsOpen,\n    onRequestClose: closeModal,\n    contentLabel: \"Document Preview\",\n    style: {\n      overlay: {\n        backgroundColor: 'rgba(0, 0, 0, 0.75)'\n      },\n      content: {\n        top: '50%',\n        left: '50%',\n        right: 'auto',\n        bottom: 'auto',\n        marginRight: '-50%',\n        transform: 'translate(-50%, -50%)',\n        width: '70%',\n        height: '90%',\n        padding: '20px',\n        borderRadius: '10px',\n        overflow: 'hidden'\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: closeModal,\n      style: {\n        position: \"absolute\",\n        top: \"10px\",\n        right: \"10px\",\n        background: \"transparent\",\n        border: \"none\",\n        fontSize: \"20px\",\n        cursor: \"pointer\",\n        zIndex: 1\n      },\n      children: \"X\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        height: '100%',\n        overflow: 'auto',\n        padding: '10px',\n        scrollbarWidth: 'thin'\n      },\n      children: [isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          justifyContent: 'center',\n          height: '200px',\n          color: '#666'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '50px',\n            height: '50px',\n            border: '3px solid #f3f3f3',\n            borderTop: '3px solid #3498db',\n            borderRadius: '50%',\n            animation: 'spin 1s linear infinite',\n            marginBottom: '20px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading PDF...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '200px',\n            height: '6px',\n            backgroundColor: '#f3f3f3',\n            borderRadius: '3px',\n            overflow: 'hidden',\n            marginTop: '10px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: `${loadingProgress}%`,\n              height: '100%',\n              backgroundColor: '#3498db',\n              transition: 'width 0.3s ease'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          style: {\n            marginTop: '5px'\n          },\n          children: [Math.round(loadingProgress), \"% loaded\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 11\n      }, this), pages.map((page, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '10px',\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"canvas\", {\n          ref: element => {\n            canvasRefs.current[index] = element;\n          },\n          style: {\n            maxWidth: '100%',\n            height: 'auto',\n            border: '1px solid black'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 13\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 11\n      }, this)), totalPages > pages.length && !isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '20px',\n          color: '#666',\n          fontStyle: 'italic'\n        },\n        children: [\"Loading remaining pages... (\", pages.length, \"/\", totalPages, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 193,\n    columnNumber: 5\n  }, this);\n};\n_s(PDFModal, \"bdem5RaQChnFhqrMTxHShA5srpA=\");\n_c = PDFModal;\nexport default PDFModal;\nvar _c;\n$RefreshReg$(_c, \"PDFModal\");", "map": {"version": 3, "names": ["useEffect", "useRef", "useState", "pdfjsLib", "ReactModal", "jsxDEV", "_jsxDEV", "setAppElement", "GlobalWorkerOptions", "workerSrc", "spinnerStyle", "document", "style", "createElement", "textContent", "head", "append<PERSON><PERSON><PERSON>", "PDFModal", "modalIsOpen", "closeModal", "documentUrl", "_s", "pages", "setPages", "isLoading", "setIsLoading", "loadingProgress", "setLoadingProgress", "totalPages", "setTotalPages", "selectedText", "setSelectedText", "showCopyButton", "setShowCopyButton", "canvasRefs", "textLayerRefs", "containerRef", "renderingRefs", "handleTextSelection", "selection", "window", "getSelection", "text", "toString", "trim", "copyToClipboard", "navigator", "clipboard", "writeText", "alert", "removeAllRanges", "err", "textArea", "value", "body", "select", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "renderPDF", "url", "pdf", "getDocument", "promise", "console", "log", "numPages", "initialPagesToLoad", "Math", "min", "pagesData", "i", "page", "getPage", "push", "loadRemainingPages", "error", "startPage", "existingPages", "updatedPages", "Promise", "resolve", "setTimeout", "renderPage", "index", "canvas", "current", "viewport", "getViewport", "scale", "containerWidth", "clientWidth", "width", "scaledViewport", "context", "getContext", "height", "renderContext", "canvasContext", "render", "length", "for<PERSON>ach", "handleResize", "addEventListener", "removeEventListener", "isOpen", "onRequestClose", "contentLabel", "overlay", "backgroundColor", "content", "top", "left", "right", "bottom", "marginRight", "transform", "padding", "borderRadius", "overflow", "children", "onClick", "position", "background", "border", "fontSize", "cursor", "zIndex", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "scrollbarWidth", "display", "flexDirection", "alignItems", "justifyContent", "color", "borderTop", "animation", "marginBottom", "marginTop", "transition", "round", "map", "element", "max<PERSON><PERSON><PERSON>", "textAlign", "fontStyle", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/StudyMaterial/PDFModal.js"], "sourcesContent": ["import { useEffect, useRef, useState } from \"react\";\r\nimport * as pdfjsLib from \"pdfjs-dist\";\r\nimport ReactModal from \"react-modal\";\r\nReactModal.setAppElement('#root');\r\n\r\npdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@2.15.349/build/pdf.worker.min.js`;\r\n\r\n// Add CSS for spinner animation\r\nconst spinnerStyle = `\r\n  @keyframes spin {\r\n    0% { transform: rotate(0deg); }\r\n    100% { transform: rotate(360deg); }\r\n  }\r\n`;\r\n\r\n// Inject the CSS\r\nif (typeof document !== 'undefined') {\r\n  const style = document.createElement('style');\r\n  style.textContent = spinnerStyle;\r\n  document.head.appendChild(style);\r\n}\r\n\r\nconst PDFModal = ({ modalIsOpen, closeModal, documentUrl }) => {\r\n  const [pages, setPages] = useState([]);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [loadingProgress, setLoadingProgress] = useState(0);\r\n  const [totalPages, setTotalPages] = useState(0);\r\n  const [selectedText, setSelectedText] = useState('');\r\n  const [showCopyButton, setShowCopyButton] = useState(false);\r\n  const canvasRefs = useRef([]);\r\n  const textLayerRefs = useRef([]);\r\n  const containerRef = useRef(null);\r\n  const renderingRefs = useRef({}); // Track rendering state per page\r\n\r\n  // Handle text selection\r\n  const handleTextSelection = () => {\r\n    const selection = window.getSelection();\r\n    const text = selection.toString().trim();\r\n\r\n    if (text) {\r\n      setSelectedText(text);\r\n      setShowCopyButton(true);\r\n    } else {\r\n      setSelectedText('');\r\n      setShowCopyButton(false);\r\n    }\r\n  };\r\n\r\n  // Copy selected text to clipboard\r\n  const copyToClipboard = async () => {\r\n    if (selectedText) {\r\n      try {\r\n        await navigator.clipboard.writeText(selectedText);\r\n        alert('Text copied to clipboard!');\r\n        setShowCopyButton(false);\r\n        window.getSelection().removeAllRanges();\r\n      } catch (err) {\r\n        // Fallback for older browsers\r\n        const textArea = document.createElement('textarea');\r\n        textArea.value = selectedText;\r\n        document.body.appendChild(textArea);\r\n        textArea.select();\r\n        document.execCommand('copy');\r\n        document.body.removeChild(textArea);\r\n        alert('Text copied to clipboard!');\r\n        setShowCopyButton(false);\r\n        window.getSelection().removeAllRanges();\r\n      }\r\n    }\r\n  };\r\n\r\n  const renderPDF = async (url) => {\r\n    try {\r\n      setIsLoading(true);\r\n      setLoadingProgress(0);\r\n\r\n      const pdf = await pdfjsLib.getDocument(url).promise;\r\n      console.log(\"PDF loaded\");\r\n\r\n      setTotalPages(pdf.numPages);\r\n\r\n      // Load pages progressively (first 3 pages immediately, then lazy load others)\r\n      const initialPagesToLoad = Math.min(3, pdf.numPages);\r\n      const pagesData = [];\r\n\r\n      for (let i = 1; i <= initialPagesToLoad; i++) {\r\n        const page = await pdf.getPage(i);\r\n        pagesData.push(page);\r\n        setLoadingProgress((i / pdf.numPages) * 100);\r\n      }\r\n\r\n      setPages(pagesData);\r\n      setIsLoading(false);\r\n\r\n      // Load remaining pages in background\r\n      if (pdf.numPages > initialPagesToLoad) {\r\n        loadRemainingPages(pdf, initialPagesToLoad + 1, pagesData);\r\n      }\r\n\r\n    } catch (error) {\r\n      console.error(\"Error loading PDF:\", error);\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const loadRemainingPages = async (pdf, startPage, existingPages) => {\r\n    const updatedPages = [...existingPages];\r\n\r\n    for (let i = startPage; i <= pdf.numPages; i++) {\r\n      try {\r\n        const page = await pdf.getPage(i);\r\n        updatedPages.push(page);\r\n        setPages([...updatedPages]);\r\n        setLoadingProgress((i / pdf.numPages) * 100);\r\n\r\n        // Small delay to prevent blocking the UI\r\n        await new Promise(resolve => setTimeout(resolve, 50));\r\n      } catch (error) {\r\n        console.error(`Error loading page ${i}:`, error);\r\n      }\r\n    }\r\n  };\r\n\r\n  const renderPage = async (page, index) => {\r\n    const canvas = canvasRefs.current[index];\r\n    if (!canvas || renderingRefs.current[index] || !containerRef.current) return;\r\n\r\n    try {\r\n      renderingRefs.current[index] = true;\r\n\r\n      const viewport = page.getViewport({ scale: 1.0 });\r\n      const containerWidth = containerRef.current.clientWidth;\r\n      const scale = containerWidth / viewport.width;\r\n      const scaledViewport = page.getViewport({ scale });\r\n\r\n      const context = canvas.getContext(\"2d\");\r\n      canvas.height = scaledViewport.height;\r\n      canvas.width = scaledViewport.width;\r\n      canvas.style.width = '100%';\r\n      canvas.style.height = 'auto';\r\n\r\n      const renderContext = {\r\n        canvasContext: context,\r\n        viewport: scaledViewport,\r\n      };\r\n\r\n      await page.render(renderContext).promise;\r\n      console.log(`Page ${index + 1} rendered`);\r\n    } catch (error) {\r\n      console.error(`Error rendering page ${index + 1}:`, error);\r\n    } finally {\r\n      renderingRefs.current[index] = false;\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (modalIsOpen && documentUrl) {\r\n      setPages([]);\r\n      setTotalPages(0);\r\n      setLoadingProgress(0);\r\n      canvasRefs.current = [];\r\n      renderingRefs.current = {};\r\n      renderPDF(documentUrl);\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [modalIsOpen, documentUrl]);\r\n\r\n  // Effect to render pages when they're loaded\r\n  useEffect(() => {\r\n    if (pages.length > 0 && containerRef.current) {\r\n      pages.forEach((page, index) => {\r\n        renderPage(page, index);\r\n      });\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [pages]);\r\n\r\n  // Re-render pages when window is resized\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      if (pages.length > 0) {\r\n        pages.forEach((page, index) => {\r\n          renderPage(page, index);\r\n        });\r\n      }\r\n    };\r\n\r\n    window.addEventListener('resize', handleResize);\r\n    return () => window.removeEventListener('resize', handleResize);\r\n  }, [pages]);\r\n\r\n  return (\r\n    <ReactModal\r\n      isOpen={modalIsOpen}\r\n      onRequestClose={closeModal}\r\n      contentLabel=\"Document Preview\"\r\n      style={{\r\n        overlay: {\r\n          backgroundColor: 'rgba(0, 0, 0, 0.75)'\r\n        },\r\n        content: {\r\n          top: '50%',\r\n          left: '50%',\r\n          right: 'auto',\r\n          bottom: 'auto',\r\n          marginRight: '-50%',\r\n          transform: 'translate(-50%, -50%)',\r\n          width: '70%',\r\n          height: '90%',\r\n          padding: '20px',\r\n          borderRadius: '10px',\r\n          overflow: 'hidden',\r\n        },\r\n      }}\r\n    >\r\n      <button\r\n        onClick={closeModal}\r\n        style={{\r\n          position: \"absolute\",\r\n          top: \"10px\",\r\n          right: \"10px\",\r\n          background: \"transparent\",\r\n          border: \"none\",\r\n          fontSize: \"20px\",\r\n          cursor: \"pointer\",\r\n          zIndex: 1,\r\n        }}\r\n      >\r\n        X\r\n      </button>\r\n\r\n      <div\r\n        ref={containerRef}\r\n        style={{\r\n          height: '100%',\r\n          overflow: 'auto',\r\n          padding: '10px',\r\n          scrollbarWidth: 'thin'\r\n        }}\r\n      >\r\n        {isLoading && (\r\n          <div style={{\r\n            display: 'flex',\r\n            flexDirection: 'column',\r\n            alignItems: 'center',\r\n            justifyContent: 'center',\r\n            height: '200px',\r\n            color: '#666'\r\n          }}>\r\n            <div style={{\r\n              width: '50px',\r\n              height: '50px',\r\n              border: '3px solid #f3f3f3',\r\n              borderTop: '3px solid #3498db',\r\n              borderRadius: '50%',\r\n              animation: 'spin 1s linear infinite',\r\n              marginBottom: '20px'\r\n            }}></div>\r\n            <p>Loading PDF...</p>\r\n            <div style={{\r\n              width: '200px',\r\n              height: '6px',\r\n              backgroundColor: '#f3f3f3',\r\n              borderRadius: '3px',\r\n              overflow: 'hidden',\r\n              marginTop: '10px'\r\n            }}>\r\n              <div style={{\r\n                width: `${loadingProgress}%`,\r\n                height: '100%',\r\n                backgroundColor: '#3498db',\r\n                transition: 'width 0.3s ease'\r\n              }}></div>\r\n            </div>\r\n            <small style={{ marginTop: '5px' }}>\r\n              {Math.round(loadingProgress)}% loaded\r\n            </small>\r\n          </div>\r\n        )}\r\n\r\n        {pages.map((page, index) => (\r\n          <div\r\n            key={index}\r\n            style={{\r\n              marginBottom: '10px',\r\n              display: 'flex',\r\n              flexDirection: 'column',\r\n              alignItems: 'center'\r\n            }}\r\n          >\r\n            <canvas\r\n              ref={element => {\r\n                canvasRefs.current[index] = element;\r\n              }}\r\n              style={{\r\n                maxWidth: '100%',\r\n                height: 'auto',\r\n                border: '1px solid black'\r\n              }}\r\n            />\r\n          </div>\r\n        ))}\r\n\r\n        {totalPages > pages.length && !isLoading && (\r\n          <div style={{\r\n            textAlign: 'center',\r\n            padding: '20px',\r\n            color: '#666',\r\n            fontStyle: 'italic'\r\n          }}>\r\n            Loading remaining pages... ({pages.length}/{totalPages})\r\n          </div>\r\n        )}\r\n      </div>\r\n    </ReactModal>\r\n  );\r\n};\r\n\r\nexport default PDFModal;"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,OAAO,KAAKC,QAAQ,MAAM,YAAY;AACtC,OAAOC,UAAU,MAAM,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AACrCF,UAAU,CAACG,aAAa,CAAC,OAAO,CAAC;AAEjCJ,QAAQ,CAACK,mBAAmB,CAACC,SAAS,GAAI,+DAA8D;;AAExG;AACA,MAAMC,YAAY,GAAI;AACtB;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;EACnC,MAAMC,KAAK,GAAGD,QAAQ,CAACE,aAAa,CAAC,OAAO,CAAC;EAC7CD,KAAK,CAACE,WAAW,GAAGJ,YAAY;EAChCC,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACJ,KAAK,CAAC;AAClC;AAEA,MAAMK,QAAQ,GAAGA,CAAC;EAAEC,WAAW;EAAEC,UAAU;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EAC7D,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACsB,SAAS,EAAEC,YAAY,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACwB,eAAe,EAAEC,kBAAkB,CAAC,GAAGzB,QAAQ,CAAC,CAAC,CAAC;EACzD,MAAM,CAAC0B,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC8B,cAAc,EAAEC,iBAAiB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAMgC,UAAU,GAAGjC,MAAM,CAAC,EAAE,CAAC;EAC7B,MAAMkC,aAAa,GAAGlC,MAAM,CAAC,EAAE,CAAC;EAChC,MAAMmC,YAAY,GAAGnC,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMoC,aAAa,GAAGpC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAElC;EACA,MAAMqC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,SAAS,GAAGC,MAAM,CAACC,YAAY,CAAC,CAAC;IACvC,MAAMC,IAAI,GAAGH,SAAS,CAACI,QAAQ,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;IAExC,IAAIF,IAAI,EAAE;MACRX,eAAe,CAACW,IAAI,CAAC;MACrBT,iBAAiB,CAAC,IAAI,CAAC;IACzB,CAAC,MAAM;MACLF,eAAe,CAAC,EAAE,CAAC;MACnBE,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;;EAED;EACA,MAAMY,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAIf,YAAY,EAAE;MAChB,IAAI;QACF,MAAMgB,SAAS,CAACC,SAAS,CAACC,SAAS,CAAClB,YAAY,CAAC;QACjDmB,KAAK,CAAC,2BAA2B,CAAC;QAClChB,iBAAiB,CAAC,KAAK,CAAC;QACxBO,MAAM,CAACC,YAAY,CAAC,CAAC,CAACS,eAAe,CAAC,CAAC;MACzC,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZ;QACA,MAAMC,QAAQ,GAAGzC,QAAQ,CAACE,aAAa,CAAC,UAAU,CAAC;QACnDuC,QAAQ,CAACC,KAAK,GAAGvB,YAAY;QAC7BnB,QAAQ,CAAC2C,IAAI,CAACtC,WAAW,CAACoC,QAAQ,CAAC;QACnCA,QAAQ,CAACG,MAAM,CAAC,CAAC;QACjB5C,QAAQ,CAAC6C,WAAW,CAAC,MAAM,CAAC;QAC5B7C,QAAQ,CAAC2C,IAAI,CAACG,WAAW,CAACL,QAAQ,CAAC;QACnCH,KAAK,CAAC,2BAA2B,CAAC;QAClChB,iBAAiB,CAAC,KAAK,CAAC;QACxBO,MAAM,CAACC,YAAY,CAAC,CAAC,CAACS,eAAe,CAAC,CAAC;MACzC;IACF;EACF,CAAC;EAED,MAAMQ,SAAS,GAAG,MAAOC,GAAG,IAAK;IAC/B,IAAI;MACFlC,YAAY,CAAC,IAAI,CAAC;MAClBE,kBAAkB,CAAC,CAAC,CAAC;MAErB,MAAMiC,GAAG,GAAG,MAAMzD,QAAQ,CAAC0D,WAAW,CAACF,GAAG,CAAC,CAACG,OAAO;MACnDC,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;MAEzBnC,aAAa,CAAC+B,GAAG,CAACK,QAAQ,CAAC;;MAE3B;MACA,MAAMC,kBAAkB,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAER,GAAG,CAACK,QAAQ,CAAC;MACpD,MAAMI,SAAS,GAAG,EAAE;MAEpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIJ,kBAAkB,EAAEI,CAAC,EAAE,EAAE;QAC5C,MAAMC,IAAI,GAAG,MAAMX,GAAG,CAACY,OAAO,CAACF,CAAC,CAAC;QACjCD,SAAS,CAACI,IAAI,CAACF,IAAI,CAAC;QACpB5C,kBAAkB,CAAE2C,CAAC,GAAGV,GAAG,CAACK,QAAQ,GAAI,GAAG,CAAC;MAC9C;MAEA1C,QAAQ,CAAC8C,SAAS,CAAC;MACnB5C,YAAY,CAAC,KAAK,CAAC;;MAEnB;MACA,IAAImC,GAAG,CAACK,QAAQ,GAAGC,kBAAkB,EAAE;QACrCQ,kBAAkB,CAACd,GAAG,EAAEM,kBAAkB,GAAG,CAAC,EAAEG,SAAS,CAAC;MAC5D;IAEF,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1ClD,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMiD,kBAAkB,GAAG,MAAAA,CAAOd,GAAG,EAAEgB,SAAS,EAAEC,aAAa,KAAK;IAClE,MAAMC,YAAY,GAAG,CAAC,GAAGD,aAAa,CAAC;IAEvC,KAAK,IAAIP,CAAC,GAAGM,SAAS,EAAEN,CAAC,IAAIV,GAAG,CAACK,QAAQ,EAAEK,CAAC,EAAE,EAAE;MAC9C,IAAI;QACF,MAAMC,IAAI,GAAG,MAAMX,GAAG,CAACY,OAAO,CAACF,CAAC,CAAC;QACjCQ,YAAY,CAACL,IAAI,CAACF,IAAI,CAAC;QACvBhD,QAAQ,CAAC,CAAC,GAAGuD,YAAY,CAAC,CAAC;QAC3BnD,kBAAkB,CAAE2C,CAAC,GAAGV,GAAG,CAACK,QAAQ,GAAI,GAAG,CAAC;;QAE5C;QACA,MAAM,IAAIc,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,EAAE,CAAC,CAAC;MACvD,CAAC,CAAC,OAAOL,KAAK,EAAE;QACdZ,OAAO,CAACY,KAAK,CAAE,sBAAqBL,CAAE,GAAE,EAAEK,KAAK,CAAC;MAClD;IACF;EACF,CAAC;EAED,MAAMO,UAAU,GAAG,MAAAA,CAAOX,IAAI,EAAEY,KAAK,KAAK;IACxC,MAAMC,MAAM,GAAGlD,UAAU,CAACmD,OAAO,CAACF,KAAK,CAAC;IACxC,IAAI,CAACC,MAAM,IAAI/C,aAAa,CAACgD,OAAO,CAACF,KAAK,CAAC,IAAI,CAAC/C,YAAY,CAACiD,OAAO,EAAE;IAEtE,IAAI;MACFhD,aAAa,CAACgD,OAAO,CAACF,KAAK,CAAC,GAAG,IAAI;MAEnC,MAAMG,QAAQ,GAAGf,IAAI,CAACgB,WAAW,CAAC;QAAEC,KAAK,EAAE;MAAI,CAAC,CAAC;MACjD,MAAMC,cAAc,GAAGrD,YAAY,CAACiD,OAAO,CAACK,WAAW;MACvD,MAAMF,KAAK,GAAGC,cAAc,GAAGH,QAAQ,CAACK,KAAK;MAC7C,MAAMC,cAAc,GAAGrB,IAAI,CAACgB,WAAW,CAAC;QAAEC;MAAM,CAAC,CAAC;MAElD,MAAMK,OAAO,GAAGT,MAAM,CAACU,UAAU,CAAC,IAAI,CAAC;MACvCV,MAAM,CAACW,MAAM,GAAGH,cAAc,CAACG,MAAM;MACrCX,MAAM,CAACO,KAAK,GAAGC,cAAc,CAACD,KAAK;MACnCP,MAAM,CAACxE,KAAK,CAAC+E,KAAK,GAAG,MAAM;MAC3BP,MAAM,CAACxE,KAAK,CAACmF,MAAM,GAAG,MAAM;MAE5B,MAAMC,aAAa,GAAG;QACpBC,aAAa,EAAEJ,OAAO;QACtBP,QAAQ,EAAEM;MACZ,CAAC;MAED,MAAMrB,IAAI,CAAC2B,MAAM,CAACF,aAAa,CAAC,CAAClC,OAAO;MACxCC,OAAO,CAACC,GAAG,CAAE,QAAOmB,KAAK,GAAG,CAAE,WAAU,CAAC;IAC3C,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAE,wBAAuBQ,KAAK,GAAG,CAAE,GAAE,EAAER,KAAK,CAAC;IAC5D,CAAC,SAAS;MACRtC,aAAa,CAACgD,OAAO,CAACF,KAAK,CAAC,GAAG,KAAK;IACtC;EACF,CAAC;EAEDnF,SAAS,CAAC,MAAM;IACd,IAAIkB,WAAW,IAAIE,WAAW,EAAE;MAC9BG,QAAQ,CAAC,EAAE,CAAC;MACZM,aAAa,CAAC,CAAC,CAAC;MAChBF,kBAAkB,CAAC,CAAC,CAAC;MACrBO,UAAU,CAACmD,OAAO,GAAG,EAAE;MACvBhD,aAAa,CAACgD,OAAO,GAAG,CAAC,CAAC;MAC1B3B,SAAS,CAACtC,WAAW,CAAC;IACxB;IACA;EACF,CAAC,EAAE,CAACF,WAAW,EAAEE,WAAW,CAAC,CAAC;;EAE9B;EACApB,SAAS,CAAC,MAAM;IACd,IAAIsB,KAAK,CAAC6E,MAAM,GAAG,CAAC,IAAI/D,YAAY,CAACiD,OAAO,EAAE;MAC5C/D,KAAK,CAAC8E,OAAO,CAAC,CAAC7B,IAAI,EAAEY,KAAK,KAAK;QAC7BD,UAAU,CAACX,IAAI,EAAEY,KAAK,CAAC;MACzB,CAAC,CAAC;IACJ;IACA;EACF,CAAC,EAAE,CAAC7D,KAAK,CAAC,CAAC;;EAEX;EACAtB,SAAS,CAAC,MAAM;IACd,MAAMqG,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAI/E,KAAK,CAAC6E,MAAM,GAAG,CAAC,EAAE;QACpB7E,KAAK,CAAC8E,OAAO,CAAC,CAAC7B,IAAI,EAAEY,KAAK,KAAK;UAC7BD,UAAU,CAACX,IAAI,EAAEY,KAAK,CAAC;QACzB,CAAC,CAAC;MACJ;IACF,CAAC;IAED3C,MAAM,CAAC8D,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAM7D,MAAM,CAAC+D,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EACjE,CAAC,EAAE,CAAC/E,KAAK,CAAC,CAAC;EAEX,oBACEhB,OAAA,CAACF,UAAU;IACToG,MAAM,EAAEtF,WAAY;IACpBuF,cAAc,EAAEtF,UAAW;IAC3BuF,YAAY,EAAC,kBAAkB;IAC/B9F,KAAK,EAAE;MACL+F,OAAO,EAAE;QACPC,eAAe,EAAE;MACnB,CAAC;MACDC,OAAO,EAAE;QACPC,GAAG,EAAE,KAAK;QACVC,IAAI,EAAE,KAAK;QACXC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdC,WAAW,EAAE,MAAM;QACnBC,SAAS,EAAE,uBAAuB;QAClCxB,KAAK,EAAE,KAAK;QACZI,MAAM,EAAE,KAAK;QACbqB,OAAO,EAAE,MAAM;QACfC,YAAY,EAAE,MAAM;QACpBC,QAAQ,EAAE;MACZ;IACF,CAAE;IAAAC,QAAA,gBAEFjH,OAAA;MACEkH,OAAO,EAAErG,UAAW;MACpBP,KAAK,EAAE;QACL6G,QAAQ,EAAE,UAAU;QACpBX,GAAG,EAAE,MAAM;QACXE,KAAK,EAAE,MAAM;QACbU,UAAU,EAAE,aAAa;QACzBC,MAAM,EAAE,MAAM;QACdC,QAAQ,EAAE,MAAM;QAChBC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE;MACV,CAAE;MAAAP,QAAA,EACH;IAED;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAET5H,OAAA;MACE6H,GAAG,EAAE/F,YAAa;MAClBxB,KAAK,EAAE;QACLmF,MAAM,EAAE,MAAM;QACduB,QAAQ,EAAE,MAAM;QAChBF,OAAO,EAAE,MAAM;QACfgB,cAAc,EAAE;MAClB,CAAE;MAAAb,QAAA,GAED/F,SAAS,iBACRlB,OAAA;QAAKM,KAAK,EAAE;UACVyH,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBzC,MAAM,EAAE,OAAO;UACf0C,KAAK,EAAE;QACT,CAAE;QAAAlB,QAAA,gBACAjH,OAAA;UAAKM,KAAK,EAAE;YACV+E,KAAK,EAAE,MAAM;YACbI,MAAM,EAAE,MAAM;YACd4B,MAAM,EAAE,mBAAmB;YAC3Be,SAAS,EAAE,mBAAmB;YAC9BrB,YAAY,EAAE,KAAK;YACnBsB,SAAS,EAAE,yBAAyB;YACpCC,YAAY,EAAE;UAChB;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACT5H,OAAA;UAAAiH,QAAA,EAAG;QAAc;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACrB5H,OAAA;UAAKM,KAAK,EAAE;YACV+E,KAAK,EAAE,OAAO;YACdI,MAAM,EAAE,KAAK;YACba,eAAe,EAAE,SAAS;YAC1BS,YAAY,EAAE,KAAK;YACnBC,QAAQ,EAAE,QAAQ;YAClBuB,SAAS,EAAE;UACb,CAAE;UAAAtB,QAAA,eACAjH,OAAA;YAAKM,KAAK,EAAE;cACV+E,KAAK,EAAG,GAAEjE,eAAgB,GAAE;cAC5BqE,MAAM,EAAE,MAAM;cACda,eAAe,EAAE,SAAS;cAC1BkC,UAAU,EAAE;YACd;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN5H,OAAA;UAAOM,KAAK,EAAE;YAAEiI,SAAS,EAAE;UAAM,CAAE;UAAAtB,QAAA,GAChCpD,IAAI,CAAC4E,KAAK,CAACrH,eAAe,CAAC,EAAC,UAC/B;QAAA;UAAAqG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACN,EAEA5G,KAAK,CAAC0H,GAAG,CAAC,CAACzE,IAAI,EAAEY,KAAK,kBACrB7E,OAAA;QAEEM,KAAK,EAAE;UACLgI,YAAY,EAAE,MAAM;UACpBP,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBC,UAAU,EAAE;QACd,CAAE;QAAAhB,QAAA,eAEFjH,OAAA;UACE6H,GAAG,EAAEc,OAAO,IAAI;YACd/G,UAAU,CAACmD,OAAO,CAACF,KAAK,CAAC,GAAG8D,OAAO;UACrC,CAAE;UACFrI,KAAK,EAAE;YACLsI,QAAQ,EAAE,MAAM;YAChBnD,MAAM,EAAE,MAAM;YACd4B,MAAM,EAAE;UACV;QAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAjBG/C,KAAK;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAkBP,CACN,CAAC,EAEDtG,UAAU,GAAGN,KAAK,CAAC6E,MAAM,IAAI,CAAC3E,SAAS,iBACtClB,OAAA;QAAKM,KAAK,EAAE;UACVuI,SAAS,EAAE,QAAQ;UACnB/B,OAAO,EAAE,MAAM;UACfqB,KAAK,EAAE,MAAM;UACbW,SAAS,EAAE;QACb,CAAE;QAAA7B,QAAA,GAAC,8BAC2B,EAACjG,KAAK,CAAC6E,MAAM,EAAC,GAAC,EAACvE,UAAU,EAAC,GACzD;MAAA;QAAAmG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEjB,CAAC;AAAC7G,EAAA,CAtSIJ,QAAQ;AAAAoI,EAAA,GAARpI,QAAQ;AAwSd,eAAeA,QAAQ;AAAC,IAAAoI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}