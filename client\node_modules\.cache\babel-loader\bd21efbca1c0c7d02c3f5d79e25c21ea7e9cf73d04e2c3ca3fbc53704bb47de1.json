{"ast": null, "code": "import { genTable } from 'rc-table';\n/**\n * Same as `rc-table` but we modify trigger children update logic instead.\n */\nexport default genTable((prev, next) => {\n  const {\n    _renderTimes: prevRenderTimes\n  } = prev;\n  const {\n    _renderTimes: nextRenderTimes\n  } = next;\n  return prevRenderTimes !== nextRenderTimes;\n});", "map": {"version": 3, "names": ["genTable", "prev", "next", "_renderTimes", "prevRenderTimes", "nextRenderTimes"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/table/RcTable.js"], "sourcesContent": ["import { genTable } from 'rc-table';\n/**\n * Same as `rc-table` but we modify trigger children update logic instead.\n */\nexport default genTable((prev, next) => {\n  const {\n    _renderTimes: prevRenderTimes\n  } = prev;\n  const {\n    _renderTimes: nextRenderTimes\n  } = next;\n  return prevRenderTimes !== nextRenderTimes;\n});"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,UAAU;AACnC;AACA;AACA;AACA,eAAeA,QAAQ,CAAC,CAACC,IAAI,EAAEC,IAAI,KAAK;EACtC,MAAM;IACJC,YAAY,EAAEC;EAChB,CAAC,GAAGH,IAAI;EACR,MAAM;IACJE,YAAY,EAAEE;EAChB,CAAC,GAAGH,IAAI;EACR,OAAOE,eAAe,KAAKC,eAAe;AAC5C,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}