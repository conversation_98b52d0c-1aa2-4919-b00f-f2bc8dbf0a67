{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar _excluded = [\"key\", \"forceRender\", \"style\", \"className\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport TabContext from \"../TabContext\";\nimport TabPane from \"./TabPane\";\nexport default function TabPanelList(_ref) {\n  var id = _ref.id,\n    activeKey = _ref.activeKey,\n    animated = _ref.animated,\n    tabPosition = _ref.tabPosition,\n    destroyInactiveTabPane = _ref.destroyInactiveTabPane;\n  var _React$useContext = React.useContext(TabContext),\n    prefixCls = _React$useContext.prefixCls,\n    tabs = _React$useContext.tabs;\n  var tabPaneAnimated = animated.tabPane;\n  var tabPanePrefixCls = \"\".concat(prefixCls, \"-tabpane\");\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-content-holder\"))\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-content\"), \"\".concat(prefixCls, \"-content-\").concat(tabPosition), _defineProperty({}, \"\".concat(prefixCls, \"-content-animated\"), tabPaneAnimated))\n  }, tabs.map(function (_ref2) {\n    var key = _ref2.key,\n      forceRender = _ref2.forceRender,\n      paneStyle = _ref2.style,\n      paneClassName = _ref2.className,\n      restTabProps = _objectWithoutProperties(_ref2, _excluded);\n    var active = key === activeKey;\n    return /*#__PURE__*/React.createElement(CSSMotion, _extends({\n      key: key,\n      visible: active,\n      forceRender: forceRender,\n      removeOnLeave: !!destroyInactiveTabPane,\n      leavedClassName: \"\".concat(tabPanePrefixCls, \"-hidden\")\n    }, animated.tabPaneMotion), function (_ref3, ref) {\n      var motionStyle = _ref3.style,\n        motionClassName = _ref3.className;\n      return /*#__PURE__*/React.createElement(TabPane, _extends({}, restTabProps, {\n        prefixCls: tabPanePrefixCls,\n        id: id,\n        tabKey: key,\n        animated: tabPaneAnimated,\n        active: active,\n        style: _objectSpread(_objectSpread({}, paneStyle), motionStyle),\n        className: classNames(paneClassName, motionClassName),\n        ref: ref\n      }));\n    });\n  })));\n}", "map": {"version": 3, "names": ["_extends", "_objectSpread", "_objectWithoutProperties", "_defineProperty", "_excluded", "React", "classNames", "CSSMotion", "TabContext", "TabPane", "TabPanelList", "_ref", "id", "active<PERSON><PERSON>", "animated", "tabPosition", "destroyInactiveTabPane", "_React$useContext", "useContext", "prefixCls", "tabs", "tabPaneAnimated", "tabPane", "tabPanePrefixCls", "concat", "createElement", "className", "map", "_ref2", "key", "forceRender", "paneStyle", "style", "paneClassName", "restTabProps", "active", "visible", "removeOnLeave", "leavedClassName", "tabPaneMotion", "_ref3", "ref", "motionStyle", "motionClassName", "tabKey"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-tabs/es/TabPanelList/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar _excluded = [\"key\", \"forceRender\", \"style\", \"className\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport TabContext from \"../TabContext\";\nimport TabPane from \"./TabPane\";\nexport default function TabPanelList(_ref) {\n  var id = _ref.id,\n    activeKey = _ref.activeKey,\n    animated = _ref.animated,\n    tabPosition = _ref.tabPosition,\n    destroyInactiveTabPane = _ref.destroyInactiveTabPane;\n  var _React$useContext = React.useContext(TabContext),\n    prefixCls = _React$useContext.prefixCls,\n    tabs = _React$useContext.tabs;\n  var tabPaneAnimated = animated.tabPane;\n  var tabPanePrefixCls = \"\".concat(prefixCls, \"-tabpane\");\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-content-holder\"))\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-content\"), \"\".concat(prefixCls, \"-content-\").concat(tabPosition), _defineProperty({}, \"\".concat(prefixCls, \"-content-animated\"), tabPaneAnimated))\n  }, tabs.map(function (_ref2) {\n    var key = _ref2.key,\n      forceRender = _ref2.forceRender,\n      paneStyle = _ref2.style,\n      paneClassName = _ref2.className,\n      restTabProps = _objectWithoutProperties(_ref2, _excluded);\n    var active = key === activeKey;\n    return /*#__PURE__*/React.createElement(CSSMotion, _extends({\n      key: key,\n      visible: active,\n      forceRender: forceRender,\n      removeOnLeave: !!destroyInactiveTabPane,\n      leavedClassName: \"\".concat(tabPanePrefixCls, \"-hidden\")\n    }, animated.tabPaneMotion), function (_ref3, ref) {\n      var motionStyle = _ref3.style,\n        motionClassName = _ref3.className;\n      return /*#__PURE__*/React.createElement(TabPane, _extends({}, restTabProps, {\n        prefixCls: tabPanePrefixCls,\n        id: id,\n        tabKey: key,\n        animated: tabPaneAnimated,\n        active: active,\n        style: _objectSpread(_objectSpread({}, paneStyle), motionStyle),\n        className: classNames(paneClassName, motionClassName),\n        ref: ref\n      }));\n    });\n  })));\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,eAAe,MAAM,2CAA2C;AACvE,IAAIC,SAAS,GAAG,CAAC,KAAK,EAAE,aAAa,EAAE,OAAO,EAAE,WAAW,CAAC;AAC5D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,UAAU,MAAM,eAAe;AACtC,OAAOC,OAAO,MAAM,WAAW;AAC/B,eAAe,SAASC,YAAYA,CAACC,IAAI,EAAE;EACzC,IAAIC,EAAE,GAAGD,IAAI,CAACC,EAAE;IACdC,SAAS,GAAGF,IAAI,CAACE,SAAS;IAC1BC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;IACxBC,WAAW,GAAGJ,IAAI,CAACI,WAAW;IAC9BC,sBAAsB,GAAGL,IAAI,CAACK,sBAAsB;EACtD,IAAIC,iBAAiB,GAAGZ,KAAK,CAACa,UAAU,CAACV,UAAU,CAAC;IAClDW,SAAS,GAAGF,iBAAiB,CAACE,SAAS;IACvCC,IAAI,GAAGH,iBAAiB,CAACG,IAAI;EAC/B,IAAIC,eAAe,GAAGP,QAAQ,CAACQ,OAAO;EACtC,IAAIC,gBAAgB,GAAG,EAAE,CAACC,MAAM,CAACL,SAAS,EAAE,UAAU,CAAC;EACvD,OAAO,aAAad,KAAK,CAACoB,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAEpB,UAAU,CAAC,EAAE,CAACkB,MAAM,CAACL,SAAS,EAAE,iBAAiB,CAAC;EAC/D,CAAC,EAAE,aAAad,KAAK,CAACoB,aAAa,CAAC,KAAK,EAAE;IACzCC,SAAS,EAAEpB,UAAU,CAAC,EAAE,CAACkB,MAAM,CAACL,SAAS,EAAE,UAAU,CAAC,EAAE,EAAE,CAACK,MAAM,CAACL,SAAS,EAAE,WAAW,CAAC,CAACK,MAAM,CAACT,WAAW,CAAC,EAAEZ,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACqB,MAAM,CAACL,SAAS,EAAE,mBAAmB,CAAC,EAAEE,eAAe,CAAC;EAChM,CAAC,EAAED,IAAI,CAACO,GAAG,CAAC,UAAUC,KAAK,EAAE;IAC3B,IAAIC,GAAG,GAAGD,KAAK,CAACC,GAAG;MACjBC,WAAW,GAAGF,KAAK,CAACE,WAAW;MAC/BC,SAAS,GAAGH,KAAK,CAACI,KAAK;MACvBC,aAAa,GAAGL,KAAK,CAACF,SAAS;MAC/BQ,YAAY,GAAGhC,wBAAwB,CAAC0B,KAAK,EAAExB,SAAS,CAAC;IAC3D,IAAI+B,MAAM,GAAGN,GAAG,KAAKhB,SAAS;IAC9B,OAAO,aAAaR,KAAK,CAACoB,aAAa,CAAClB,SAAS,EAAEP,QAAQ,CAAC;MAC1D6B,GAAG,EAAEA,GAAG;MACRO,OAAO,EAAED,MAAM;MACfL,WAAW,EAAEA,WAAW;MACxBO,aAAa,EAAE,CAAC,CAACrB,sBAAsB;MACvCsB,eAAe,EAAE,EAAE,CAACd,MAAM,CAACD,gBAAgB,EAAE,SAAS;IACxD,CAAC,EAAET,QAAQ,CAACyB,aAAa,CAAC,EAAE,UAAUC,KAAK,EAAEC,GAAG,EAAE;MAChD,IAAIC,WAAW,GAAGF,KAAK,CAACR,KAAK;QAC3BW,eAAe,GAAGH,KAAK,CAACd,SAAS;MACnC,OAAO,aAAarB,KAAK,CAACoB,aAAa,CAAChB,OAAO,EAAET,QAAQ,CAAC,CAAC,CAAC,EAAEkC,YAAY,EAAE;QAC1Ef,SAAS,EAAEI,gBAAgB;QAC3BX,EAAE,EAAEA,EAAE;QACNgC,MAAM,EAAEf,GAAG;QACXf,QAAQ,EAAEO,eAAe;QACzBc,MAAM,EAAEA,MAAM;QACdH,KAAK,EAAE/B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE8B,SAAS,CAAC,EAAEW,WAAW,CAAC;QAC/DhB,SAAS,EAAEpB,UAAU,CAAC2B,aAAa,EAAEU,eAAe,CAAC;QACrDF,GAAG,EAAEA;MACP,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}