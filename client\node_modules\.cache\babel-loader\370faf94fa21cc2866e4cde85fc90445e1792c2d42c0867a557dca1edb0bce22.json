{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport useState from \"rc-util/es/hooks/useState\";\nimport * as React from 'react';\nimport { useEffect, useRef } from 'react';\nimport { STATUS_APPEAR, STATUS_ENTER, STATUS_LEAVE, STATUS_NONE, STEP_ACTIVE, STEP_PREPARE, STEP_PREPARED, STEP_START } from \"../interface\";\nimport useDomMotionEvents from \"./useDomMotionEvents\";\nimport useIsomorphicLayoutEffect from \"./useIsomorphicLayoutEffect\";\nimport useStepQueue, { DoStep, isActive, SkipStep } from \"./useStepQueue\";\nexport default function useStatus(supportMotion, visible, getElement, _ref) {\n  var _ref$motionEnter = _ref.motionEnter,\n    motionEnter = _ref$motionEnter === void 0 ? true : _ref$motionEnter,\n    _ref$motionAppear = _ref.motionAppear,\n    motionAppear = _ref$motionAppear === void 0 ? true : _ref$motionAppear,\n    _ref$motionLeave = _ref.motionLeave,\n    motionLeave = _ref$motionLeave === void 0 ? true : _ref$motionLeave,\n    motionDeadline = _ref.motionDeadline,\n    motionLeaveImmediately = _ref.motionLeaveImmediately,\n    onAppearPrepare = _ref.onAppearPrepare,\n    onEnterPrepare = _ref.onEnterPrepare,\n    onLeavePrepare = _ref.onLeavePrepare,\n    onAppearStart = _ref.onAppearStart,\n    onEnterStart = _ref.onEnterStart,\n    onLeaveStart = _ref.onLeaveStart,\n    onAppearActive = _ref.onAppearActive,\n    onEnterActive = _ref.onEnterActive,\n    onLeaveActive = _ref.onLeaveActive,\n    onAppearEnd = _ref.onAppearEnd,\n    onEnterEnd = _ref.onEnterEnd,\n    onLeaveEnd = _ref.onLeaveEnd,\n    onVisibleChanged = _ref.onVisibleChanged;\n  // Used for outer render usage to avoid `visible: false & status: none` to render nothing\n  var _useState = useState(),\n    _useState2 = _slicedToArray(_useState, 2),\n    asyncVisible = _useState2[0],\n    setAsyncVisible = _useState2[1];\n  var _useState3 = useState(STATUS_NONE),\n    _useState4 = _slicedToArray(_useState3, 2),\n    status = _useState4[0],\n    setStatus = _useState4[1];\n  var _useState5 = useState(null),\n    _useState6 = _slicedToArray(_useState5, 2),\n    style = _useState6[0],\n    setStyle = _useState6[1];\n  var mountedRef = useRef(false);\n  var deadlineRef = useRef(null);\n\n  // =========================== Dom Node ===========================\n  function getDomElement() {\n    return getElement();\n  }\n\n  // ========================== Motion End ==========================\n  var activeRef = useRef(false);\n\n  /**\n   * Clean up status & style\n   */\n  function updateMotionEndStatus() {\n    setStatus(STATUS_NONE, true);\n    setStyle(null, true);\n  }\n  function onInternalMotionEnd(event) {\n    var element = getDomElement();\n    if (event && !event.deadline && event.target !== element) {\n      // event exists\n      // not initiated by deadline\n      // transitionEnd not fired by inner elements\n      return;\n    }\n    var currentActive = activeRef.current;\n    var canEnd;\n    if (status === STATUS_APPEAR && currentActive) {\n      canEnd = onAppearEnd === null || onAppearEnd === void 0 ? void 0 : onAppearEnd(element, event);\n    } else if (status === STATUS_ENTER && currentActive) {\n      canEnd = onEnterEnd === null || onEnterEnd === void 0 ? void 0 : onEnterEnd(element, event);\n    } else if (status === STATUS_LEAVE && currentActive) {\n      canEnd = onLeaveEnd === null || onLeaveEnd === void 0 ? void 0 : onLeaveEnd(element, event);\n    }\n\n    // Only update status when `canEnd` and not destroyed\n    if (status !== STATUS_NONE && currentActive && canEnd !== false) {\n      updateMotionEndStatus();\n    }\n  }\n  var _useDomMotionEvents = useDomMotionEvents(onInternalMotionEnd),\n    _useDomMotionEvents2 = _slicedToArray(_useDomMotionEvents, 1),\n    patchMotionEvents = _useDomMotionEvents2[0];\n\n  // ============================= Step =============================\n  var getEventHandlers = function getEventHandlers(targetStatus) {\n    var _ref2, _ref3, _ref4;\n    switch (targetStatus) {\n      case STATUS_APPEAR:\n        return _ref2 = {}, _defineProperty(_ref2, STEP_PREPARE, onAppearPrepare), _defineProperty(_ref2, STEP_START, onAppearStart), _defineProperty(_ref2, STEP_ACTIVE, onAppearActive), _ref2;\n      case STATUS_ENTER:\n        return _ref3 = {}, _defineProperty(_ref3, STEP_PREPARE, onEnterPrepare), _defineProperty(_ref3, STEP_START, onEnterStart), _defineProperty(_ref3, STEP_ACTIVE, onEnterActive), _ref3;\n      case STATUS_LEAVE:\n        return _ref4 = {}, _defineProperty(_ref4, STEP_PREPARE, onLeavePrepare), _defineProperty(_ref4, STEP_START, onLeaveStart), _defineProperty(_ref4, STEP_ACTIVE, onLeaveActive), _ref4;\n      default:\n        return {};\n    }\n  };\n  var eventHandlers = React.useMemo(function () {\n    return getEventHandlers(status);\n  }, [status]);\n  var _useStepQueue = useStepQueue(status, !supportMotion, function (newStep) {\n      // Only prepare step can be skip\n      if (newStep === STEP_PREPARE) {\n        var onPrepare = eventHandlers[STEP_PREPARE];\n        if (!onPrepare) {\n          return SkipStep;\n        }\n        return onPrepare(getDomElement());\n      }\n\n      // Rest step is sync update\n      if (step in eventHandlers) {\n        var _eventHandlers$step;\n        setStyle(((_eventHandlers$step = eventHandlers[step]) === null || _eventHandlers$step === void 0 ? void 0 : _eventHandlers$step.call(eventHandlers, getDomElement(), null)) || null);\n      }\n      if (step === STEP_ACTIVE) {\n        // Patch events when motion needed\n        patchMotionEvents(getDomElement());\n        if (motionDeadline > 0) {\n          clearTimeout(deadlineRef.current);\n          deadlineRef.current = setTimeout(function () {\n            onInternalMotionEnd({\n              deadline: true\n            });\n          }, motionDeadline);\n        }\n      }\n      if (step === STEP_PREPARED) {\n        updateMotionEndStatus();\n      }\n      return DoStep;\n    }),\n    _useStepQueue2 = _slicedToArray(_useStepQueue, 2),\n    startStep = _useStepQueue2[0],\n    step = _useStepQueue2[1];\n  var active = isActive(step);\n  activeRef.current = active;\n\n  // ============================ Status ============================\n  // Update with new status\n  useIsomorphicLayoutEffect(function () {\n    setAsyncVisible(visible);\n    var isMounted = mountedRef.current;\n    mountedRef.current = true;\n\n    // if (!supportMotion) {\n    //   return;\n    // }\n\n    var nextStatus;\n\n    // Appear\n    if (!isMounted && visible && motionAppear) {\n      nextStatus = STATUS_APPEAR;\n    }\n\n    // Enter\n    if (isMounted && visible && motionEnter) {\n      nextStatus = STATUS_ENTER;\n    }\n\n    // Leave\n    if (isMounted && !visible && motionLeave || !isMounted && motionLeaveImmediately && !visible && motionLeave) {\n      nextStatus = STATUS_LEAVE;\n    }\n    var nextEventHandlers = getEventHandlers(nextStatus);\n\n    // Update to next status\n    if (nextStatus && (supportMotion || nextEventHandlers[STEP_PREPARE])) {\n      setStatus(nextStatus);\n      startStep();\n    } else {\n      // Set back in case no motion but prev status has prepare step\n      setStatus(STATUS_NONE);\n    }\n  }, [visible]);\n\n  // ============================ Effect ============================\n  // Reset when motion changed\n  useEffect(function () {\n    if (\n    // Cancel appear\n    status === STATUS_APPEAR && !motionAppear ||\n    // Cancel enter\n    status === STATUS_ENTER && !motionEnter ||\n    // Cancel leave\n    status === STATUS_LEAVE && !motionLeave) {\n      setStatus(STATUS_NONE);\n    }\n  }, [motionAppear, motionEnter, motionLeave]);\n  useEffect(function () {\n    return function () {\n      mountedRef.current = false;\n      clearTimeout(deadlineRef.current);\n    };\n  }, []);\n\n  // Trigger `onVisibleChanged`\n  var firstMountChangeRef = React.useRef(false);\n  useEffect(function () {\n    // [visible & motion not end] => [!visible & motion end] still need trigger onVisibleChanged\n    if (asyncVisible) {\n      firstMountChangeRef.current = true;\n    }\n    if (asyncVisible !== undefined && status === STATUS_NONE) {\n      // Skip first render is invisible since it's nothing changed\n      if (firstMountChangeRef.current || asyncVisible) {\n        onVisibleChanged === null || onVisibleChanged === void 0 ? void 0 : onVisibleChanged(asyncVisible);\n      }\n      firstMountChangeRef.current = true;\n    }\n  }, [asyncVisible, status]);\n\n  // ============================ Styles ============================\n  var mergedStyle = style;\n  if (eventHandlers[STEP_PREPARE] && step === STEP_START) {\n    mergedStyle = _objectSpread({\n      transition: 'none'\n    }, mergedStyle);\n  }\n  return [status, step, mergedStyle, asyncVisible !== null && asyncVisible !== void 0 ? asyncVisible : visible];\n}", "map": {"version": 3, "names": ["_objectSpread", "_defineProperty", "_slicedToArray", "useState", "React", "useEffect", "useRef", "STATUS_APPEAR", "STATUS_ENTER", "STATUS_LEAVE", "STATUS_NONE", "STEP_ACTIVE", "STEP_PREPARE", "STEP_PREPARED", "STEP_START", "useDomMotionEvents", "useIsomorphicLayoutEffect", "useStepQueue", "DoStep", "isActive", "SkipStep", "useStatus", "supportMotion", "visible", "getElement", "_ref", "_ref$motionEnter", "motionEnter", "_ref$motionAppear", "motionAppear", "_ref$motionLeave", "motionLeave", "motionDeadline", "motionLeaveImmediately", "onAppearPrepare", "onEnterPrepare", "onLeavePrepare", "onAppearStart", "onEnterStart", "onLeaveStart", "onAppearActive", "onEnterActive", "onLeaveActive", "onAppearEnd", "onEnterEnd", "onLeaveEnd", "onVisibleChanged", "_useState", "_useState2", "asyncVisible", "setAsyncVisible", "_useState3", "_useState4", "status", "setStatus", "_useState5", "_useState6", "style", "setStyle", "mountedRef", "deadlineRef", "getDomElement", "activeRef", "updateMotionEndStatus", "onInternalMotionEnd", "event", "element", "deadline", "target", "currentActive", "current", "canEnd", "_useDomMotionEvents", "_useDomMotionEvents2", "patchMotionEvents", "getEventHandlers", "targetStatus", "_ref2", "_ref3", "_ref4", "eventHandlers", "useMemo", "_useStepQueue", "newStep", "onPrepare", "step", "_eventHandlers$step", "call", "clearTimeout", "setTimeout", "_useStepQueue2", "startStep", "active", "isMounted", "nextStatus", "nextEventHandlers", "firstMountChangeRef", "undefined", "mergedStyle", "transition"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-motion/es/hooks/useStatus.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport useState from \"rc-util/es/hooks/useState\";\nimport * as React from 'react';\nimport { useEffect, useRef } from 'react';\nimport { STATUS_APPEAR, STATUS_ENTER, STATUS_LEAVE, STATUS_NONE, STEP_ACTIVE, STEP_PREPARE, STEP_PREPARED, STEP_START } from \"../interface\";\nimport useDomMotionEvents from \"./useDomMotionEvents\";\nimport useIsomorphicLayoutEffect from \"./useIsomorphicLayoutEffect\";\nimport useStepQueue, { DoStep, isActive, SkipStep } from \"./useStepQueue\";\nexport default function useStatus(supportMotion, visible, getElement, _ref) {\n  var _ref$motionEnter = _ref.motionEnter,\n    motionEnter = _ref$motionEnter === void 0 ? true : _ref$motionEnter,\n    _ref$motionAppear = _ref.motionAppear,\n    motionAppear = _ref$motionAppear === void 0 ? true : _ref$motionAppear,\n    _ref$motionLeave = _ref.motionLeave,\n    motionLeave = _ref$motionLeave === void 0 ? true : _ref$motionLeave,\n    motionDeadline = _ref.motionDeadline,\n    motionLeaveImmediately = _ref.motionLeaveImmediately,\n    onAppearPrepare = _ref.onAppearPrepare,\n    onEnterPrepare = _ref.onEnterPrepare,\n    onLeavePrepare = _ref.onLeavePrepare,\n    onAppearStart = _ref.onAppearStart,\n    onEnterStart = _ref.onEnterStart,\n    onLeaveStart = _ref.onLeaveStart,\n    onAppearActive = _ref.onAppearActive,\n    onEnterActive = _ref.onEnterActive,\n    onLeaveActive = _ref.onLeaveActive,\n    onAppearEnd = _ref.onAppearEnd,\n    onEnterEnd = _ref.onEnterEnd,\n    onLeaveEnd = _ref.onLeaveEnd,\n    onVisibleChanged = _ref.onVisibleChanged;\n  // Used for outer render usage to avoid `visible: false & status: none` to render nothing\n  var _useState = useState(),\n    _useState2 = _slicedToArray(_useState, 2),\n    asyncVisible = _useState2[0],\n    setAsyncVisible = _useState2[1];\n  var _useState3 = useState(STATUS_NONE),\n    _useState4 = _slicedToArray(_useState3, 2),\n    status = _useState4[0],\n    setStatus = _useState4[1];\n  var _useState5 = useState(null),\n    _useState6 = _slicedToArray(_useState5, 2),\n    style = _useState6[0],\n    setStyle = _useState6[1];\n  var mountedRef = useRef(false);\n  var deadlineRef = useRef(null);\n\n  // =========================== Dom Node ===========================\n  function getDomElement() {\n    return getElement();\n  }\n\n  // ========================== Motion End ==========================\n  var activeRef = useRef(false);\n\n  /**\n   * Clean up status & style\n   */\n  function updateMotionEndStatus() {\n    setStatus(STATUS_NONE, true);\n    setStyle(null, true);\n  }\n  function onInternalMotionEnd(event) {\n    var element = getDomElement();\n    if (event && !event.deadline && event.target !== element) {\n      // event exists\n      // not initiated by deadline\n      // transitionEnd not fired by inner elements\n      return;\n    }\n    var currentActive = activeRef.current;\n    var canEnd;\n    if (status === STATUS_APPEAR && currentActive) {\n      canEnd = onAppearEnd === null || onAppearEnd === void 0 ? void 0 : onAppearEnd(element, event);\n    } else if (status === STATUS_ENTER && currentActive) {\n      canEnd = onEnterEnd === null || onEnterEnd === void 0 ? void 0 : onEnterEnd(element, event);\n    } else if (status === STATUS_LEAVE && currentActive) {\n      canEnd = onLeaveEnd === null || onLeaveEnd === void 0 ? void 0 : onLeaveEnd(element, event);\n    }\n\n    // Only update status when `canEnd` and not destroyed\n    if (status !== STATUS_NONE && currentActive && canEnd !== false) {\n      updateMotionEndStatus();\n    }\n  }\n  var _useDomMotionEvents = useDomMotionEvents(onInternalMotionEnd),\n    _useDomMotionEvents2 = _slicedToArray(_useDomMotionEvents, 1),\n    patchMotionEvents = _useDomMotionEvents2[0];\n\n  // ============================= Step =============================\n  var getEventHandlers = function getEventHandlers(targetStatus) {\n    var _ref2, _ref3, _ref4;\n    switch (targetStatus) {\n      case STATUS_APPEAR:\n        return _ref2 = {}, _defineProperty(_ref2, STEP_PREPARE, onAppearPrepare), _defineProperty(_ref2, STEP_START, onAppearStart), _defineProperty(_ref2, STEP_ACTIVE, onAppearActive), _ref2;\n      case STATUS_ENTER:\n        return _ref3 = {}, _defineProperty(_ref3, STEP_PREPARE, onEnterPrepare), _defineProperty(_ref3, STEP_START, onEnterStart), _defineProperty(_ref3, STEP_ACTIVE, onEnterActive), _ref3;\n      case STATUS_LEAVE:\n        return _ref4 = {}, _defineProperty(_ref4, STEP_PREPARE, onLeavePrepare), _defineProperty(_ref4, STEP_START, onLeaveStart), _defineProperty(_ref4, STEP_ACTIVE, onLeaveActive), _ref4;\n      default:\n        return {};\n    }\n  };\n  var eventHandlers = React.useMemo(function () {\n    return getEventHandlers(status);\n  }, [status]);\n  var _useStepQueue = useStepQueue(status, !supportMotion, function (newStep) {\n      // Only prepare step can be skip\n      if (newStep === STEP_PREPARE) {\n        var onPrepare = eventHandlers[STEP_PREPARE];\n        if (!onPrepare) {\n          return SkipStep;\n        }\n        return onPrepare(getDomElement());\n      }\n\n      // Rest step is sync update\n      if (step in eventHandlers) {\n        var _eventHandlers$step;\n        setStyle(((_eventHandlers$step = eventHandlers[step]) === null || _eventHandlers$step === void 0 ? void 0 : _eventHandlers$step.call(eventHandlers, getDomElement(), null)) || null);\n      }\n      if (step === STEP_ACTIVE) {\n        // Patch events when motion needed\n        patchMotionEvents(getDomElement());\n        if (motionDeadline > 0) {\n          clearTimeout(deadlineRef.current);\n          deadlineRef.current = setTimeout(function () {\n            onInternalMotionEnd({\n              deadline: true\n            });\n          }, motionDeadline);\n        }\n      }\n      if (step === STEP_PREPARED) {\n        updateMotionEndStatus();\n      }\n      return DoStep;\n    }),\n    _useStepQueue2 = _slicedToArray(_useStepQueue, 2),\n    startStep = _useStepQueue2[0],\n    step = _useStepQueue2[1];\n  var active = isActive(step);\n  activeRef.current = active;\n\n  // ============================ Status ============================\n  // Update with new status\n  useIsomorphicLayoutEffect(function () {\n    setAsyncVisible(visible);\n    var isMounted = mountedRef.current;\n    mountedRef.current = true;\n\n    // if (!supportMotion) {\n    //   return;\n    // }\n\n    var nextStatus;\n\n    // Appear\n    if (!isMounted && visible && motionAppear) {\n      nextStatus = STATUS_APPEAR;\n    }\n\n    // Enter\n    if (isMounted && visible && motionEnter) {\n      nextStatus = STATUS_ENTER;\n    }\n\n    // Leave\n    if (isMounted && !visible && motionLeave || !isMounted && motionLeaveImmediately && !visible && motionLeave) {\n      nextStatus = STATUS_LEAVE;\n    }\n    var nextEventHandlers = getEventHandlers(nextStatus);\n\n    // Update to next status\n    if (nextStatus && (supportMotion || nextEventHandlers[STEP_PREPARE])) {\n      setStatus(nextStatus);\n      startStep();\n    } else {\n      // Set back in case no motion but prev status has prepare step\n      setStatus(STATUS_NONE);\n    }\n  }, [visible]);\n\n  // ============================ Effect ============================\n  // Reset when motion changed\n  useEffect(function () {\n    if (\n    // Cancel appear\n    status === STATUS_APPEAR && !motionAppear ||\n    // Cancel enter\n    status === STATUS_ENTER && !motionEnter ||\n    // Cancel leave\n    status === STATUS_LEAVE && !motionLeave) {\n      setStatus(STATUS_NONE);\n    }\n  }, [motionAppear, motionEnter, motionLeave]);\n  useEffect(function () {\n    return function () {\n      mountedRef.current = false;\n      clearTimeout(deadlineRef.current);\n    };\n  }, []);\n\n  // Trigger `onVisibleChanged`\n  var firstMountChangeRef = React.useRef(false);\n  useEffect(function () {\n    // [visible & motion not end] => [!visible & motion end] still need trigger onVisibleChanged\n    if (asyncVisible) {\n      firstMountChangeRef.current = true;\n    }\n    if (asyncVisible !== undefined && status === STATUS_NONE) {\n      // Skip first render is invisible since it's nothing changed\n      if (firstMountChangeRef.current || asyncVisible) {\n        onVisibleChanged === null || onVisibleChanged === void 0 ? void 0 : onVisibleChanged(asyncVisible);\n      }\n      firstMountChangeRef.current = true;\n    }\n  }, [asyncVisible, status]);\n\n  // ============================ Styles ============================\n  var mergedStyle = style;\n  if (eventHandlers[STEP_PREPARE] && step === STEP_START) {\n    mergedStyle = _objectSpread({\n      transition: 'none'\n    }, mergedStyle);\n  }\n  return [status, step, mergedStyle, asyncVisible !== null && asyncVisible !== void 0 ? asyncVisible : visible];\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACzC,SAASC,aAAa,EAAEC,YAAY,EAAEC,YAAY,EAAEC,WAAW,EAAEC,WAAW,EAAEC,YAAY,EAAEC,aAAa,EAAEC,UAAU,QAAQ,cAAc;AAC3I,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,yBAAyB,MAAM,6BAA6B;AACnE,OAAOC,YAAY,IAAIC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,gBAAgB;AACzE,eAAe,SAASC,SAASA,CAACC,aAAa,EAAEC,OAAO,EAAEC,UAAU,EAAEC,IAAI,EAAE;EAC1E,IAAIC,gBAAgB,GAAGD,IAAI,CAACE,WAAW;IACrCA,WAAW,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,gBAAgB;IACnEE,iBAAiB,GAAGH,IAAI,CAACI,YAAY;IACrCA,YAAY,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,iBAAiB;IACtEE,gBAAgB,GAAGL,IAAI,CAACM,WAAW;IACnCA,WAAW,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,gBAAgB;IACnEE,cAAc,GAAGP,IAAI,CAACO,cAAc;IACpCC,sBAAsB,GAAGR,IAAI,CAACQ,sBAAsB;IACpDC,eAAe,GAAGT,IAAI,CAACS,eAAe;IACtCC,cAAc,GAAGV,IAAI,CAACU,cAAc;IACpCC,cAAc,GAAGX,IAAI,CAACW,cAAc;IACpCC,aAAa,GAAGZ,IAAI,CAACY,aAAa;IAClCC,YAAY,GAAGb,IAAI,CAACa,YAAY;IAChCC,YAAY,GAAGd,IAAI,CAACc,YAAY;IAChCC,cAAc,GAAGf,IAAI,CAACe,cAAc;IACpCC,aAAa,GAAGhB,IAAI,CAACgB,aAAa;IAClCC,aAAa,GAAGjB,IAAI,CAACiB,aAAa;IAClCC,WAAW,GAAGlB,IAAI,CAACkB,WAAW;IAC9BC,UAAU,GAAGnB,IAAI,CAACmB,UAAU;IAC5BC,UAAU,GAAGpB,IAAI,CAACoB,UAAU;IAC5BC,gBAAgB,GAAGrB,IAAI,CAACqB,gBAAgB;EAC1C;EACA,IAAIC,SAAS,GAAG5C,QAAQ,CAAC,CAAC;IACxB6C,UAAU,GAAG9C,cAAc,CAAC6C,SAAS,EAAE,CAAC,CAAC;IACzCE,YAAY,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC5BE,eAAe,GAAGF,UAAU,CAAC,CAAC,CAAC;EACjC,IAAIG,UAAU,GAAGhD,QAAQ,CAACO,WAAW,CAAC;IACpC0C,UAAU,GAAGlD,cAAc,CAACiD,UAAU,EAAE,CAAC,CAAC;IAC1CE,MAAM,GAAGD,UAAU,CAAC,CAAC,CAAC;IACtBE,SAAS,GAAGF,UAAU,CAAC,CAAC,CAAC;EAC3B,IAAIG,UAAU,GAAGpD,QAAQ,CAAC,IAAI,CAAC;IAC7BqD,UAAU,GAAGtD,cAAc,CAACqD,UAAU,EAAE,CAAC,CAAC;IAC1CE,KAAK,GAAGD,UAAU,CAAC,CAAC,CAAC;IACrBE,QAAQ,GAAGF,UAAU,CAAC,CAAC,CAAC;EAC1B,IAAIG,UAAU,GAAGrD,MAAM,CAAC,KAAK,CAAC;EAC9B,IAAIsD,WAAW,GAAGtD,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,SAASuD,aAAaA,CAAA,EAAG;IACvB,OAAOrC,UAAU,CAAC,CAAC;EACrB;;EAEA;EACA,IAAIsC,SAAS,GAAGxD,MAAM,CAAC,KAAK,CAAC;;EAE7B;AACF;AACA;EACE,SAASyD,qBAAqBA,CAAA,EAAG;IAC/BT,SAAS,CAAC5C,WAAW,EAAE,IAAI,CAAC;IAC5BgD,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC;EACtB;EACA,SAASM,mBAAmBA,CAACC,KAAK,EAAE;IAClC,IAAIC,OAAO,GAAGL,aAAa,CAAC,CAAC;IAC7B,IAAII,KAAK,IAAI,CAACA,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACG,MAAM,KAAKF,OAAO,EAAE;MACxD;MACA;MACA;MACA;IACF;IACA,IAAIG,aAAa,GAAGP,SAAS,CAACQ,OAAO;IACrC,IAAIC,MAAM;IACV,IAAIlB,MAAM,KAAK9C,aAAa,IAAI8D,aAAa,EAAE;MAC7CE,MAAM,GAAG5B,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACuB,OAAO,EAAED,KAAK,CAAC;IAChG,CAAC,MAAM,IAAIZ,MAAM,KAAK7C,YAAY,IAAI6D,aAAa,EAAE;MACnDE,MAAM,GAAG3B,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACsB,OAAO,EAAED,KAAK,CAAC;IAC7F,CAAC,MAAM,IAAIZ,MAAM,KAAK5C,YAAY,IAAI4D,aAAa,EAAE;MACnDE,MAAM,GAAG1B,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACqB,OAAO,EAAED,KAAK,CAAC;IAC7F;;IAEA;IACA,IAAIZ,MAAM,KAAK3C,WAAW,IAAI2D,aAAa,IAAIE,MAAM,KAAK,KAAK,EAAE;MAC/DR,qBAAqB,CAAC,CAAC;IACzB;EACF;EACA,IAAIS,mBAAmB,GAAGzD,kBAAkB,CAACiD,mBAAmB,CAAC;IAC/DS,oBAAoB,GAAGvE,cAAc,CAACsE,mBAAmB,EAAE,CAAC,CAAC;IAC7DE,iBAAiB,GAAGD,oBAAoB,CAAC,CAAC,CAAC;;EAE7C;EACA,IAAIE,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,YAAY,EAAE;IAC7D,IAAIC,KAAK,EAAEC,KAAK,EAAEC,KAAK;IACvB,QAAQH,YAAY;MAClB,KAAKrE,aAAa;QAChB,OAAOsE,KAAK,GAAG,CAAC,CAAC,EAAE5E,eAAe,CAAC4E,KAAK,EAAEjE,YAAY,EAAEsB,eAAe,CAAC,EAAEjC,eAAe,CAAC4E,KAAK,EAAE/D,UAAU,EAAEuB,aAAa,CAAC,EAAEpC,eAAe,CAAC4E,KAAK,EAAElE,WAAW,EAAE6B,cAAc,CAAC,EAAEqC,KAAK;MACzL,KAAKrE,YAAY;QACf,OAAOsE,KAAK,GAAG,CAAC,CAAC,EAAE7E,eAAe,CAAC6E,KAAK,EAAElE,YAAY,EAAEuB,cAAc,CAAC,EAAElC,eAAe,CAAC6E,KAAK,EAAEhE,UAAU,EAAEwB,YAAY,CAAC,EAAErC,eAAe,CAAC6E,KAAK,EAAEnE,WAAW,EAAE8B,aAAa,CAAC,EAAEqC,KAAK;MACtL,KAAKrE,YAAY;QACf,OAAOsE,KAAK,GAAG,CAAC,CAAC,EAAE9E,eAAe,CAAC8E,KAAK,EAAEnE,YAAY,EAAEwB,cAAc,CAAC,EAAEnC,eAAe,CAAC8E,KAAK,EAAEjE,UAAU,EAAEyB,YAAY,CAAC,EAAEtC,eAAe,CAAC8E,KAAK,EAAEpE,WAAW,EAAE+B,aAAa,CAAC,EAAEqC,KAAK;MACtL;QACE,OAAO,CAAC,CAAC;IACb;EACF,CAAC;EACD,IAAIC,aAAa,GAAG5E,KAAK,CAAC6E,OAAO,CAAC,YAAY;IAC5C,OAAON,gBAAgB,CAACtB,MAAM,CAAC;EACjC,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACZ,IAAI6B,aAAa,GAAGjE,YAAY,CAACoC,MAAM,EAAE,CAAC/B,aAAa,EAAE,UAAU6D,OAAO,EAAE;MACxE;MACA,IAAIA,OAAO,KAAKvE,YAAY,EAAE;QAC5B,IAAIwE,SAAS,GAAGJ,aAAa,CAACpE,YAAY,CAAC;QAC3C,IAAI,CAACwE,SAAS,EAAE;UACd,OAAOhE,QAAQ;QACjB;QACA,OAAOgE,SAAS,CAACvB,aAAa,CAAC,CAAC,CAAC;MACnC;;MAEA;MACA,IAAIwB,IAAI,IAAIL,aAAa,EAAE;QACzB,IAAIM,mBAAmB;QACvB5B,QAAQ,CAAC,CAAC,CAAC4B,mBAAmB,GAAGN,aAAa,CAACK,IAAI,CAAC,MAAM,IAAI,IAAIC,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAACC,IAAI,CAACP,aAAa,EAAEnB,aAAa,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,IAAI,CAAC;MACtL;MACA,IAAIwB,IAAI,KAAK1E,WAAW,EAAE;QACxB;QACA+D,iBAAiB,CAACb,aAAa,CAAC,CAAC,CAAC;QAClC,IAAI7B,cAAc,GAAG,CAAC,EAAE;UACtBwD,YAAY,CAAC5B,WAAW,CAACU,OAAO,CAAC;UACjCV,WAAW,CAACU,OAAO,GAAGmB,UAAU,CAAC,YAAY;YAC3CzB,mBAAmB,CAAC;cAClBG,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ,CAAC,EAAEnC,cAAc,CAAC;QACpB;MACF;MACA,IAAIqD,IAAI,KAAKxE,aAAa,EAAE;QAC1BkD,qBAAqB,CAAC,CAAC;MACzB;MACA,OAAO7C,MAAM;IACf,CAAC,CAAC;IACFwE,cAAc,GAAGxF,cAAc,CAACgF,aAAa,EAAE,CAAC,CAAC;IACjDS,SAAS,GAAGD,cAAc,CAAC,CAAC,CAAC;IAC7BL,IAAI,GAAGK,cAAc,CAAC,CAAC,CAAC;EAC1B,IAAIE,MAAM,GAAGzE,QAAQ,CAACkE,IAAI,CAAC;EAC3BvB,SAAS,CAACQ,OAAO,GAAGsB,MAAM;;EAE1B;EACA;EACA5E,yBAAyB,CAAC,YAAY;IACpCkC,eAAe,CAAC3B,OAAO,CAAC;IACxB,IAAIsE,SAAS,GAAGlC,UAAU,CAACW,OAAO;IAClCX,UAAU,CAACW,OAAO,GAAG,IAAI;;IAEzB;IACA;IACA;;IAEA,IAAIwB,UAAU;;IAEd;IACA,IAAI,CAACD,SAAS,IAAItE,OAAO,IAAIM,YAAY,EAAE;MACzCiE,UAAU,GAAGvF,aAAa;IAC5B;;IAEA;IACA,IAAIsF,SAAS,IAAItE,OAAO,IAAII,WAAW,EAAE;MACvCmE,UAAU,GAAGtF,YAAY;IAC3B;;IAEA;IACA,IAAIqF,SAAS,IAAI,CAACtE,OAAO,IAAIQ,WAAW,IAAI,CAAC8D,SAAS,IAAI5D,sBAAsB,IAAI,CAACV,OAAO,IAAIQ,WAAW,EAAE;MAC3G+D,UAAU,GAAGrF,YAAY;IAC3B;IACA,IAAIsF,iBAAiB,GAAGpB,gBAAgB,CAACmB,UAAU,CAAC;;IAEpD;IACA,IAAIA,UAAU,KAAKxE,aAAa,IAAIyE,iBAAiB,CAACnF,YAAY,CAAC,CAAC,EAAE;MACpE0C,SAAS,CAACwC,UAAU,CAAC;MACrBH,SAAS,CAAC,CAAC;IACb,CAAC,MAAM;MACL;MACArC,SAAS,CAAC5C,WAAW,CAAC;IACxB;EACF,CAAC,EAAE,CAACa,OAAO,CAAC,CAAC;;EAEb;EACA;EACAlB,SAAS,CAAC,YAAY;IACpB;IACA;IACAgD,MAAM,KAAK9C,aAAa,IAAI,CAACsB,YAAY;IACzC;IACAwB,MAAM,KAAK7C,YAAY,IAAI,CAACmB,WAAW;IACvC;IACA0B,MAAM,KAAK5C,YAAY,IAAI,CAACsB,WAAW,EAAE;MACvCuB,SAAS,CAAC5C,WAAW,CAAC;IACxB;EACF,CAAC,EAAE,CAACmB,YAAY,EAAEF,WAAW,EAAEI,WAAW,CAAC,CAAC;EAC5C1B,SAAS,CAAC,YAAY;IACpB,OAAO,YAAY;MACjBsD,UAAU,CAACW,OAAO,GAAG,KAAK;MAC1BkB,YAAY,CAAC5B,WAAW,CAACU,OAAO,CAAC;IACnC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,IAAI0B,mBAAmB,GAAG5F,KAAK,CAACE,MAAM,CAAC,KAAK,CAAC;EAC7CD,SAAS,CAAC,YAAY;IACpB;IACA,IAAI4C,YAAY,EAAE;MAChB+C,mBAAmB,CAAC1B,OAAO,GAAG,IAAI;IACpC;IACA,IAAIrB,YAAY,KAAKgD,SAAS,IAAI5C,MAAM,KAAK3C,WAAW,EAAE;MACxD;MACA,IAAIsF,mBAAmB,CAAC1B,OAAO,IAAIrB,YAAY,EAAE;QAC/CH,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACG,YAAY,CAAC;MACpG;MACA+C,mBAAmB,CAAC1B,OAAO,GAAG,IAAI;IACpC;EACF,CAAC,EAAE,CAACrB,YAAY,EAAEI,MAAM,CAAC,CAAC;;EAE1B;EACA,IAAI6C,WAAW,GAAGzC,KAAK;EACvB,IAAIuB,aAAa,CAACpE,YAAY,CAAC,IAAIyE,IAAI,KAAKvE,UAAU,EAAE;IACtDoF,WAAW,GAAGlG,aAAa,CAAC;MAC1BmG,UAAU,EAAE;IACd,CAAC,EAAED,WAAW,CAAC;EACjB;EACA,OAAO,CAAC7C,MAAM,EAAEgC,IAAI,EAAEa,WAAW,EAAEjD,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAG1B,OAAO,CAAC;AAC/G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}