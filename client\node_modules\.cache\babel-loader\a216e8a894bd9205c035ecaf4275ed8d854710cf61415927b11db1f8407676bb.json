{"ast": null, "code": "import { inputToRGB, rgbToHex, rgbToHsv } from '@ctrl/tinycolor';\nvar hueStep = 2; // 色相阶梯\n\nvar saturationStep = 0.16; // 饱和度阶梯，浅色部分\n\nvar saturationStep2 = 0.05; // 饱和度阶梯，深色部分\n\nvar brightnessStep1 = 0.05; // 亮度阶梯，浅色部分\n\nvar brightnessStep2 = 0.15; // 亮度阶梯，深色部分\n\nvar lightColorCount = 5; // 浅色数量，主色上\n\nvar darkColorCount = 4; // 深色数量，主色下\n// 暗色主题颜色映射关系表\n\nvar darkColorMap = [{\n  index: 7,\n  opacity: 0.15\n}, {\n  index: 6,\n  opacity: 0.25\n}, {\n  index: 5,\n  opacity: 0.3\n}, {\n  index: 5,\n  opacity: 0.45\n}, {\n  index: 5,\n  opacity: 0.65\n}, {\n  index: 5,\n  opacity: 0.85\n}, {\n  index: 4,\n  opacity: 0.9\n}, {\n  index: 3,\n  opacity: 0.95\n}, {\n  index: 2,\n  opacity: 0.97\n}, {\n  index: 1,\n  opacity: 0.98\n}];\n\n// Wrapper function ported from TinyColor.prototype.toHsv\n// Keep it here because of `hsv.h * 360`\nfunction toHsv(_ref) {\n  var r = _ref.r,\n    g = _ref.g,\n    b = _ref.b;\n  var hsv = rgbToHsv(r, g, b);\n  return {\n    h: hsv.h * 360,\n    s: hsv.s,\n    v: hsv.v\n  };\n} // Wrapper function ported from TinyColor.prototype.toHexString\n// Keep it here because of the prefix `#`\n\nfunction toHex(_ref2) {\n  var r = _ref2.r,\n    g = _ref2.g,\n    b = _ref2.b;\n  return \"#\".concat(rgbToHex(r, g, b, false));\n} // Wrapper function ported from TinyColor.prototype.mix, not treeshakable.\n// Amount in range [0, 1]\n// Assume color1 & color2 has no alpha, since the following src code did so.\n\nfunction mix(rgb1, rgb2, amount) {\n  var p = amount / 100;\n  var rgb = {\n    r: (rgb2.r - rgb1.r) * p + rgb1.r,\n    g: (rgb2.g - rgb1.g) * p + rgb1.g,\n    b: (rgb2.b - rgb1.b) * p + rgb1.b\n  };\n  return rgb;\n}\nfunction getHue(hsv, i, light) {\n  var hue; // 根据色相不同，色相转向不同\n\n  if (Math.round(hsv.h) >= 60 && Math.round(hsv.h) <= 240) {\n    hue = light ? Math.round(hsv.h) - hueStep * i : Math.round(hsv.h) + hueStep * i;\n  } else {\n    hue = light ? Math.round(hsv.h) + hueStep * i : Math.round(hsv.h) - hueStep * i;\n  }\n  if (hue < 0) {\n    hue += 360;\n  } else if (hue >= 360) {\n    hue -= 360;\n  }\n  return hue;\n}\nfunction getSaturation(hsv, i, light) {\n  // grey color don't change saturation\n  if (hsv.h === 0 && hsv.s === 0) {\n    return hsv.s;\n  }\n  var saturation;\n  if (light) {\n    saturation = hsv.s - saturationStep * i;\n  } else if (i === darkColorCount) {\n    saturation = hsv.s + saturationStep;\n  } else {\n    saturation = hsv.s + saturationStep2 * i;\n  } // 边界值修正\n\n  if (saturation > 1) {\n    saturation = 1;\n  } // 第一格的 s 限制在 0.06-0.1 之间\n\n  if (light && i === lightColorCount && saturation > 0.1) {\n    saturation = 0.1;\n  }\n  if (saturation < 0.06) {\n    saturation = 0.06;\n  }\n  return Number(saturation.toFixed(2));\n}\nfunction getValue(hsv, i, light) {\n  var value;\n  if (light) {\n    value = hsv.v + brightnessStep1 * i;\n  } else {\n    value = hsv.v - brightnessStep2 * i;\n  }\n  if (value > 1) {\n    value = 1;\n  }\n  return Number(value.toFixed(2));\n}\nexport default function generate(color) {\n  var opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var patterns = [];\n  var pColor = inputToRGB(color);\n  for (var i = lightColorCount; i > 0; i -= 1) {\n    var hsv = toHsv(pColor);\n    var colorString = toHex(inputToRGB({\n      h: getHue(hsv, i, true),\n      s: getSaturation(hsv, i, true),\n      v: getValue(hsv, i, true)\n    }));\n    patterns.push(colorString);\n  }\n  patterns.push(toHex(pColor));\n  for (var _i = 1; _i <= darkColorCount; _i += 1) {\n    var _hsv = toHsv(pColor);\n    var _colorString = toHex(inputToRGB({\n      h: getHue(_hsv, _i),\n      s: getSaturation(_hsv, _i),\n      v: getValue(_hsv, _i)\n    }));\n    patterns.push(_colorString);\n  } // dark theme patterns\n\n  if (opts.theme === 'dark') {\n    return darkColorMap.map(function (_ref3) {\n      var index = _ref3.index,\n        opacity = _ref3.opacity;\n      var darkColorString = toHex(mix(inputToRGB(opts.backgroundColor || '#141414'), inputToRGB(patterns[index]), opacity * 100));\n      return darkColorString;\n    });\n  }\n  return patterns;\n}", "map": {"version": 3, "names": ["inputToRGB", "rgbToHex", "rgbToHsv", "hueStep", "saturationStep", "saturationStep2", "brightnessStep1", "brightnessStep2", "lightColorCount", "darkColorCount", "darkColorMap", "index", "opacity", "toHsv", "_ref", "r", "g", "b", "hsv", "h", "s", "v", "toHex", "_ref2", "concat", "mix", "rgb1", "rgb2", "amount", "p", "rgb", "getHue", "i", "light", "hue", "Math", "round", "getSaturation", "saturation", "Number", "toFixed", "getValue", "value", "generate", "color", "opts", "arguments", "length", "undefined", "patterns", "pColor", "colorString", "push", "_i", "_hsv", "_colorString", "theme", "map", "_ref3", "darkColorString", "backgroundColor"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/@ant-design/colors/es/generate.js"], "sourcesContent": ["import { inputToRGB, rgbToHex, rgbToHsv } from '@ctrl/tinycolor';\nvar hueStep = 2; // 色相阶梯\n\nvar saturationStep = 0.16; // 饱和度阶梯，浅色部分\n\nvar saturationStep2 = 0.05; // 饱和度阶梯，深色部分\n\nvar brightnessStep1 = 0.05; // 亮度阶梯，浅色部分\n\nvar brightnessStep2 = 0.15; // 亮度阶梯，深色部分\n\nvar lightColorCount = 5; // 浅色数量，主色上\n\nvar darkColorCount = 4; // 深色数量，主色下\n// 暗色主题颜色映射关系表\n\nvar darkColorMap = [{\n  index: 7,\n  opacity: 0.15\n}, {\n  index: 6,\n  opacity: 0.25\n}, {\n  index: 5,\n  opacity: 0.3\n}, {\n  index: 5,\n  opacity: 0.45\n}, {\n  index: 5,\n  opacity: 0.65\n}, {\n  index: 5,\n  opacity: 0.85\n}, {\n  index: 4,\n  opacity: 0.9\n}, {\n  index: 3,\n  opacity: 0.95\n}, {\n  index: 2,\n  opacity: 0.97\n}, {\n  index: 1,\n  opacity: 0.98\n}];\n\n// Wrapper function ported from TinyColor.prototype.toHsv\n// Keep it here because of `hsv.h * 360`\nfunction toHsv(_ref) {\n  var r = _ref.r,\n      g = _ref.g,\n      b = _ref.b;\n  var hsv = rgbToHsv(r, g, b);\n  return {\n    h: hsv.h * 360,\n    s: hsv.s,\n    v: hsv.v\n  };\n} // Wrapper function ported from TinyColor.prototype.toHexString\n// Keep it here because of the prefix `#`\n\n\nfunction toHex(_ref2) {\n  var r = _ref2.r,\n      g = _ref2.g,\n      b = _ref2.b;\n  return \"#\".concat(rgbToHex(r, g, b, false));\n} // Wrapper function ported from TinyColor.prototype.mix, not treeshakable.\n// Amount in range [0, 1]\n// Assume color1 & color2 has no alpha, since the following src code did so.\n\n\nfunction mix(rgb1, rgb2, amount) {\n  var p = amount / 100;\n  var rgb = {\n    r: (rgb2.r - rgb1.r) * p + rgb1.r,\n    g: (rgb2.g - rgb1.g) * p + rgb1.g,\n    b: (rgb2.b - rgb1.b) * p + rgb1.b\n  };\n  return rgb;\n}\n\nfunction getHue(hsv, i, light) {\n  var hue; // 根据色相不同，色相转向不同\n\n  if (Math.round(hsv.h) >= 60 && Math.round(hsv.h) <= 240) {\n    hue = light ? Math.round(hsv.h) - hueStep * i : Math.round(hsv.h) + hueStep * i;\n  } else {\n    hue = light ? Math.round(hsv.h) + hueStep * i : Math.round(hsv.h) - hueStep * i;\n  }\n\n  if (hue < 0) {\n    hue += 360;\n  } else if (hue >= 360) {\n    hue -= 360;\n  }\n\n  return hue;\n}\n\nfunction getSaturation(hsv, i, light) {\n  // grey color don't change saturation\n  if (hsv.h === 0 && hsv.s === 0) {\n    return hsv.s;\n  }\n\n  var saturation;\n\n  if (light) {\n    saturation = hsv.s - saturationStep * i;\n  } else if (i === darkColorCount) {\n    saturation = hsv.s + saturationStep;\n  } else {\n    saturation = hsv.s + saturationStep2 * i;\n  } // 边界值修正\n\n\n  if (saturation > 1) {\n    saturation = 1;\n  } // 第一格的 s 限制在 0.06-0.1 之间\n\n\n  if (light && i === lightColorCount && saturation > 0.1) {\n    saturation = 0.1;\n  }\n\n  if (saturation < 0.06) {\n    saturation = 0.06;\n  }\n\n  return Number(saturation.toFixed(2));\n}\n\nfunction getValue(hsv, i, light) {\n  var value;\n\n  if (light) {\n    value = hsv.v + brightnessStep1 * i;\n  } else {\n    value = hsv.v - brightnessStep2 * i;\n  }\n\n  if (value > 1) {\n    value = 1;\n  }\n\n  return Number(value.toFixed(2));\n}\n\nexport default function generate(color) {\n  var opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var patterns = [];\n  var pColor = inputToRGB(color);\n\n  for (var i = lightColorCount; i > 0; i -= 1) {\n    var hsv = toHsv(pColor);\n    var colorString = toHex(inputToRGB({\n      h: getHue(hsv, i, true),\n      s: getSaturation(hsv, i, true),\n      v: getValue(hsv, i, true)\n    }));\n    patterns.push(colorString);\n  }\n\n  patterns.push(toHex(pColor));\n\n  for (var _i = 1; _i <= darkColorCount; _i += 1) {\n    var _hsv = toHsv(pColor);\n\n    var _colorString = toHex(inputToRGB({\n      h: getHue(_hsv, _i),\n      s: getSaturation(_hsv, _i),\n      v: getValue(_hsv, _i)\n    }));\n\n    patterns.push(_colorString);\n  } // dark theme patterns\n\n\n  if (opts.theme === 'dark') {\n    return darkColorMap.map(function (_ref3) {\n      var index = _ref3.index,\n          opacity = _ref3.opacity;\n      var darkColorString = toHex(mix(inputToRGB(opts.backgroundColor || '#141414'), inputToRGB(patterns[index]), opacity * 100));\n      return darkColorString;\n    });\n  }\n\n  return patterns;\n}"], "mappings": "AAAA,SAASA,UAAU,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,iBAAiB;AAChE,IAAIC,OAAO,GAAG,CAAC,CAAC,CAAC;;AAEjB,IAAIC,cAAc,GAAG,IAAI,CAAC,CAAC;;AAE3B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;;AAE5B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;;AAE5B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;;AAE5B,IAAIC,eAAe,GAAG,CAAC,CAAC,CAAC;;AAEzB,IAAIC,cAAc,GAAG,CAAC,CAAC,CAAC;AACxB;;AAEA,IAAIC,YAAY,GAAG,CAAC;EAClBC,KAAK,EAAE,CAAC;EACRC,OAAO,EAAE;AACX,CAAC,EAAE;EACDD,KAAK,EAAE,CAAC;EACRC,OAAO,EAAE;AACX,CAAC,EAAE;EACDD,KAAK,EAAE,CAAC;EACRC,OAAO,EAAE;AACX,CAAC,EAAE;EACDD,KAAK,EAAE,CAAC;EACRC,OAAO,EAAE;AACX,CAAC,EAAE;EACDD,KAAK,EAAE,CAAC;EACRC,OAAO,EAAE;AACX,CAAC,EAAE;EACDD,KAAK,EAAE,CAAC;EACRC,OAAO,EAAE;AACX,CAAC,EAAE;EACDD,KAAK,EAAE,CAAC;EACRC,OAAO,EAAE;AACX,CAAC,EAAE;EACDD,KAAK,EAAE,CAAC;EACRC,OAAO,EAAE;AACX,CAAC,EAAE;EACDD,KAAK,EAAE,CAAC;EACRC,OAAO,EAAE;AACX,CAAC,EAAE;EACDD,KAAK,EAAE,CAAC;EACRC,OAAO,EAAE;AACX,CAAC,CAAC;;AAEF;AACA;AACA,SAASC,KAAKA,CAACC,IAAI,EAAE;EACnB,IAAIC,CAAC,GAAGD,IAAI,CAACC,CAAC;IACVC,CAAC,GAAGF,IAAI,CAACE,CAAC;IACVC,CAAC,GAAGH,IAAI,CAACG,CAAC;EACd,IAAIC,GAAG,GAAGhB,QAAQ,CAACa,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;EAC3B,OAAO;IACLE,CAAC,EAAED,GAAG,CAACC,CAAC,GAAG,GAAG;IACdC,CAAC,EAAEF,GAAG,CAACE,CAAC;IACRC,CAAC,EAAEH,GAAG,CAACG;EACT,CAAC;AACH,CAAC,CAAC;AACF;;AAGA,SAASC,KAAKA,CAACC,KAAK,EAAE;EACpB,IAAIR,CAAC,GAAGQ,KAAK,CAACR,CAAC;IACXC,CAAC,GAAGO,KAAK,CAACP,CAAC;IACXC,CAAC,GAAGM,KAAK,CAACN,CAAC;EACf,OAAO,GAAG,CAACO,MAAM,CAACvB,QAAQ,CAACc,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE,KAAK,CAAC,CAAC;AAC7C,CAAC,CAAC;AACF;AACA;;AAGA,SAASQ,GAAGA,CAACC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAE;EAC/B,IAAIC,CAAC,GAAGD,MAAM,GAAG,GAAG;EACpB,IAAIE,GAAG,GAAG;IACRf,CAAC,EAAE,CAACY,IAAI,CAACZ,CAAC,GAAGW,IAAI,CAACX,CAAC,IAAIc,CAAC,GAAGH,IAAI,CAACX,CAAC;IACjCC,CAAC,EAAE,CAACW,IAAI,CAACX,CAAC,GAAGU,IAAI,CAACV,CAAC,IAAIa,CAAC,GAAGH,IAAI,CAACV,CAAC;IACjCC,CAAC,EAAE,CAACU,IAAI,CAACV,CAAC,GAAGS,IAAI,CAACT,CAAC,IAAIY,CAAC,GAAGH,IAAI,CAACT;EAClC,CAAC;EACD,OAAOa,GAAG;AACZ;AAEA,SAASC,MAAMA,CAACb,GAAG,EAAEc,CAAC,EAAEC,KAAK,EAAE;EAC7B,IAAIC,GAAG,CAAC,CAAC;;EAET,IAAIC,IAAI,CAACC,KAAK,CAAClB,GAAG,CAACC,CAAC,CAAC,IAAI,EAAE,IAAIgB,IAAI,CAACC,KAAK,CAAClB,GAAG,CAACC,CAAC,CAAC,IAAI,GAAG,EAAE;IACvDe,GAAG,GAAGD,KAAK,GAAGE,IAAI,CAACC,KAAK,CAAClB,GAAG,CAACC,CAAC,CAAC,GAAGhB,OAAO,GAAG6B,CAAC,GAAGG,IAAI,CAACC,KAAK,CAAClB,GAAG,CAACC,CAAC,CAAC,GAAGhB,OAAO,GAAG6B,CAAC;EACjF,CAAC,MAAM;IACLE,GAAG,GAAGD,KAAK,GAAGE,IAAI,CAACC,KAAK,CAAClB,GAAG,CAACC,CAAC,CAAC,GAAGhB,OAAO,GAAG6B,CAAC,GAAGG,IAAI,CAACC,KAAK,CAAClB,GAAG,CAACC,CAAC,CAAC,GAAGhB,OAAO,GAAG6B,CAAC;EACjF;EAEA,IAAIE,GAAG,GAAG,CAAC,EAAE;IACXA,GAAG,IAAI,GAAG;EACZ,CAAC,MAAM,IAAIA,GAAG,IAAI,GAAG,EAAE;IACrBA,GAAG,IAAI,GAAG;EACZ;EAEA,OAAOA,GAAG;AACZ;AAEA,SAASG,aAAaA,CAACnB,GAAG,EAAEc,CAAC,EAAEC,KAAK,EAAE;EACpC;EACA,IAAIf,GAAG,CAACC,CAAC,KAAK,CAAC,IAAID,GAAG,CAACE,CAAC,KAAK,CAAC,EAAE;IAC9B,OAAOF,GAAG,CAACE,CAAC;EACd;EAEA,IAAIkB,UAAU;EAEd,IAAIL,KAAK,EAAE;IACTK,UAAU,GAAGpB,GAAG,CAACE,CAAC,GAAGhB,cAAc,GAAG4B,CAAC;EACzC,CAAC,MAAM,IAAIA,CAAC,KAAKvB,cAAc,EAAE;IAC/B6B,UAAU,GAAGpB,GAAG,CAACE,CAAC,GAAGhB,cAAc;EACrC,CAAC,MAAM;IACLkC,UAAU,GAAGpB,GAAG,CAACE,CAAC,GAAGf,eAAe,GAAG2B,CAAC;EAC1C,CAAC,CAAC;;EAGF,IAAIM,UAAU,GAAG,CAAC,EAAE;IAClBA,UAAU,GAAG,CAAC;EAChB,CAAC,CAAC;;EAGF,IAAIL,KAAK,IAAID,CAAC,KAAKxB,eAAe,IAAI8B,UAAU,GAAG,GAAG,EAAE;IACtDA,UAAU,GAAG,GAAG;EAClB;EAEA,IAAIA,UAAU,GAAG,IAAI,EAAE;IACrBA,UAAU,GAAG,IAAI;EACnB;EAEA,OAAOC,MAAM,CAACD,UAAU,CAACE,OAAO,CAAC,CAAC,CAAC,CAAC;AACtC;AAEA,SAASC,QAAQA,CAACvB,GAAG,EAAEc,CAAC,EAAEC,KAAK,EAAE;EAC/B,IAAIS,KAAK;EAET,IAAIT,KAAK,EAAE;IACTS,KAAK,GAAGxB,GAAG,CAACG,CAAC,GAAGf,eAAe,GAAG0B,CAAC;EACrC,CAAC,MAAM;IACLU,KAAK,GAAGxB,GAAG,CAACG,CAAC,GAAGd,eAAe,GAAGyB,CAAC;EACrC;EAEA,IAAIU,KAAK,GAAG,CAAC,EAAE;IACbA,KAAK,GAAG,CAAC;EACX;EAEA,OAAOH,MAAM,CAACG,KAAK,CAACF,OAAO,CAAC,CAAC,CAAC,CAAC;AACjC;AAEA,eAAe,SAASG,QAAQA,CAACC,KAAK,EAAE;EACtC,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACjF,IAAIG,QAAQ,GAAG,EAAE;EACjB,IAAIC,MAAM,GAAGlD,UAAU,CAAC4C,KAAK,CAAC;EAE9B,KAAK,IAAIZ,CAAC,GAAGxB,eAAe,EAAEwB,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;IAC3C,IAAId,GAAG,GAAGL,KAAK,CAACqC,MAAM,CAAC;IACvB,IAAIC,WAAW,GAAG7B,KAAK,CAACtB,UAAU,CAAC;MACjCmB,CAAC,EAAEY,MAAM,CAACb,GAAG,EAAEc,CAAC,EAAE,IAAI,CAAC;MACvBZ,CAAC,EAAEiB,aAAa,CAACnB,GAAG,EAAEc,CAAC,EAAE,IAAI,CAAC;MAC9BX,CAAC,EAAEoB,QAAQ,CAACvB,GAAG,EAAEc,CAAC,EAAE,IAAI;IAC1B,CAAC,CAAC,CAAC;IACHiB,QAAQ,CAACG,IAAI,CAACD,WAAW,CAAC;EAC5B;EAEAF,QAAQ,CAACG,IAAI,CAAC9B,KAAK,CAAC4B,MAAM,CAAC,CAAC;EAE5B,KAAK,IAAIG,EAAE,GAAG,CAAC,EAAEA,EAAE,IAAI5C,cAAc,EAAE4C,EAAE,IAAI,CAAC,EAAE;IAC9C,IAAIC,IAAI,GAAGzC,KAAK,CAACqC,MAAM,CAAC;IAExB,IAAIK,YAAY,GAAGjC,KAAK,CAACtB,UAAU,CAAC;MAClCmB,CAAC,EAAEY,MAAM,CAACuB,IAAI,EAAED,EAAE,CAAC;MACnBjC,CAAC,EAAEiB,aAAa,CAACiB,IAAI,EAAED,EAAE,CAAC;MAC1BhC,CAAC,EAAEoB,QAAQ,CAACa,IAAI,EAAED,EAAE;IACtB,CAAC,CAAC,CAAC;IAEHJ,QAAQ,CAACG,IAAI,CAACG,YAAY,CAAC;EAC7B,CAAC,CAAC;;EAGF,IAAIV,IAAI,CAACW,KAAK,KAAK,MAAM,EAAE;IACzB,OAAO9C,YAAY,CAAC+C,GAAG,CAAC,UAAUC,KAAK,EAAE;MACvC,IAAI/C,KAAK,GAAG+C,KAAK,CAAC/C,KAAK;QACnBC,OAAO,GAAG8C,KAAK,CAAC9C,OAAO;MAC3B,IAAI+C,eAAe,GAAGrC,KAAK,CAACG,GAAG,CAACzB,UAAU,CAAC6C,IAAI,CAACe,eAAe,IAAI,SAAS,CAAC,EAAE5D,UAAU,CAACiD,QAAQ,CAACtC,KAAK,CAAC,CAAC,EAAEC,OAAO,GAAG,GAAG,CAAC,CAAC;MAC3H,OAAO+C,eAAe;IACxB,CAAC,CAAC;EACJ;EAEA,OAAOV,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}