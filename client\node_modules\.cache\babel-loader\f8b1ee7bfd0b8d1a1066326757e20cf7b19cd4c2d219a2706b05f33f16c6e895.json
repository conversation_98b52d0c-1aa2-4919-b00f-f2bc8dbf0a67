{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\PastPaperDiscussion.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { useLanguage } from '../contexts/LanguageContext';\nimport { getPastPaperAIResponse } from '../apicalls/aiResponse';\nimport { FaRobot, FaPaperPlane, FaTimes, FaExpand, FaCompress, FaUser, FaSpinner } from 'react-icons/fa';\nimport './PastPaperDiscussion.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PastPaperDiscussion = ({\n  pastPaper,\n  isOpen,\n  onClose,\n  subject,\n  className\n}) => {\n  _s();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    t,\n    isKiswahili\n  } = useLanguage();\n  const [messages, setMessages] = useState([]);\n  const [currentMessage, setCurrentMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [isExpanded, setIsExpanded] = useState(false);\n\n  // Initialize with welcome message\n  useEffect(() => {\n    if (isOpen && messages.length === 0) {\n      const welcomeMessage = {\n        id: 'welcome',\n        content: isKiswahili ? `Hujambo! Mimi ni Brainwave AI. Nina tayari kukusaidia na karatasi ya mtihani \"${pastPaper === null || pastPaper === void 0 ? void 0 : pastPaper.title}\". Uliza chochote kuhusu maswali, majibu, au mada yoyote katika karatasi hii.` : `Hello! I'm Brainwave AI. I'm ready to help you with the past paper \"${pastPaper === null || pastPaper === void 0 ? void 0 : pastPaper.title}\". Ask me anything about questions, answers, or any topic in this paper.`,\n        isAI: true,\n        timestamp: new Date()\n      };\n      setMessages([welcomeMessage]);\n    }\n  }, [isOpen, pastPaper, isKiswahili, messages.length]);\n\n  // Handle sending message\n  const handleSendMessage = async () => {\n    if (!currentMessage.trim()) return;\n    const userMessage = {\n      id: Date.now(),\n      content: currentMessage,\n      isAI: false,\n      timestamp: new Date()\n    };\n    setMessages(prev => [...prev, userMessage]);\n    setCurrentMessage('');\n    setIsLoading(true);\n    try {\n      const aiResponseData = {\n        question: currentMessage,\n        pastPaperTitle: pastPaper === null || pastPaper === void 0 ? void 0 : pastPaper.title,\n        subject: subject,\n        class: user === null || user === void 0 ? void 0 : user.class,\n        userLevel: user === null || user === void 0 ? void 0 : user.level,\n        language: isKiswahili ? 'kiswahili' : 'english'\n      };\n      const response = await getPastPaperAIResponse(aiResponseData);\n      if (response.success) {\n        const aiMessage = {\n          id: Date.now() + 1,\n          content: response.data.response,\n          isAI: true,\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, aiMessage]);\n      } else {\n        throw new Error(response.message);\n      }\n    } catch (error) {\n      console.error('AI Response Error:', error);\n      const errorMessage = {\n        id: Date.now() + 1,\n        content: isKiswahili ? 'Samahani, kuna hitilafu katika kupata jibu. Jaribu tena.' : 'Sorry, there was an error getting a response. Please try again.',\n        isAI: true,\n        timestamp: new Date(),\n        isError: true\n      };\n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Handle key press\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  // Clear conversation\n  const handleClearConversation = () => {\n    setMessages([]);\n    // Re-add welcome message\n    const welcomeMessage = {\n      id: 'welcome',\n      content: isKiswahili ? `Mazungumzo yamefutwa. Uliza swali jingine kuhusu karatasi ya mtihani \"${pastPaper === null || pastPaper === void 0 ? void 0 : pastPaper.title}\".` : `Conversation cleared. Ask another question about the past paper \"${pastPaper === null || pastPaper === void 0 ? void 0 : pastPaper.title}\".`,\n      isAI: true,\n      timestamp: new Date()\n    };\n    setMessages([welcomeMessage]);\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `past-paper-discussion-overlay ${isExpanded ? 'expanded' : ''}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `past-paper-discussion ${isExpanded ? 'expanded' : ''}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"discussion-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-info\",\n          children: [/*#__PURE__*/_jsxDEV(FaRobot, {\n            className: \"ai-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"header-text\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"discussion-title\",\n              children: isKiswahili ? 'Jadili na Brainwave AI' : 'Discuss with Brainwave AI'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"paper-title\",\n              children: pastPaper === null || pastPaper === void 0 ? void 0 : pastPaper.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-controls\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsExpanded(!isExpanded),\n            className: \"expand-btn\",\n            title: isExpanded ? 'Minimize' : 'Expand',\n            children: isExpanded ? /*#__PURE__*/_jsxDEV(FaCompress, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 29\n            }, this) : /*#__PURE__*/_jsxDEV(FaExpand, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onClose,\n            className: \"close-btn\",\n            title: isKiswahili ? 'Funga' : 'Close',\n            children: /*#__PURE__*/_jsxDEV(FaTimes, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"messages-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"messages-list\",\n          children: [messages.map(msg => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `message ${msg.isAI ? 'ai-message' : 'user-message'} ${msg.isError ? 'error-message' : ''}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"message-avatar\",\n              children: msg.isAI ? /*#__PURE__*/_jsxDEV(FaRobot, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 31\n              }, this) : /*#__PURE__*/_jsxDEV(FaUser, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"message-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"message-text\",\n                children: msg.content\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"message-time\",\n                children: msg.timestamp.toLocaleTimeString([], {\n                  hour: '2-digit',\n                  minute: '2-digit'\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this)]\n          }, msg.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 15\n          }, this)), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"message ai-message loading-message\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"message-avatar\",\n              children: /*#__PURE__*/_jsxDEV(FaSpinner, {\n                className: \"spinning\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"message-content\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"message-text\",\n                children: isKiswahili ? 'Brainwave AI inafikiri...' : 'Brainwave AI is thinking...'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"input-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"input-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n            value: currentMessage,\n            onChange: e => setCurrentMessage(e.target.value),\n            onKeyPress: handleKeyPress,\n            placeholder: isKiswahili ? 'Uliza swali kuhusu karatasi hii ya mtihani...' : 'Ask a question about this past paper...',\n            className: \"message-input\",\n            rows: \"2\",\n            disabled: isLoading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSendMessage,\n            disabled: !currentMessage.trim() || isLoading,\n            className: \"send-btn\",\n            title: isKiswahili ? 'Tuma' : 'Send',\n            children: /*#__PURE__*/_jsxDEV(FaPaperPlane, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), messages.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"input-actions\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleClearConversation,\n            className: \"clear-btn\",\n            children: isKiswahili ? 'Futa Mazungumzo' : 'Clear Conversation'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"paper-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"info-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: isKiswahili ? 'Somo:' : 'Subject:'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this), \" \", subject]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this), className && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"info-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: isKiswahili ? 'Darasa:' : 'Class:'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 15\n          }, this), \" \", className]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 128,\n    columnNumber: 5\n  }, this);\n};\n_s(PastPaperDiscussion, \"lGeICs4Dzd0uomhunfskH2PzJdY=\", false, function () {\n  return [useSelector, useLanguage];\n});\n_c = PastPaperDiscussion;\nexport default PastPaperDiscussion;\nvar _c;\n$RefreshReg$(_c, \"PastPaperDiscussion\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSelector", "message", "useLanguage", "getPastPaperAIResponse", "FaRobot", "FaPaperPlane", "FaTimes", "FaExpand", "FaCompress", "FaUser", "FaSpinner", "jsxDEV", "_jsxDEV", "PastPaperDiscussion", "pastPaper", "isOpen", "onClose", "subject", "className", "_s", "user", "state", "t", "isKiswahili", "messages", "setMessages", "currentMessage", "setCurrentMessage", "isLoading", "setIsLoading", "isExpanded", "setIsExpanded", "length", "welcomeMessage", "id", "content", "title", "isAI", "timestamp", "Date", "handleSendMessage", "trim", "userMessage", "now", "prev", "aiResponseData", "question", "pastPaperTitle", "class", "userLevel", "level", "language", "response", "success", "aiMessage", "data", "Error", "error", "console", "errorMessage", "isError", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "handleClearConversation", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "map", "msg", "toLocaleTimeString", "hour", "minute", "value", "onChange", "target", "onKeyPress", "placeholder", "rows", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/PastPaperDiscussion.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { useLanguage } from '../contexts/LanguageContext';\nimport { getPastPaperAIResponse } from '../apicalls/aiResponse';\nimport {\n  FaRobot,\n  FaPaperPlane,\n  FaTimes,\n  FaExpand,\n  FaCompress,\n  FaUser,\n  FaSpinner\n} from 'react-icons/fa';\nimport './PastPaperDiscussion.css';\n\nconst PastPaperDiscussion = ({ \n  pastPaper, \n  isOpen, \n  onClose, \n  subject,\n  className \n}) => {\n  const { user } = useSelector((state) => state.user);\n  const { t, isKiswahili } = useLanguage();\n  \n  const [messages, setMessages] = useState([]);\n  const [currentMessage, setCurrentMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [isExpanded, setIsExpanded] = useState(false);\n\n  // Initialize with welcome message\n  useEffect(() => {\n    if (isOpen && messages.length === 0) {\n      const welcomeMessage = {\n        id: 'welcome',\n        content: isKiswahili \n          ? `Hujambo! Mimi ni Brainwave AI. Nina tayari kukusaidia na karatasi ya mtihani \"${pastPaper?.title}\". Uliza chochote kuhusu maswali, majibu, au mada yoyote katika karatasi hii.`\n          : `Hello! I'm Brainwave AI. I'm ready to help you with the past paper \"${pastPaper?.title}\". Ask me anything about questions, answers, or any topic in this paper.`,\n        isAI: true,\n        timestamp: new Date()\n      };\n      setMessages([welcomeMessage]);\n    }\n  }, [isOpen, pastPaper, isKiswahili, messages.length]);\n\n  // Handle sending message\n  const handleSendMessage = async () => {\n    if (!currentMessage.trim()) return;\n\n    const userMessage = {\n      id: Date.now(),\n      content: currentMessage,\n      isAI: false,\n      timestamp: new Date()\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setCurrentMessage('');\n    setIsLoading(true);\n\n    try {\n      const aiResponseData = {\n        question: currentMessage,\n        pastPaperTitle: pastPaper?.title,\n        subject: subject,\n        class: user?.class,\n        userLevel: user?.level,\n        language: isKiswahili ? 'kiswahili' : 'english'\n      };\n\n      const response = await getPastPaperAIResponse(aiResponseData);\n\n      if (response.success) {\n        const aiMessage = {\n          id: Date.now() + 1,\n          content: response.data.response,\n          isAI: true,\n          timestamp: new Date()\n        };\n        setMessages(prev => [...prev, aiMessage]);\n      } else {\n        throw new Error(response.message);\n      }\n    } catch (error) {\n      console.error('AI Response Error:', error);\n      const errorMessage = {\n        id: Date.now() + 1,\n        content: isKiswahili \n          ? 'Samahani, kuna hitilafu katika kupata jibu. Jaribu tena.'\n          : 'Sorry, there was an error getting a response. Please try again.',\n        isAI: true,\n        timestamp: new Date(),\n        isError: true\n      };\n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Handle key press\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  // Clear conversation\n  const handleClearConversation = () => {\n    setMessages([]);\n    // Re-add welcome message\n    const welcomeMessage = {\n      id: 'welcome',\n      content: isKiswahili \n        ? `Mazungumzo yamefutwa. Uliza swali jingine kuhusu karatasi ya mtihani \"${pastPaper?.title}\".`\n        : `Conversation cleared. Ask another question about the past paper \"${pastPaper?.title}\".`,\n      isAI: true,\n      timestamp: new Date()\n    };\n    setMessages([welcomeMessage]);\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className={`past-paper-discussion-overlay ${isExpanded ? 'expanded' : ''}`}>\n      <div className={`past-paper-discussion ${isExpanded ? 'expanded' : ''}`}>\n        {/* Header */}\n        <div className=\"discussion-header\">\n          <div className=\"header-info\">\n            <FaRobot className=\"ai-icon\" />\n            <div className=\"header-text\">\n              <h3 className=\"discussion-title\">\n                {isKiswahili ? 'Jadili na Brainwave AI' : 'Discuss with Brainwave AI'}\n              </h3>\n              <p className=\"paper-title\">{pastPaper?.title}</p>\n            </div>\n          </div>\n          <div className=\"header-controls\">\n            <button \n              onClick={() => setIsExpanded(!isExpanded)}\n              className=\"expand-btn\"\n              title={isExpanded ? 'Minimize' : 'Expand'}\n            >\n              {isExpanded ? <FaCompress /> : <FaExpand />}\n            </button>\n            <button \n              onClick={onClose}\n              className=\"close-btn\"\n              title={isKiswahili ? 'Funga' : 'Close'}\n            >\n              <FaTimes />\n            </button>\n          </div>\n        </div>\n\n        {/* Messages Area */}\n        <div className=\"messages-container\">\n          <div className=\"messages-list\">\n            {messages.map((msg) => (\n              <div \n                key={msg.id} \n                className={`message ${msg.isAI ? 'ai-message' : 'user-message'} ${msg.isError ? 'error-message' : ''}`}\n              >\n                <div className=\"message-avatar\">\n                  {msg.isAI ? <FaRobot /> : <FaUser />}\n                </div>\n                <div className=\"message-content\">\n                  <div className=\"message-text\">{msg.content}</div>\n                  <div className=\"message-time\">\n                    {msg.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}\n                  </div>\n                </div>\n              </div>\n            ))}\n            \n            {isLoading && (\n              <div className=\"message ai-message loading-message\">\n                <div className=\"message-avatar\">\n                  <FaSpinner className=\"spinning\" />\n                </div>\n                <div className=\"message-content\">\n                  <div className=\"message-text\">\n                    {isKiswahili ? 'Brainwave AI inafikiri...' : 'Brainwave AI is thinking...'}\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Input Area */}\n        <div className=\"input-container\">\n          <div className=\"input-wrapper\">\n            <textarea\n              value={currentMessage}\n              onChange={(e) => setCurrentMessage(e.target.value)}\n              onKeyPress={handleKeyPress}\n              placeholder={isKiswahili \n                ? 'Uliza swali kuhusu karatasi hii ya mtihani...' \n                : 'Ask a question about this past paper...'}\n              className=\"message-input\"\n              rows=\"2\"\n              disabled={isLoading}\n            />\n            <button\n              onClick={handleSendMessage}\n              disabled={!currentMessage.trim() || isLoading}\n              className=\"send-btn\"\n              title={isKiswahili ? 'Tuma' : 'Send'}\n            >\n              <FaPaperPlane />\n            </button>\n          </div>\n          \n          {messages.length > 1 && (\n            <div className=\"input-actions\">\n              <button\n                onClick={handleClearConversation}\n                className=\"clear-btn\"\n              >\n                {isKiswahili ? 'Futa Mazungumzo' : 'Clear Conversation'}\n              </button>\n            </div>\n          )}\n        </div>\n\n        {/* Paper Info */}\n        <div className=\"paper-info\">\n          <div className=\"info-item\">\n            <strong>{isKiswahili ? 'Somo:' : 'Subject:'}</strong> {subject}\n          </div>\n          {className && (\n            <div className=\"info-item\">\n              <strong>{isKiswahili ? 'Darasa:' : 'Class:'}</strong> {className}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PastPaperDiscussion;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,sBAAsB,QAAQ,wBAAwB;AAC/D,SACEC,OAAO,EACPC,YAAY,EACZC,OAAO,EACPC,QAAQ,EACRC,UAAU,EACVC,MAAM,EACNC,SAAS,QACJ,gBAAgB;AACvB,OAAO,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnC,MAAMC,mBAAmB,GAAGA,CAAC;EAC3BC,SAAS;EACTC,MAAM;EACNC,OAAO;EACPC,OAAO;EACPC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC;EAAK,CAAC,GAAGpB,WAAW,CAAEqB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE,CAAC;IAAEC;EAAY,CAAC,GAAGrB,WAAW,CAAC,CAAC;EAExC,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC4B,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgC,UAAU,EAAEC,aAAa,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACAC,SAAS,CAAC,MAAM;IACd,IAAIgB,MAAM,IAAIS,QAAQ,CAACQ,MAAM,KAAK,CAAC,EAAE;MACnC,MAAMC,cAAc,GAAG;QACrBC,EAAE,EAAE,SAAS;QACbC,OAAO,EAAEZ,WAAW,GACf,iFAAgFT,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEsB,KAAM,+EAA8E,GAC/K,uEAAsEtB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEsB,KAAM,0EAAyE;QACrKC,IAAI,EAAE,IAAI;QACVC,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC;MACDd,WAAW,CAAC,CAACQ,cAAc,CAAC,CAAC;IAC/B;EACF,CAAC,EAAE,CAAClB,MAAM,EAAED,SAAS,EAAES,WAAW,EAAEC,QAAQ,CAACQ,MAAM,CAAC,CAAC;;EAErD;EACA,MAAMQ,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACd,cAAc,CAACe,IAAI,CAAC,CAAC,EAAE;IAE5B,MAAMC,WAAW,GAAG;MAClBR,EAAE,EAAEK,IAAI,CAACI,GAAG,CAAC,CAAC;MACdR,OAAO,EAAET,cAAc;MACvBW,IAAI,EAAE,KAAK;MACXC,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC;IAEDd,WAAW,CAACmB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEF,WAAW,CAAC,CAAC;IAC3Cf,iBAAiB,CAAC,EAAE,CAAC;IACrBE,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF,MAAMgB,cAAc,GAAG;QACrBC,QAAQ,EAAEpB,cAAc;QACxBqB,cAAc,EAAEjC,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEsB,KAAK;QAChCnB,OAAO,EAAEA,OAAO;QAChB+B,KAAK,EAAE5B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4B,KAAK;QAClBC,SAAS,EAAE7B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8B,KAAK;QACtBC,QAAQ,EAAE5B,WAAW,GAAG,WAAW,GAAG;MACxC,CAAC;MAED,MAAM6B,QAAQ,GAAG,MAAMjD,sBAAsB,CAAC0C,cAAc,CAAC;MAE7D,IAAIO,QAAQ,CAACC,OAAO,EAAE;QACpB,MAAMC,SAAS,GAAG;UAChBpB,EAAE,EAAEK,IAAI,CAACI,GAAG,CAAC,CAAC,GAAG,CAAC;UAClBR,OAAO,EAAEiB,QAAQ,CAACG,IAAI,CAACH,QAAQ;UAC/Bf,IAAI,EAAE,IAAI;UACVC,SAAS,EAAE,IAAIC,IAAI,CAAC;QACtB,CAAC;QACDd,WAAW,CAACmB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEU,SAAS,CAAC,CAAC;MAC3C,CAAC,MAAM;QACL,MAAM,IAAIE,KAAK,CAACJ,QAAQ,CAACnD,OAAO,CAAC;MACnC;IACF,CAAC,CAAC,OAAOwD,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAME,YAAY,GAAG;QACnBzB,EAAE,EAAEK,IAAI,CAACI,GAAG,CAAC,CAAC,GAAG,CAAC;QAClBR,OAAO,EAAEZ,WAAW,GAChB,0DAA0D,GAC1D,iEAAiE;QACrEc,IAAI,EAAE,IAAI;QACVC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;QACrBqB,OAAO,EAAE;MACX,CAAC;MACDnC,WAAW,CAACmB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEe,YAAY,CAAC,CAAC;IAC9C,CAAC,SAAS;MACR9B,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMgC,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBzB,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAM0B,uBAAuB,GAAGA,CAAA,KAAM;IACpCzC,WAAW,CAAC,EAAE,CAAC;IACf;IACA,MAAMQ,cAAc,GAAG;MACrBC,EAAE,EAAE,SAAS;MACbC,OAAO,EAAEZ,WAAW,GACf,yEAAwET,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEsB,KAAM,IAAG,GAC5F,oEAAmEtB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEsB,KAAM,IAAG;MAC5FC,IAAI,EAAE,IAAI;MACVC,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC;IACDd,WAAW,CAAC,CAACQ,cAAc,CAAC,CAAC;EAC/B,CAAC;EAED,IAAI,CAAClB,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEH,OAAA;IAAKM,SAAS,EAAG,iCAAgCY,UAAU,GAAG,UAAU,GAAG,EAAG,EAAE;IAAAqC,QAAA,eAC9EvD,OAAA;MAAKM,SAAS,EAAG,yBAAwBY,UAAU,GAAG,UAAU,GAAG,EAAG,EAAE;MAAAqC,QAAA,gBAEtEvD,OAAA;QAAKM,SAAS,EAAC,mBAAmB;QAAAiD,QAAA,gBAChCvD,OAAA;UAAKM,SAAS,EAAC,aAAa;UAAAiD,QAAA,gBAC1BvD,OAAA,CAACR,OAAO;YAACc,SAAS,EAAC;UAAS;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/B3D,OAAA;YAAKM,SAAS,EAAC,aAAa;YAAAiD,QAAA,gBAC1BvD,OAAA;cAAIM,SAAS,EAAC,kBAAkB;cAAAiD,QAAA,EAC7B5C,WAAW,GAAG,wBAAwB,GAAG;YAA2B;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,eACL3D,OAAA;cAAGM,SAAS,EAAC,aAAa;cAAAiD,QAAA,EAAErD,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEsB;YAAK;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3D,OAAA;UAAKM,SAAS,EAAC,iBAAiB;UAAAiD,QAAA,gBAC9BvD,OAAA;YACE4D,OAAO,EAAEA,CAAA,KAAMzC,aAAa,CAAC,CAACD,UAAU,CAAE;YAC1CZ,SAAS,EAAC,YAAY;YACtBkB,KAAK,EAAEN,UAAU,GAAG,UAAU,GAAG,QAAS;YAAAqC,QAAA,EAEzCrC,UAAU,gBAAGlB,OAAA,CAACJ,UAAU;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG3D,OAAA,CAACL,QAAQ;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACT3D,OAAA;YACE4D,OAAO,EAAExD,OAAQ;YACjBE,SAAS,EAAC,WAAW;YACrBkB,KAAK,EAAEb,WAAW,GAAG,OAAO,GAAG,OAAQ;YAAA4C,QAAA,eAEvCvD,OAAA,CAACN,OAAO;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN3D,OAAA;QAAKM,SAAS,EAAC,oBAAoB;QAAAiD,QAAA,eACjCvD,OAAA;UAAKM,SAAS,EAAC,eAAe;UAAAiD,QAAA,GAC3B3C,QAAQ,CAACiD,GAAG,CAAEC,GAAG,iBAChB9D,OAAA;YAEEM,SAAS,EAAG,WAAUwD,GAAG,CAACrC,IAAI,GAAG,YAAY,GAAG,cAAe,IAAGqC,GAAG,CAACd,OAAO,GAAG,eAAe,GAAG,EAAG,EAAE;YAAAO,QAAA,gBAEvGvD,OAAA;cAAKM,SAAS,EAAC,gBAAgB;cAAAiD,QAAA,EAC5BO,GAAG,CAACrC,IAAI,gBAAGzB,OAAA,CAACR,OAAO;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAG3D,OAAA,CAACH,MAAM;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eACN3D,OAAA;cAAKM,SAAS,EAAC,iBAAiB;cAAAiD,QAAA,gBAC9BvD,OAAA;gBAAKM,SAAS,EAAC,cAAc;gBAAAiD,QAAA,EAAEO,GAAG,CAACvC;cAAO;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjD3D,OAAA;gBAAKM,SAAS,EAAC,cAAc;gBAAAiD,QAAA,EAC1BO,GAAG,CAACpC,SAAS,CAACqC,kBAAkB,CAAC,EAAE,EAAE;kBAAEC,IAAI,EAAE,SAAS;kBAAEC,MAAM,EAAE;gBAAU,CAAC;cAAC;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GAXDG,GAAG,CAACxC,EAAE;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAYR,CACN,CAAC,EAED3C,SAAS,iBACRhB,OAAA;YAAKM,SAAS,EAAC,oCAAoC;YAAAiD,QAAA,gBACjDvD,OAAA;cAAKM,SAAS,EAAC,gBAAgB;cAAAiD,QAAA,eAC7BvD,OAAA,CAACF,SAAS;gBAACQ,SAAS,EAAC;cAAU;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,eACN3D,OAAA;cAAKM,SAAS,EAAC,iBAAiB;cAAAiD,QAAA,eAC9BvD,OAAA;gBAAKM,SAAS,EAAC,cAAc;gBAAAiD,QAAA,EAC1B5C,WAAW,GAAG,2BAA2B,GAAG;cAA6B;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN3D,OAAA;QAAKM,SAAS,EAAC,iBAAiB;QAAAiD,QAAA,gBAC9BvD,OAAA;UAAKM,SAAS,EAAC,eAAe;UAAAiD,QAAA,gBAC5BvD,OAAA;YACEkE,KAAK,EAAEpD,cAAe;YACtBqD,QAAQ,EAAGjB,CAAC,IAAKnC,iBAAiB,CAACmC,CAAC,CAACkB,MAAM,CAACF,KAAK,CAAE;YACnDG,UAAU,EAAEpB,cAAe;YAC3BqB,WAAW,EAAE3D,WAAW,GACpB,+CAA+C,GAC/C,yCAA0C;YAC9CL,SAAS,EAAC,eAAe;YACzBiE,IAAI,EAAC,GAAG;YACRC,QAAQ,EAAExD;UAAU;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACF3D,OAAA;YACE4D,OAAO,EAAEhC,iBAAkB;YAC3B4C,QAAQ,EAAE,CAAC1D,cAAc,CAACe,IAAI,CAAC,CAAC,IAAIb,SAAU;YAC9CV,SAAS,EAAC,UAAU;YACpBkB,KAAK,EAAEb,WAAW,GAAG,MAAM,GAAG,MAAO;YAAA4C,QAAA,eAErCvD,OAAA,CAACP,YAAY;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAEL/C,QAAQ,CAACQ,MAAM,GAAG,CAAC,iBAClBpB,OAAA;UAAKM,SAAS,EAAC,eAAe;UAAAiD,QAAA,eAC5BvD,OAAA;YACE4D,OAAO,EAAEN,uBAAwB;YACjChD,SAAS,EAAC,WAAW;YAAAiD,QAAA,EAEpB5C,WAAW,GAAG,iBAAiB,GAAG;UAAoB;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN3D,OAAA;QAAKM,SAAS,EAAC,YAAY;QAAAiD,QAAA,gBACzBvD,OAAA;UAAKM,SAAS,EAAC,WAAW;UAAAiD,QAAA,gBACxBvD,OAAA;YAAAuD,QAAA,EAAS5C,WAAW,GAAG,OAAO,GAAG;UAAU;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,KAAC,EAACtD,OAAO;QAAA;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,EACLrD,SAAS,iBACRN,OAAA;UAAKM,SAAS,EAAC,WAAW;UAAAiD,QAAA,gBACxBvD,OAAA;YAAAuD,QAAA,EAAS5C,WAAW,GAAG,SAAS,GAAG;UAAQ;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,KAAC,EAACrD,SAAS;QAAA;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpD,EAAA,CAnOIN,mBAAmB;EAAA,QAONb,WAAW,EACDE,WAAW;AAAA;AAAAmF,EAAA,GARlCxE,mBAAmB;AAqOzB,eAAeA,mBAAmB;AAAC,IAAAwE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}