{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport React from \"react\";\nexport function clamp(number, lowerBound, upperBound) {\n  return Math.max(lowerBound, Math.min(number, upperBound));\n}\nexport var safePreventDefault = function safePreventDefault(event) {\n  var passiveEvents = [\"onTouchStart\", \"onTouchMove\", \"onWheel\"];\n  if (!passiveEvents.includes(event._reactName)) {\n    event.preventDefault();\n  }\n};\nexport var getOnDemandLazySlides = function getOnDemandLazySlides(spec) {\n  var onDemandSlides = [];\n  var startIndex = lazyStartIndex(spec);\n  var endIndex = lazyEndIndex(spec);\n  for (var slideIndex = startIndex; slideIndex < endIndex; slideIndex++) {\n    if (spec.lazyLoadedList.indexOf(slideIndex) < 0) {\n      onDemandSlides.push(slideIndex);\n    }\n  }\n  return onDemandSlides;\n};\n\n// return list of slides that need to be present\nexport var getRequiredLazySlides = function getRequiredLazySlides(spec) {\n  var requiredSlides = [];\n  var startIndex = lazyStartIndex(spec);\n  var endIndex = lazyEndIndex(spec);\n  for (var slideIndex = startIndex; slideIndex < endIndex; slideIndex++) {\n    requiredSlides.push(slideIndex);\n  }\n  return requiredSlides;\n};\n\n// startIndex that needs to be present\nexport var lazyStartIndex = function lazyStartIndex(spec) {\n  return spec.currentSlide - lazySlidesOnLeft(spec);\n};\nexport var lazyEndIndex = function lazyEndIndex(spec) {\n  return spec.currentSlide + lazySlidesOnRight(spec);\n};\nexport var lazySlidesOnLeft = function lazySlidesOnLeft(spec) {\n  return spec.centerMode ? Math.floor(spec.slidesToShow / 2) + (parseInt(spec.centerPadding) > 0 ? 1 : 0) : 0;\n};\nexport var lazySlidesOnRight = function lazySlidesOnRight(spec) {\n  return spec.centerMode ? Math.floor((spec.slidesToShow - 1) / 2) + 1 + (parseInt(spec.centerPadding) > 0 ? 1 : 0) : spec.slidesToShow;\n};\n\n// get width of an element\nexport var getWidth = function getWidth(elem) {\n  return elem && elem.offsetWidth || 0;\n};\nexport var getHeight = function getHeight(elem) {\n  return elem && elem.offsetHeight || 0;\n};\nexport var getSwipeDirection = function getSwipeDirection(touchObject) {\n  var verticalSwiping = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  var xDist, yDist, r, swipeAngle;\n  xDist = touchObject.startX - touchObject.curX;\n  yDist = touchObject.startY - touchObject.curY;\n  r = Math.atan2(yDist, xDist);\n  swipeAngle = Math.round(r * 180 / Math.PI);\n  if (swipeAngle < 0) {\n    swipeAngle = 360 - Math.abs(swipeAngle);\n  }\n  if (swipeAngle <= 45 && swipeAngle >= 0 || swipeAngle <= 360 && swipeAngle >= 315) {\n    return \"left\";\n  }\n  if (swipeAngle >= 135 && swipeAngle <= 225) {\n    return \"right\";\n  }\n  if (verticalSwiping === true) {\n    if (swipeAngle >= 35 && swipeAngle <= 135) {\n      return \"up\";\n    } else {\n      return \"down\";\n    }\n  }\n  return \"vertical\";\n};\n\n// whether or not we can go next\nexport var canGoNext = function canGoNext(spec) {\n  var canGo = true;\n  if (!spec.infinite) {\n    if (spec.centerMode && spec.currentSlide >= spec.slideCount - 1) {\n      canGo = false;\n    } else if (spec.slideCount <= spec.slidesToShow || spec.currentSlide >= spec.slideCount - spec.slidesToShow) {\n      canGo = false;\n    }\n  }\n  return canGo;\n};\n\n// given an object and a list of keys, return new object with given keys\nexport var extractObject = function extractObject(spec, keys) {\n  var newObject = {};\n  keys.forEach(function (key) {\n    return newObject[key] = spec[key];\n  });\n  return newObject;\n};\n\n// get initialized state\nexport var initializedState = function initializedState(spec) {\n  // spec also contains listRef, trackRef\n  var slideCount = React.Children.count(spec.children);\n  var listNode = spec.listRef;\n  var listWidth = Math.ceil(getWidth(listNode));\n  var trackNode = spec.trackRef && spec.trackRef.node;\n  var trackWidth = Math.ceil(getWidth(trackNode));\n  var slideWidth;\n  if (!spec.vertical) {\n    var centerPaddingAdj = spec.centerMode && parseInt(spec.centerPadding) * 2;\n    if (typeof spec.centerPadding === \"string\" && spec.centerPadding.slice(-1) === \"%\") {\n      centerPaddingAdj *= listWidth / 100;\n    }\n    slideWidth = Math.ceil((listWidth - centerPaddingAdj) / spec.slidesToShow);\n  } else {\n    slideWidth = listWidth;\n  }\n  var slideHeight = listNode && getHeight(listNode.querySelector('[data-index=\"0\"]'));\n  var listHeight = slideHeight * spec.slidesToShow;\n  var currentSlide = spec.currentSlide === undefined ? spec.initialSlide : spec.currentSlide;\n  if (spec.rtl && spec.currentSlide === undefined) {\n    currentSlide = slideCount - 1 - spec.initialSlide;\n  }\n  var lazyLoadedList = spec.lazyLoadedList || [];\n  var slidesToLoad = getOnDemandLazySlides(_objectSpread(_objectSpread({}, spec), {}, {\n    currentSlide: currentSlide,\n    lazyLoadedList: lazyLoadedList\n  }));\n  lazyLoadedList = lazyLoadedList.concat(slidesToLoad);\n  var state = {\n    slideCount: slideCount,\n    slideWidth: slideWidth,\n    listWidth: listWidth,\n    trackWidth: trackWidth,\n    currentSlide: currentSlide,\n    slideHeight: slideHeight,\n    listHeight: listHeight,\n    lazyLoadedList: lazyLoadedList\n  };\n  if (spec.autoplaying === null && spec.autoplay) {\n    state[\"autoplaying\"] = \"playing\";\n  }\n  return state;\n};\nexport var slideHandler = function slideHandler(spec) {\n  var waitForAnimate = spec.waitForAnimate,\n    animating = spec.animating,\n    fade = spec.fade,\n    infinite = spec.infinite,\n    index = spec.index,\n    slideCount = spec.slideCount,\n    lazyLoad = spec.lazyLoad,\n    currentSlide = spec.currentSlide,\n    centerMode = spec.centerMode,\n    slidesToScroll = spec.slidesToScroll,\n    slidesToShow = spec.slidesToShow,\n    useCSS = spec.useCSS;\n  var lazyLoadedList = spec.lazyLoadedList;\n  if (waitForAnimate && animating) return {};\n  var animationSlide = index,\n    finalSlide,\n    animationLeft,\n    finalLeft;\n  var state = {},\n    nextState = {};\n  var targetSlide = infinite ? index : clamp(index, 0, slideCount - 1);\n  if (fade) {\n    if (!infinite && (index < 0 || index >= slideCount)) return {};\n    if (index < 0) {\n      animationSlide = index + slideCount;\n    } else if (index >= slideCount) {\n      animationSlide = index - slideCount;\n    }\n    if (lazyLoad && lazyLoadedList.indexOf(animationSlide) < 0) {\n      lazyLoadedList = lazyLoadedList.concat(animationSlide);\n    }\n    state = {\n      animating: true,\n      currentSlide: animationSlide,\n      lazyLoadedList: lazyLoadedList,\n      targetSlide: animationSlide\n    };\n    nextState = {\n      animating: false,\n      targetSlide: animationSlide\n    };\n  } else {\n    finalSlide = animationSlide;\n    if (animationSlide < 0) {\n      finalSlide = animationSlide + slideCount;\n      if (!infinite) finalSlide = 0;else if (slideCount % slidesToScroll !== 0) finalSlide = slideCount - slideCount % slidesToScroll;\n    } else if (!canGoNext(spec) && animationSlide > currentSlide) {\n      animationSlide = finalSlide = currentSlide;\n    } else if (centerMode && animationSlide >= slideCount) {\n      animationSlide = infinite ? slideCount : slideCount - 1;\n      finalSlide = infinite ? 0 : slideCount - 1;\n    } else if (animationSlide >= slideCount) {\n      finalSlide = animationSlide - slideCount;\n      if (!infinite) finalSlide = slideCount - slidesToShow;else if (slideCount % slidesToScroll !== 0) finalSlide = 0;\n    }\n    if (!infinite && animationSlide + slidesToShow >= slideCount) {\n      finalSlide = slideCount - slidesToShow;\n    }\n    animationLeft = getTrackLeft(_objectSpread(_objectSpread({}, spec), {}, {\n      slideIndex: animationSlide\n    }));\n    finalLeft = getTrackLeft(_objectSpread(_objectSpread({}, spec), {}, {\n      slideIndex: finalSlide\n    }));\n    if (!infinite) {\n      if (animationLeft === finalLeft) animationSlide = finalSlide;\n      animationLeft = finalLeft;\n    }\n    if (lazyLoad) {\n      lazyLoadedList = lazyLoadedList.concat(getOnDemandLazySlides(_objectSpread(_objectSpread({}, spec), {}, {\n        currentSlide: animationSlide\n      })));\n    }\n    if (!useCSS) {\n      state = {\n        currentSlide: finalSlide,\n        trackStyle: getTrackCSS(_objectSpread(_objectSpread({}, spec), {}, {\n          left: finalLeft\n        })),\n        lazyLoadedList: lazyLoadedList,\n        targetSlide: targetSlide\n      };\n    } else {\n      state = {\n        animating: true,\n        currentSlide: finalSlide,\n        trackStyle: getTrackAnimateCSS(_objectSpread(_objectSpread({}, spec), {}, {\n          left: animationLeft\n        })),\n        lazyLoadedList: lazyLoadedList,\n        targetSlide: targetSlide\n      };\n      nextState = {\n        animating: false,\n        currentSlide: finalSlide,\n        trackStyle: getTrackCSS(_objectSpread(_objectSpread({}, spec), {}, {\n          left: finalLeft\n        })),\n        swipeLeft: null,\n        targetSlide: targetSlide\n      };\n    }\n  }\n  return {\n    state: state,\n    nextState: nextState\n  };\n};\nexport var changeSlide = function changeSlide(spec, options) {\n  var indexOffset, previousInt, slideOffset, unevenOffset, targetSlide;\n  var slidesToScroll = spec.slidesToScroll,\n    slidesToShow = spec.slidesToShow,\n    slideCount = spec.slideCount,\n    currentSlide = spec.currentSlide,\n    previousTargetSlide = spec.targetSlide,\n    lazyLoad = spec.lazyLoad,\n    infinite = spec.infinite;\n  unevenOffset = slideCount % slidesToScroll !== 0;\n  indexOffset = unevenOffset ? 0 : (slideCount - currentSlide) % slidesToScroll;\n  if (options.message === \"previous\") {\n    slideOffset = indexOffset === 0 ? slidesToScroll : slidesToShow - indexOffset;\n    targetSlide = currentSlide - slideOffset;\n    if (lazyLoad && !infinite) {\n      previousInt = currentSlide - slideOffset;\n      targetSlide = previousInt === -1 ? slideCount - 1 : previousInt;\n    }\n    if (!infinite) {\n      targetSlide = previousTargetSlide - slidesToScroll;\n    }\n  } else if (options.message === \"next\") {\n    slideOffset = indexOffset === 0 ? slidesToScroll : indexOffset;\n    targetSlide = currentSlide + slideOffset;\n    if (lazyLoad && !infinite) {\n      targetSlide = (currentSlide + slidesToScroll) % slideCount + indexOffset;\n    }\n    if (!infinite) {\n      targetSlide = previousTargetSlide + slidesToScroll;\n    }\n  } else if (options.message === \"dots\") {\n    // Click on dots\n    targetSlide = options.index * options.slidesToScroll;\n  } else if (options.message === \"children\") {\n    // Click on the slides\n    targetSlide = options.index;\n    if (infinite) {\n      var direction = siblingDirection(_objectSpread(_objectSpread({}, spec), {}, {\n        targetSlide: targetSlide\n      }));\n      if (targetSlide > options.currentSlide && direction === \"left\") {\n        targetSlide = targetSlide - slideCount;\n      } else if (targetSlide < options.currentSlide && direction === \"right\") {\n        targetSlide = targetSlide + slideCount;\n      }\n    }\n  } else if (options.message === \"index\") {\n    targetSlide = Number(options.index);\n  }\n  return targetSlide;\n};\nexport var keyHandler = function keyHandler(e, accessibility, rtl) {\n  if (e.target.tagName.match(\"TEXTAREA|INPUT|SELECT\") || !accessibility) return \"\";\n  if (e.keyCode === 37) return rtl ? \"next\" : \"previous\";\n  if (e.keyCode === 39) return rtl ? \"previous\" : \"next\";\n  return \"\";\n};\nexport var swipeStart = function swipeStart(e, swipe, draggable) {\n  e.target.tagName === \"IMG\" && safePreventDefault(e);\n  if (!swipe || !draggable && e.type.indexOf(\"mouse\") !== -1) return \"\";\n  return {\n    dragging: true,\n    touchObject: {\n      startX: e.touches ? e.touches[0].pageX : e.clientX,\n      startY: e.touches ? e.touches[0].pageY : e.clientY,\n      curX: e.touches ? e.touches[0].pageX : e.clientX,\n      curY: e.touches ? e.touches[0].pageY : e.clientY\n    }\n  };\n};\nexport var swipeMove = function swipeMove(e, spec) {\n  // spec also contains, trackRef and slideIndex\n  var scrolling = spec.scrolling,\n    animating = spec.animating,\n    vertical = spec.vertical,\n    swipeToSlide = spec.swipeToSlide,\n    verticalSwiping = spec.verticalSwiping,\n    rtl = spec.rtl,\n    currentSlide = spec.currentSlide,\n    edgeFriction = spec.edgeFriction,\n    edgeDragged = spec.edgeDragged,\n    onEdge = spec.onEdge,\n    swiped = spec.swiped,\n    swiping = spec.swiping,\n    slideCount = spec.slideCount,\n    slidesToScroll = spec.slidesToScroll,\n    infinite = spec.infinite,\n    touchObject = spec.touchObject,\n    swipeEvent = spec.swipeEvent,\n    listHeight = spec.listHeight,\n    listWidth = spec.listWidth;\n  if (scrolling) return;\n  if (animating) return safePreventDefault(e);\n  if (vertical && swipeToSlide && verticalSwiping) safePreventDefault(e);\n  var swipeLeft,\n    state = {};\n  var curLeft = getTrackLeft(spec);\n  touchObject.curX = e.touches ? e.touches[0].pageX : e.clientX;\n  touchObject.curY = e.touches ? e.touches[0].pageY : e.clientY;\n  touchObject.swipeLength = Math.round(Math.sqrt(Math.pow(touchObject.curX - touchObject.startX, 2)));\n  var verticalSwipeLength = Math.round(Math.sqrt(Math.pow(touchObject.curY - touchObject.startY, 2)));\n  if (!verticalSwiping && !swiping && verticalSwipeLength > 10) {\n    return {\n      scrolling: true\n    };\n  }\n  if (verticalSwiping) touchObject.swipeLength = verticalSwipeLength;\n  var positionOffset = (!rtl ? 1 : -1) * (touchObject.curX > touchObject.startX ? 1 : -1);\n  if (verticalSwiping) positionOffset = touchObject.curY > touchObject.startY ? 1 : -1;\n  var dotCount = Math.ceil(slideCount / slidesToScroll);\n  var swipeDirection = getSwipeDirection(spec.touchObject, verticalSwiping);\n  var touchSwipeLength = touchObject.swipeLength;\n  if (!infinite) {\n    if (currentSlide === 0 && (swipeDirection === \"right\" || swipeDirection === \"down\") || currentSlide + 1 >= dotCount && (swipeDirection === \"left\" || swipeDirection === \"up\") || !canGoNext(spec) && (swipeDirection === \"left\" || swipeDirection === \"up\")) {\n      touchSwipeLength = touchObject.swipeLength * edgeFriction;\n      if (edgeDragged === false && onEdge) {\n        onEdge(swipeDirection);\n        state[\"edgeDragged\"] = true;\n      }\n    }\n  }\n  if (!swiped && swipeEvent) {\n    swipeEvent(swipeDirection);\n    state[\"swiped\"] = true;\n  }\n  if (!vertical) {\n    if (!rtl) {\n      swipeLeft = curLeft + touchSwipeLength * positionOffset;\n    } else {\n      swipeLeft = curLeft - touchSwipeLength * positionOffset;\n    }\n  } else {\n    swipeLeft = curLeft + touchSwipeLength * (listHeight / listWidth) * positionOffset;\n  }\n  if (verticalSwiping) {\n    swipeLeft = curLeft + touchSwipeLength * positionOffset;\n  }\n  state = _objectSpread(_objectSpread({}, state), {}, {\n    touchObject: touchObject,\n    swipeLeft: swipeLeft,\n    trackStyle: getTrackCSS(_objectSpread(_objectSpread({}, spec), {}, {\n      left: swipeLeft\n    }))\n  });\n  if (Math.abs(touchObject.curX - touchObject.startX) < Math.abs(touchObject.curY - touchObject.startY) * 0.8) {\n    return state;\n  }\n  if (touchObject.swipeLength > 10) {\n    state[\"swiping\"] = true;\n    safePreventDefault(e);\n  }\n  return state;\n};\nexport var swipeEnd = function swipeEnd(e, spec) {\n  var dragging = spec.dragging,\n    swipe = spec.swipe,\n    touchObject = spec.touchObject,\n    listWidth = spec.listWidth,\n    touchThreshold = spec.touchThreshold,\n    verticalSwiping = spec.verticalSwiping,\n    listHeight = spec.listHeight,\n    swipeToSlide = spec.swipeToSlide,\n    scrolling = spec.scrolling,\n    onSwipe = spec.onSwipe,\n    targetSlide = spec.targetSlide,\n    currentSlide = spec.currentSlide,\n    infinite = spec.infinite;\n  if (!dragging) {\n    if (swipe) safePreventDefault(e);\n    return {};\n  }\n  var minSwipe = verticalSwiping ? listHeight / touchThreshold : listWidth / touchThreshold;\n  var swipeDirection = getSwipeDirection(touchObject, verticalSwiping);\n  // reset the state of touch related state variables.\n  var state = {\n    dragging: false,\n    edgeDragged: false,\n    scrolling: false,\n    swiping: false,\n    swiped: false,\n    swipeLeft: null,\n    touchObject: {}\n  };\n  if (scrolling) {\n    return state;\n  }\n  if (!touchObject.swipeLength) {\n    return state;\n  }\n  if (touchObject.swipeLength > minSwipe) {\n    safePreventDefault(e);\n    if (onSwipe) {\n      onSwipe(swipeDirection);\n    }\n    var slideCount, newSlide;\n    var activeSlide = infinite ? currentSlide : targetSlide;\n    switch (swipeDirection) {\n      case \"left\":\n      case \"up\":\n        newSlide = activeSlide + getSlideCount(spec);\n        slideCount = swipeToSlide ? checkNavigable(spec, newSlide) : newSlide;\n        state[\"currentDirection\"] = 0;\n        break;\n      case \"right\":\n      case \"down\":\n        newSlide = activeSlide - getSlideCount(spec);\n        slideCount = swipeToSlide ? checkNavigable(spec, newSlide) : newSlide;\n        state[\"currentDirection\"] = 1;\n        break;\n      default:\n        slideCount = activeSlide;\n    }\n    state[\"triggerSlideHandler\"] = slideCount;\n  } else {\n    // Adjust the track back to it's original position.\n    var currentLeft = getTrackLeft(spec);\n    state[\"trackStyle\"] = getTrackAnimateCSS(_objectSpread(_objectSpread({}, spec), {}, {\n      left: currentLeft\n    }));\n  }\n  return state;\n};\nexport var getNavigableIndexes = function getNavigableIndexes(spec) {\n  var max = spec.infinite ? spec.slideCount * 2 : spec.slideCount;\n  var breakpoint = spec.infinite ? spec.slidesToShow * -1 : 0;\n  var counter = spec.infinite ? spec.slidesToShow * -1 : 0;\n  var indexes = [];\n  while (breakpoint < max) {\n    indexes.push(breakpoint);\n    breakpoint = counter + spec.slidesToScroll;\n    counter += Math.min(spec.slidesToScroll, spec.slidesToShow);\n  }\n  return indexes;\n};\nexport var checkNavigable = function checkNavigable(spec, index) {\n  var navigables = getNavigableIndexes(spec);\n  var prevNavigable = 0;\n  if (index > navigables[navigables.length - 1]) {\n    index = navigables[navigables.length - 1];\n  } else {\n    for (var n in navigables) {\n      if (index < navigables[n]) {\n        index = prevNavigable;\n        break;\n      }\n      prevNavigable = navigables[n];\n    }\n  }\n  return index;\n};\nexport var getSlideCount = function getSlideCount(spec) {\n  var centerOffset = spec.centerMode ? spec.slideWidth * Math.floor(spec.slidesToShow / 2) : 0;\n  if (spec.swipeToSlide) {\n    var swipedSlide;\n    var slickList = spec.listRef;\n    var slides = slickList.querySelectorAll && slickList.querySelectorAll(\".slick-slide\") || [];\n    Array.from(slides).every(function (slide) {\n      if (!spec.vertical) {\n        if (slide.offsetLeft - centerOffset + getWidth(slide) / 2 > spec.swipeLeft * -1) {\n          swipedSlide = slide;\n          return false;\n        }\n      } else {\n        if (slide.offsetTop + getHeight(slide) / 2 > spec.swipeLeft * -1) {\n          swipedSlide = slide;\n          return false;\n        }\n      }\n      return true;\n    });\n    if (!swipedSlide) {\n      return 0;\n    }\n    var currentIndex = spec.rtl === true ? spec.slideCount - spec.currentSlide : spec.currentSlide;\n    var slidesTraversed = Math.abs(swipedSlide.dataset.index - currentIndex) || 1;\n    return slidesTraversed;\n  } else {\n    return spec.slidesToScroll;\n  }\n};\nexport var checkSpecKeys = function checkSpecKeys(spec, keysArray) {\n  return (\n    // eslint-disable-next-line no-prototype-builtins\n    keysArray.reduce(function (value, key) {\n      return value && spec.hasOwnProperty(key);\n    }, true) ? null : console.error(\"Keys Missing:\", spec)\n  );\n};\nexport var getTrackCSS = function getTrackCSS(spec) {\n  checkSpecKeys(spec, [\"left\", \"variableWidth\", \"slideCount\", \"slidesToShow\", \"slideWidth\"]);\n  var trackWidth, trackHeight;\n  var trackChildren = spec.slideCount + 2 * spec.slidesToShow;\n  if (!spec.vertical) {\n    trackWidth = getTotalSlides(spec) * spec.slideWidth;\n  } else {\n    trackHeight = trackChildren * spec.slideHeight;\n  }\n  var style = {\n    opacity: 1,\n    transition: \"\",\n    WebkitTransition: \"\"\n  };\n  if (spec.useTransform) {\n    var WebkitTransform = !spec.vertical ? \"translate3d(\" + spec.left + \"px, 0px, 0px)\" : \"translate3d(0px, \" + spec.left + \"px, 0px)\";\n    var transform = !spec.vertical ? \"translate3d(\" + spec.left + \"px, 0px, 0px)\" : \"translate3d(0px, \" + spec.left + \"px, 0px)\";\n    var msTransform = !spec.vertical ? \"translateX(\" + spec.left + \"px)\" : \"translateY(\" + spec.left + \"px)\";\n    style = _objectSpread(_objectSpread({}, style), {}, {\n      WebkitTransform: WebkitTransform,\n      transform: transform,\n      msTransform: msTransform\n    });\n  } else {\n    if (spec.vertical) {\n      style[\"top\"] = spec.left;\n    } else {\n      style[\"left\"] = spec.left;\n    }\n  }\n  if (spec.fade) style = {\n    opacity: 1\n  };\n  if (trackWidth) style.width = trackWidth;\n  if (trackHeight) style.height = trackHeight;\n\n  // Fallback for IE8\n  if (window && !window.addEventListener && window.attachEvent) {\n    if (!spec.vertical) {\n      style.marginLeft = spec.left + \"px\";\n    } else {\n      style.marginTop = spec.left + \"px\";\n    }\n  }\n  return style;\n};\nexport var getTrackAnimateCSS = function getTrackAnimateCSS(spec) {\n  checkSpecKeys(spec, [\"left\", \"variableWidth\", \"slideCount\", \"slidesToShow\", \"slideWidth\", \"speed\", \"cssEase\"]);\n  var style = getTrackCSS(spec);\n  // useCSS is true by default so it can be undefined\n  if (spec.useTransform) {\n    style.WebkitTransition = \"-webkit-transform \" + spec.speed + \"ms \" + spec.cssEase;\n    style.transition = \"transform \" + spec.speed + \"ms \" + spec.cssEase;\n  } else {\n    if (spec.vertical) {\n      style.transition = \"top \" + spec.speed + \"ms \" + spec.cssEase;\n    } else {\n      style.transition = \"left \" + spec.speed + \"ms \" + spec.cssEase;\n    }\n  }\n  return style;\n};\nexport var getTrackLeft = function getTrackLeft(spec) {\n  if (spec.unslick) {\n    return 0;\n  }\n  checkSpecKeys(spec, [\"slideIndex\", \"trackRef\", \"infinite\", \"centerMode\", \"slideCount\", \"slidesToShow\", \"slidesToScroll\", \"slideWidth\", \"listWidth\", \"variableWidth\", \"slideHeight\"]);\n  var slideIndex = spec.slideIndex,\n    trackRef = spec.trackRef,\n    infinite = spec.infinite,\n    centerMode = spec.centerMode,\n    slideCount = spec.slideCount,\n    slidesToShow = spec.slidesToShow,\n    slidesToScroll = spec.slidesToScroll,\n    slideWidth = spec.slideWidth,\n    listWidth = spec.listWidth,\n    variableWidth = spec.variableWidth,\n    slideHeight = spec.slideHeight,\n    fade = spec.fade,\n    vertical = spec.vertical;\n  var slideOffset = 0;\n  var targetLeft;\n  var targetSlide;\n  var verticalOffset = 0;\n  if (fade || spec.slideCount === 1) {\n    return 0;\n  }\n  var slidesToOffset = 0;\n  if (infinite) {\n    slidesToOffset = -getPreClones(spec); // bring active slide to the beginning of visual area\n    // if next scroll doesn't have enough children, just reach till the end of original slides instead of shifting slidesToScroll children\n    if (slideCount % slidesToScroll !== 0 && slideIndex + slidesToScroll > slideCount) {\n      slidesToOffset = -(slideIndex > slideCount ? slidesToShow - (slideIndex - slideCount) : slideCount % slidesToScroll);\n    }\n    // shift current slide to center of the frame\n    if (centerMode) {\n      slidesToOffset += parseInt(slidesToShow / 2);\n    }\n  } else {\n    if (slideCount % slidesToScroll !== 0 && slideIndex + slidesToScroll > slideCount) {\n      slidesToOffset = slidesToShow - slideCount % slidesToScroll;\n    }\n    if (centerMode) {\n      slidesToOffset = parseInt(slidesToShow / 2);\n    }\n  }\n  slideOffset = slidesToOffset * slideWidth;\n  verticalOffset = slidesToOffset * slideHeight;\n  if (!vertical) {\n    targetLeft = slideIndex * slideWidth * -1 + slideOffset;\n  } else {\n    targetLeft = slideIndex * slideHeight * -1 + verticalOffset;\n  }\n  if (variableWidth === true) {\n    var targetSlideIndex;\n    var trackElem = trackRef && trackRef.node;\n    targetSlideIndex = slideIndex + getPreClones(spec);\n    targetSlide = trackElem && trackElem.childNodes[targetSlideIndex];\n    targetLeft = targetSlide ? targetSlide.offsetLeft * -1 : 0;\n    if (centerMode === true) {\n      targetSlideIndex = infinite ? slideIndex + getPreClones(spec) : slideIndex;\n      targetSlide = trackElem && trackElem.children[targetSlideIndex];\n      targetLeft = 0;\n      for (var slide = 0; slide < targetSlideIndex; slide++) {\n        targetLeft -= trackElem && trackElem.children[slide] && trackElem.children[slide].offsetWidth;\n      }\n      targetLeft -= parseInt(spec.centerPadding);\n      targetLeft += targetSlide && (listWidth - targetSlide.offsetWidth) / 2;\n    }\n  }\n  return targetLeft;\n};\nexport var getPreClones = function getPreClones(spec) {\n  if (spec.unslick || !spec.infinite) {\n    return 0;\n  }\n  if (spec.variableWidth) {\n    return spec.slideCount;\n  }\n  return spec.slidesToShow + (spec.centerMode ? 1 : 0);\n};\nexport var getPostClones = function getPostClones(spec) {\n  if (spec.unslick || !spec.infinite) {\n    return 0;\n  }\n  return spec.slideCount;\n};\nexport var getTotalSlides = function getTotalSlides(spec) {\n  return spec.slideCount === 1 ? 1 : getPreClones(spec) + spec.slideCount + getPostClones(spec);\n};\nexport var siblingDirection = function siblingDirection(spec) {\n  if (spec.targetSlide > spec.currentSlide) {\n    if (spec.targetSlide > spec.currentSlide + slidesOnRight(spec)) {\n      return \"left\";\n    }\n    return \"right\";\n  } else {\n    if (spec.targetSlide < spec.currentSlide - slidesOnLeft(spec)) {\n      return \"right\";\n    }\n    return \"left\";\n  }\n};\nexport var slidesOnRight = function slidesOnRight(_ref) {\n  var slidesToShow = _ref.slidesToShow,\n    centerMode = _ref.centerMode,\n    rtl = _ref.rtl,\n    centerPadding = _ref.centerPadding;\n  // returns no of slides on the right of active slide\n  if (centerMode) {\n    var right = (slidesToShow - 1) / 2 + 1;\n    if (parseInt(centerPadding) > 0) right += 1;\n    if (rtl && slidesToShow % 2 === 0) right += 1;\n    return right;\n  }\n  if (rtl) {\n    return 0;\n  }\n  return slidesToShow - 1;\n};\nexport var slidesOnLeft = function slidesOnLeft(_ref2) {\n  var slidesToShow = _ref2.slidesToShow,\n    centerMode = _ref2.centerMode,\n    rtl = _ref2.rtl,\n    centerPadding = _ref2.centerPadding;\n  // returns no of slides on the left of active slide\n  if (centerMode) {\n    var left = (slidesToShow - 1) / 2 + 1;\n    if (parseInt(centerPadding) > 0) left += 1;\n    if (!rtl && slidesToShow % 2 === 0) left += 1;\n    return left;\n  }\n  if (rtl) {\n    return slidesToShow - 1;\n  }\n  return 0;\n};\nexport var canUseDOM = function canUseDOM() {\n  return !!(typeof window !== \"undefined\" && window.document && window.document.createElement);\n};", "map": {"version": 3, "names": ["_objectSpread", "React", "clamp", "number", "lowerBound", "upperBound", "Math", "max", "min", "safePreventDefault", "event", "passiveEvents", "includes", "_reactName", "preventDefault", "getOnDemandLazySlides", "spec", "onDemandSlides", "startIndex", "lazyStartIndex", "endIndex", "lazyEndIndex", "slideIndex", "lazyLoadedList", "indexOf", "push", "getRequiredLazySlides", "requiredSlides", "currentSlide", "lazySlidesOnLeft", "lazySlidesOnRight", "centerMode", "floor", "slidesToShow", "parseInt", "centerPadding", "getWidth", "elem", "offsetWidth", "getHeight", "offsetHeight", "getSwipeDirection", "touchObject", "verticalSwiping", "arguments", "length", "undefined", "xDist", "yDist", "r", "swipeAngle", "startX", "curX", "startY", "curY", "atan2", "round", "PI", "abs", "canGoNext", "canGo", "infinite", "slideCount", "extractObject", "keys", "newObject", "for<PERSON>ach", "key", "initializedState", "Children", "count", "children", "listNode", "listRef", "listWidth", "ceil", "trackNode", "trackRef", "node", "trackWidth", "slideWidth", "vertical", "centerPaddingAdj", "slice", "slideHeight", "querySelector", "listHeight", "initialSlide", "rtl", "slidesToLoad", "concat", "state", "autoplaying", "autoplay", "<PERSON><PERSON><PERSON><PERSON>", "waitForAnimate", "animating", "fade", "index", "lazyLoad", "slidesToScroll", "useCSS", "animationSlide", "finalSlide", "animationLeft", "finalLeft", "nextState", "targetSlide", "getTrackLeft", "trackStyle", "getTrackCSS", "left", "getTrackAnimateCSS", "swipeLeft", "changeSlide", "options", "indexOffset", "previousInt", "slideOffset", "unevenOffset", "previousTargetSlide", "message", "direction", "siblingDirection", "Number", "<PERSON><PERSON><PERSON><PERSON>", "e", "accessibility", "target", "tagName", "match", "keyCode", "swipeStart", "swipe", "draggable", "type", "dragging", "touches", "pageX", "clientX", "pageY", "clientY", "swipeMove", "scrolling", "swipeToSlide", "edgeFriction", "edgeDragged", "onEdge", "swiped", "swiping", "swipeEvent", "curL<PERSON>t", "swipe<PERSON><PERSON><PERSON>", "sqrt", "pow", "verticalSwipeLength", "positionOffset", "dotCount", "swipeDirection", "touchSwipeLength", "swipeEnd", "touchThreshold", "onSwipe", "minSwipe", "newSlide", "activeSlide", "getSlideCount", "checkNavigable", "currentLeft", "getNavigableIndexes", "breakpoint", "counter", "indexes", "navigables", "prevNavigable", "n", "centerOffset", "swipedSlide", "slickList", "slides", "querySelectorAll", "Array", "from", "every", "slide", "offsetLeft", "offsetTop", "currentIndex", "slidesTraversed", "dataset", "checkSpecKeys", "keysArray", "reduce", "value", "hasOwnProperty", "console", "error", "trackHeight", "trackChildren", "getTotalSlides", "style", "opacity", "transition", "WebkitTransition", "useTransform", "WebkitTransform", "transform", "msTransform", "width", "height", "window", "addEventListener", "attachEvent", "marginLeft", "marginTop", "speed", "cssEase", "unslick", "variableWidth", "targetLeft", "verticalOffset", "slidesToOffset", "getPreClones", "targetSlideIndex", "trackElem", "childNodes", "getPostClones", "slidesOnRight", "slidesOnLeft", "_ref", "right", "_ref2", "canUseDOM", "document", "createElement"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/@ant-design/react-slick/es/utils/innerSliderUtils.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport React from \"react\";\nexport function clamp(number, lowerBound, upperBound) {\n  return Math.max(lowerBound, Math.min(number, upperBound));\n}\nexport var safePreventDefault = function safePreventDefault(event) {\n  var passiveEvents = [\"onTouchStart\", \"onTouchMove\", \"onWheel\"];\n  if (!passiveEvents.includes(event._reactName)) {\n    event.preventDefault();\n  }\n};\nexport var getOnDemandLazySlides = function getOnDemandLazySlides(spec) {\n  var onDemandSlides = [];\n  var startIndex = lazyStartIndex(spec);\n  var endIndex = lazyEndIndex(spec);\n  for (var slideIndex = startIndex; slideIndex < endIndex; slideIndex++) {\n    if (spec.lazyLoadedList.indexOf(slideIndex) < 0) {\n      onDemandSlides.push(slideIndex);\n    }\n  }\n  return onDemandSlides;\n};\n\n// return list of slides that need to be present\nexport var getRequiredLazySlides = function getRequiredLazySlides(spec) {\n  var requiredSlides = [];\n  var startIndex = lazyStartIndex(spec);\n  var endIndex = lazyEndIndex(spec);\n  for (var slideIndex = startIndex; slideIndex < endIndex; slideIndex++) {\n    requiredSlides.push(slideIndex);\n  }\n  return requiredSlides;\n};\n\n// startIndex that needs to be present\nexport var lazyStartIndex = function lazyStartIndex(spec) {\n  return spec.currentSlide - lazySlidesOnLeft(spec);\n};\nexport var lazyEndIndex = function lazyEndIndex(spec) {\n  return spec.currentSlide + lazySlidesOnRight(spec);\n};\nexport var lazySlidesOnLeft = function lazySlidesOnLeft(spec) {\n  return spec.centerMode ? Math.floor(spec.slidesToShow / 2) + (parseInt(spec.centerPadding) > 0 ? 1 : 0) : 0;\n};\nexport var lazySlidesOnRight = function lazySlidesOnRight(spec) {\n  return spec.centerMode ? Math.floor((spec.slidesToShow - 1) / 2) + 1 + (parseInt(spec.centerPadding) > 0 ? 1 : 0) : spec.slidesToShow;\n};\n\n// get width of an element\nexport var getWidth = function getWidth(elem) {\n  return elem && elem.offsetWidth || 0;\n};\nexport var getHeight = function getHeight(elem) {\n  return elem && elem.offsetHeight || 0;\n};\nexport var getSwipeDirection = function getSwipeDirection(touchObject) {\n  var verticalSwiping = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  var xDist, yDist, r, swipeAngle;\n  xDist = touchObject.startX - touchObject.curX;\n  yDist = touchObject.startY - touchObject.curY;\n  r = Math.atan2(yDist, xDist);\n  swipeAngle = Math.round(r * 180 / Math.PI);\n  if (swipeAngle < 0) {\n    swipeAngle = 360 - Math.abs(swipeAngle);\n  }\n  if (swipeAngle <= 45 && swipeAngle >= 0 || swipeAngle <= 360 && swipeAngle >= 315) {\n    return \"left\";\n  }\n  if (swipeAngle >= 135 && swipeAngle <= 225) {\n    return \"right\";\n  }\n  if (verticalSwiping === true) {\n    if (swipeAngle >= 35 && swipeAngle <= 135) {\n      return \"up\";\n    } else {\n      return \"down\";\n    }\n  }\n  return \"vertical\";\n};\n\n// whether or not we can go next\nexport var canGoNext = function canGoNext(spec) {\n  var canGo = true;\n  if (!spec.infinite) {\n    if (spec.centerMode && spec.currentSlide >= spec.slideCount - 1) {\n      canGo = false;\n    } else if (spec.slideCount <= spec.slidesToShow || spec.currentSlide >= spec.slideCount - spec.slidesToShow) {\n      canGo = false;\n    }\n  }\n  return canGo;\n};\n\n// given an object and a list of keys, return new object with given keys\nexport var extractObject = function extractObject(spec, keys) {\n  var newObject = {};\n  keys.forEach(function (key) {\n    return newObject[key] = spec[key];\n  });\n  return newObject;\n};\n\n// get initialized state\nexport var initializedState = function initializedState(spec) {\n  // spec also contains listRef, trackRef\n  var slideCount = React.Children.count(spec.children);\n  var listNode = spec.listRef;\n  var listWidth = Math.ceil(getWidth(listNode));\n  var trackNode = spec.trackRef && spec.trackRef.node;\n  var trackWidth = Math.ceil(getWidth(trackNode));\n  var slideWidth;\n  if (!spec.vertical) {\n    var centerPaddingAdj = spec.centerMode && parseInt(spec.centerPadding) * 2;\n    if (typeof spec.centerPadding === \"string\" && spec.centerPadding.slice(-1) === \"%\") {\n      centerPaddingAdj *= listWidth / 100;\n    }\n    slideWidth = Math.ceil((listWidth - centerPaddingAdj) / spec.slidesToShow);\n  } else {\n    slideWidth = listWidth;\n  }\n  var slideHeight = listNode && getHeight(listNode.querySelector('[data-index=\"0\"]'));\n  var listHeight = slideHeight * spec.slidesToShow;\n  var currentSlide = spec.currentSlide === undefined ? spec.initialSlide : spec.currentSlide;\n  if (spec.rtl && spec.currentSlide === undefined) {\n    currentSlide = slideCount - 1 - spec.initialSlide;\n  }\n  var lazyLoadedList = spec.lazyLoadedList || [];\n  var slidesToLoad = getOnDemandLazySlides(_objectSpread(_objectSpread({}, spec), {}, {\n    currentSlide: currentSlide,\n    lazyLoadedList: lazyLoadedList\n  }));\n  lazyLoadedList = lazyLoadedList.concat(slidesToLoad);\n  var state = {\n    slideCount: slideCount,\n    slideWidth: slideWidth,\n    listWidth: listWidth,\n    trackWidth: trackWidth,\n    currentSlide: currentSlide,\n    slideHeight: slideHeight,\n    listHeight: listHeight,\n    lazyLoadedList: lazyLoadedList\n  };\n  if (spec.autoplaying === null && spec.autoplay) {\n    state[\"autoplaying\"] = \"playing\";\n  }\n  return state;\n};\nexport var slideHandler = function slideHandler(spec) {\n  var waitForAnimate = spec.waitForAnimate,\n    animating = spec.animating,\n    fade = spec.fade,\n    infinite = spec.infinite,\n    index = spec.index,\n    slideCount = spec.slideCount,\n    lazyLoad = spec.lazyLoad,\n    currentSlide = spec.currentSlide,\n    centerMode = spec.centerMode,\n    slidesToScroll = spec.slidesToScroll,\n    slidesToShow = spec.slidesToShow,\n    useCSS = spec.useCSS;\n  var lazyLoadedList = spec.lazyLoadedList;\n  if (waitForAnimate && animating) return {};\n  var animationSlide = index,\n    finalSlide,\n    animationLeft,\n    finalLeft;\n  var state = {},\n    nextState = {};\n  var targetSlide = infinite ? index : clamp(index, 0, slideCount - 1);\n  if (fade) {\n    if (!infinite && (index < 0 || index >= slideCount)) return {};\n    if (index < 0) {\n      animationSlide = index + slideCount;\n    } else if (index >= slideCount) {\n      animationSlide = index - slideCount;\n    }\n    if (lazyLoad && lazyLoadedList.indexOf(animationSlide) < 0) {\n      lazyLoadedList = lazyLoadedList.concat(animationSlide);\n    }\n    state = {\n      animating: true,\n      currentSlide: animationSlide,\n      lazyLoadedList: lazyLoadedList,\n      targetSlide: animationSlide\n    };\n    nextState = {\n      animating: false,\n      targetSlide: animationSlide\n    };\n  } else {\n    finalSlide = animationSlide;\n    if (animationSlide < 0) {\n      finalSlide = animationSlide + slideCount;\n      if (!infinite) finalSlide = 0;else if (slideCount % slidesToScroll !== 0) finalSlide = slideCount - slideCount % slidesToScroll;\n    } else if (!canGoNext(spec) && animationSlide > currentSlide) {\n      animationSlide = finalSlide = currentSlide;\n    } else if (centerMode && animationSlide >= slideCount) {\n      animationSlide = infinite ? slideCount : slideCount - 1;\n      finalSlide = infinite ? 0 : slideCount - 1;\n    } else if (animationSlide >= slideCount) {\n      finalSlide = animationSlide - slideCount;\n      if (!infinite) finalSlide = slideCount - slidesToShow;else if (slideCount % slidesToScroll !== 0) finalSlide = 0;\n    }\n    if (!infinite && animationSlide + slidesToShow >= slideCount) {\n      finalSlide = slideCount - slidesToShow;\n    }\n    animationLeft = getTrackLeft(_objectSpread(_objectSpread({}, spec), {}, {\n      slideIndex: animationSlide\n    }));\n    finalLeft = getTrackLeft(_objectSpread(_objectSpread({}, spec), {}, {\n      slideIndex: finalSlide\n    }));\n    if (!infinite) {\n      if (animationLeft === finalLeft) animationSlide = finalSlide;\n      animationLeft = finalLeft;\n    }\n    if (lazyLoad) {\n      lazyLoadedList = lazyLoadedList.concat(getOnDemandLazySlides(_objectSpread(_objectSpread({}, spec), {}, {\n        currentSlide: animationSlide\n      })));\n    }\n    if (!useCSS) {\n      state = {\n        currentSlide: finalSlide,\n        trackStyle: getTrackCSS(_objectSpread(_objectSpread({}, spec), {}, {\n          left: finalLeft\n        })),\n        lazyLoadedList: lazyLoadedList,\n        targetSlide: targetSlide\n      };\n    } else {\n      state = {\n        animating: true,\n        currentSlide: finalSlide,\n        trackStyle: getTrackAnimateCSS(_objectSpread(_objectSpread({}, spec), {}, {\n          left: animationLeft\n        })),\n        lazyLoadedList: lazyLoadedList,\n        targetSlide: targetSlide\n      };\n      nextState = {\n        animating: false,\n        currentSlide: finalSlide,\n        trackStyle: getTrackCSS(_objectSpread(_objectSpread({}, spec), {}, {\n          left: finalLeft\n        })),\n        swipeLeft: null,\n        targetSlide: targetSlide\n      };\n    }\n  }\n  return {\n    state: state,\n    nextState: nextState\n  };\n};\nexport var changeSlide = function changeSlide(spec, options) {\n  var indexOffset, previousInt, slideOffset, unevenOffset, targetSlide;\n  var slidesToScroll = spec.slidesToScroll,\n    slidesToShow = spec.slidesToShow,\n    slideCount = spec.slideCount,\n    currentSlide = spec.currentSlide,\n    previousTargetSlide = spec.targetSlide,\n    lazyLoad = spec.lazyLoad,\n    infinite = spec.infinite;\n  unevenOffset = slideCount % slidesToScroll !== 0;\n  indexOffset = unevenOffset ? 0 : (slideCount - currentSlide) % slidesToScroll;\n  if (options.message === \"previous\") {\n    slideOffset = indexOffset === 0 ? slidesToScroll : slidesToShow - indexOffset;\n    targetSlide = currentSlide - slideOffset;\n    if (lazyLoad && !infinite) {\n      previousInt = currentSlide - slideOffset;\n      targetSlide = previousInt === -1 ? slideCount - 1 : previousInt;\n    }\n    if (!infinite) {\n      targetSlide = previousTargetSlide - slidesToScroll;\n    }\n  } else if (options.message === \"next\") {\n    slideOffset = indexOffset === 0 ? slidesToScroll : indexOffset;\n    targetSlide = currentSlide + slideOffset;\n    if (lazyLoad && !infinite) {\n      targetSlide = (currentSlide + slidesToScroll) % slideCount + indexOffset;\n    }\n    if (!infinite) {\n      targetSlide = previousTargetSlide + slidesToScroll;\n    }\n  } else if (options.message === \"dots\") {\n    // Click on dots\n    targetSlide = options.index * options.slidesToScroll;\n  } else if (options.message === \"children\") {\n    // Click on the slides\n    targetSlide = options.index;\n    if (infinite) {\n      var direction = siblingDirection(_objectSpread(_objectSpread({}, spec), {}, {\n        targetSlide: targetSlide\n      }));\n      if (targetSlide > options.currentSlide && direction === \"left\") {\n        targetSlide = targetSlide - slideCount;\n      } else if (targetSlide < options.currentSlide && direction === \"right\") {\n        targetSlide = targetSlide + slideCount;\n      }\n    }\n  } else if (options.message === \"index\") {\n    targetSlide = Number(options.index);\n  }\n  return targetSlide;\n};\nexport var keyHandler = function keyHandler(e, accessibility, rtl) {\n  if (e.target.tagName.match(\"TEXTAREA|INPUT|SELECT\") || !accessibility) return \"\";\n  if (e.keyCode === 37) return rtl ? \"next\" : \"previous\";\n  if (e.keyCode === 39) return rtl ? \"previous\" : \"next\";\n  return \"\";\n};\nexport var swipeStart = function swipeStart(e, swipe, draggable) {\n  e.target.tagName === \"IMG\" && safePreventDefault(e);\n  if (!swipe || !draggable && e.type.indexOf(\"mouse\") !== -1) return \"\";\n  return {\n    dragging: true,\n    touchObject: {\n      startX: e.touches ? e.touches[0].pageX : e.clientX,\n      startY: e.touches ? e.touches[0].pageY : e.clientY,\n      curX: e.touches ? e.touches[0].pageX : e.clientX,\n      curY: e.touches ? e.touches[0].pageY : e.clientY\n    }\n  };\n};\nexport var swipeMove = function swipeMove(e, spec) {\n  // spec also contains, trackRef and slideIndex\n  var scrolling = spec.scrolling,\n    animating = spec.animating,\n    vertical = spec.vertical,\n    swipeToSlide = spec.swipeToSlide,\n    verticalSwiping = spec.verticalSwiping,\n    rtl = spec.rtl,\n    currentSlide = spec.currentSlide,\n    edgeFriction = spec.edgeFriction,\n    edgeDragged = spec.edgeDragged,\n    onEdge = spec.onEdge,\n    swiped = spec.swiped,\n    swiping = spec.swiping,\n    slideCount = spec.slideCount,\n    slidesToScroll = spec.slidesToScroll,\n    infinite = spec.infinite,\n    touchObject = spec.touchObject,\n    swipeEvent = spec.swipeEvent,\n    listHeight = spec.listHeight,\n    listWidth = spec.listWidth;\n  if (scrolling) return;\n  if (animating) return safePreventDefault(e);\n  if (vertical && swipeToSlide && verticalSwiping) safePreventDefault(e);\n  var swipeLeft,\n    state = {};\n  var curLeft = getTrackLeft(spec);\n  touchObject.curX = e.touches ? e.touches[0].pageX : e.clientX;\n  touchObject.curY = e.touches ? e.touches[0].pageY : e.clientY;\n  touchObject.swipeLength = Math.round(Math.sqrt(Math.pow(touchObject.curX - touchObject.startX, 2)));\n  var verticalSwipeLength = Math.round(Math.sqrt(Math.pow(touchObject.curY - touchObject.startY, 2)));\n  if (!verticalSwiping && !swiping && verticalSwipeLength > 10) {\n    return {\n      scrolling: true\n    };\n  }\n  if (verticalSwiping) touchObject.swipeLength = verticalSwipeLength;\n  var positionOffset = (!rtl ? 1 : -1) * (touchObject.curX > touchObject.startX ? 1 : -1);\n  if (verticalSwiping) positionOffset = touchObject.curY > touchObject.startY ? 1 : -1;\n  var dotCount = Math.ceil(slideCount / slidesToScroll);\n  var swipeDirection = getSwipeDirection(spec.touchObject, verticalSwiping);\n  var touchSwipeLength = touchObject.swipeLength;\n  if (!infinite) {\n    if (currentSlide === 0 && (swipeDirection === \"right\" || swipeDirection === \"down\") || currentSlide + 1 >= dotCount && (swipeDirection === \"left\" || swipeDirection === \"up\") || !canGoNext(spec) && (swipeDirection === \"left\" || swipeDirection === \"up\")) {\n      touchSwipeLength = touchObject.swipeLength * edgeFriction;\n      if (edgeDragged === false && onEdge) {\n        onEdge(swipeDirection);\n        state[\"edgeDragged\"] = true;\n      }\n    }\n  }\n  if (!swiped && swipeEvent) {\n    swipeEvent(swipeDirection);\n    state[\"swiped\"] = true;\n  }\n  if (!vertical) {\n    if (!rtl) {\n      swipeLeft = curLeft + touchSwipeLength * positionOffset;\n    } else {\n      swipeLeft = curLeft - touchSwipeLength * positionOffset;\n    }\n  } else {\n    swipeLeft = curLeft + touchSwipeLength * (listHeight / listWidth) * positionOffset;\n  }\n  if (verticalSwiping) {\n    swipeLeft = curLeft + touchSwipeLength * positionOffset;\n  }\n  state = _objectSpread(_objectSpread({}, state), {}, {\n    touchObject: touchObject,\n    swipeLeft: swipeLeft,\n    trackStyle: getTrackCSS(_objectSpread(_objectSpread({}, spec), {}, {\n      left: swipeLeft\n    }))\n  });\n  if (Math.abs(touchObject.curX - touchObject.startX) < Math.abs(touchObject.curY - touchObject.startY) * 0.8) {\n    return state;\n  }\n  if (touchObject.swipeLength > 10) {\n    state[\"swiping\"] = true;\n    safePreventDefault(e);\n  }\n  return state;\n};\nexport var swipeEnd = function swipeEnd(e, spec) {\n  var dragging = spec.dragging,\n    swipe = spec.swipe,\n    touchObject = spec.touchObject,\n    listWidth = spec.listWidth,\n    touchThreshold = spec.touchThreshold,\n    verticalSwiping = spec.verticalSwiping,\n    listHeight = spec.listHeight,\n    swipeToSlide = spec.swipeToSlide,\n    scrolling = spec.scrolling,\n    onSwipe = spec.onSwipe,\n    targetSlide = spec.targetSlide,\n    currentSlide = spec.currentSlide,\n    infinite = spec.infinite;\n  if (!dragging) {\n    if (swipe) safePreventDefault(e);\n    return {};\n  }\n  var minSwipe = verticalSwiping ? listHeight / touchThreshold : listWidth / touchThreshold;\n  var swipeDirection = getSwipeDirection(touchObject, verticalSwiping);\n  // reset the state of touch related state variables.\n  var state = {\n    dragging: false,\n    edgeDragged: false,\n    scrolling: false,\n    swiping: false,\n    swiped: false,\n    swipeLeft: null,\n    touchObject: {}\n  };\n  if (scrolling) {\n    return state;\n  }\n  if (!touchObject.swipeLength) {\n    return state;\n  }\n  if (touchObject.swipeLength > minSwipe) {\n    safePreventDefault(e);\n    if (onSwipe) {\n      onSwipe(swipeDirection);\n    }\n    var slideCount, newSlide;\n    var activeSlide = infinite ? currentSlide : targetSlide;\n    switch (swipeDirection) {\n      case \"left\":\n      case \"up\":\n        newSlide = activeSlide + getSlideCount(spec);\n        slideCount = swipeToSlide ? checkNavigable(spec, newSlide) : newSlide;\n        state[\"currentDirection\"] = 0;\n        break;\n      case \"right\":\n      case \"down\":\n        newSlide = activeSlide - getSlideCount(spec);\n        slideCount = swipeToSlide ? checkNavigable(spec, newSlide) : newSlide;\n        state[\"currentDirection\"] = 1;\n        break;\n      default:\n        slideCount = activeSlide;\n    }\n    state[\"triggerSlideHandler\"] = slideCount;\n  } else {\n    // Adjust the track back to it's original position.\n    var currentLeft = getTrackLeft(spec);\n    state[\"trackStyle\"] = getTrackAnimateCSS(_objectSpread(_objectSpread({}, spec), {}, {\n      left: currentLeft\n    }));\n  }\n  return state;\n};\nexport var getNavigableIndexes = function getNavigableIndexes(spec) {\n  var max = spec.infinite ? spec.slideCount * 2 : spec.slideCount;\n  var breakpoint = spec.infinite ? spec.slidesToShow * -1 : 0;\n  var counter = spec.infinite ? spec.slidesToShow * -1 : 0;\n  var indexes = [];\n  while (breakpoint < max) {\n    indexes.push(breakpoint);\n    breakpoint = counter + spec.slidesToScroll;\n    counter += Math.min(spec.slidesToScroll, spec.slidesToShow);\n  }\n  return indexes;\n};\nexport var checkNavigable = function checkNavigable(spec, index) {\n  var navigables = getNavigableIndexes(spec);\n  var prevNavigable = 0;\n  if (index > navigables[navigables.length - 1]) {\n    index = navigables[navigables.length - 1];\n  } else {\n    for (var n in navigables) {\n      if (index < navigables[n]) {\n        index = prevNavigable;\n        break;\n      }\n      prevNavigable = navigables[n];\n    }\n  }\n  return index;\n};\nexport var getSlideCount = function getSlideCount(spec) {\n  var centerOffset = spec.centerMode ? spec.slideWidth * Math.floor(spec.slidesToShow / 2) : 0;\n  if (spec.swipeToSlide) {\n    var swipedSlide;\n    var slickList = spec.listRef;\n    var slides = slickList.querySelectorAll && slickList.querySelectorAll(\".slick-slide\") || [];\n    Array.from(slides).every(function (slide) {\n      if (!spec.vertical) {\n        if (slide.offsetLeft - centerOffset + getWidth(slide) / 2 > spec.swipeLeft * -1) {\n          swipedSlide = slide;\n          return false;\n        }\n      } else {\n        if (slide.offsetTop + getHeight(slide) / 2 > spec.swipeLeft * -1) {\n          swipedSlide = slide;\n          return false;\n        }\n      }\n      return true;\n    });\n    if (!swipedSlide) {\n      return 0;\n    }\n    var currentIndex = spec.rtl === true ? spec.slideCount - spec.currentSlide : spec.currentSlide;\n    var slidesTraversed = Math.abs(swipedSlide.dataset.index - currentIndex) || 1;\n    return slidesTraversed;\n  } else {\n    return spec.slidesToScroll;\n  }\n};\nexport var checkSpecKeys = function checkSpecKeys(spec, keysArray) {\n  return (\n    // eslint-disable-next-line no-prototype-builtins\n    keysArray.reduce(function (value, key) {\n      return value && spec.hasOwnProperty(key);\n    }, true) ? null : console.error(\"Keys Missing:\", spec)\n  );\n};\nexport var getTrackCSS = function getTrackCSS(spec) {\n  checkSpecKeys(spec, [\"left\", \"variableWidth\", \"slideCount\", \"slidesToShow\", \"slideWidth\"]);\n  var trackWidth, trackHeight;\n  var trackChildren = spec.slideCount + 2 * spec.slidesToShow;\n  if (!spec.vertical) {\n    trackWidth = getTotalSlides(spec) * spec.slideWidth;\n  } else {\n    trackHeight = trackChildren * spec.slideHeight;\n  }\n  var style = {\n    opacity: 1,\n    transition: \"\",\n    WebkitTransition: \"\"\n  };\n  if (spec.useTransform) {\n    var WebkitTransform = !spec.vertical ? \"translate3d(\" + spec.left + \"px, 0px, 0px)\" : \"translate3d(0px, \" + spec.left + \"px, 0px)\";\n    var transform = !spec.vertical ? \"translate3d(\" + spec.left + \"px, 0px, 0px)\" : \"translate3d(0px, \" + spec.left + \"px, 0px)\";\n    var msTransform = !spec.vertical ? \"translateX(\" + spec.left + \"px)\" : \"translateY(\" + spec.left + \"px)\";\n    style = _objectSpread(_objectSpread({}, style), {}, {\n      WebkitTransform: WebkitTransform,\n      transform: transform,\n      msTransform: msTransform\n    });\n  } else {\n    if (spec.vertical) {\n      style[\"top\"] = spec.left;\n    } else {\n      style[\"left\"] = spec.left;\n    }\n  }\n  if (spec.fade) style = {\n    opacity: 1\n  };\n  if (trackWidth) style.width = trackWidth;\n  if (trackHeight) style.height = trackHeight;\n\n  // Fallback for IE8\n  if (window && !window.addEventListener && window.attachEvent) {\n    if (!spec.vertical) {\n      style.marginLeft = spec.left + \"px\";\n    } else {\n      style.marginTop = spec.left + \"px\";\n    }\n  }\n  return style;\n};\nexport var getTrackAnimateCSS = function getTrackAnimateCSS(spec) {\n  checkSpecKeys(spec, [\"left\", \"variableWidth\", \"slideCount\", \"slidesToShow\", \"slideWidth\", \"speed\", \"cssEase\"]);\n  var style = getTrackCSS(spec);\n  // useCSS is true by default so it can be undefined\n  if (spec.useTransform) {\n    style.WebkitTransition = \"-webkit-transform \" + spec.speed + \"ms \" + spec.cssEase;\n    style.transition = \"transform \" + spec.speed + \"ms \" + spec.cssEase;\n  } else {\n    if (spec.vertical) {\n      style.transition = \"top \" + spec.speed + \"ms \" + spec.cssEase;\n    } else {\n      style.transition = \"left \" + spec.speed + \"ms \" + spec.cssEase;\n    }\n  }\n  return style;\n};\nexport var getTrackLeft = function getTrackLeft(spec) {\n  if (spec.unslick) {\n    return 0;\n  }\n  checkSpecKeys(spec, [\"slideIndex\", \"trackRef\", \"infinite\", \"centerMode\", \"slideCount\", \"slidesToShow\", \"slidesToScroll\", \"slideWidth\", \"listWidth\", \"variableWidth\", \"slideHeight\"]);\n  var slideIndex = spec.slideIndex,\n    trackRef = spec.trackRef,\n    infinite = spec.infinite,\n    centerMode = spec.centerMode,\n    slideCount = spec.slideCount,\n    slidesToShow = spec.slidesToShow,\n    slidesToScroll = spec.slidesToScroll,\n    slideWidth = spec.slideWidth,\n    listWidth = spec.listWidth,\n    variableWidth = spec.variableWidth,\n    slideHeight = spec.slideHeight,\n    fade = spec.fade,\n    vertical = spec.vertical;\n  var slideOffset = 0;\n  var targetLeft;\n  var targetSlide;\n  var verticalOffset = 0;\n  if (fade || spec.slideCount === 1) {\n    return 0;\n  }\n  var slidesToOffset = 0;\n  if (infinite) {\n    slidesToOffset = -getPreClones(spec); // bring active slide to the beginning of visual area\n    // if next scroll doesn't have enough children, just reach till the end of original slides instead of shifting slidesToScroll children\n    if (slideCount % slidesToScroll !== 0 && slideIndex + slidesToScroll > slideCount) {\n      slidesToOffset = -(slideIndex > slideCount ? slidesToShow - (slideIndex - slideCount) : slideCount % slidesToScroll);\n    }\n    // shift current slide to center of the frame\n    if (centerMode) {\n      slidesToOffset += parseInt(slidesToShow / 2);\n    }\n  } else {\n    if (slideCount % slidesToScroll !== 0 && slideIndex + slidesToScroll > slideCount) {\n      slidesToOffset = slidesToShow - slideCount % slidesToScroll;\n    }\n    if (centerMode) {\n      slidesToOffset = parseInt(slidesToShow / 2);\n    }\n  }\n  slideOffset = slidesToOffset * slideWidth;\n  verticalOffset = slidesToOffset * slideHeight;\n  if (!vertical) {\n    targetLeft = slideIndex * slideWidth * -1 + slideOffset;\n  } else {\n    targetLeft = slideIndex * slideHeight * -1 + verticalOffset;\n  }\n  if (variableWidth === true) {\n    var targetSlideIndex;\n    var trackElem = trackRef && trackRef.node;\n    targetSlideIndex = slideIndex + getPreClones(spec);\n    targetSlide = trackElem && trackElem.childNodes[targetSlideIndex];\n    targetLeft = targetSlide ? targetSlide.offsetLeft * -1 : 0;\n    if (centerMode === true) {\n      targetSlideIndex = infinite ? slideIndex + getPreClones(spec) : slideIndex;\n      targetSlide = trackElem && trackElem.children[targetSlideIndex];\n      targetLeft = 0;\n      for (var slide = 0; slide < targetSlideIndex; slide++) {\n        targetLeft -= trackElem && trackElem.children[slide] && trackElem.children[slide].offsetWidth;\n      }\n      targetLeft -= parseInt(spec.centerPadding);\n      targetLeft += targetSlide && (listWidth - targetSlide.offsetWidth) / 2;\n    }\n  }\n  return targetLeft;\n};\nexport var getPreClones = function getPreClones(spec) {\n  if (spec.unslick || !spec.infinite) {\n    return 0;\n  }\n  if (spec.variableWidth) {\n    return spec.slideCount;\n  }\n  return spec.slidesToShow + (spec.centerMode ? 1 : 0);\n};\nexport var getPostClones = function getPostClones(spec) {\n  if (spec.unslick || !spec.infinite) {\n    return 0;\n  }\n  return spec.slideCount;\n};\nexport var getTotalSlides = function getTotalSlides(spec) {\n  return spec.slideCount === 1 ? 1 : getPreClones(spec) + spec.slideCount + getPostClones(spec);\n};\nexport var siblingDirection = function siblingDirection(spec) {\n  if (spec.targetSlide > spec.currentSlide) {\n    if (spec.targetSlide > spec.currentSlide + slidesOnRight(spec)) {\n      return \"left\";\n    }\n    return \"right\";\n  } else {\n    if (spec.targetSlide < spec.currentSlide - slidesOnLeft(spec)) {\n      return \"right\";\n    }\n    return \"left\";\n  }\n};\nexport var slidesOnRight = function slidesOnRight(_ref) {\n  var slidesToShow = _ref.slidesToShow,\n    centerMode = _ref.centerMode,\n    rtl = _ref.rtl,\n    centerPadding = _ref.centerPadding;\n  // returns no of slides on the right of active slide\n  if (centerMode) {\n    var right = (slidesToShow - 1) / 2 + 1;\n    if (parseInt(centerPadding) > 0) right += 1;\n    if (rtl && slidesToShow % 2 === 0) right += 1;\n    return right;\n  }\n  if (rtl) {\n    return 0;\n  }\n  return slidesToShow - 1;\n};\nexport var slidesOnLeft = function slidesOnLeft(_ref2) {\n  var slidesToShow = _ref2.slidesToShow,\n    centerMode = _ref2.centerMode,\n    rtl = _ref2.rtl,\n    centerPadding = _ref2.centerPadding;\n  // returns no of slides on the left of active slide\n  if (centerMode) {\n    var left = (slidesToShow - 1) / 2 + 1;\n    if (parseInt(centerPadding) > 0) left += 1;\n    if (!rtl && slidesToShow % 2 === 0) left += 1;\n    return left;\n  }\n  if (rtl) {\n    return slidesToShow - 1;\n  }\n  return 0;\n};\nexport var canUseDOM = function canUseDOM() {\n  return !!(typeof window !== \"undefined\" && window.document && window.document.createElement);\n};"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,SAASC,KAAKA,CAACC,MAAM,EAAEC,UAAU,EAAEC,UAAU,EAAE;EACpD,OAAOC,IAAI,CAACC,GAAG,CAACH,UAAU,EAAEE,IAAI,CAACE,GAAG,CAACL,MAAM,EAAEE,UAAU,CAAC,CAAC;AAC3D;AACA,OAAO,IAAII,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,KAAK,EAAE;EACjE,IAAIC,aAAa,GAAG,CAAC,cAAc,EAAE,aAAa,EAAE,SAAS,CAAC;EAC9D,IAAI,CAACA,aAAa,CAACC,QAAQ,CAACF,KAAK,CAACG,UAAU,CAAC,EAAE;IAC7CH,KAAK,CAACI,cAAc,CAAC,CAAC;EACxB;AACF,CAAC;AACD,OAAO,IAAIC,qBAAqB,GAAG,SAASA,qBAAqBA,CAACC,IAAI,EAAE;EACtE,IAAIC,cAAc,GAAG,EAAE;EACvB,IAAIC,UAAU,GAAGC,cAAc,CAACH,IAAI,CAAC;EACrC,IAAII,QAAQ,GAAGC,YAAY,CAACL,IAAI,CAAC;EACjC,KAAK,IAAIM,UAAU,GAAGJ,UAAU,EAAEI,UAAU,GAAGF,QAAQ,EAAEE,UAAU,EAAE,EAAE;IACrE,IAAIN,IAAI,CAACO,cAAc,CAACC,OAAO,CAACF,UAAU,CAAC,GAAG,CAAC,EAAE;MAC/CL,cAAc,CAACQ,IAAI,CAACH,UAAU,CAAC;IACjC;EACF;EACA,OAAOL,cAAc;AACvB,CAAC;;AAED;AACA,OAAO,IAAIS,qBAAqB,GAAG,SAASA,qBAAqBA,CAACV,IAAI,EAAE;EACtE,IAAIW,cAAc,GAAG,EAAE;EACvB,IAAIT,UAAU,GAAGC,cAAc,CAACH,IAAI,CAAC;EACrC,IAAII,QAAQ,GAAGC,YAAY,CAACL,IAAI,CAAC;EACjC,KAAK,IAAIM,UAAU,GAAGJ,UAAU,EAAEI,UAAU,GAAGF,QAAQ,EAAEE,UAAU,EAAE,EAAE;IACrEK,cAAc,CAACF,IAAI,CAACH,UAAU,CAAC;EACjC;EACA,OAAOK,cAAc;AACvB,CAAC;;AAED;AACA,OAAO,IAAIR,cAAc,GAAG,SAASA,cAAcA,CAACH,IAAI,EAAE;EACxD,OAAOA,IAAI,CAACY,YAAY,GAAGC,gBAAgB,CAACb,IAAI,CAAC;AACnD,CAAC;AACD,OAAO,IAAIK,YAAY,GAAG,SAASA,YAAYA,CAACL,IAAI,EAAE;EACpD,OAAOA,IAAI,CAACY,YAAY,GAAGE,iBAAiB,CAACd,IAAI,CAAC;AACpD,CAAC;AACD,OAAO,IAAIa,gBAAgB,GAAG,SAASA,gBAAgBA,CAACb,IAAI,EAAE;EAC5D,OAAOA,IAAI,CAACe,UAAU,GAAGzB,IAAI,CAAC0B,KAAK,CAAChB,IAAI,CAACiB,YAAY,GAAG,CAAC,CAAC,IAAIC,QAAQ,CAAClB,IAAI,CAACmB,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;AAC7G,CAAC;AACD,OAAO,IAAIL,iBAAiB,GAAG,SAASA,iBAAiBA,CAACd,IAAI,EAAE;EAC9D,OAAOA,IAAI,CAACe,UAAU,GAAGzB,IAAI,CAAC0B,KAAK,CAAC,CAAChB,IAAI,CAACiB,YAAY,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAIC,QAAQ,CAAClB,IAAI,CAACmB,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAGnB,IAAI,CAACiB,YAAY;AACvI,CAAC;;AAED;AACA,OAAO,IAAIG,QAAQ,GAAG,SAASA,QAAQA,CAACC,IAAI,EAAE;EAC5C,OAAOA,IAAI,IAAIA,IAAI,CAACC,WAAW,IAAI,CAAC;AACtC,CAAC;AACD,OAAO,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACF,IAAI,EAAE;EAC9C,OAAOA,IAAI,IAAIA,IAAI,CAACG,YAAY,IAAI,CAAC;AACvC,CAAC;AACD,OAAO,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,WAAW,EAAE;EACrE,IAAIC,eAAe,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EAC/F,IAAIG,KAAK,EAAEC,KAAK,EAAEC,CAAC,EAAEC,UAAU;EAC/BH,KAAK,GAAGL,WAAW,CAACS,MAAM,GAAGT,WAAW,CAACU,IAAI;EAC7CJ,KAAK,GAAGN,WAAW,CAACW,MAAM,GAAGX,WAAW,CAACY,IAAI;EAC7CL,CAAC,GAAG3C,IAAI,CAACiD,KAAK,CAACP,KAAK,EAAED,KAAK,CAAC;EAC5BG,UAAU,GAAG5C,IAAI,CAACkD,KAAK,CAACP,CAAC,GAAG,GAAG,GAAG3C,IAAI,CAACmD,EAAE,CAAC;EAC1C,IAAIP,UAAU,GAAG,CAAC,EAAE;IAClBA,UAAU,GAAG,GAAG,GAAG5C,IAAI,CAACoD,GAAG,CAACR,UAAU,CAAC;EACzC;EACA,IAAIA,UAAU,IAAI,EAAE,IAAIA,UAAU,IAAI,CAAC,IAAIA,UAAU,IAAI,GAAG,IAAIA,UAAU,IAAI,GAAG,EAAE;IACjF,OAAO,MAAM;EACf;EACA,IAAIA,UAAU,IAAI,GAAG,IAAIA,UAAU,IAAI,GAAG,EAAE;IAC1C,OAAO,OAAO;EAChB;EACA,IAAIP,eAAe,KAAK,IAAI,EAAE;IAC5B,IAAIO,UAAU,IAAI,EAAE,IAAIA,UAAU,IAAI,GAAG,EAAE;MACzC,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAO,MAAM;IACf;EACF;EACA,OAAO,UAAU;AACnB,CAAC;;AAED;AACA,OAAO,IAAIS,SAAS,GAAG,SAASA,SAASA,CAAC3C,IAAI,EAAE;EAC9C,IAAI4C,KAAK,GAAG,IAAI;EAChB,IAAI,CAAC5C,IAAI,CAAC6C,QAAQ,EAAE;IAClB,IAAI7C,IAAI,CAACe,UAAU,IAAIf,IAAI,CAACY,YAAY,IAAIZ,IAAI,CAAC8C,UAAU,GAAG,CAAC,EAAE;MAC/DF,KAAK,GAAG,KAAK;IACf,CAAC,MAAM,IAAI5C,IAAI,CAAC8C,UAAU,IAAI9C,IAAI,CAACiB,YAAY,IAAIjB,IAAI,CAACY,YAAY,IAAIZ,IAAI,CAAC8C,UAAU,GAAG9C,IAAI,CAACiB,YAAY,EAAE;MAC3G2B,KAAK,GAAG,KAAK;IACf;EACF;EACA,OAAOA,KAAK;AACd,CAAC;;AAED;AACA,OAAO,IAAIG,aAAa,GAAG,SAASA,aAAaA,CAAC/C,IAAI,EAAEgD,IAAI,EAAE;EAC5D,IAAIC,SAAS,GAAG,CAAC,CAAC;EAClBD,IAAI,CAACE,OAAO,CAAC,UAAUC,GAAG,EAAE;IAC1B,OAAOF,SAAS,CAACE,GAAG,CAAC,GAAGnD,IAAI,CAACmD,GAAG,CAAC;EACnC,CAAC,CAAC;EACF,OAAOF,SAAS;AAClB,CAAC;;AAED;AACA,OAAO,IAAIG,gBAAgB,GAAG,SAASA,gBAAgBA,CAACpD,IAAI,EAAE;EAC5D;EACA,IAAI8C,UAAU,GAAG7D,KAAK,CAACoE,QAAQ,CAACC,KAAK,CAACtD,IAAI,CAACuD,QAAQ,CAAC;EACpD,IAAIC,QAAQ,GAAGxD,IAAI,CAACyD,OAAO;EAC3B,IAAIC,SAAS,GAAGpE,IAAI,CAACqE,IAAI,CAACvC,QAAQ,CAACoC,QAAQ,CAAC,CAAC;EAC7C,IAAII,SAAS,GAAG5D,IAAI,CAAC6D,QAAQ,IAAI7D,IAAI,CAAC6D,QAAQ,CAACC,IAAI;EACnD,IAAIC,UAAU,GAAGzE,IAAI,CAACqE,IAAI,CAACvC,QAAQ,CAACwC,SAAS,CAAC,CAAC;EAC/C,IAAII,UAAU;EACd,IAAI,CAAChE,IAAI,CAACiE,QAAQ,EAAE;IAClB,IAAIC,gBAAgB,GAAGlE,IAAI,CAACe,UAAU,IAAIG,QAAQ,CAAClB,IAAI,CAACmB,aAAa,CAAC,GAAG,CAAC;IAC1E,IAAI,OAAOnB,IAAI,CAACmB,aAAa,KAAK,QAAQ,IAAInB,IAAI,CAACmB,aAAa,CAACgD,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MAClFD,gBAAgB,IAAIR,SAAS,GAAG,GAAG;IACrC;IACAM,UAAU,GAAG1E,IAAI,CAACqE,IAAI,CAAC,CAACD,SAAS,GAAGQ,gBAAgB,IAAIlE,IAAI,CAACiB,YAAY,CAAC;EAC5E,CAAC,MAAM;IACL+C,UAAU,GAAGN,SAAS;EACxB;EACA,IAAIU,WAAW,GAAGZ,QAAQ,IAAIjC,SAAS,CAACiC,QAAQ,CAACa,aAAa,CAAC,kBAAkB,CAAC,CAAC;EACnF,IAAIC,UAAU,GAAGF,WAAW,GAAGpE,IAAI,CAACiB,YAAY;EAChD,IAAIL,YAAY,GAAGZ,IAAI,CAACY,YAAY,KAAKkB,SAAS,GAAG9B,IAAI,CAACuE,YAAY,GAAGvE,IAAI,CAACY,YAAY;EAC1F,IAAIZ,IAAI,CAACwE,GAAG,IAAIxE,IAAI,CAACY,YAAY,KAAKkB,SAAS,EAAE;IAC/ClB,YAAY,GAAGkC,UAAU,GAAG,CAAC,GAAG9C,IAAI,CAACuE,YAAY;EACnD;EACA,IAAIhE,cAAc,GAAGP,IAAI,CAACO,cAAc,IAAI,EAAE;EAC9C,IAAIkE,YAAY,GAAG1E,qBAAqB,CAACf,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgB,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;IAClFY,YAAY,EAAEA,YAAY;IAC1BL,cAAc,EAAEA;EAClB,CAAC,CAAC,CAAC;EACHA,cAAc,GAAGA,cAAc,CAACmE,MAAM,CAACD,YAAY,CAAC;EACpD,IAAIE,KAAK,GAAG;IACV7B,UAAU,EAAEA,UAAU;IACtBkB,UAAU,EAAEA,UAAU;IACtBN,SAAS,EAAEA,SAAS;IACpBK,UAAU,EAAEA,UAAU;IACtBnD,YAAY,EAAEA,YAAY;IAC1BwD,WAAW,EAAEA,WAAW;IACxBE,UAAU,EAAEA,UAAU;IACtB/D,cAAc,EAAEA;EAClB,CAAC;EACD,IAAIP,IAAI,CAAC4E,WAAW,KAAK,IAAI,IAAI5E,IAAI,CAAC6E,QAAQ,EAAE;IAC9CF,KAAK,CAAC,aAAa,CAAC,GAAG,SAAS;EAClC;EACA,OAAOA,KAAK;AACd,CAAC;AACD,OAAO,IAAIG,YAAY,GAAG,SAASA,YAAYA,CAAC9E,IAAI,EAAE;EACpD,IAAI+E,cAAc,GAAG/E,IAAI,CAAC+E,cAAc;IACtCC,SAAS,GAAGhF,IAAI,CAACgF,SAAS;IAC1BC,IAAI,GAAGjF,IAAI,CAACiF,IAAI;IAChBpC,QAAQ,GAAG7C,IAAI,CAAC6C,QAAQ;IACxBqC,KAAK,GAAGlF,IAAI,CAACkF,KAAK;IAClBpC,UAAU,GAAG9C,IAAI,CAAC8C,UAAU;IAC5BqC,QAAQ,GAAGnF,IAAI,CAACmF,QAAQ;IACxBvE,YAAY,GAAGZ,IAAI,CAACY,YAAY;IAChCG,UAAU,GAAGf,IAAI,CAACe,UAAU;IAC5BqE,cAAc,GAAGpF,IAAI,CAACoF,cAAc;IACpCnE,YAAY,GAAGjB,IAAI,CAACiB,YAAY;IAChCoE,MAAM,GAAGrF,IAAI,CAACqF,MAAM;EACtB,IAAI9E,cAAc,GAAGP,IAAI,CAACO,cAAc;EACxC,IAAIwE,cAAc,IAAIC,SAAS,EAAE,OAAO,CAAC,CAAC;EAC1C,IAAIM,cAAc,GAAGJ,KAAK;IACxBK,UAAU;IACVC,aAAa;IACbC,SAAS;EACX,IAAId,KAAK,GAAG,CAAC,CAAC;IACZe,SAAS,GAAG,CAAC,CAAC;EAChB,IAAIC,WAAW,GAAG9C,QAAQ,GAAGqC,KAAK,GAAGhG,KAAK,CAACgG,KAAK,EAAE,CAAC,EAAEpC,UAAU,GAAG,CAAC,CAAC;EACpE,IAAImC,IAAI,EAAE;IACR,IAAI,CAACpC,QAAQ,KAAKqC,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAIpC,UAAU,CAAC,EAAE,OAAO,CAAC,CAAC;IAC9D,IAAIoC,KAAK,GAAG,CAAC,EAAE;MACbI,cAAc,GAAGJ,KAAK,GAAGpC,UAAU;IACrC,CAAC,MAAM,IAAIoC,KAAK,IAAIpC,UAAU,EAAE;MAC9BwC,cAAc,GAAGJ,KAAK,GAAGpC,UAAU;IACrC;IACA,IAAIqC,QAAQ,IAAI5E,cAAc,CAACC,OAAO,CAAC8E,cAAc,CAAC,GAAG,CAAC,EAAE;MAC1D/E,cAAc,GAAGA,cAAc,CAACmE,MAAM,CAACY,cAAc,CAAC;IACxD;IACAX,KAAK,GAAG;MACNK,SAAS,EAAE,IAAI;MACfpE,YAAY,EAAE0E,cAAc;MAC5B/E,cAAc,EAAEA,cAAc;MAC9BoF,WAAW,EAAEL;IACf,CAAC;IACDI,SAAS,GAAG;MACVV,SAAS,EAAE,KAAK;MAChBW,WAAW,EAAEL;IACf,CAAC;EACH,CAAC,MAAM;IACLC,UAAU,GAAGD,cAAc;IAC3B,IAAIA,cAAc,GAAG,CAAC,EAAE;MACtBC,UAAU,GAAGD,cAAc,GAAGxC,UAAU;MACxC,IAAI,CAACD,QAAQ,EAAE0C,UAAU,GAAG,CAAC,CAAC,KAAK,IAAIzC,UAAU,GAAGsC,cAAc,KAAK,CAAC,EAAEG,UAAU,GAAGzC,UAAU,GAAGA,UAAU,GAAGsC,cAAc;IACjI,CAAC,MAAM,IAAI,CAACzC,SAAS,CAAC3C,IAAI,CAAC,IAAIsF,cAAc,GAAG1E,YAAY,EAAE;MAC5D0E,cAAc,GAAGC,UAAU,GAAG3E,YAAY;IAC5C,CAAC,MAAM,IAAIG,UAAU,IAAIuE,cAAc,IAAIxC,UAAU,EAAE;MACrDwC,cAAc,GAAGzC,QAAQ,GAAGC,UAAU,GAAGA,UAAU,GAAG,CAAC;MACvDyC,UAAU,GAAG1C,QAAQ,GAAG,CAAC,GAAGC,UAAU,GAAG,CAAC;IAC5C,CAAC,MAAM,IAAIwC,cAAc,IAAIxC,UAAU,EAAE;MACvCyC,UAAU,GAAGD,cAAc,GAAGxC,UAAU;MACxC,IAAI,CAACD,QAAQ,EAAE0C,UAAU,GAAGzC,UAAU,GAAG7B,YAAY,CAAC,KAAK,IAAI6B,UAAU,GAAGsC,cAAc,KAAK,CAAC,EAAEG,UAAU,GAAG,CAAC;IAClH;IACA,IAAI,CAAC1C,QAAQ,IAAIyC,cAAc,GAAGrE,YAAY,IAAI6B,UAAU,EAAE;MAC5DyC,UAAU,GAAGzC,UAAU,GAAG7B,YAAY;IACxC;IACAuE,aAAa,GAAGI,YAAY,CAAC5G,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgB,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;MACtEM,UAAU,EAAEgF;IACd,CAAC,CAAC,CAAC;IACHG,SAAS,GAAGG,YAAY,CAAC5G,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgB,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;MAClEM,UAAU,EAAEiF;IACd,CAAC,CAAC,CAAC;IACH,IAAI,CAAC1C,QAAQ,EAAE;MACb,IAAI2C,aAAa,KAAKC,SAAS,EAAEH,cAAc,GAAGC,UAAU;MAC5DC,aAAa,GAAGC,SAAS;IAC3B;IACA,IAAIN,QAAQ,EAAE;MACZ5E,cAAc,GAAGA,cAAc,CAACmE,MAAM,CAAC3E,qBAAqB,CAACf,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgB,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QACtGY,YAAY,EAAE0E;MAChB,CAAC,CAAC,CAAC,CAAC;IACN;IACA,IAAI,CAACD,MAAM,EAAE;MACXV,KAAK,GAAG;QACN/D,YAAY,EAAE2E,UAAU;QACxBM,UAAU,EAAEC,WAAW,CAAC9G,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgB,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;UACjE+F,IAAI,EAAEN;QACR,CAAC,CAAC,CAAC;QACHlF,cAAc,EAAEA,cAAc;QAC9BoF,WAAW,EAAEA;MACf,CAAC;IACH,CAAC,MAAM;MACLhB,KAAK,GAAG;QACNK,SAAS,EAAE,IAAI;QACfpE,YAAY,EAAE2E,UAAU;QACxBM,UAAU,EAAEG,kBAAkB,CAAChH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgB,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;UACxE+F,IAAI,EAAEP;QACR,CAAC,CAAC,CAAC;QACHjF,cAAc,EAAEA,cAAc;QAC9BoF,WAAW,EAAEA;MACf,CAAC;MACDD,SAAS,GAAG;QACVV,SAAS,EAAE,KAAK;QAChBpE,YAAY,EAAE2E,UAAU;QACxBM,UAAU,EAAEC,WAAW,CAAC9G,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgB,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;UACjE+F,IAAI,EAAEN;QACR,CAAC,CAAC,CAAC;QACHQ,SAAS,EAAE,IAAI;QACfN,WAAW,EAAEA;MACf,CAAC;IACH;EACF;EACA,OAAO;IACLhB,KAAK,EAAEA,KAAK;IACZe,SAAS,EAAEA;EACb,CAAC;AACH,CAAC;AACD,OAAO,IAAIQ,WAAW,GAAG,SAASA,WAAWA,CAAClG,IAAI,EAAEmG,OAAO,EAAE;EAC3D,IAAIC,WAAW,EAAEC,WAAW,EAAEC,WAAW,EAAEC,YAAY,EAAEZ,WAAW;EACpE,IAAIP,cAAc,GAAGpF,IAAI,CAACoF,cAAc;IACtCnE,YAAY,GAAGjB,IAAI,CAACiB,YAAY;IAChC6B,UAAU,GAAG9C,IAAI,CAAC8C,UAAU;IAC5BlC,YAAY,GAAGZ,IAAI,CAACY,YAAY;IAChC4F,mBAAmB,GAAGxG,IAAI,CAAC2F,WAAW;IACtCR,QAAQ,GAAGnF,IAAI,CAACmF,QAAQ;IACxBtC,QAAQ,GAAG7C,IAAI,CAAC6C,QAAQ;EAC1B0D,YAAY,GAAGzD,UAAU,GAAGsC,cAAc,KAAK,CAAC;EAChDgB,WAAW,GAAGG,YAAY,GAAG,CAAC,GAAG,CAACzD,UAAU,GAAGlC,YAAY,IAAIwE,cAAc;EAC7E,IAAIe,OAAO,CAACM,OAAO,KAAK,UAAU,EAAE;IAClCH,WAAW,GAAGF,WAAW,KAAK,CAAC,GAAGhB,cAAc,GAAGnE,YAAY,GAAGmF,WAAW;IAC7ET,WAAW,GAAG/E,YAAY,GAAG0F,WAAW;IACxC,IAAInB,QAAQ,IAAI,CAACtC,QAAQ,EAAE;MACzBwD,WAAW,GAAGzF,YAAY,GAAG0F,WAAW;MACxCX,WAAW,GAAGU,WAAW,KAAK,CAAC,CAAC,GAAGvD,UAAU,GAAG,CAAC,GAAGuD,WAAW;IACjE;IACA,IAAI,CAACxD,QAAQ,EAAE;MACb8C,WAAW,GAAGa,mBAAmB,GAAGpB,cAAc;IACpD;EACF,CAAC,MAAM,IAAIe,OAAO,CAACM,OAAO,KAAK,MAAM,EAAE;IACrCH,WAAW,GAAGF,WAAW,KAAK,CAAC,GAAGhB,cAAc,GAAGgB,WAAW;IAC9DT,WAAW,GAAG/E,YAAY,GAAG0F,WAAW;IACxC,IAAInB,QAAQ,IAAI,CAACtC,QAAQ,EAAE;MACzB8C,WAAW,GAAG,CAAC/E,YAAY,GAAGwE,cAAc,IAAItC,UAAU,GAAGsD,WAAW;IAC1E;IACA,IAAI,CAACvD,QAAQ,EAAE;MACb8C,WAAW,GAAGa,mBAAmB,GAAGpB,cAAc;IACpD;EACF,CAAC,MAAM,IAAIe,OAAO,CAACM,OAAO,KAAK,MAAM,EAAE;IACrC;IACAd,WAAW,GAAGQ,OAAO,CAACjB,KAAK,GAAGiB,OAAO,CAACf,cAAc;EACtD,CAAC,MAAM,IAAIe,OAAO,CAACM,OAAO,KAAK,UAAU,EAAE;IACzC;IACAd,WAAW,GAAGQ,OAAO,CAACjB,KAAK;IAC3B,IAAIrC,QAAQ,EAAE;MACZ,IAAI6D,SAAS,GAAGC,gBAAgB,CAAC3H,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgB,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QAC1E2F,WAAW,EAAEA;MACf,CAAC,CAAC,CAAC;MACH,IAAIA,WAAW,GAAGQ,OAAO,CAACvF,YAAY,IAAI8F,SAAS,KAAK,MAAM,EAAE;QAC9Df,WAAW,GAAGA,WAAW,GAAG7C,UAAU;MACxC,CAAC,MAAM,IAAI6C,WAAW,GAAGQ,OAAO,CAACvF,YAAY,IAAI8F,SAAS,KAAK,OAAO,EAAE;QACtEf,WAAW,GAAGA,WAAW,GAAG7C,UAAU;MACxC;IACF;EACF,CAAC,MAAM,IAAIqD,OAAO,CAACM,OAAO,KAAK,OAAO,EAAE;IACtCd,WAAW,GAAGiB,MAAM,CAACT,OAAO,CAACjB,KAAK,CAAC;EACrC;EACA,OAAOS,WAAW;AACpB,CAAC;AACD,OAAO,IAAIkB,UAAU,GAAG,SAASA,UAAUA,CAACC,CAAC,EAAEC,aAAa,EAAEvC,GAAG,EAAE;EACjE,IAAIsC,CAAC,CAACE,MAAM,CAACC,OAAO,CAACC,KAAK,CAAC,uBAAuB,CAAC,IAAI,CAACH,aAAa,EAAE,OAAO,EAAE;EAChF,IAAID,CAAC,CAACK,OAAO,KAAK,EAAE,EAAE,OAAO3C,GAAG,GAAG,MAAM,GAAG,UAAU;EACtD,IAAIsC,CAAC,CAACK,OAAO,KAAK,EAAE,EAAE,OAAO3C,GAAG,GAAG,UAAU,GAAG,MAAM;EACtD,OAAO,EAAE;AACX,CAAC;AACD,OAAO,IAAI4C,UAAU,GAAG,SAASA,UAAUA,CAACN,CAAC,EAAEO,KAAK,EAAEC,SAAS,EAAE;EAC/DR,CAAC,CAACE,MAAM,CAACC,OAAO,KAAK,KAAK,IAAIxH,kBAAkB,CAACqH,CAAC,CAAC;EACnD,IAAI,CAACO,KAAK,IAAI,CAACC,SAAS,IAAIR,CAAC,CAACS,IAAI,CAAC/G,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,EAAE;EACrE,OAAO;IACLgH,QAAQ,EAAE,IAAI;IACd9F,WAAW,EAAE;MACXS,MAAM,EAAE2E,CAAC,CAACW,OAAO,GAAGX,CAAC,CAACW,OAAO,CAAC,CAAC,CAAC,CAACC,KAAK,GAAGZ,CAAC,CAACa,OAAO;MAClDtF,MAAM,EAAEyE,CAAC,CAACW,OAAO,GAAGX,CAAC,CAACW,OAAO,CAAC,CAAC,CAAC,CAACG,KAAK,GAAGd,CAAC,CAACe,OAAO;MAClDzF,IAAI,EAAE0E,CAAC,CAACW,OAAO,GAAGX,CAAC,CAACW,OAAO,CAAC,CAAC,CAAC,CAACC,KAAK,GAAGZ,CAAC,CAACa,OAAO;MAChDrF,IAAI,EAAEwE,CAAC,CAACW,OAAO,GAAGX,CAAC,CAACW,OAAO,CAAC,CAAC,CAAC,CAACG,KAAK,GAAGd,CAAC,CAACe;IAC3C;EACF,CAAC;AACH,CAAC;AACD,OAAO,IAAIC,SAAS,GAAG,SAASA,SAASA,CAAChB,CAAC,EAAE9G,IAAI,EAAE;EACjD;EACA,IAAI+H,SAAS,GAAG/H,IAAI,CAAC+H,SAAS;IAC5B/C,SAAS,GAAGhF,IAAI,CAACgF,SAAS;IAC1Bf,QAAQ,GAAGjE,IAAI,CAACiE,QAAQ;IACxB+D,YAAY,GAAGhI,IAAI,CAACgI,YAAY;IAChCrG,eAAe,GAAG3B,IAAI,CAAC2B,eAAe;IACtC6C,GAAG,GAAGxE,IAAI,CAACwE,GAAG;IACd5D,YAAY,GAAGZ,IAAI,CAACY,YAAY;IAChCqH,YAAY,GAAGjI,IAAI,CAACiI,YAAY;IAChCC,WAAW,GAAGlI,IAAI,CAACkI,WAAW;IAC9BC,MAAM,GAAGnI,IAAI,CAACmI,MAAM;IACpBC,MAAM,GAAGpI,IAAI,CAACoI,MAAM;IACpBC,OAAO,GAAGrI,IAAI,CAACqI,OAAO;IACtBvF,UAAU,GAAG9C,IAAI,CAAC8C,UAAU;IAC5BsC,cAAc,GAAGpF,IAAI,CAACoF,cAAc;IACpCvC,QAAQ,GAAG7C,IAAI,CAAC6C,QAAQ;IACxBnB,WAAW,GAAG1B,IAAI,CAAC0B,WAAW;IAC9B4G,UAAU,GAAGtI,IAAI,CAACsI,UAAU;IAC5BhE,UAAU,GAAGtE,IAAI,CAACsE,UAAU;IAC5BZ,SAAS,GAAG1D,IAAI,CAAC0D,SAAS;EAC5B,IAAIqE,SAAS,EAAE;EACf,IAAI/C,SAAS,EAAE,OAAOvF,kBAAkB,CAACqH,CAAC,CAAC;EAC3C,IAAI7C,QAAQ,IAAI+D,YAAY,IAAIrG,eAAe,EAAElC,kBAAkB,CAACqH,CAAC,CAAC;EACtE,IAAIb,SAAS;IACXtB,KAAK,GAAG,CAAC,CAAC;EACZ,IAAI4D,OAAO,GAAG3C,YAAY,CAAC5F,IAAI,CAAC;EAChC0B,WAAW,CAACU,IAAI,GAAG0E,CAAC,CAACW,OAAO,GAAGX,CAAC,CAACW,OAAO,CAAC,CAAC,CAAC,CAACC,KAAK,GAAGZ,CAAC,CAACa,OAAO;EAC7DjG,WAAW,CAACY,IAAI,GAAGwE,CAAC,CAACW,OAAO,GAAGX,CAAC,CAACW,OAAO,CAAC,CAAC,CAAC,CAACG,KAAK,GAAGd,CAAC,CAACe,OAAO;EAC7DnG,WAAW,CAAC8G,WAAW,GAAGlJ,IAAI,CAACkD,KAAK,CAAClD,IAAI,CAACmJ,IAAI,CAACnJ,IAAI,CAACoJ,GAAG,CAAChH,WAAW,CAACU,IAAI,GAAGV,WAAW,CAACS,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;EACnG,IAAIwG,mBAAmB,GAAGrJ,IAAI,CAACkD,KAAK,CAAClD,IAAI,CAACmJ,IAAI,CAACnJ,IAAI,CAACoJ,GAAG,CAAChH,WAAW,CAACY,IAAI,GAAGZ,WAAW,CAACW,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;EACnG,IAAI,CAACV,eAAe,IAAI,CAAC0G,OAAO,IAAIM,mBAAmB,GAAG,EAAE,EAAE;IAC5D,OAAO;MACLZ,SAAS,EAAE;IACb,CAAC;EACH;EACA,IAAIpG,eAAe,EAAED,WAAW,CAAC8G,WAAW,GAAGG,mBAAmB;EAClE,IAAIC,cAAc,GAAG,CAAC,CAACpE,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK9C,WAAW,CAACU,IAAI,GAAGV,WAAW,CAACS,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EACvF,IAAIR,eAAe,EAAEiH,cAAc,GAAGlH,WAAW,CAACY,IAAI,GAAGZ,WAAW,CAACW,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;EACpF,IAAIwG,QAAQ,GAAGvJ,IAAI,CAACqE,IAAI,CAACb,UAAU,GAAGsC,cAAc,CAAC;EACrD,IAAI0D,cAAc,GAAGrH,iBAAiB,CAACzB,IAAI,CAAC0B,WAAW,EAAEC,eAAe,CAAC;EACzE,IAAIoH,gBAAgB,GAAGrH,WAAW,CAAC8G,WAAW;EAC9C,IAAI,CAAC3F,QAAQ,EAAE;IACb,IAAIjC,YAAY,KAAK,CAAC,KAAKkI,cAAc,KAAK,OAAO,IAAIA,cAAc,KAAK,MAAM,CAAC,IAAIlI,YAAY,GAAG,CAAC,IAAIiI,QAAQ,KAAKC,cAAc,KAAK,MAAM,IAAIA,cAAc,KAAK,IAAI,CAAC,IAAI,CAACnG,SAAS,CAAC3C,IAAI,CAAC,KAAK8I,cAAc,KAAK,MAAM,IAAIA,cAAc,KAAK,IAAI,CAAC,EAAE;MAC3PC,gBAAgB,GAAGrH,WAAW,CAAC8G,WAAW,GAAGP,YAAY;MACzD,IAAIC,WAAW,KAAK,KAAK,IAAIC,MAAM,EAAE;QACnCA,MAAM,CAACW,cAAc,CAAC;QACtBnE,KAAK,CAAC,aAAa,CAAC,GAAG,IAAI;MAC7B;IACF;EACF;EACA,IAAI,CAACyD,MAAM,IAAIE,UAAU,EAAE;IACzBA,UAAU,CAACQ,cAAc,CAAC;IAC1BnE,KAAK,CAAC,QAAQ,CAAC,GAAG,IAAI;EACxB;EACA,IAAI,CAACV,QAAQ,EAAE;IACb,IAAI,CAACO,GAAG,EAAE;MACRyB,SAAS,GAAGsC,OAAO,GAAGQ,gBAAgB,GAAGH,cAAc;IACzD,CAAC,MAAM;MACL3C,SAAS,GAAGsC,OAAO,GAAGQ,gBAAgB,GAAGH,cAAc;IACzD;EACF,CAAC,MAAM;IACL3C,SAAS,GAAGsC,OAAO,GAAGQ,gBAAgB,IAAIzE,UAAU,GAAGZ,SAAS,CAAC,GAAGkF,cAAc;EACpF;EACA,IAAIjH,eAAe,EAAE;IACnBsE,SAAS,GAAGsC,OAAO,GAAGQ,gBAAgB,GAAGH,cAAc;EACzD;EACAjE,KAAK,GAAG3F,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2F,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;IAClDjD,WAAW,EAAEA,WAAW;IACxBuE,SAAS,EAAEA,SAAS;IACpBJ,UAAU,EAAEC,WAAW,CAAC9G,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgB,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;MACjE+F,IAAI,EAAEE;IACR,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,IAAI3G,IAAI,CAACoD,GAAG,CAAChB,WAAW,CAACU,IAAI,GAAGV,WAAW,CAACS,MAAM,CAAC,GAAG7C,IAAI,CAACoD,GAAG,CAAChB,WAAW,CAACY,IAAI,GAAGZ,WAAW,CAACW,MAAM,CAAC,GAAG,GAAG,EAAE;IAC3G,OAAOsC,KAAK;EACd;EACA,IAAIjD,WAAW,CAAC8G,WAAW,GAAG,EAAE,EAAE;IAChC7D,KAAK,CAAC,SAAS,CAAC,GAAG,IAAI;IACvBlF,kBAAkB,CAACqH,CAAC,CAAC;EACvB;EACA,OAAOnC,KAAK;AACd,CAAC;AACD,OAAO,IAAIqE,QAAQ,GAAG,SAASA,QAAQA,CAAClC,CAAC,EAAE9G,IAAI,EAAE;EAC/C,IAAIwH,QAAQ,GAAGxH,IAAI,CAACwH,QAAQ;IAC1BH,KAAK,GAAGrH,IAAI,CAACqH,KAAK;IAClB3F,WAAW,GAAG1B,IAAI,CAAC0B,WAAW;IAC9BgC,SAAS,GAAG1D,IAAI,CAAC0D,SAAS;IAC1BuF,cAAc,GAAGjJ,IAAI,CAACiJ,cAAc;IACpCtH,eAAe,GAAG3B,IAAI,CAAC2B,eAAe;IACtC2C,UAAU,GAAGtE,IAAI,CAACsE,UAAU;IAC5B0D,YAAY,GAAGhI,IAAI,CAACgI,YAAY;IAChCD,SAAS,GAAG/H,IAAI,CAAC+H,SAAS;IAC1BmB,OAAO,GAAGlJ,IAAI,CAACkJ,OAAO;IACtBvD,WAAW,GAAG3F,IAAI,CAAC2F,WAAW;IAC9B/E,YAAY,GAAGZ,IAAI,CAACY,YAAY;IAChCiC,QAAQ,GAAG7C,IAAI,CAAC6C,QAAQ;EAC1B,IAAI,CAAC2E,QAAQ,EAAE;IACb,IAAIH,KAAK,EAAE5H,kBAAkB,CAACqH,CAAC,CAAC;IAChC,OAAO,CAAC,CAAC;EACX;EACA,IAAIqC,QAAQ,GAAGxH,eAAe,GAAG2C,UAAU,GAAG2E,cAAc,GAAGvF,SAAS,GAAGuF,cAAc;EACzF,IAAIH,cAAc,GAAGrH,iBAAiB,CAACC,WAAW,EAAEC,eAAe,CAAC;EACpE;EACA,IAAIgD,KAAK,GAAG;IACV6C,QAAQ,EAAE,KAAK;IACfU,WAAW,EAAE,KAAK;IAClBH,SAAS,EAAE,KAAK;IAChBM,OAAO,EAAE,KAAK;IACdD,MAAM,EAAE,KAAK;IACbnC,SAAS,EAAE,IAAI;IACfvE,WAAW,EAAE,CAAC;EAChB,CAAC;EACD,IAAIqG,SAAS,EAAE;IACb,OAAOpD,KAAK;EACd;EACA,IAAI,CAACjD,WAAW,CAAC8G,WAAW,EAAE;IAC5B,OAAO7D,KAAK;EACd;EACA,IAAIjD,WAAW,CAAC8G,WAAW,GAAGW,QAAQ,EAAE;IACtC1J,kBAAkB,CAACqH,CAAC,CAAC;IACrB,IAAIoC,OAAO,EAAE;MACXA,OAAO,CAACJ,cAAc,CAAC;IACzB;IACA,IAAIhG,UAAU,EAAEsG,QAAQ;IACxB,IAAIC,WAAW,GAAGxG,QAAQ,GAAGjC,YAAY,GAAG+E,WAAW;IACvD,QAAQmD,cAAc;MACpB,KAAK,MAAM;MACX,KAAK,IAAI;QACPM,QAAQ,GAAGC,WAAW,GAAGC,aAAa,CAACtJ,IAAI,CAAC;QAC5C8C,UAAU,GAAGkF,YAAY,GAAGuB,cAAc,CAACvJ,IAAI,EAAEoJ,QAAQ,CAAC,GAAGA,QAAQ;QACrEzE,KAAK,CAAC,kBAAkB,CAAC,GAAG,CAAC;QAC7B;MACF,KAAK,OAAO;MACZ,KAAK,MAAM;QACTyE,QAAQ,GAAGC,WAAW,GAAGC,aAAa,CAACtJ,IAAI,CAAC;QAC5C8C,UAAU,GAAGkF,YAAY,GAAGuB,cAAc,CAACvJ,IAAI,EAAEoJ,QAAQ,CAAC,GAAGA,QAAQ;QACrEzE,KAAK,CAAC,kBAAkB,CAAC,GAAG,CAAC;QAC7B;MACF;QACE7B,UAAU,GAAGuG,WAAW;IAC5B;IACA1E,KAAK,CAAC,qBAAqB,CAAC,GAAG7B,UAAU;EAC3C,CAAC,MAAM;IACL;IACA,IAAI0G,WAAW,GAAG5D,YAAY,CAAC5F,IAAI,CAAC;IACpC2E,KAAK,CAAC,YAAY,CAAC,GAAGqB,kBAAkB,CAAChH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgB,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;MAClF+F,IAAI,EAAEyD;IACR,CAAC,CAAC,CAAC;EACL;EACA,OAAO7E,KAAK;AACd,CAAC;AACD,OAAO,IAAI8E,mBAAmB,GAAG,SAASA,mBAAmBA,CAACzJ,IAAI,EAAE;EAClE,IAAIT,GAAG,GAAGS,IAAI,CAAC6C,QAAQ,GAAG7C,IAAI,CAAC8C,UAAU,GAAG,CAAC,GAAG9C,IAAI,CAAC8C,UAAU;EAC/D,IAAI4G,UAAU,GAAG1J,IAAI,CAAC6C,QAAQ,GAAG7C,IAAI,CAACiB,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC;EAC3D,IAAI0I,OAAO,GAAG3J,IAAI,CAAC6C,QAAQ,GAAG7C,IAAI,CAACiB,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC;EACxD,IAAI2I,OAAO,GAAG,EAAE;EAChB,OAAOF,UAAU,GAAGnK,GAAG,EAAE;IACvBqK,OAAO,CAACnJ,IAAI,CAACiJ,UAAU,CAAC;IACxBA,UAAU,GAAGC,OAAO,GAAG3J,IAAI,CAACoF,cAAc;IAC1CuE,OAAO,IAAIrK,IAAI,CAACE,GAAG,CAACQ,IAAI,CAACoF,cAAc,EAAEpF,IAAI,CAACiB,YAAY,CAAC;EAC7D;EACA,OAAO2I,OAAO;AAChB,CAAC;AACD,OAAO,IAAIL,cAAc,GAAG,SAASA,cAAcA,CAACvJ,IAAI,EAAEkF,KAAK,EAAE;EAC/D,IAAI2E,UAAU,GAAGJ,mBAAmB,CAACzJ,IAAI,CAAC;EAC1C,IAAI8J,aAAa,GAAG,CAAC;EACrB,IAAI5E,KAAK,GAAG2E,UAAU,CAACA,UAAU,CAAChI,MAAM,GAAG,CAAC,CAAC,EAAE;IAC7CqD,KAAK,GAAG2E,UAAU,CAACA,UAAU,CAAChI,MAAM,GAAG,CAAC,CAAC;EAC3C,CAAC,MAAM;IACL,KAAK,IAAIkI,CAAC,IAAIF,UAAU,EAAE;MACxB,IAAI3E,KAAK,GAAG2E,UAAU,CAACE,CAAC,CAAC,EAAE;QACzB7E,KAAK,GAAG4E,aAAa;QACrB;MACF;MACAA,aAAa,GAAGD,UAAU,CAACE,CAAC,CAAC;IAC/B;EACF;EACA,OAAO7E,KAAK;AACd,CAAC;AACD,OAAO,IAAIoE,aAAa,GAAG,SAASA,aAAaA,CAACtJ,IAAI,EAAE;EACtD,IAAIgK,YAAY,GAAGhK,IAAI,CAACe,UAAU,GAAGf,IAAI,CAACgE,UAAU,GAAG1E,IAAI,CAAC0B,KAAK,CAAChB,IAAI,CAACiB,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC;EAC5F,IAAIjB,IAAI,CAACgI,YAAY,EAAE;IACrB,IAAIiC,WAAW;IACf,IAAIC,SAAS,GAAGlK,IAAI,CAACyD,OAAO;IAC5B,IAAI0G,MAAM,GAAGD,SAAS,CAACE,gBAAgB,IAAIF,SAAS,CAACE,gBAAgB,CAAC,cAAc,CAAC,IAAI,EAAE;IAC3FC,KAAK,CAACC,IAAI,CAACH,MAAM,CAAC,CAACI,KAAK,CAAC,UAAUC,KAAK,EAAE;MACxC,IAAI,CAACxK,IAAI,CAACiE,QAAQ,EAAE;QAClB,IAAIuG,KAAK,CAACC,UAAU,GAAGT,YAAY,GAAG5I,QAAQ,CAACoJ,KAAK,CAAC,GAAG,CAAC,GAAGxK,IAAI,CAACiG,SAAS,GAAG,CAAC,CAAC,EAAE;UAC/EgE,WAAW,GAAGO,KAAK;UACnB,OAAO,KAAK;QACd;MACF,CAAC,MAAM;QACL,IAAIA,KAAK,CAACE,SAAS,GAAGnJ,SAAS,CAACiJ,KAAK,CAAC,GAAG,CAAC,GAAGxK,IAAI,CAACiG,SAAS,GAAG,CAAC,CAAC,EAAE;UAChEgE,WAAW,GAAGO,KAAK;UACnB,OAAO,KAAK;QACd;MACF;MACA,OAAO,IAAI;IACb,CAAC,CAAC;IACF,IAAI,CAACP,WAAW,EAAE;MAChB,OAAO,CAAC;IACV;IACA,IAAIU,YAAY,GAAG3K,IAAI,CAACwE,GAAG,KAAK,IAAI,GAAGxE,IAAI,CAAC8C,UAAU,GAAG9C,IAAI,CAACY,YAAY,GAAGZ,IAAI,CAACY,YAAY;IAC9F,IAAIgK,eAAe,GAAGtL,IAAI,CAACoD,GAAG,CAACuH,WAAW,CAACY,OAAO,CAAC3F,KAAK,GAAGyF,YAAY,CAAC,IAAI,CAAC;IAC7E,OAAOC,eAAe;EACxB,CAAC,MAAM;IACL,OAAO5K,IAAI,CAACoF,cAAc;EAC5B;AACF,CAAC;AACD,OAAO,IAAI0F,aAAa,GAAG,SAASA,aAAaA,CAAC9K,IAAI,EAAE+K,SAAS,EAAE;EACjE;IACE;IACAA,SAAS,CAACC,MAAM,CAAC,UAAUC,KAAK,EAAE9H,GAAG,EAAE;MACrC,OAAO8H,KAAK,IAAIjL,IAAI,CAACkL,cAAc,CAAC/H,GAAG,CAAC;IAC1C,CAAC,EAAE,IAAI,CAAC,GAAG,IAAI,GAAGgI,OAAO,CAACC,KAAK,CAAC,eAAe,EAAEpL,IAAI;EAAC;AAE1D,CAAC;AACD,OAAO,IAAI8F,WAAW,GAAG,SAASA,WAAWA,CAAC9F,IAAI,EAAE;EAClD8K,aAAa,CAAC9K,IAAI,EAAE,CAAC,MAAM,EAAE,eAAe,EAAE,YAAY,EAAE,cAAc,EAAE,YAAY,CAAC,CAAC;EAC1F,IAAI+D,UAAU,EAAEsH,WAAW;EAC3B,IAAIC,aAAa,GAAGtL,IAAI,CAAC8C,UAAU,GAAG,CAAC,GAAG9C,IAAI,CAACiB,YAAY;EAC3D,IAAI,CAACjB,IAAI,CAACiE,QAAQ,EAAE;IAClBF,UAAU,GAAGwH,cAAc,CAACvL,IAAI,CAAC,GAAGA,IAAI,CAACgE,UAAU;EACrD,CAAC,MAAM;IACLqH,WAAW,GAAGC,aAAa,GAAGtL,IAAI,CAACoE,WAAW;EAChD;EACA,IAAIoH,KAAK,GAAG;IACVC,OAAO,EAAE,CAAC;IACVC,UAAU,EAAE,EAAE;IACdC,gBAAgB,EAAE;EACpB,CAAC;EACD,IAAI3L,IAAI,CAAC4L,YAAY,EAAE;IACrB,IAAIC,eAAe,GAAG,CAAC7L,IAAI,CAACiE,QAAQ,GAAG,cAAc,GAAGjE,IAAI,CAAC+F,IAAI,GAAG,eAAe,GAAG,mBAAmB,GAAG/F,IAAI,CAAC+F,IAAI,GAAG,UAAU;IAClI,IAAI+F,SAAS,GAAG,CAAC9L,IAAI,CAACiE,QAAQ,GAAG,cAAc,GAAGjE,IAAI,CAAC+F,IAAI,GAAG,eAAe,GAAG,mBAAmB,GAAG/F,IAAI,CAAC+F,IAAI,GAAG,UAAU;IAC5H,IAAIgG,WAAW,GAAG,CAAC/L,IAAI,CAACiE,QAAQ,GAAG,aAAa,GAAGjE,IAAI,CAAC+F,IAAI,GAAG,KAAK,GAAG,aAAa,GAAG/F,IAAI,CAAC+F,IAAI,GAAG,KAAK;IACxGyF,KAAK,GAAGxM,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEwM,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MAClDK,eAAe,EAAEA,eAAe;MAChCC,SAAS,EAAEA,SAAS;MACpBC,WAAW,EAAEA;IACf,CAAC,CAAC;EACJ,CAAC,MAAM;IACL,IAAI/L,IAAI,CAACiE,QAAQ,EAAE;MACjBuH,KAAK,CAAC,KAAK,CAAC,GAAGxL,IAAI,CAAC+F,IAAI;IAC1B,CAAC,MAAM;MACLyF,KAAK,CAAC,MAAM,CAAC,GAAGxL,IAAI,CAAC+F,IAAI;IAC3B;EACF;EACA,IAAI/F,IAAI,CAACiF,IAAI,EAAEuG,KAAK,GAAG;IACrBC,OAAO,EAAE;EACX,CAAC;EACD,IAAI1H,UAAU,EAAEyH,KAAK,CAACQ,KAAK,GAAGjI,UAAU;EACxC,IAAIsH,WAAW,EAAEG,KAAK,CAACS,MAAM,GAAGZ,WAAW;;EAE3C;EACA,IAAIa,MAAM,IAAI,CAACA,MAAM,CAACC,gBAAgB,IAAID,MAAM,CAACE,WAAW,EAAE;IAC5D,IAAI,CAACpM,IAAI,CAACiE,QAAQ,EAAE;MAClBuH,KAAK,CAACa,UAAU,GAAGrM,IAAI,CAAC+F,IAAI,GAAG,IAAI;IACrC,CAAC,MAAM;MACLyF,KAAK,CAACc,SAAS,GAAGtM,IAAI,CAAC+F,IAAI,GAAG,IAAI;IACpC;EACF;EACA,OAAOyF,KAAK;AACd,CAAC;AACD,OAAO,IAAIxF,kBAAkB,GAAG,SAASA,kBAAkBA,CAAChG,IAAI,EAAE;EAChE8K,aAAa,CAAC9K,IAAI,EAAE,CAAC,MAAM,EAAE,eAAe,EAAE,YAAY,EAAE,cAAc,EAAE,YAAY,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;EAC9G,IAAIwL,KAAK,GAAG1F,WAAW,CAAC9F,IAAI,CAAC;EAC7B;EACA,IAAIA,IAAI,CAAC4L,YAAY,EAAE;IACrBJ,KAAK,CAACG,gBAAgB,GAAG,oBAAoB,GAAG3L,IAAI,CAACuM,KAAK,GAAG,KAAK,GAAGvM,IAAI,CAACwM,OAAO;IACjFhB,KAAK,CAACE,UAAU,GAAG,YAAY,GAAG1L,IAAI,CAACuM,KAAK,GAAG,KAAK,GAAGvM,IAAI,CAACwM,OAAO;EACrE,CAAC,MAAM;IACL,IAAIxM,IAAI,CAACiE,QAAQ,EAAE;MACjBuH,KAAK,CAACE,UAAU,GAAG,MAAM,GAAG1L,IAAI,CAACuM,KAAK,GAAG,KAAK,GAAGvM,IAAI,CAACwM,OAAO;IAC/D,CAAC,MAAM;MACLhB,KAAK,CAACE,UAAU,GAAG,OAAO,GAAG1L,IAAI,CAACuM,KAAK,GAAG,KAAK,GAAGvM,IAAI,CAACwM,OAAO;IAChE;EACF;EACA,OAAOhB,KAAK;AACd,CAAC;AACD,OAAO,IAAI5F,YAAY,GAAG,SAASA,YAAYA,CAAC5F,IAAI,EAAE;EACpD,IAAIA,IAAI,CAACyM,OAAO,EAAE;IAChB,OAAO,CAAC;EACV;EACA3B,aAAa,CAAC9K,IAAI,EAAE,CAAC,YAAY,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,gBAAgB,EAAE,YAAY,EAAE,WAAW,EAAE,eAAe,EAAE,aAAa,CAAC,CAAC;EACpL,IAAIM,UAAU,GAAGN,IAAI,CAACM,UAAU;IAC9BuD,QAAQ,GAAG7D,IAAI,CAAC6D,QAAQ;IACxBhB,QAAQ,GAAG7C,IAAI,CAAC6C,QAAQ;IACxB9B,UAAU,GAAGf,IAAI,CAACe,UAAU;IAC5B+B,UAAU,GAAG9C,IAAI,CAAC8C,UAAU;IAC5B7B,YAAY,GAAGjB,IAAI,CAACiB,YAAY;IAChCmE,cAAc,GAAGpF,IAAI,CAACoF,cAAc;IACpCpB,UAAU,GAAGhE,IAAI,CAACgE,UAAU;IAC5BN,SAAS,GAAG1D,IAAI,CAAC0D,SAAS;IAC1BgJ,aAAa,GAAG1M,IAAI,CAAC0M,aAAa;IAClCtI,WAAW,GAAGpE,IAAI,CAACoE,WAAW;IAC9Ba,IAAI,GAAGjF,IAAI,CAACiF,IAAI;IAChBhB,QAAQ,GAAGjE,IAAI,CAACiE,QAAQ;EAC1B,IAAIqC,WAAW,GAAG,CAAC;EACnB,IAAIqG,UAAU;EACd,IAAIhH,WAAW;EACf,IAAIiH,cAAc,GAAG,CAAC;EACtB,IAAI3H,IAAI,IAAIjF,IAAI,CAAC8C,UAAU,KAAK,CAAC,EAAE;IACjC,OAAO,CAAC;EACV;EACA,IAAI+J,cAAc,GAAG,CAAC;EACtB,IAAIhK,QAAQ,EAAE;IACZgK,cAAc,GAAG,CAACC,YAAY,CAAC9M,IAAI,CAAC,CAAC,CAAC;IACtC;IACA,IAAI8C,UAAU,GAAGsC,cAAc,KAAK,CAAC,IAAI9E,UAAU,GAAG8E,cAAc,GAAGtC,UAAU,EAAE;MACjF+J,cAAc,GAAG,EAAEvM,UAAU,GAAGwC,UAAU,GAAG7B,YAAY,IAAIX,UAAU,GAAGwC,UAAU,CAAC,GAAGA,UAAU,GAAGsC,cAAc,CAAC;IACtH;IACA;IACA,IAAIrE,UAAU,EAAE;MACd8L,cAAc,IAAI3L,QAAQ,CAACD,YAAY,GAAG,CAAC,CAAC;IAC9C;EACF,CAAC,MAAM;IACL,IAAI6B,UAAU,GAAGsC,cAAc,KAAK,CAAC,IAAI9E,UAAU,GAAG8E,cAAc,GAAGtC,UAAU,EAAE;MACjF+J,cAAc,GAAG5L,YAAY,GAAG6B,UAAU,GAAGsC,cAAc;IAC7D;IACA,IAAIrE,UAAU,EAAE;MACd8L,cAAc,GAAG3L,QAAQ,CAACD,YAAY,GAAG,CAAC,CAAC;IAC7C;EACF;EACAqF,WAAW,GAAGuG,cAAc,GAAG7I,UAAU;EACzC4I,cAAc,GAAGC,cAAc,GAAGzI,WAAW;EAC7C,IAAI,CAACH,QAAQ,EAAE;IACb0I,UAAU,GAAGrM,UAAU,GAAG0D,UAAU,GAAG,CAAC,CAAC,GAAGsC,WAAW;EACzD,CAAC,MAAM;IACLqG,UAAU,GAAGrM,UAAU,GAAG8D,WAAW,GAAG,CAAC,CAAC,GAAGwI,cAAc;EAC7D;EACA,IAAIF,aAAa,KAAK,IAAI,EAAE;IAC1B,IAAIK,gBAAgB;IACpB,IAAIC,SAAS,GAAGnJ,QAAQ,IAAIA,QAAQ,CAACC,IAAI;IACzCiJ,gBAAgB,GAAGzM,UAAU,GAAGwM,YAAY,CAAC9M,IAAI,CAAC;IAClD2F,WAAW,GAAGqH,SAAS,IAAIA,SAAS,CAACC,UAAU,CAACF,gBAAgB,CAAC;IACjEJ,UAAU,GAAGhH,WAAW,GAAGA,WAAW,CAAC8E,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC;IAC1D,IAAI1J,UAAU,KAAK,IAAI,EAAE;MACvBgM,gBAAgB,GAAGlK,QAAQ,GAAGvC,UAAU,GAAGwM,YAAY,CAAC9M,IAAI,CAAC,GAAGM,UAAU;MAC1EqF,WAAW,GAAGqH,SAAS,IAAIA,SAAS,CAACzJ,QAAQ,CAACwJ,gBAAgB,CAAC;MAC/DJ,UAAU,GAAG,CAAC;MACd,KAAK,IAAInC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGuC,gBAAgB,EAAEvC,KAAK,EAAE,EAAE;QACrDmC,UAAU,IAAIK,SAAS,IAAIA,SAAS,CAACzJ,QAAQ,CAACiH,KAAK,CAAC,IAAIwC,SAAS,CAACzJ,QAAQ,CAACiH,KAAK,CAAC,CAAClJ,WAAW;MAC/F;MACAqL,UAAU,IAAIzL,QAAQ,CAAClB,IAAI,CAACmB,aAAa,CAAC;MAC1CwL,UAAU,IAAIhH,WAAW,IAAI,CAACjC,SAAS,GAAGiC,WAAW,CAACrE,WAAW,IAAI,CAAC;IACxE;EACF;EACA,OAAOqL,UAAU;AACnB,CAAC;AACD,OAAO,IAAIG,YAAY,GAAG,SAASA,YAAYA,CAAC9M,IAAI,EAAE;EACpD,IAAIA,IAAI,CAACyM,OAAO,IAAI,CAACzM,IAAI,CAAC6C,QAAQ,EAAE;IAClC,OAAO,CAAC;EACV;EACA,IAAI7C,IAAI,CAAC0M,aAAa,EAAE;IACtB,OAAO1M,IAAI,CAAC8C,UAAU;EACxB;EACA,OAAO9C,IAAI,CAACiB,YAAY,IAAIjB,IAAI,CAACe,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;AACtD,CAAC;AACD,OAAO,IAAImM,aAAa,GAAG,SAASA,aAAaA,CAAClN,IAAI,EAAE;EACtD,IAAIA,IAAI,CAACyM,OAAO,IAAI,CAACzM,IAAI,CAAC6C,QAAQ,EAAE;IAClC,OAAO,CAAC;EACV;EACA,OAAO7C,IAAI,CAAC8C,UAAU;AACxB,CAAC;AACD,OAAO,IAAIyI,cAAc,GAAG,SAASA,cAAcA,CAACvL,IAAI,EAAE;EACxD,OAAOA,IAAI,CAAC8C,UAAU,KAAK,CAAC,GAAG,CAAC,GAAGgK,YAAY,CAAC9M,IAAI,CAAC,GAAGA,IAAI,CAAC8C,UAAU,GAAGoK,aAAa,CAAClN,IAAI,CAAC;AAC/F,CAAC;AACD,OAAO,IAAI2G,gBAAgB,GAAG,SAASA,gBAAgBA,CAAC3G,IAAI,EAAE;EAC5D,IAAIA,IAAI,CAAC2F,WAAW,GAAG3F,IAAI,CAACY,YAAY,EAAE;IACxC,IAAIZ,IAAI,CAAC2F,WAAW,GAAG3F,IAAI,CAACY,YAAY,GAAGuM,aAAa,CAACnN,IAAI,CAAC,EAAE;MAC9D,OAAO,MAAM;IACf;IACA,OAAO,OAAO;EAChB,CAAC,MAAM;IACL,IAAIA,IAAI,CAAC2F,WAAW,GAAG3F,IAAI,CAACY,YAAY,GAAGwM,YAAY,CAACpN,IAAI,CAAC,EAAE;MAC7D,OAAO,OAAO;IAChB;IACA,OAAO,MAAM;EACf;AACF,CAAC;AACD,OAAO,IAAImN,aAAa,GAAG,SAASA,aAAaA,CAACE,IAAI,EAAE;EACtD,IAAIpM,YAAY,GAAGoM,IAAI,CAACpM,YAAY;IAClCF,UAAU,GAAGsM,IAAI,CAACtM,UAAU;IAC5ByD,GAAG,GAAG6I,IAAI,CAAC7I,GAAG;IACdrD,aAAa,GAAGkM,IAAI,CAAClM,aAAa;EACpC;EACA,IAAIJ,UAAU,EAAE;IACd,IAAIuM,KAAK,GAAG,CAACrM,YAAY,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;IACtC,IAAIC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,EAAEmM,KAAK,IAAI,CAAC;IAC3C,IAAI9I,GAAG,IAAIvD,YAAY,GAAG,CAAC,KAAK,CAAC,EAAEqM,KAAK,IAAI,CAAC;IAC7C,OAAOA,KAAK;EACd;EACA,IAAI9I,GAAG,EAAE;IACP,OAAO,CAAC;EACV;EACA,OAAOvD,YAAY,GAAG,CAAC;AACzB,CAAC;AACD,OAAO,IAAImM,YAAY,GAAG,SAASA,YAAYA,CAACG,KAAK,EAAE;EACrD,IAAItM,YAAY,GAAGsM,KAAK,CAACtM,YAAY;IACnCF,UAAU,GAAGwM,KAAK,CAACxM,UAAU;IAC7ByD,GAAG,GAAG+I,KAAK,CAAC/I,GAAG;IACfrD,aAAa,GAAGoM,KAAK,CAACpM,aAAa;EACrC;EACA,IAAIJ,UAAU,EAAE;IACd,IAAIgF,IAAI,GAAG,CAAC9E,YAAY,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;IACrC,IAAIC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,EAAE4E,IAAI,IAAI,CAAC;IAC1C,IAAI,CAACvB,GAAG,IAAIvD,YAAY,GAAG,CAAC,KAAK,CAAC,EAAE8E,IAAI,IAAI,CAAC;IAC7C,OAAOA,IAAI;EACb;EACA,IAAIvB,GAAG,EAAE;IACP,OAAOvD,YAAY,GAAG,CAAC;EACzB;EACA,OAAO,CAAC;AACV,CAAC;AACD,OAAO,IAAIuM,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;EAC1C,OAAO,CAAC,EAAE,OAAOtB,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACuB,QAAQ,IAAIvB,MAAM,CAACuB,QAAQ,CAACC,aAAa,CAAC;AAC9F,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}