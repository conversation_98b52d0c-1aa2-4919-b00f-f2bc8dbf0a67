{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizResult.js\",\n  _s = $RefreshSig$();\nimport React, { startTransition, useEffect, useState } from 'react';\nimport { useNavigate, useLocation, useParams } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { TbTrophy, TbCheck, TbX, TbHome, TbStar, TbBrain, TbChartBar } from 'react-icons/tb';\nimport { chatWithChatGPTToExplainAns } from '../../../apicalls/chat';\nimport { useLanguage } from '../../../contexts/LanguageContext';\nimport ContentRenderer from '../../../components/ContentRenderer';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst QuizResult = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    id\n  } = useParams();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    isKiswahili\n  } = useLanguage();\n  // Get result data from navigation state first\n  const resultData = location.state || {\n    percentage: 0,\n    correctAnswers: 0,\n    totalQuestions: 0,\n    timeTaken: 0,\n    resultDetails: [],\n    xpData: null,\n    quizName: 'Quiz',\n    quizSubject: 'General',\n    passingPercentage: 60,\n    verdict: 'Fail'\n  };\n  const {\n    percentage,\n    correctAnswers,\n    totalQuestions,\n    timeTaken,\n    xpData,\n    quizName,\n    quizSubject,\n    passingPercentage,\n    verdict,\n    resultDetails\n  } = resultData;\n  const isPassed = verdict === 'Pass' || percentage >= (passingPercentage || 60);\n  const [confetti, setConfetti] = useState([]);\n  const [explanations, setExplanations] = useState({});\n  const [loadingExplanations, setLoadingExplanations] = useState({});\n  const [isFlashing, setIsFlashing] = useState(false);\n\n  // Play sound and trigger animations when component loads\n  useEffect(() => {\n    // Play enhanced sound effects and trigger flash animation\n    const playSound = () => {\n      // Trigger premium flash animation\n      setIsFlashing(true);\n      setTimeout(() => setIsFlashing(false), 3200); // Flash for 3.2 seconds (2 cycles of 0.8s each)\n\n      try {\n        if (isPassed) {\n          // Success sound with clapping\n          const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n\n          // Create a success melody\n          const playTone = (frequency, startTime, duration, type = 'sine') => {\n            const oscillator = audioContext.createOscillator();\n            const gainNode = audioContext.createGain();\n            oscillator.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n            oscillator.frequency.setValueAtTime(frequency, startTime);\n            oscillator.type = type;\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(0.1, startTime + 0.01);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + duration);\n            oscillator.start(startTime);\n            oscillator.stop(startTime + duration);\n          };\n\n          // Create clapping sound effect\n          const createClap = startTime => {\n            const noise = audioContext.createBufferSource();\n            const buffer = audioContext.createBuffer(1, audioContext.sampleRate * 0.1, audioContext.sampleRate);\n            const data = buffer.getChannelData(0);\n\n            // Generate white noise for clap\n            for (let i = 0; i < data.length; i++) {\n              data[i] = Math.random() * 2 - 1;\n            }\n            noise.buffer = buffer;\n            const filter = audioContext.createBiquadFilter();\n            filter.type = 'highpass';\n            filter.frequency.setValueAtTime(1000, startTime);\n            const gainNode = audioContext.createGain();\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(0.3, startTime + 0.01);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + 0.1);\n            noise.connect(filter);\n            filter.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n            noise.start(startTime);\n            noise.stop(startTime + 0.1);\n          };\n          const now = audioContext.currentTime;\n\n          // Play success melody: C-E-G-C (major chord progression)\n          playTone(523.25, now, 0.2); // C5\n          playTone(659.25, now + 0.1, 0.2); // E5\n          playTone(783.99, now + 0.2, 0.2); // G5\n          playTone(1046.5, now + 0.3, 0.4); // C6\n\n          // Add clapping sounds\n          createClap(now + 0.5);\n          createClap(now + 0.7);\n          createClap(now + 0.9);\n          createClap(now + 1.1);\n        } else {\n          // Fail sound - create a gentle, encouraging tone\n          const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n          const playTone = (frequency, startTime, duration) => {\n            const oscillator = audioContext.createOscillator();\n            const gainNode = audioContext.createGain();\n            oscillator.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n            oscillator.frequency.setValueAtTime(frequency, startTime);\n            oscillator.type = 'sine';\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(0.08, startTime + 0.01);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + duration);\n            oscillator.start(startTime);\n            oscillator.stop(startTime + duration);\n          };\n\n          // Play gentle fail tone: A-F (not harsh, encouraging)\n          const now = audioContext.currentTime;\n          playTone(440, now, 0.3); // A4\n          playTone(349.23, now + 0.2, 0.4); // F4\n        }\n      } catch (error) {\n        console.log('Audio not supported');\n      }\n    };\n\n    // Generate premium animations based on pass/fail\n    if (isPassed) {\n      // Premium confetti explosion\n      const premiumConfetti = [];\n\n      // Main confetti burst (200 pieces)\n      for (let i = 0; i < 200; i++) {\n        const colors = ['#FFD700', '#FFA500', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FF69B4', '#32CD32', '#FF4500', '#9370DB', '#00CED1', '#FF1493', '#00FF7F', '#FF8C00', '#DA70D6'];\n        premiumConfetti.push({\n          id: `confetti_${i}`,\n          left: 20 + Math.random() * 60,\n          // More centered\n          delay: Math.random() * 2,\n          duration: 4 + Math.random() * 3,\n          color: colors[Math.floor(Math.random() * colors.length)],\n          shape: ['circle', 'square', 'triangle'][Math.floor(Math.random() * 3)],\n          size: 3 + Math.random() * 6,\n          type: 'confetti',\n          randomX: (Math.random() - 0.5) * 200 // For burst effect\n        });\n      }\n\n      // Premium sparkles (50 pieces)\n      for (let i = 0; i < 50; i++) {\n        premiumConfetti.push({\n          id: `sparkle_${i}`,\n          left: Math.random() * 100,\n          delay: Math.random() * 3,\n          duration: 2 + Math.random() * 2,\n          color: ['#FFD700', '#FFFFFF', '#FFF700', '#FFFF00'][Math.floor(Math.random() * 4)],\n          size: 1 + Math.random() * 3,\n          type: 'sparkle'\n        });\n      }\n\n      // Burst confetti (100 pieces from center)\n      for (let i = 0; i < 100; i++) {\n        premiumConfetti.push({\n          id: `burst_${i}`,\n          left: 45 + Math.random() * 10,\n          // Center burst\n          delay: Math.random() * 0.5,\n          duration: 3 + Math.random() * 2,\n          color: ['#FFD700', '#FF6B6B', '#4ECDC4', '#FF69B4'][Math.floor(Math.random() * 4)],\n          shape: 'circle',\n          size: 2 + Math.random() * 4,\n          type: 'burst',\n          randomX: (Math.random() - 0.5) * 300\n        });\n      }\n      setConfetti(premiumConfetti);\n\n      // Remove all animations after 10 seconds\n      setTimeout(() => setConfetti([]), 10000);\n    } else {\n      // Premium motivational elements\n      const motivationalElements = [];\n      const motivationalEmojis = ['💪', '🌟', '📚', '🎯', '🚀', '💡', '⭐', '✨', '🔥', '💎'];\n      for (let i = 0; i < 30; i++) {\n        motivationalElements.push({\n          id: `motivate_${i}`,\n          left: Math.random() * 100,\n          delay: Math.random() * 4,\n          duration: 5 + Math.random() * 3,\n          emoji: motivationalEmojis[Math.floor(Math.random() * motivationalEmojis.length)],\n          isMotivational: true,\n          size: 2 + Math.random() * 2\n        });\n      }\n      setConfetti(motivationalElements);\n\n      // Remove motivational elements after 8 seconds\n      setTimeout(() => setConfetti([]), 8000);\n    }\n    playSound();\n  }, [isPassed]);\n\n  // Function to fetch explanation\n  const fetchExplanation = async (questionIndex, detail) => {\n    const questionKey = `question_${questionIndex}`;\n\n    // Don't fetch if already loading or already have explanation\n    if (loadingExplanations[questionKey] || explanations[questionKey]) {\n      return;\n    }\n    try {\n      setLoadingExplanations(prev => ({\n        ...prev,\n        [questionKey]: true\n      }));\n      const response = await chatWithChatGPTToExplainAns({\n        question: detail.questionText || detail.questionName,\n        expectedAnswer: detail.correctAnswer,\n        userAnswer: detail.userAnswer,\n        imageUrl: detail.questionImage || detail.image || detail.imageUrl || null,\n        language: isKiswahili ? 'kiswahili' : 'english'\n      });\n      if (response.success) {\n        setExplanations(prev => ({\n          ...prev,\n          [questionKey]: response.explanation\n        }));\n      } else {\n        console.error('Failed to fetch explanation:', response.error);\n        setExplanations(prev => ({\n          ...prev,\n          [questionKey]: 'Sorry, we could not generate an explanation at this time. Please try again later.'\n        }));\n      }\n    } catch (error) {\n      console.error('Error fetching explanation:', error);\n      setExplanations(prev => ({\n        ...prev,\n        [questionKey]: 'Sorry, we could not generate an explanation at this time. Please try again later.'\n      }));\n    } finally {\n      setLoadingExplanations(prev => ({\n        ...prev,\n        [questionKey]: false\n      }));\n    }\n  };\n  const handleBackToQuizzes = () => {\n    console.log('🏠 Navigating to quiz listing...');\n    startTransition(() => {\n      navigate('/user/quiz');\n    });\n  };\n  const handleRetakeQuiz = () => {\n    console.log('🔄 Retaking quiz with ID:', id);\n    if (id) {\n      startTransition(() => {\n        navigate(`/quiz/${id}/play`);\n      });\n    } else {\n      console.log('❌ No quiz ID available, going to quiz listing');\n      startTransition(() => {\n        navigate('/user/quiz');\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `min-h-screen flex items-center justify-center relative overflow-hidden ${isPassed ? 'bg-gradient-to-br from-green-50 via-emerald-50 to-teal-100' : 'bg-gradient-to-br from-red-50 via-pink-50 to-orange-100'} ${isFlashing ? isPassed ? 'flash-green' : 'flash-red' : ''}`,\n    style: {\n      padding: window.innerWidth <= 768 ? '8px' : window.innerWidth <= 1024 ? '16px' : '24px'\n    },\n    children: [confetti.map(piece => {\n      if (piece.isMotivational) {\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute opacity-90\",\n          style: {\n            left: `${piece.left}%`,\n            top: `${20 + Math.random() * 60}%`,\n            fontSize: `${piece.size || 2}rem`,\n            animation: `heart-beat ${piece.duration}s ease-in-out ${piece.delay}s infinite`,\n            zIndex: 100\n          },\n          children: piece.emoji\n        }, piece.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 13\n        }, this);\n      }\n      if (piece.type === 'sparkle') {\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute\",\n          style: {\n            left: `${piece.left}%`,\n            width: `${piece.size}px`,\n            height: `${piece.size}px`,\n            background: `radial-gradient(circle, ${piece.color}, transparent)`,\n            borderRadius: '50%',\n            animation: `premium-sparkle ${piece.duration}s ease-in-out ${piece.delay}s infinite`,\n            top: `${Math.random() * 100}%`,\n            boxShadow: `0 0 ${piece.size * 2}px ${piece.color}`,\n            zIndex: 100\n          }\n        }, piece.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 13\n        }, this);\n      }\n      if (piece.type === 'burst') {\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute opacity-90\",\n          style: {\n            left: `${piece.left}%`,\n            width: `${piece.size}px`,\n            height: `${piece.size}px`,\n            backgroundColor: piece.color,\n            borderRadius: piece.shape === 'circle' ? '50%' : piece.shape === 'triangle' ? '0' : '0%',\n            clipPath: piece.shape === 'triangle' ? 'polygon(50% 0%, 0% 100%, 100% 100%)' : 'none',\n            animation: `confetti-burst ${piece.duration}s ease-out ${piece.delay}s forwards`,\n            top: '40%',\n            '--random-x': `${piece.randomX}px`,\n            boxShadow: `0 0 ${piece.size}px ${piece.color}40`,\n            zIndex: 100\n          }\n        }, piece.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 13\n        }, this);\n      }\n\n      // Regular premium confetti\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute opacity-90\",\n        style: {\n          left: `${piece.left}%`,\n          width: `${piece.size}px`,\n          height: `${piece.size}px`,\n          backgroundColor: piece.color,\n          borderRadius: piece.shape === 'circle' ? '50%' : piece.shape === 'triangle' ? '0' : '0%',\n          clipPath: piece.shape === 'triangle' ? 'polygon(50% 0%, 0% 100%, 100% 100%)' : 'none',\n          animation: `confetti-fall ${piece.duration}s ease-out ${piece.delay}s forwards`,\n          top: '-20px',\n          boxShadow: `0 0 ${piece.size}px ${piece.color}60`,\n          border: `1px solid ${piece.color}`,\n          background: `linear-gradient(45deg, ${piece.color}, ${piece.color}80)`,\n          zIndex: 100\n        }\n      }, piece.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 11\n      }, this);\n    }), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        @keyframes confetti-fall {\n          0% {\n            transform: translateY(-20px) rotate(0deg) scale(0);\n            opacity: 0;\n          }\n          10% {\n            opacity: 1;\n            transform: translateY(0px) rotate(36deg) scale(1);\n          }\n          100% {\n            transform: translateY(100vh) rotate(1080deg) scale(0.3);\n            opacity: 0;\n          }\n        }\n\n        @keyframes confetti-burst {\n          0% {\n            transform: translateY(-10px) translateX(0px) rotate(0deg) scale(0);\n            opacity: 0;\n          }\n          15% {\n            transform: translateY(-50px) translateX(var(--random-x)) rotate(180deg) scale(1.2);\n            opacity: 1;\n          }\n          100% {\n            transform: translateY(100vh) translateX(calc(var(--random-x) * 2)) rotate(1440deg) scale(0);\n            opacity: 0;\n          }\n        }\n\n        @keyframes premium-sparkle {\n          0%, 100% {\n            transform: scale(0) rotate(0deg);\n            opacity: 0;\n          }\n          50% {\n            transform: scale(1.5) rotate(180deg);\n            opacity: 1;\n          }\n        }\n\n\n\n        @keyframes heart-beat {\n          0%, 100% { transform: scale(1); }\n          50% { transform: scale(1.3); }\n        }\n\n        @keyframes text-bounce {\n          0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }\n          40% { transform: translateY(-20px) scale(1.1); }\n          60% { transform: translateY(-10px) scale(1.05); }\n        }\n\n        @keyframes text-glow {\n          0%, 100% {\n            text-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor;\n            transform: scale(1);\n          }\n          50% {\n            text-shadow: 0 0 20px currentColor, 0 0 40px currentColor, 0 0 60px currentColor;\n            transform: scale(1.05);\n          }\n        }\n\n        @keyframes rainbow-text {\n          0% {\n            color: #FF6B6B;\n            text-shadow: 0 0 20px #FF6B6B, 0 0 40px #FF6B6B;\n          }\n          16% {\n            color: #FFD93D;\n            text-shadow: 0 0 20px #FFD93D, 0 0 40px #FFD93D;\n          }\n          33% {\n            color: #6BCF7F;\n            text-shadow: 0 0 20px #6BCF7F, 0 0 40px #6BCF7F;\n          }\n          50% {\n            color: #4D96FF;\n            text-shadow: 0 0 20px #4D96FF, 0 0 40px #4D96FF;\n          }\n          66% {\n            color: #9B59B6;\n            text-shadow: 0 0 20px #9B59B6, 0 0 40px #9B59B6;\n          }\n          83% {\n            color: #FF69B4;\n            text-shadow: 0 0 20px #FF69B4, 0 0 40px #FF69B4;\n          }\n          100% {\n            color: #FF6B6B;\n            text-shadow: 0 0 20px #FF6B6B, 0 0 40px #FF6B6B;\n          }\n        }\n\n        @keyframes shake-celebrate {\n          0%, 100% { transform: translateX(0) rotate(0deg); }\n          10% { transform: translateX(-5px) rotate(-1deg); }\n          20% { transform: translateX(5px) rotate(1deg); }\n          30% { transform: translateX(-5px) rotate(-1deg); }\n          40% { transform: translateX(5px) rotate(1deg); }\n          50% { transform: translateX(-3px) rotate(-0.5deg); }\n          60% { transform: translateX(3px) rotate(0.5deg); }\n          70% { transform: translateX(-2px) rotate(-0.5deg); }\n          80% { transform: translateX(2px) rotate(0.5deg); }\n          90% { transform: translateX(-1px) rotate(0deg); }\n        }\n\n        @keyframes zoom-in-out {\n          0%, 100% { transform: scale(1); }\n          50% { transform: scale(1.2); }\n        }\n\n        .animate-text-bounce {\n          animation: text-bounce 2s ease-in-out infinite;\n        }\n\n        .animate-text-glow {\n          animation: text-glow 2s ease-in-out infinite;\n        }\n\n        .animate-rainbow {\n          animation: rainbow-text 3s linear infinite;\n        }\n\n        .animate-shake-celebrate {\n          animation: shake-celebrate 1s ease-in-out infinite;\n        }\n\n        .animate-zoom {\n          animation: zoom-in-out 2s ease-in-out infinite;\n        }\n\n        @keyframes elegant-scale {\n          0%, 100% {\n            transform: scale(1) rotateY(0deg);\n            text-shadow: 0 0 20px currentColor;\n          }\n          50% {\n            transform: scale(1.1) rotateY(5deg);\n            text-shadow: 0 0 40px currentColor, 0 0 60px currentColor;\n          }\n        }\n\n        @keyframes premium-pulse {\n          0%, 100% {\n            transform: scale(1);\n            opacity: 1;\n          }\n          50% {\n            transform: scale(1.05);\n            opacity: 0.9;\n          }\n        }\n\n        @keyframes smooth-glow {\n          0%, 100% {\n            text-shadow: 0 0 15px currentColor, 0 0 30px currentColor, 0 0 45px currentColor;\n            filter: brightness(1) saturate(1.2);\n          }\n          50% {\n            text-shadow: 0 0 40px currentColor, 0 0 60px currentColor, 0 0 80px currentColor, 0 0 100px currentColor;\n            filter: brightness(1.3) saturate(1.5);\n          }\n        }\n\n        @keyframes rainbow-glow {\n          0% {\n            color: #FF6B6B;\n            text-shadow: 0 0 20px #FF6B6B, 0 0 40px #FF6B6B, 0 0 60px #FF6B6B;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          16% {\n            color: #FFD93D;\n            text-shadow: 0 0 20px #FFD93D, 0 0 40px #FFD93D, 0 0 60px #FFD93D;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          33% {\n            color: #6BCF7F;\n            text-shadow: 0 0 20px #6BCF7F, 0 0 40px #6BCF7F, 0 0 60px #6BCF7F;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          50% {\n            color: #4D96FF;\n            text-shadow: 0 0 20px #4D96FF, 0 0 40px #4D96FF, 0 0 60px #4D96FF;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          66% {\n            color: #9B59B6;\n            text-shadow: 0 0 20px #9B59B6, 0 0 40px #9B59B6, 0 0 60px #9B59B6;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          83% {\n            color: #FF69B4;\n            text-shadow: 0 0 20px #FF69B4, 0 0 40px #FF69B4, 0 0 60px #FF69B4;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          100% {\n            color: #FF6B6B;\n            text-shadow: 0 0 20px #FF6B6B, 0 0 40px #FF6B6B, 0 0 60px #FF6B6B;\n            filter: brightness(1.2) saturate(1.3);\n          }\n        }\n\n        @keyframes celebration-bounce {\n          0%, 20%, 50%, 80%, 100% {\n            transform: translateY(0) scale(1);\n          }\n          40% {\n            transform: translateY(-15px) scale(1.05);\n          }\n          60% {\n            transform: translateY(-8px) scale(1.02);\n          }\n        }\n\n        .animate-elegant {\n          animation: elegant-scale 3s ease-in-out infinite;\n        }\n\n        .animate-premium-pulse {\n          animation: premium-pulse 2s ease-in-out infinite;\n        }\n\n        .animate-smooth-glow {\n          animation: smooth-glow 2.5s ease-in-out infinite;\n        }\n\n        .animate-celebration {\n          animation: celebration-bounce 2s ease-in-out infinite;\n        }\n\n        .animate-rainbow {\n          animation: rainbow-text 3s linear infinite;\n        }\n\n        .animate-rainbow-glow {\n          animation: rainbow-glow 3s linear infinite;\n        }\n\n        @keyframes red-glow {\n          0%, 100% {\n            color: #EF4444;\n            text-shadow: 0 0 20px #EF4444, 0 0 40px #EF4444, 0 0 60px #EF4444;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          25% {\n            color: #DC2626;\n            text-shadow: 0 0 25px #DC2626, 0 0 50px #DC2626, 0 0 75px #DC2626;\n            filter: brightness(1.3) saturate(1.4);\n          }\n          50% {\n            color: #B91C1C;\n            text-shadow: 0 0 30px #B91C1C, 0 0 60px #B91C1C, 0 0 90px #B91C1C;\n            filter: brightness(1.4) saturate(1.5);\n          }\n          75% {\n            color: #DC2626;\n            text-shadow: 0 0 25px #DC2626, 0 0 50px #DC2626, 0 0 75px #DC2626;\n            filter: brightness(1.3) saturate(1.4);\n          }\n        }\n\n        .animate-red-glow {\n          animation: red-glow 2.5s ease-in-out infinite;\n        }\n\n        @keyframes premium-green-flash {\n          0% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n          25% {\n            background: linear-gradient(45deg, rgba(34, 197, 94, 0.1), rgba(16, 185, 129, 0.1));\n            box-shadow: inset 0 0 100px rgba(34, 197, 94, 0.2);\n          }\n          50% {\n            background: linear-gradient(45deg, rgba(34, 197, 94, 0.4), rgba(16, 185, 129, 0.4));\n            box-shadow: inset 0 0 200px rgba(34, 197, 94, 0.5), 0 0 50px rgba(34, 197, 94, 0.3);\n          }\n          75% {\n            background: linear-gradient(45deg, rgba(34, 197, 94, 0.2), rgba(16, 185, 129, 0.2));\n            box-shadow: inset 0 0 150px rgba(34, 197, 94, 0.3);\n          }\n          100% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n        }\n\n        @keyframes premium-red-flash {\n          0% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n          25% {\n            background: linear-gradient(45deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 127, 0.1));\n            box-shadow: inset 0 0 100px rgba(239, 68, 68, 0.2);\n          }\n          50% {\n            background: linear-gradient(45deg, rgba(239, 68, 68, 0.4), rgba(220, 38, 127, 0.4));\n            box-shadow: inset 0 0 200px rgba(239, 68, 68, 0.5), 0 0 50px rgba(239, 68, 68, 0.3);\n          }\n          75% {\n            background: linear-gradient(45deg, rgba(239, 68, 68, 0.2), rgba(220, 38, 127, 0.2));\n            box-shadow: inset 0 0 150px rgba(239, 68, 68, 0.3);\n          }\n          100% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n        }\n\n        .flash-green {\n          animation: premium-green-flash 0.8s ease-in-out 2;\n        }\n\n        .flash-red {\n          animation: premium-red-flash 0.8s ease-in-out 2;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 404,\n      columnNumber: 7\n    }, this), isFlashing && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 pointer-events-none\",\n      style: {\n        background: isPassed ? 'radial-gradient(circle at center, rgba(34, 197, 94, 0.3) 0%, rgba(16, 185, 129, 0.2) 50%, transparent 100%)' : 'radial-gradient(circle at center, rgba(239, 68, 68, 0.3) 0%, rgba(220, 38, 127, 0.2) 50%, transparent 100%)',\n        animation: isPassed ? 'premium-green-flash 0.8s ease-in-out infinite' : 'premium-red-flash 0.8s ease-in-out infinite',\n        zIndex: 5\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 730,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `bg-white rounded-2xl shadow-2xl border-2 w-full relative ${isPassed ? 'border-green-400 shadow-green-200' : 'border-red-400 shadow-red-200'} ${isFlashing ? 'shadow-3xl' : ''}`,\n      style: {\n        background: isFlashing ? isPassed ? 'linear-gradient(135deg, rgba(255,255,255,0.95), rgba(34, 197, 94, 0.05))' : 'linear-gradient(135deg, rgba(255,255,255,0.95), rgba(239, 68, 68, 0.05))' : 'white',\n        boxShadow: isFlashing ? isPassed ? '0 25px 50px rgba(34, 197, 94, 0.3), 0 0 100px rgba(34, 197, 94, 0.2)' : '0 25px 50px rgba(239, 68, 68, 0.3), 0 0 100px rgba(239, 68, 68, 0.2)' : '0 25px 50px rgba(0,0,0,0.15)',\n        zIndex: 10,\n        padding: window.innerWidth <= 768 ? '16px' : window.innerWidth <= 1024 ? '24px' : '32px',\n        maxWidth: window.innerWidth <= 768 ? '100%' : window.innerWidth <= 1024 ? '90%' : '800px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        style: {\n          marginBottom: window.innerWidth <= 768 ? '16px' : '32px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `inline-flex items-center justify-center rounded-full mb-4 relative ${isPassed ? 'bg-gradient-to-br from-green-100 to-emerald-100' : 'bg-gradient-to-br from-red-100 to-pink-100'}`,\n          style: {\n            width: window.innerWidth <= 768 ? '60px' : '96px',\n            height: window.innerWidth <= 768 ? '60px' : '96px'\n          },\n          children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n            className: `${isPassed ? 'text-yellow-500' : 'text-gray-500'}`,\n            style: {\n              width: window.innerWidth <= 768 ? '30px' : '48px',\n              height: window.innerWidth <= 768 ? '30px' : '48px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 774,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 765,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: `font-bold mb-4 ${isPassed ? 'text-green-600 animate-elegant animate-smooth-glow' : 'animate-premium-pulse animate-red-glow'}`,\n          style: {\n            fontSize: window.innerWidth <= 768 ? '24px' : window.innerWidth <= 1024 ? '36px' : '48px'\n          },\n          children: isPassed ? /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"flex items-center justify-center\",\n            style: {\n              gap: window.innerWidth <= 768 ? '8px' : '16px',\n              flexWrap: 'wrap'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"animate-celebration\",\n              style: {\n                fontSize: window.innerWidth <= 768 ? '32px' : '56px'\n              },\n              children: \"\\uD83C\\uDF89\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 798,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"animate-rainbow-glow animate-elegant\",\n              children: \"Congratulations!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 802,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"animate-celebration\",\n              style: {\n                fontSize: window.innerWidth <= 768 ? '32px' : '56px'\n              },\n              children: \"\\uD83C\\uDF89\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 803,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 794,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"flex items-center justify-center\",\n            style: {\n              gap: window.innerWidth <= 768 ? '8px' : '16px',\n              flexWrap: 'wrap'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"animate-premium-pulse\",\n              style: {\n                fontSize: window.innerWidth <= 768 ? '32px' : '56px'\n              },\n              children: \"\\uD83D\\uDCAA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 813,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"animate-red-glow animate-elegant\",\n              children: \"Keep Going!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 817,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"animate-premium-pulse\",\n              style: {\n                fontSize: window.innerWidth <= 768 ? '32px' : '56px'\n              },\n              children: \"\\uD83D\\uDCAA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 818,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 809,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 783,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `text-3xl font-bold mb-4 ${isPassed ? 'animate-celebration animate-rainbow-glow' : 'animate-premium-pulse animate-red-glow'}`,\n          children: isPassed ? /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"animate-elegant animate-rainbow-glow\",\n            children: \"\\u2728 You Passed! \\u2728\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 832,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"animate-red-glow animate-elegant\",\n            children: \"\\uD83C\\uDF1F You Can Do It! \\uD83C\\uDF1F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 834,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 826,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mt-2 text-lg\",\n          children: [\"\\uD83D\\uDCDA \", quizName, \" - \", quizSubject]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 838,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 761,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `inline-block px-8 py-4 rounded-2xl ${isPassed ? 'bg-green-50 border-2 border-green-200' : 'bg-red-50 border-2 border-red-200'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `text-5xl font-bold mb-2 ${isPassed ? 'text-green-600' : 'text-red-600'}`,\n            children: [percentage, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 848,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-600\",\n            children: \"Your Score\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 853,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 845,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 844,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex gap-2 mb-2 justify-center items-center\",\n        style: {\n          flexWrap: 'wrap'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: '#ffffff',\n            padding: '4px 8px',\n            border: '1px solid #e5e7eb',\n            borderRadius: '6px',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '8px',\n            fontSize: '12px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '2px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: '10px'\n              },\n              children: \"\\u2705\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 880,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: '14px',\n                fontWeight: 'bold',\n                color: '#16a34a'\n              },\n              children: correctAnswers\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 881,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 879,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '2px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: '10px'\n              },\n              children: \"\\u274C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 884,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: '14px',\n                fontWeight: 'bold',\n                color: '#dc2626'\n              },\n              children: totalQuestions - correctAnswers\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 885,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 883,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 867,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: '#ffffff',\n            padding: '4px 8px',\n            border: '1px solid #e5e7eb',\n            borderRadius: '6px',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '4px',\n            fontSize: '12px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '10px'\n            },\n            children: \"\\uD83D\\uDCCA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 902,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '14px',\n              fontWeight: 'bold',\n              color: isPassed ? '#16a34a' : '#dc2626'\n            },\n            children: [percentage, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 903,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 890,\n          columnNumber: 11\n        }, this), timeTaken && timeTaken > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: '#ffffff',\n            padding: '4px 8px',\n            border: '1px solid #e5e7eb',\n            borderRadius: '6px',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '4px',\n            fontSize: '12px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '10px'\n            },\n            children: \"\\u23F1\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 920,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '14px',\n              fontWeight: 'bold',\n              color: '#2563eb'\n            },\n            children: [Math.floor(timeTaken / 60), \":\", (timeTaken % 60).toString().padStart(2, '0')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 921,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 908,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 862,\n        columnNumber: 9\n      }, this), xpData && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-4 mb-6 border border-purple-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbStar, {\n                className: \"w-6 h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 934,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 933,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-purple-600 font-bold text-xl\",\n                children: [\"+\", xpData.xpAwarded || 0, \" XP\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 937,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Earned\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 938,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 936,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 932,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-right\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-indigo-600\",\n              children: (((user === null || user === void 0 ? void 0 : user.totalXP) || 0) + (xpData.xpAwarded || 0)).toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 943,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-600\",\n              children: [\"Total XP \\u2022 Level \", (user === null || user === void 0 ? void 0 : user.currentLevel) || 1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 946,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 942,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 931,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 930,\n        columnNumber: 11\n      }, this), !xpData && user && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-4 mb-6 border border-indigo-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbStar, {\n                className: \"w-6 h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 958,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 957,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: \"Your Progress\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 961,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 960,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 956,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-right\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl font-bold text-indigo-600\",\n              children: (user.totalXP || 0).toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 966,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-600\",\n              children: [\"Total XP \\u2022 Level \", user.currentLevel || 1]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 969,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 965,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 955,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 954,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl p-6 mb-6 border border-blue-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(TbBrain, {\n              className: \"w-6 h-6 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 979,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 978,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: \"\\uD83D\\uDCDA Learning Summary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 981,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 977,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 976,\n        columnNumber: 9\n      }, this), resultDetails && resultDetails.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl p-6 mb-6 border border-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(TbChartBar, {\n              className: \"w-6 h-6 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 992,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 991,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: \"\\uD83D\\uDCCB Question by Question Review\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 994,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 990,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4 max-h-96 overflow-y-auto\",\n          children: resultDetails.map((detail, index) => {\n            // Debug: Log question data to see what's available\n            console.log(`Question ${index + 1} data:`, detail);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `rounded-xl overflow-hidden transition-all duration-300 hover:shadow-xl ${detail.isCorrect ? 'bg-gradient-to-r from-green-100 to-emerald-100 shadow-lg shadow-green-300' : 'bg-gradient-to-r from-red-100 to-pink-100 shadow-lg shadow-red-300'}`,\n              style: {\n                border: detail.isCorrect ? '4px solid #16a34a' // Green border for correct answers\n                : '4px solid #dc2626',\n                // Red border for wrong answers\n                boxShadow: detail.isCorrect ? '0 10px 25px rgba(34, 197, 94, 0.4), 0 0 0 2px rgba(34, 197, 94, 0.2)' : '0 10px 25px rgba(239, 68, 68, 0.4), 0 0 0 2px rgba(239, 68, 68, 0.2)'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `p-4 ${detail.isCorrect ? 'bg-green-300 border-b-4 border-green-500' : 'bg-red-300 border-b-4 border-red-500'}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `w-10 h-10 rounded-full flex items-center justify-center font-bold ${detail.isCorrect ? 'bg-green-500 text-white shadow-lg' : 'bg-red-500 text-white shadow-lg'}`,\n                    children: detail.isCorrect ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                      className: \"w-5 h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1030,\n                      columnNumber: 45\n                    }, this) : /*#__PURE__*/_jsxDEV(TbX, {\n                      className: \"w-5 h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1030,\n                      columnNumber: 79\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1025,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-bold text-gray-900 text-lg\",\n                      children: [\"Question \", index + 1]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1034,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-2 mt-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `px-3 py-1 rounded-full text-xs font-bold ${detail.isCorrect ? 'bg-green-500 text-white' : 'bg-red-500 text-white'}`,\n                        children: detail.isCorrect ? '✅ CORRECT' : '❌ WRONG'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1038,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1037,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1033,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1024,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1019,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-700 bg-white p-3 rounded-lg border\",\n                    children: detail.questionText || detail.questionName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1053,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1052,\n                  columnNumber: 21\n                }, this), (detail.questionType === 'image' || detail.questionImage || detail.image || detail.imageUrl) && (detail.questionImage || detail.image || detail.imageUrl) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `p-3 rounded-lg border-2 ${detail.isCorrect ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-2 mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-semibold text-gray-700\",\n                        children: \"\\uD83D\\uDCF7 Question Image:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1067,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `px-2 py-1 rounded-full text-xs font-bold ${detail.isCorrect ? 'bg-green-500 text-white' : 'bg-red-500 text-white'}`,\n                        children: detail.isCorrect ? '✅ CORRECT' : '❌ WRONG'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1068,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1066,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white p-2 rounded-lg border\",\n                      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                        src: detail.questionImage || detail.image || detail.imageUrl,\n                        alt: \"Question Image\",\n                        className: \"max-w-full h-auto rounded-lg shadow-sm mx-auto block\",\n                        style: {\n                          maxHeight: '300px'\n                        },\n                        onError: e => {\n                          e.target.style.display = 'none';\n                          e.target.nextSibling.style.display = 'block';\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1077,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-center text-gray-500 text-sm p-4 bg-gray-100 rounded-lg\",\n                        style: {\n                          display: 'none'\n                        },\n                        children: \"\\uD83D\\uDCF7 Image could not be loaded\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1087,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1076,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1061,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1060,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `p-4 rounded-lg ${detail.isCorrect ? 'bg-green-50' : 'bg-red-50'}`,\n                    style: {\n                      border: detail.isCorrect ? '3px solid #16a34a' // Green border for correct answers\n                      : '3px solid #dc2626' // Red border for wrong answers\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-3 mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `w-6 h-6 rounded-full flex items-center justify-center ${detail.isCorrect ? 'bg-green-500' : 'bg-red-500'}`,\n                        children: detail.isCorrect ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                          className: \"w-4 h-4 text-white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1117,\n                          columnNumber: 31\n                        }, this) : /*#__PURE__*/_jsxDEV(TbX, {\n                          className: \"w-4 h-4 text-white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1119,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1113,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-semibold text-gray-700\",\n                        children: \"Your Answer:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1122,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1112,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `p-3 rounded-lg font-bold text-lg ${detail.isCorrect ? 'bg-green-100 text-green-700 border border-green-200' : 'bg-red-100 text-red-700 border border-red-200'}`,\n                      children: detail.userAnswer || 'No answer provided'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1124,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1100,\n                    columnNumber: 23\n                  }, this), !detail.isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-green-50 p-4 rounded-lg border-4 border-green-500\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-3 mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-6 h-6 rounded-full bg-green-500 flex items-center justify-center\",\n                        children: /*#__PURE__*/_jsxDEV(TbCheck, {\n                          className: \"w-4 h-4 text-white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1137,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1136,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-semibold text-gray-700\",\n                        children: \"Correct Answer:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1139,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1135,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-green-100 p-3 rounded-lg border-2 border-green-400 font-bold text-lg text-green-700\",\n                      children: detail.correctAnswer\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1141,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1134,\n                    columnNumber: 25\n                  }, this), !detail.isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: `w-full font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 ${loadingExplanations[`question_${index}`] ? 'bg-gray-400 cursor-not-allowed' : 'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl'}`,\n                      onClick: () => fetchExplanation(index, detail),\n                      disabled: loadingExplanations[`question_${index}`],\n                      children: loadingExplanations[`question_${index}`] ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1161,\n                          columnNumber: 33\n                        }, this), \"Getting Explanation...\"]\n                      }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                          className: \"w-5 h-5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1166,\n                          columnNumber: 33\n                        }, this), \"Get Explanation\"]\n                      }, void 0, true)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1150,\n                      columnNumber: 27\n                    }, this), explanations[`question_${index}`] && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-3 p-4 bg-gradient-to-r from-blue-50 to-purple-50 border-2 border-blue-200 rounded-lg\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center gap-2 mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                          className: \"w-5 h-5 text-blue-600\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1176,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"font-bold text-blue-800\",\n                          children: \"\\uD83D\\uDCA1 Explanation:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1177,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1175,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-blue-700 leading-relaxed\",\n                        children: /*#__PURE__*/_jsxDEV(ContentRenderer, {\n                          text: explanations[`question_${index}`]\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1180,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1179,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1174,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1149,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1099,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1051,\n                columnNumber: 19\n              }, this)]\n            }, detail.questionId || index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1002,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 997,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 989,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex gap-4\",\n        style: {\n          flexDirection: window.innerWidth <= 768 ? 'column' : 'row'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: e => {\n            e.preventDefault();\n            console.log('🔥 More Quizzes button clicked!');\n            handleBackToQuizzes();\n          },\n          className: \"flex-1 flex items-center justify-center gap-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all font-medium cursor-pointer\",\n          style: {\n            padding: window.innerWidth <= 768 ? '12px 16px' : '12px 24px',\n            fontSize: window.innerWidth <= 768 ? '14px' : '16px'\n          },\n          type: \"button\",\n          children: [/*#__PURE__*/_jsxDEV(TbHome, {\n            style: {\n              width: window.innerWidth <= 768 ? '16px' : '20px',\n              height: window.innerWidth <= 768 ? '16px' : '20px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1217,\n            columnNumber: 13\n          }, this), \"More Quizzes\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: e => {\n            e.preventDefault();\n            console.log('🔥 Retake Quiz button clicked!');\n            handleRetakeQuiz();\n          },\n          className: \"flex-1 flex items-center justify-center gap-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all font-medium cursor-pointer\",\n          style: {\n            padding: window.innerWidth <= 768 ? '12px 16px' : '12px 24px',\n            fontSize: window.innerWidth <= 768 ? '14px' : '16px'\n          },\n          type: \"button\",\n          children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n            style: {\n              width: window.innerWidth <= 768 ? '16px' : '20px',\n              height: window.innerWidth <= 768 ? '16px' : '20px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1239,\n            columnNumber: 13\n          }, this), \"Retake Quiz\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1226,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1198,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 742,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 309,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizResult, \"IXJPZQXsegeWsTWwh0bJgPrhbsE=\", false, function () {\n  return [useNavigate, useLocation, useParams, useSelector, useLanguage];\n});\n_c = QuizResult;\nexport default QuizResult;\nvar _c;\n$RefreshReg$(_c, \"QuizResult\");", "map": {"version": 3, "names": ["React", "startTransition", "useEffect", "useState", "useNavigate", "useLocation", "useParams", "useSelector", "TbTrophy", "TbCheck", "TbX", "TbHome", "TbStar", "TbBrain", "TbChartBar", "chatWithChatGPTToExplainAns", "useLanguage", "Content<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "QuizResult", "_s", "navigate", "location", "id", "user", "state", "isKiswahili", "resultData", "percentage", "correctAnswers", "totalQuestions", "timeTaken", "resultDetails", "xpData", "quizName", "quizSubject", "passingPercentage", "verdict", "isPassed", "confetti", "set<PERSON>on<PERSON>tti", "explanations", "setExplanations", "loadingExplanations", "setLoadingExplanations", "isFlashing", "setIsFlashing", "playSound", "setTimeout", "audioContext", "window", "AudioContext", "webkitAudioContext", "playTone", "frequency", "startTime", "duration", "type", "oscillator", "createOscillator", "gainNode", "createGain", "connect", "destination", "setValueAtTime", "gain", "linearRampToValueAtTime", "exponentialRampToValueAtTime", "start", "stop", "createClap", "noise", "createBufferSource", "buffer", "createBuffer", "sampleRate", "data", "getChannelData", "i", "length", "Math", "random", "filter", "createBiquadFilter", "now", "currentTime", "error", "console", "log", "premiumConfetti", "colors", "push", "left", "delay", "color", "floor", "shape", "size", "randomX", "motivationalElements", "motivationalEmojis", "emoji", "isMotivational", "fetchExplanation", "questionIndex", "detail", "<PERSON><PERSON><PERSON>", "prev", "response", "question", "questionText", "questionName", "expectedAnswer", "<PERSON><PERSON><PERSON><PERSON>", "userAnswer", "imageUrl", "questionImage", "image", "language", "success", "explanation", "handleBackToQuizzes", "handleRetakeQuiz", "className", "style", "padding", "innerWidth", "children", "map", "piece", "top", "fontSize", "animation", "zIndex", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "height", "background", "borderRadius", "boxShadow", "backgroundColor", "clipPath", "border", "jsx", "max<PERSON><PERSON><PERSON>", "marginBottom", "gap", "flexWrap", "display", "alignItems", "fontWeight", "toString", "padStart", "xpAwarded", "totalXP", "toLocaleString", "currentLevel", "index", "isCorrect", "questionType", "src", "alt", "maxHeight", "onError", "e", "target", "nextS<PERSON>ling", "onClick", "disabled", "text", "questionId", "flexDirection", "preventDefault", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizResult.js"], "sourcesContent": ["import React, { startTransition, useEffect, useState } from 'react';\nimport { useNavigate, useLocation, useParams } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { TbTrophy, TbCheck, TbX, TbHome, TbStar, TbBrain, TbChartBar } from 'react-icons/tb';\nimport { chatWithChatGPTToExplainAns } from '../../../apicalls/chat';\nimport { useLanguage } from '../../../contexts/LanguageContext';\nimport ContentRenderer from '../../../components/ContentRenderer';\n\nconst QuizResult = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { id } = useParams();\n  const { user } = useSelector((state) => state.user);\n  const { isKiswahili } = useLanguage();\n  // Get result data from navigation state first\n  const resultData = location.state || {\n    percentage: 0,\n    correctAnswers: 0,\n    totalQuestions: 0,\n    timeTaken: 0,\n    resultDetails: [],\n    xpData: null,\n    quizName: 'Quiz',\n    quizSubject: 'General',\n    passingPercentage: 60,\n    verdict: 'Fail'\n  };\n\n  const {\n    percentage,\n    correctAnswers,\n    totalQuestions,\n    timeTaken,\n    xpData,\n    quizName,\n    quizSubject,\n    passingPercentage,\n    verdict,\n    resultDetails\n  } = resultData;\n  const isPassed = verdict === 'Pass' || percentage >= (passingPercentage || 60);\n\n  const [confetti, setConfetti] = useState([]);\n  const [explanations, setExplanations] = useState({});\n  const [loadingExplanations, setLoadingExplanations] = useState({});\n  const [isFlashing, setIsFlashing] = useState(false);\n\n  // Play sound and trigger animations when component loads\n  useEffect(() => {\n\n    // Play enhanced sound effects and trigger flash animation\n    const playSound = () => {\n      // Trigger premium flash animation\n      setIsFlashing(true);\n      setTimeout(() => setIsFlashing(false), 3200); // Flash for 3.2 seconds (2 cycles of 0.8s each)\n\n      try {\n        if (isPassed) {\n          // Success sound with clapping\n          const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n\n          // Create a success melody\n          const playTone = (frequency, startTime, duration, type = 'sine') => {\n            const oscillator = audioContext.createOscillator();\n            const gainNode = audioContext.createGain();\n\n            oscillator.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n\n            oscillator.frequency.setValueAtTime(frequency, startTime);\n            oscillator.type = type;\n\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(0.1, startTime + 0.01);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + duration);\n\n            oscillator.start(startTime);\n            oscillator.stop(startTime + duration);\n          };\n\n          // Create clapping sound effect\n          const createClap = (startTime) => {\n            const noise = audioContext.createBufferSource();\n            const buffer = audioContext.createBuffer(1, audioContext.sampleRate * 0.1, audioContext.sampleRate);\n            const data = buffer.getChannelData(0);\n\n            // Generate white noise for clap\n            for (let i = 0; i < data.length; i++) {\n              data[i] = Math.random() * 2 - 1;\n            }\n\n            noise.buffer = buffer;\n\n            const filter = audioContext.createBiquadFilter();\n            filter.type = 'highpass';\n            filter.frequency.setValueAtTime(1000, startTime);\n\n            const gainNode = audioContext.createGain();\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(0.3, startTime + 0.01);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + 0.1);\n\n            noise.connect(filter);\n            filter.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n\n            noise.start(startTime);\n            noise.stop(startTime + 0.1);\n          };\n\n          const now = audioContext.currentTime;\n\n          // Play success melody: C-E-G-C (major chord progression)\n          playTone(523.25, now, 0.2); // C5\n          playTone(659.25, now + 0.1, 0.2); // E5\n          playTone(783.99, now + 0.2, 0.2); // G5\n          playTone(1046.5, now + 0.3, 0.4); // C6\n\n          // Add clapping sounds\n          createClap(now + 0.5);\n          createClap(now + 0.7);\n          createClap(now + 0.9);\n          createClap(now + 1.1);\n\n        } else {\n          // Fail sound - create a gentle, encouraging tone\n          const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n\n          const playTone = (frequency, startTime, duration) => {\n            const oscillator = audioContext.createOscillator();\n            const gainNode = audioContext.createGain();\n\n            oscillator.connect(gainNode);\n            gainNode.connect(audioContext.destination);\n\n            oscillator.frequency.setValueAtTime(frequency, startTime);\n            oscillator.type = 'sine';\n\n            gainNode.gain.setValueAtTime(0, startTime);\n            gainNode.gain.linearRampToValueAtTime(0.08, startTime + 0.01);\n            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + duration);\n\n            oscillator.start(startTime);\n            oscillator.stop(startTime + duration);\n          };\n\n          // Play gentle fail tone: A-F (not harsh, encouraging)\n          const now = audioContext.currentTime;\n          playTone(440, now, 0.3); // A4\n          playTone(349.23, now + 0.2, 0.4); // F4\n        }\n      } catch (error) {\n        console.log('Audio not supported');\n      }\n    };\n\n    // Generate premium animations based on pass/fail\n    if (isPassed) {\n      // Premium confetti explosion\n      const premiumConfetti = [];\n\n      // Main confetti burst (200 pieces)\n      for (let i = 0; i < 200; i++) {\n        const colors = [\n          '#FFD700', '#FFA500', '#FF6B6B', '#4ECDC4', '#45B7D1',\n          '#96CEB4', '#FF69B4', '#32CD32', '#FF4500', '#9370DB',\n          '#00CED1', '#FF1493', '#00FF7F', '#FF8C00', '#DA70D6'\n        ];\n\n        premiumConfetti.push({\n          id: `confetti_${i}`,\n          left: 20 + Math.random() * 60, // More centered\n          delay: Math.random() * 2,\n          duration: 4 + Math.random() * 3,\n          color: colors[Math.floor(Math.random() * colors.length)],\n          shape: ['circle', 'square', 'triangle'][Math.floor(Math.random() * 3)],\n          size: 3 + Math.random() * 6,\n          type: 'confetti',\n          randomX: (Math.random() - 0.5) * 200 // For burst effect\n        });\n      }\n\n      // Premium sparkles (50 pieces)\n      for (let i = 0; i < 50; i++) {\n        premiumConfetti.push({\n          id: `sparkle_${i}`,\n          left: Math.random() * 100,\n          delay: Math.random() * 3,\n          duration: 2 + Math.random() * 2,\n          color: ['#FFD700', '#FFFFFF', '#FFF700', '#FFFF00'][Math.floor(Math.random() * 4)],\n          size: 1 + Math.random() * 3,\n          type: 'sparkle'\n        });\n      }\n\n      // Burst confetti (100 pieces from center)\n      for (let i = 0; i < 100; i++) {\n        premiumConfetti.push({\n          id: `burst_${i}`,\n          left: 45 + Math.random() * 10, // Center burst\n          delay: Math.random() * 0.5,\n          duration: 3 + Math.random() * 2,\n          color: ['#FFD700', '#FF6B6B', '#4ECDC4', '#FF69B4'][Math.floor(Math.random() * 4)],\n          shape: 'circle',\n          size: 2 + Math.random() * 4,\n          type: 'burst',\n          randomX: (Math.random() - 0.5) * 300\n        });\n      }\n\n      setConfetti(premiumConfetti);\n\n      // Remove all animations after 10 seconds\n      setTimeout(() => setConfetti([]), 10000);\n    } else {\n      // Premium motivational elements\n      const motivationalElements = [];\n      const motivationalEmojis = ['💪', '🌟', '📚', '🎯', '🚀', '💡', '⭐', '✨', '🔥', '💎'];\n\n      for (let i = 0; i < 30; i++) {\n        motivationalElements.push({\n          id: `motivate_${i}`,\n          left: Math.random() * 100,\n          delay: Math.random() * 4,\n          duration: 5 + Math.random() * 3,\n          emoji: motivationalEmojis[Math.floor(Math.random() * motivationalEmojis.length)],\n          isMotivational: true,\n          size: 2 + Math.random() * 2\n        });\n      }\n      setConfetti(motivationalElements);\n\n      // Remove motivational elements after 8 seconds\n      setTimeout(() => setConfetti([]), 8000);\n    }\n\n    playSound();\n  }, [isPassed]);\n\n\n\n  // Function to fetch explanation\n  const fetchExplanation = async (questionIndex, detail) => {\n    const questionKey = `question_${questionIndex}`;\n\n    // Don't fetch if already loading or already have explanation\n    if (loadingExplanations[questionKey] || explanations[questionKey]) {\n      return;\n    }\n\n    try {\n      setLoadingExplanations(prev => ({ ...prev, [questionKey]: true }));\n\n      const response = await chatWithChatGPTToExplainAns({\n        question: detail.questionText || detail.questionName,\n        expectedAnswer: detail.correctAnswer,\n        userAnswer: detail.userAnswer,\n        imageUrl: detail.questionImage || detail.image || detail.imageUrl || null,\n        language: isKiswahili ? 'kiswahili' : 'english'\n      });\n\n      if (response.success) {\n        setExplanations(prev => ({\n          ...prev,\n          [questionKey]: response.explanation\n        }));\n      } else {\n        console.error('Failed to fetch explanation:', response.error);\n        setExplanations(prev => ({\n          ...prev,\n          [questionKey]: 'Sorry, we could not generate an explanation at this time. Please try again later.'\n        }));\n      }\n    } catch (error) {\n      console.error('Error fetching explanation:', error);\n      setExplanations(prev => ({\n        ...prev,\n        [questionKey]: 'Sorry, we could not generate an explanation at this time. Please try again later.'\n      }));\n    } finally {\n      setLoadingExplanations(prev => ({ ...prev, [questionKey]: false }));\n    }\n  };\n\n\n\n  const handleBackToQuizzes = () => {\n    console.log('🏠 Navigating to quiz listing...');\n    startTransition(() => {\n      navigate('/user/quiz');\n    });\n  };\n\n  const handleRetakeQuiz = () => {\n    console.log('🔄 Retaking quiz with ID:', id);\n    if (id) {\n      startTransition(() => {\n        navigate(`/quiz/${id}/play`);\n      });\n    } else {\n      console.log('❌ No quiz ID available, going to quiz listing');\n      startTransition(() => {\n        navigate('/user/quiz');\n      });\n    }\n  };\n\n  return (\n    <div className={`min-h-screen flex items-center justify-center relative overflow-hidden ${\n      isPassed\n        ? 'bg-gradient-to-br from-green-50 via-emerald-50 to-teal-100'\n        : 'bg-gradient-to-br from-red-50 via-pink-50 to-orange-100'\n    } ${isFlashing ? (isPassed ? 'flash-green' : 'flash-red') : ''}`}\n    style={{\n      padding: window.innerWidth <= 768 ? '8px' : window.innerWidth <= 1024 ? '16px' : '24px'\n    }}>\n\n      {/* Premium Confetti System */}\n      {confetti.map((piece) => {\n        if (piece.isMotivational) {\n          return (\n            <div\n              key={piece.id}\n              className=\"absolute opacity-90\"\n              style={{\n                left: `${piece.left}%`,\n                top: `${20 + Math.random() * 60}%`,\n                fontSize: `${piece.size || 2}rem`,\n                animation: `heart-beat ${piece.duration}s ease-in-out ${piece.delay}s infinite`,\n                zIndex: 100\n              }}\n            >\n              {piece.emoji}\n            </div>\n          );\n        }\n\n        if (piece.type === 'sparkle') {\n          return (\n            <div\n              key={piece.id}\n              className=\"absolute\"\n              style={{\n                left: `${piece.left}%`,\n                width: `${piece.size}px`,\n                height: `${piece.size}px`,\n                background: `radial-gradient(circle, ${piece.color}, transparent)`,\n                borderRadius: '50%',\n                animation: `premium-sparkle ${piece.duration}s ease-in-out ${piece.delay}s infinite`,\n                top: `${Math.random() * 100}%`,\n                boxShadow: `0 0 ${piece.size * 2}px ${piece.color}`,\n                zIndex: 100\n              }}\n            />\n          );\n        }\n\n        if (piece.type === 'burst') {\n          return (\n            <div\n              key={piece.id}\n              className=\"absolute opacity-90\"\n              style={{\n                left: `${piece.left}%`,\n                width: `${piece.size}px`,\n                height: `${piece.size}px`,\n                backgroundColor: piece.color,\n                borderRadius: piece.shape === 'circle' ? '50%' : piece.shape === 'triangle' ? '0' : '0%',\n                clipPath: piece.shape === 'triangle' ? 'polygon(50% 0%, 0% 100%, 100% 100%)' : 'none',\n                animation: `confetti-burst ${piece.duration}s ease-out ${piece.delay}s forwards`,\n                top: '40%',\n                '--random-x': `${piece.randomX}px`,\n                boxShadow: `0 0 ${piece.size}px ${piece.color}40`,\n                zIndex: 100\n              }}\n            />\n          );\n        }\n\n        // Regular premium confetti\n        return (\n          <div\n            key={piece.id}\n            className=\"absolute opacity-90\"\n            style={{\n              left: `${piece.left}%`,\n              width: `${piece.size}px`,\n              height: `${piece.size}px`,\n              backgroundColor: piece.color,\n              borderRadius: piece.shape === 'circle' ? '50%' : piece.shape === 'triangle' ? '0' : '0%',\n              clipPath: piece.shape === 'triangle' ? 'polygon(50% 0%, 0% 100%, 100% 100%)' : 'none',\n              animation: `confetti-fall ${piece.duration}s ease-out ${piece.delay}s forwards`,\n              top: '-20px',\n              boxShadow: `0 0 ${piece.size}px ${piece.color}60`,\n              border: `1px solid ${piece.color}`,\n              background: `linear-gradient(45deg, ${piece.color}, ${piece.color}80)`,\n              zIndex: 100\n            }}\n          />\n        );\n      })}\n\n      {/* CSS Animations */}\n      <style jsx>{`\n        @keyframes confetti-fall {\n          0% {\n            transform: translateY(-20px) rotate(0deg) scale(0);\n            opacity: 0;\n          }\n          10% {\n            opacity: 1;\n            transform: translateY(0px) rotate(36deg) scale(1);\n          }\n          100% {\n            transform: translateY(100vh) rotate(1080deg) scale(0.3);\n            opacity: 0;\n          }\n        }\n\n        @keyframes confetti-burst {\n          0% {\n            transform: translateY(-10px) translateX(0px) rotate(0deg) scale(0);\n            opacity: 0;\n          }\n          15% {\n            transform: translateY(-50px) translateX(var(--random-x)) rotate(180deg) scale(1.2);\n            opacity: 1;\n          }\n          100% {\n            transform: translateY(100vh) translateX(calc(var(--random-x) * 2)) rotate(1440deg) scale(0);\n            opacity: 0;\n          }\n        }\n\n        @keyframes premium-sparkle {\n          0%, 100% {\n            transform: scale(0) rotate(0deg);\n            opacity: 0;\n          }\n          50% {\n            transform: scale(1.5) rotate(180deg);\n            opacity: 1;\n          }\n        }\n\n\n\n        @keyframes heart-beat {\n          0%, 100% { transform: scale(1); }\n          50% { transform: scale(1.3); }\n        }\n\n        @keyframes text-bounce {\n          0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }\n          40% { transform: translateY(-20px) scale(1.1); }\n          60% { transform: translateY(-10px) scale(1.05); }\n        }\n\n        @keyframes text-glow {\n          0%, 100% {\n            text-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor;\n            transform: scale(1);\n          }\n          50% {\n            text-shadow: 0 0 20px currentColor, 0 0 40px currentColor, 0 0 60px currentColor;\n            transform: scale(1.05);\n          }\n        }\n\n        @keyframes rainbow-text {\n          0% {\n            color: #FF6B6B;\n            text-shadow: 0 0 20px #FF6B6B, 0 0 40px #FF6B6B;\n          }\n          16% {\n            color: #FFD93D;\n            text-shadow: 0 0 20px #FFD93D, 0 0 40px #FFD93D;\n          }\n          33% {\n            color: #6BCF7F;\n            text-shadow: 0 0 20px #6BCF7F, 0 0 40px #6BCF7F;\n          }\n          50% {\n            color: #4D96FF;\n            text-shadow: 0 0 20px #4D96FF, 0 0 40px #4D96FF;\n          }\n          66% {\n            color: #9B59B6;\n            text-shadow: 0 0 20px #9B59B6, 0 0 40px #9B59B6;\n          }\n          83% {\n            color: #FF69B4;\n            text-shadow: 0 0 20px #FF69B4, 0 0 40px #FF69B4;\n          }\n          100% {\n            color: #FF6B6B;\n            text-shadow: 0 0 20px #FF6B6B, 0 0 40px #FF6B6B;\n          }\n        }\n\n        @keyframes shake-celebrate {\n          0%, 100% { transform: translateX(0) rotate(0deg); }\n          10% { transform: translateX(-5px) rotate(-1deg); }\n          20% { transform: translateX(5px) rotate(1deg); }\n          30% { transform: translateX(-5px) rotate(-1deg); }\n          40% { transform: translateX(5px) rotate(1deg); }\n          50% { transform: translateX(-3px) rotate(-0.5deg); }\n          60% { transform: translateX(3px) rotate(0.5deg); }\n          70% { transform: translateX(-2px) rotate(-0.5deg); }\n          80% { transform: translateX(2px) rotate(0.5deg); }\n          90% { transform: translateX(-1px) rotate(0deg); }\n        }\n\n        @keyframes zoom-in-out {\n          0%, 100% { transform: scale(1); }\n          50% { transform: scale(1.2); }\n        }\n\n        .animate-text-bounce {\n          animation: text-bounce 2s ease-in-out infinite;\n        }\n\n        .animate-text-glow {\n          animation: text-glow 2s ease-in-out infinite;\n        }\n\n        .animate-rainbow {\n          animation: rainbow-text 3s linear infinite;\n        }\n\n        .animate-shake-celebrate {\n          animation: shake-celebrate 1s ease-in-out infinite;\n        }\n\n        .animate-zoom {\n          animation: zoom-in-out 2s ease-in-out infinite;\n        }\n\n        @keyframes elegant-scale {\n          0%, 100% {\n            transform: scale(1) rotateY(0deg);\n            text-shadow: 0 0 20px currentColor;\n          }\n          50% {\n            transform: scale(1.1) rotateY(5deg);\n            text-shadow: 0 0 40px currentColor, 0 0 60px currentColor;\n          }\n        }\n\n        @keyframes premium-pulse {\n          0%, 100% {\n            transform: scale(1);\n            opacity: 1;\n          }\n          50% {\n            transform: scale(1.05);\n            opacity: 0.9;\n          }\n        }\n\n        @keyframes smooth-glow {\n          0%, 100% {\n            text-shadow: 0 0 15px currentColor, 0 0 30px currentColor, 0 0 45px currentColor;\n            filter: brightness(1) saturate(1.2);\n          }\n          50% {\n            text-shadow: 0 0 40px currentColor, 0 0 60px currentColor, 0 0 80px currentColor, 0 0 100px currentColor;\n            filter: brightness(1.3) saturate(1.5);\n          }\n        }\n\n        @keyframes rainbow-glow {\n          0% {\n            color: #FF6B6B;\n            text-shadow: 0 0 20px #FF6B6B, 0 0 40px #FF6B6B, 0 0 60px #FF6B6B;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          16% {\n            color: #FFD93D;\n            text-shadow: 0 0 20px #FFD93D, 0 0 40px #FFD93D, 0 0 60px #FFD93D;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          33% {\n            color: #6BCF7F;\n            text-shadow: 0 0 20px #6BCF7F, 0 0 40px #6BCF7F, 0 0 60px #6BCF7F;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          50% {\n            color: #4D96FF;\n            text-shadow: 0 0 20px #4D96FF, 0 0 40px #4D96FF, 0 0 60px #4D96FF;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          66% {\n            color: #9B59B6;\n            text-shadow: 0 0 20px #9B59B6, 0 0 40px #9B59B6, 0 0 60px #9B59B6;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          83% {\n            color: #FF69B4;\n            text-shadow: 0 0 20px #FF69B4, 0 0 40px #FF69B4, 0 0 60px #FF69B4;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          100% {\n            color: #FF6B6B;\n            text-shadow: 0 0 20px #FF6B6B, 0 0 40px #FF6B6B, 0 0 60px #FF6B6B;\n            filter: brightness(1.2) saturate(1.3);\n          }\n        }\n\n        @keyframes celebration-bounce {\n          0%, 20%, 50%, 80%, 100% {\n            transform: translateY(0) scale(1);\n          }\n          40% {\n            transform: translateY(-15px) scale(1.05);\n          }\n          60% {\n            transform: translateY(-8px) scale(1.02);\n          }\n        }\n\n        .animate-elegant {\n          animation: elegant-scale 3s ease-in-out infinite;\n        }\n\n        .animate-premium-pulse {\n          animation: premium-pulse 2s ease-in-out infinite;\n        }\n\n        .animate-smooth-glow {\n          animation: smooth-glow 2.5s ease-in-out infinite;\n        }\n\n        .animate-celebration {\n          animation: celebration-bounce 2s ease-in-out infinite;\n        }\n\n        .animate-rainbow {\n          animation: rainbow-text 3s linear infinite;\n        }\n\n        .animate-rainbow-glow {\n          animation: rainbow-glow 3s linear infinite;\n        }\n\n        @keyframes red-glow {\n          0%, 100% {\n            color: #EF4444;\n            text-shadow: 0 0 20px #EF4444, 0 0 40px #EF4444, 0 0 60px #EF4444;\n            filter: brightness(1.2) saturate(1.3);\n          }\n          25% {\n            color: #DC2626;\n            text-shadow: 0 0 25px #DC2626, 0 0 50px #DC2626, 0 0 75px #DC2626;\n            filter: brightness(1.3) saturate(1.4);\n          }\n          50% {\n            color: #B91C1C;\n            text-shadow: 0 0 30px #B91C1C, 0 0 60px #B91C1C, 0 0 90px #B91C1C;\n            filter: brightness(1.4) saturate(1.5);\n          }\n          75% {\n            color: #DC2626;\n            text-shadow: 0 0 25px #DC2626, 0 0 50px #DC2626, 0 0 75px #DC2626;\n            filter: brightness(1.3) saturate(1.4);\n          }\n        }\n\n        .animate-red-glow {\n          animation: red-glow 2.5s ease-in-out infinite;\n        }\n\n        @keyframes premium-green-flash {\n          0% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n          25% {\n            background: linear-gradient(45deg, rgba(34, 197, 94, 0.1), rgba(16, 185, 129, 0.1));\n            box-shadow: inset 0 0 100px rgba(34, 197, 94, 0.2);\n          }\n          50% {\n            background: linear-gradient(45deg, rgba(34, 197, 94, 0.4), rgba(16, 185, 129, 0.4));\n            box-shadow: inset 0 0 200px rgba(34, 197, 94, 0.5), 0 0 50px rgba(34, 197, 94, 0.3);\n          }\n          75% {\n            background: linear-gradient(45deg, rgba(34, 197, 94, 0.2), rgba(16, 185, 129, 0.2));\n            box-shadow: inset 0 0 150px rgba(34, 197, 94, 0.3);\n          }\n          100% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n        }\n\n        @keyframes premium-red-flash {\n          0% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n          25% {\n            background: linear-gradient(45deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 127, 0.1));\n            box-shadow: inset 0 0 100px rgba(239, 68, 68, 0.2);\n          }\n          50% {\n            background: linear-gradient(45deg, rgba(239, 68, 68, 0.4), rgba(220, 38, 127, 0.4));\n            box-shadow: inset 0 0 200px rgba(239, 68, 68, 0.5), 0 0 50px rgba(239, 68, 68, 0.3);\n          }\n          75% {\n            background: linear-gradient(45deg, rgba(239, 68, 68, 0.2), rgba(220, 38, 127, 0.2));\n            box-shadow: inset 0 0 150px rgba(239, 68, 68, 0.3);\n          }\n          100% {\n            background: linear-gradient(45deg, transparent, transparent);\n            box-shadow: none;\n          }\n        }\n\n        .flash-green {\n          animation: premium-green-flash 0.8s ease-in-out 2;\n        }\n\n        .flash-red {\n          animation: premium-red-flash 0.8s ease-in-out 2;\n        }\n      `}</style>\n\n      {/* Premium Overlay Effect */}\n      {isFlashing && (\n        <div\n          className=\"fixed inset-0 pointer-events-none\"\n          style={{\n            background: isPassed\n              ? 'radial-gradient(circle at center, rgba(34, 197, 94, 0.3) 0%, rgba(16, 185, 129, 0.2) 50%, transparent 100%)'\n              : 'radial-gradient(circle at center, rgba(239, 68, 68, 0.3) 0%, rgba(220, 38, 127, 0.2) 50%, transparent 100%)',\n            animation: isPassed ? 'premium-green-flash 0.8s ease-in-out infinite' : 'premium-red-flash 0.8s ease-in-out infinite',\n            zIndex: 5\n          }}\n        />\n      )}\n\n      <div className={`bg-white rounded-2xl shadow-2xl border-2 w-full relative ${\n        isPassed ? 'border-green-400 shadow-green-200' : 'border-red-400 shadow-red-200'\n      } ${isFlashing ? 'shadow-3xl' : ''}`}\n      style={{\n        background: isFlashing\n          ? (isPassed\n              ? 'linear-gradient(135deg, rgba(255,255,255,0.95), rgba(34, 197, 94, 0.05))'\n              : 'linear-gradient(135deg, rgba(255,255,255,0.95), rgba(239, 68, 68, 0.05))')\n          : 'white',\n        boxShadow: isFlashing\n          ? (isPassed\n              ? '0 25px 50px rgba(34, 197, 94, 0.3), 0 0 100px rgba(34, 197, 94, 0.2)'\n              : '0 25px 50px rgba(239, 68, 68, 0.3), 0 0 100px rgba(239, 68, 68, 0.2)')\n          : '0 25px 50px rgba(0,0,0,0.15)',\n        zIndex: 10,\n        padding: window.innerWidth <= 768 ? '16px' : window.innerWidth <= 1024 ? '24px' : '32px',\n        maxWidth: window.innerWidth <= 768 ? '100%' : window.innerWidth <= 1024 ? '90%' : '800px'\n      }}>\n        {/* Header */}\n        <div\n          className=\"text-center\"\n          style={{ marginBottom: window.innerWidth <= 768 ? '16px' : '32px' }}\n        >\n          <div\n            className={`inline-flex items-center justify-center rounded-full mb-4 relative ${\n              isPassed ? 'bg-gradient-to-br from-green-100 to-emerald-100' : 'bg-gradient-to-br from-red-100 to-pink-100'\n            }`}\n            style={{\n              width: window.innerWidth <= 768 ? '60px' : '96px',\n              height: window.innerWidth <= 768 ? '60px' : '96px'\n            }}\n          >\n            <TbTrophy\n              className={`${isPassed ? 'text-yellow-500' : 'text-gray-500'}`}\n              style={{\n                width: window.innerWidth <= 768 ? '30px' : '48px',\n                height: window.innerWidth <= 768 ? '30px' : '48px'\n              }}\n            />\n          </div>\n\n          <h1\n            className={`font-bold mb-4 ${\n              isPassed\n                ? 'text-green-600 animate-elegant animate-smooth-glow'\n                : 'animate-premium-pulse animate-red-glow'\n            }`}\n            style={{\n              fontSize: window.innerWidth <= 768 ? '24px' : window.innerWidth <= 1024 ? '36px' : '48px'\n            }}\n          >\n            {isPassed ? (\n              <span\n                className=\"flex items-center justify-center\"\n                style={{ gap: window.innerWidth <= 768 ? '8px' : '16px', flexWrap: 'wrap' }}\n              >\n                <span\n                  className=\"animate-celebration\"\n                  style={{ fontSize: window.innerWidth <= 768 ? '32px' : '56px' }}\n                >🎉</span>\n                <span className=\"animate-rainbow-glow animate-elegant\">Congratulations!</span>\n                <span\n                  className=\"animate-celebration\"\n                  style={{ fontSize: window.innerWidth <= 768 ? '32px' : '56px' }}\n                >🎉</span>\n              </span>\n            ) : (\n              <span\n                className=\"flex items-center justify-center\"\n                style={{ gap: window.innerWidth <= 768 ? '8px' : '16px', flexWrap: 'wrap' }}\n              >\n                <span\n                  className=\"animate-premium-pulse\"\n                  style={{ fontSize: window.innerWidth <= 768 ? '32px' : '56px' }}\n                >💪</span>\n                <span className=\"animate-red-glow animate-elegant\">Keep Going!</span>\n                <span\n                  className=\"animate-premium-pulse\"\n                  style={{ fontSize: window.innerWidth <= 768 ? '32px' : '56px' }}\n                >💪</span>\n              </span>\n            )}\n          </h1>\n\n          <div className={`text-3xl font-bold mb-4 ${\n            isPassed\n              ? 'animate-celebration animate-rainbow-glow'\n              : 'animate-premium-pulse animate-red-glow'\n          }`}>\n            {isPassed ? (\n              <span className=\"animate-elegant animate-rainbow-glow\">✨ You Passed! ✨</span>\n            ) : (\n              <span className=\"animate-red-glow animate-elegant\">🌟 You Can Do It! 🌟</span>\n            )}\n          </div>\n\n          <p className=\"text-gray-600 mt-2 text-lg\">\n            📚 {quizName} - {quizSubject}\n          </p>\n        </div>\n\n        {/* Score Display */}\n        <div className=\"text-center mb-8\">\n          <div className={`inline-block px-8 py-4 rounded-2xl ${\n            isPassed ? 'bg-green-50 border-2 border-green-200' : 'bg-red-50 border-2 border-red-200'\n          }`}>\n            <div className={`text-5xl font-bold mb-2 ${\n              isPassed ? 'text-green-600' : 'text-red-600'\n            }`}>\n              {percentage}%\n            </div>\n            <div className=\"text-gray-600\">\n              Your Score\n            </div>\n          </div>\n        </div>\n\n\n\n        {/* Horizontal Compact Results */}\n        <div\n          className=\"flex gap-2 mb-2 justify-center items-center\"\n          style={{ flexWrap: 'wrap' }}\n        >\n          {/* Correct and Wrong - Horizontal */}\n          <div\n            style={{\n              backgroundColor: '#ffffff',\n              padding: '4px 8px',\n              border: '1px solid #e5e7eb',\n              borderRadius: '6px',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '8px',\n              fontSize: '12px'\n            }}\n          >\n            <div style={{ display: 'flex', alignItems: 'center', gap: '2px' }}>\n              <span style={{ fontSize: '10px' }}>✅</span>\n              <span style={{ fontSize: '14px', fontWeight: 'bold', color: '#16a34a' }}>{correctAnswers}</span>\n            </div>\n            <div style={{ display: 'flex', alignItems: 'center', gap: '2px' }}>\n              <span style={{ fontSize: '10px' }}>❌</span>\n              <span style={{ fontSize: '14px', fontWeight: 'bold', color: '#dc2626' }}>{totalQuestions - correctAnswers}</span>\n            </div>\n          </div>\n\n          {/* Score */}\n          <div\n            style={{\n              backgroundColor: '#ffffff',\n              padding: '4px 8px',\n              border: '1px solid #e5e7eb',\n              borderRadius: '6px',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '4px',\n              fontSize: '12px'\n            }}\n          >\n            <span style={{ fontSize: '10px' }}>📊</span>\n            <span style={{ fontSize: '14px', fontWeight: 'bold', color: isPassed ? '#16a34a' : '#dc2626' }}>{percentage}%</span>\n          </div>\n\n          {/* Time - Only if available */}\n          {timeTaken && timeTaken > 0 && (\n            <div\n              style={{\n                backgroundColor: '#ffffff',\n                padding: '4px 8px',\n                border: '1px solid #e5e7eb',\n                borderRadius: '6px',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '4px',\n                fontSize: '12px'\n              }}\n            >\n              <span style={{ fontSize: '10px' }}>⏱️</span>\n              <span style={{ fontSize: '14px', fontWeight: 'bold', color: '#2563eb' }}>\n                {Math.floor(timeTaken / 60)}:{(timeTaken % 60).toString().padStart(2, '0')}\n              </span>\n            </div>\n          )}\n        </div>\n\n        {/* Streamlined XP Section */}\n        {xpData && (\n          <div className=\"bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-4 mb-6 border border-purple-200\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center\">\n                  <TbStar className=\"w-6 h-6 text-white\" />\n                </div>\n                <div>\n                  <div className=\"text-purple-600 font-bold text-xl\">+{xpData.xpAwarded || 0} XP</div>\n                  <div className=\"text-sm text-gray-600\">Earned</div>\n                </div>\n              </div>\n\n              <div className=\"text-right\">\n                <div className=\"text-2xl font-bold text-indigo-600\">\n                  {((user?.totalXP || 0) + (xpData.xpAwarded || 0)).toLocaleString()}\n                </div>\n                <div className=\"text-sm text-gray-600\">Total XP • Level {user?.currentLevel || 1}</div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Streamlined XP Section (for users without XP data) */}\n        {!xpData && user && (\n          <div className=\"bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-4 mb-6 border border-indigo-200\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center\">\n                  <TbStar className=\"w-6 h-6 text-white\" />\n                </div>\n                <div>\n                  <div className=\"text-sm text-gray-600\">Your Progress</div>\n                </div>\n              </div>\n\n              <div className=\"text-right\">\n                <div className=\"text-2xl font-bold text-indigo-600\">\n                  {(user.totalXP || 0).toLocaleString()}\n                </div>\n                <div className=\"text-sm text-gray-600\">Total XP • Level {user.currentLevel || 1}</div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Learning Summary Section */}\n        <div className=\"bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl p-6 mb-6 border border-blue-200\">\n          <div className=\"flex items-center gap-3 mb-4\">\n            <div className=\"w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center\">\n              <TbBrain className=\"w-6 h-6 text-white\" />\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900\">📚 Learning Summary</h3>\n          </div>\n\n          {/* Simplified Learning Summary - Only Question Review */}\n        </div>\n\n        {/* Detailed Question Breakdown */}\n        {resultDetails && resultDetails.length > 0 && (\n          <div className=\"bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl p-6 mb-6 border border-gray-200\">\n            <div className=\"flex items-center gap-3 mb-4\">\n              <div className=\"w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center\">\n                <TbChartBar className=\"w-6 h-6 text-white\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900\">📋 Question by Question Review</h3>\n            </div>\n\n            <div className=\"space-y-4 max-h-96 overflow-y-auto\">\n              {resultDetails.map((detail, index) => {\n                // Debug: Log question data to see what's available\n                console.log(`Question ${index + 1} data:`, detail);\n                return (\n                <div\n                  key={detail.questionId || index}\n                  className={`rounded-xl overflow-hidden transition-all duration-300 hover:shadow-xl ${\n                    detail.isCorrect\n                      ? 'bg-gradient-to-r from-green-100 to-emerald-100 shadow-lg shadow-green-300'\n                      : 'bg-gradient-to-r from-red-100 to-pink-100 shadow-lg shadow-red-300'\n                  }`}\n                  style={{\n                    border: detail.isCorrect\n                      ? '4px solid #16a34a' // Green border for correct answers\n                      : '4px solid #dc2626', // Red border for wrong answers\n                    boxShadow: detail.isCorrect\n                      ? '0 10px 25px rgba(34, 197, 94, 0.4), 0 0 0 2px rgba(34, 197, 94, 0.2)'\n                      : '0 10px 25px rgba(239, 68, 68, 0.4), 0 0 0 2px rgba(239, 68, 68, 0.2)'\n                  }}\n                >\n                  {/* Question Header */}\n                  <div className={`p-4 ${\n                    detail.isCorrect\n                      ? 'bg-green-300 border-b-4 border-green-500'\n                      : 'bg-red-300 border-b-4 border-red-500'\n                  }`}>\n                    <div className=\"flex items-center gap-3\">\n                      <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold ${\n                        detail.isCorrect\n                          ? 'bg-green-500 text-white shadow-lg'\n                          : 'bg-red-500 text-white shadow-lg'\n                      }`}>\n                        {detail.isCorrect ? <TbCheck className=\"w-5 h-5\" /> : <TbX className=\"w-5 h-5\" />}\n                      </div>\n\n                      <div className=\"flex-1\">\n                        <h4 className=\"font-bold text-gray-900 text-lg\">\n                          Question {index + 1}\n                        </h4>\n                        <div className=\"flex items-center gap-2 mt-1\">\n                          <span className={`px-3 py-1 rounded-full text-xs font-bold ${\n                            detail.isCorrect\n                              ? 'bg-green-500 text-white'\n                              : 'bg-red-500 text-white'\n                          }`}>\n                            {detail.isCorrect ? '✅ CORRECT' : '❌ WRONG'}\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Question Content */}\n                  <div className=\"p-4\">\n                    <div className=\"mb-4\">\n                      <p className=\"text-gray-700 bg-white p-3 rounded-lg border\">\n                        {detail.questionText || detail.questionName}\n                      </p>\n                    </div>\n\n                    {/* Show Image for image questions or any question with an image */}\n                    {(detail.questionType === 'image' || detail.questionImage || detail.image || detail.imageUrl) && (detail.questionImage || detail.image || detail.imageUrl) && (\n                      <div className=\"mb-4\">\n                        <div className={`p-3 rounded-lg border-2 ${\n                          detail.isCorrect\n                            ? 'bg-green-50 border-green-200'\n                            : 'bg-red-50 border-red-200'\n                        }`}>\n                          <div className=\"flex items-center gap-2 mb-2\">\n                            <span className=\"text-sm font-semibold text-gray-700\">📷 Question Image:</span>\n                            <span className={`px-2 py-1 rounded-full text-xs font-bold ${\n                              detail.isCorrect\n                                ? 'bg-green-500 text-white'\n                                : 'bg-red-500 text-white'\n                            }`}>\n                              {detail.isCorrect ? '✅ CORRECT' : '❌ WRONG'}\n                            </span>\n                          </div>\n                          <div className=\"bg-white p-2 rounded-lg border\">\n                            <img\n                              src={detail.questionImage || detail.image || detail.imageUrl}\n                              alt=\"Question Image\"\n                              className=\"max-w-full h-auto rounded-lg shadow-sm mx-auto block\"\n                              style={{ maxHeight: '300px' }}\n                              onError={(e) => {\n                                e.target.style.display = 'none';\n                                e.target.nextSibling.style.display = 'block';\n                              }}\n                            />\n                            <div\n                              className=\"text-center text-gray-500 text-sm p-4 bg-gray-100 rounded-lg\"\n                              style={{ display: 'none' }}\n                            >\n                              📷 Image could not be loaded\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    )}\n\n                    {/* Enhanced Answer Section with Color Indicators */}\n                    <div className=\"space-y-3\">\n                      <div\n                        className={`p-4 rounded-lg ${\n                          detail.isCorrect\n                            ? 'bg-green-50'\n                            : 'bg-red-50'\n                        }`}\n                        style={{\n                          border: detail.isCorrect\n                            ? '3px solid #16a34a' // Green border for correct answers\n                            : '3px solid #dc2626'  // Red border for wrong answers\n                        }}\n                      >\n                        <div className=\"flex items-center gap-3 mb-2\">\n                          <div className={`w-6 h-6 rounded-full flex items-center justify-center ${\n                            detail.isCorrect ? 'bg-green-500' : 'bg-red-500'\n                          }`}>\n                            {detail.isCorrect ? (\n                              <TbCheck className=\"w-4 h-4 text-white\" />\n                            ) : (\n                              <TbX className=\"w-4 h-4 text-white\" />\n                            )}\n                          </div>\n                          <span className=\"font-semibold text-gray-700\">Your Answer:</span>\n                        </div>\n                        <div className={`p-3 rounded-lg font-bold text-lg ${\n                          detail.isCorrect\n                            ? 'bg-green-100 text-green-700 border border-green-200'\n                            : 'bg-red-100 text-red-700 border border-red-200'\n                        }`}>\n                          {detail.userAnswer || 'No answer provided'}\n                        </div>\n                      </div>\n\n                      {!detail.isCorrect && (\n                        <div className=\"bg-green-50 p-4 rounded-lg border-4 border-green-500\">\n                          <div className=\"flex items-center gap-3 mb-2\">\n                            <div className=\"w-6 h-6 rounded-full bg-green-500 flex items-center justify-center\">\n                              <TbCheck className=\"w-4 h-4 text-white\" />\n                            </div>\n                            <span className=\"font-semibold text-gray-700\">Correct Answer:</span>\n                          </div>\n                          <div className=\"bg-green-100 p-3 rounded-lg border-2 border-green-400 font-bold text-lg text-green-700\">\n                            {detail.correctAnswer}\n                          </div>\n                        </div>\n                      )}\n\n                      {/* Explanation Button for Wrong Answers */}\n                      {!detail.isCorrect && (\n                        <div className=\"mt-3\">\n                          <button\n                            className={`w-full font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 ${\n                              loadingExplanations[`question_${index}`]\n                                ? 'bg-gray-400 cursor-not-allowed'\n                                : 'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl'\n                            }`}\n                            onClick={() => fetchExplanation(index, detail)}\n                            disabled={loadingExplanations[`question_${index}`]}\n                          >\n                            {loadingExplanations[`question_${index}`] ? (\n                              <>\n                                <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n                                Getting Explanation...\n                              </>\n                            ) : (\n                              <>\n                                <TbBrain className=\"w-5 h-5\" />\n                                Get Explanation\n                              </>\n                            )}\n                          </button>\n\n                          {/* Explanation Display with Math Support */}\n                          {explanations[`question_${index}`] && (\n                            <div className=\"mt-3 p-4 bg-gradient-to-r from-blue-50 to-purple-50 border-2 border-blue-200 rounded-lg\">\n                              <div className=\"flex items-center gap-2 mb-3\">\n                                <TbBrain className=\"w-5 h-5 text-blue-600\" />\n                                <h6 className=\"font-bold text-blue-800\">💡 Explanation:</h6>\n                              </div>\n                              <div className=\"text-blue-700 leading-relaxed\">\n                                <ContentRenderer text={explanations[`question_${index}`]} />\n                              </div>\n                            </div>\n                          )}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n                );\n              })}\n            </div>\n\n\n          </div>\n        )}\n\n        {/* Actions */}\n        <div\n          className=\"flex gap-4\"\n          style={{\n            flexDirection: window.innerWidth <= 768 ? 'column' : 'row'\n          }}\n        >\n          <button\n            onClick={(e) => {\n              e.preventDefault();\n              console.log('🔥 More Quizzes button clicked!');\n              handleBackToQuizzes();\n            }}\n            className=\"flex-1 flex items-center justify-center gap-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all font-medium cursor-pointer\"\n            style={{\n              padding: window.innerWidth <= 768 ? '12px 16px' : '12px 24px',\n              fontSize: window.innerWidth <= 768 ? '14px' : '16px'\n            }}\n            type=\"button\"\n          >\n            <TbHome\n              style={{\n                width: window.innerWidth <= 768 ? '16px' : '20px',\n                height: window.innerWidth <= 768 ? '16px' : '20px'\n              }}\n            />\n            More Quizzes\n          </button>\n\n          <button\n            onClick={(e) => {\n              e.preventDefault();\n              console.log('🔥 Retake Quiz button clicked!');\n              handleRetakeQuiz();\n            }}\n            className=\"flex-1 flex items-center justify-center gap-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all font-medium cursor-pointer\"\n            style={{\n              padding: window.innerWidth <= 768 ? '12px 16px' : '12px 24px',\n              fontSize: window.innerWidth <= 768 ? '14px' : '16px'\n            }}\n            type=\"button\"\n          >\n            <TbTrophy\n              style={{\n                width: window.innerWidth <= 768 ? '16px' : '20px',\n                height: window.innerWidth <= 768 ? '16px' : '20px'\n              }}\n            />\n            Retake Quiz\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizResult;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,eAAe,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AACnE,SAASC,WAAW,EAAEC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACtE,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,QAAQ,EAAEC,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,UAAU,QAAQ,gBAAgB;AAC5F,SAASC,2BAA2B,QAAQ,wBAAwB;AACpE,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,OAAOC,eAAe,MAAM,qCAAqC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAElE,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAMC,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAMqB,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEqB;EAAG,CAAC,GAAGpB,SAAS,CAAC,CAAC;EAC1B,MAAM;IAAEqB;EAAK,CAAC,GAAGpB,WAAW,CAAEqB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE;EAAY,CAAC,GAAGb,WAAW,CAAC,CAAC;EACrC;EACA,MAAMc,UAAU,GAAGL,QAAQ,CAACG,KAAK,IAAI;IACnCG,UAAU,EAAE,CAAC;IACbC,cAAc,EAAE,CAAC;IACjBC,cAAc,EAAE,CAAC;IACjBC,SAAS,EAAE,CAAC;IACZC,aAAa,EAAE,EAAE;IACjBC,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE,MAAM;IAChBC,WAAW,EAAE,SAAS;IACtBC,iBAAiB,EAAE,EAAE;IACrBC,OAAO,EAAE;EACX,CAAC;EAED,MAAM;IACJT,UAAU;IACVC,cAAc;IACdC,cAAc;IACdC,SAAS;IACTE,MAAM;IACNC,QAAQ;IACRC,WAAW;IACXC,iBAAiB;IACjBC,OAAO;IACPL;EACF,CAAC,GAAGL,UAAU;EACd,MAAMW,QAAQ,GAAGD,OAAO,KAAK,MAAM,IAAIT,UAAU,KAAKQ,iBAAiB,IAAI,EAAE,CAAC;EAE9E,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACyC,YAAY,EAAEC,eAAe,CAAC,GAAG1C,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAAC2C,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG5C,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClE,MAAM,CAAC6C,UAAU,EAAEC,aAAa,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACAD,SAAS,CAAC,MAAM;IAEd;IACA,MAAMgD,SAAS,GAAGA,CAAA,KAAM;MACtB;MACAD,aAAa,CAAC,IAAI,CAAC;MACnBE,UAAU,CAAC,MAAMF,aAAa,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAE9C,IAAI;QACF,IAAIR,QAAQ,EAAE;UACZ;UACA,MAAMW,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAAID,MAAM,CAACE,kBAAkB,EAAE,CAAC;;UAE7E;UACA,MAAMC,QAAQ,GAAGA,CAACC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,IAAI,GAAG,MAAM,KAAK;YAClE,MAAMC,UAAU,GAAGT,YAAY,CAACU,gBAAgB,CAAC,CAAC;YAClD,MAAMC,QAAQ,GAAGX,YAAY,CAACY,UAAU,CAAC,CAAC;YAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;YAC5BA,QAAQ,CAACE,OAAO,CAACb,YAAY,CAACc,WAAW,CAAC;YAE1CL,UAAU,CAACJ,SAAS,CAACU,cAAc,CAACV,SAAS,EAAEC,SAAS,CAAC;YACzDG,UAAU,CAACD,IAAI,GAAGA,IAAI;YAEtBG,QAAQ,CAACK,IAAI,CAACD,cAAc,CAAC,CAAC,EAAET,SAAS,CAAC;YAC1CK,QAAQ,CAACK,IAAI,CAACC,uBAAuB,CAAC,GAAG,EAAEX,SAAS,GAAG,IAAI,CAAC;YAC5DK,QAAQ,CAACK,IAAI,CAACE,4BAA4B,CAAC,IAAI,EAAEZ,SAAS,GAAGC,QAAQ,CAAC;YAEtEE,UAAU,CAACU,KAAK,CAACb,SAAS,CAAC;YAC3BG,UAAU,CAACW,IAAI,CAACd,SAAS,GAAGC,QAAQ,CAAC;UACvC,CAAC;;UAED;UACA,MAAMc,UAAU,GAAIf,SAAS,IAAK;YAChC,MAAMgB,KAAK,GAAGtB,YAAY,CAACuB,kBAAkB,CAAC,CAAC;YAC/C,MAAMC,MAAM,GAAGxB,YAAY,CAACyB,YAAY,CAAC,CAAC,EAAEzB,YAAY,CAAC0B,UAAU,GAAG,GAAG,EAAE1B,YAAY,CAAC0B,UAAU,CAAC;YACnG,MAAMC,IAAI,GAAGH,MAAM,CAACI,cAAc,CAAC,CAAC,CAAC;;YAErC;YACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;cACpCF,IAAI,CAACE,CAAC,CAAC,GAAGE,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;YACjC;YAEAV,KAAK,CAACE,MAAM,GAAGA,MAAM;YAErB,MAAMS,MAAM,GAAGjC,YAAY,CAACkC,kBAAkB,CAAC,CAAC;YAChDD,MAAM,CAACzB,IAAI,GAAG,UAAU;YACxByB,MAAM,CAAC5B,SAAS,CAACU,cAAc,CAAC,IAAI,EAAET,SAAS,CAAC;YAEhD,MAAMK,QAAQ,GAAGX,YAAY,CAACY,UAAU,CAAC,CAAC;YAC1CD,QAAQ,CAACK,IAAI,CAACD,cAAc,CAAC,CAAC,EAAET,SAAS,CAAC;YAC1CK,QAAQ,CAACK,IAAI,CAACC,uBAAuB,CAAC,GAAG,EAAEX,SAAS,GAAG,IAAI,CAAC;YAC5DK,QAAQ,CAACK,IAAI,CAACE,4BAA4B,CAAC,IAAI,EAAEZ,SAAS,GAAG,GAAG,CAAC;YAEjEgB,KAAK,CAACT,OAAO,CAACoB,MAAM,CAAC;YACrBA,MAAM,CAACpB,OAAO,CAACF,QAAQ,CAAC;YACxBA,QAAQ,CAACE,OAAO,CAACb,YAAY,CAACc,WAAW,CAAC;YAE1CQ,KAAK,CAACH,KAAK,CAACb,SAAS,CAAC;YACtBgB,KAAK,CAACF,IAAI,CAACd,SAAS,GAAG,GAAG,CAAC;UAC7B,CAAC;UAED,MAAM6B,GAAG,GAAGnC,YAAY,CAACoC,WAAW;;UAEpC;UACAhC,QAAQ,CAAC,MAAM,EAAE+B,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;UAC5B/B,QAAQ,CAAC,MAAM,EAAE+B,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;UAClC/B,QAAQ,CAAC,MAAM,EAAE+B,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;UAClC/B,QAAQ,CAAC,MAAM,EAAE+B,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;;UAElC;UACAd,UAAU,CAACc,GAAG,GAAG,GAAG,CAAC;UACrBd,UAAU,CAACc,GAAG,GAAG,GAAG,CAAC;UACrBd,UAAU,CAACc,GAAG,GAAG,GAAG,CAAC;UACrBd,UAAU,CAACc,GAAG,GAAG,GAAG,CAAC;QAEvB,CAAC,MAAM;UACL;UACA,MAAMnC,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAAID,MAAM,CAACE,kBAAkB,EAAE,CAAC;UAE7E,MAAMC,QAAQ,GAAGA,CAACC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,KAAK;YACnD,MAAME,UAAU,GAAGT,YAAY,CAACU,gBAAgB,CAAC,CAAC;YAClD,MAAMC,QAAQ,GAAGX,YAAY,CAACY,UAAU,CAAC,CAAC;YAE1CH,UAAU,CAACI,OAAO,CAACF,QAAQ,CAAC;YAC5BA,QAAQ,CAACE,OAAO,CAACb,YAAY,CAACc,WAAW,CAAC;YAE1CL,UAAU,CAACJ,SAAS,CAACU,cAAc,CAACV,SAAS,EAAEC,SAAS,CAAC;YACzDG,UAAU,CAACD,IAAI,GAAG,MAAM;YAExBG,QAAQ,CAACK,IAAI,CAACD,cAAc,CAAC,CAAC,EAAET,SAAS,CAAC;YAC1CK,QAAQ,CAACK,IAAI,CAACC,uBAAuB,CAAC,IAAI,EAAEX,SAAS,GAAG,IAAI,CAAC;YAC7DK,QAAQ,CAACK,IAAI,CAACE,4BAA4B,CAAC,IAAI,EAAEZ,SAAS,GAAGC,QAAQ,CAAC;YAEtEE,UAAU,CAACU,KAAK,CAACb,SAAS,CAAC;YAC3BG,UAAU,CAACW,IAAI,CAACd,SAAS,GAAGC,QAAQ,CAAC;UACvC,CAAC;;UAED;UACA,MAAM4B,GAAG,GAAGnC,YAAY,CAACoC,WAAW;UACpChC,QAAQ,CAAC,GAAG,EAAE+B,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;UACzB/B,QAAQ,CAAC,MAAM,EAAE+B,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QACpC;MACF,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;MACpC;IACF,CAAC;;IAED;IACA,IAAIlD,QAAQ,EAAE;MACZ;MACA,MAAMmD,eAAe,GAAG,EAAE;;MAE1B;MACA,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;QAC5B,MAAMY,MAAM,GAAG,CACb,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EACrD,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EACrD,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CACtD;QAEDD,eAAe,CAACE,IAAI,CAAC;UACnBpE,EAAE,EAAG,YAAWuD,CAAE,EAAC;UACnBc,IAAI,EAAE,EAAE,GAAGZ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;UAAE;UAC/BY,KAAK,EAAEb,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UACxBzB,QAAQ,EAAE,CAAC,GAAGwB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UAC/Ba,KAAK,EAAEJ,MAAM,CAACV,IAAI,CAACe,KAAK,CAACf,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGS,MAAM,CAACX,MAAM,CAAC,CAAC;UACxDiB,KAAK,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAChB,IAAI,CAACe,KAAK,CAACf,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;UACtEgB,IAAI,EAAE,CAAC,GAAGjB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UAC3BxB,IAAI,EAAE,UAAU;UAChByC,OAAO,EAAE,CAAClB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC;QACvC,CAAC,CAAC;MACJ;;MAEA;MACA,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAC3BW,eAAe,CAACE,IAAI,CAAC;UACnBpE,EAAE,EAAG,WAAUuD,CAAE,EAAC;UAClBc,IAAI,EAAEZ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UACzBY,KAAK,EAAEb,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UACxBzB,QAAQ,EAAE,CAAC,GAAGwB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UAC/Ba,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAACd,IAAI,CAACe,KAAK,CAACf,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;UAClFgB,IAAI,EAAE,CAAC,GAAGjB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UAC3BxB,IAAI,EAAE;QACR,CAAC,CAAC;MACJ;;MAEA;MACA,KAAK,IAAIqB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;QAC5BW,eAAe,CAACE,IAAI,CAAC;UACnBpE,EAAE,EAAG,SAAQuD,CAAE,EAAC;UAChBc,IAAI,EAAE,EAAE,GAAGZ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;UAAE;UAC/BY,KAAK,EAAEb,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UAC1BzB,QAAQ,EAAE,CAAC,GAAGwB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UAC/Ba,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAACd,IAAI,CAACe,KAAK,CAACf,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;UAClFe,KAAK,EAAE,QAAQ;UACfC,IAAI,EAAE,CAAC,GAAGjB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UAC3BxB,IAAI,EAAE,OAAO;UACbyC,OAAO,EAAE,CAAClB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI;QACnC,CAAC,CAAC;MACJ;MAEAzC,WAAW,CAACiD,eAAe,CAAC;;MAE5B;MACAzC,UAAU,CAAC,MAAMR,WAAW,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC;IAC1C,CAAC,MAAM;MACL;MACA,MAAM2D,oBAAoB,GAAG,EAAE;MAC/B,MAAMC,kBAAkB,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;MAErF,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAC3BqB,oBAAoB,CAACR,IAAI,CAAC;UACxBpE,EAAE,EAAG,YAAWuD,CAAE,EAAC;UACnBc,IAAI,EAAEZ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UACzBY,KAAK,EAAEb,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UACxBzB,QAAQ,EAAE,CAAC,GAAGwB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UAC/BoB,KAAK,EAAED,kBAAkB,CAACpB,IAAI,CAACe,KAAK,CAACf,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGmB,kBAAkB,CAACrB,MAAM,CAAC,CAAC;UAChFuB,cAAc,EAAE,IAAI;UACpBL,IAAI,EAAE,CAAC,GAAGjB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;QAC5B,CAAC,CAAC;MACJ;MACAzC,WAAW,CAAC2D,oBAAoB,CAAC;;MAEjC;MACAnD,UAAU,CAAC,MAAMR,WAAW,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACzC;IAEAO,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACT,QAAQ,CAAC,CAAC;;EAId;EACA,MAAMiE,gBAAgB,GAAG,MAAAA,CAAOC,aAAa,EAAEC,MAAM,KAAK;IACxD,MAAMC,WAAW,GAAI,YAAWF,aAAc,EAAC;;IAE/C;IACA,IAAI7D,mBAAmB,CAAC+D,WAAW,CAAC,IAAIjE,YAAY,CAACiE,WAAW,CAAC,EAAE;MACjE;IACF;IAEA,IAAI;MACF9D,sBAAsB,CAAC+D,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACD,WAAW,GAAG;MAAK,CAAC,CAAC,CAAC;MAElE,MAAME,QAAQ,GAAG,MAAMhG,2BAA2B,CAAC;QACjDiG,QAAQ,EAAEJ,MAAM,CAACK,YAAY,IAAIL,MAAM,CAACM,YAAY;QACpDC,cAAc,EAAEP,MAAM,CAACQ,aAAa;QACpCC,UAAU,EAAET,MAAM,CAACS,UAAU;QAC7BC,QAAQ,EAAEV,MAAM,CAACW,aAAa,IAAIX,MAAM,CAACY,KAAK,IAAIZ,MAAM,CAACU,QAAQ,IAAI,IAAI;QACzEG,QAAQ,EAAE5F,WAAW,GAAG,WAAW,GAAG;MACxC,CAAC,CAAC;MAEF,IAAIkF,QAAQ,CAACW,OAAO,EAAE;QACpB7E,eAAe,CAACiE,IAAI,KAAK;UACvB,GAAGA,IAAI;UACP,CAACD,WAAW,GAAGE,QAAQ,CAACY;QAC1B,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLjC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEsB,QAAQ,CAACtB,KAAK,CAAC;QAC7D5C,eAAe,CAACiE,IAAI,KAAK;UACvB,GAAGA,IAAI;UACP,CAACD,WAAW,GAAG;QACjB,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD5C,eAAe,CAACiE,IAAI,KAAK;QACvB,GAAGA,IAAI;QACP,CAACD,WAAW,GAAG;MACjB,CAAC,CAAC,CAAC;IACL,CAAC,SAAS;MACR9D,sBAAsB,CAAC+D,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACD,WAAW,GAAG;MAAM,CAAC,CAAC,CAAC;IACrE;EACF,CAAC;EAID,MAAMe,mBAAmB,GAAGA,CAAA,KAAM;IAChClC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAC/C1F,eAAe,CAAC,MAAM;MACpBuB,QAAQ,CAAC,YAAY,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMqG,gBAAgB,GAAGA,CAAA,KAAM;IAC7BnC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEjE,EAAE,CAAC;IAC5C,IAAIA,EAAE,EAAE;MACNzB,eAAe,CAAC,MAAM;QACpBuB,QAAQ,CAAE,SAAQE,EAAG,OAAM,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC,MAAM;MACLgE,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC5D1F,eAAe,CAAC,MAAM;QACpBuB,QAAQ,CAAC,YAAY,CAAC;MACxB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,oBACEL,OAAA;IAAK2G,SAAS,EAAG,0EACfrF,QAAQ,GACJ,4DAA4D,GAC5D,yDACL,IAAGO,UAAU,GAAIP,QAAQ,GAAG,aAAa,GAAG,WAAW,GAAI,EAAG,EAAE;IACjEsF,KAAK,EAAE;MACLC,OAAO,EAAE3E,MAAM,CAAC4E,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG5E,MAAM,CAAC4E,UAAU,IAAI,IAAI,GAAG,MAAM,GAAG;IACnF,CAAE;IAAAC,QAAA,GAGCxF,QAAQ,CAACyF,GAAG,CAAEC,KAAK,IAAK;MACvB,IAAIA,KAAK,CAAC3B,cAAc,EAAE;QACxB,oBACEtF,OAAA;UAEE2G,SAAS,EAAC,qBAAqB;UAC/BC,KAAK,EAAE;YACLhC,IAAI,EAAG,GAAEqC,KAAK,CAACrC,IAAK,GAAE;YACtBsC,GAAG,EAAG,GAAE,EAAE,GAAGlD,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAG,GAAE;YAClCkD,QAAQ,EAAG,GAAEF,KAAK,CAAChC,IAAI,IAAI,CAAE,KAAI;YACjCmC,SAAS,EAAG,cAAaH,KAAK,CAACzE,QAAS,iBAAgByE,KAAK,CAACpC,KAAM,YAAW;YAC/EwC,MAAM,EAAE;UACV,CAAE;UAAAN,QAAA,EAEDE,KAAK,CAAC5B;QAAK,GAVP4B,KAAK,CAAC1G,EAAE;UAAA+G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAWV,CAAC;MAEV;MAEA,IAAIR,KAAK,CAACxE,IAAI,KAAK,SAAS,EAAE;QAC5B,oBACEzC,OAAA;UAEE2G,SAAS,EAAC,UAAU;UACpBC,KAAK,EAAE;YACLhC,IAAI,EAAG,GAAEqC,KAAK,CAACrC,IAAK,GAAE;YACtB8C,KAAK,EAAG,GAAET,KAAK,CAAChC,IAAK,IAAG;YACxB0C,MAAM,EAAG,GAAEV,KAAK,CAAChC,IAAK,IAAG;YACzB2C,UAAU,EAAG,2BAA0BX,KAAK,CAACnC,KAAM,gBAAe;YAClE+C,YAAY,EAAE,KAAK;YACnBT,SAAS,EAAG,mBAAkBH,KAAK,CAACzE,QAAS,iBAAgByE,KAAK,CAACpC,KAAM,YAAW;YACpFqC,GAAG,EAAG,GAAElD,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAI,GAAE;YAC9B6D,SAAS,EAAG,OAAMb,KAAK,CAAChC,IAAI,GAAG,CAAE,MAAKgC,KAAK,CAACnC,KAAM,EAAC;YACnDuC,MAAM,EAAE;UACV;QAAE,GAZGJ,KAAK,CAAC1G,EAAE;UAAA+G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAad,CAAC;MAEN;MAEA,IAAIR,KAAK,CAACxE,IAAI,KAAK,OAAO,EAAE;QAC1B,oBACEzC,OAAA;UAEE2G,SAAS,EAAC,qBAAqB;UAC/BC,KAAK,EAAE;YACLhC,IAAI,EAAG,GAAEqC,KAAK,CAACrC,IAAK,GAAE;YACtB8C,KAAK,EAAG,GAAET,KAAK,CAAChC,IAAK,IAAG;YACxB0C,MAAM,EAAG,GAAEV,KAAK,CAAChC,IAAK,IAAG;YACzB8C,eAAe,EAAEd,KAAK,CAACnC,KAAK;YAC5B+C,YAAY,EAAEZ,KAAK,CAACjC,KAAK,KAAK,QAAQ,GAAG,KAAK,GAAGiC,KAAK,CAACjC,KAAK,KAAK,UAAU,GAAG,GAAG,GAAG,IAAI;YACxFgD,QAAQ,EAAEf,KAAK,CAACjC,KAAK,KAAK,UAAU,GAAG,qCAAqC,GAAG,MAAM;YACrFoC,SAAS,EAAG,kBAAiBH,KAAK,CAACzE,QAAS,cAAayE,KAAK,CAACpC,KAAM,YAAW;YAChFqC,GAAG,EAAE,KAAK;YACV,YAAY,EAAG,GAAED,KAAK,CAAC/B,OAAQ,IAAG;YAClC4C,SAAS,EAAG,OAAMb,KAAK,CAAChC,IAAK,MAAKgC,KAAK,CAACnC,KAAM,IAAG;YACjDuC,MAAM,EAAE;UACV;QAAE,GAdGJ,KAAK,CAAC1G,EAAE;UAAA+G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAed,CAAC;MAEN;;MAEA;MACA,oBACEzH,OAAA;QAEE2G,SAAS,EAAC,qBAAqB;QAC/BC,KAAK,EAAE;UACLhC,IAAI,EAAG,GAAEqC,KAAK,CAACrC,IAAK,GAAE;UACtB8C,KAAK,EAAG,GAAET,KAAK,CAAChC,IAAK,IAAG;UACxB0C,MAAM,EAAG,GAAEV,KAAK,CAAChC,IAAK,IAAG;UACzB8C,eAAe,EAAEd,KAAK,CAACnC,KAAK;UAC5B+C,YAAY,EAAEZ,KAAK,CAACjC,KAAK,KAAK,QAAQ,GAAG,KAAK,GAAGiC,KAAK,CAACjC,KAAK,KAAK,UAAU,GAAG,GAAG,GAAG,IAAI;UACxFgD,QAAQ,EAAEf,KAAK,CAACjC,KAAK,KAAK,UAAU,GAAG,qCAAqC,GAAG,MAAM;UACrFoC,SAAS,EAAG,iBAAgBH,KAAK,CAACzE,QAAS,cAAayE,KAAK,CAACpC,KAAM,YAAW;UAC/EqC,GAAG,EAAE,OAAO;UACZY,SAAS,EAAG,OAAMb,KAAK,CAAChC,IAAK,MAAKgC,KAAK,CAACnC,KAAM,IAAG;UACjDmD,MAAM,EAAG,aAAYhB,KAAK,CAACnC,KAAM,EAAC;UAClC8C,UAAU,EAAG,0BAAyBX,KAAK,CAACnC,KAAM,KAAImC,KAAK,CAACnC,KAAM,KAAI;UACtEuC,MAAM,EAAE;QACV;MAAE,GAfGJ,KAAK,CAAC1G,EAAE;QAAA+G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgBd,CAAC;IAEN,CAAC,CAAC,eAGFzH,OAAA;MAAOkI,GAAG;MAAAnB,QAAA,EAAG;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,EAGT5F,UAAU,iBACT7B,OAAA;MACE2G,SAAS,EAAC,mCAAmC;MAC7CC,KAAK,EAAE;QACLgB,UAAU,EAAEtG,QAAQ,GAChB,6GAA6G,GAC7G,6GAA6G;QACjH8F,SAAS,EAAE9F,QAAQ,GAAG,+CAA+C,GAAG,6CAA6C;QACrH+F,MAAM,EAAE;MACV;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF,eAEDzH,OAAA;MAAK2G,SAAS,EAAG,4DACfrF,QAAQ,GAAG,mCAAmC,GAAG,+BAClD,IAAGO,UAAU,GAAG,YAAY,GAAG,EAAG,EAAE;MACrC+E,KAAK,EAAE;QACLgB,UAAU,EAAE/F,UAAU,GACjBP,QAAQ,GACL,0EAA0E,GAC1E,0EAA0E,GAC9E,OAAO;QACXwG,SAAS,EAAEjG,UAAU,GAChBP,QAAQ,GACL,sEAAsE,GACtE,sEAAsE,GAC1E,8BAA8B;QAClC+F,MAAM,EAAE,EAAE;QACVR,OAAO,EAAE3E,MAAM,CAAC4E,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG5E,MAAM,CAAC4E,UAAU,IAAI,IAAI,GAAG,MAAM,GAAG,MAAM;QACxFqB,QAAQ,EAAEjG,MAAM,CAAC4E,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG5E,MAAM,CAAC4E,UAAU,IAAI,IAAI,GAAG,KAAK,GAAG;MACpF,CAAE;MAAAC,QAAA,gBAEA/G,OAAA;QACE2G,SAAS,EAAC,aAAa;QACvBC,KAAK,EAAE;UAAEwB,YAAY,EAAElG,MAAM,CAAC4E,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;QAAO,CAAE;QAAAC,QAAA,gBAEpE/G,OAAA;UACE2G,SAAS,EAAG,sEACVrF,QAAQ,GAAG,iDAAiD,GAAG,4CAChE,EAAE;UACHsF,KAAK,EAAE;YACLc,KAAK,EAAExF,MAAM,CAAC4E,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;YACjDa,MAAM,EAAEzF,MAAM,CAAC4E,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;UAC9C,CAAE;UAAAC,QAAA,eAEF/G,OAAA,CAACX,QAAQ;YACPsH,SAAS,EAAG,GAAErF,QAAQ,GAAG,iBAAiB,GAAG,eAAgB,EAAE;YAC/DsF,KAAK,EAAE;cACLc,KAAK,EAAExF,MAAM,CAAC4E,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;cACjDa,MAAM,EAAEzF,MAAM,CAAC4E,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;YAC9C;UAAE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENzH,OAAA;UACE2G,SAAS,EAAG,kBACVrF,QAAQ,GACJ,oDAAoD,GACpD,wCACL,EAAE;UACHsF,KAAK,EAAE;YACLO,QAAQ,EAAEjF,MAAM,CAAC4E,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG5E,MAAM,CAAC4E,UAAU,IAAI,IAAI,GAAG,MAAM,GAAG;UACrF,CAAE;UAAAC,QAAA,EAEDzF,QAAQ,gBACPtB,OAAA;YACE2G,SAAS,EAAC,kCAAkC;YAC5CC,KAAK,EAAE;cAAEyB,GAAG,EAAEnG,MAAM,CAAC4E,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG,MAAM;cAAEwB,QAAQ,EAAE;YAAO,CAAE;YAAAvB,QAAA,gBAE5E/G,OAAA;cACE2G,SAAS,EAAC,qBAAqB;cAC/BC,KAAK,EAAE;gBAAEO,QAAQ,EAAEjF,MAAM,CAAC4E,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;cAAO,CAAE;cAAAC,QAAA,EACjE;YAAE;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACVzH,OAAA;cAAM2G,SAAS,EAAC,sCAAsC;cAAAI,QAAA,EAAC;YAAgB;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9EzH,OAAA;cACE2G,SAAS,EAAC,qBAAqB;cAC/BC,KAAK,EAAE;gBAAEO,QAAQ,EAAEjF,MAAM,CAAC4E,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;cAAO,CAAE;cAAAC,QAAA,EACjE;YAAE;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,gBAEPzH,OAAA;YACE2G,SAAS,EAAC,kCAAkC;YAC5CC,KAAK,EAAE;cAAEyB,GAAG,EAAEnG,MAAM,CAAC4E,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG,MAAM;cAAEwB,QAAQ,EAAE;YAAO,CAAE;YAAAvB,QAAA,gBAE5E/G,OAAA;cACE2G,SAAS,EAAC,uBAAuB;cACjCC,KAAK,EAAE;gBAAEO,QAAQ,EAAEjF,MAAM,CAAC4E,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;cAAO,CAAE;cAAAC,QAAA,EACjE;YAAE;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACVzH,OAAA;cAAM2G,SAAS,EAAC,kCAAkC;cAAAI,QAAA,EAAC;YAAW;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrEzH,OAAA;cACE2G,SAAS,EAAC,uBAAuB;cACjCC,KAAK,EAAE;gBAAEO,QAAQ,EAAEjF,MAAM,CAAC4E,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;cAAO,CAAE;cAAAC,QAAA,EACjE;YAAE;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACP;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAELzH,OAAA;UAAK2G,SAAS,EAAG,2BACfrF,QAAQ,GACJ,0CAA0C,GAC1C,wCACL,EAAE;UAAAyF,QAAA,EACAzF,QAAQ,gBACPtB,OAAA;YAAM2G,SAAS,EAAC,sCAAsC;YAAAI,QAAA,EAAC;UAAe;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,gBAE7EzH,OAAA;YAAM2G,SAAS,EAAC,kCAAkC;YAAAI,QAAA,EAAC;UAAoB;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAC9E;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENzH,OAAA;UAAG2G,SAAS,EAAC,4BAA4B;UAAAI,QAAA,GAAC,eACrC,EAAC7F,QAAQ,EAAC,KAAG,EAACC,WAAW;QAAA;UAAAmG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNzH,OAAA;QAAK2G,SAAS,EAAC,kBAAkB;QAAAI,QAAA,eAC/B/G,OAAA;UAAK2G,SAAS,EAAG,sCACfrF,QAAQ,GAAG,uCAAuC,GAAG,mCACtD,EAAE;UAAAyF,QAAA,gBACD/G,OAAA;YAAK2G,SAAS,EAAG,2BACfrF,QAAQ,GAAG,gBAAgB,GAAG,cAC/B,EAAE;YAAAyF,QAAA,GACAnG,UAAU,EAAC,GACd;UAAA;YAAA0G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNzH,OAAA;YAAK2G,SAAS,EAAC,eAAe;YAAAI,QAAA,EAAC;UAE/B;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAKNzH,OAAA;QACE2G,SAAS,EAAC,6CAA6C;QACvDC,KAAK,EAAE;UAAE0B,QAAQ,EAAE;QAAO,CAAE;QAAAvB,QAAA,gBAG5B/G,OAAA;UACE4G,KAAK,EAAE;YACLmB,eAAe,EAAE,SAAS;YAC1BlB,OAAO,EAAE,SAAS;YAClBoB,MAAM,EAAE,mBAAmB;YAC3BJ,YAAY,EAAE,KAAK;YACnBU,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBH,GAAG,EAAE,KAAK;YACVlB,QAAQ,EAAE;UACZ,CAAE;UAAAJ,QAAA,gBAEF/G,OAAA;YAAK4G,KAAK,EAAE;cAAE2B,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEH,GAAG,EAAE;YAAM,CAAE;YAAAtB,QAAA,gBAChE/G,OAAA;cAAM4G,KAAK,EAAE;gBAAEO,QAAQ,EAAE;cAAO,CAAE;cAAAJ,QAAA,EAAC;YAAC;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3CzH,OAAA;cAAM4G,KAAK,EAAE;gBAAEO,QAAQ,EAAE,MAAM;gBAAEsB,UAAU,EAAE,MAAM;gBAAE3D,KAAK,EAAE;cAAU,CAAE;cAAAiC,QAAA,EAAElG;YAAc;cAAAyG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7F,CAAC,eACNzH,OAAA;YAAK4G,KAAK,EAAE;cAAE2B,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEH,GAAG,EAAE;YAAM,CAAE;YAAAtB,QAAA,gBAChE/G,OAAA;cAAM4G,KAAK,EAAE;gBAAEO,QAAQ,EAAE;cAAO,CAAE;cAAAJ,QAAA,EAAC;YAAC;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3CzH,OAAA;cAAM4G,KAAK,EAAE;gBAAEO,QAAQ,EAAE,MAAM;gBAAEsB,UAAU,EAAE,MAAM;gBAAE3D,KAAK,EAAE;cAAU,CAAE;cAAAiC,QAAA,EAAEjG,cAAc,GAAGD;YAAc;cAAAyG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9G,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNzH,OAAA;UACE4G,KAAK,EAAE;YACLmB,eAAe,EAAE,SAAS;YAC1BlB,OAAO,EAAE,SAAS;YAClBoB,MAAM,EAAE,mBAAmB;YAC3BJ,YAAY,EAAE,KAAK;YACnBU,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBH,GAAG,EAAE,KAAK;YACVlB,QAAQ,EAAE;UACZ,CAAE;UAAAJ,QAAA,gBAEF/G,OAAA;YAAM4G,KAAK,EAAE;cAAEO,QAAQ,EAAE;YAAO,CAAE;YAAAJ,QAAA,EAAC;UAAE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5CzH,OAAA;YAAM4G,KAAK,EAAE;cAAEO,QAAQ,EAAE,MAAM;cAAEsB,UAAU,EAAE,MAAM;cAAE3D,KAAK,EAAExD,QAAQ,GAAG,SAAS,GAAG;YAAU,CAAE;YAAAyF,QAAA,GAAEnG,UAAU,EAAC,GAAC;UAAA;YAAA0G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjH,CAAC,EAGL1G,SAAS,IAAIA,SAAS,GAAG,CAAC,iBACzBf,OAAA;UACE4G,KAAK,EAAE;YACLmB,eAAe,EAAE,SAAS;YAC1BlB,OAAO,EAAE,SAAS;YAClBoB,MAAM,EAAE,mBAAmB;YAC3BJ,YAAY,EAAE,KAAK;YACnBU,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBH,GAAG,EAAE,KAAK;YACVlB,QAAQ,EAAE;UACZ,CAAE;UAAAJ,QAAA,gBAEF/G,OAAA;YAAM4G,KAAK,EAAE;cAAEO,QAAQ,EAAE;YAAO,CAAE;YAAAJ,QAAA,EAAC;UAAE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5CzH,OAAA;YAAM4G,KAAK,EAAE;cAAEO,QAAQ,EAAE,MAAM;cAAEsB,UAAU,EAAE,MAAM;cAAE3D,KAAK,EAAE;YAAU,CAAE;YAAAiC,QAAA,GACrE/C,IAAI,CAACe,KAAK,CAAChE,SAAS,GAAG,EAAE,CAAC,EAAC,GAAC,EAAC,CAACA,SAAS,GAAG,EAAE,EAAE2H,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;UAAA;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGLxG,MAAM,iBACLjB,OAAA;QAAK2G,SAAS,EAAC,2FAA2F;QAAAI,QAAA,eACxG/G,OAAA;UAAK2G,SAAS,EAAC,mCAAmC;UAAAI,QAAA,gBAChD/G,OAAA;YAAK2G,SAAS,EAAC,yBAAyB;YAAAI,QAAA,gBACtC/G,OAAA;cAAK2G,SAAS,EAAC,uEAAuE;cAAAI,QAAA,eACpF/G,OAAA,CAACP,MAAM;gBAACkH,SAAS,EAAC;cAAoB;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACNzH,OAAA;cAAA+G,QAAA,gBACE/G,OAAA;gBAAK2G,SAAS,EAAC,mCAAmC;gBAAAI,QAAA,GAAC,GAAC,EAAC9F,MAAM,CAAC2H,SAAS,IAAI,CAAC,EAAC,KAAG;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpFzH,OAAA;gBAAK2G,SAAS,EAAC,uBAAuB;gBAAAI,QAAA,EAAC;cAAM;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENzH,OAAA;YAAK2G,SAAS,EAAC,YAAY;YAAAI,QAAA,gBACzB/G,OAAA;cAAK2G,SAAS,EAAC,oCAAoC;cAAAI,QAAA,EAChD,CAAC,CAAC,CAAAvG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqI,OAAO,KAAI,CAAC,KAAK5H,MAAM,CAAC2H,SAAS,IAAI,CAAC,CAAC,EAAEE,cAAc,CAAC;YAAC;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC,eACNzH,OAAA;cAAK2G,SAAS,EAAC,uBAAuB;cAAAI,QAAA,GAAC,wBAAiB,EAAC,CAAAvG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuI,YAAY,KAAI,CAAC;YAAA;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA,CAACxG,MAAM,IAAIT,IAAI,iBACdR,OAAA;QAAK2G,SAAS,EAAC,2FAA2F;QAAAI,QAAA,eACxG/G,OAAA;UAAK2G,SAAS,EAAC,mCAAmC;UAAAI,QAAA,gBAChD/G,OAAA;YAAK2G,SAAS,EAAC,yBAAyB;YAAAI,QAAA,gBACtC/G,OAAA;cAAK2G,SAAS,EAAC,uEAAuE;cAAAI,QAAA,eACpF/G,OAAA,CAACP,MAAM;gBAACkH,SAAS,EAAC;cAAoB;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACNzH,OAAA;cAAA+G,QAAA,eACE/G,OAAA;gBAAK2G,SAAS,EAAC,uBAAuB;gBAAAI,QAAA,EAAC;cAAa;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENzH,OAAA;YAAK2G,SAAS,EAAC,YAAY;YAAAI,QAAA,gBACzB/G,OAAA;cAAK2G,SAAS,EAAC,oCAAoC;cAAAI,QAAA,EAChD,CAACvG,IAAI,CAACqI,OAAO,IAAI,CAAC,EAAEC,cAAc,CAAC;YAAC;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACNzH,OAAA;cAAK2G,SAAS,EAAC,uBAAuB;cAAAI,QAAA,GAAC,wBAAiB,EAACvG,IAAI,CAACuI,YAAY,IAAI,CAAC;YAAA;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGDzH,OAAA;QAAK2G,SAAS,EAAC,qFAAqF;QAAAI,QAAA,eAClG/G,OAAA;UAAK2G,SAAS,EAAC,8BAA8B;UAAAI,QAAA,gBAC3C/G,OAAA;YAAK2G,SAAS,EAAC,qEAAqE;YAAAI,QAAA,eAClF/G,OAAA,CAACN,OAAO;cAACiH,SAAS,EAAC;YAAoB;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACNzH,OAAA;YAAI2G,SAAS,EAAC,qCAAqC;YAAAI,QAAA,EAAC;UAAmB;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGH,CAAC,EAGLzG,aAAa,IAAIA,aAAa,CAAC+C,MAAM,GAAG,CAAC,iBACxC/D,OAAA;QAAK2G,SAAS,EAAC,sFAAsF;QAAAI,QAAA,gBACnG/G,OAAA;UAAK2G,SAAS,EAAC,8BAA8B;UAAAI,QAAA,gBAC3C/G,OAAA;YAAK2G,SAAS,EAAC,qEAAqE;YAAAI,QAAA,eAClF/G,OAAA,CAACL,UAAU;cAACgH,SAAS,EAAC;YAAoB;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACNzH,OAAA;YAAI2G,SAAS,EAAC,qCAAqC;YAAAI,QAAA,EAAC;UAA8B;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC,eAENzH,OAAA;UAAK2G,SAAS,EAAC,oCAAoC;UAAAI,QAAA,EAChD/F,aAAa,CAACgG,GAAG,CAAC,CAACvB,MAAM,EAAEuD,KAAK,KAAK;YACpC;YACAzE,OAAO,CAACC,GAAG,CAAE,YAAWwE,KAAK,GAAG,CAAE,QAAO,EAAEvD,MAAM,CAAC;YAClD,oBACAzF,OAAA;cAEE2G,SAAS,EAAG,0EACVlB,MAAM,CAACwD,SAAS,GACZ,2EAA2E,GAC3E,oEACL,EAAE;cACHrC,KAAK,EAAE;gBACLqB,MAAM,EAAExC,MAAM,CAACwD,SAAS,GACpB,mBAAmB,CAAC;gBAAA,EACpB,mBAAmB;gBAAE;gBACzBnB,SAAS,EAAErC,MAAM,CAACwD,SAAS,GACvB,sEAAsE,GACtE;cACN,CAAE;cAAAlC,QAAA,gBAGF/G,OAAA;gBAAK2G,SAAS,EAAG,OACflB,MAAM,CAACwD,SAAS,GACZ,0CAA0C,GAC1C,sCACL,EAAE;gBAAAlC,QAAA,eACD/G,OAAA;kBAAK2G,SAAS,EAAC,yBAAyB;kBAAAI,QAAA,gBACtC/G,OAAA;oBAAK2G,SAAS,EAAG,qEACflB,MAAM,CAACwD,SAAS,GACZ,mCAAmC,GACnC,iCACL,EAAE;oBAAAlC,QAAA,EACAtB,MAAM,CAACwD,SAAS,gBAAGjJ,OAAA,CAACV,OAAO;sBAACqH,SAAS,EAAC;oBAAS;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAGzH,OAAA,CAACT,GAAG;sBAACoH,SAAS,EAAC;oBAAS;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9E,CAAC,eAENzH,OAAA;oBAAK2G,SAAS,EAAC,QAAQ;oBAAAI,QAAA,gBACrB/G,OAAA;sBAAI2G,SAAS,EAAC,iCAAiC;sBAAAI,QAAA,GAAC,WACrC,EAACiC,KAAK,GAAG,CAAC;oBAAA;sBAAA1B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACLzH,OAAA;sBAAK2G,SAAS,EAAC,8BAA8B;sBAAAI,QAAA,eAC3C/G,OAAA;wBAAM2G,SAAS,EAAG,4CAChBlB,MAAM,CAACwD,SAAS,GACZ,yBAAyB,GACzB,uBACL,EAAE;wBAAAlC,QAAA,EACAtB,MAAM,CAACwD,SAAS,GAAG,WAAW,GAAG;sBAAS;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNzH,OAAA;gBAAK2G,SAAS,EAAC,KAAK;gBAAAI,QAAA,gBAClB/G,OAAA;kBAAK2G,SAAS,EAAC,MAAM;kBAAAI,QAAA,eACnB/G,OAAA;oBAAG2G,SAAS,EAAC,8CAA8C;oBAAAI,QAAA,EACxDtB,MAAM,CAACK,YAAY,IAAIL,MAAM,CAACM;kBAAY;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,EAGL,CAAChC,MAAM,CAACyD,YAAY,KAAK,OAAO,IAAIzD,MAAM,CAACW,aAAa,IAAIX,MAAM,CAACY,KAAK,IAAIZ,MAAM,CAACU,QAAQ,MAAMV,MAAM,CAACW,aAAa,IAAIX,MAAM,CAACY,KAAK,IAAIZ,MAAM,CAACU,QAAQ,CAAC,iBACxJnG,OAAA;kBAAK2G,SAAS,EAAC,MAAM;kBAAAI,QAAA,eACnB/G,OAAA;oBAAK2G,SAAS,EAAG,2BACflB,MAAM,CAACwD,SAAS,GACZ,8BAA8B,GAC9B,0BACL,EAAE;oBAAAlC,QAAA,gBACD/G,OAAA;sBAAK2G,SAAS,EAAC,8BAA8B;sBAAAI,QAAA,gBAC3C/G,OAAA;wBAAM2G,SAAS,EAAC,qCAAqC;wBAAAI,QAAA,EAAC;sBAAkB;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC/EzH,OAAA;wBAAM2G,SAAS,EAAG,4CAChBlB,MAAM,CAACwD,SAAS,GACZ,yBAAyB,GACzB,uBACL,EAAE;wBAAAlC,QAAA,EACAtB,MAAM,CAACwD,SAAS,GAAG,WAAW,GAAG;sBAAS;wBAAA3B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACNzH,OAAA;sBAAK2G,SAAS,EAAC,gCAAgC;sBAAAI,QAAA,gBAC7C/G,OAAA;wBACEmJ,GAAG,EAAE1D,MAAM,CAACW,aAAa,IAAIX,MAAM,CAACY,KAAK,IAAIZ,MAAM,CAACU,QAAS;wBAC7DiD,GAAG,EAAC,gBAAgB;wBACpBzC,SAAS,EAAC,sDAAsD;wBAChEC,KAAK,EAAE;0BAAEyC,SAAS,EAAE;wBAAQ,CAAE;wBAC9BC,OAAO,EAAGC,CAAC,IAAK;0BACdA,CAAC,CAACC,MAAM,CAAC5C,KAAK,CAAC2B,OAAO,GAAG,MAAM;0BAC/BgB,CAAC,CAACC,MAAM,CAACC,WAAW,CAAC7C,KAAK,CAAC2B,OAAO,GAAG,OAAO;wBAC9C;sBAAE;wBAAAjB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFzH,OAAA;wBACE2G,SAAS,EAAC,8DAA8D;wBACxEC,KAAK,EAAE;0BAAE2B,OAAO,EAAE;wBAAO,CAAE;wBAAAxB,QAAA,EAC5B;sBAED;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,eAGDzH,OAAA;kBAAK2G,SAAS,EAAC,WAAW;kBAAAI,QAAA,gBACxB/G,OAAA;oBACE2G,SAAS,EAAG,kBACVlB,MAAM,CAACwD,SAAS,GACZ,aAAa,GACb,WACL,EAAE;oBACHrC,KAAK,EAAE;sBACLqB,MAAM,EAAExC,MAAM,CAACwD,SAAS,GACpB,mBAAmB,CAAC;sBAAA,EACpB,mBAAmB,CAAE;oBAC3B,CAAE;oBAAAlC,QAAA,gBAEF/G,OAAA;sBAAK2G,SAAS,EAAC,8BAA8B;sBAAAI,QAAA,gBAC3C/G,OAAA;wBAAK2G,SAAS,EAAG,yDACflB,MAAM,CAACwD,SAAS,GAAG,cAAc,GAAG,YACrC,EAAE;wBAAAlC,QAAA,EACAtB,MAAM,CAACwD,SAAS,gBACfjJ,OAAA,CAACV,OAAO;0BAACqH,SAAS,EAAC;wBAAoB;0BAAAW,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gBAE1CzH,OAAA,CAACT,GAAG;0BAACoH,SAAS,EAAC;wBAAoB;0BAAAW,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBACtC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eACNzH,OAAA;wBAAM2G,SAAS,EAAC,6BAA6B;wBAAAI,QAAA,EAAC;sBAAY;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9D,CAAC,eACNzH,OAAA;sBAAK2G,SAAS,EAAG,oCACflB,MAAM,CAACwD,SAAS,GACZ,qDAAqD,GACrD,+CACL,EAAE;sBAAAlC,QAAA,EACAtB,MAAM,CAACS,UAAU,IAAI;oBAAoB;sBAAAoB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EAEL,CAAChC,MAAM,CAACwD,SAAS,iBAChBjJ,OAAA;oBAAK2G,SAAS,EAAC,sDAAsD;oBAAAI,QAAA,gBACnE/G,OAAA;sBAAK2G,SAAS,EAAC,8BAA8B;sBAAAI,QAAA,gBAC3C/G,OAAA;wBAAK2G,SAAS,EAAC,oEAAoE;wBAAAI,QAAA,eACjF/G,OAAA,CAACV,OAAO;0BAACqH,SAAS,EAAC;wBAAoB;0BAAAW,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC,CAAC,eACNzH,OAAA;wBAAM2G,SAAS,EAAC,6BAA6B;wBAAAI,QAAA,EAAC;sBAAe;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjE,CAAC,eACNzH,OAAA;sBAAK2G,SAAS,EAAC,wFAAwF;sBAAAI,QAAA,EACpGtB,MAAM,CAACQ;oBAAa;sBAAAqB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN,EAGA,CAAChC,MAAM,CAACwD,SAAS,iBAChBjJ,OAAA;oBAAK2G,SAAS,EAAC,MAAM;oBAAAI,QAAA,gBACnB/G,OAAA;sBACE2G,SAAS,EAAG,gHACVhF,mBAAmB,CAAE,YAAWqH,KAAM,EAAC,CAAC,GACpC,gCAAgC,GAChC,2HACL,EAAE;sBACHU,OAAO,EAAEA,CAAA,KAAMnE,gBAAgB,CAACyD,KAAK,EAAEvD,MAAM,CAAE;sBAC/CkE,QAAQ,EAAEhI,mBAAmB,CAAE,YAAWqH,KAAM,EAAC,CAAE;sBAAAjC,QAAA,EAElDpF,mBAAmB,CAAE,YAAWqH,KAAM,EAAC,CAAC,gBACvChJ,OAAA,CAAAE,SAAA;wBAAA6G,QAAA,gBACE/G,OAAA;0BAAK2G,SAAS,EAAC;wBAA8E;0BAAAW,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,0BAEtG;sBAAA,eAAE,CAAC,gBAEHzH,OAAA,CAAAE,SAAA;wBAAA6G,QAAA,gBACE/G,OAAA,CAACN,OAAO;0BAACiH,SAAS,EAAC;wBAAS;0BAAAW,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,mBAEjC;sBAAA,eAAE;oBACH;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACK,CAAC,EAGRhG,YAAY,CAAE,YAAWuH,KAAM,EAAC,CAAC,iBAChChJ,OAAA;sBAAK2G,SAAS,EAAC,yFAAyF;sBAAAI,QAAA,gBACtG/G,OAAA;wBAAK2G,SAAS,EAAC,8BAA8B;wBAAAI,QAAA,gBAC3C/G,OAAA,CAACN,OAAO;0BAACiH,SAAS,EAAC;wBAAuB;0BAAAW,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC7CzH,OAAA;0BAAI2G,SAAS,EAAC,yBAAyB;0BAAAI,QAAA,EAAC;wBAAe;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzD,CAAC,eACNzH,OAAA;wBAAK2G,SAAS,EAAC,+BAA+B;wBAAAI,QAAA,eAC5C/G,OAAA,CAACF,eAAe;0BAAC8J,IAAI,EAAEnI,YAAY,CAAE,YAAWuH,KAAM,EAAC;wBAAE;0BAAA1B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GAxLDhC,MAAM,CAACoE,UAAU,IAAIb,KAAK;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyL5B,CAAC;UAER,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGH,CACN,eAGDzH,OAAA;QACE2G,SAAS,EAAC,YAAY;QACtBC,KAAK,EAAE;UACLkD,aAAa,EAAE5H,MAAM,CAAC4E,UAAU,IAAI,GAAG,GAAG,QAAQ,GAAG;QACvD,CAAE;QAAAC,QAAA,gBAEF/G,OAAA;UACE0J,OAAO,EAAGH,CAAC,IAAK;YACdA,CAAC,CAACQ,cAAc,CAAC,CAAC;YAClBxF,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;YAC9CiC,mBAAmB,CAAC,CAAC;UACvB,CAAE;UACFE,SAAS,EAAC,qNAAqN;UAC/NC,KAAK,EAAE;YACLC,OAAO,EAAE3E,MAAM,CAAC4E,UAAU,IAAI,GAAG,GAAG,WAAW,GAAG,WAAW;YAC7DK,QAAQ,EAAEjF,MAAM,CAAC4E,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;UAChD,CAAE;UACFrE,IAAI,EAAC,QAAQ;UAAAsE,QAAA,gBAEb/G,OAAA,CAACR,MAAM;YACLoH,KAAK,EAAE;cACLc,KAAK,EAAExF,MAAM,CAAC4E,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;cACjDa,MAAM,EAAEzF,MAAM,CAAC4E,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;YAC9C;UAAE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAEJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETzH,OAAA;UACE0J,OAAO,EAAGH,CAAC,IAAK;YACdA,CAAC,CAACQ,cAAc,CAAC,CAAC;YAClBxF,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;YAC7CkC,gBAAgB,CAAC,CAAC;UACpB,CAAE;UACFC,SAAS,EAAC,wNAAwN;UAClOC,KAAK,EAAE;YACLC,OAAO,EAAE3E,MAAM,CAAC4E,UAAU,IAAI,GAAG,GAAG,WAAW,GAAG,WAAW;YAC7DK,QAAQ,EAAEjF,MAAM,CAAC4E,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;UAChD,CAAE;UACFrE,IAAI,EAAC,QAAQ;UAAAsE,QAAA,gBAEb/G,OAAA,CAACX,QAAQ;YACPuH,KAAK,EAAE;cACLc,KAAK,EAAExF,MAAM,CAAC4E,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;cACjDa,MAAM,EAAEzF,MAAM,CAAC4E,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;YAC9C;UAAE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrH,EAAA,CA1tCID,UAAU;EAAA,QACGlB,WAAW,EACXC,WAAW,EACbC,SAAS,EACPC,WAAW,EACJS,WAAW;AAAA;AAAAmK,EAAA,GAL/B7J,UAAU;AA4tChB,eAAeA,UAAU;AAAC,IAAA6J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}