{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"renderTabBar\"],\n  _excluded2 = [\"label\", \"key\"];\n// zombieJ: To compatible with `renderTabBar` usage.\n\nimport * as React from 'react';\nimport TabNavList from '.';\nimport TabContext from \"../TabContext\";\nimport TabPane from \"../TabPanelList/TabPane\";\n// We have to create a TabNavList components.\nexport default function TabNavListWrapper(_ref) {\n  var renderTabBar = _ref.renderTabBar,\n    restProps = _objectWithoutProperties(_ref, _excluded);\n  var _React$useContext = React.useContext(TabContext),\n    tabs = _React$useContext.tabs;\n  if (renderTabBar) {\n    var tabNavBarProps = _objectSpread(_objectSpread({}, restProps), {}, {\n      // Legacy support. We do not use this actually\n      panes: tabs.map(function (_ref2) {\n        var label = _ref2.label,\n          key = _ref2.key,\n          restTabProps = _objectWithoutProperties(_ref2, _excluded2);\n        return /*#__PURE__*/React.createElement(TabPane, _extends({\n          tab: label,\n          key: key,\n          tabKey: key\n        }, restTabProps));\n      })\n    });\n    return renderTabBar(tabNavBarProps, TabNavList);\n  }\n  return /*#__PURE__*/React.createElement(TabNavList, restProps);\n}", "map": {"version": 3, "names": ["_extends", "_objectSpread", "_objectWithoutProperties", "_excluded", "_excluded2", "React", "TabNavList", "TabContext", "TabPane", "TabNavListWrapper", "_ref", "renderTabBar", "restProps", "_React$useContext", "useContext", "tabs", "tabNavBarProps", "panes", "map", "_ref2", "label", "key", "restTabProps", "createElement", "tab", "tabKey"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-tabs/es/TabNavList/Wrapper.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"renderTabBar\"],\n  _excluded2 = [\"label\", \"key\"];\n// zombieJ: To compatible with `renderTabBar` usage.\n\nimport * as React from 'react';\nimport TabNavList from '.';\nimport TabContext from \"../TabContext\";\nimport TabPane from \"../TabPanelList/TabPane\";\n// We have to create a TabNavList components.\nexport default function TabNavListWrapper(_ref) {\n  var renderTabBar = _ref.renderTabBar,\n    restProps = _objectWithoutProperties(_ref, _excluded);\n  var _React$useContext = React.useContext(TabContext),\n    tabs = _React$useContext.tabs;\n  if (renderTabBar) {\n    var tabNavBarProps = _objectSpread(_objectSpread({}, restProps), {}, {\n      // Legacy support. We do not use this actually\n      panes: tabs.map(function (_ref2) {\n        var label = _ref2.label,\n          key = _ref2.key,\n          restTabProps = _objectWithoutProperties(_ref2, _excluded2);\n        return /*#__PURE__*/React.createElement(TabPane, _extends({\n          tab: label,\n          key: key,\n          tabKey: key\n        }, restTabProps));\n      })\n    });\n    return renderTabBar(tabNavBarProps, TabNavList);\n  }\n  return /*#__PURE__*/React.createElement(TabNavList, restProps);\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,cAAc,CAAC;EAC9BC,UAAU,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC;AAC/B;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,GAAG;AAC1B,OAAOC,UAAU,MAAM,eAAe;AACtC,OAAOC,OAAO,MAAM,yBAAyB;AAC7C;AACA,eAAe,SAASC,iBAAiBA,CAACC,IAAI,EAAE;EAC9C,IAAIC,YAAY,GAAGD,IAAI,CAACC,YAAY;IAClCC,SAAS,GAAGV,wBAAwB,CAACQ,IAAI,EAAEP,SAAS,CAAC;EACvD,IAAIU,iBAAiB,GAAGR,KAAK,CAACS,UAAU,CAACP,UAAU,CAAC;IAClDQ,IAAI,GAAGF,iBAAiB,CAACE,IAAI;EAC/B,IAAIJ,YAAY,EAAE;IAChB,IAAIK,cAAc,GAAGf,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEW,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;MACnE;MACAK,KAAK,EAAEF,IAAI,CAACG,GAAG,CAAC,UAAUC,KAAK,EAAE;QAC/B,IAAIC,KAAK,GAAGD,KAAK,CAACC,KAAK;UACrBC,GAAG,GAAGF,KAAK,CAACE,GAAG;UACfC,YAAY,GAAGpB,wBAAwB,CAACiB,KAAK,EAAEf,UAAU,CAAC;QAC5D,OAAO,aAAaC,KAAK,CAACkB,aAAa,CAACf,OAAO,EAAER,QAAQ,CAAC;UACxDwB,GAAG,EAAEJ,KAAK;UACVC,GAAG,EAAEA,GAAG;UACRI,MAAM,EAAEJ;QACV,CAAC,EAAEC,YAAY,CAAC,CAAC;MACnB,CAAC;IACH,CAAC,CAAC;IACF,OAAOX,YAAY,CAACK,cAAc,EAAEV,UAAU,CAAC;EACjD;EACA,OAAO,aAAaD,KAAK,CAACkB,aAAa,CAACjB,UAAU,EAAEM,SAAS,CAAC;AAChE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}