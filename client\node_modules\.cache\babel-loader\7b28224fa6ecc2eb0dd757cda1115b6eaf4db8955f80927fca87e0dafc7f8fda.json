{"ast": null, "code": "/* tslint:disable: no-object-literal-type-assertion */\nimport * as React from 'react';\n// We will never use default, here only to fix TypeScript warning\nvar MentionsContext = /*#__PURE__*/React.createContext(null);\nexport default MentionsContext;", "map": {"version": 3, "names": ["React", "MentionsContext", "createContext"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-mentions/es/MentionsContext.js"], "sourcesContent": ["/* tslint:disable: no-object-literal-type-assertion */\nimport * as React from 'react';\n// We will never use default, here only to fix TypeScript warning\nvar MentionsContext = /*#__PURE__*/React.createContext(null);\nexport default MentionsContext;"], "mappings": "AAAA;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B;AACA,IAAIC,eAAe,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAC5D,eAAeD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}