{"ast": null, "code": "import classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport { render, unmount } from \"rc-util/es/React/render\";\nimport raf from \"rc-util/es/raf\";\nimport * as React from 'react';\nimport { getTargetWaveColor } from './util';\nimport { TARGET_CLS } from './interface';\nfunction validateNum(value) {\n  return Number.isNaN(value) ? 0 : value;\n}\nconst WaveEffect = props => {\n  const {\n    className,\n    target,\n    component\n  } = props;\n  const divRef = React.useRef(null);\n  const [color, setWaveColor] = React.useState(null);\n  const [borderRadius, setBorderRadius] = React.useState([]);\n  const [left, setLeft] = React.useState(0);\n  const [top, setTop] = React.useState(0);\n  const [width, setWidth] = React.useState(0);\n  const [height, setHeight] = React.useState(0);\n  const [enabled, setEnabled] = React.useState(false);\n  const waveStyle = {\n    left,\n    top,\n    width,\n    height,\n    borderRadius: borderRadius.map(radius => \"\".concat(radius, \"px\")).join(' ')\n  };\n  if (color) {\n    waveStyle['--wave-color'] = color;\n  }\n  function syncPos() {\n    const nodeStyle = getComputedStyle(target);\n    // Get wave color from target\n    setWaveColor(getTargetWaveColor(target));\n    const isStatic = nodeStyle.position === 'static';\n    // Rect\n    const {\n      borderLeftWidth,\n      borderTopWidth\n    } = nodeStyle;\n    setLeft(isStatic ? target.offsetLeft : validateNum(-parseFloat(borderLeftWidth)));\n    setTop(isStatic ? target.offsetTop : validateNum(-parseFloat(borderTopWidth)));\n    setWidth(target.offsetWidth);\n    setHeight(target.offsetHeight);\n    // Get border radius\n    const {\n      borderTopLeftRadius,\n      borderTopRightRadius,\n      borderBottomLeftRadius,\n      borderBottomRightRadius\n    } = nodeStyle;\n    setBorderRadius([borderTopLeftRadius, borderTopRightRadius, borderBottomRightRadius, borderBottomLeftRadius].map(radius => validateNum(parseFloat(radius))));\n  }\n  React.useEffect(() => {\n    if (target) {\n      // We need delay to check position here\n      // since UI may change after click\n      const id = raf(() => {\n        syncPos();\n        setEnabled(true);\n      });\n      // Add resize observer to follow size\n      let resizeObserver;\n      if (typeof ResizeObserver !== 'undefined') {\n        resizeObserver = new ResizeObserver(syncPos);\n        resizeObserver.observe(target);\n      }\n      return () => {\n        raf.cancel(id);\n        resizeObserver === null || resizeObserver === void 0 ? void 0 : resizeObserver.disconnect();\n      };\n    }\n  }, []);\n  if (!enabled) {\n    return null;\n  }\n  const isSmallComponent = (component === 'Checkbox' || component === 'Radio') && (target === null || target === void 0 ? void 0 : target.classList.contains(TARGET_CLS));\n  return /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: true,\n    motionAppear: true,\n    motionName: \"wave-motion\",\n    motionDeadline: 5000,\n    onAppearEnd: (_, event) => {\n      var _a;\n      if (event.deadline || event.propertyName === 'opacity') {\n        const holder = (_a = divRef.current) === null || _a === void 0 ? void 0 : _a.parentElement;\n        unmount(holder).then(() => {\n          holder === null || holder === void 0 ? void 0 : holder.remove();\n        });\n      }\n      return false;\n    }\n  }, _ref => {\n    let {\n      className: motionClassName\n    } = _ref;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      ref: divRef,\n      className: classNames(className, {\n        'wave-quick': isSmallComponent\n      }, motionClassName),\n      style: waveStyle\n    });\n  });\n};\nconst showWaveEffect = (target, info) => {\n  var _a;\n  const {\n    component\n  } = info;\n  // Skip for unchecked checkbox\n  if (component === 'Checkbox' && !((_a = target.querySelector('input')) === null || _a === void 0 ? void 0 : _a.checked)) {\n    return;\n  }\n  // Create holder\n  const holder = document.createElement('div');\n  holder.style.position = 'absolute';\n  holder.style.left = '0px';\n  holder.style.top = '0px';\n  target === null || target === void 0 ? void 0 : target.insertBefore(holder, target === null || target === void 0 ? void 0 : target.firstChild);\n  render( /*#__PURE__*/React.createElement(WaveEffect, Object.assign({}, info, {\n    target: target\n  })), holder);\n};\nexport default showWaveEffect;", "map": {"version": 3, "names": ["classNames", "CSSMotion", "render", "unmount", "raf", "React", "getTargetWaveColor", "TARGET_CLS", "validateNum", "value", "Number", "isNaN", "WaveEffect", "props", "className", "target", "component", "divRef", "useRef", "color", "setWaveColor", "useState", "borderRadius", "setBorderRadius", "left", "setLeft", "top", "setTop", "width", "<PERSON><PERSON><PERSON><PERSON>", "height", "setHeight", "enabled", "setEnabled", "waveStyle", "map", "radius", "concat", "join", "syncPos", "nodeStyle", "getComputedStyle", "isStatic", "position", "borderLeftWidth", "borderTopWidth", "offsetLeft", "parseFloat", "offsetTop", "offsetWidth", "offsetHeight", "borderTopLeftRadius", "borderTopRightRadius", "borderBottomLeftRadius", "borderBottomRightRadius", "useEffect", "id", "resizeObserver", "ResizeObserver", "observe", "cancel", "disconnect", "isSmallComponent", "classList", "contains", "createElement", "visible", "motionAppear", "motionName", "motionDeadline", "onAppearEnd", "_", "event", "_a", "deadline", "propertyName", "holder", "current", "parentElement", "then", "remove", "_ref", "motionClassName", "ref", "style", "showWaveEffect", "info", "querySelector", "checked", "document", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "Object", "assign"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/_util/wave/WaveEffect.js"], "sourcesContent": ["import classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport { render, unmount } from \"rc-util/es/React/render\";\nimport raf from \"rc-util/es/raf\";\nimport * as React from 'react';\nimport { getTargetWaveColor } from './util';\nimport { TARGET_CLS } from './interface';\nfunction validateNum(value) {\n  return Number.isNaN(value) ? 0 : value;\n}\nconst WaveEffect = props => {\n  const {\n    className,\n    target,\n    component\n  } = props;\n  const divRef = React.useRef(null);\n  const [color, setWaveColor] = React.useState(null);\n  const [borderRadius, setBorderRadius] = React.useState([]);\n  const [left, setLeft] = React.useState(0);\n  const [top, setTop] = React.useState(0);\n  const [width, setWidth] = React.useState(0);\n  const [height, setHeight] = React.useState(0);\n  const [enabled, setEnabled] = React.useState(false);\n  const waveStyle = {\n    left,\n    top,\n    width,\n    height,\n    borderRadius: borderRadius.map(radius => `${radius}px`).join(' ')\n  };\n  if (color) {\n    waveStyle['--wave-color'] = color;\n  }\n  function syncPos() {\n    const nodeStyle = getComputedStyle(target);\n    // Get wave color from target\n    setWaveColor(getTargetWaveColor(target));\n    const isStatic = nodeStyle.position === 'static';\n    // Rect\n    const {\n      borderLeftWidth,\n      borderTopWidth\n    } = nodeStyle;\n    setLeft(isStatic ? target.offsetLeft : validateNum(-parseFloat(borderLeftWidth)));\n    setTop(isStatic ? target.offsetTop : validateNum(-parseFloat(borderTopWidth)));\n    setWidth(target.offsetWidth);\n    setHeight(target.offsetHeight);\n    // Get border radius\n    const {\n      borderTopLeftRadius,\n      borderTopRightRadius,\n      borderBottomLeftRadius,\n      borderBottomRightRadius\n    } = nodeStyle;\n    setBorderRadius([borderTopLeftRadius, borderTopRightRadius, borderBottomRightRadius, borderBottomLeftRadius].map(radius => validateNum(parseFloat(radius))));\n  }\n  React.useEffect(() => {\n    if (target) {\n      // We need delay to check position here\n      // since UI may change after click\n      const id = raf(() => {\n        syncPos();\n        setEnabled(true);\n      });\n      // Add resize observer to follow size\n      let resizeObserver;\n      if (typeof ResizeObserver !== 'undefined') {\n        resizeObserver = new ResizeObserver(syncPos);\n        resizeObserver.observe(target);\n      }\n      return () => {\n        raf.cancel(id);\n        resizeObserver === null || resizeObserver === void 0 ? void 0 : resizeObserver.disconnect();\n      };\n    }\n  }, []);\n  if (!enabled) {\n    return null;\n  }\n  const isSmallComponent = (component === 'Checkbox' || component === 'Radio') && (target === null || target === void 0 ? void 0 : target.classList.contains(TARGET_CLS));\n  return /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: true,\n    motionAppear: true,\n    motionName: \"wave-motion\",\n    motionDeadline: 5000,\n    onAppearEnd: (_, event) => {\n      var _a;\n      if (event.deadline || event.propertyName === 'opacity') {\n        const holder = (_a = divRef.current) === null || _a === void 0 ? void 0 : _a.parentElement;\n        unmount(holder).then(() => {\n          holder === null || holder === void 0 ? void 0 : holder.remove();\n        });\n      }\n      return false;\n    }\n  }, _ref => {\n    let {\n      className: motionClassName\n    } = _ref;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      ref: divRef,\n      className: classNames(className, {\n        'wave-quick': isSmallComponent\n      }, motionClassName),\n      style: waveStyle\n    });\n  });\n};\nconst showWaveEffect = (target, info) => {\n  var _a;\n  const {\n    component\n  } = info;\n  // Skip for unchecked checkbox\n  if (component === 'Checkbox' && !((_a = target.querySelector('input')) === null || _a === void 0 ? void 0 : _a.checked)) {\n    return;\n  }\n  // Create holder\n  const holder = document.createElement('div');\n  holder.style.position = 'absolute';\n  holder.style.left = '0px';\n  holder.style.top = '0px';\n  target === null || target === void 0 ? void 0 : target.insertBefore(holder, target === null || target === void 0 ? void 0 : target.firstChild);\n  render( /*#__PURE__*/React.createElement(WaveEffect, Object.assign({}, info, {\n    target: target\n  })), holder);\n};\nexport default showWaveEffect;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,WAAW;AACjC,SAASC,MAAM,EAAEC,OAAO,QAAQ,yBAAyB;AACzD,OAAOC,GAAG,MAAM,gBAAgB;AAChC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,QAAQ;AAC3C,SAASC,UAAU,QAAQ,aAAa;AACxC,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC1B,OAAOC,MAAM,CAACC,KAAK,CAACF,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK;AACxC;AACA,MAAMG,UAAU,GAAGC,KAAK,IAAI;EAC1B,MAAM;IACJC,SAAS;IACTC,MAAM;IACNC;EACF,CAAC,GAAGH,KAAK;EACT,MAAMI,MAAM,GAAGZ,KAAK,CAACa,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM,CAACC,KAAK,EAAEC,YAAY,CAAC,GAAGf,KAAK,CAACgB,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGlB,KAAK,CAACgB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACG,IAAI,EAAEC,OAAO,CAAC,GAAGpB,KAAK,CAACgB,QAAQ,CAAC,CAAC,CAAC;EACzC,MAAM,CAACK,GAAG,EAAEC,MAAM,CAAC,GAAGtB,KAAK,CAACgB,QAAQ,CAAC,CAAC,CAAC;EACvC,MAAM,CAACO,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,KAAK,CAACgB,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACS,MAAM,EAAEC,SAAS,CAAC,GAAG1B,KAAK,CAACgB,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAG5B,KAAK,CAACgB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMa,SAAS,GAAG;IAChBV,IAAI;IACJE,GAAG;IACHE,KAAK;IACLE,MAAM;IACNR,YAAY,EAAEA,YAAY,CAACa,GAAG,CAACC,MAAM,OAAAC,MAAA,CAAOD,MAAM,OAAI,CAAC,CAACE,IAAI,CAAC,GAAG;EAClE,CAAC;EACD,IAAInB,KAAK,EAAE;IACTe,SAAS,CAAC,cAAc,CAAC,GAAGf,KAAK;EACnC;EACA,SAASoB,OAAOA,CAAA,EAAG;IACjB,MAAMC,SAAS,GAAGC,gBAAgB,CAAC1B,MAAM,CAAC;IAC1C;IACAK,YAAY,CAACd,kBAAkB,CAACS,MAAM,CAAC,CAAC;IACxC,MAAM2B,QAAQ,GAAGF,SAAS,CAACG,QAAQ,KAAK,QAAQ;IAChD;IACA,MAAM;MACJC,eAAe;MACfC;IACF,CAAC,GAAGL,SAAS;IACbf,OAAO,CAACiB,QAAQ,GAAG3B,MAAM,CAAC+B,UAAU,GAAGtC,WAAW,CAAC,CAACuC,UAAU,CAACH,eAAe,CAAC,CAAC,CAAC;IACjFjB,MAAM,CAACe,QAAQ,GAAG3B,MAAM,CAACiC,SAAS,GAAGxC,WAAW,CAAC,CAACuC,UAAU,CAACF,cAAc,CAAC,CAAC,CAAC;IAC9EhB,QAAQ,CAACd,MAAM,CAACkC,WAAW,CAAC;IAC5BlB,SAAS,CAAChB,MAAM,CAACmC,YAAY,CAAC;IAC9B;IACA,MAAM;MACJC,mBAAmB;MACnBC,oBAAoB;MACpBC,sBAAsB;MACtBC;IACF,CAAC,GAAGd,SAAS;IACbjB,eAAe,CAAC,CAAC4B,mBAAmB,EAAEC,oBAAoB,EAAEE,uBAAuB,EAAED,sBAAsB,CAAC,CAAClB,GAAG,CAACC,MAAM,IAAI5B,WAAW,CAACuC,UAAU,CAACX,MAAM,CAAC,CAAC,CAAC,CAAC;EAC9J;EACA/B,KAAK,CAACkD,SAAS,CAAC,MAAM;IACpB,IAAIxC,MAAM,EAAE;MACV;MACA;MACA,MAAMyC,EAAE,GAAGpD,GAAG,CAAC,MAAM;QACnBmC,OAAO,CAAC,CAAC;QACTN,UAAU,CAAC,IAAI,CAAC;MAClB,CAAC,CAAC;MACF;MACA,IAAIwB,cAAc;MAClB,IAAI,OAAOC,cAAc,KAAK,WAAW,EAAE;QACzCD,cAAc,GAAG,IAAIC,cAAc,CAACnB,OAAO,CAAC;QAC5CkB,cAAc,CAACE,OAAO,CAAC5C,MAAM,CAAC;MAChC;MACA,OAAO,MAAM;QACXX,GAAG,CAACwD,MAAM,CAACJ,EAAE,CAAC;QACdC,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACI,UAAU,CAAC,CAAC;MAC7F,CAAC;IACH;EACF,CAAC,EAAE,EAAE,CAAC;EACN,IAAI,CAAC7B,OAAO,EAAE;IACZ,OAAO,IAAI;EACb;EACA,MAAM8B,gBAAgB,GAAG,CAAC9C,SAAS,KAAK,UAAU,IAAIA,SAAS,KAAK,OAAO,MAAMD,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACgD,SAAS,CAACC,QAAQ,CAACzD,UAAU,CAAC,CAAC;EACvK,OAAO,aAAaF,KAAK,CAAC4D,aAAa,CAAChE,SAAS,EAAE;IACjDiE,OAAO,EAAE,IAAI;IACbC,YAAY,EAAE,IAAI;IAClBC,UAAU,EAAE,aAAa;IACzBC,cAAc,EAAE,IAAI;IACpBC,WAAW,EAAEA,CAACC,CAAC,EAAEC,KAAK,KAAK;MACzB,IAAIC,EAAE;MACN,IAAID,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACG,YAAY,KAAK,SAAS,EAAE;QACtD,MAAMC,MAAM,GAAG,CAACH,EAAE,GAAGxD,MAAM,CAAC4D,OAAO,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,aAAa;QAC1F3E,OAAO,CAACyE,MAAM,CAAC,CAACG,IAAI,CAAC,MAAM;UACzBH,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACI,MAAM,CAAC,CAAC;QACjE,CAAC,CAAC;MACJ;MACA,OAAO,KAAK;IACd;EACF,CAAC,EAAEC,IAAI,IAAI;IACT,IAAI;MACFnE,SAAS,EAAEoE;IACb,CAAC,GAAGD,IAAI;IACR,OAAO,aAAa5E,KAAK,CAAC4D,aAAa,CAAC,KAAK,EAAE;MAC7CkB,GAAG,EAAElE,MAAM;MACXH,SAAS,EAAEd,UAAU,CAACc,SAAS,EAAE;QAC/B,YAAY,EAAEgD;MAChB,CAAC,EAAEoB,eAAe,CAAC;MACnBE,KAAK,EAAElD;IACT,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;AACD,MAAMmD,cAAc,GAAGA,CAACtE,MAAM,EAAEuE,IAAI,KAAK;EACvC,IAAIb,EAAE;EACN,MAAM;IACJzD;EACF,CAAC,GAAGsE,IAAI;EACR;EACA,IAAItE,SAAS,KAAK,UAAU,IAAI,EAAE,CAACyD,EAAE,GAAG1D,MAAM,CAACwE,aAAa,CAAC,OAAO,CAAC,MAAM,IAAI,IAAId,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACe,OAAO,CAAC,EAAE;IACvH;EACF;EACA;EACA,MAAMZ,MAAM,GAAGa,QAAQ,CAACxB,aAAa,CAAC,KAAK,CAAC;EAC5CW,MAAM,CAACQ,KAAK,CAACzC,QAAQ,GAAG,UAAU;EAClCiC,MAAM,CAACQ,KAAK,CAAC5D,IAAI,GAAG,KAAK;EACzBoD,MAAM,CAACQ,KAAK,CAAC1D,GAAG,GAAG,KAAK;EACxBX,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC2E,YAAY,CAACd,MAAM,EAAE7D,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC4E,UAAU,CAAC;EAC9IzF,MAAM,EAAE,aAAaG,KAAK,CAAC4D,aAAa,CAACrD,UAAU,EAAEgF,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEP,IAAI,EAAE;IAC3EvE,MAAM,EAAEA;EACV,CAAC,CAAC,CAAC,EAAE6D,MAAM,CAAC;AACd,CAAC;AACD,eAAeS,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}