{"ast": null, "code": "import { ColorBlock, Color as RcColor } from '@rc-component/color-picker';\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport React, { useMemo } from 'react';\nimport Collapse from '../../collapse';\nimport { useLocale } from '../../locale';\nimport theme from '../../theme';\nimport { generateColor } from '../util';\nconst genPresetColor = list => list.map(value => {\n  value.colors = value.colors.map(generateColor);\n  return value;\n});\nconst isBright = (value, bgColorToken) => {\n  const {\n    r,\n    g,\n    b,\n    a\n  } = value.toRgb();\n  const hsv = new RcColor(value.toRgbString()).onBackground(bgColorToken).toHsv();\n  if (a <= 0.5) {\n    // Adapted to dark mode\n    return hsv.v > 0.5;\n  }\n  return r * 0.299 + g * 0.587 + b * 0.114 > 192;\n};\nconst ColorPresets = _ref => {\n  let {\n    prefixCls,\n    presets,\n    value: color,\n    onChange\n  } = _ref;\n  const [locale] = useLocale('ColorPicker');\n  const {\n    token: {\n      colorBgElevated\n    }\n  } = theme.useToken();\n  const [presetsValue] = useMergedState(genPresetColor(presets), {\n    value: genPresetColor(presets),\n    postState: genPresetColor\n  });\n  const colorPresetsPrefixCls = \"\".concat(prefixCls, \"-presets\");\n  const activeKeys = useMemo(() => presetsValue.map(preset => \"panel-\".concat(preset.label)), [presetsValue]);\n  const handleClick = colorValue => {\n    onChange === null || onChange === void 0 ? void 0 : onChange(colorValue);\n  };\n  const items = presetsValue.map(preset => {\n    var _a;\n    return {\n      key: \"panel-\".concat(preset.label),\n      label: /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(colorPresetsPrefixCls, \"-label\")\n      }, preset === null || preset === void 0 ? void 0 : preset.label),\n      children: /*#__PURE__*/React.createElement(\"div\", {\n        className: \"\".concat(colorPresetsPrefixCls, \"-items\")\n      }, Array.isArray(preset === null || preset === void 0 ? void 0 : preset.colors) && ((_a = preset.colors) === null || _a === void 0 ? void 0 : _a.length) > 0 ? preset.colors.map(presetColor => /*#__PURE__*/React.createElement(ColorBlock, {\n        key: \"preset-\".concat(presetColor.toHexString()),\n        color: generateColor(presetColor).toRgbString(),\n        prefixCls: prefixCls,\n        className: classNames(\"\".concat(colorPresetsPrefixCls, \"-color\"), {\n          [\"\".concat(colorPresetsPrefixCls, \"-color-checked\")]: presetColor.toHexString() === (color === null || color === void 0 ? void 0 : color.toHexString()),\n          [\"\".concat(colorPresetsPrefixCls, \"-color-bright\")]: isBright(presetColor, colorBgElevated)\n        }),\n        onClick: () => handleClick(presetColor)\n      })) : /*#__PURE__*/React.createElement(\"span\", {\n        className: \"\".concat(colorPresetsPrefixCls, \"-empty\")\n      }, locale.presetEmpty))\n    };\n  });\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: colorPresetsPrefixCls\n  }, /*#__PURE__*/React.createElement(Collapse, {\n    defaultActiveKey: activeKeys,\n    ghost: true,\n    items: items\n  }));\n};\nexport default ColorPresets;", "map": {"version": 3, "names": ["ColorBlock", "Color", "RcColor", "classNames", "useMergedState", "React", "useMemo", "Collapse", "useLocale", "theme", "generateColor", "genPresetColor", "list", "map", "value", "colors", "isBright", "bgColorToken", "r", "g", "b", "a", "toRgb", "hsv", "toRgbString", "onBackground", "toHsv", "v", "ColorPresets", "_ref", "prefixCls", "presets", "color", "onChange", "locale", "token", "colorBgElevated", "useToken", "presetsValue", "postState", "colorPresetsPrefixCls", "concat", "activeKeys", "preset", "label", "handleClick", "colorValue", "items", "_a", "key", "createElement", "className", "children", "Array", "isArray", "length", "presetColor", "toHexString", "onClick", "presetEmpty", "defaultActiveKey", "ghost"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/color-picker/components/ColorPresets.js"], "sourcesContent": ["import { ColorBlock, Color as RcColor } from '@rc-component/color-picker';\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport React, { useMemo } from 'react';\nimport Collapse from '../../collapse';\nimport { useLocale } from '../../locale';\nimport theme from '../../theme';\nimport { generateColor } from '../util';\nconst genPresetColor = list => list.map(value => {\n  value.colors = value.colors.map(generateColor);\n  return value;\n});\nconst isBright = (value, bgColorToken) => {\n  const {\n    r,\n    g,\n    b,\n    a\n  } = value.toRgb();\n  const hsv = new RcColor(value.toRgbString()).onBackground(bgColorToken).toHsv();\n  if (a <= 0.5) {\n    // Adapted to dark mode\n    return hsv.v > 0.5;\n  }\n  return r * 0.299 + g * 0.587 + b * 0.114 > 192;\n};\nconst ColorPresets = _ref => {\n  let {\n    prefixCls,\n    presets,\n    value: color,\n    onChange\n  } = _ref;\n  const [locale] = useLocale('ColorPicker');\n  const {\n    token: {\n      colorBgElevated\n    }\n  } = theme.useToken();\n  const [presetsValue] = useMergedState(genPresetColor(presets), {\n    value: genPresetColor(presets),\n    postState: genPresetColor\n  });\n  const colorPresetsPrefixCls = `${prefixCls}-presets`;\n  const activeKeys = useMemo(() => presetsValue.map(preset => `panel-${preset.label}`), [presetsValue]);\n  const handleClick = colorValue => {\n    onChange === null || onChange === void 0 ? void 0 : onChange(colorValue);\n  };\n  const items = presetsValue.map(preset => {\n    var _a;\n    return {\n      key: `panel-${preset.label}`,\n      label: /*#__PURE__*/React.createElement(\"div\", {\n        className: `${colorPresetsPrefixCls}-label`\n      }, preset === null || preset === void 0 ? void 0 : preset.label),\n      children: /*#__PURE__*/React.createElement(\"div\", {\n        className: `${colorPresetsPrefixCls}-items`\n      }, Array.isArray(preset === null || preset === void 0 ? void 0 : preset.colors) && ((_a = preset.colors) === null || _a === void 0 ? void 0 : _a.length) > 0 ? preset.colors.map(presetColor => /*#__PURE__*/React.createElement(ColorBlock, {\n        key: `preset-${presetColor.toHexString()}`,\n        color: generateColor(presetColor).toRgbString(),\n        prefixCls: prefixCls,\n        className: classNames(`${colorPresetsPrefixCls}-color`, {\n          [`${colorPresetsPrefixCls}-color-checked`]: presetColor.toHexString() === (color === null || color === void 0 ? void 0 : color.toHexString()),\n          [`${colorPresetsPrefixCls}-color-bright`]: isBright(presetColor, colorBgElevated)\n        }),\n        onClick: () => handleClick(presetColor)\n      })) : /*#__PURE__*/React.createElement(\"span\", {\n        className: `${colorPresetsPrefixCls}-empty`\n      }, locale.presetEmpty))\n    };\n  });\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: colorPresetsPrefixCls\n  }, /*#__PURE__*/React.createElement(Collapse, {\n    defaultActiveKey: activeKeys,\n    ghost: true,\n    items: items\n  }));\n};\nexport default ColorPresets;"], "mappings": "AAAA,SAASA,UAAU,EAAEC,KAAK,IAAIC,OAAO,QAAQ,4BAA4B;AACzE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,KAAK,IAAIC,OAAO,QAAQ,OAAO;AACtC,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,SAASC,SAAS,QAAQ,cAAc;AACxC,OAAOC,KAAK,MAAM,aAAa;AAC/B,SAASC,aAAa,QAAQ,SAAS;AACvC,MAAMC,cAAc,GAAGC,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,KAAK,IAAI;EAC/CA,KAAK,CAACC,MAAM,GAAGD,KAAK,CAACC,MAAM,CAACF,GAAG,CAACH,aAAa,CAAC;EAC9C,OAAOI,KAAK;AACd,CAAC,CAAC;AACF,MAAME,QAAQ,GAAGA,CAACF,KAAK,EAAEG,YAAY,KAAK;EACxC,MAAM;IACJC,CAAC;IACDC,CAAC;IACDC,CAAC;IACDC;EACF,CAAC,GAAGP,KAAK,CAACQ,KAAK,CAAC,CAAC;EACjB,MAAMC,GAAG,GAAG,IAAIrB,OAAO,CAACY,KAAK,CAACU,WAAW,CAAC,CAAC,CAAC,CAACC,YAAY,CAACR,YAAY,CAAC,CAACS,KAAK,CAAC,CAAC;EAC/E,IAAIL,CAAC,IAAI,GAAG,EAAE;IACZ;IACA,OAAOE,GAAG,CAACI,CAAC,GAAG,GAAG;EACpB;EACA,OAAOT,CAAC,GAAG,KAAK,GAAGC,CAAC,GAAG,KAAK,GAAGC,CAAC,GAAG,KAAK,GAAG,GAAG;AAChD,CAAC;AACD,MAAMQ,YAAY,GAAGC,IAAI,IAAI;EAC3B,IAAI;IACFC,SAAS;IACTC,OAAO;IACPjB,KAAK,EAAEkB,KAAK;IACZC;EACF,CAAC,GAAGJ,IAAI;EACR,MAAM,CAACK,MAAM,CAAC,GAAG1B,SAAS,CAAC,aAAa,CAAC;EACzC,MAAM;IACJ2B,KAAK,EAAE;MACLC;IACF;EACF,CAAC,GAAG3B,KAAK,CAAC4B,QAAQ,CAAC,CAAC;EACpB,MAAM,CAACC,YAAY,CAAC,GAAGlC,cAAc,CAACO,cAAc,CAACoB,OAAO,CAAC,EAAE;IAC7DjB,KAAK,EAAEH,cAAc,CAACoB,OAAO,CAAC;IAC9BQ,SAAS,EAAE5B;EACb,CAAC,CAAC;EACF,MAAM6B,qBAAqB,MAAAC,MAAA,CAAMX,SAAS,aAAU;EACpD,MAAMY,UAAU,GAAGpC,OAAO,CAAC,MAAMgC,YAAY,CAACzB,GAAG,CAAC8B,MAAM,aAAAF,MAAA,CAAaE,MAAM,CAACC,KAAK,CAAE,CAAC,EAAE,CAACN,YAAY,CAAC,CAAC;EACrG,MAAMO,WAAW,GAAGC,UAAU,IAAI;IAChCb,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACa,UAAU,CAAC;EAC1E,CAAC;EACD,MAAMC,KAAK,GAAGT,YAAY,CAACzB,GAAG,CAAC8B,MAAM,IAAI;IACvC,IAAIK,EAAE;IACN,OAAO;MACLC,GAAG,WAAAR,MAAA,CAAWE,MAAM,CAACC,KAAK,CAAE;MAC5BA,KAAK,EAAE,aAAavC,KAAK,CAAC6C,aAAa,CAAC,KAAK,EAAE;QAC7CC,SAAS,KAAAV,MAAA,CAAKD,qBAAqB;MACrC,CAAC,EAAEG,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACC,KAAK,CAAC;MAChEQ,QAAQ,EAAE,aAAa/C,KAAK,CAAC6C,aAAa,CAAC,KAAK,EAAE;QAChDC,SAAS,KAAAV,MAAA,CAAKD,qBAAqB;MACrC,CAAC,EAAEa,KAAK,CAACC,OAAO,CAACX,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC5B,MAAM,CAAC,IAAI,CAAC,CAACiC,EAAE,GAAGL,MAAM,CAAC5B,MAAM,MAAM,IAAI,IAAIiC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACO,MAAM,IAAI,CAAC,GAAGZ,MAAM,CAAC5B,MAAM,CAACF,GAAG,CAAC2C,WAAW,IAAI,aAAanD,KAAK,CAAC6C,aAAa,CAAClD,UAAU,EAAE;QAC3OiD,GAAG,YAAAR,MAAA,CAAYe,WAAW,CAACC,WAAW,CAAC,CAAC,CAAE;QAC1CzB,KAAK,EAAEtB,aAAa,CAAC8C,WAAW,CAAC,CAAChC,WAAW,CAAC,CAAC;QAC/CM,SAAS,EAAEA,SAAS;QACpBqB,SAAS,EAAEhD,UAAU,IAAAsC,MAAA,CAAID,qBAAqB,aAAU;UACtD,IAAAC,MAAA,CAAID,qBAAqB,sBAAmBgB,WAAW,CAACC,WAAW,CAAC,CAAC,MAAMzB,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACyB,WAAW,CAAC,CAAC,CAAC;UAC7I,IAAAhB,MAAA,CAAID,qBAAqB,qBAAkBxB,QAAQ,CAACwC,WAAW,EAAEpB,eAAe;QAClF,CAAC,CAAC;QACFsB,OAAO,EAAEA,CAAA,KAAMb,WAAW,CAACW,WAAW;MACxC,CAAC,CAAC,CAAC,GAAG,aAAanD,KAAK,CAAC6C,aAAa,CAAC,MAAM,EAAE;QAC7CC,SAAS,KAAAV,MAAA,CAAKD,qBAAqB;MACrC,CAAC,EAAEN,MAAM,CAACyB,WAAW,CAAC;IACxB,CAAC;EACH,CAAC,CAAC;EACF,OAAO,aAAatD,KAAK,CAAC6C,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAEX;EACb,CAAC,EAAE,aAAanC,KAAK,CAAC6C,aAAa,CAAC3C,QAAQ,EAAE;IAC5CqD,gBAAgB,EAAElB,UAAU;IAC5BmB,KAAK,EAAE,IAAI;IACXd,KAAK,EAAEA;EACT,CAAC,CAAC,CAAC;AACL,CAAC;AACD,eAAenB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}