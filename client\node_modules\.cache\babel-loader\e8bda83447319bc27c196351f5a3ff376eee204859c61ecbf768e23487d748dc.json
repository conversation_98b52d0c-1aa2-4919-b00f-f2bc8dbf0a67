{"ast": null, "code": "// Kiswahili translations for Primary Kiswahili Medium level\nexport const kiswahiliTranslations = {\n  // Navigation and Common UI\n  navigation: {\n    home: \"Nyumba<PERSON>\",\n    quizzes: \"<PERSON><PERSON><PERSON>\",\n    videos: \"Video\",\n    ranking: \"<PERSON><PERSON> ya Ushindi\",\n    forum: \"<PERSON><PERSON><PERSON><PERSON>\",\n    profile: \"<PERSON><PERSON><PERSON>\",\n    logout: \"Ondoka\",\n    settings: \"Mipangilio\"\n  },\n  // Authentication\n  auth: {\n    login: \"Ingia\",\n    register: \"<PERSON><PERSON><PERSON><PERSON>\",\n    username: \"<PERSON><PERSON> la Mtumiaji\",\n    password: \"<PERSON><PERSON><PERSON><PERSON>\",\n    firstName: \"<PERSON><PERSON>wan<PERSON>\",\n    lastName: \"<PERSON><PERSON>wish<PERSON>\",\n    school: \"<PERSON><PERSON>\",\n    level: \"<PERSON><PERSON><PERSON>\",\n    class: \"Darasa\",\n    phoneNumber: \"<PERSON><PERSON> ya Simu\",\n    email: \"Barua Pepe\"\n  },\n  // Quiz System\n  quiz: {\n    startQuiz: \"Anza Mtihani\",\n    submitQuiz: \"<PERSON><PERSON><PERSON>ihani\",\n    nextQuestion: \"Swali Lijalo\",\n    previousQuestion: \"<PERSON>wal<PERSON>\",\n    timeRemaining: \"<PERSON><PERSON>\",\n    score: \"<PERSON><PERSON>\",\n    correct: \"<PERSON><PERSON><PERSON>\",\n    incorrect: \"Makosa\",\n    wellDone: \"Hongera!\",\n    tryAgain: \"<PERSON><PERSON><PERSON> Ten<PERSON>\",\n    minutes: \"<PERSON><PERSON><PERSON>\",\n    seconds: \"Se<PERSON><PERSON>\"\n  },\n  // Subjects (Primary Level in Kiswahili)\n  subjects: {\n    \"Hisabati\": \"Hisabati\",\n    \"Sayansi na Teknolojia\": \"Sayansi na Teknolojia\",\n    \"Jiografia\": \"Jiografia\",\n    \"Kiswahili\": \"Kiswahili\",\n    \"Maarifa ya Jamii\": \"Maarifa ya Jamii\",\n    \"Kiingereza\": \"Kiingereza\",\n    \"Dini\": \"Dini\",\n    \"Hesabu\": \"Hesabu\",\n    \"Michezo na Sanaa\": \"Michezo na Sanaa\",\n    \"Afya na Mazingira\": \"Afya na Mazingira\",\n    \"Uraia na Maadili\": \"Uraia na Maadili\",\n    \"Kifaransa\": \"Kifaransa\",\n    \"Historia ya Tanzania\": \"Historia ya Tanzania\"\n  },\n  // Classes\n  classes: {\n    \"1\": \"Darasa la Kwanza\",\n    \"2\": \"Darasa la Pili\",\n    \"3\": \"Darasa la Tatu\",\n    \"4\": \"Darasa la Nne\",\n    \"5\": \"Darasa la Tano\",\n    \"6\": \"Darasa la Sita\",\n    \"7\": \"Darasa la Saba\"\n  },\n  // Common Messages\n  messages: {\n    welcome: \"Karibu\",\n    loading: \"Inapakia...\",\n    error: \"Hitilafu\",\n    success: \"Mafanikio\",\n    save: \"Hifadhi\",\n    cancel: \"Ghairi\",\n    delete: \"Futa\",\n    edit: \"Hariri\",\n    view: \"Ona\",\n    search: \"Tafuta\",\n    filter: \"Chuja\",\n    refresh: \"Onyesha Upya\"\n  },\n  // Study Materials\n  studyMaterials: {\n    videos: \"Video za Masomo\",\n    documents: \"Hati za Masomo\",\n    notes: \"Maelezo\",\n    exercises: \"Mazoezi\",\n    download: \"Pakua\",\n    watch: \"Tazama\",\n    read: \"Soma\"\n  },\n  // Profile and Settings\n  profile: {\n    myProfile: \"Wasifu Wangu\",\n    editProfile: \"Hariri Wasifu\",\n    changePassword: \"Badilisha Nywila\",\n    statistics: \"Takwimu\",\n    achievements: \"Mafanikio\",\n    level: \"Kiwango\",\n    xp: \"Pointi za Uzoefu\",\n    streak: \"Mfuatano\"\n  },\n  // Time and Date\n  time: {\n    today: \"Leo\",\n    yesterday: \"Jana\",\n    tomorrow: \"Kesho\",\n    week: \"Wiki\",\n    month: \"Mwezi\",\n    year: \"Mwaka\",\n    morning: \"Asubuhi\",\n    afternoon: \"Mchana\",\n    evening: \"Jioni\",\n    night: \"Usiku\"\n  },\n  // Numbers in Kiswahili\n  numbers: {\n    \"1\": \"moja\",\n    \"2\": \"mbili\",\n    \"3\": \"tatu\",\n    \"4\": \"nne\",\n    \"5\": \"tano\",\n    \"6\": \"sita\",\n    \"7\": \"saba\",\n    \"8\": \"nane\",\n    \"9\": \"tisa\",\n    \"10\": \"kumi\"\n  },\n  // Educational Terms\n  education: {\n    student: \"Mwanafunzi\",\n    teacher: \"Mwalimu\",\n    lesson: \"Somo\",\n    homework: \"Kazi ya Nyumbani\",\n    exam: \"Mtihani\",\n    grade: \"Daraja\",\n    certificate: \"Cheti\",\n    graduation: \"Kuhitimu\",\n    knowledge: \"Maarifa\",\n    learning: \"Kujifunza\"\n  },\n  // Motivational Messages\n  motivation: {\n    keepLearning: \"Endelea Kujifunza!\",\n    greatJob: \"Kazi Nzuri!\",\n    almostThere: \"Karibu Kufika!\",\n    excellent: \"Bora Sana!\",\n    goodLuck: \"Bahati Njema!\",\n    believeInYourself: \"Jiamini Mwenyewe!\",\n    neverGiveUp: \"Usikate Tamaa!\",\n    practiceMore: \"Zoeza Zaidi!\"\n  },\n  // Hub Page\n  hub: {\n    welcome: \"Karibu\",\n    dashboard: \"Dashibodi\",\n    studySmarter: \"Jifunze Kwa Akili\",\n    yourProgress: \"Maendeleo Yako\",\n    recentActivity: \"Shughuli za Hivi Karibuni\",\n    quickActions: \"Vitendo vya Haraka\",\n    startQuiz: \"Anza Mtihani\",\n    watchVideos: \"Tazama Video\",\n    readNotes: \"Soma Maelezo\",\n    viewRanking: \"Ona Orodha ya Ushindi\",\n    totalXP: \"Jumla ya Pointi\",\n    currentStreak: \"Mfuatano wa Sasa\",\n    quizzesCompleted: \"Mitihani Iliyokamilika\",\n    studyTime: \"Muda wa Kusoma\"\n  },\n  // Quiz Pages\n  quizPages: {\n    availableQuizzes: \"Mitihani Iliyopatikana\",\n    selectSubject: \"Chagua Somo\",\n    selectClass: \"Chagua Darasa\",\n    difficulty: \"Kiwango cha Ugumu\",\n    easy: \"Rahisi\",\n    medium: \"Wastani\",\n    hard: \"Ngumu\",\n    questionsCount: \"Idadi ya Maswali\",\n    timeLimit: \"Kikomo cha Muda\",\n    startNow: \"Anza Sasa\",\n    question: \"Swali\",\n    of: \"kati ya\",\n    chooseAnswer: \"Chagua Jibu\",\n    nextQuestion: \"Swali Lijalo\",\n    previousQuestion: \"Swali Lililopita\",\n    submitQuiz: \"Wasilisha Mtihani\",\n    timeUp: \"Muda Umeisha\",\n    results: \"Matokeo\",\n    yourScore: \"Alama Zako\",\n    correctAnswers: \"Majibu Sahihi\",\n    wrongAnswers: \"Majibu Makosa\",\n    timeTaken: \"Muda Uliotumika\",\n    retakeQuiz: \"Rudia Mtihani\",\n    viewExplanation: \"Ona Maelezo\",\n    explanation: \"Maelezo\"\n  },\n  // Video Lessons\n  videoLessons: {\n    videoLessons: \"Masomo ya Video\",\n    watchLesson: \"Tazama Somo\",\n    lessonCompleted: \"Somo Limekamilika\",\n    duration: \"Muda\",\n    description: \"Maelezo\",\n    relatedVideos: \"Video Zinazohusiana\",\n    comments: \"Maoni\",\n    addComment: \"Ongeza Maoni\",\n    reply: \"Jibu\",\n    likes: \"Mapendekezo\",\n    views: \"Miwani\",\n    shareVideo: \"Shiriki Video\",\n    downloadVideo: \"Pakua Video\",\n    fullscreen: \"Skrini Nzima\",\n    playVideo: \"Cheza Video\",\n    pauseVideo: \"Simamisha Video\"\n  },\n  // Study Materials\n  studyMaterials: {\n    studyMaterials: \"Vifaa vya Kusoma\",\n    books: \"Vitabu\",\n    notes: \"Maelezo\",\n    pastPapers: \"Karatasi za Zamani\",\n    documents: \"Hati\",\n    downloadMaterial: \"Pakua Kifaa\",\n    viewMaterial: \"Ona Kifaa\",\n    searchMaterials: \"Tafuta Vifaa\",\n    filterBySubject: \"Chuja kwa Somo\",\n    filterByClass: \"Chuja kwa Darasa\",\n    uploadDate: \"Tarehe ya Kupakia\",\n    fileSize: \"Ukubwa wa Faili\",\n    fileType: \"Aina ya Faili\",\n    preview: \"Onyesho la Awali\"\n  },\n  // Ranking Page\n  ranking: {\n    leaderboard: \"Orodha ya Viongozi\",\n    myRank: \"Nafasi Yangu\",\n    topStudents: \"Wanafunzi Bora\",\n    thisWeek: \"Wiki Hii\",\n    thisMonth: \"Mwezi Huu\",\n    allTime: \"Wakati Wote\",\n    rank: \"Nafasi\",\n    student: \"Mwanafunzi\",\n    points: \"Pointi\",\n    level: \"Kiwango\",\n    badges: \"Vibeti\",\n    achievements: \"Mafanikio\",\n    viewProfile: \"Ona Wasifu\",\n    challenge: \"Changamoto\",\n    compete: \"Shindana\"\n  },\n  // Profile Page\n  profilePage: {\n    myProfile: \"Wasifu Wangu\",\n    personalInfo: \"Taarifa za Kibinafsi\",\n    academicInfo: \"Taarifa za Kitaaluma\",\n    statistics: \"Takwimu\",\n    achievements: \"Mafanikio\",\n    settings: \"Mipangilio\",\n    editProfile: \"Hariri Wasifu\",\n    changePhoto: \"Badilisha Picha\",\n    updateInfo: \"Sasisha Taarifa\",\n    saveChanges: \"Hifadhi Mabadiliko\",\n    accountSettings: \"Mipangilio ya Akaunti\",\n    privacySettings: \"Mipangilio ya Faragha\",\n    notificationSettings: \"Mipangilio ya Arifa\",\n    languageSettings: \"Mipangilio ya Lugha\",\n    deleteAccount: \"Futa Akaunti\"\n  },\n  // Forum\n  forum: {\n    discussionForum: \"Jukwaa la Majadiliano\",\n    askQuestion: \"Uliza Swali\",\n    recentDiscussions: \"Majadiliano ya Hivi Karibuni\",\n    popularTopics: \"Mada Maarufu\",\n    myQuestions: \"Maswali Yangu\",\n    myAnswers: \"Majibu Yangu\",\n    postQuestion: \"Chapisha Swali\",\n    answerQuestion: \"Jibu Swali\",\n    upvote: \"Piga Kura Juu\",\n    downvote: \"Piga Kura Chini\",\n    bestAnswer: \"Jibu Bora\",\n    solved: \"Imetatuliwa\",\n    unsolved: \"Haijatuliwa\",\n    replies: \"Majibu\",\n    lastActivity: \"Shughuli ya Mwisho\"\n  },\n  // Subscription\n  subscription: {\n    choosePlan: \"Chagua Mpango\",\n    currentPlan: \"Mpango wa Sasa\",\n    upgradePlan: \"Boresha Mpango\",\n    basicPlan: \"Mpango wa Msingi\",\n    premiumPlan: \"Mpango wa Juu\",\n    proPlan: \"Mpango wa Kitaalamu\",\n    features: \"Vipengele\",\n    price: \"Bei\",\n    duration: \"Muda\",\n    payNow: \"Lipa Sasa\",\n    paymentMethod: \"Njia ya Malipo\",\n    mobileMoney: \"Pesa za Simu\",\n    bankTransfer: \"Uhamisho wa Benki\",\n    paymentSuccess: \"Malipo Yamefanikiwa\",\n    paymentFailed: \"Malipo Yameshindwa\",\n    subscriptionActive: \"Uanachama Unaendelea\",\n    subscriptionExpired: \"Uanachama Umeisha\",\n    renewSubscription: \"Sasisha Uanachama\"\n  },\n  // Brainwave AI\n  brainwaveAI: {\n    brainwaveAI: \"Akili ya Brainwave\",\n    askBrainwave: \"Uliza Brainwave\",\n    chatWithAI: \"Ongea na AI\",\n    aiAssistant: \"Msaidizi wa AI\",\n    typeMessage: \"Andika Ujumbe\",\n    sendMessage: \"Tuma Ujumbe\",\n    aiThinking: \"AI Inafikiri...\",\n    aiResponse: \"Jibu la AI\",\n    clearChat: \"Futa Mazungumzo\",\n    chatHistory: \"Historia ya Mazungumzo\",\n    helpfulTips: \"Vidokezo vya Msaada\",\n    askAboutSubject: \"Uliza kuhusu Somo\",\n    explainConcept: \"Eleza Dhana\",\n    solveProblems: \"Tatua Matatizo\",\n    studyGuidance: \"Mwongozo wa Kusoma\"\n  },\n  // Skills\n  skills: {\n    skills: \"Ujuzi wa Video\",\n    videoSkills: \"Ujuzi wa Video\",\n    learnNewSkills: \"Jifunze ujuzi mpya\",\n    learnSkillsDescription: \"Jifunze ujuzi mpya kupitia video za kielimu\",\n    featuredSkills: \"Ujuzi Maalum\",\n    searchSkills: \"Tafuta ujuzi...\",\n    allLevels: \"Viwango Vyote\",\n    beginner: \"Mwanzo\",\n    amateur: \"Wastani\",\n    professional: \"Kitaalamu\",\n    expert: \"Mtaalamu\",\n    allCategories: \"Makundi Yote\",\n    sortBy: \"Panga kwa\",\n    newest: \"Mpya zaidi\",\n    mostPopular: \"Maarufu\",\n    highestRated: \"Kiwango cha Juu\",\n    loadingSkills: \"Inapakia ujuzi...\",\n    errorLoadingSkills: \"Hitilafu ya Kupakia Ujuzi\",\n    noSkillsFound: \"Hakuna Ujuzi Uliopatikana\",\n    noSkillsMessage: \"Hakuna ujuzi unaolingana na utafutaji wako. Jaribu kubadilisha vigezo vya utafutaji.\",\n    durationNotSpecified: \"Muda haujaainishwa\",\n    markAsCompleted: \"Kamilisha Ujuzi\",\n    skillCompleted: \"Ujuzi umekamilika!\",\n    errorMarkingCompleted: \"Hitilafu ya kukamilisha ujuzi\",\n    videoUnavailable: \"Video Haipatikani\",\n    videoCannotPlay: \"Video hii haiwezi kuchezwa kwa sasa.\",\n    whatYouWillLearn: \"Utakachojifunza:\",\n    browserNotSupported: \"Kivinjari chako hakitumii video.\",\n    level: \"Kiwango\",\n    category: \"Kundi\",\n    difficulty: \"Ugumu\",\n    estimatedTime: \"Muda wa Makadirio\",\n    views: \"Miwani\",\n    rating: \"Kiwango\"\n  },\n  // AI Response System\n  ai: {\n    brainwaveAI: \"Brainwave AI\",\n    discussWithAI: \"Jadili na AI\",\n    aiThinking: \"Brainwave AI inafikiri...\",\n    aiResponse: \"Jibu la AI\",\n    autoAIResponse: \"Jibu la Otomatiki la AI\",\n    askAIQuestion: \"Uliza swali la AI\",\n    aiHelp: \"Msaada wa AI\",\n    aiAssistant: \"Msaidizi wa AI\",\n    // Past Paper Discussion\n    discussWithBrainwave: \"Jadili na Brainwave AI\",\n    pastPaperDiscussion: \"Mjadala wa Karatasi ya Mtihani\",\n    askAboutPaper: \"Uliza kuhusu karatasi hii ya mtihani...\",\n    clearConversation: \"Futa Mazungumzo\",\n    aiWelcomeMessage: \"Hujambo! Mimi ni Brainwave AI. Nina tayari kukusaidia.\",\n    // Forum AI Responses\n    aiReply: \"Jibu la AI\",\n    automaticResponse: \"Jibu la Otomatiki\",\n    aiAnswered: \"AI imejibu\",\n    // Video Comments AI\n    aiCommentReply: \"Jibu la AI kwa Maoni\",\n    videoDiscussion: \"Mjadala wa Video\",\n    // Error Messages\n    aiError: \"Samahani, kuna hitilafu katika kupata jibu la AI\",\n    aiUnavailable: \"Huduma ya AI haipatikani kwa sasa\",\n    tryAgain: \"Jaribu tena\"\n  },\n  // Buttons and Actions\n  buttons: {\n    submit: \"Wasilisha\",\n    cancel: \"Ghairi\",\n    save: \"Hifadhi\",\n    edit: \"Hariri\",\n    delete: \"Futa\",\n    update: \"Sasisha\",\n    create: \"Unda\",\n    add: \"Ongeza\",\n    remove: \"Ondoa\",\n    confirm: \"Thibitisha\",\n    back: \"Rudi Nyuma\",\n    next: \"Mbele\",\n    previous: \"Nyuma\",\n    finish: \"Maliza\",\n    start: \"Anza\",\n    stop: \"Simama\",\n    pause: \"Simamisha\",\n    play: \"Cheza\",\n    download: \"Pakua\",\n    upload: \"Pakia\",\n    share: \"Shiriki\",\n    copy: \"Nakili\",\n    print: \"Chapisha\",\n    close: \"Funga\",\n    open: \"Fungua\",\n    expand: \"Panua\",\n    collapse: \"Kunja\"\n  },\n  // Status Messages\n  status: {\n    loading: \"Inapakia...\",\n    saving: \"Inahifadhi...\",\n    saved: \"Imehifadhiwa\",\n    error: \"Hitilafu\",\n    success: \"Mafanikio\",\n    warning: \"Onyo\",\n    info: \"Taarifa\",\n    completed: \"Imekamilika\",\n    pending: \"Inasubiri\",\n    failed: \"Imeshindwa\",\n    cancelled: \"Imeghairiwa\",\n    processing: \"Inachakatwa\",\n    uploading: \"Inapakia\",\n    downloading: \"Inapakua\",\n    connecting: \"Inaunganisha\",\n    connected: \"Imeunganishwa\",\n    disconnected: \"Imekatishwa\"\n  },\n  // Form Validation\n  validation: {\n    required: \"Hii ni lazima\",\n    invalidEmail: \"Barua pepe si sahihi\",\n    passwordTooShort: \"Nywila ni fupi sana\",\n    passwordsDoNotMatch: \"Nywila hazilingani\",\n    invalidPhoneNumber: \"Nambari ya simu si sahihi\",\n    fieldTooLong: \"Uga ni mrefu sana\",\n    fieldTooShort: \"Uga ni mfupi sana\",\n    invalidFormat: \"Muundo si sahihi\",\n    alreadyExists: \"Tayari ipo\",\n    notFound: \"Haijapatikana\",\n    accessDenied: \"Ufikiaji Umekataliwa\",\n    sessionExpired: \"Kipindi Kimeisha\"\n  }\n};\n\n// Helper function to get translation\nexport const getKiswahiliTranslation = (key, fallback = key) => {\n  const keys = key.split('.');\n  let translation = kiswahiliTranslations;\n  for (const k of keys) {\n    if (translation && translation[k]) {\n      translation = translation[k];\n    } else {\n      return fallback;\n    }\n  }\n  return translation || fallback;\n};\n\n// Helper function to check if user is in Kiswahili mode\nexport const isKiswahiliMode = userLevel => {\n  return userLevel === 'primary_kiswahili';\n};\nexport default kiswahiliTranslations;", "map": {"version": 3, "names": ["kiswahiliTranslations", "navigation", "home", "quizzes", "videos", "ranking", "forum", "profile", "logout", "settings", "auth", "login", "register", "username", "password", "firstName", "lastName", "school", "level", "class", "phoneNumber", "email", "quiz", "startQuiz", "submitQuiz", "nextQuestion", "previousQuestion", "timeRemaining", "score", "correct", "incorrect", "wellDone", "try<PERSON><PERSON>n", "minutes", "seconds", "subjects", "classes", "messages", "welcome", "loading", "error", "success", "save", "cancel", "delete", "edit", "view", "search", "filter", "refresh", "studyMaterials", "documents", "notes", "exercises", "download", "watch", "read", "myProfile", "editProfile", "changePassword", "statistics", "achievements", "xp", "streak", "time", "today", "yesterday", "tomorrow", "week", "month", "year", "morning", "afternoon", "evening", "night", "numbers", "education", "student", "teacher", "lesson", "homework", "exam", "grade", "certificate", "graduation", "knowledge", "learning", "motivation", "keepLearning", "<PERSON><PERSON><PERSON>", "almostThere", "excellent", "goodLuck", "believeInYourself", "neverGiveUp", "practiceMore", "hub", "dashboard", "studySmarter", "yourProgress", "recentActivity", "quickActions", "watchVideos", "readNotes", "viewRanking", "totalXP", "currentStreak", "quizzesCompleted", "studyTime", "quizPages", "availableQuizzes", "selectSubject", "selectClass", "difficulty", "easy", "medium", "hard", "questionsCount", "timeLimit", "startNow", "question", "of", "chooseAnswer", "timeUp", "results", "yourScore", "correctAnswers", "wrongAnswers", "timeTaken", "retakeQuiz", "viewExplanation", "explanation", "videoLessons", "<PERSON><PERSON><PERSON><PERSON>", "lessonCompleted", "duration", "description", "relatedVideos", "comments", "addComment", "reply", "likes", "views", "shareVideo", "downloadVideo", "fullscreen", "playVideo", "pauseVideo", "books", "pastPapers", "downloadMaterial", "viewMaterial", "searchMaterials", "filterBySubject", "filterByClass", "uploadDate", "fileSize", "fileType", "preview", "leaderboard", "myRank", "topStudents", "thisWeek", "thisMonth", "allTime", "rank", "points", "badges", "viewProfile", "challenge", "compete", "profilePage", "personalInfo", "academicInfo", "changePhoto", "updateInfo", "saveChanges", "accountSettings", "privacySettings", "notificationSettings", "languageSettings", "deleteAccount", "discussionForum", "askQuestion", "recentDiscussions", "popularTopics", "myQuestions", "myAnswers", "postQuestion", "answerQuestion", "upvote", "downvote", "bestAnswer", "solved", "unsolved", "replies", "lastActivity", "subscription", "choosePlan", "currentPlan", "upgradePlan", "basicPlan", "premiumPlan", "proPlan", "features", "price", "payNow", "paymentMethod", "mobileMoney", "bankTransfer", "paymentSuccess", "paymentFailed", "subscriptionActive", "subscriptionExpired", "renewSubscription", "brainwaveAI", "askBrainwave", "chatWithAI", "aiAssistant", "typeMessage", "sendMessage", "aiThinking", "aiResponse", "clearChat", "chatHistory", "helpfulTips", "askAboutSubject", "explainConcept", "solveProblems", "studyGuidance", "skills", "videoSkills", "learnNewSkills", "learnSkillsDescription", "featuredS<PERSON>s", "searchSkills", "allLevels", "beginner", "amateur", "professional", "expert", "allCategories", "sortBy", "newest", "mostPopular", "highestRated", "loadingSkills", "errorLoadingSkills", "noSkillsFound", "noSkillsMessage", "durationNotSpecified", "mark<PERSON><PERSON>ompleted", "skillCompleted", "errorMarkingCompleted", "videoUnavailable", "videoCannotPlay", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "browserNotSupported", "category", "estimatedTime", "rating", "ai", "discussWithAI", "autoAIResponse", "askAIQuestion", "aiHelp", "discussWithBrainwave", "pastPaperDiscussion", "askAboutPaper", "clearConversation", "aiWelcomeMessage", "aiReply", "automaticResponse", "aiAnswered", "aiCommentReply", "videoDiscussion", "aiError", "aiUnavailable", "buttons", "submit", "update", "create", "add", "remove", "confirm", "back", "next", "previous", "finish", "start", "stop", "pause", "play", "upload", "share", "copy", "print", "close", "open", "expand", "collapse", "status", "saving", "saved", "warning", "info", "completed", "pending", "failed", "cancelled", "processing", "uploading", "downloading", "connecting", "connected", "disconnected", "validation", "required", "invalidEmail", "passwordTooShort", "passwordsDoNotMatch", "invalidPhoneNumber", "fieldTooLong", "fieldTooShort", "invalidFormat", "alreadyExists", "notFound", "accessDenied", "sessionExpired", "getKiswahiliTranslation", "key", "fallback", "keys", "split", "translation", "k", "isKiswahiliMode", "userLevel"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/localization/kiswahili.js"], "sourcesContent": ["// Kiswahili translations for Primary Kiswahili Medium level\nexport const kiswahiliTranslations = {\n  // Navigation and Common UI\n  navigation: {\n    home: \"Nyumba<PERSON>\",\n    quizzes: \"<PERSON><PERSON><PERSON>\",\n    videos: \"Video\",\n    ranking: \"<PERSON><PERSON> ya Ushindi\",\n    forum: \"<PERSON><PERSON><PERSON><PERSON>\",\n    profile: \"<PERSON><PERSON><PERSON>\",\n    logout: \"Ondoka\",\n    settings: \"Mipangilio\"\n  },\n\n  // Authentication\n  auth: {\n    login: \"Ingia\",\n    register: \"<PERSON><PERSON><PERSON><PERSON>\",\n    username: \"<PERSON><PERSON> la Mtumiaji\",\n    password: \"<PERSON><PERSON><PERSON><PERSON>\",\n    firstName: \"<PERSON><PERSON>wan<PERSON>\",\n    lastName: \"<PERSON><PERSON>wish<PERSON>\",\n    school: \"<PERSON><PERSON>\",\n    level: \"<PERSON><PERSON><PERSON>\",\n    class: \"Darasa\",\n    phoneNumber: \"<PERSON><PERSON> ya Simu\",\n    email: \"Barua Pepe\"\n  },\n\n  // Quiz System\n  quiz: {\n    startQuiz: \"Anza Mtihani\",\n    submitQuiz: \"<PERSON><PERSON><PERSON>ihani\",\n    nextQuestion: \"Swali Lijalo\",\n    previousQuestion: \"<PERSON>wal<PERSON>\",\n    timeRemaining: \"<PERSON><PERSON>\",\n    score: \"<PERSON><PERSON>\",\n    correct: \"<PERSON><PERSON><PERSON>\",\n    incorrect: \"Makosa\",\n    wellDone: \"Hongera!\",\n    tryAgain: \"<PERSON><PERSON><PERSON> Ten<PERSON>\",\n    minutes: \"<PERSON><PERSON><PERSON>\",\n    seconds: \"Se<PERSON><PERSON>\"\n  },\n\n  // Subjects (Primary Level in Kiswahili)\n  subjects: {\n    \"Hisabati\": \"Hisabati\",\n    \"Sayansi na Teknolojia\": \"Sayansi na Teknolojia\",\n    \"Jiografia\": \"Jiografia\",\n    \"Kiswahili\": \"Kiswahili\",\n    \"Maarifa ya Jamii\": \"Maarifa ya Jamii\",\n    \"Kiingereza\": \"Kiingereza\",\n    \"Dini\": \"Dini\",\n    \"Hesabu\": \"Hesabu\",\n    \"Michezo na Sanaa\": \"Michezo na Sanaa\",\n    \"Afya na Mazingira\": \"Afya na Mazingira\",\n    \"Uraia na Maadili\": \"Uraia na Maadili\",\n    \"Kifaransa\": \"Kifaransa\",\n    \"Historia ya Tanzania\": \"Historia ya Tanzania\"\n  },\n\n  // Classes\n  classes: {\n    \"1\": \"Darasa la Kwanza\",\n    \"2\": \"Darasa la Pili\",\n    \"3\": \"Darasa la Tatu\",\n    \"4\": \"Darasa la Nne\",\n    \"5\": \"Darasa la Tano\",\n    \"6\": \"Darasa la Sita\",\n    \"7\": \"Darasa la Saba\"\n  },\n\n  // Common Messages\n  messages: {\n    welcome: \"Karibu\",\n    loading: \"Inapakia...\",\n    error: \"Hitilafu\",\n    success: \"Mafanikio\",\n    save: \"Hifadhi\",\n    cancel: \"Ghairi\",\n    delete: \"Futa\",\n    edit: \"Hariri\",\n    view: \"Ona\",\n    search: \"Tafuta\",\n    filter: \"Chuja\",\n    refresh: \"Onyesha Upya\"\n  },\n\n  // Study Materials\n  studyMaterials: {\n    videos: \"Video za Masomo\",\n    documents: \"Hati za Masomo\",\n    notes: \"Maelezo\",\n    exercises: \"Mazoezi\",\n    download: \"Pakua\",\n    watch: \"Tazama\",\n    read: \"Soma\"\n  },\n\n  // Profile and Settings\n  profile: {\n    myProfile: \"Wasifu Wangu\",\n    editProfile: \"Hariri Wasifu\",\n    changePassword: \"Badilisha Nywila\",\n    statistics: \"Takwimu\",\n    achievements: \"Mafanikio\",\n    level: \"Kiwango\",\n    xp: \"Pointi za Uzoefu\",\n    streak: \"Mfuatano\"\n  },\n\n  // Time and Date\n  time: {\n    today: \"Leo\",\n    yesterday: \"Jana\",\n    tomorrow: \"Kesho\",\n    week: \"Wiki\",\n    month: \"Mwezi\",\n    year: \"Mwaka\",\n    morning: \"Asubuhi\",\n    afternoon: \"Mchana\",\n    evening: \"Jioni\",\n    night: \"Usiku\"\n  },\n\n  // Numbers in Kiswahili\n  numbers: {\n    \"1\": \"moja\",\n    \"2\": \"mbili\",\n    \"3\": \"tatu\",\n    \"4\": \"nne\",\n    \"5\": \"tano\",\n    \"6\": \"sita\",\n    \"7\": \"saba\",\n    \"8\": \"nane\",\n    \"9\": \"tisa\",\n    \"10\": \"kumi\"\n  },\n\n  // Educational Terms\n  education: {\n    student: \"Mwanafunzi\",\n    teacher: \"Mwalimu\",\n    lesson: \"Somo\",\n    homework: \"Kazi ya Nyumbani\",\n    exam: \"Mtihani\",\n    grade: \"Daraja\",\n    certificate: \"Cheti\",\n    graduation: \"Kuhitimu\",\n    knowledge: \"Maarifa\",\n    learning: \"Kujifunza\"\n  },\n\n  // Motivational Messages\n  motivation: {\n    keepLearning: \"Endelea Kujifunza!\",\n    greatJob: \"Kazi Nzuri!\",\n    almostThere: \"Karibu Kufika!\",\n    excellent: \"Bora Sana!\",\n    goodLuck: \"Bahati Njema!\",\n    believeInYourself: \"Jiamini Mwenyewe!\",\n    neverGiveUp: \"Usikate Tamaa!\",\n    practiceMore: \"Zoeza Zaidi!\"\n  },\n\n  // Hub Page\n  hub: {\n    welcome: \"Karibu\",\n    dashboard: \"Dashibodi\",\n    studySmarter: \"Jifunze Kwa Akili\",\n    yourProgress: \"Maendeleo Yako\",\n    recentActivity: \"Shughuli za Hivi Karibuni\",\n    quickActions: \"Vitendo vya Haraka\",\n    startQuiz: \"Anza Mtihani\",\n    watchVideos: \"Tazama Video\",\n    readNotes: \"Soma Maelezo\",\n    viewRanking: \"Ona Orodha ya Ushindi\",\n    totalXP: \"Jumla ya Pointi\",\n    currentStreak: \"Mfuatano wa Sasa\",\n    quizzesCompleted: \"Mitihani Iliyokamilika\",\n    studyTime: \"Muda wa Kusoma\"\n  },\n\n  // Quiz Pages\n  quizPages: {\n    availableQuizzes: \"Mitihani Iliyopatikana\",\n    selectSubject: \"Chagua Somo\",\n    selectClass: \"Chagua Darasa\",\n    difficulty: \"Kiwango cha Ugumu\",\n    easy: \"Rahisi\",\n    medium: \"Wastani\",\n    hard: \"Ngumu\",\n    questionsCount: \"Idadi ya Maswali\",\n    timeLimit: \"Kikomo cha Muda\",\n    startNow: \"Anza Sasa\",\n    question: \"Swali\",\n    of: \"kati ya\",\n    chooseAnswer: \"Chagua Jibu\",\n    nextQuestion: \"Swali Lijalo\",\n    previousQuestion: \"Swali Lililopita\",\n    submitQuiz: \"Wasilisha Mtihani\",\n    timeUp: \"Muda Umeisha\",\n    results: \"Matokeo\",\n    yourScore: \"Alama Zako\",\n    correctAnswers: \"Majibu Sahihi\",\n    wrongAnswers: \"Majibu Makosa\",\n    timeTaken: \"Muda Uliotumika\",\n    retakeQuiz: \"Rudia Mtihani\",\n    viewExplanation: \"Ona Maelezo\",\n    explanation: \"Maelezo\"\n  },\n\n  // Video Lessons\n  videoLessons: {\n    videoLessons: \"Masomo ya Video\",\n    watchLesson: \"Tazama Somo\",\n    lessonCompleted: \"Somo Limekamilika\",\n    duration: \"Muda\",\n    description: \"Maelezo\",\n    relatedVideos: \"Video Zinazohusiana\",\n    comments: \"Maoni\",\n    addComment: \"Ongeza Maoni\",\n    reply: \"Jibu\",\n    likes: \"Mapendekezo\",\n    views: \"Miwani\",\n    shareVideo: \"Shiriki Video\",\n    downloadVideo: \"Pakua Video\",\n    fullscreen: \"Skrini Nzima\",\n    playVideo: \"Cheza Video\",\n    pauseVideo: \"Simamisha Video\"\n  },\n\n  // Study Materials\n  studyMaterials: {\n    studyMaterials: \"Vifaa vya Kusoma\",\n    books: \"Vitabu\",\n    notes: \"Maelezo\",\n    pastPapers: \"Karatasi za Zamani\",\n    documents: \"Hati\",\n    downloadMaterial: \"Pakua Kifaa\",\n    viewMaterial: \"Ona Kifaa\",\n    searchMaterials: \"Tafuta Vifaa\",\n    filterBySubject: \"Chuja kwa Somo\",\n    filterByClass: \"Chuja kwa Darasa\",\n    uploadDate: \"Tarehe ya Kupakia\",\n    fileSize: \"Ukubwa wa Faili\",\n    fileType: \"Aina ya Faili\",\n    preview: \"Onyesho la Awali\"\n  },\n\n  // Ranking Page\n  ranking: {\n    leaderboard: \"Orodha ya Viongozi\",\n    myRank: \"Nafasi Yangu\",\n    topStudents: \"Wanafunzi Bora\",\n    thisWeek: \"Wiki Hii\",\n    thisMonth: \"Mwezi Huu\",\n    allTime: \"Wakati Wote\",\n    rank: \"Nafasi\",\n    student: \"Mwanafunzi\",\n    points: \"Pointi\",\n    level: \"Kiwango\",\n    badges: \"Vibeti\",\n    achievements: \"Mafanikio\",\n    viewProfile: \"Ona Wasifu\",\n    challenge: \"Changamoto\",\n    compete: \"Shindana\"\n  },\n\n  // Profile Page\n  profilePage: {\n    myProfile: \"Wasifu Wangu\",\n    personalInfo: \"Taarifa za Kibinafsi\",\n    academicInfo: \"Taarifa za Kitaaluma\",\n    statistics: \"Takwimu\",\n    achievements: \"Mafanikio\",\n    settings: \"Mipangilio\",\n    editProfile: \"Hariri Wasifu\",\n    changePhoto: \"Badilisha Picha\",\n    updateInfo: \"Sasisha Taarifa\",\n    saveChanges: \"Hifadhi Mabadiliko\",\n    accountSettings: \"Mipangilio ya Akaunti\",\n    privacySettings: \"Mipangilio ya Faragha\",\n    notificationSettings: \"Mipangilio ya Arifa\",\n    languageSettings: \"Mipangilio ya Lugha\",\n    deleteAccount: \"Futa Akaunti\"\n  },\n\n  // Forum\n  forum: {\n    discussionForum: \"Jukwaa la Majadiliano\",\n    askQuestion: \"Uliza Swali\",\n    recentDiscussions: \"Majadiliano ya Hivi Karibuni\",\n    popularTopics: \"Mada Maarufu\",\n    myQuestions: \"Maswali Yangu\",\n    myAnswers: \"Majibu Yangu\",\n    postQuestion: \"Chapisha Swali\",\n    answerQuestion: \"Jibu Swali\",\n    upvote: \"Piga Kura Juu\",\n    downvote: \"Piga Kura Chini\",\n    bestAnswer: \"Jibu Bora\",\n    solved: \"Imetatuliwa\",\n    unsolved: \"Haijatuliwa\",\n    replies: \"Majibu\",\n    lastActivity: \"Shughuli ya Mwisho\"\n  },\n\n  // Subscription\n  subscription: {\n    choosePlan: \"Chagua Mpango\",\n    currentPlan: \"Mpango wa Sasa\",\n    upgradePlan: \"Boresha Mpango\",\n    basicPlan: \"Mpango wa Msingi\",\n    premiumPlan: \"Mpango wa Juu\",\n    proPlan: \"Mpango wa Kitaalamu\",\n    features: \"Vipengele\",\n    price: \"Bei\",\n    duration: \"Muda\",\n    payNow: \"Lipa Sasa\",\n    paymentMethod: \"Njia ya Malipo\",\n    mobileMoney: \"Pesa za Simu\",\n    bankTransfer: \"Uhamisho wa Benki\",\n    paymentSuccess: \"Malipo Yamefanikiwa\",\n    paymentFailed: \"Malipo Yameshindwa\",\n    subscriptionActive: \"Uanachama Unaendelea\",\n    subscriptionExpired: \"Uanachama Umeisha\",\n    renewSubscription: \"Sasisha Uanachama\"\n  },\n\n  // Brainwave AI\n  brainwaveAI: {\n    brainwaveAI: \"Akili ya Brainwave\",\n    askBrainwave: \"Uliza Brainwave\",\n    chatWithAI: \"Ongea na AI\",\n    aiAssistant: \"Msaidizi wa AI\",\n    typeMessage: \"Andika Ujumbe\",\n    sendMessage: \"Tuma Ujumbe\",\n    aiThinking: \"AI Inafikiri...\",\n    aiResponse: \"Jibu la AI\",\n    clearChat: \"Futa Mazungumzo\",\n    chatHistory: \"Historia ya Mazungumzo\",\n    helpfulTips: \"Vidokezo vya Msaada\",\n    askAboutSubject: \"Uliza kuhusu Somo\",\n    explainConcept: \"Eleza Dhana\",\n    solveProblems: \"Tatua Matatizo\",\n    studyGuidance: \"Mwongozo wa Kusoma\"\n  },\n\n  // Skills\n  skills: {\n    skills: \"Ujuzi wa Video\",\n    videoSkills: \"Ujuzi wa Video\",\n    learnNewSkills: \"Jifunze ujuzi mpya\",\n    learnSkillsDescription: \"Jifunze ujuzi mpya kupitia video za kielimu\",\n    featuredSkills: \"Ujuzi Maalum\",\n    searchSkills: \"Tafuta ujuzi...\",\n    allLevels: \"Viwango Vyote\",\n    beginner: \"Mwanzo\",\n    amateur: \"Wastani\",\n    professional: \"Kitaalamu\",\n    expert: \"Mtaalamu\",\n    allCategories: \"Makundi Yote\",\n    sortBy: \"Panga kwa\",\n    newest: \"Mpya zaidi\",\n    mostPopular: \"Maarufu\",\n    highestRated: \"Kiwango cha Juu\",\n    loadingSkills: \"Inapakia ujuzi...\",\n    errorLoadingSkills: \"Hitilafu ya Kupakia Ujuzi\",\n    noSkillsFound: \"Hakuna Ujuzi Uliopatikana\",\n    noSkillsMessage: \"Hakuna ujuzi unaolingana na utafutaji wako. Jaribu kubadilisha vigezo vya utafutaji.\",\n    durationNotSpecified: \"Muda haujaainishwa\",\n    markAsCompleted: \"Kamilisha Ujuzi\",\n    skillCompleted: \"Ujuzi umekamilika!\",\n    errorMarkingCompleted: \"Hitilafu ya kukamilisha ujuzi\",\n    videoUnavailable: \"Video Haipatikani\",\n    videoCannotPlay: \"Video hii haiwezi kuchezwa kwa sasa.\",\n    whatYouWillLearn: \"Utakachojifunza:\",\n    browserNotSupported: \"Kivinjari chako hakitumii video.\",\n    level: \"Kiwango\",\n    category: \"Kundi\",\n    difficulty: \"Ugumu\",\n    estimatedTime: \"Muda wa Makadirio\",\n    views: \"Miwani\",\n    rating: \"Kiwango\"\n  },\n\n  // AI Response System\n  ai: {\n    brainwaveAI: \"Brainwave AI\",\n    discussWithAI: \"Jadili na AI\",\n    aiThinking: \"Brainwave AI inafikiri...\",\n    aiResponse: \"Jibu la AI\",\n    autoAIResponse: \"Jibu la Otomatiki la AI\",\n    askAIQuestion: \"Uliza swali la AI\",\n    aiHelp: \"Msaada wa AI\",\n    aiAssistant: \"Msaidizi wa AI\",\n\n    // Past Paper Discussion\n    discussWithBrainwave: \"Jadili na Brainwave AI\",\n    pastPaperDiscussion: \"Mjadala wa Karatasi ya Mtihani\",\n    askAboutPaper: \"Uliza kuhusu karatasi hii ya mtihani...\",\n    clearConversation: \"Futa Mazungumzo\",\n    aiWelcomeMessage: \"Hujambo! Mimi ni Brainwave AI. Nina tayari kukusaidia.\",\n\n    // Forum AI Responses\n    aiReply: \"Jibu la AI\",\n    automaticResponse: \"Jibu la Otomatiki\",\n    aiAnswered: \"AI imejibu\",\n\n    // Video Comments AI\n    aiCommentReply: \"Jibu la AI kwa Maoni\",\n    videoDiscussion: \"Mjadala wa Video\",\n\n    // Error Messages\n    aiError: \"Samahani, kuna hitilafu katika kupata jibu la AI\",\n    aiUnavailable: \"Huduma ya AI haipatikani kwa sasa\",\n    tryAgain: \"Jaribu tena\"\n  },\n\n  // Buttons and Actions\n  buttons: {\n    submit: \"Wasilisha\",\n    cancel: \"Ghairi\",\n    save: \"Hifadhi\",\n    edit: \"Hariri\",\n    delete: \"Futa\",\n    update: \"Sasisha\",\n    create: \"Unda\",\n    add: \"Ongeza\",\n    remove: \"Ondoa\",\n    confirm: \"Thibitisha\",\n    back: \"Rudi Nyuma\",\n    next: \"Mbele\",\n    previous: \"Nyuma\",\n    finish: \"Maliza\",\n    start: \"Anza\",\n    stop: \"Simama\",\n    pause: \"Simamisha\",\n    play: \"Cheza\",\n    download: \"Pakua\",\n    upload: \"Pakia\",\n    share: \"Shiriki\",\n    copy: \"Nakili\",\n    print: \"Chapisha\",\n    close: \"Funga\",\n    open: \"Fungua\",\n    expand: \"Panua\",\n    collapse: \"Kunja\"\n  },\n\n  // Status Messages\n  status: {\n    loading: \"Inapakia...\",\n    saving: \"Inahifadhi...\",\n    saved: \"Imehifadhiwa\",\n    error: \"Hitilafu\",\n    success: \"Mafanikio\",\n    warning: \"Onyo\",\n    info: \"Taarifa\",\n    completed: \"Imekamilika\",\n    pending: \"Inasubiri\",\n    failed: \"Imeshindwa\",\n    cancelled: \"Imeghairiwa\",\n    processing: \"Inachakatwa\",\n    uploading: \"Inapakia\",\n    downloading: \"Inapakua\",\n    connecting: \"Inaunganisha\",\n    connected: \"Imeunganishwa\",\n    disconnected: \"Imekatishwa\"\n  },\n\n  // Form Validation\n  validation: {\n    required: \"Hii ni lazima\",\n    invalidEmail: \"Barua pepe si sahihi\",\n    passwordTooShort: \"Nywila ni fupi sana\",\n    passwordsDoNotMatch: \"Nywila hazilingani\",\n    invalidPhoneNumber: \"Nambari ya simu si sahihi\",\n    fieldTooLong: \"Uga ni mrefu sana\",\n    fieldTooShort: \"Uga ni mfupi sana\",\n    invalidFormat: \"Muundo si sahihi\",\n    alreadyExists: \"Tayari ipo\",\n    notFound: \"Haijapatikana\",\n    accessDenied: \"Ufikiaji Umekataliwa\",\n    sessionExpired: \"Kipindi Kimeisha\"\n  }\n};\n\n// Helper function to get translation\nexport const getKiswahiliTranslation = (key, fallback = key) => {\n  const keys = key.split('.');\n  let translation = kiswahiliTranslations;\n  \n  for (const k of keys) {\n    if (translation && translation[k]) {\n      translation = translation[k];\n    } else {\n      return fallback;\n    }\n  }\n  \n  return translation || fallback;\n};\n\n// Helper function to check if user is in Kiswahili mode\nexport const isKiswahiliMode = (userLevel) => {\n  return userLevel === 'primary_kiswahili';\n};\n\nexport default kiswahiliTranslations;\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,qBAAqB,GAAG;EACnC;EACAC,UAAU,EAAE;IACVC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,UAAU;IACnBC,MAAM,EAAE,OAAO;IACfC,OAAO,EAAE,mBAAmB;IAC5BC,KAAK,EAAE,QAAQ;IACfC,OAAO,EAAE,QAAQ;IACjBC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE;EACZ,CAAC;EAED;EACAC,IAAI,EAAE;IACJC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,kBAAkB;IAC5BC,QAAQ,EAAE,QAAQ;IAClBC,SAAS,EAAE,gBAAgB;IAC3BC,QAAQ,EAAE,gBAAgB;IAC1BC,MAAM,EAAE,OAAO;IACfC,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE,iBAAiB;IAC9BC,KAAK,EAAE;EACT,CAAC;EAED;EACAC,IAAI,EAAE;IACJC,SAAS,EAAE,cAAc;IACzBC,UAAU,EAAE,mBAAmB;IAC/BC,YAAY,EAAE,cAAc;IAC5BC,gBAAgB,EAAE,kBAAkB;IACpCC,aAAa,EAAE,eAAe;IAC9BC,KAAK,EAAE,OAAO;IACdC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,aAAa;IACvBC,OAAO,EAAE,QAAQ;IACjBC,OAAO,EAAE;EACX,CAAC;EAED;EACAC,QAAQ,EAAE;IACR,UAAU,EAAE,UAAU;IACtB,uBAAuB,EAAE,uBAAuB;IAChD,WAAW,EAAE,WAAW;IACxB,WAAW,EAAE,WAAW;IACxB,kBAAkB,EAAE,kBAAkB;IACtC,YAAY,EAAE,YAAY;IAC1B,MAAM,EAAE,MAAM;IACd,QAAQ,EAAE,QAAQ;IAClB,kBAAkB,EAAE,kBAAkB;IACtC,mBAAmB,EAAE,mBAAmB;IACxC,kBAAkB,EAAE,kBAAkB;IACtC,WAAW,EAAE,WAAW;IACxB,sBAAsB,EAAE;EAC1B,CAAC;EAED;EACAC,OAAO,EAAE;IACP,GAAG,EAAE,kBAAkB;IACvB,GAAG,EAAE,gBAAgB;IACrB,GAAG,EAAE,gBAAgB;IACrB,GAAG,EAAE,eAAe;IACpB,GAAG,EAAE,gBAAgB;IACrB,GAAG,EAAE,gBAAgB;IACrB,GAAG,EAAE;EACP,CAAC;EAED;EACAC,QAAQ,EAAE;IACRC,OAAO,EAAE,QAAQ;IACjBC,OAAO,EAAE,aAAa;IACtBC,KAAK,EAAE,UAAU;IACjBC,OAAO,EAAE,WAAW;IACpBC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,KAAK;IACXC,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,OAAO;IACfC,OAAO,EAAE;EACX,CAAC;EAED;EACAC,cAAc,EAAE;IACd9C,MAAM,EAAE,iBAAiB;IACzB+C,SAAS,EAAE,gBAAgB;IAC3BC,KAAK,EAAE,SAAS;IAChBC,SAAS,EAAE,SAAS;IACpBC,QAAQ,EAAE,OAAO;IACjBC,KAAK,EAAE,QAAQ;IACfC,IAAI,EAAE;EACR,CAAC;EAED;EACAjD,OAAO,EAAE;IACPkD,SAAS,EAAE,cAAc;IACzBC,WAAW,EAAE,eAAe;IAC5BC,cAAc,EAAE,kBAAkB;IAClCC,UAAU,EAAE,SAAS;IACrBC,YAAY,EAAE,WAAW;IACzB3C,KAAK,EAAE,SAAS;IAChB4C,EAAE,EAAE,kBAAkB;IACtBC,MAAM,EAAE;EACV,CAAC;EAED;EACAC,IAAI,EAAE;IACJC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,MAAM;IACjBC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,OAAO;IACdC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EAED;EACAC,OAAO,EAAE;IACP,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,OAAO;IACZ,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,KAAK;IACV,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,IAAI,EAAE;EACR,CAAC;EAED;EACAC,SAAS,EAAE;IACTC,OAAO,EAAE,YAAY;IACrBC,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE,kBAAkB;IAC5BC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE,OAAO;IACpBC,UAAU,EAAE,UAAU;IACtBC,SAAS,EAAE,SAAS;IACpBC,QAAQ,EAAE;EACZ,CAAC;EAED;EACAC,UAAU,EAAE;IACVC,YAAY,EAAE,oBAAoB;IAClCC,QAAQ,EAAE,aAAa;IACvBC,WAAW,EAAE,gBAAgB;IAC7BC,SAAS,EAAE,YAAY;IACvBC,QAAQ,EAAE,eAAe;IACzBC,iBAAiB,EAAE,mBAAmB;IACtCC,WAAW,EAAE,gBAAgB;IAC7BC,YAAY,EAAE;EAChB,CAAC;EAED;EACAC,GAAG,EAAE;IACH1D,OAAO,EAAE,QAAQ;IACjB2D,SAAS,EAAE,WAAW;IACtBC,YAAY,EAAE,mBAAmB;IACjCC,YAAY,EAAE,gBAAgB;IAC9BC,cAAc,EAAE,2BAA2B;IAC3CC,YAAY,EAAE,oBAAoB;IAClC9E,SAAS,EAAE,cAAc;IACzB+E,WAAW,EAAE,cAAc;IAC3BC,SAAS,EAAE,cAAc;IACzBC,WAAW,EAAE,uBAAuB;IACpCC,OAAO,EAAE,iBAAiB;IAC1BC,aAAa,EAAE,kBAAkB;IACjCC,gBAAgB,EAAE,wBAAwB;IAC1CC,SAAS,EAAE;EACb,CAAC;EAED;EACAC,SAAS,EAAE;IACTC,gBAAgB,EAAE,wBAAwB;IAC1CC,aAAa,EAAE,aAAa;IAC5BC,WAAW,EAAE,eAAe;IAC5BC,UAAU,EAAE,mBAAmB;IAC/BC,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE,SAAS;IACjBC,IAAI,EAAE,OAAO;IACbC,cAAc,EAAE,kBAAkB;IAClCC,SAAS,EAAE,iBAAiB;IAC5BC,QAAQ,EAAE,WAAW;IACrBC,QAAQ,EAAE,OAAO;IACjBC,EAAE,EAAE,SAAS;IACbC,YAAY,EAAE,aAAa;IAC3BjG,YAAY,EAAE,cAAc;IAC5BC,gBAAgB,EAAE,kBAAkB;IACpCF,UAAU,EAAE,mBAAmB;IAC/BmG,MAAM,EAAE,cAAc;IACtBC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,YAAY;IACvBC,cAAc,EAAE,eAAe;IAC/BC,YAAY,EAAE,eAAe;IAC7BC,SAAS,EAAE,iBAAiB;IAC5BC,UAAU,EAAE,eAAe;IAC3BC,eAAe,EAAE,aAAa;IAC9BC,WAAW,EAAE;EACf,CAAC;EAED;EACAC,YAAY,EAAE;IACZA,YAAY,EAAE,iBAAiB;IAC/BC,WAAW,EAAE,aAAa;IAC1BC,eAAe,EAAE,mBAAmB;IACpCC,QAAQ,EAAE,MAAM;IAChBC,WAAW,EAAE,SAAS;IACtBC,aAAa,EAAE,qBAAqB;IACpCC,QAAQ,EAAE,OAAO;IACjBC,UAAU,EAAE,cAAc;IAC1BC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE,QAAQ;IACfC,UAAU,EAAE,eAAe;IAC3BC,aAAa,EAAE,aAAa;IAC5BC,UAAU,EAAE,cAAc;IAC1BC,SAAS,EAAE,aAAa;IACxBC,UAAU,EAAE;EACd,CAAC;EAED;EACAjG,cAAc,EAAE;IACdA,cAAc,EAAE,kBAAkB;IAClCkG,KAAK,EAAE,QAAQ;IACfhG,KAAK,EAAE,SAAS;IAChBiG,UAAU,EAAE,oBAAoB;IAChClG,SAAS,EAAE,MAAM;IACjBmG,gBAAgB,EAAE,aAAa;IAC/BC,YAAY,EAAE,WAAW;IACzBC,eAAe,EAAE,cAAc;IAC/BC,eAAe,EAAE,gBAAgB;IACjCC,aAAa,EAAE,kBAAkB;IACjCC,UAAU,EAAE,mBAAmB;IAC/BC,QAAQ,EAAE,iBAAiB;IAC3BC,QAAQ,EAAE,eAAe;IACzBC,OAAO,EAAE;EACX,CAAC;EAED;EACAzJ,OAAO,EAAE;IACP0J,WAAW,EAAE,oBAAoB;IACjCC,MAAM,EAAE,cAAc;IACtBC,WAAW,EAAE,gBAAgB;IAC7BC,QAAQ,EAAE,UAAU;IACpBC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,aAAa;IACtBC,IAAI,EAAE,QAAQ;IACdxF,OAAO,EAAE,YAAY;IACrByF,MAAM,EAAE,QAAQ;IAChBpJ,KAAK,EAAE,SAAS;IAChBqJ,MAAM,EAAE,QAAQ;IAChB1G,YAAY,EAAE,WAAW;IACzB2G,WAAW,EAAE,YAAY;IACzBC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE;EACX,CAAC;EAED;EACAC,WAAW,EAAE;IACXlH,SAAS,EAAE,cAAc;IACzBmH,YAAY,EAAE,sBAAsB;IACpCC,YAAY,EAAE,sBAAsB;IACpCjH,UAAU,EAAE,SAAS;IACrBC,YAAY,EAAE,WAAW;IACzBpD,QAAQ,EAAE,YAAY;IACtBiD,WAAW,EAAE,eAAe;IAC5BoH,WAAW,EAAE,iBAAiB;IAC9BC,UAAU,EAAE,iBAAiB;IAC7BC,WAAW,EAAE,oBAAoB;IACjCC,eAAe,EAAE,uBAAuB;IACxCC,eAAe,EAAE,uBAAuB;IACxCC,oBAAoB,EAAE,qBAAqB;IAC3CC,gBAAgB,EAAE,qBAAqB;IACvCC,aAAa,EAAE;EACjB,CAAC;EAED;EACA/K,KAAK,EAAE;IACLgL,eAAe,EAAE,uBAAuB;IACxCC,WAAW,EAAE,aAAa;IAC1BC,iBAAiB,EAAE,8BAA8B;IACjDC,aAAa,EAAE,cAAc;IAC7BC,WAAW,EAAE,eAAe;IAC5BC,SAAS,EAAE,cAAc;IACzBC,YAAY,EAAE,gBAAgB;IAC9BC,cAAc,EAAE,YAAY;IAC5BC,MAAM,EAAE,eAAe;IACvBC,QAAQ,EAAE,iBAAiB;IAC3BC,UAAU,EAAE,WAAW;IACvBC,MAAM,EAAE,aAAa;IACrBC,QAAQ,EAAE,aAAa;IACvBC,OAAO,EAAE,QAAQ;IACjBC,YAAY,EAAE;EAChB,CAAC;EAED;EACAC,YAAY,EAAE;IACZC,UAAU,EAAE,eAAe;IAC3BC,WAAW,EAAE,gBAAgB;IAC7BC,WAAW,EAAE,gBAAgB;IAC7BC,SAAS,EAAE,kBAAkB;IAC7BC,WAAW,EAAE,eAAe;IAC5BC,OAAO,EAAE,qBAAqB;IAC9BC,QAAQ,EAAE,WAAW;IACrBC,KAAK,EAAE,KAAK;IACZtE,QAAQ,EAAE,MAAM;IAChBuE,MAAM,EAAE,WAAW;IACnBC,aAAa,EAAE,gBAAgB;IAC/BC,WAAW,EAAE,cAAc;IAC3BC,YAAY,EAAE,mBAAmB;IACjCC,cAAc,EAAE,qBAAqB;IACrCC,aAAa,EAAE,oBAAoB;IACnCC,kBAAkB,EAAE,sBAAsB;IAC1CC,mBAAmB,EAAE,mBAAmB;IACxCC,iBAAiB,EAAE;EACrB,CAAC;EAED;EACAC,WAAW,EAAE;IACXA,WAAW,EAAE,oBAAoB;IACjCC,YAAY,EAAE,iBAAiB;IAC/BC,UAAU,EAAE,aAAa;IACzBC,WAAW,EAAE,gBAAgB;IAC7BC,WAAW,EAAE,eAAe;IAC5BC,WAAW,EAAE,aAAa;IAC1BC,UAAU,EAAE,iBAAiB;IAC7BC,UAAU,EAAE,YAAY;IACxBC,SAAS,EAAE,iBAAiB;IAC5BC,WAAW,EAAE,wBAAwB;IACrCC,WAAW,EAAE,qBAAqB;IAClCC,eAAe,EAAE,mBAAmB;IACpCC,cAAc,EAAE,aAAa;IAC7BC,aAAa,EAAE,gBAAgB;IAC/BC,aAAa,EAAE;EACjB,CAAC;EAED;EACAC,MAAM,EAAE;IACNA,MAAM,EAAE,gBAAgB;IACxBC,WAAW,EAAE,gBAAgB;IAC7BC,cAAc,EAAE,oBAAoB;IACpCC,sBAAsB,EAAE,6CAA6C;IACrEC,cAAc,EAAE,cAAc;IAC9BC,YAAY,EAAE,iBAAiB;IAC/BC,SAAS,EAAE,eAAe;IAC1BC,QAAQ,EAAE,QAAQ;IAClBC,OAAO,EAAE,SAAS;IAClBC,YAAY,EAAE,WAAW;IACzBC,MAAM,EAAE,UAAU;IAClBC,aAAa,EAAE,cAAc;IAC7BC,MAAM,EAAE,WAAW;IACnBC,MAAM,EAAE,YAAY;IACpBC,WAAW,EAAE,SAAS;IACtBC,YAAY,EAAE,iBAAiB;IAC/BC,aAAa,EAAE,mBAAmB;IAClCC,kBAAkB,EAAE,2BAA2B;IAC/CC,aAAa,EAAE,2BAA2B;IAC1CC,eAAe,EAAE,sFAAsF;IACvGC,oBAAoB,EAAE,oBAAoB;IAC1CC,eAAe,EAAE,iBAAiB;IAClCC,cAAc,EAAE,oBAAoB;IACpCC,qBAAqB,EAAE,+BAA+B;IACtDC,gBAAgB,EAAE,mBAAmB;IACrCC,eAAe,EAAE,sCAAsC;IACvDC,gBAAgB,EAAE,kBAAkB;IACpCC,mBAAmB,EAAE,kCAAkC;IACvD/O,KAAK,EAAE,SAAS;IAChBgP,QAAQ,EAAE,OAAO;IACjBjJ,UAAU,EAAE,OAAO;IACnBkJ,aAAa,EAAE,mBAAmB;IAClCrH,KAAK,EAAE,QAAQ;IACfsH,MAAM,EAAE;EACV,CAAC;EAED;EACAC,EAAE,EAAE;IACF9C,WAAW,EAAE,cAAc;IAC3B+C,aAAa,EAAE,cAAc;IAC7BzC,UAAU,EAAE,2BAA2B;IACvCC,UAAU,EAAE,YAAY;IACxByC,cAAc,EAAE,yBAAyB;IACzCC,aAAa,EAAE,mBAAmB;IAClCC,MAAM,EAAE,cAAc;IACtB/C,WAAW,EAAE,gBAAgB;IAE7B;IACAgD,oBAAoB,EAAE,wBAAwB;IAC9CC,mBAAmB,EAAE,gCAAgC;IACrDC,aAAa,EAAE,yCAAyC;IACxDC,iBAAiB,EAAE,iBAAiB;IACpCC,gBAAgB,EAAE,wDAAwD;IAE1E;IACAC,OAAO,EAAE,YAAY;IACrBC,iBAAiB,EAAE,mBAAmB;IACtCC,UAAU,EAAE,YAAY;IAExB;IACAC,cAAc,EAAE,sBAAsB;IACtCC,eAAe,EAAE,kBAAkB;IAEnC;IACAC,OAAO,EAAE,kDAAkD;IAC3DC,aAAa,EAAE,mCAAmC;IAClDrP,QAAQ,EAAE;EACZ,CAAC;EAED;EACAsP,OAAO,EAAE;IACPC,MAAM,EAAE,WAAW;IACnB5O,MAAM,EAAE,QAAQ;IAChBD,IAAI,EAAE,SAAS;IACfG,IAAI,EAAE,QAAQ;IACdD,MAAM,EAAE,MAAM;IACd4O,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE,MAAM;IACdC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAE,OAAO;IACfC,OAAO,EAAE,YAAY;IACrBC,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,OAAO;IACbC,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,QAAQ;IAChBC,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE,WAAW;IAClBC,IAAI,EAAE,OAAO;IACb9O,QAAQ,EAAE,OAAO;IACjB+O,MAAM,EAAE,OAAO;IACfC,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,OAAO;IACdC,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE,OAAO;IACfC,QAAQ,EAAE;EACZ,CAAC;EAED;EACAC,MAAM,EAAE;IACNtQ,OAAO,EAAE,aAAa;IACtBuQ,MAAM,EAAE,eAAe;IACvBC,KAAK,EAAE,cAAc;IACrBvQ,KAAK,EAAE,UAAU;IACjBC,OAAO,EAAE,WAAW;IACpBuQ,OAAO,EAAE,MAAM;IACfC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,WAAW;IACpBC,MAAM,EAAE,YAAY;IACpBC,SAAS,EAAE,aAAa;IACxBC,UAAU,EAAE,aAAa;IACzBC,SAAS,EAAE,UAAU;IACrBC,WAAW,EAAE,UAAU;IACvBC,UAAU,EAAE,cAAc;IAC1BC,SAAS,EAAE,eAAe;IAC1BC,YAAY,EAAE;EAChB,CAAC;EAED;EACAC,UAAU,EAAE;IACVC,QAAQ,EAAE,eAAe;IACzBC,YAAY,EAAE,sBAAsB;IACpCC,gBAAgB,EAAE,qBAAqB;IACvCC,mBAAmB,EAAE,oBAAoB;IACzCC,kBAAkB,EAAE,2BAA2B;IAC/CC,YAAY,EAAE,mBAAmB;IACjCC,aAAa,EAAE,mBAAmB;IAClCC,aAAa,EAAE,kBAAkB;IACjCC,aAAa,EAAE,YAAY;IAC3BC,QAAQ,EAAE,eAAe;IACzBC,YAAY,EAAE,sBAAsB;IACpCC,cAAc,EAAE;EAClB;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,uBAAuB,GAAGA,CAACC,GAAG,EAAEC,QAAQ,GAAGD,GAAG,KAAK;EAC9D,MAAME,IAAI,GAAGF,GAAG,CAACG,KAAK,CAAC,GAAG,CAAC;EAC3B,IAAIC,WAAW,GAAG9U,qBAAqB;EAEvC,KAAK,MAAM+U,CAAC,IAAIH,IAAI,EAAE;IACpB,IAAIE,WAAW,IAAIA,WAAW,CAACC,CAAC,CAAC,EAAE;MACjCD,WAAW,GAAGA,WAAW,CAACC,CAAC,CAAC;IAC9B,CAAC,MAAM;MACL,OAAOJ,QAAQ;IACjB;EACF;EAEA,OAAOG,WAAW,IAAIH,QAAQ;AAChC,CAAC;;AAED;AACA,OAAO,MAAMK,eAAe,GAAIC,SAAS,IAAK;EAC5C,OAAOA,SAAS,KAAK,mBAAmB;AAC1C,CAAC;AAED,eAAejV,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}