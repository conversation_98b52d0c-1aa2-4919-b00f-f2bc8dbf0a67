{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Forum\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport \"./index.css\";\nimport { getUserInfo } from \"../../../apicalls/users\";\nimport { message, Button, Input, Form, Avatar, Pagination } from \"antd\";\nimport PageTitle from \"../../../components/PageTitle\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport ProfilePicture from \"../../../components/common/ProfilePicture\";\nimport OnlineStatusIndicator from \"../../../components/common/OnlineStatusIndicator\";\nimport { useLanguage } from \"../../../contexts/LanguageContext\";\nimport { addQuestion, addReply, getAllQuestions, deleteQuestion, updateQuestion, updateReplyStatus } from \"../../../apicalls/forum\";\nimport image from \"../../../assets/person.png\";\nimport { FaPencilAlt } from \"react-icons/fa\";\nimport { MdDelete, MdMessage } from \"react-icons/md\";\nimport { FaCheck } from \"react-icons/fa\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Forum = () => {\n  _s();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    t,\n    isKiswahili\n  } = useLanguage();\n  const [isAdmin, setIsAdmin] = useState(false);\n  const [userData, setUserData] = useState(\"\");\n  const [questions, setQuestions] = useState([]);\n  const [expandedReplies, setExpandedReplies] = useState({});\n  const [askQuestionVisible, setAskQuestionVisible] = useState(false);\n  const [replyQuestionId, setReplyQuestionId] = useState(null);\n  const [editQuestion, setEditQuestion] = useState(null);\n  const [form] = Form.useForm();\n  const [isInitialLoad, setIsInitialLoad] = useState(true);\n  const dispatch = useDispatch();\n\n  // Skeleton loader component\n  const ForumSkeleton = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"forum-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"forum-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-8 bg-gray-200 rounded w-48 mb-4 animate-pulse\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-10 bg-blue-100 rounded w-32 animate-pulse\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"forum-content\",\n      children: [...Array(5)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"question-card mb-4 p-4 border rounded-lg\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-start space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-10 h-10 bg-gray-200 rounded-full animate-pulse\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-4 bg-gray-200 rounded w-3/4 mb-2 animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-3 bg-gray-100 rounded w-1/2 mb-3 animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-3 bg-gray-100 rounded w-full mb-2 animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-3 bg-gray-100 rounded w-2/3 animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 13\n        }, this)\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 40,\n    columnNumber: 5\n  }, this);\n  const [form2] = Form.useForm();\n  const [replyRefs, setReplyRefs] = useState({});\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [limit] = useState(10);\n  const [totalQuestions, setTotalQuestions] = useState(0);\n\n  // Function to clear all forum caches\n  const clearAllForumCaches = () => {\n    const allLevels = ['primary', 'secondary', 'advance'];\n    allLevels.forEach(level => {\n      for (let p = 1; p <= 20; p++) {\n        // Clear up to 20 pages\n        localStorage.removeItem(`forum_questions_${level}_${p}_${limit}`);\n        localStorage.removeItem(`forum_questions_${level}_${p}_${limit}_time`);\n      }\n    });\n  };\n  const fetchQuestions = async page => {\n    try {\n      // Clear caches for other levels to prevent contamination\n      const userLevel = (userData === null || userData === void 0 ? void 0 : userData.level) || 'primary';\n      const allLevels = ['primary', 'secondary', 'advance'];\n      allLevels.forEach(level => {\n        if (level !== userLevel) {\n          // Clear cache for other levels\n          for (let p = 1; p <= 10; p++) {\n            // Clear up to 10 pages\n            localStorage.removeItem(`forum_questions_${level}_${p}_${limit}`);\n            localStorage.removeItem(`forum_questions_${level}_${p}_${limit}_time`);\n          }\n        }\n      });\n\n      // Check cache first - make it level-specific to prevent cross-level contamination\n      const cacheKey = `forum_questions_${userLevel}_${page}_${limit}`;\n      const cachedData = localStorage.getItem(cacheKey);\n      const cacheTime = localStorage.getItem(`${cacheKey}_time`);\n      const now = Date.now();\n\n      // Use cache if less than 5 minutes old\n      if (cachedData && cacheTime && now - parseInt(cacheTime) < 300000) {\n        const cached = JSON.parse(cachedData);\n        setQuestions(cached.questions);\n        setTotalQuestions(cached.totalQuestions);\n        setTotalPages(cached.totalPages);\n        return;\n      }\n      dispatch(ShowLoading());\n      const response = await getAllQuestions({\n        page,\n        limit\n      }); // Pass query params to API call\n      if (response.success) {\n        console.log(response.data);\n        setQuestions(response.data); // No need to reverse as backend will handle order\n        setTotalQuestions(response.totalQuestions);\n        setTotalPages(response.totalPages);\n\n        // Cache the data with level-specific key\n        localStorage.setItem(cacheKey, JSON.stringify({\n          questions: response.data,\n          totalQuestions: response.totalQuestions,\n          totalPages: response.totalPages\n        }));\n        localStorage.setItem(`${cacheKey}_time`, now.toString());\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  useEffect(() => {\n    // Clear caches for other levels when component mounts or page changes\n    clearAllForumCaches();\n    fetchQuestions(currentPage).finally(() => {\n      setIsInitialLoad(false);\n    });\n  }, [currentPage, limit]);\n  const handlePageChange = page => {\n    setCurrentPage(page);\n  };\n  const getUserData = async () => {\n    dispatch(ShowLoading());\n    try {\n      const response = await getUserInfo();\n      if (response.success) {\n        if (response.data.isAdmin) {\n          setIsAdmin(true);\n          setUserData(response.data);\n          await fetchQuestions();\n        } else {\n          setIsAdmin(false);\n          setUserData(response.data);\n          await fetchQuestions();\n        }\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  useEffect(() => {\n    if (localStorage.getItem(\"token\")) {\n      getUserData();\n    }\n  }, []);\n  const toggleReplies = questionId => {\n    setExpandedReplies(prevExpandedReplies => ({\n      ...prevExpandedReplies,\n      [questionId]: !prevExpandedReplies[questionId]\n    }));\n  };\n  const handleAskQuestion = async values => {\n    try {\n      // Create optimistic question object\n      const optimisticQuestion = {\n        _id: `temp_${Date.now()}`,\n        title: values.title,\n        body: values.body,\n        user: {\n          _id: userData._id,\n          name: userData.name,\n          email: userData.email\n        },\n        replies: [],\n        createdAt: new Date().toISOString(),\n        level: userData.level,\n        isOptimistic: true // Flag to identify optimistic updates\n      };\n\n      // Add question optimistically to the UI\n      setQuestions(prevQuestions => [optimisticQuestion, ...prevQuestions]);\n\n      // Close form and reset immediately\n      setAskQuestionVisible(false);\n      form.resetFields();\n      message.success(\"Question posted successfully!\");\n\n      // Send to server in background\n      const response = await addQuestion(values);\n      if (response.success) {\n        // Replace optimistic question with real one from server\n        clearAllForumCaches();\n        await fetchQuestions();\n      } else {\n        // Remove optimistic question on failure\n        setQuestions(prevQuestions => prevQuestions.filter(q => q._id !== optimisticQuestion._id));\n        message.error(response.message);\n      }\n    } catch (error) {\n      // Remove optimistic question on error\n      setQuestions(prevQuestions => prevQuestions.filter(q => !q.isOptimistic));\n      message.error(error.message);\n    }\n  };\n  const handleReply = questionId => {\n    setReplyQuestionId(questionId);\n  };\n  const handleReplySubmit = async values => {\n    try {\n      const payload = {\n        questionId: replyQuestionId,\n        text: values.text\n      };\n      const response = await addReply(payload);\n      if (response.success) {\n        message.success(response.message);\n        setReplyQuestionId(null);\n        form.resetFields();\n        clearAllForumCaches(); // Clear cache when new reply is added\n        await fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  useEffect(() => {\n    if (replyQuestionId && !replyRefs[replyQuestionId]) {\n      setReplyRefs(prevRefs => ({\n        ...prevRefs,\n        [replyQuestionId]: /*#__PURE__*/React.createRef()\n      }));\n    }\n  }, [replyQuestionId, replyRefs]);\n  useEffect(() => {\n    if (replyQuestionId && replyRefs[replyQuestionId]) {\n      replyRefs[replyQuestionId].current.scrollIntoView({\n        behavior: \"smooth\"\n      });\n    }\n  }, [replyQuestionId, replyRefs]);\n  const handleEdit = question => {\n    setEditQuestion(question);\n  };\n  const handleDelete = async question => {\n    try {\n      const confirmDelete = window.confirm(\"Are you sure you want to delete this question?\");\n      if (!confirmDelete) {\n        return;\n      }\n      const response = await deleteQuestion(question._id);\n      if (response.success) {\n        message.success(response.message);\n        clearAllForumCaches(); // Clear cache when question is deleted\n        await fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  const handleUpdateQuestion = async values => {\n    try {\n      const response = await updateQuestion(values, editQuestion._id);\n      if (response.success) {\n        message.success(response.message);\n        setEditQuestion(null);\n        clearAllForumCaches(); // Clear cache when question is updated\n        await fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  const handleCancelUpdate = () => {\n    setEditQuestion(\"\");\n  };\n  const handleCancelAdd = () => {\n    setAskQuestionVisible(false);\n    form.resetFields();\n  };\n  useEffect(() => {\n    if (editQuestion) {\n      form2.setFieldsValue({\n        title: editQuestion.title,\n        body: editQuestion.body\n      });\n    } else {\n      form2.resetFields();\n    }\n  }, [editQuestion]);\n  const handleUpdateStatus = async (questionId, replyId, status) => {\n    try {\n      const response = await updateReplyStatus({\n        replyId,\n        status\n      }, questionId);\n      if (response.success) {\n        message.success(response.message);\n        clearAllForumCaches(); // Clear cache when reply status is updated\n        await fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n\n  // Show skeleton on initial load\n  if (isInitialLoad && questions.length === 0) {\n    return /*#__PURE__*/_jsxDEV(ForumSkeleton, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 350,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"Forum max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 lg:py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8 sm:mb-10 lg:mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl sm:rounded-2xl mb-4 sm:mb-6 shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"ri-discuss-line text-lg sm:text-xl lg:text-2xl text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 px-4\",\n          children: [\"Community \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600\",\n            children: \"Forum\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 23\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm sm:text-base lg:text-lg text-gray-600 max-w-xl sm:max-w-2xl mx-auto mb-6 sm:mb-8 px-4\",\n          children: \"Connect with fellow learners, ask questions, share knowledge, and grow together in our vibrant learning community.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setAskQuestionVisible(true),\n          className: \"inline-flex items-center px-6 py-3 sm:px-8 sm:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold rounded-lg sm:rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 text-sm sm:text-base\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"ri-add-line text-lg sm:text-xl mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 13\n          }, this), \"Ask a Question\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this), askQuestionVisible && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl sm:rounded-2xl shadow-xl p-4 sm:p-6 lg:p-8 mb-6 sm:mb-8 border border-gray-100\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center mb-4 sm:mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center mr-3 sm:mr-4\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"ri-question-line text-white text-sm sm:text-lg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl sm:text-2xl font-bold text-gray-900\",\n            children: \"Ask a Question\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form, {\n          form: form,\n          onFinish: handleAskQuestion,\n          layout: \"vertical\",\n          className: \"modern-form\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"title\",\n            label: \"Question Title\",\n            rules: [{\n              required: true,\n              message: \"Please enter a descriptive title\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"What would you like to know?\",\n              className: \"h-12 text-lg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"body\",\n            label: \"Question Details\",\n            rules: [{\n              required: true,\n              message: \"Please provide more details about your question\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n              rows: 6,\n              placeholder: \"Describe your question in detail. The more information you provide, the better answers you'll receive.\",\n              className: \"text-base\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            className: \"mb-0\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                htmlType: \"submit\",\n                className: \"h-12 px-8 bg-gradient-to-r from-blue-600 to-indigo-600 border-none rounded-lg font-semibold text-base\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"ri-send-plane-line mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 417,\n                  columnNumber: 21\n                }, this), \"Post Question\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                onClick: handleCancelAdd,\n                className: \"h-12 px-6 border-gray-300 rounded-lg font-semibold text-base hover:bg-gray-50\",\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 380,\n        columnNumber: 11\n      }, this), questions.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"ri-loader-4-line text-2xl text-gray-400 animate-spin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 435,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500\",\n          children: \"Loading discussions...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 438,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 434,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4 sm:space-y-6\",\n        children: questions.filter(question => question && question.user).map(question => {\n          var _question$user, _question$user2;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl sm:rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 sm:p-6 border-b border-gray-100\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3 sm:space-x-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative\",\n                    children: [/*#__PURE__*/_jsxDEV(ProfilePicture, {\n                      user: question.user,\n                      size: \"sm\",\n                      showOnlineStatus: false,\n                      style: {\n                        width: '32px',\n                        height: '32px'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 451,\n                      columnNumber: 23\n                    }, this), question.user && question.user.isOnline && /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        position: 'absolute',\n                        bottom: '-2px',\n                        right: '-2px',\n                        width: '12px',\n                        height: '12px',\n                        backgroundColor: '#22c55e',\n                        borderRadius: '50%',\n                        border: '2px solid #ffffff',\n                        boxShadow: '0 2px 8px rgba(34, 197, 94, 0.6)',\n                        zIndex: 10\n                      },\n                      title: \"Online\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 462,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"min-w-0 flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-semibold text-gray-900 text-sm sm:text-base truncate\",\n                      children: ((_question$user = question.user) === null || _question$user === void 0 ? void 0 : _question$user.name) || 'Unknown User'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 480,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs sm:text-sm text-gray-500\",\n                      children: new Date(question.createdAt).toLocaleDateString('en-US', {\n                        year: 'numeric',\n                        month: 'short',\n                        day: 'numeric',\n                        hour: '2-digit',\n                        minute: '2-digit'\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 481,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 479,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-1 sm:space-x-2 ml-2\",\n                  children: (userData._id === ((_question$user2 = question.user) === null || _question$user2 === void 0 ? void 0 : _question$user2._id) || userData.isAdmin) && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleEdit(question),\n                      className: \"flex items-center px-2 py-2 sm:px-3 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200\",\n                      children: /*#__PURE__*/_jsxDEV(FaPencilAlt, {\n                        className: \"w-3 h-3 sm:w-4 sm:h-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 501,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 497,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleDelete(question),\n                      className: \"flex items-center px-2 py-2 sm:px-3 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200\",\n                      children: /*#__PURE__*/_jsxDEV(MdDelete, {\n                        className: \"w-3 h-3 sm:w-4 sm:h-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 507,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 503,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 494,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 sm:p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg sm:text-xl font-bold text-gray-900 mb-3 leading-tight\",\n                children: question.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-700 leading-relaxed mb-6\",\n                children: question.body\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 518,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between pt-3 sm:pt-4 border-t border-gray-100 gap-3 sm:gap-0\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 sm:space-x-4 overflow-x-auto\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => toggleReplies(question._id),\n                    className: \"flex items-center px-3 py-2 sm:px-4 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200 text-sm sm:text-base whitespace-nowrap\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"ri-eye-line mr-1 sm:mr-2 text-sm sm:text-base\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 527,\n                      columnNumber: 23\n                    }, this), expandedReplies[question._id] ? \"Hide Replies\" : \"View Replies\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 523,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleReply(question._id),\n                    className: \"flex items-center px-3 py-2 sm:px-4 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors duration-200 text-sm sm:text-base whitespace-nowrap\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"ri-reply-line mr-1 sm:mr-2 text-sm sm:text-base\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 534,\n                      columnNumber: 23\n                    }, this), \"Reply\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 530,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 522,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center px-2 py-1.5 sm:px-3 sm:py-2 bg-gray-50 rounded-lg self-start sm:self-auto\",\n                  children: [/*#__PURE__*/_jsxDEV(MdMessage, {\n                    className: \"w-3 h-3 sm:w-4 sm:h-4 text-gray-500 mr-1 sm:mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 540,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-xs sm:text-sm font-medium text-gray-700\",\n                    children: question.replies.length\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 541,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 539,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 521,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 15\n            }, this), editQuestion && editQuestion._id === question._id && /*#__PURE__*/_jsxDEV(Form, {\n              form: form2,\n              onFinish: handleUpdateQuestion,\n              layout: \"vertical\",\n              initialValues: {\n                title: editQuestion.title,\n                body: editQuestion.body\n              },\n              children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"title\",\n                label: \"Title\",\n                rules: [{\n                  required: true,\n                  message: \"Please enter the title\"\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  style: {\n                    padding: \"18px 12px\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 564,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 557,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"body\",\n                label: \"Body\",\n                rules: [{\n                  required: true,\n                  message: \"Please enter the body\"\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input.TextArea, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 571,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 566,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  htmlType: \"submit\",\n                  children: \"Update Question\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 574,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  onClick: handleCancelUpdate,\n                  style: {\n                    marginLeft: 10\n                  },\n                  children: \"Cancel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 577,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 573,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 17\n            }, this), expandedReplies[question._id] && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4 sm:mt-6 space-y-3 sm:space-y-4 bg-gray-50 rounded-lg sm:rounded-xl p-3 sm:p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-base sm:text-lg font-semibold text-gray-800 mb-3 sm:mb-4 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"ri-chat-3-line mr-2 text-blue-600 text-sm sm:text-base\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 589,\n                  columnNumber: 19\n                }, this), \"Replies (\", question.replies.filter(reply => reply && reply.user).length, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 588,\n                columnNumber: 17\n              }, this), question.replies.filter(reply => reply && reply.user).map((reply, index) => {\n                var _reply$user, _reply$user2, _reply$user3, _reply$user4, _reply$user5, _reply$user6, _reply$user7;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `bg-white rounded-lg p-3 sm:p-4 shadow-sm border-l-4 ${(_reply$user = reply.user) !== null && _reply$user !== void 0 && _reply$user.isAdmin ? \"border-purple-500 bg-purple-50\" : reply.isVerified ? \"border-green-500 bg-green-50\" : \"border-gray-300\"}`,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-start space-x-2 sm:space-x-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-shrink-0 relative\",\n                      children: [/*#__PURE__*/_jsxDEV(ProfilePicture, {\n                        user: reply.user,\n                        size: \"xs\",\n                        showOnlineStatus: false,\n                        style: {\n                          width: '20px',\n                          height: '20px'\n                        },\n                        className: \"sm:w-6 sm:h-6\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 606,\n                        columnNumber: 25\n                      }, this), reply.user && reply.user.isOnline && /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          position: 'absolute',\n                          bottom: '-1px',\n                          right: '-1px',\n                          width: '6px',\n                          height: '6px',\n                          backgroundColor: '#22c55e',\n                          borderRadius: '50%',\n                          border: '1px solid #ffffff',\n                          boxShadow: '0 1px 4px rgba(34, 197, 94, 0.6)',\n                          zIndex: 10\n                        },\n                        className: \"sm:w-2 sm:h-2\",\n                        title: \"Online\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 618,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 605,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1 min-w-0\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-start sm:items-center justify-between mb-2 gap-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center space-x-1 sm:space-x-2 min-w-0 flex-1\",\n                          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                            className: \"font-semibold text-gray-900 text-sm sm:text-base truncate\",\n                            children: ((_reply$user2 = reply.user) === null || _reply$user2 === void 0 ? void 0 : _reply$user2.name) || 'Unknown User'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 642,\n                            columnNumber: 29\n                          }, this), ((_reply$user3 = reply.user) === null || _reply$user3 === void 0 ? void 0 : _reply$user3.isAdmin) && /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"px-1.5 py-0.5 sm:px-2 sm:py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full whitespace-nowrap\",\n                            children: \"Admin\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 644,\n                            columnNumber: 31\n                          }, this), reply.isVerified && !((_reply$user4 = reply.user) !== null && _reply$user4 !== void 0 && _reply$user4.isAdmin) && /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center space-x-1\",\n                            children: [/*#__PURE__*/_jsxDEV(FaCheck, {\n                              className: \"w-3 h-3 sm:w-4 sm:h-4 text-green-600\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 650,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"px-1.5 py-0.5 sm:px-2 sm:py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full whitespace-nowrap\",\n                              children: \"Verified\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 651,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 649,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 641,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-xs sm:text-sm text-gray-500 whitespace-nowrap\",\n                          children: (() => {\n                            try {\n                              const date = new Date(reply.createdAt);\n                              if (isNaN(date.getTime())) {\n                                return 'Invalid date';\n                              }\n                              return date.toLocaleDateString('en-US', {\n                                month: \"short\",\n                                day: \"numeric\",\n                                hour: \"2-digit\",\n                                minute: \"2-digit\"\n                              });\n                            } catch (error) {\n                              return 'Invalid date';\n                            }\n                          })()\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 657,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 640,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `leading-relaxed mb-3 text-sm sm:text-base ${reply.isVerified && !((_reply$user5 = reply.user) !== null && _reply$user5 !== void 0 && _reply$user5.isAdmin) ? 'text-green-800 font-medium' : (_reply$user6 = reply.user) !== null && _reply$user6 !== void 0 && _reply$user6.isAdmin ? 'text-purple-800 font-medium' : 'text-gray-700'}`,\n                        children: reply.text\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 678,\n                        columnNumber: 25\n                      }, this), isAdmin && !((_reply$user7 = reply.user) !== null && _reply$user7 !== void 0 && _reply$user7.isAdmin) && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex justify-end\",\n                        children: /*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: () => handleUpdateStatus(question._id, reply._id, !reply.isVerified),\n                          className: `px-3 py-1.5 sm:px-4 sm:py-2 text-xs sm:text-sm font-medium rounded-lg transition-colors duration-200 ${reply.isVerified ? \"bg-red-100 text-red-700 hover:bg-red-200\" : \"bg-green-100 text-green-700 hover:bg-green-200\"}`,\n                          children: reply.isVerified ? \"Disapprove\" : \"Approve\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 691,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 690,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 638,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 603,\n                    columnNumber: 21\n                  }, this)\n                }, reply._id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 593,\n                  columnNumber: 19\n                }, this);\n              })]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              ref: replyRefs[question._id],\n              className: \"mt-4 sm:mt-6\",\n              children: replyQuestionId === question._id && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-lg sm:rounded-xl p-4 sm:p-6 border border-gray-200\",\n                children: /*#__PURE__*/_jsxDEV(Form, {\n                  form: form,\n                  onFinish: handleReplySubmit,\n                  layout: \"vertical\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                    name: \"text\",\n                    label: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm sm:text-base font-medium\",\n                      children: \"Your Reply\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 725,\n                      columnNumber: 30\n                    }, this),\n                    rules: [{\n                      required: true,\n                      message: \"Please enter your reply\"\n                    }],\n                    children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n                      rows: 3,\n                      className: \"text-sm sm:text-base\",\n                      placeholder: \"Write your reply here...\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 730,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 723,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                    className: \"mb-0\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex flex-col sm:flex-row gap-2 sm:gap-3\",\n                      children: [/*#__PURE__*/_jsxDEV(Button, {\n                        type: \"primary\",\n                        htmlType: \"submit\",\n                        className: \"w-full sm:w-auto\",\n                        size: \"large\",\n                        children: \"Submit Reply\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 738,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        onClick: () => setReplyQuestionId(null),\n                        className: \"w-full sm:w-auto\",\n                        size: \"large\",\n                        children: \"Cancel\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 746,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 737,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 736,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 718,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 717,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 715,\n              columnNumber: 13\n            }, this)]\n          }, question._id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 13\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 443,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Pagination, {\n        current: currentPage,\n        total: totalQuestions,\n        pageSize: limit,\n        onChange: handlePageChange,\n        style: {\n          marginTop: \"20px\",\n          textAlign: \"center\"\n        },\n        showSizeChanger: false\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 764,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 355,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 354,\n    columnNumber: 5\n  }, this);\n};\n_s(Forum, \"r6NlT3BNEkT+PQjNuRtvULernQw=\", false, function () {\n  return [useSelector, useLanguage, Form.useForm, useDispatch, Form.useForm];\n});\n_c = Forum;\nexport default Forum;\nvar _c;\n$RefreshReg$(_c, \"Forum\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "getUserInfo", "message", "<PERSON><PERSON>", "Input", "Form", "Avatar", "Pagination", "Page<PERSON><PERSON>le", "useDispatch", "useSelector", "HideLoading", "ShowLoading", "ProfilePicture", "OnlineStatusIndicator", "useLanguage", "addQuestion", "addReply", "getAllQuestions", "deleteQuestion", "updateQuestion", "updateReplyStatus", "image", "FaPencilAlt", "MdDelete", "MdMessage", "FaCheck", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Forum", "_s", "user", "state", "t", "isKiswahili", "isAdmin", "setIsAdmin", "userData", "setUserData", "questions", "setQuestions", "expandedReplies", "setExpandedReplies", "askQuestionVisible", "setAskQuestionVisible", "replyQuestionId", "setReplyQuestionId", "editQuestion", "setEditQuestion", "form", "useForm", "isInitialLoad", "setIsInitialLoad", "dispatch", "ForumSkeleton", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Array", "map", "_", "i", "form2", "replyRefs", "setReplyRefs", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "limit", "totalQuestions", "setTotalQuestions", "clearAllForumCaches", "allLevels", "for<PERSON>ach", "level", "p", "localStorage", "removeItem", "fetchQuestions", "page", "userLevel", "cache<PERSON>ey", "cachedData", "getItem", "cacheTime", "now", "Date", "parseInt", "cached", "JSON", "parse", "response", "success", "console", "log", "data", "setItem", "stringify", "toString", "error", "finally", "handlePageChange", "getUserData", "toggleReplies", "questionId", "prevExpandedReplies", "handleAskQuestion", "values", "optimisticQuestion", "_id", "title", "body", "name", "email", "replies", "createdAt", "toISOString", "isOptimistic", "prevQuestions", "resetFields", "filter", "q", "handleReply", "handleReplySubmit", "payload", "text", "prevRefs", "createRef", "current", "scrollIntoView", "behavior", "handleEdit", "question", "handleDelete", "confirmDelete", "window", "confirm", "handleUpdateQuestion", "handleCancelUpdate", "handleCancelAdd", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleUpdateStatus", "replyId", "status", "length", "onClick", "onFinish", "layout", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "TextArea", "rows", "type", "htmlType", "_question$user", "_question$user2", "size", "showOnlineStatus", "style", "width", "height", "isOnline", "position", "bottom", "right", "backgroundColor", "borderRadius", "border", "boxShadow", "zIndex", "toLocaleDateString", "year", "month", "day", "hour", "minute", "initialValues", "padding", "marginLeft", "reply", "index", "_reply$user", "_reply$user2", "_reply$user3", "_reply$user4", "_reply$user5", "_reply$user6", "_reply$user7", "isVerified", "date", "isNaN", "getTime", "ref", "total", "pageSize", "onChange", "marginTop", "textAlign", "showSizeChanger", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Forum/index.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\r\nimport \"./index.css\";\r\nimport { getUserInfo } from \"../../../apicalls/users\";\r\nimport { message, Button, Input, Form, Avatar, Pagination } from \"antd\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport ProfilePicture from \"../../../components/common/ProfilePicture\";\r\nimport OnlineStatusIndicator from \"../../../components/common/OnlineStatusIndicator\";\r\nimport { useLanguage } from \"../../../contexts/LanguageContext\";\r\nimport {\r\n  addQuestion,\r\n  addReply,\r\n  getAllQuestions,\r\n  deleteQuestion,\r\n  updateQuestion,\r\n  updateReplyStatus,\r\n} from \"../../../apicalls/forum\";\r\nimport image from \"../../../assets/person.png\";\r\nimport { FaPencilAlt } from \"react-icons/fa\";\r\nimport { MdDelete, MdMessage } from \"react-icons/md\";\r\nimport { FaCheck } from \"react-icons/fa\";\r\n\r\nconst Forum = () => {\r\n  const { user } = useSelector((state) => state.user);\r\n  const { t, isKiswahili } = useLanguage();\r\n  const [isAdmin, setIsAdmin] = useState(false);\r\n  const [userData, setUserData] = useState(\"\");\r\n  const [questions, setQuestions] = useState([]);\r\n  const [expandedReplies, setExpandedReplies] = useState({});\r\n  const [askQuestionVisible, setAskQuestionVisible] = useState(false);\r\n  const [replyQuestionId, setReplyQuestionId] = useState(null);\r\n  const [editQuestion, setEditQuestion] = useState(null);\r\n  const [form] = Form.useForm();\r\n  const [isInitialLoad, setIsInitialLoad] = useState(true);\r\n  const dispatch = useDispatch();\r\n\r\n  // Skeleton loader component\r\n  const ForumSkeleton = () => (\r\n    <div className=\"forum-container\">\r\n      <div className=\"forum-header\">\r\n        <div className=\"h-8 bg-gray-200 rounded w-48 mb-4 animate-pulse\"></div>\r\n        <div className=\"h-10 bg-blue-100 rounded w-32 animate-pulse\"></div>\r\n      </div>\r\n      <div className=\"forum-content\">\r\n        {[...Array(5)].map((_, i) => (\r\n          <div key={i} className=\"question-card mb-4 p-4 border rounded-lg\">\r\n            <div className=\"flex items-start space-x-3\">\r\n              <div className=\"w-10 h-10 bg-gray-200 rounded-full animate-pulse\"></div>\r\n              <div className=\"flex-1\">\r\n                <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2 animate-pulse\"></div>\r\n                <div className=\"h-3 bg-gray-100 rounded w-1/2 mb-3 animate-pulse\"></div>\r\n                <div className=\"h-3 bg-gray-100 rounded w-full mb-2 animate-pulse\"></div>\r\n                <div className=\"h-3 bg-gray-100 rounded w-2/3 animate-pulse\"></div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n  const [form2] = Form.useForm();\r\n  const [replyRefs, setReplyRefs] = useState({});\r\n\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [totalPages, setTotalPages] = useState(1);\r\n  const [limit] = useState(10);\r\n  const [totalQuestions, setTotalQuestions] = useState(0);\r\n\r\n  // Function to clear all forum caches\r\n  const clearAllForumCaches = () => {\r\n    const allLevels = ['primary', 'secondary', 'advance'];\r\n    allLevels.forEach(level => {\r\n      for (let p = 1; p <= 20; p++) { // Clear up to 20 pages\r\n        localStorage.removeItem(`forum_questions_${level}_${p}_${limit}`);\r\n        localStorage.removeItem(`forum_questions_${level}_${p}_${limit}_time`);\r\n      }\r\n    });\r\n  };\r\n\r\n  const fetchQuestions = async (page) => {\r\n    try {\r\n      // Clear caches for other levels to prevent contamination\r\n      const userLevel = userData?.level || 'primary';\r\n      const allLevels = ['primary', 'secondary', 'advance'];\r\n      allLevels.forEach(level => {\r\n        if (level !== userLevel) {\r\n          // Clear cache for other levels\r\n          for (let p = 1; p <= 10; p++) { // Clear up to 10 pages\r\n            localStorage.removeItem(`forum_questions_${level}_${p}_${limit}`);\r\n            localStorage.removeItem(`forum_questions_${level}_${p}_${limit}_time`);\r\n          }\r\n        }\r\n      });\r\n\r\n      // Check cache first - make it level-specific to prevent cross-level contamination\r\n      const cacheKey = `forum_questions_${userLevel}_${page}_${limit}`;\r\n      const cachedData = localStorage.getItem(cacheKey);\r\n      const cacheTime = localStorage.getItem(`${cacheKey}_time`);\r\n      const now = Date.now();\r\n\r\n      // Use cache if less than 5 minutes old\r\n      if (cachedData && cacheTime && (now - parseInt(cacheTime)) < 300000) {\r\n        const cached = JSON.parse(cachedData);\r\n        setQuestions(cached.questions);\r\n        setTotalQuestions(cached.totalQuestions);\r\n        setTotalPages(cached.totalPages);\r\n        return;\r\n      }\r\n\r\n      dispatch(ShowLoading());\r\n      const response = await getAllQuestions({ page, limit }); // Pass query params to API call\r\n      if (response.success) {\r\n        console.log(response.data);\r\n        setQuestions(response.data); // No need to reverse as backend will handle order\r\n        setTotalQuestions(response.totalQuestions);\r\n        setTotalPages(response.totalPages);\r\n\r\n        // Cache the data with level-specific key\r\n        localStorage.setItem(cacheKey, JSON.stringify({\r\n          questions: response.data,\r\n          totalQuestions: response.totalQuestions,\r\n          totalPages: response.totalPages\r\n        }));\r\n        localStorage.setItem(`${cacheKey}_time`, now.toString());\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    // Clear caches for other levels when component mounts or page changes\r\n    clearAllForumCaches();\r\n    fetchQuestions(currentPage).finally(() => {\r\n      setIsInitialLoad(false);\r\n    });\r\n  }, [currentPage, limit]);\r\n\r\n  const handlePageChange = (page) => {\r\n    setCurrentPage(page);\r\n  };\r\n\r\n  const getUserData = async () => {\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await getUserInfo();\r\n      if (response.success) {\r\n        if (response.data.isAdmin) {\r\n          setIsAdmin(true);\r\n          setUserData(response.data);\r\n          await fetchQuestions();\r\n        } else {\r\n          setIsAdmin(false);\r\n          setUserData(response.data);\r\n          await fetchQuestions();\r\n        }\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (localStorage.getItem(\"token\")) {\r\n      getUserData();\r\n    }\r\n  }, []);\r\n\r\n  const toggleReplies = (questionId) => {\r\n    setExpandedReplies((prevExpandedReplies) => ({\r\n      ...prevExpandedReplies,\r\n      [questionId]: !prevExpandedReplies[questionId],\r\n    }));\r\n  };\r\n\r\n  const handleAskQuestion = async (values) => {\r\n    try {\r\n      // Create optimistic question object\r\n      const optimisticQuestion = {\r\n        _id: `temp_${Date.now()}`,\r\n        title: values.title,\r\n        body: values.body,\r\n        user: {\r\n          _id: userData._id,\r\n          name: userData.name,\r\n          email: userData.email\r\n        },\r\n        replies: [],\r\n        createdAt: new Date().toISOString(),\r\n        level: userData.level,\r\n        isOptimistic: true // Flag to identify optimistic updates\r\n      };\r\n\r\n      // Add question optimistically to the UI\r\n      setQuestions(prevQuestions => [optimisticQuestion, ...prevQuestions]);\r\n\r\n      // Close form and reset immediately\r\n      setAskQuestionVisible(false);\r\n      form.resetFields();\r\n      message.success(\"Question posted successfully!\");\r\n\r\n      // Send to server in background\r\n      const response = await addQuestion(values);\r\n      if (response.success) {\r\n        // Replace optimistic question with real one from server\r\n        clearAllForumCaches();\r\n        await fetchQuestions();\r\n      } else {\r\n        // Remove optimistic question on failure\r\n        setQuestions(prevQuestions =>\r\n          prevQuestions.filter(q => q._id !== optimisticQuestion._id)\r\n        );\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      // Remove optimistic question on error\r\n      setQuestions(prevQuestions =>\r\n        prevQuestions.filter(q => !q.isOptimistic)\r\n      );\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n\r\n\r\n  const handleReply = (questionId) => {\r\n    setReplyQuestionId(questionId);\r\n  };\r\n\r\n  const handleReplySubmit = async (values) => {\r\n    try {\r\n      const payload = {\r\n        questionId: replyQuestionId,\r\n        text: values.text,\r\n      };\r\n      const response = await addReply(payload);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        setReplyQuestionId(null);\r\n        form.resetFields();\r\n        clearAllForumCaches(); // Clear cache when new reply is added\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (replyQuestionId && !replyRefs[replyQuestionId]) {\r\n      setReplyRefs((prevRefs) => ({\r\n        ...prevRefs,\r\n        [replyQuestionId]: React.createRef(),\r\n      }));\r\n    }\r\n  }, [replyQuestionId, replyRefs]);\r\n\r\n  useEffect(() => {\r\n    if (replyQuestionId && replyRefs[replyQuestionId]) {\r\n      replyRefs[replyQuestionId].current.scrollIntoView({ behavior: \"smooth\" });\r\n    }\r\n  }, [replyQuestionId, replyRefs]);\r\n\r\n  const handleEdit = (question) => {\r\n    setEditQuestion(question);\r\n  };\r\n\r\n  const handleDelete = async (question) => {\r\n    try {\r\n      const confirmDelete = window.confirm(\r\n        \"Are you sure you want to delete this question?\"\r\n      );\r\n      if (!confirmDelete) {\r\n        return;\r\n      }\r\n      const response = await deleteQuestion(question._id);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        clearAllForumCaches(); // Clear cache when question is deleted\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const handleUpdateQuestion = async (values) => {\r\n    try {\r\n      const response = await updateQuestion(values, editQuestion._id);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        setEditQuestion(null);\r\n        clearAllForumCaches(); // Clear cache when question is updated\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const handleCancelUpdate = () => {\r\n    setEditQuestion(\"\");\r\n  };\r\n  const handleCancelAdd = () => {\r\n    setAskQuestionVisible(false);\r\n    form.resetFields();\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (editQuestion) {\r\n      form2.setFieldsValue({\r\n        title: editQuestion.title,\r\n        body: editQuestion.body,\r\n      });\r\n    } else {\r\n      form2.resetFields();\r\n    }\r\n  }, [editQuestion]);\r\n\r\n  const handleUpdateStatus = async (questionId, replyId, status) => {\r\n    try {\r\n      const response = await updateReplyStatus({ replyId, status }, questionId);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        clearAllForumCaches(); // Clear cache when reply status is updated\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  // Show skeleton on initial load\r\n  if (isInitialLoad && questions.length === 0) {\r\n    return <ForumSkeleton />;\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\r\n      <div className=\"Forum max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 lg:py-8\">\r\n        {/* Modern Header Section */}\r\n        <div className=\"text-center mb-8 sm:mb-10 lg:mb-12\">\r\n          <div className=\"inline-flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl sm:rounded-2xl mb-4 sm:mb-6 shadow-lg\">\r\n            <i className=\"ri-discuss-line text-lg sm:text-xl lg:text-2xl text-white\"></i>\r\n          </div>\r\n          <h1 className=\"text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 px-4\">\r\n            Community <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600\">Forum</span>\r\n          </h1>\r\n          <p className=\"text-sm sm:text-base lg:text-lg text-gray-600 max-w-xl sm:max-w-2xl mx-auto mb-6 sm:mb-8 px-4\">\r\n            Connect with fellow learners, ask questions, share knowledge, and grow together in our vibrant learning community.\r\n          </p>\r\n\r\n          {/* Ask Question Button */}\r\n          <button\r\n            onClick={() => setAskQuestionVisible(true)}\r\n            className=\"inline-flex items-center px-6 py-3 sm:px-8 sm:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold rounded-lg sm:rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 text-sm sm:text-base\"\r\n          >\r\n            <i className=\"ri-add-line text-lg sm:text-xl mr-2\"></i>\r\n            Ask a Question\r\n          </button>\r\n        </div>\r\n\r\n        {/* Modern Ask Question Form */}\r\n        {askQuestionVisible && (\r\n          <div className=\"bg-white rounded-xl sm:rounded-2xl shadow-xl p-4 sm:p-6 lg:p-8 mb-6 sm:mb-8 border border-gray-100\">\r\n            <div className=\"flex items-center mb-4 sm:mb-6\">\r\n              <div className=\"w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center mr-3 sm:mr-4\">\r\n                <i className=\"ri-question-line text-white text-sm sm:text-lg\"></i>\r\n              </div>\r\n              <h2 className=\"text-xl sm:text-2xl font-bold text-gray-900\">Ask a Question</h2>\r\n            </div>\r\n\r\n            <Form form={form} onFinish={handleAskQuestion} layout=\"vertical\" className=\"modern-form\">\r\n              <Form.Item\r\n                name=\"title\"\r\n                label=\"Question Title\"\r\n                rules={[{ required: true, message: \"Please enter a descriptive title\" }]}\r\n              >\r\n                <Input\r\n                  placeholder=\"What would you like to know?\"\r\n                  className=\"h-12 text-lg\"\r\n                />\r\n              </Form.Item>\r\n              <Form.Item\r\n                name=\"body\"\r\n                label=\"Question Details\"\r\n                rules={[{ required: true, message: \"Please provide more details about your question\" }]}\r\n              >\r\n                <Input.TextArea\r\n                  rows={6}\r\n                  placeholder=\"Describe your question in detail. The more information you provide, the better answers you'll receive.\"\r\n                  className=\"text-base\"\r\n                />\r\n              </Form.Item>\r\n              <Form.Item className=\"mb-0\">\r\n                <div className=\"flex items-center space-x-4\">\r\n                  <Button\r\n                    type=\"primary\"\r\n                    htmlType=\"submit\"\r\n                    className=\"h-12 px-8 bg-gradient-to-r from-blue-600 to-indigo-600 border-none rounded-lg font-semibold text-base\"\r\n                  >\r\n                    <i className=\"ri-send-plane-line mr-2\"></i>\r\n                    Post Question\r\n                  </Button>\r\n                  <Button\r\n                    onClick={handleCancelAdd}\r\n                    className=\"h-12 px-6 border-gray-300 rounded-lg font-semibold text-base hover:bg-gray-50\"\r\n                  >\r\n                    Cancel\r\n                  </Button>\r\n                </div>\r\n              </Form.Item>\r\n            </Form>\r\n          </div>\r\n        )}\r\n\r\n        {/* Loading State */}\r\n        {questions.length === 0 && (\r\n          <div className=\"text-center py-12\">\r\n            <div className=\"inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4\">\r\n              <i className=\"ri-loader-4-line text-2xl text-gray-400 animate-spin\"></i>\r\n            </div>\r\n            <p className=\"text-gray-500\">Loading discussions...</p>\r\n          </div>\r\n        )}\r\n\r\n        {/* Questions Grid */}\r\n        <div className=\"space-y-4 sm:space-y-6\">\r\n          {questions.filter(question => question && question.user).map((question) => (\r\n            <div key={question._id} className=\"bg-white rounded-xl sm:rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100\">\r\n              {/* Question Header */}\r\n              <div className=\"p-4 sm:p-6 border-b border-gray-100\">\r\n                <div className=\"flex items-start justify-between\">\r\n                  <div className=\"flex items-center space-x-3 sm:space-x-4\">\r\n                    <div className=\"relative\">\r\n                      <ProfilePicture\r\n                        user={question.user}\r\n                        size=\"sm\"\r\n                        showOnlineStatus={false}\r\n                        style={{\r\n                          width: '32px',\r\n                          height: '32px'\r\n                        }}\r\n                      />\r\n                      {/* Only show online dot if user exists and is actually online */}\r\n                      {question.user && question.user.isOnline && (\r\n                        <div\r\n                          style={{\r\n                            position: 'absolute',\r\n                            bottom: '-2px',\r\n                            right: '-2px',\r\n                            width: '12px',\r\n                            height: '12px',\r\n                            backgroundColor: '#22c55e',\r\n                            borderRadius: '50%',\r\n                            border: '2px solid #ffffff',\r\n                            boxShadow: '0 2px 8px rgba(34, 197, 94, 0.6)',\r\n                            zIndex: 10\r\n                          }}\r\n                          title=\"Online\"\r\n                        />\r\n                      )}\r\n                    </div>\r\n                    <div className=\"min-w-0 flex-1\">\r\n                      <h4 className=\"font-semibold text-gray-900 text-sm sm:text-base truncate\">{question.user?.name || 'Unknown User'}</h4>\r\n                      <p className=\"text-xs sm:text-sm text-gray-500\">\r\n                        {new Date(question.createdAt).toLocaleDateString('en-US', {\r\n                          year: 'numeric',\r\n                          month: 'short',\r\n                          day: 'numeric',\r\n                          hour: '2-digit',\r\n                          minute: '2-digit'\r\n                        })}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Action Buttons */}\r\n                  <div className=\"flex items-center space-x-1 sm:space-x-2 ml-2\">\r\n                    {(userData._id === question.user?._id || userData.isAdmin) && (\r\n                      <>\r\n                        <button\r\n                          onClick={() => handleEdit(question)}\r\n                          className=\"flex items-center px-2 py-2 sm:px-3 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200\"\r\n                        >\r\n                          <FaPencilAlt className=\"w-3 h-3 sm:w-4 sm:h-4\" />\r\n                        </button>\r\n                        <button\r\n                          onClick={() => handleDelete(question)}\r\n                          className=\"flex items-center px-2 py-2 sm:px-3 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200\"\r\n                        >\r\n                          <MdDelete className=\"w-3 h-3 sm:w-4 sm:h-4\" />\r\n                        </button>\r\n                      </>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Question Content */}\r\n              <div className=\"p-4 sm:p-6\">\r\n                <h3 className=\"text-lg sm:text-xl font-bold text-gray-900 mb-3 leading-tight\">{question.title}</h3>\r\n                <p className=\"text-gray-700 leading-relaxed mb-6\">{question.body}</p>\r\n\r\n                {/* Action Bar */}\r\n                <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between pt-3 sm:pt-4 border-t border-gray-100 gap-3 sm:gap-0\">\r\n                  <div className=\"flex items-center space-x-2 sm:space-x-4 overflow-x-auto\">\r\n                    <button\r\n                      onClick={() => toggleReplies(question._id)}\r\n                      className=\"flex items-center px-3 py-2 sm:px-4 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200 text-sm sm:text-base whitespace-nowrap\"\r\n                    >\r\n                      <i className=\"ri-eye-line mr-1 sm:mr-2 text-sm sm:text-base\"></i>\r\n                      {expandedReplies[question._id] ? \"Hide Replies\" : \"View Replies\"}\r\n                    </button>\r\n                    <button\r\n                      onClick={() => handleReply(question._id)}\r\n                      className=\"flex items-center px-3 py-2 sm:px-4 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors duration-200 text-sm sm:text-base whitespace-nowrap\"\r\n                    >\r\n                      <i className=\"ri-reply-line mr-1 sm:mr-2 text-sm sm:text-base\"></i>\r\n                      Reply\r\n                    </button>\r\n                  </div>\r\n\r\n                  <div className=\"flex items-center px-2 py-1.5 sm:px-3 sm:py-2 bg-gray-50 rounded-lg self-start sm:self-auto\">\r\n                    <MdMessage className=\"w-3 h-3 sm:w-4 sm:h-4 text-gray-500 mr-1 sm:mr-2\" />\r\n                    <span className=\"text-xs sm:text-sm font-medium text-gray-700\">{question.replies.length}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Edit Question Form */}\r\n              {editQuestion && editQuestion._id === question._id && (\r\n                <Form\r\n                form={form2}\r\n                onFinish={handleUpdateQuestion}\r\n                layout=\"vertical\"\r\n                initialValues={{\r\n                  title: editQuestion.title,\r\n                  body: editQuestion.body,\r\n                }}\r\n              >\r\n                <Form.Item\r\n                  name=\"title\"\r\n                  label=\"Title\"\r\n                  rules={[\r\n                    { required: true, message: \"Please enter the title\" },\r\n                  ]}\r\n                >\r\n                  <Input style={{ padding: \"18px 12px\" }} />\r\n                </Form.Item>\r\n                <Form.Item\r\n                  name=\"body\"\r\n                  label=\"Body\"\r\n                  rules={[{ required: true, message: \"Please enter the body\" }]}\r\n                >\r\n                  <Input.TextArea />\r\n                </Form.Item>\r\n                <Form.Item>\r\n                  <Button type=\"primary\" htmlType=\"submit\">\r\n                    Update Question\r\n                  </Button>\r\n                  <Button\r\n                    onClick={handleCancelUpdate}\r\n                    style={{ marginLeft: 10 }}\r\n                  >\r\n                    Cancel\r\n                  </Button>\r\n                </Form.Item>\r\n              </Form>\r\n              )}\r\n            {expandedReplies[question._id] && (\r\n              <div className=\"mt-4 sm:mt-6 space-y-3 sm:space-y-4 bg-gray-50 rounded-lg sm:rounded-xl p-3 sm:p-4\">\r\n                <h4 className=\"text-base sm:text-lg font-semibold text-gray-800 mb-3 sm:mb-4 flex items-center\">\r\n                  <i className=\"ri-chat-3-line mr-2 text-blue-600 text-sm sm:text-base\"></i>\r\n                  Replies ({question.replies.filter(reply => reply && reply.user).length})\r\n                </h4>\r\n                {question.replies.filter(reply => reply && reply.user).map((reply, index) => (\r\n                  <div\r\n                    key={reply._id}\r\n                    className={`bg-white rounded-lg p-3 sm:p-4 shadow-sm border-l-4 ${\r\n                      reply.user?.isAdmin\r\n                        ? \"border-purple-500 bg-purple-50\"\r\n                        : reply.isVerified\r\n                        ? \"border-green-500 bg-green-50\"\r\n                        : \"border-gray-300\"\r\n                    }`}\r\n                  >\r\n                    <div className=\"flex items-start space-x-2 sm:space-x-3\">\r\n                      {/* Avatar with Online Status */}\r\n                      <div className=\"flex-shrink-0 relative\">\r\n                        <ProfilePicture\r\n                          user={reply.user}\r\n                          size=\"xs\"\r\n                          showOnlineStatus={false}\r\n                          style={{\r\n                            width: '20px',\r\n                            height: '20px'\r\n                          }}\r\n                          className=\"sm:w-6 sm:h-6\"\r\n                        />\r\n                        {/* Only show online dot if user exists and is actually online */}\r\n                        {reply.user && reply.user.isOnline && (\r\n                          <div\r\n                            style={{\r\n                              position: 'absolute',\r\n                              bottom: '-1px',\r\n                              right: '-1px',\r\n                              width: '6px',\r\n                              height: '6px',\r\n                              backgroundColor: '#22c55e',\r\n                              borderRadius: '50%',\r\n                              border: '1px solid #ffffff',\r\n                              boxShadow: '0 1px 4px rgba(34, 197, 94, 0.6)',\r\n                              zIndex: 10\r\n                            }}\r\n                            className=\"sm:w-2 sm:h-2\"\r\n                            title=\"Online\"\r\n                          />\r\n                        )}\r\n                      </div>\r\n\r\n                      {/* Reply Content */}\r\n                      <div className=\"flex-1 min-w-0\">\r\n                        {/* Header */}\r\n                        <div className=\"flex items-start sm:items-center justify-between mb-2 gap-2\">\r\n                          <div className=\"flex items-center space-x-1 sm:space-x-2 min-w-0 flex-1\">\r\n                            <h5 className=\"font-semibold text-gray-900 text-sm sm:text-base truncate\">{reply.user?.name || 'Unknown User'}</h5>\r\n                            {reply.user?.isAdmin && (\r\n                              <span className=\"px-1.5 py-0.5 sm:px-2 sm:py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full whitespace-nowrap\">\r\n                                Admin\r\n                              </span>\r\n                            )}\r\n                            {reply.isVerified && !reply.user?.isAdmin && (\r\n                              <div className=\"flex items-center space-x-1\">\r\n                                <FaCheck className=\"w-3 h-3 sm:w-4 sm:h-4 text-green-600\" />\r\n                                <span className=\"px-1.5 py-0.5 sm:px-2 sm:py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full whitespace-nowrap\">\r\n                                  Verified\r\n                                </span>\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                          <span className=\"text-xs sm:text-sm text-gray-500 whitespace-nowrap\">\r\n                            {(() => {\r\n                              try {\r\n                                const date = new Date(reply.createdAt);\r\n                                if (isNaN(date.getTime())) {\r\n                                  return 'Invalid date';\r\n                                }\r\n                                return date.toLocaleDateString('en-US', {\r\n                                  month: \"short\",\r\n                                  day: \"numeric\",\r\n                                  hour: \"2-digit\",\r\n                                  minute: \"2-digit\"\r\n                                });\r\n                              } catch (error) {\r\n                                return 'Invalid date';\r\n                              }\r\n                            })()}\r\n                          </span>\r\n                        </div>\r\n\r\n                        {/* Reply Text */}\r\n                        <div className={`leading-relaxed mb-3 text-sm sm:text-base ${\r\n                          reply.isVerified && !reply.user?.isAdmin\r\n                            ? 'text-green-800 font-medium'\r\n                            : reply.user?.isAdmin\r\n                            ? 'text-purple-800 font-medium'\r\n                            : 'text-gray-700'\r\n                        }`}>\r\n                          {reply.text}\r\n                        </div>\r\n\r\n                        {/* Admin Actions */}\r\n                        {isAdmin && !reply.user?.isAdmin && (\r\n                          <div className=\"flex justify-end\">\r\n                            <button\r\n                              onClick={() =>\r\n                                handleUpdateStatus(\r\n                                  question._id,\r\n                                  reply._id,\r\n                                  !reply.isVerified\r\n                                )\r\n                              }\r\n                              className={`px-3 py-1.5 sm:px-4 sm:py-2 text-xs sm:text-sm font-medium rounded-lg transition-colors duration-200 ${\r\n                                reply.isVerified\r\n                                  ? \"bg-red-100 text-red-700 hover:bg-red-200\"\r\n                                  : \"bg-green-100 text-green-700 hover:bg-green-200\"\r\n                              }`}\r\n                            >\r\n                              {reply.isVerified ? \"Disapprove\" : \"Approve\"}\r\n                            </button>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n            <div ref={replyRefs[question._id]} className=\"mt-4 sm:mt-6\">\r\n              {replyQuestionId === question._id && (\r\n                <div className=\"bg-white rounded-lg sm:rounded-xl p-4 sm:p-6 border border-gray-200\">\r\n                  <Form\r\n                    form={form}\r\n                    onFinish={handleReplySubmit}\r\n                    layout=\"vertical\"\r\n                  >\r\n                    <Form.Item\r\n                      name=\"text\"\r\n                      label={<span className=\"text-sm sm:text-base font-medium\">Your Reply</span>}\r\n                      rules={[\r\n                        { required: true, message: \"Please enter your reply\" },\r\n                      ]}\r\n                    >\r\n                      <Input.TextArea\r\n                        rows={3}\r\n                        className=\"text-sm sm:text-base\"\r\n                        placeholder=\"Write your reply here...\"\r\n                      />\r\n                    </Form.Item>\r\n                    <Form.Item className=\"mb-0\">\r\n                      <div className=\"flex flex-col sm:flex-row gap-2 sm:gap-3\">\r\n                        <Button\r\n                          type=\"primary\"\r\n                          htmlType=\"submit\"\r\n                          className=\"w-full sm:w-auto\"\r\n                          size=\"large\"\r\n                        >\r\n                          Submit Reply\r\n                        </Button>\r\n                        <Button\r\n                          onClick={() => setReplyQuestionId(null)}\r\n                          className=\"w-full sm:w-auto\"\r\n                          size=\"large\"\r\n                        >\r\n                          Cancel\r\n                        </Button>\r\n                      </div>\r\n                    </Form.Item>\r\n                  </Form>\r\n                </div>\r\n              )}\r\n            </div>\r\n            </div>\r\n          ))}\r\n\r\n        </div>\r\n\r\n        <Pagination\r\n          current={currentPage}\r\n          total={totalQuestions}\r\n          pageSize={limit}\r\n          onChange={handlePageChange}\r\n          style={{ marginTop: \"20px\", textAlign: \"center\" }}\r\n          showSizeChanger={false}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Forum;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,aAAa;AACpB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,OAAO,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAEC,UAAU,QAAQ,MAAM;AACvE,OAAOC,SAAS,MAAM,+BAA+B;AACrD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAOC,cAAc,MAAM,2CAA2C;AACtE,OAAOC,qBAAqB,MAAM,kDAAkD;AACpF,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,SACEC,WAAW,EACXC,QAAQ,EACRC,eAAe,EACfC,cAAc,EACdC,cAAc,EACdC,iBAAiB,QACZ,yBAAyB;AAChC,OAAOC,KAAK,MAAM,4BAA4B;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,QAAQ,EAAEC,SAAS,QAAQ,gBAAgB;AACpD,SAASC,OAAO,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEzC,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM;IAAEC;EAAK,CAAC,GAAGvB,WAAW,CAAEwB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE,CAAC;IAAEC;EAAY,CAAC,GAAGrB,WAAW,CAAC,CAAC;EACxC,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyC,QAAQ,EAAEC,WAAW,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC2C,SAAS,EAAEC,YAAY,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC6C,eAAe,EAAEC,kBAAkB,CAAC,GAAG9C,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAAC+C,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACiD,eAAe,EAAEC,kBAAkB,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACmD,YAAY,EAAEC,eAAe,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACqD,IAAI,CAAC,GAAG9C,IAAI,CAAC+C,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAMyD,QAAQ,GAAG9C,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM+C,aAAa,GAAGA,CAAA,kBACpB5B,OAAA;IAAK6B,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC9B9B,OAAA;MAAK6B,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3B9B,OAAA;QAAK6B,SAAS,EAAC;MAAiD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvElC,OAAA;QAAK6B,SAAS,EAAC;MAA6C;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChE,CAAC,eACNlC,OAAA;MAAK6B,SAAS,EAAC,eAAe;MAAAC,QAAA,EAC3B,CAAC,GAAGK,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACtBtC,OAAA;QAAa6B,SAAS,EAAC,0CAA0C;QAAAC,QAAA,eAC/D9B,OAAA;UAAK6B,SAAS,EAAC,4BAA4B;UAAAC,QAAA,gBACzC9B,OAAA;YAAK6B,SAAS,EAAC;UAAkD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxElC,OAAA;YAAK6B,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACrB9B,OAAA;cAAK6B,SAAS,EAAC;YAAkD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxElC,OAAA;cAAK6B,SAAS,EAAC;YAAkD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxElC,OAAA;cAAK6B,SAAS,EAAC;YAAmD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzElC,OAAA;cAAK6B,SAAS,EAAC;YAA6C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GATEI,CAAC;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUN,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EACD,MAAM,CAACK,KAAK,CAAC,GAAG9D,IAAI,CAAC+C,OAAO,CAAC,CAAC;EAC9B,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGvE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAE9C,MAAM,CAACwE,WAAW,EAAEC,cAAc,CAAC,GAAGzE,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC0E,UAAU,EAAEC,aAAa,CAAC,GAAG3E,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC4E,KAAK,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EAC5B,MAAM,CAAC6E,cAAc,EAAEC,iBAAiB,CAAC,GAAG9E,QAAQ,CAAC,CAAC,CAAC;;EAEvD;EACA,MAAM+E,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,SAAS,GAAG,CAAC,SAAS,EAAE,WAAW,EAAE,SAAS,CAAC;IACrDA,SAAS,CAACC,OAAO,CAACC,KAAK,IAAI;MACzB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,EAAE,EAAEA,CAAC,EAAE,EAAE;QAAE;QAC9BC,YAAY,CAACC,UAAU,CAAE,mBAAkBH,KAAM,IAAGC,CAAE,IAAGP,KAAM,EAAC,CAAC;QACjEQ,YAAY,CAACC,UAAU,CAAE,mBAAkBH,KAAM,IAAGC,CAAE,IAAGP,KAAM,OAAM,CAAC;MACxE;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMU,cAAc,GAAG,MAAOC,IAAI,IAAK;IACrC,IAAI;MACF;MACA,MAAMC,SAAS,GAAG,CAAA/C,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEyC,KAAK,KAAI,SAAS;MAC9C,MAAMF,SAAS,GAAG,CAAC,SAAS,EAAE,WAAW,EAAE,SAAS,CAAC;MACrDA,SAAS,CAACC,OAAO,CAACC,KAAK,IAAI;QACzB,IAAIA,KAAK,KAAKM,SAAS,EAAE;UACvB;UACA,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,EAAE,EAAEA,CAAC,EAAE,EAAE;YAAE;YAC9BC,YAAY,CAACC,UAAU,CAAE,mBAAkBH,KAAM,IAAGC,CAAE,IAAGP,KAAM,EAAC,CAAC;YACjEQ,YAAY,CAACC,UAAU,CAAE,mBAAkBH,KAAM,IAAGC,CAAE,IAAGP,KAAM,OAAM,CAAC;UACxE;QACF;MACF,CAAC,CAAC;;MAEF;MACA,MAAMa,QAAQ,GAAI,mBAAkBD,SAAU,IAAGD,IAAK,IAAGX,KAAM,EAAC;MAChE,MAAMc,UAAU,GAAGN,YAAY,CAACO,OAAO,CAACF,QAAQ,CAAC;MACjD,MAAMG,SAAS,GAAGR,YAAY,CAACO,OAAO,CAAE,GAAEF,QAAS,OAAM,CAAC;MAC1D,MAAMI,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;;MAEtB;MACA,IAAIH,UAAU,IAAIE,SAAS,IAAKC,GAAG,GAAGE,QAAQ,CAACH,SAAS,CAAC,GAAI,MAAM,EAAE;QACnE,MAAMI,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACR,UAAU,CAAC;QACrC9C,YAAY,CAACoD,MAAM,CAACrD,SAAS,CAAC;QAC9BmC,iBAAiB,CAACkB,MAAM,CAACnB,cAAc,CAAC;QACxCF,aAAa,CAACqB,MAAM,CAACtB,UAAU,CAAC;QAChC;MACF;MAEAjB,QAAQ,CAAC3C,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMqF,QAAQ,GAAG,MAAM/E,eAAe,CAAC;QAAEmE,IAAI;QAAEX;MAAM,CAAC,CAAC,CAAC,CAAC;MACzD,IAAIuB,QAAQ,CAACC,OAAO,EAAE;QACpBC,OAAO,CAACC,GAAG,CAACH,QAAQ,CAACI,IAAI,CAAC;QAC1B3D,YAAY,CAACuD,QAAQ,CAACI,IAAI,CAAC,CAAC,CAAC;QAC7BzB,iBAAiB,CAACqB,QAAQ,CAACtB,cAAc,CAAC;QAC1CF,aAAa,CAACwB,QAAQ,CAACzB,UAAU,CAAC;;QAElC;QACAU,YAAY,CAACoB,OAAO,CAACf,QAAQ,EAAEQ,IAAI,CAACQ,SAAS,CAAC;UAC5C9D,SAAS,EAAEwD,QAAQ,CAACI,IAAI;UACxB1B,cAAc,EAAEsB,QAAQ,CAACtB,cAAc;UACvCH,UAAU,EAAEyB,QAAQ,CAACzB;QACvB,CAAC,CAAC,CAAC;QACHU,YAAY,CAACoB,OAAO,CAAE,GAAEf,QAAS,OAAM,EAAEI,GAAG,CAACa,QAAQ,CAAC,CAAC,CAAC;MAC1D,CAAC,MAAM;QACLtG,OAAO,CAACuG,KAAK,CAACR,QAAQ,CAAC/F,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOuG,KAAK,EAAE;MACdvG,OAAO,CAACuG,KAAK,CAACA,KAAK,CAACvG,OAAO,CAAC;IAC9B,CAAC,SAAS;MACRqD,QAAQ,CAAC5C,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAEDZ,SAAS,CAAC,MAAM;IACd;IACA8E,mBAAmB,CAAC,CAAC;IACrBO,cAAc,CAACd,WAAW,CAAC,CAACoC,OAAO,CAAC,MAAM;MACxCpD,gBAAgB,CAAC,KAAK,CAAC;IACzB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACgB,WAAW,EAAEI,KAAK,CAAC,CAAC;EAExB,MAAMiC,gBAAgB,GAAItB,IAAI,IAAK;IACjCd,cAAc,CAACc,IAAI,CAAC;EACtB,CAAC;EAED,MAAMuB,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9BrD,QAAQ,CAAC3C,WAAW,CAAC,CAAC,CAAC;IACvB,IAAI;MACF,MAAMqF,QAAQ,GAAG,MAAMhG,WAAW,CAAC,CAAC;MACpC,IAAIgG,QAAQ,CAACC,OAAO,EAAE;QACpB,IAAID,QAAQ,CAACI,IAAI,CAAChE,OAAO,EAAE;UACzBC,UAAU,CAAC,IAAI,CAAC;UAChBE,WAAW,CAACyD,QAAQ,CAACI,IAAI,CAAC;UAC1B,MAAMjB,cAAc,CAAC,CAAC;QACxB,CAAC,MAAM;UACL9C,UAAU,CAAC,KAAK,CAAC;UACjBE,WAAW,CAACyD,QAAQ,CAACI,IAAI,CAAC;UAC1B,MAAMjB,cAAc,CAAC,CAAC;QACxB;MACF,CAAC,MAAM;QACLlF,OAAO,CAACuG,KAAK,CAACR,QAAQ,CAAC/F,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOuG,KAAK,EAAE;MACdvG,OAAO,CAACuG,KAAK,CAACA,KAAK,CAACvG,OAAO,CAAC;IAC9B;EACF,CAAC;EAEDH,SAAS,CAAC,MAAM;IACd,IAAImF,YAAY,CAACO,OAAO,CAAC,OAAO,CAAC,EAAE;MACjCmB,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,aAAa,GAAIC,UAAU,IAAK;IACpClE,kBAAkB,CAAEmE,mBAAmB,KAAM;MAC3C,GAAGA,mBAAmB;MACtB,CAACD,UAAU,GAAG,CAACC,mBAAmB,CAACD,UAAU;IAC/C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,iBAAiB,GAAG,MAAOC,MAAM,IAAK;IAC1C,IAAI;MACF;MACA,MAAMC,kBAAkB,GAAG;QACzBC,GAAG,EAAG,QAAOvB,IAAI,CAACD,GAAG,CAAC,CAAE,EAAC;QACzByB,KAAK,EAAEH,MAAM,CAACG,KAAK;QACnBC,IAAI,EAAEJ,MAAM,CAACI,IAAI;QACjBpF,IAAI,EAAE;UACJkF,GAAG,EAAE5E,QAAQ,CAAC4E,GAAG;UACjBG,IAAI,EAAE/E,QAAQ,CAAC+E,IAAI;UACnBC,KAAK,EAAEhF,QAAQ,CAACgF;QAClB,CAAC;QACDC,OAAO,EAAE,EAAE;QACXC,SAAS,EAAE,IAAI7B,IAAI,CAAC,CAAC,CAAC8B,WAAW,CAAC,CAAC;QACnC1C,KAAK,EAAEzC,QAAQ,CAACyC,KAAK;QACrB2C,YAAY,EAAE,IAAI,CAAC;MACrB,CAAC;;MAED;MACAjF,YAAY,CAACkF,aAAa,IAAI,CAACV,kBAAkB,EAAE,GAAGU,aAAa,CAAC,CAAC;;MAErE;MACA9E,qBAAqB,CAAC,KAAK,CAAC;MAC5BK,IAAI,CAAC0E,WAAW,CAAC,CAAC;MAClB3H,OAAO,CAACgG,OAAO,CAAC,+BAA+B,CAAC;;MAEhD;MACA,MAAMD,QAAQ,GAAG,MAAMjF,WAAW,CAACiG,MAAM,CAAC;MAC1C,IAAIhB,QAAQ,CAACC,OAAO,EAAE;QACpB;QACArB,mBAAmB,CAAC,CAAC;QACrB,MAAMO,cAAc,CAAC,CAAC;MACxB,CAAC,MAAM;QACL;QACA1C,YAAY,CAACkF,aAAa,IACxBA,aAAa,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACZ,GAAG,KAAKD,kBAAkB,CAACC,GAAG,CAC5D,CAAC;QACDjH,OAAO,CAACuG,KAAK,CAACR,QAAQ,CAAC/F,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOuG,KAAK,EAAE;MACd;MACA/D,YAAY,CAACkF,aAAa,IACxBA,aAAa,CAACE,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACJ,YAAY,CAC3C,CAAC;MACDzH,OAAO,CAACuG,KAAK,CAACA,KAAK,CAACvG,OAAO,CAAC;IAC9B;EACF,CAAC;EAID,MAAM8H,WAAW,GAAIlB,UAAU,IAAK;IAClC9D,kBAAkB,CAAC8D,UAAU,CAAC;EAChC,CAAC;EAED,MAAMmB,iBAAiB,GAAG,MAAOhB,MAAM,IAAK;IAC1C,IAAI;MACF,MAAMiB,OAAO,GAAG;QACdpB,UAAU,EAAE/D,eAAe;QAC3BoF,IAAI,EAAElB,MAAM,CAACkB;MACf,CAAC;MACD,MAAMlC,QAAQ,GAAG,MAAMhF,QAAQ,CAACiH,OAAO,CAAC;MACxC,IAAIjC,QAAQ,CAACC,OAAO,EAAE;QACpBhG,OAAO,CAACgG,OAAO,CAACD,QAAQ,CAAC/F,OAAO,CAAC;QACjC8C,kBAAkB,CAAC,IAAI,CAAC;QACxBG,IAAI,CAAC0E,WAAW,CAAC,CAAC;QAClBhD,mBAAmB,CAAC,CAAC,CAAC,CAAC;QACvB,MAAMO,cAAc,CAAC,CAAC;MACxB,CAAC,MAAM;QACLlF,OAAO,CAACuG,KAAK,CAACR,QAAQ,CAAC/F,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOuG,KAAK,EAAE;MACdvG,OAAO,CAACuG,KAAK,CAACA,KAAK,CAACvG,OAAO,CAAC;IAC9B;EACF,CAAC;EAEDH,SAAS,CAAC,MAAM;IACd,IAAIgD,eAAe,IAAI,CAACqB,SAAS,CAACrB,eAAe,CAAC,EAAE;MAClDsB,YAAY,CAAE+D,QAAQ,KAAM;QAC1B,GAAGA,QAAQ;QACX,CAACrF,eAAe,gBAAGlD,KAAK,CAACwI,SAAS,CAAC;MACrC,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACtF,eAAe,EAAEqB,SAAS,CAAC,CAAC;EAEhCrE,SAAS,CAAC,MAAM;IACd,IAAIgD,eAAe,IAAIqB,SAAS,CAACrB,eAAe,CAAC,EAAE;MACjDqB,SAAS,CAACrB,eAAe,CAAC,CAACuF,OAAO,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAC3E;EACF,CAAC,EAAE,CAACzF,eAAe,EAAEqB,SAAS,CAAC,CAAC;EAEhC,MAAMqE,UAAU,GAAIC,QAAQ,IAAK;IAC/BxF,eAAe,CAACwF,QAAQ,CAAC;EAC3B,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOD,QAAQ,IAAK;IACvC,IAAI;MACF,MAAME,aAAa,GAAGC,MAAM,CAACC,OAAO,CAClC,gDACF,CAAC;MACD,IAAI,CAACF,aAAa,EAAE;QAClB;MACF;MACA,MAAM3C,QAAQ,GAAG,MAAM9E,cAAc,CAACuH,QAAQ,CAACvB,GAAG,CAAC;MACnD,IAAIlB,QAAQ,CAACC,OAAO,EAAE;QACpBhG,OAAO,CAACgG,OAAO,CAACD,QAAQ,CAAC/F,OAAO,CAAC;QACjC2E,mBAAmB,CAAC,CAAC,CAAC,CAAC;QACvB,MAAMO,cAAc,CAAC,CAAC;MACxB,CAAC,MAAM;QACLlF,OAAO,CAACuG,KAAK,CAACR,QAAQ,CAAC/F,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOuG,KAAK,EAAE;MACdvG,OAAO,CAACuG,KAAK,CAACA,KAAK,CAACvG,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAM6I,oBAAoB,GAAG,MAAO9B,MAAM,IAAK;IAC7C,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAM7E,cAAc,CAAC6F,MAAM,EAAEhE,YAAY,CAACkE,GAAG,CAAC;MAC/D,IAAIlB,QAAQ,CAACC,OAAO,EAAE;QACpBhG,OAAO,CAACgG,OAAO,CAACD,QAAQ,CAAC/F,OAAO,CAAC;QACjCgD,eAAe,CAAC,IAAI,CAAC;QACrB2B,mBAAmB,CAAC,CAAC,CAAC,CAAC;QACvB,MAAMO,cAAc,CAAC,CAAC;MACxB,CAAC,MAAM;QACLlF,OAAO,CAACuG,KAAK,CAACR,QAAQ,CAAC/F,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOuG,KAAK,EAAE;MACdvG,OAAO,CAACuG,KAAK,CAACA,KAAK,CAACvG,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAM8I,kBAAkB,GAAGA,CAAA,KAAM;IAC/B9F,eAAe,CAAC,EAAE,CAAC;EACrB,CAAC;EACD,MAAM+F,eAAe,GAAGA,CAAA,KAAM;IAC5BnG,qBAAqB,CAAC,KAAK,CAAC;IAC5BK,IAAI,CAAC0E,WAAW,CAAC,CAAC;EACpB,CAAC;EAED9H,SAAS,CAAC,MAAM;IACd,IAAIkD,YAAY,EAAE;MAChBkB,KAAK,CAAC+E,cAAc,CAAC;QACnB9B,KAAK,EAAEnE,YAAY,CAACmE,KAAK;QACzBC,IAAI,EAAEpE,YAAY,CAACoE;MACrB,CAAC,CAAC;IACJ,CAAC,MAAM;MACLlD,KAAK,CAAC0D,WAAW,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAAC5E,YAAY,CAAC,CAAC;EAElB,MAAMkG,kBAAkB,GAAG,MAAAA,CAAOrC,UAAU,EAAEsC,OAAO,EAAEC,MAAM,KAAK;IAChE,IAAI;MACF,MAAMpD,QAAQ,GAAG,MAAM5E,iBAAiB,CAAC;QAAE+H,OAAO;QAAEC;MAAO,CAAC,EAAEvC,UAAU,CAAC;MACzE,IAAIb,QAAQ,CAACC,OAAO,EAAE;QACpBhG,OAAO,CAACgG,OAAO,CAACD,QAAQ,CAAC/F,OAAO,CAAC;QACjC2E,mBAAmB,CAAC,CAAC,CAAC,CAAC;QACvB,MAAMO,cAAc,CAAC,CAAC;MACxB,CAAC,MAAM;QACLlF,OAAO,CAACuG,KAAK,CAACR,QAAQ,CAAC/F,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOuG,KAAK,EAAE;MACdvG,OAAO,CAACuG,KAAK,CAACA,KAAK,CAACvG,OAAO,CAAC;IAC9B;EACF,CAAC;;EAED;EACA,IAAImD,aAAa,IAAIZ,SAAS,CAAC6G,MAAM,KAAK,CAAC,EAAE;IAC3C,oBAAO1H,OAAA,CAAC4B,aAAa;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC1B;EAEA,oBACElC,OAAA;IAAK6B,SAAS,EAAC,oEAAoE;IAAAC,QAAA,eACjF9B,OAAA;MAAK6B,SAAS,EAAC,2EAA2E;MAAAC,QAAA,gBAExF9B,OAAA;QAAK6B,SAAS,EAAC,oCAAoC;QAAAC,QAAA,gBACjD9B,OAAA;UAAK6B,SAAS,EAAC,iLAAiL;UAAAC,QAAA,eAC9L9B,OAAA;YAAG6B,SAAS,EAAC;UAA2D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC,eACNlC,OAAA;UAAI6B,SAAS,EAAC,4EAA4E;UAAAC,QAAA,GAAC,YAC/E,eAAA9B,OAAA;YAAM6B,SAAS,EAAC,4EAA4E;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjH,CAAC,eACLlC,OAAA;UAAG6B,SAAS,EAAC,+FAA+F;UAAAC,QAAA,EAAC;QAE7G;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAGJlC,OAAA;UACE2H,OAAO,EAAEA,CAAA,KAAMzG,qBAAqB,CAAC,IAAI,CAAE;UAC3CW,SAAS,EAAC,wPAAwP;UAAAC,QAAA,gBAElQ9B,OAAA;YAAG6B,SAAS,EAAC;UAAqC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,kBAEzD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLjB,kBAAkB,iBACjBjB,OAAA;QAAK6B,SAAS,EAAC,oGAAoG;QAAAC,QAAA,gBACjH9B,OAAA;UAAK6B,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBAC7C9B,OAAA;YAAK6B,SAAS,EAAC,+HAA+H;YAAAC,QAAA,eAC5I9B,OAAA;cAAG6B,SAAS,EAAC;YAAgD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,eACNlC,OAAA;YAAI6B,SAAS,EAAC,6CAA6C;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CAAC,eAENlC,OAAA,CAACvB,IAAI;UAAC8C,IAAI,EAAEA,IAAK;UAACqG,QAAQ,EAAExC,iBAAkB;UAACyC,MAAM,EAAC,UAAU;UAAChG,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACtF9B,OAAA,CAACvB,IAAI,CAACqJ,IAAI;YACRpC,IAAI,EAAC,OAAO;YACZqC,KAAK,EAAC,gBAAgB;YACtBC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAE3J,OAAO,EAAE;YAAmC,CAAC,CAAE;YAAAwD,QAAA,eAEzE9B,OAAA,CAACxB,KAAK;cACJ0J,WAAW,EAAC,8BAA8B;cAC1CrG,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eACZlC,OAAA,CAACvB,IAAI,CAACqJ,IAAI;YACRpC,IAAI,EAAC,MAAM;YACXqC,KAAK,EAAC,kBAAkB;YACxBC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAE3J,OAAO,EAAE;YAAkD,CAAC,CAAE;YAAAwD,QAAA,eAExF9B,OAAA,CAACxB,KAAK,CAAC2J,QAAQ;cACbC,IAAI,EAAE,CAAE;cACRF,WAAW,EAAC,wGAAwG;cACpHrG,SAAS,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eACZlC,OAAA,CAACvB,IAAI,CAACqJ,IAAI;YAACjG,SAAS,EAAC,MAAM;YAAAC,QAAA,eACzB9B,OAAA;cAAK6B,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1C9B,OAAA,CAACzB,MAAM;gBACL8J,IAAI,EAAC,SAAS;gBACdC,QAAQ,EAAC,QAAQ;gBACjBzG,SAAS,EAAC,uGAAuG;gBAAAC,QAAA,gBAEjH9B,OAAA;kBAAG6B,SAAS,EAAC;gBAAyB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,iBAE7C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTlC,OAAA,CAACzB,MAAM;gBACLoJ,OAAO,EAAEN,eAAgB;gBACzBxF,SAAS,EAAC,+EAA+E;gBAAAC,QAAA,EAC1F;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN,EAGArB,SAAS,CAAC6G,MAAM,KAAK,CAAC,iBACrB1H,OAAA;QAAK6B,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC9B,OAAA;UAAK6B,SAAS,EAAC,iFAAiF;UAAAC,QAAA,eAC9F9B,OAAA;YAAG6B,SAAS,EAAC;UAAsD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC,eACNlC,OAAA;UAAG6B,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CACN,eAGDlC,OAAA;QAAK6B,SAAS,EAAC,wBAAwB;QAAAC,QAAA,EACpCjB,SAAS,CAACqF,MAAM,CAACY,QAAQ,IAAIA,QAAQ,IAAIA,QAAQ,CAACzG,IAAI,CAAC,CAAC+B,GAAG,CAAE0E,QAAQ;UAAA,IAAAyB,cAAA,EAAAC,eAAA;UAAA,oBACpExI,OAAA;YAAwB6B,SAAS,EAAC,iIAAiI;YAAAC,QAAA,gBAEjK9B,OAAA;cAAK6B,SAAS,EAAC,qCAAqC;cAAAC,QAAA,eAClD9B,OAAA;gBAAK6B,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/C9B,OAAA;kBAAK6B,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,gBACvD9B,OAAA;oBAAK6B,SAAS,EAAC,UAAU;oBAAAC,QAAA,gBACvB9B,OAAA,CAACf,cAAc;sBACboB,IAAI,EAAEyG,QAAQ,CAACzG,IAAK;sBACpBoI,IAAI,EAAC,IAAI;sBACTC,gBAAgB,EAAE,KAAM;sBACxBC,KAAK,EAAE;wBACLC,KAAK,EAAE,MAAM;wBACbC,MAAM,EAAE;sBACV;oBAAE;sBAAA9G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,EAED4E,QAAQ,CAACzG,IAAI,IAAIyG,QAAQ,CAACzG,IAAI,CAACyI,QAAQ,iBACtC9I,OAAA;sBACE2I,KAAK,EAAE;wBACLI,QAAQ,EAAE,UAAU;wBACpBC,MAAM,EAAE,MAAM;wBACdC,KAAK,EAAE,MAAM;wBACbL,KAAK,EAAE,MAAM;wBACbC,MAAM,EAAE,MAAM;wBACdK,eAAe,EAAE,SAAS;wBAC1BC,YAAY,EAAE,KAAK;wBACnBC,MAAM,EAAE,mBAAmB;wBAC3BC,SAAS,EAAE,kCAAkC;wBAC7CC,MAAM,EAAE;sBACV,CAAE;sBACF9D,KAAK,EAAC;oBAAQ;sBAAAzD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf,CACF;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACNlC,OAAA;oBAAK6B,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC7B9B,OAAA;sBAAI6B,SAAS,EAAC,2DAA2D;sBAAAC,QAAA,EAAE,EAAAyG,cAAA,GAAAzB,QAAQ,CAACzG,IAAI,cAAAkI,cAAA,uBAAbA,cAAA,CAAe7C,IAAI,KAAI;oBAAc;sBAAA3D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACtHlC,OAAA;sBAAG6B,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAC5C,IAAIkC,IAAI,CAAC8C,QAAQ,CAACjB,SAAS,CAAC,CAAC0D,kBAAkB,CAAC,OAAO,EAAE;wBACxDC,IAAI,EAAE,SAAS;wBACfC,KAAK,EAAE,OAAO;wBACdC,GAAG,EAAE,SAAS;wBACdC,IAAI,EAAE,SAAS;wBACfC,MAAM,EAAE;sBACV,CAAC;oBAAC;sBAAA7H,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNlC,OAAA;kBAAK6B,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,EAC3D,CAACnB,QAAQ,CAAC4E,GAAG,OAAAiD,eAAA,GAAK1B,QAAQ,CAACzG,IAAI,cAAAmI,eAAA,uBAAbA,eAAA,CAAejD,GAAG,KAAI5E,QAAQ,CAACF,OAAO,kBACvDT,OAAA,CAAAE,SAAA;oBAAA4B,QAAA,gBACE9B,OAAA;sBACE2H,OAAO,EAAEA,CAAA,KAAMd,UAAU,CAACC,QAAQ,CAAE;sBACpCjF,SAAS,EAAC,8GAA8G;sBAAAC,QAAA,eAExH9B,OAAA,CAACL,WAAW;wBAACkC,SAAS,EAAC;sBAAuB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C,CAAC,eACTlC,OAAA;sBACE2H,OAAO,EAAEA,CAAA,KAAMZ,YAAY,CAACD,QAAQ,CAAE;sBACtCjF,SAAS,EAAC,4GAA4G;sBAAAC,QAAA,eAEtH9B,OAAA,CAACJ,QAAQ;wBAACiC,SAAS,EAAC;sBAAuB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CAAC;kBAAA,eACT;gBACH;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlC,OAAA;cAAK6B,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB9B,OAAA;gBAAI6B,SAAS,EAAC,+DAA+D;gBAAAC,QAAA,EAAEgF,QAAQ,CAACtB;cAAK;gBAAAzD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnGlC,OAAA;gBAAG6B,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAEgF,QAAQ,CAACrB;cAAI;gBAAA1D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAGrElC,OAAA;gBAAK6B,SAAS,EAAC,mHAAmH;gBAAAC,QAAA,gBAChI9B,OAAA;kBAAK6B,SAAS,EAAC,0DAA0D;kBAAAC,QAAA,gBACvE9B,OAAA;oBACE2H,OAAO,EAAEA,CAAA,KAAM1C,aAAa,CAAC6B,QAAQ,CAACvB,GAAG,CAAE;oBAC3C1D,SAAS,EAAC,yKAAyK;oBAAAC,QAAA,gBAEnL9B,OAAA;sBAAG6B,SAAS,EAAC;oBAA+C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EAChEnB,eAAe,CAAC+F,QAAQ,CAACvB,GAAG,CAAC,GAAG,cAAc,GAAG,cAAc;kBAAA;oBAAAxD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D,CAAC,eACTlC,OAAA;oBACE2H,OAAO,EAAEA,CAAA,KAAMvB,WAAW,CAACU,QAAQ,CAACvB,GAAG,CAAE;oBACzC1D,SAAS,EAAC,2KAA2K;oBAAAC,QAAA,gBAErL9B,OAAA;sBAAG6B,SAAS,EAAC;oBAAiD;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,SAErE;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAENlC,OAAA;kBAAK6B,SAAS,EAAC,6FAA6F;kBAAAC,QAAA,gBAC1G9B,OAAA,CAACH,SAAS;oBAACgC,SAAS,EAAC;kBAAkD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC1ElC,OAAA;oBAAM6B,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAEgF,QAAQ,CAAClB,OAAO,CAAC8B;kBAAM;oBAAA3F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGLb,YAAY,IAAIA,YAAY,CAACkE,GAAG,KAAKuB,QAAQ,CAACvB,GAAG,iBAChDvF,OAAA,CAACvB,IAAI;cACL8C,IAAI,EAAEgB,KAAM;cACZqF,QAAQ,EAAET,oBAAqB;cAC/BU,MAAM,EAAC,UAAU;cACjBgC,aAAa,EAAE;gBACbrE,KAAK,EAAEnE,YAAY,CAACmE,KAAK;gBACzBC,IAAI,EAAEpE,YAAY,CAACoE;cACrB,CAAE;cAAA3D,QAAA,gBAEF9B,OAAA,CAACvB,IAAI,CAACqJ,IAAI;gBACRpC,IAAI,EAAC,OAAO;gBACZqC,KAAK,EAAC,OAAO;gBACbC,KAAK,EAAE,CACL;kBAAEC,QAAQ,EAAE,IAAI;kBAAE3J,OAAO,EAAE;gBAAyB,CAAC,CACrD;gBAAAwD,QAAA,eAEF9B,OAAA,CAACxB,KAAK;kBAACmK,KAAK,EAAE;oBAAEmB,OAAO,EAAE;kBAAY;gBAAE;kBAAA/H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACZlC,OAAA,CAACvB,IAAI,CAACqJ,IAAI;gBACRpC,IAAI,EAAC,MAAM;gBACXqC,KAAK,EAAC,MAAM;gBACZC,KAAK,EAAE,CAAC;kBAAEC,QAAQ,EAAE,IAAI;kBAAE3J,OAAO,EAAE;gBAAwB,CAAC,CAAE;gBAAAwD,QAAA,eAE9D9B,OAAA,CAACxB,KAAK,CAAC2J,QAAQ;kBAAApG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACZlC,OAAA,CAACvB,IAAI,CAACqJ,IAAI;gBAAAhG,QAAA,gBACR9B,OAAA,CAACzB,MAAM;kBAAC8J,IAAI,EAAC,SAAS;kBAACC,QAAQ,EAAC,QAAQ;kBAAAxG,QAAA,EAAC;gBAEzC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlC,OAAA,CAACzB,MAAM;kBACLoJ,OAAO,EAAEP,kBAAmB;kBAC5BuB,KAAK,EAAE;oBAAEoB,UAAU,EAAE;kBAAG,CAAE;kBAAAjI,QAAA,EAC3B;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CACL,EACFnB,eAAe,CAAC+F,QAAQ,CAACvB,GAAG,CAAC,iBAC5BvF,OAAA;cAAK6B,SAAS,EAAC,oFAAoF;cAAAC,QAAA,gBACjG9B,OAAA;gBAAI6B,SAAS,EAAC,iFAAiF;gBAAAC,QAAA,gBAC7F9B,OAAA;kBAAG6B,SAAS,EAAC;gBAAwD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,aACjE,EAAC4E,QAAQ,CAAClB,OAAO,CAACM,MAAM,CAAC8D,KAAK,IAAIA,KAAK,IAAIA,KAAK,CAAC3J,IAAI,CAAC,CAACqH,MAAM,EAAC,GACzE;cAAA;gBAAA3F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACJ4E,QAAQ,CAAClB,OAAO,CAACM,MAAM,CAAC8D,KAAK,IAAIA,KAAK,IAAIA,KAAK,CAAC3J,IAAI,CAAC,CAAC+B,GAAG,CAAC,CAAC4H,KAAK,EAAEC,KAAK;gBAAA,IAAAC,WAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA;gBAAA,oBACtExK,OAAA;kBAEE6B,SAAS,EAAG,uDACV,CAAAqI,WAAA,GAAAF,KAAK,CAAC3J,IAAI,cAAA6J,WAAA,eAAVA,WAAA,CAAYzJ,OAAO,GACf,gCAAgC,GAChCuJ,KAAK,CAACS,UAAU,GAChB,8BAA8B,GAC9B,iBACL,EAAE;kBAAA3I,QAAA,eAEH9B,OAAA;oBAAK6B,SAAS,EAAC,yCAAyC;oBAAAC,QAAA,gBAEtD9B,OAAA;sBAAK6B,SAAS,EAAC,wBAAwB;sBAAAC,QAAA,gBACrC9B,OAAA,CAACf,cAAc;wBACboB,IAAI,EAAE2J,KAAK,CAAC3J,IAAK;wBACjBoI,IAAI,EAAC,IAAI;wBACTC,gBAAgB,EAAE,KAAM;wBACxBC,KAAK,EAAE;0BACLC,KAAK,EAAE,MAAM;0BACbC,MAAM,EAAE;wBACV,CAAE;wBACFhH,SAAS,EAAC;sBAAe;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC,EAED8H,KAAK,CAAC3J,IAAI,IAAI2J,KAAK,CAAC3J,IAAI,CAACyI,QAAQ,iBAChC9I,OAAA;wBACE2I,KAAK,EAAE;0BACLI,QAAQ,EAAE,UAAU;0BACpBC,MAAM,EAAE,MAAM;0BACdC,KAAK,EAAE,MAAM;0BACbL,KAAK,EAAE,KAAK;0BACZC,MAAM,EAAE,KAAK;0BACbK,eAAe,EAAE,SAAS;0BAC1BC,YAAY,EAAE,KAAK;0BACnBC,MAAM,EAAE,mBAAmB;0BAC3BC,SAAS,EAAE,kCAAkC;0BAC7CC,MAAM,EAAE;wBACV,CAAE;wBACFzH,SAAS,EAAC,eAAe;wBACzB2D,KAAK,EAAC;sBAAQ;wBAAAzD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACf,CACF;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eAGNlC,OAAA;sBAAK6B,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,gBAE7B9B,OAAA;wBAAK6B,SAAS,EAAC,6DAA6D;wBAAAC,QAAA,gBAC1E9B,OAAA;0BAAK6B,SAAS,EAAC,yDAAyD;0BAAAC,QAAA,gBACtE9B,OAAA;4BAAI6B,SAAS,EAAC,2DAA2D;4BAAAC,QAAA,EAAE,EAAAqI,YAAA,GAAAH,KAAK,CAAC3J,IAAI,cAAA8J,YAAA,uBAAVA,YAAA,CAAYzE,IAAI,KAAI;0BAAc;4BAAA3D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,EAClH,EAAAkI,YAAA,GAAAJ,KAAK,CAAC3J,IAAI,cAAA+J,YAAA,uBAAVA,YAAA,CAAY3J,OAAO,kBAClBT,OAAA;4BAAM6B,SAAS,EAAC,gHAAgH;4BAAAC,QAAA,EAAC;0BAEjI;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CACP,EACA8H,KAAK,CAACS,UAAU,IAAI,GAAAJ,YAAA,GAACL,KAAK,CAAC3J,IAAI,cAAAgK,YAAA,eAAVA,YAAA,CAAY5J,OAAO,kBACvCT,OAAA;4BAAK6B,SAAS,EAAC,6BAA6B;4BAAAC,QAAA,gBAC1C9B,OAAA,CAACF,OAAO;8BAAC+B,SAAS,EAAC;4BAAsC;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,eAC5DlC,OAAA;8BAAM6B,SAAS,EAAC,8GAA8G;8BAAAC,QAAA,EAAC;4BAE/H;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ,CACN;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,eACNlC,OAAA;0BAAM6B,SAAS,EAAC,oDAAoD;0BAAAC,QAAA,EACjE,CAAC,MAAM;4BACN,IAAI;8BACF,MAAM4I,IAAI,GAAG,IAAI1G,IAAI,CAACgG,KAAK,CAACnE,SAAS,CAAC;8BACtC,IAAI8E,KAAK,CAACD,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE;gCACzB,OAAO,cAAc;8BACvB;8BACA,OAAOF,IAAI,CAACnB,kBAAkB,CAAC,OAAO,EAAE;gCACtCE,KAAK,EAAE,OAAO;gCACdC,GAAG,EAAE,SAAS;gCACdC,IAAI,EAAE,SAAS;gCACfC,MAAM,EAAE;8BACV,CAAC,CAAC;4BACJ,CAAC,CAAC,OAAO/E,KAAK,EAAE;8BACd,OAAO,cAAc;4BACvB;0BACF,CAAC,EAAE;wBAAC;0BAAA9C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACA,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eAGNlC,OAAA;wBAAK6B,SAAS,EAAG,6CACfmI,KAAK,CAACS,UAAU,IAAI,GAAAH,YAAA,GAACN,KAAK,CAAC3J,IAAI,cAAAiK,YAAA,eAAVA,YAAA,CAAY7J,OAAO,IACpC,4BAA4B,GAC5B,CAAA8J,YAAA,GAAAP,KAAK,CAAC3J,IAAI,cAAAkK,YAAA,eAAVA,YAAA,CAAY9J,OAAO,GACnB,6BAA6B,GAC7B,eACL,EAAE;wBAAAqB,QAAA,EACAkI,KAAK,CAACzD;sBAAI;wBAAAxE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACR,CAAC,EAGLzB,OAAO,IAAI,GAAA+J,YAAA,GAACR,KAAK,CAAC3J,IAAI,cAAAmK,YAAA,eAAVA,YAAA,CAAY/J,OAAO,kBAC9BT,OAAA;wBAAK6B,SAAS,EAAC,kBAAkB;wBAAAC,QAAA,eAC/B9B,OAAA;0BACE2H,OAAO,EAAEA,CAAA,KACPJ,kBAAkB,CAChBT,QAAQ,CAACvB,GAAG,EACZyE,KAAK,CAACzE,GAAG,EACT,CAACyE,KAAK,CAACS,UACT,CACD;0BACD5I,SAAS,EAAG,wGACVmI,KAAK,CAACS,UAAU,GACZ,0CAA0C,GAC1C,gDACL,EAAE;0BAAA3I,QAAA,EAEFkI,KAAK,CAACS,UAAU,GAAG,YAAY,GAAG;wBAAS;0BAAA1I,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC,GApHD8H,KAAK,CAACzE,GAAG;kBAAAxD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAqHX,CAAC;cAAA,CACP,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN,eACDlC,OAAA;cAAK6K,GAAG,EAAErI,SAAS,CAACsE,QAAQ,CAACvB,GAAG,CAAE;cAAC1D,SAAS,EAAC,cAAc;cAAAC,QAAA,EACxDX,eAAe,KAAK2F,QAAQ,CAACvB,GAAG,iBAC/BvF,OAAA;gBAAK6B,SAAS,EAAC,qEAAqE;gBAAAC,QAAA,eAClF9B,OAAA,CAACvB,IAAI;kBACH8C,IAAI,EAAEA,IAAK;kBACXqG,QAAQ,EAAEvB,iBAAkB;kBAC5BwB,MAAM,EAAC,UAAU;kBAAA/F,QAAA,gBAEjB9B,OAAA,CAACvB,IAAI,CAACqJ,IAAI;oBACRpC,IAAI,EAAC,MAAM;oBACXqC,KAAK,eAAE/H,OAAA;sBAAM6B,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAE;oBAC5E8F,KAAK,EAAE,CACL;sBAAEC,QAAQ,EAAE,IAAI;sBAAE3J,OAAO,EAAE;oBAA0B,CAAC,CACtD;oBAAAwD,QAAA,eAEF9B,OAAA,CAACxB,KAAK,CAAC2J,QAAQ;sBACbC,IAAI,EAAE,CAAE;sBACRvG,SAAS,EAAC,sBAAsB;sBAChCqG,WAAW,EAAC;oBAA0B;sBAAAnG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC,eACZlC,OAAA,CAACvB,IAAI,CAACqJ,IAAI;oBAACjG,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACzB9B,OAAA;sBAAK6B,SAAS,EAAC,0CAA0C;sBAAAC,QAAA,gBACvD9B,OAAA,CAACzB,MAAM;wBACL8J,IAAI,EAAC,SAAS;wBACdC,QAAQ,EAAC,QAAQ;wBACjBzG,SAAS,EAAC,kBAAkB;wBAC5B4G,IAAI,EAAC,OAAO;wBAAA3G,QAAA,EACb;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACTlC,OAAA,CAACzB,MAAM;wBACLoJ,OAAO,EAAEA,CAAA,KAAMvG,kBAAkB,CAAC,IAAI,CAAE;wBACxCS,SAAS,EAAC,kBAAkB;wBAC5B4G,IAAI,EAAC,OAAO;wBAAA3G,QAAA,EACb;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA,GAzTI4E,QAAQ,CAACvB,GAAG;YAAAxD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0TjB,CAAC;QAAA,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEC,CAAC,eAENlC,OAAA,CAACrB,UAAU;QACT+H,OAAO,EAAEhE,WAAY;QACrBoI,KAAK,EAAE/H,cAAe;QACtBgI,QAAQ,EAAEjI,KAAM;QAChBkI,QAAQ,EAAEjG,gBAAiB;QAC3B4D,KAAK,EAAE;UAAEsC,SAAS,EAAE,MAAM;UAAEC,SAAS,EAAE;QAAS,CAAE;QAClDC,eAAe,EAAE;MAAM;QAAApJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9B,EAAA,CA/uBID,KAAK;EAAA,QACQrB,WAAW,EACDK,WAAW,EAQvBV,IAAI,CAAC+C,OAAO,EAEV3C,WAAW,EA0BZJ,IAAI,CAAC+C,OAAO;AAAA;AAAA4J,EAAA,GAtCxBjL,KAAK;AAivBX,eAAeA,KAAK;AAAC,IAAAiL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}