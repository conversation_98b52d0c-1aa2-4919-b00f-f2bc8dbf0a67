{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"style\", \"onStartMove\", \"onOffsetChange\", \"values\", \"handleRender\", \"draggingIndex\"];\nimport * as React from 'react';\nimport Handle from './Handle';\nimport { getIndex } from '../util';\nvar Handles = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    style = props.style,\n    onStartMove = props.onStartMove,\n    onOffsetChange = props.onOffsetChange,\n    values = props.values,\n    handleRender = props.handleRender,\n    draggingIndex = props.draggingIndex,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var handlesRef = React.useRef({});\n  React.useImperativeHandle(ref, function () {\n    return {\n      focus: function focus(index) {\n        var _handlesRef$current$i;\n        (_handlesRef$current$i = handlesRef.current[index]) === null || _handlesRef$current$i === void 0 ? void 0 : _handlesRef$current$i.focus();\n      }\n    };\n  });\n  return /*#__PURE__*/React.createElement(React.Fragment, null, values.map(function (value, index) {\n    return /*#__PURE__*/React.createElement(Handle, _extends({\n      ref: function ref(node) {\n        if (!node) {\n          delete handlesRef.current[index];\n        } else {\n          handlesRef.current[index] = node;\n        }\n      },\n      dragging: draggingIndex === index,\n      prefixCls: prefixCls,\n      style: getIndex(style, index),\n      key: index,\n      value: value,\n      valueIndex: index,\n      onStartMove: onStartMove,\n      onOffsetChange: onOffsetChange,\n      render: handleRender\n    }, restProps));\n  }));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Handles.displayName = 'Handles';\n}\nexport default Handles;", "map": {"version": 3, "names": ["_extends", "_objectWithoutProperties", "_excluded", "React", "<PERSON><PERSON>", "getIndex", "<PERSON><PERSON>", "forwardRef", "props", "ref", "prefixCls", "style", "onStartMove", "onOffsetChange", "values", "handleRender", "draggingIndex", "restProps", "handlesRef", "useRef", "useImperativeHandle", "focus", "index", "_handlesRef$current$i", "current", "createElement", "Fragment", "map", "value", "node", "dragging", "key", "valueIndex", "render", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-slider/es/Handles/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"style\", \"onStartMove\", \"onOffsetChange\", \"values\", \"handleRender\", \"draggingIndex\"];\nimport * as React from 'react';\nimport Handle from './Handle';\nimport { getIndex } from '../util';\nvar Handles = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    style = props.style,\n    onStartMove = props.onStartMove,\n    onOffsetChange = props.onOffsetChange,\n    values = props.values,\n    handleRender = props.handleRender,\n    draggingIndex = props.draggingIndex,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var handlesRef = React.useRef({});\n  React.useImperativeHandle(ref, function () {\n    return {\n      focus: function focus(index) {\n        var _handlesRef$current$i;\n        (_handlesRef$current$i = handlesRef.current[index]) === null || _handlesRef$current$i === void 0 ? void 0 : _handlesRef$current$i.focus();\n      }\n    };\n  });\n  return /*#__PURE__*/React.createElement(React.Fragment, null, values.map(function (value, index) {\n    return /*#__PURE__*/React.createElement(Handle, _extends({\n      ref: function ref(node) {\n        if (!node) {\n          delete handlesRef.current[index];\n        } else {\n          handlesRef.current[index] = node;\n        }\n      },\n      dragging: draggingIndex === index,\n      prefixCls: prefixCls,\n      style: getIndex(style, index),\n      key: index,\n      value: value,\n      valueIndex: index,\n      onStartMove: onStartMove,\n      onOffsetChange: onOffsetChange,\n      render: handleRender\n    }, restProps));\n  }));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Handles.displayName = 'Handles';\n}\nexport default Handles;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,aAAa,EAAE,gBAAgB,EAAE,QAAQ,EAAE,cAAc,EAAE,eAAe,CAAC;AAClH,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,QAAQ,QAAQ,SAAS;AAClC,IAAIC,OAAO,GAAG,aAAaH,KAAK,CAACI,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAChE,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC7BC,KAAK,GAAGH,KAAK,CAACG,KAAK;IACnBC,WAAW,GAAGJ,KAAK,CAACI,WAAW;IAC/BC,cAAc,GAAGL,KAAK,CAACK,cAAc;IACrCC,MAAM,GAAGN,KAAK,CAACM,MAAM;IACrBC,YAAY,GAAGP,KAAK,CAACO,YAAY;IACjCC,aAAa,GAAGR,KAAK,CAACQ,aAAa;IACnCC,SAAS,GAAGhB,wBAAwB,CAACO,KAAK,EAAEN,SAAS,CAAC;EACxD,IAAIgB,UAAU,GAAGf,KAAK,CAACgB,MAAM,CAAC,CAAC,CAAC,CAAC;EACjChB,KAAK,CAACiB,mBAAmB,CAACX,GAAG,EAAE,YAAY;IACzC,OAAO;MACLY,KAAK,EAAE,SAASA,KAAKA,CAACC,KAAK,EAAE;QAC3B,IAAIC,qBAAqB;QACzB,CAACA,qBAAqB,GAAGL,UAAU,CAACM,OAAO,CAACF,KAAK,CAAC,MAAM,IAAI,IAAIC,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACF,KAAK,CAAC,CAAC;MAC3I;IACF,CAAC;EACH,CAAC,CAAC;EACF,OAAO,aAAalB,KAAK,CAACsB,aAAa,CAACtB,KAAK,CAACuB,QAAQ,EAAE,IAAI,EAAEZ,MAAM,CAACa,GAAG,CAAC,UAAUC,KAAK,EAAEN,KAAK,EAAE;IAC/F,OAAO,aAAanB,KAAK,CAACsB,aAAa,CAACrB,MAAM,EAAEJ,QAAQ,CAAC;MACvDS,GAAG,EAAE,SAASA,GAAGA,CAACoB,IAAI,EAAE;QACtB,IAAI,CAACA,IAAI,EAAE;UACT,OAAOX,UAAU,CAACM,OAAO,CAACF,KAAK,CAAC;QAClC,CAAC,MAAM;UACLJ,UAAU,CAACM,OAAO,CAACF,KAAK,CAAC,GAAGO,IAAI;QAClC;MACF,CAAC;MACDC,QAAQ,EAAEd,aAAa,KAAKM,KAAK;MACjCZ,SAAS,EAAEA,SAAS;MACpBC,KAAK,EAAEN,QAAQ,CAACM,KAAK,EAAEW,KAAK,CAAC;MAC7BS,GAAG,EAAET,KAAK;MACVM,KAAK,EAAEA,KAAK;MACZI,UAAU,EAAEV,KAAK;MACjBV,WAAW,EAAEA,WAAW;MACxBC,cAAc,EAAEA,cAAc;MAC9BoB,MAAM,EAAElB;IACV,CAAC,EAAEE,SAAS,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,IAAIiB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC9B,OAAO,CAAC+B,WAAW,GAAG,SAAS;AACjC;AACA,eAAe/B,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}