{"ast": null, "code": "import axiosInstance from \"./index\";\n\n// Auto AI Response for Forum Questions\nexport const getForumAIResponse = async questionData => {\n  try {\n    const response = await axiosInstance.post(\"/api/ai-response/forum-response\", questionData);\n    return response.data;\n  } catch (error) {\n    var _error$response;\n    return ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data) || {\n      success: false,\n      message: \"Error getting AI response\"\n    };\n  }\n};\n\n// Auto AI Response for Video Comments\nexport const getVideoCommentAIResponse = async commentData => {\n  try {\n    const response = await axiosInstance.post(\"/api/ai-response/video-comment-response\", commentData);\n    return response.data;\n  } catch (error) {\n    var _error$response2;\n    return ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data) || {\n      success: false,\n      message: \"Error getting AI response\"\n    };\n  }\n};\n\n// Past Paper Discussion with AI\nexport const getPastPaperAIResponse = async discussionData => {\n  try {\n    const response = await axiosInstance.post(\"/api/ai-response/past-paper-discussion\", discussionData);\n    return response.data;\n  } catch (error) {\n    var _error$response3;\n    return ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data) || {\n      success: false,\n      message: \"Error getting AI response\"\n    };\n  }\n};\n\n// Get AI Conversation History\nexport const getAIConversationHistory = async (contextType, contextId) => {\n  try {\n    const response = await axiosInstance.get(`/api/ai-response/conversation/${contextType}/${contextId}`);\n    return response.data;\n  } catch (error) {\n    var _error$response4;\n    return ((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : _error$response4.data) || {\n      success: false,\n      message: \"Error getting conversation history\"\n    };\n  }\n};\n\n// Check AI Service Health\nexport const checkAIServiceHealth = async () => {\n  try {\n    const response = await axiosInstance.get(\"/api/ai-response/health\");\n    return response.data;\n  } catch (error) {\n    var _error$response5;\n    return ((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : _error$response5.data) || {\n      success: false,\n      message: \"AI service unavailable\"\n    };\n  }\n};", "map": {"version": 3, "names": ["axiosInstance", "getForumAIResponse", "questionData", "response", "post", "data", "error", "_error$response", "success", "message", "getVideoCommentAIResponse", "commentData", "_error$response2", "getPastPaperAIResponse", "discussionData", "_error$response3", "getAIConversationHistory", "contextType", "contextId", "get", "_error$response4", "checkAIServiceHealth", "_error$response5"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/apicalls/aiResponse.js"], "sourcesContent": ["import axiosInstance from \"./index\";\n\n// Auto AI Response for Forum Questions\nexport const getForumAIResponse = async (questionData) => {\n  try {\n    const response = await axiosInstance.post(\"/api/ai-response/forum-response\", questionData);\n    return response.data;\n  } catch (error) {\n    return error.response?.data || { success: false, message: \"<PERSON>rror getting AI response\" };\n  }\n};\n\n// Auto AI Response for Video Comments\nexport const getVideoCommentAIResponse = async (commentData) => {\n  try {\n    const response = await axiosInstance.post(\"/api/ai-response/video-comment-response\", commentData);\n    return response.data;\n  } catch (error) {\n    return error.response?.data || { success: false, message: \"<PERSON>rror getting AI response\" };\n  }\n};\n\n// Past Paper Discussion with AI\nexport const getPastPaperAIResponse = async (discussionData) => {\n  try {\n    const response = await axiosInstance.post(\"/api/ai-response/past-paper-discussion\", discussionData);\n    return response.data;\n  } catch (error) {\n    return error.response?.data || { success: false, message: \"<PERSON>rro<PERSON> getting AI response\" };\n  }\n};\n\n// Get AI Conversation History\nexport const getAIConversationHistory = async (contextType, contextId) => {\n  try {\n    const response = await axiosInstance.get(`/api/ai-response/conversation/${contextType}/${contextId}`);\n    return response.data;\n  } catch (error) {\n    return error.response?.data || { success: false, message: \"Error getting conversation history\" };\n  }\n};\n\n// Check AI Service Health\nexport const checkAIServiceHealth = async () => {\n  try {\n    const response = await axiosInstance.get(\"/api/ai-response/health\");\n    return response.data;\n  } catch (error) {\n    return error.response?.data || { success: false, message: \"AI service unavailable\" };\n  }\n};\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,SAAS;;AAEnC;AACA,OAAO,MAAMC,kBAAkB,GAAG,MAAOC,YAAY,IAAK;EACxD,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMH,aAAa,CAACI,IAAI,CAAC,iCAAiC,EAAEF,YAAY,CAAC;IAC1F,OAAOC,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA,IAAAC,eAAA;IACd,OAAO,EAAAA,eAAA,GAAAD,KAAK,CAACH,QAAQ,cAAAI,eAAA,uBAAdA,eAAA,CAAgBF,IAAI,KAAI;MAAEG,OAAO,EAAE,KAAK;MAAEC,OAAO,EAAE;IAA4B,CAAC;EACzF;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,yBAAyB,GAAG,MAAOC,WAAW,IAAK;EAC9D,IAAI;IACF,MAAMR,QAAQ,GAAG,MAAMH,aAAa,CAACI,IAAI,CAAC,yCAAyC,EAAEO,WAAW,CAAC;IACjG,OAAOR,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA,IAAAM,gBAAA;IACd,OAAO,EAAAA,gBAAA,GAAAN,KAAK,CAACH,QAAQ,cAAAS,gBAAA,uBAAdA,gBAAA,CAAgBP,IAAI,KAAI;MAAEG,OAAO,EAAE,KAAK;MAAEC,OAAO,EAAE;IAA4B,CAAC;EACzF;AACF,CAAC;;AAED;AACA,OAAO,MAAMI,sBAAsB,GAAG,MAAOC,cAAc,IAAK;EAC9D,IAAI;IACF,MAAMX,QAAQ,GAAG,MAAMH,aAAa,CAACI,IAAI,CAAC,wCAAwC,EAAEU,cAAc,CAAC;IACnG,OAAOX,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA,IAAAS,gBAAA;IACd,OAAO,EAAAA,gBAAA,GAAAT,KAAK,CAACH,QAAQ,cAAAY,gBAAA,uBAAdA,gBAAA,CAAgBV,IAAI,KAAI;MAAEG,OAAO,EAAE,KAAK;MAAEC,OAAO,EAAE;IAA4B,CAAC;EACzF;AACF,CAAC;;AAED;AACA,OAAO,MAAMO,wBAAwB,GAAG,MAAAA,CAAOC,WAAW,EAAEC,SAAS,KAAK;EACxE,IAAI;IACF,MAAMf,QAAQ,GAAG,MAAMH,aAAa,CAACmB,GAAG,CAAE,iCAAgCF,WAAY,IAAGC,SAAU,EAAC,CAAC;IACrG,OAAOf,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA,IAAAc,gBAAA;IACd,OAAO,EAAAA,gBAAA,GAAAd,KAAK,CAACH,QAAQ,cAAAiB,gBAAA,uBAAdA,gBAAA,CAAgBf,IAAI,KAAI;MAAEG,OAAO,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAqC,CAAC;EAClG;AACF,CAAC;;AAED;AACA,OAAO,MAAMY,oBAAoB,GAAG,MAAAA,CAAA,KAAY;EAC9C,IAAI;IACF,MAAMlB,QAAQ,GAAG,MAAMH,aAAa,CAACmB,GAAG,CAAC,yBAAyB,CAAC;IACnE,OAAOhB,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA,IAAAgB,gBAAA;IACd,OAAO,EAAAA,gBAAA,GAAAhB,KAAK,CAACH,QAAQ,cAAAmB,gBAAA,uBAAdA,gBAAA,CAAgBjB,IAAI,KAAI;MAAEG,OAAO,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAyB,CAAC;EACtF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}