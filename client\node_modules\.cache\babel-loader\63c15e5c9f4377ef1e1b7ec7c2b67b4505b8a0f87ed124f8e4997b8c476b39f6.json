{"ast": null, "code": "import toArray from \"rc-util/es/Children/toArray\";\nimport useIsomorphicLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport * as React from 'react';\nfunction cuttable(node) {\n  const type = typeof node;\n  return type === 'string' || type === 'number';\n}\nfunction getNodesLen(nodeList) {\n  let totalLen = 0;\n  nodeList.forEach(node => {\n    if (cuttable(node)) {\n      totalLen += String(node).length;\n    } else {\n      totalLen += 1;\n    }\n  });\n  return totalLen;\n}\nfunction sliceNodes(nodeList, len) {\n  let currLen = 0;\n  const currentNodeList = [];\n  for (let i = 0; i < nodeList.length; i += 1) {\n    // Match to return\n    if (currLen === len) {\n      return currentNodeList;\n    }\n    const node = nodeList[i];\n    const canCut = cuttable(node);\n    const nodeLen = canCut ? String(node).length : 1;\n    const nextLen = currLen + nodeLen;\n    // Exceed but current not which means we need cut this\n    // This will not happen on validate ReactElement\n    if (nextLen > len) {\n      const restLen = len - currLen;\n      currentNodeList.push(String(node).slice(0, restLen));\n      return currentNodeList;\n    }\n    currentNodeList.push(node);\n    currLen = nextLen;\n  }\n  return nodeList;\n}\nconst NONE = 0;\nconst PREPARE = 1;\nconst WALKING = 2;\nconst DONE_WITH_ELLIPSIS = 3;\nconst DONE_WITHOUT_ELLIPSIS = 4;\nconst Ellipsis = _ref => {\n  let {\n    enabledMeasure,\n    children,\n    text,\n    width,\n    fontSize,\n    rows,\n    onEllipsis\n  } = _ref;\n  const [[startLen, midLen, endLen], setCutLength] = React.useState([0, 0, 0]);\n  const [walkingState, setWalkingState] = React.useState(NONE);\n  const [singleRowHeight, setSingleRowHeight] = React.useState(0);\n  const singleRowRef = React.useRef(null);\n  const midRowRef = React.useRef(null);\n  const nodeList = React.useMemo(() => toArray(text), [text]);\n  const totalLen = React.useMemo(() => getNodesLen(nodeList), [nodeList]);\n  const mergedChildren = React.useMemo(() => {\n    if (!enabledMeasure || walkingState !== DONE_WITH_ELLIPSIS) {\n      return children(nodeList, false);\n    }\n    return children(sliceNodes(nodeList, midLen), midLen < totalLen);\n  }, [enabledMeasure, walkingState, children, nodeList, midLen, totalLen]);\n  // ======================== Walk ========================\n  useIsomorphicLayoutEffect(() => {\n    if (enabledMeasure && width && fontSize && totalLen) {\n      setWalkingState(PREPARE);\n      setCutLength([0, Math.ceil(totalLen / 2), totalLen]);\n    }\n  }, [enabledMeasure, width, fontSize, text, totalLen, rows]);\n  useIsomorphicLayoutEffect(() => {\n    var _a;\n    if (walkingState === PREPARE) {\n      setSingleRowHeight(((_a = singleRowRef.current) === null || _a === void 0 ? void 0 : _a.offsetHeight) || 0);\n    }\n  }, [walkingState]);\n  useIsomorphicLayoutEffect(() => {\n    var _a, _b;\n    if (singleRowHeight) {\n      if (walkingState === PREPARE) {\n        // Ignore if position is enough\n        const midHeight = ((_a = midRowRef.current) === null || _a === void 0 ? void 0 : _a.offsetHeight) || 0;\n        const maxHeight = rows * singleRowHeight;\n        if (midHeight <= maxHeight) {\n          setWalkingState(DONE_WITHOUT_ELLIPSIS);\n          onEllipsis(false);\n        } else {\n          setWalkingState(WALKING);\n        }\n      } else if (walkingState === WALKING) {\n        if (startLen !== endLen) {\n          const midHeight = ((_b = midRowRef.current) === null || _b === void 0 ? void 0 : _b.offsetHeight) || 0;\n          const maxHeight = rows * singleRowHeight;\n          let nextStartLen = startLen;\n          let nextEndLen = endLen;\n          // We reach the last round\n          if (startLen === endLen - 1) {\n            nextEndLen = startLen;\n          } else if (midHeight <= maxHeight) {\n            nextStartLen = midLen;\n          } else {\n            nextEndLen = midLen;\n          }\n          const nextMidLen = Math.ceil((nextStartLen + nextEndLen) / 2);\n          setCutLength([nextStartLen, nextMidLen, nextEndLen]);\n        } else {\n          setWalkingState(DONE_WITH_ELLIPSIS);\n          onEllipsis(true);\n        }\n      }\n    }\n  }, [walkingState, startLen, endLen, rows, singleRowHeight]);\n  // ======================= Render =======================\n  const measureStyle = {\n    width,\n    whiteSpace: 'normal',\n    margin: 0,\n    padding: 0\n  };\n  const renderMeasure = (content, ref, style) => /*#__PURE__*/React.createElement(\"span\", {\n    \"aria-hidden\": true,\n    ref: ref,\n    style: Object.assign({\n      position: 'fixed',\n      display: 'block',\n      left: 0,\n      top: 0,\n      zIndex: -9999,\n      visibility: 'hidden',\n      pointerEvents: 'none',\n      fontSize: Math.floor(fontSize / 2) * 2\n    }, style)\n  }, content);\n  const renderMeasureSlice = (len, ref) => {\n    const sliceNodeList = sliceNodes(nodeList, len);\n    return renderMeasure(children(sliceNodeList, true), ref, measureStyle);\n  };\n  return /*#__PURE__*/React.createElement(React.Fragment, null, mergedChildren, enabledMeasure && walkingState !== DONE_WITH_ELLIPSIS && walkingState !== DONE_WITHOUT_ELLIPSIS && /*#__PURE__*/React.createElement(React.Fragment, null, renderMeasure('lg', singleRowRef, {\n    wordBreak: 'keep-all',\n    whiteSpace: 'nowrap'\n  }), walkingState === PREPARE ? renderMeasure(children(nodeList, false), midRowRef, measureStyle) : renderMeasureSlice(midLen, midRowRef)));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Ellipsis.displayName = 'Ellipsis';\n}\nexport default Ellipsis;", "map": {"version": 3, "names": ["toArray", "useIsomorphicLayoutEffect", "React", "cuttable", "node", "type", "getNodesLen", "nodeList", "totalLen", "for<PERSON>ach", "String", "length", "sliceNodes", "len", "currLen", "currentNodeList", "i", "canCut", "nodeLen", "nextLen", "restLen", "push", "slice", "NONE", "PREPARE", "WALKING", "DONE_WITH_ELLIPSIS", "DONE_WITHOUT_ELLIPSIS", "El<PERSON><PERSON>", "_ref", "enabledMeasure", "children", "text", "width", "fontSize", "rows", "onEllipsis", "startLen", "midLen", "endLen", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "useState", "walkingState", "setWalkingState", "singleRowHeight", "setSingleRowHeight", "singleRowRef", "useRef", "midRowRef", "useMemo", "mergedChildren", "Math", "ceil", "_a", "current", "offsetHeight", "_b", "midHeight", "maxHeight", "nextStartLen", "nextEndLen", "nextMidLen", "measureStyle", "whiteSpace", "margin", "padding", "renderMeasure", "content", "ref", "style", "createElement", "Object", "assign", "position", "display", "left", "top", "zIndex", "visibility", "pointerEvents", "floor", "renderMeasureSlice", "sliceNodeList", "Fragment", "wordBreak", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/typography/Base/Ellipsis.js"], "sourcesContent": ["import toArray from \"rc-util/es/Children/toArray\";\nimport useIsomorphicLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport * as React from 'react';\nfunction cuttable(node) {\n  const type = typeof node;\n  return type === 'string' || type === 'number';\n}\nfunction getNodesLen(nodeList) {\n  let totalLen = 0;\n  nodeList.forEach(node => {\n    if (cuttable(node)) {\n      totalLen += String(node).length;\n    } else {\n      totalLen += 1;\n    }\n  });\n  return totalLen;\n}\nfunction sliceNodes(nodeList, len) {\n  let currLen = 0;\n  const currentNodeList = [];\n  for (let i = 0; i < nodeList.length; i += 1) {\n    // Match to return\n    if (currLen === len) {\n      return currentNodeList;\n    }\n    const node = nodeList[i];\n    const canCut = cuttable(node);\n    const nodeLen = canCut ? String(node).length : 1;\n    const nextLen = currLen + nodeLen;\n    // Exceed but current not which means we need cut this\n    // This will not happen on validate ReactElement\n    if (nextLen > len) {\n      const restLen = len - currLen;\n      currentNodeList.push(String(node).slice(0, restLen));\n      return currentNodeList;\n    }\n    currentNodeList.push(node);\n    currLen = nextLen;\n  }\n  return nodeList;\n}\nconst NONE = 0;\nconst PREPARE = 1;\nconst WALKING = 2;\nconst DONE_WITH_ELLIPSIS = 3;\nconst DONE_WITHOUT_ELLIPSIS = 4;\nconst Ellipsis = _ref => {\n  let {\n    enabledMeasure,\n    children,\n    text,\n    width,\n    fontSize,\n    rows,\n    onEllipsis\n  } = _ref;\n  const [[startLen, midLen, endLen], setCutLength] = React.useState([0, 0, 0]);\n  const [walkingState, setWalkingState] = React.useState(NONE);\n  const [singleRowHeight, setSingleRowHeight] = React.useState(0);\n  const singleRowRef = React.useRef(null);\n  const midRowRef = React.useRef(null);\n  const nodeList = React.useMemo(() => toArray(text), [text]);\n  const totalLen = React.useMemo(() => getNodesLen(nodeList), [nodeList]);\n  const mergedChildren = React.useMemo(() => {\n    if (!enabledMeasure || walkingState !== DONE_WITH_ELLIPSIS) {\n      return children(nodeList, false);\n    }\n    return children(sliceNodes(nodeList, midLen), midLen < totalLen);\n  }, [enabledMeasure, walkingState, children, nodeList, midLen, totalLen]);\n  // ======================== Walk ========================\n  useIsomorphicLayoutEffect(() => {\n    if (enabledMeasure && width && fontSize && totalLen) {\n      setWalkingState(PREPARE);\n      setCutLength([0, Math.ceil(totalLen / 2), totalLen]);\n    }\n  }, [enabledMeasure, width, fontSize, text, totalLen, rows]);\n  useIsomorphicLayoutEffect(() => {\n    var _a;\n    if (walkingState === PREPARE) {\n      setSingleRowHeight(((_a = singleRowRef.current) === null || _a === void 0 ? void 0 : _a.offsetHeight) || 0);\n    }\n  }, [walkingState]);\n  useIsomorphicLayoutEffect(() => {\n    var _a, _b;\n    if (singleRowHeight) {\n      if (walkingState === PREPARE) {\n        // Ignore if position is enough\n        const midHeight = ((_a = midRowRef.current) === null || _a === void 0 ? void 0 : _a.offsetHeight) || 0;\n        const maxHeight = rows * singleRowHeight;\n        if (midHeight <= maxHeight) {\n          setWalkingState(DONE_WITHOUT_ELLIPSIS);\n          onEllipsis(false);\n        } else {\n          setWalkingState(WALKING);\n        }\n      } else if (walkingState === WALKING) {\n        if (startLen !== endLen) {\n          const midHeight = ((_b = midRowRef.current) === null || _b === void 0 ? void 0 : _b.offsetHeight) || 0;\n          const maxHeight = rows * singleRowHeight;\n          let nextStartLen = startLen;\n          let nextEndLen = endLen;\n          // We reach the last round\n          if (startLen === endLen - 1) {\n            nextEndLen = startLen;\n          } else if (midHeight <= maxHeight) {\n            nextStartLen = midLen;\n          } else {\n            nextEndLen = midLen;\n          }\n          const nextMidLen = Math.ceil((nextStartLen + nextEndLen) / 2);\n          setCutLength([nextStartLen, nextMidLen, nextEndLen]);\n        } else {\n          setWalkingState(DONE_WITH_ELLIPSIS);\n          onEllipsis(true);\n        }\n      }\n    }\n  }, [walkingState, startLen, endLen, rows, singleRowHeight]);\n  // ======================= Render =======================\n  const measureStyle = {\n    width,\n    whiteSpace: 'normal',\n    margin: 0,\n    padding: 0\n  };\n  const renderMeasure = (content, ref, style) => /*#__PURE__*/React.createElement(\"span\", {\n    \"aria-hidden\": true,\n    ref: ref,\n    style: Object.assign({\n      position: 'fixed',\n      display: 'block',\n      left: 0,\n      top: 0,\n      zIndex: -9999,\n      visibility: 'hidden',\n      pointerEvents: 'none',\n      fontSize: Math.floor(fontSize / 2) * 2\n    }, style)\n  }, content);\n  const renderMeasureSlice = (len, ref) => {\n    const sliceNodeList = sliceNodes(nodeList, len);\n    return renderMeasure(children(sliceNodeList, true), ref, measureStyle);\n  };\n  return /*#__PURE__*/React.createElement(React.Fragment, null, mergedChildren, enabledMeasure && walkingState !== DONE_WITH_ELLIPSIS && walkingState !== DONE_WITHOUT_ELLIPSIS && /*#__PURE__*/React.createElement(React.Fragment, null, renderMeasure('lg', singleRowRef, {\n    wordBreak: 'keep-all',\n    whiteSpace: 'nowrap'\n  }), walkingState === PREPARE ? renderMeasure(children(nodeList, false), midRowRef, measureStyle) : renderMeasureSlice(midLen, midRowRef)));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Ellipsis.displayName = 'Ellipsis';\n}\nexport default Ellipsis;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,6BAA6B;AACjD,OAAOC,yBAAyB,MAAM,kCAAkC;AACxE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQA,CAACC,IAAI,EAAE;EACtB,MAAMC,IAAI,GAAG,OAAOD,IAAI;EACxB,OAAOC,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,QAAQ;AAC/C;AACA,SAASC,WAAWA,CAACC,QAAQ,EAAE;EAC7B,IAAIC,QAAQ,GAAG,CAAC;EAChBD,QAAQ,CAACE,OAAO,CAACL,IAAI,IAAI;IACvB,IAAID,QAAQ,CAACC,IAAI,CAAC,EAAE;MAClBI,QAAQ,IAAIE,MAAM,CAACN,IAAI,CAAC,CAACO,MAAM;IACjC,CAAC,MAAM;MACLH,QAAQ,IAAI,CAAC;IACf;EACF,CAAC,CAAC;EACF,OAAOA,QAAQ;AACjB;AACA,SAASI,UAAUA,CAACL,QAAQ,EAAEM,GAAG,EAAE;EACjC,IAAIC,OAAO,GAAG,CAAC;EACf,MAAMC,eAAe,GAAG,EAAE;EAC1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,QAAQ,CAACI,MAAM,EAAEK,CAAC,IAAI,CAAC,EAAE;IAC3C;IACA,IAAIF,OAAO,KAAKD,GAAG,EAAE;MACnB,OAAOE,eAAe;IACxB;IACA,MAAMX,IAAI,GAAGG,QAAQ,CAACS,CAAC,CAAC;IACxB,MAAMC,MAAM,GAAGd,QAAQ,CAACC,IAAI,CAAC;IAC7B,MAAMc,OAAO,GAAGD,MAAM,GAAGP,MAAM,CAACN,IAAI,CAAC,CAACO,MAAM,GAAG,CAAC;IAChD,MAAMQ,OAAO,GAAGL,OAAO,GAAGI,OAAO;IACjC;IACA;IACA,IAAIC,OAAO,GAAGN,GAAG,EAAE;MACjB,MAAMO,OAAO,GAAGP,GAAG,GAAGC,OAAO;MAC7BC,eAAe,CAACM,IAAI,CAACX,MAAM,CAACN,IAAI,CAAC,CAACkB,KAAK,CAAC,CAAC,EAAEF,OAAO,CAAC,CAAC;MACpD,OAAOL,eAAe;IACxB;IACAA,eAAe,CAACM,IAAI,CAACjB,IAAI,CAAC;IAC1BU,OAAO,GAAGK,OAAO;EACnB;EACA,OAAOZ,QAAQ;AACjB;AACA,MAAMgB,IAAI,GAAG,CAAC;AACd,MAAMC,OAAO,GAAG,CAAC;AACjB,MAAMC,OAAO,GAAG,CAAC;AACjB,MAAMC,kBAAkB,GAAG,CAAC;AAC5B,MAAMC,qBAAqB,GAAG,CAAC;AAC/B,MAAMC,QAAQ,GAAGC,IAAI,IAAI;EACvB,IAAI;IACFC,cAAc;IACdC,QAAQ;IACRC,IAAI;IACJC,KAAK;IACLC,QAAQ;IACRC,IAAI;IACJC;EACF,CAAC,GAAGP,IAAI;EACR,MAAM,CAAC,CAACQ,QAAQ,EAAEC,MAAM,EAAEC,MAAM,CAAC,EAAEC,YAAY,CAAC,GAAGtC,KAAK,CAACuC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EAC5E,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,KAAK,CAACuC,QAAQ,CAAClB,IAAI,CAAC;EAC5D,MAAM,CAACqB,eAAe,EAAEC,kBAAkB,CAAC,GAAG3C,KAAK,CAACuC,QAAQ,CAAC,CAAC,CAAC;EAC/D,MAAMK,YAAY,GAAG5C,KAAK,CAAC6C,MAAM,CAAC,IAAI,CAAC;EACvC,MAAMC,SAAS,GAAG9C,KAAK,CAAC6C,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMxC,QAAQ,GAAGL,KAAK,CAAC+C,OAAO,CAAC,MAAMjD,OAAO,CAACgC,IAAI,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;EAC3D,MAAMxB,QAAQ,GAAGN,KAAK,CAAC+C,OAAO,CAAC,MAAM3C,WAAW,CAACC,QAAQ,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EACvE,MAAM2C,cAAc,GAAGhD,KAAK,CAAC+C,OAAO,CAAC,MAAM;IACzC,IAAI,CAACnB,cAAc,IAAIY,YAAY,KAAKhB,kBAAkB,EAAE;MAC1D,OAAOK,QAAQ,CAACxB,QAAQ,EAAE,KAAK,CAAC;IAClC;IACA,OAAOwB,QAAQ,CAACnB,UAAU,CAACL,QAAQ,EAAE+B,MAAM,CAAC,EAAEA,MAAM,GAAG9B,QAAQ,CAAC;EAClE,CAAC,EAAE,CAACsB,cAAc,EAAEY,YAAY,EAAEX,QAAQ,EAAExB,QAAQ,EAAE+B,MAAM,EAAE9B,QAAQ,CAAC,CAAC;EACxE;EACAP,yBAAyB,CAAC,MAAM;IAC9B,IAAI6B,cAAc,IAAIG,KAAK,IAAIC,QAAQ,IAAI1B,QAAQ,EAAE;MACnDmC,eAAe,CAACnB,OAAO,CAAC;MACxBgB,YAAY,CAAC,CAAC,CAAC,EAAEW,IAAI,CAACC,IAAI,CAAC5C,QAAQ,GAAG,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC;IACtD;EACF,CAAC,EAAE,CAACsB,cAAc,EAAEG,KAAK,EAAEC,QAAQ,EAAEF,IAAI,EAAExB,QAAQ,EAAE2B,IAAI,CAAC,CAAC;EAC3DlC,yBAAyB,CAAC,MAAM;IAC9B,IAAIoD,EAAE;IACN,IAAIX,YAAY,KAAKlB,OAAO,EAAE;MAC5BqB,kBAAkB,CAAC,CAAC,CAACQ,EAAE,GAAGP,YAAY,CAACQ,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,YAAY,KAAK,CAAC,CAAC;IAC7G;EACF,CAAC,EAAE,CAACb,YAAY,CAAC,CAAC;EAClBzC,yBAAyB,CAAC,MAAM;IAC9B,IAAIoD,EAAE,EAAEG,EAAE;IACV,IAAIZ,eAAe,EAAE;MACnB,IAAIF,YAAY,KAAKlB,OAAO,EAAE;QAC5B;QACA,MAAMiC,SAAS,GAAG,CAAC,CAACJ,EAAE,GAAGL,SAAS,CAACM,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,YAAY,KAAK,CAAC;QACtG,MAAMG,SAAS,GAAGvB,IAAI,GAAGS,eAAe;QACxC,IAAIa,SAAS,IAAIC,SAAS,EAAE;UAC1Bf,eAAe,CAAChB,qBAAqB,CAAC;UACtCS,UAAU,CAAC,KAAK,CAAC;QACnB,CAAC,MAAM;UACLO,eAAe,CAAClB,OAAO,CAAC;QAC1B;MACF,CAAC,MAAM,IAAIiB,YAAY,KAAKjB,OAAO,EAAE;QACnC,IAAIY,QAAQ,KAAKE,MAAM,EAAE;UACvB,MAAMkB,SAAS,GAAG,CAAC,CAACD,EAAE,GAAGR,SAAS,CAACM,OAAO,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACD,YAAY,KAAK,CAAC;UACtG,MAAMG,SAAS,GAAGvB,IAAI,GAAGS,eAAe;UACxC,IAAIe,YAAY,GAAGtB,QAAQ;UAC3B,IAAIuB,UAAU,GAAGrB,MAAM;UACvB;UACA,IAAIF,QAAQ,KAAKE,MAAM,GAAG,CAAC,EAAE;YAC3BqB,UAAU,GAAGvB,QAAQ;UACvB,CAAC,MAAM,IAAIoB,SAAS,IAAIC,SAAS,EAAE;YACjCC,YAAY,GAAGrB,MAAM;UACvB,CAAC,MAAM;YACLsB,UAAU,GAAGtB,MAAM;UACrB;UACA,MAAMuB,UAAU,GAAGV,IAAI,CAACC,IAAI,CAAC,CAACO,YAAY,GAAGC,UAAU,IAAI,CAAC,CAAC;UAC7DpB,YAAY,CAAC,CAACmB,YAAY,EAAEE,UAAU,EAAED,UAAU,CAAC,CAAC;QACtD,CAAC,MAAM;UACLjB,eAAe,CAACjB,kBAAkB,CAAC;UACnCU,UAAU,CAAC,IAAI,CAAC;QAClB;MACF;IACF;EACF,CAAC,EAAE,CAACM,YAAY,EAAEL,QAAQ,EAAEE,MAAM,EAAEJ,IAAI,EAAES,eAAe,CAAC,CAAC;EAC3D;EACA,MAAMkB,YAAY,GAAG;IACnB7B,KAAK;IACL8B,UAAU,EAAE,QAAQ;IACpBC,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE;EACX,CAAC;EACD,MAAMC,aAAa,GAAGA,CAACC,OAAO,EAAEC,GAAG,EAAEC,KAAK,KAAK,aAAanE,KAAK,CAACoE,aAAa,CAAC,MAAM,EAAE;IACtF,aAAa,EAAE,IAAI;IACnBF,GAAG,EAAEA,GAAG;IACRC,KAAK,EAAEE,MAAM,CAACC,MAAM,CAAC;MACnBC,QAAQ,EAAE,OAAO;MACjBC,OAAO,EAAE,OAAO;MAChBC,IAAI,EAAE,CAAC;MACPC,GAAG,EAAE,CAAC;MACNC,MAAM,EAAE,CAAC,IAAI;MACbC,UAAU,EAAE,QAAQ;MACpBC,aAAa,EAAE,MAAM;MACrB7C,QAAQ,EAAEiB,IAAI,CAAC6B,KAAK,CAAC9C,QAAQ,GAAG,CAAC,CAAC,GAAG;IACvC,CAAC,EAAEmC,KAAK;EACV,CAAC,EAAEF,OAAO,CAAC;EACX,MAAMc,kBAAkB,GAAGA,CAACpE,GAAG,EAAEuD,GAAG,KAAK;IACvC,MAAMc,aAAa,GAAGtE,UAAU,CAACL,QAAQ,EAAEM,GAAG,CAAC;IAC/C,OAAOqD,aAAa,CAACnC,QAAQ,CAACmD,aAAa,EAAE,IAAI,CAAC,EAAEd,GAAG,EAAEN,YAAY,CAAC;EACxE,CAAC;EACD,OAAO,aAAa5D,KAAK,CAACoE,aAAa,CAACpE,KAAK,CAACiF,QAAQ,EAAE,IAAI,EAAEjC,cAAc,EAAEpB,cAAc,IAAIY,YAAY,KAAKhB,kBAAkB,IAAIgB,YAAY,KAAKf,qBAAqB,IAAI,aAAazB,KAAK,CAACoE,aAAa,CAACpE,KAAK,CAACiF,QAAQ,EAAE,IAAI,EAAEjB,aAAa,CAAC,IAAI,EAAEpB,YAAY,EAAE;IACxQsC,SAAS,EAAE,UAAU;IACrBrB,UAAU,EAAE;EACd,CAAC,CAAC,EAAErB,YAAY,KAAKlB,OAAO,GAAG0C,aAAa,CAACnC,QAAQ,CAACxB,QAAQ,EAAE,KAAK,CAAC,EAAEyC,SAAS,EAAEc,YAAY,CAAC,GAAGmB,kBAAkB,CAAC3C,MAAM,EAAEU,SAAS,CAAC,CAAC,CAAC;AAC5I,CAAC;AACD,IAAIqC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC3D,QAAQ,CAAC4D,WAAW,GAAG,UAAU;AACnC;AACA,eAAe5D,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}