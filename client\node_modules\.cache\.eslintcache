[{"C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\store.js": "4", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ProtectedRoute.js": "5", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\Loader.js": "6", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminProtectedRoute.js": "7", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\contexts\\ThemeContext.js": "8", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\RankingErrorBoundary.js": "9", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\DebugAuth.jsx": "10", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\RankingDemo.js": "11", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\loaderSlice.js": "12", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\usersSlice.js": "13", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\subscriptionSlice.js": "14", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\trial\\TrialPage.js": "15", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\paymentSlice.js": "16", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditExam.js": "17", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Notifications\\AdminNotifications.jsx": "18", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizPlay.js": "19", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizResult.js": "20", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\index.js": "21", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\index.js": "22", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\index.js": "23", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Forum\\index.js": "24", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AdminReports\\index.js": "25", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Dashboard\\index.js": "26", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\index.js": "27", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Users\\index.js": "28", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Subscription\\index.js": "29", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Test\\index.js": "30", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\AboutUs\\index.js": "31", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\UserReports\\index.js": "32", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Ranking\\index.js": "33", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\index.js": "34", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\index.js": "35", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\VideoLessons\\index.js": "36", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Hub\\index.js": "37", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Home\\index.js": "38", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Register\\index.js": "39", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Login\\index.js": "40", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Forum\\index.js": "41", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Profile\\index.js": "42", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\payment.js": "43", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ModernSidebar.js": "44", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminNavigation.js": "45", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\FloatingBrainwaveAI.js": "46", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\users.js": "47", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\syllabus.js": "48", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\aiQuestions.js": "49", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\notifications.js": "50", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\PageTitle.js": "51", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\exams.js": "52", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reports.js": "53", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\chat.js": "54", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\UserRankingList.js": "55", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ContentRenderer.js": "56", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\NotificationBell.js": "57", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\UserRankingCard.js": "58", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\ProfilePicture.js": "59", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\OnlineStatusIndicator.js": "60", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialQuizPlay.js": "61", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialQuizSelection.js": "62", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialQuizResult.js": "63", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Button.js": "64", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Loading.js": "65", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Card.js": "66", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialRegistrationPrompt.js": "67", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Input.js": "68", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizTimer.js": "69", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\LazyImage.js": "70", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizCard.js": "71", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizQuestion.js": "72", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\forum.js": "73", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\data\\Subjects.jsx": "74", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ThemeToggle.js": "75", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminLayout.js": "76", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminCard.js": "77", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditQuestion.js": "78", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ErrorBoundary.js": "79", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ResponsiveContainer.js": "80", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\PerformanceMonitor.js": "81", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\SubtitleManager.js": "82", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\AddStudyMaterialForm.js": "83", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\plans.js": "84", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reviews.js": "85", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\EditStudyMaterialForm.js": "86", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\StudyMaterialManager.js": "87", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\study.js": "88", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\utils\\quizDataUtils.js": "89", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\XPResultDisplay.js": "90", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\SubscriptionExpiredModal\\SubscriptionExpiredModal.jsx": "91", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\TryForFreeModal.js": "92", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\UpgradeRestrictionModal\\UpgradeRestrictionModal.jsx": "93", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\PDFModal.js": "94", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\Instructions.js": "95", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\index.js": "96", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\SubscriptionModal\\SubscriptionModal.jsx": "97", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminTopNavigation.js": "98", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\trial.js": "99", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\subtitles.js": "100", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\XPProgressBar.js": "101", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\AchievementBadge.js": "102", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\EnhancedAchievementBadge.js": "103", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\LevelBadge.js": "104", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\contexts\\LanguageContext.js": "105", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\localization\\kiswahili.js": "106", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Skills\\index.js": "107", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\skills.js": "108", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Skills\\index.js": "109", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\PastPaperDiscussion.js": "110", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\aiResponse.js": "111"}, {"size": 395, "mtime": 1696247250000, "results": "112", "hashOfConfig": "113"}, {"size": 13439, "mtime": 1752343277235, "results": "114", "hashOfConfig": "113"}, {"size": 362, "mtime": 1696247250000, "results": "115", "hashOfConfig": "113"}, {"size": 430, "mtime": 1736735017645, "results": "116", "hashOfConfig": "113"}, {"size": 20340, "mtime": 1752145561052, "results": "117", "hashOfConfig": "113"}, {"size": 180, "mtime": 1696247250000, "results": "118", "hashOfConfig": "113"}, {"size": 1601, "mtime": 1751575879221, "results": "119", "hashOfConfig": "113"}, {"size": 1410, "mtime": 1751140352157, "results": "120", "hashOfConfig": "113"}, {"size": 3109, "mtime": 1751260973778, "results": "121", "hashOfConfig": "113"}, {"size": 9770, "mtime": 1751495320007, "results": "122", "hashOfConfig": "113"}, {"size": 12711, "mtime": 1751260271529, "results": "123", "hashOfConfig": "113"}, {"size": 416, "mtime": 1696247250000, "results": "124", "hashOfConfig": "113"}, {"size": 334, "mtime": 1696247250000, "results": "125", "hashOfConfig": "113"}, {"size": 404, "mtime": 1736731932223, "results": "126", "hashOfConfig": "113"}, {"size": 6016, "mtime": 1751638273351, "results": "127", "hashOfConfig": "113"}, {"size": 449, "mtime": 1736732007232, "results": "128", "hashOfConfig": "113"}, {"size": 15139, "mtime": 1751582824879, "results": "129", "hashOfConfig": "113"}, {"size": 10989, "mtime": 1751586090664, "results": "130", "hashOfConfig": "113"}, {"size": 37700, "mtime": 1752198101933, "results": "131", "hashOfConfig": "113"}, {"size": 48118, "mtime": 1752336893747, "results": "132", "hashOfConfig": "113"}, {"size": 1140, "mtime": 1751426583568, "results": "133", "hashOfConfig": "113"}, {"size": 33135, "mtime": 1752338943014, "results": "134", "hashOfConfig": "113"}, {"size": 10464, "mtime": 1752187297118, "results": "135", "hashOfConfig": "113"}, {"size": 12434, "mtime": 1751869385895, "results": "136", "hashOfConfig": "113"}, {"size": 16063, "mtime": 1751870755172, "results": "137", "hashOfConfig": "113"}, {"size": 12035, "mtime": 1751869525840, "results": "138", "hashOfConfig": "113"}, {"size": 8883, "mtime": 1751585554937, "results": "139", "hashOfConfig": "113"}, {"size": 22154, "mtime": 1751585459342, "results": "140", "hashOfConfig": "113"}, {"size": 45883, "mtime": 1752135725326, "results": "141", "hashOfConfig": "113"}, {"size": 1327, "mtime": 1709427669270, "results": "142", "hashOfConfig": "113"}, {"size": 8089, "mtime": 1740446459586, "results": "143", "hashOfConfig": "113"}, {"size": 27236, "mtime": 1751873126836, "results": "144", "hashOfConfig": "113"}, {"size": 136450, "mtime": 1752148102332, "results": "145", "hashOfConfig": "113"}, {"size": 24119, "mtime": 1752346154566, "results": "146", "hashOfConfig": "113"}, {"size": 48284, "mtime": 1752336822163, "results": "147", "hashOfConfig": "113"}, {"size": 41681, "mtime": 1752346657231, "results": "148", "hashOfConfig": "113"}, {"size": 7870, "mtime": 1752338665955, "results": "149", "hashOfConfig": "113"}, {"size": 68001, "mtime": 1752145575965, "results": "150", "hashOfConfig": "113"}, {"size": 9971, "mtime": 1752333866970, "results": "151", "hashOfConfig": "113"}, {"size": 4378, "mtime": 1752193825539, "results": "152", "hashOfConfig": "113"}, {"size": 32699, "mtime": 1752346549126, "results": "153", "hashOfConfig": "113"}, {"size": 47852, "mtime": 1752339297891, "results": "154", "hashOfConfig": "113"}, {"size": 2046, "mtime": 1752102606942, "results": "155", "hashOfConfig": "113"}, {"size": 13836, "mtime": 1752343174543, "results": "156", "hashOfConfig": "113"}, {"size": 15668, "mtime": 1752343935697, "results": "157", "hashOfConfig": "113"}, {"size": 24116, "mtime": 1752336667751, "results": "158", "hashOfConfig": "113"}, {"size": 3191, "mtime": 1751940514617, "results": "159", "hashOfConfig": "113"}, {"size": 7315, "mtime": 1751495843287, "results": "160", "hashOfConfig": "113"}, {"size": 6337, "mtime": 1751558223480, "results": "161", "hashOfConfig": "113"}, {"size": 3632, "mtime": 1751487806125, "results": "162", "hashOfConfig": "113"}, {"size": 388, "mtime": 1703845955779, "results": "163", "hashOfConfig": "113"}, {"size": 2455, "mtime": 1751479784424, "results": "164", "hashOfConfig": "113"}, {"size": 3391, "mtime": 1751304153158, "results": "165", "hashOfConfig": "113"}, {"size": 1104, "mtime": 1749936905424, "results": "166", "hashOfConfig": "113"}, {"size": 29870, "mtime": 1752145669266, "results": "167", "hashOfConfig": "113"}, {"size": 5595, "mtime": 1751164672302, "results": "168", "hashOfConfig": "113"}, {"size": 11831, "mtime": 1752096169929, "results": "169", "hashOfConfig": "113"}, {"size": 18256, "mtime": 1751482855935, "results": "170", "hashOfConfig": "113"}, {"size": 6382, "mtime": 1752146723210, "results": "171", "hashOfConfig": "113"}, {"size": 3307, "mtime": 1751855844189, "results": "172", "hashOfConfig": "113"}, {"size": 24195, "mtime": 1752195775706, "results": "173", "hashOfConfig": "113"}, {"size": 10009, "mtime": 1751649332583, "results": "174", "hashOfConfig": "113"}, {"size": 27796, "mtime": 1751916780076, "results": "175", "hashOfConfig": "113"}, {"size": 2913, "mtime": 1751140370241, "results": "176", "hashOfConfig": "113"}, {"size": 3119, "mtime": 1751164996340, "results": "177", "hashOfConfig": "113"}, {"size": 1857, "mtime": 1751140385464, "results": "178", "hashOfConfig": "113"}, {"size": 10040, "mtime": 1751638250072, "results": "179", "hashOfConfig": "113"}, {"size": 2324, "mtime": 1751140401815, "results": "180", "hashOfConfig": "113"}, {"size": 7165, "mtime": 1752195834207, "results": "181", "hashOfConfig": "113"}, {"size": 2504, "mtime": 1751957740575, "results": "182", "hashOfConfig": "113"}, {"size": 17286, "mtime": 1752193750030, "results": "183", "hashOfConfig": "113"}, {"size": 13299, "mtime": 1751249005755, "results": "184", "hashOfConfig": "113"}, {"size": 1787, "mtime": 1734985908268, "results": "185", "hashOfConfig": "113"}, {"size": 1245, "mtime": 1752333815446, "results": "186", "hashOfConfig": "113"}, {"size": 3904, "mtime": 1751143777976, "results": "187", "hashOfConfig": "113"}, {"size": 2200, "mtime": 1751563008113, "results": "188", "hashOfConfig": "113"}, {"size": 1717, "mtime": 1751561083661, "results": "189", "hashOfConfig": "113"}, {"size": 12864, "mtime": 1751134045332, "results": "190", "hashOfConfig": "113"}, {"size": 5088, "mtime": 1751143254906, "results": "191", "hashOfConfig": "113"}, {"size": 4989, "mtime": 1751143312418, "results": "192", "hashOfConfig": "113"}, {"size": 6304, "mtime": 1751188593099, "results": "193", "hashOfConfig": "113"}, {"size": 9494, "mtime": 1750995979612, "results": "194", "hashOfConfig": "113"}, {"size": 29255, "mtime": 1752333961995, "results": "195", "hashOfConfig": "113"}, {"size": 279, "mtime": 1736719733927, "results": "196", "hashOfConfig": "113"}, {"size": 578, "mtime": 1705434185826, "results": "197", "hashOfConfig": "113"}, {"size": 17375, "mtime": 1751000106093, "results": "198", "hashOfConfig": "113"}, {"size": 11161, "mtime": 1750999560542, "results": "199", "hashOfConfig": "113"}, {"size": 6669, "mtime": 1750999504134, "results": "200", "hashOfConfig": "113"}, {"size": 9114, "mtime": 1751691985112, "results": "201", "hashOfConfig": "113"}, {"size": 16372, "mtime": 1751479340474, "results": "202", "hashOfConfig": "113"}, {"size": 9653, "mtime": 1752084006410, "results": "203", "hashOfConfig": "113"}, {"size": 9006, "mtime": 1752334003537, "results": "204", "hashOfConfig": "113"}, {"size": 6486, "mtime": 1752078868080, "results": "205", "hashOfConfig": "113"}, {"size": 8101, "mtime": 1750963515173, "results": "206", "hashOfConfig": "113"}, {"size": 3835, "mtime": 1751478376207, "results": "207", "hashOfConfig": "113"}, {"size": 3672, "mtime": 1752017325220, "results": "208", "hashOfConfig": "113"}, {"size": 13621, "mtime": 1752105816006, "results": "209", "hashOfConfig": "113"}, {"size": 6575, "mtime": 1752343969641, "results": "210", "hashOfConfig": "113"}, {"size": 1024, "mtime": 1751637471453, "results": "211", "hashOfConfig": "113"}, {"size": 1524, "mtime": 1750994293078, "results": "212", "hashOfConfig": "113"}, {"size": 8429, "mtime": 1751244672688, "results": "213", "hashOfConfig": "113"}, {"size": 11901, "mtime": 1751236424130, "results": "214", "hashOfConfig": "113"}, {"size": 10081, "mtime": 1751244608756, "results": "215", "hashOfConfig": "113"}, {"size": 7685, "mtime": 1751244700154, "results": "216", "hashOfConfig": "113"}, {"size": 3773, "mtime": 1752336238191, "results": "217", "hashOfConfig": "113"}, {"size": 13619, "mtime": 1752346804847, "results": "218", "hashOfConfig": "113"}, {"size": 17717, "mtime": 1752343073846, "results": "219", "hashOfConfig": "113"}, {"size": 2718, "mtime": 1752343803321, "results": "220", "hashOfConfig": "113"}, {"size": 16536, "mtime": 1752343895497, "results": "221", "hashOfConfig": "113"}, {"size": 7929, "mtime": 1752345739823, "results": "222", "hashOfConfig": "113"}, {"size": 1790, "mtime": 1752345695021, "results": "223", "hashOfConfig": "113"}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ymk59w", {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "347", "usedDeprecatedRules": "348"}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\store.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ProtectedRoute.js", ["559", "560", "561", "562", "563", "564", "565", "566", "567", "568", "569", "570", "571", "572", "573", "574", "575", "576"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\Loader.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\contexts\\ThemeContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\RankingErrorBoundary.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\DebugAuth.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\RankingDemo.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\loaderSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\usersSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\subscriptionSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\trial\\TrialPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\paymentSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditExam.js", ["577", "578"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Notifications\\AdminNotifications.jsx", [], ["579"], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizPlay.js", ["580"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizResult.js", ["581"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\index.js", ["582", "583", "584", "585", "586", "587", "588", "589", "590"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\index.js", ["591"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Forum\\index.js", ["592", "593", "594"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AdminReports\\index.js", ["595", "596", "597"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Dashboard\\index.js", ["598", "599", "600", "601", "602"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Users\\index.js", ["603", "604", "605", "606"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Subscription\\index.js", ["607", "608", "609", "610", "611", "612"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Test\\index.js", ["613", "614"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\AboutUs\\index.js", ["615"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\UserReports\\index.js", ["616", "617", "618", "619", "620", "621", "622", "623"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Ranking\\index.js", ["624", "625", "626", "627", "628", "629", "630", "631", "632", "633", "634", "635", "636", "637", "638", "639", "640"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\index.js", ["641", "642", "643", "644", "645", "646", "647", "648", "649", "650", "651", "652", "653", "654", "655", "656", "657"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\index.js", ["658", "659", "660"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\VideoLessons\\index.js", ["661", "662", "663", "664", "665", "666", "667", "668", "669", "670", "671"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Hub\\index.js", ["672", "673", "674", "675", "676", "677"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Home\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Register\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Login\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Forum\\index.js", ["678"], [], "import React, { useState, useEffect, useRef } from \"react\";\r\nimport \"./index.css\";\r\nimport { getUserInfo } from \"../../../apicalls/users\";\r\nimport { message, Button, Input, Form, Avatar, Pagination } from \"antd\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport ProfilePicture from \"../../../components/common/ProfilePicture\";\r\nimport OnlineStatusIndicator from \"../../../components/common/OnlineStatusIndicator\";\r\nimport { useLanguage } from \"../../../contexts/LanguageContext\";\r\nimport {\r\n  addQuestion,\r\n  addReply,\r\n  getAllQuestions,\r\n  deleteQuestion,\r\n  updateQuestion,\r\n  updateReplyStatus,\r\n} from \"../../../apicalls/forum\";\r\nimport { getForumAIResponse } from \"../../../apicalls/aiResponse\";\r\nimport image from \"../../../assets/person.png\";\r\nimport { FaPencilAlt, FaRobot } from \"react-icons/fa\";\r\nimport { MdDelete, MdMessage } from \"react-icons/md\";\r\nimport { FaCheck } from \"react-icons/fa\";\r\n\r\nconst Forum = () => {\r\n  const { user } = useSelector((state) => state.user);\r\n  const { t, isKiswahili } = useLanguage();\r\n  const [isAdmin, setIsAdmin] = useState(false);\r\n  const [userData, setUserData] = useState(\"\");\r\n  const [questions, setQuestions] = useState([]);\r\n  const [expandedReplies, setExpandedReplies] = useState({});\r\n  const [askQuestionVisible, setAskQuestionVisible] = useState(false);\r\n  const [replyQuestionId, setReplyQuestionId] = useState(null);\r\n  const [editQuestion, setEditQuestion] = useState(null);\r\n  const [form] = Form.useForm();\r\n  const [isInitialLoad, setIsInitialLoad] = useState(true);\r\n  const dispatch = useDispatch();\r\n\r\n  // Skeleton loader component\r\n  const ForumSkeleton = () => (\r\n    <div className=\"forum-container\">\r\n      <div className=\"forum-header\">\r\n        <div className=\"h-8 bg-gray-200 rounded w-48 mb-4 animate-pulse\"></div>\r\n        <div className=\"h-10 bg-blue-100 rounded w-32 animate-pulse\"></div>\r\n      </div>\r\n      <div className=\"forum-content\">\r\n        {[...Array(5)].map((_, i) => (\r\n          <div key={i} className=\"question-card mb-4 p-4 border rounded-lg\">\r\n            <div className=\"flex items-start space-x-3\">\r\n              <div className=\"w-10 h-10 bg-gray-200 rounded-full animate-pulse\"></div>\r\n              <div className=\"flex-1\">\r\n                <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2 animate-pulse\"></div>\r\n                <div className=\"h-3 bg-gray-100 rounded w-1/2 mb-3 animate-pulse\"></div>\r\n                <div className=\"h-3 bg-gray-100 rounded w-full mb-2 animate-pulse\"></div>\r\n                <div className=\"h-3 bg-gray-100 rounded w-2/3 animate-pulse\"></div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n  const [form2] = Form.useForm();\r\n  const dispatch = useDispatch();\r\n  const [replyRefs, setReplyRefs] = useState({});\r\n\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [totalPages, setTotalPages] = useState(1);\r\n  const [limit] = useState(10);\r\n  const [totalQuestions, setTotalQuestions] = useState(0);\r\n\r\n  // Function to clear all forum caches\r\n  const clearAllForumCaches = () => {\r\n    const allLevels = ['primary', 'secondary', 'advance'];\r\n    allLevels.forEach(level => {\r\n      for (let p = 1; p <= 20; p++) { // Clear up to 20 pages\r\n        localStorage.removeItem(`forum_questions_${level}_${p}_${limit}`);\r\n        localStorage.removeItem(`forum_questions_${level}_${p}_${limit}_time`);\r\n      }\r\n    });\r\n  };\r\n\r\n  const fetchQuestions = async (page) => {\r\n    try {\r\n      // Clear caches for other levels to prevent contamination\r\n      const userLevel = userData?.level || 'primary';\r\n      const allLevels = ['primary', 'secondary', 'advance'];\r\n      allLevels.forEach(level => {\r\n        if (level !== userLevel) {\r\n          // Clear cache for other levels\r\n          for (let p = 1; p <= 10; p++) { // Clear up to 10 pages\r\n            localStorage.removeItem(`forum_questions_${level}_${p}_${limit}`);\r\n            localStorage.removeItem(`forum_questions_${level}_${p}_${limit}_time`);\r\n          }\r\n        }\r\n      });\r\n\r\n      // Check cache first - make it level-specific to prevent cross-level contamination\r\n      const cacheKey = `forum_questions_${userLevel}_${page}_${limit}`;\r\n      const cachedData = localStorage.getItem(cacheKey);\r\n      const cacheTime = localStorage.getItem(`${cacheKey}_time`);\r\n      const now = Date.now();\r\n\r\n      // Use cache if less than 5 minutes old\r\n      if (cachedData && cacheTime && (now - parseInt(cacheTime)) < 300000) {\r\n        const cached = JSON.parse(cachedData);\r\n        setQuestions(cached.questions);\r\n        setTotalQuestions(cached.totalQuestions);\r\n        setTotalPages(cached.totalPages);\r\n        return;\r\n      }\r\n\r\n      dispatch(ShowLoading());\r\n      const response = await getAllQuestions({ page, limit }); // Pass query params to API call\r\n      if (response.success) {\r\n        console.log(response.data);\r\n        setQuestions(response.data); // No need to reverse as backend will handle order\r\n        setTotalQuestions(response.totalQuestions);\r\n        setTotalPages(response.totalPages);\r\n\r\n        // Cache the data with level-specific key\r\n        localStorage.setItem(cacheKey, JSON.stringify({\r\n          questions: response.data,\r\n          totalQuestions: response.totalQuestions,\r\n          totalPages: response.totalPages\r\n        }));\r\n        localStorage.setItem(`${cacheKey}_time`, now.toString());\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    // Clear caches for other levels when component mounts or page changes\r\n    clearAllForumCaches();\r\n    fetchQuestions(currentPage).finally(() => {\r\n      setIsInitialLoad(false);\r\n    });\r\n  }, [currentPage, limit]);\r\n\r\n  const handlePageChange = (page) => {\r\n    setCurrentPage(page);\r\n  };\r\n\r\n  const getUserData = async () => {\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await getUserInfo();\r\n      if (response.success) {\r\n        if (response.data.isAdmin) {\r\n          setIsAdmin(true);\r\n          setUserData(response.data);\r\n          await fetchQuestions();\r\n        } else {\r\n          setIsAdmin(false);\r\n          setUserData(response.data);\r\n          await fetchQuestions();\r\n        }\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (localStorage.getItem(\"token\")) {\r\n      getUserData();\r\n    }\r\n  }, []);\r\n\r\n  const toggleReplies = (questionId) => {\r\n    setExpandedReplies((prevExpandedReplies) => ({\r\n      ...prevExpandedReplies,\r\n      [questionId]: !prevExpandedReplies[questionId],\r\n    }));\r\n  };\r\n\r\n  const handleAskQuestion = async (values) => {\r\n    try {\r\n      const response = await addQuestion(values);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        setAskQuestionVisible(false);\r\n        form.resetFields();\r\n        clearAllForumCaches(); // Clear cache when new question is added\r\n        await fetchQuestions();\r\n\r\n        // Trigger automatic AI response\r\n        await generateAutoAIResponse(values, response.data);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  // Generate automatic AI response for new questions\r\n  const generateAutoAIResponse = async (questionData, questionResponse) => {\r\n    try {\r\n      const aiResponseData = {\r\n        questionContent: `${questionData.title}\\n\\n${questionData.body}`,\r\n        subject: questionData.subject,\r\n        topic: questionData.topic,\r\n        userLevel: user?.level,\r\n        language: isKiswahili ? 'kiswahili' : 'english'\r\n      };\r\n\r\n      const aiResponse = await getForumAIResponse(aiResponseData);\r\n\r\n      if (aiResponse.success) {\r\n        // Add AI response as a reply to the question\r\n        const aiReplyData = {\r\n          questionId: questionResponse._id,\r\n          text: aiResponse.data.response,\r\n          isAI: true\r\n        };\r\n\r\n        await addReply(aiReplyData);\r\n        // Refresh questions to show the AI response\r\n        await fetchQuestions();\r\n      }\r\n    } catch (error) {\r\n      console.error('Auto AI Response Error:', error);\r\n      // Don't show error to user as this is automatic\r\n    }\r\n  };\r\n\r\n  const handleReply = (questionId) => {\r\n    setReplyQuestionId(questionId);\r\n  };\r\n\r\n  const handleReplySubmit = async (values) => {\r\n    try {\r\n      const payload = {\r\n        questionId: replyQuestionId,\r\n        text: values.text,\r\n      };\r\n      const response = await addReply(payload);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        setReplyQuestionId(null);\r\n        form.resetFields();\r\n        clearAllForumCaches(); // Clear cache when new reply is added\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (replyQuestionId && !replyRefs[replyQuestionId]) {\r\n      setReplyRefs((prevRefs) => ({\r\n        ...prevRefs,\r\n        [replyQuestionId]: React.createRef(),\r\n      }));\r\n    }\r\n  }, [replyQuestionId, replyRefs]);\r\n\r\n  useEffect(() => {\r\n    if (replyQuestionId && replyRefs[replyQuestionId]) {\r\n      replyRefs[replyQuestionId].current.scrollIntoView({ behavior: \"smooth\" });\r\n    }\r\n  }, [replyQuestionId, replyRefs]);\r\n\r\n  const handleEdit = (question) => {\r\n    setEditQuestion(question);\r\n  };\r\n\r\n  const handleDelete = async (question) => {\r\n    try {\r\n      const confirmDelete = window.confirm(\r\n        \"Are you sure you want to delete this question?\"\r\n      );\r\n      if (!confirmDelete) {\r\n        return;\r\n      }\r\n      const response = await deleteQuestion(question._id);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        clearAllForumCaches(); // Clear cache when question is deleted\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const handleUpdateQuestion = async (values) => {\r\n    try {\r\n      const response = await updateQuestion(values, editQuestion._id);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        setEditQuestion(null);\r\n        clearAllForumCaches(); // Clear cache when question is updated\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const handleCancelUpdate = () => {\r\n    setEditQuestion(\"\");\r\n  };\r\n  const handleCancelAdd = () => {\r\n    setAskQuestionVisible(false);\r\n    form.resetFields();\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (editQuestion) {\r\n      form2.setFieldsValue({\r\n        title: editQuestion.title,\r\n        body: editQuestion.body,\r\n      });\r\n    } else {\r\n      form2.resetFields();\r\n    }\r\n  }, [editQuestion]);\r\n\r\n  const handleUpdateStatus = async (questionId, replyId, status) => {\r\n    try {\r\n      const response = await updateReplyStatus({ replyId, status }, questionId);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        clearAllForumCaches(); // Clear cache when reply status is updated\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  // Show skeleton on initial load\r\n  if (isInitialLoad && questions.length === 0) {\r\n    return <ForumSkeleton />;\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\r\n      <div className=\"Forum max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-4 sm:py-6 lg:py-8\">\r\n        {/* Modern Header Section */}\r\n        <div className=\"text-center mb-8 sm:mb-10 lg:mb-12\">\r\n          <div className=\"inline-flex items-center justify-center w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl sm:rounded-2xl mb-4 sm:mb-6 shadow-lg\">\r\n            <i className=\"ri-discuss-line text-lg sm:text-xl lg:text-2xl text-white\"></i>\r\n          </div>\r\n          <h1 className=\"text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 px-4\">\r\n            Community <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600\">Forum</span>\r\n          </h1>\r\n          <p className=\"text-sm sm:text-base lg:text-lg text-gray-600 max-w-xl sm:max-w-2xl mx-auto mb-6 sm:mb-8 px-4\">\r\n            Connect with fellow learners, ask questions, share knowledge, and grow together in our vibrant learning community.\r\n          </p>\r\n\r\n          {/* Ask Question Button */}\r\n          <button\r\n            onClick={() => setAskQuestionVisible(true)}\r\n            className=\"inline-flex items-center px-6 py-3 sm:px-8 sm:py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold rounded-lg sm:rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 text-sm sm:text-base\"\r\n          >\r\n            <i className=\"ri-add-line text-lg sm:text-xl mr-2\"></i>\r\n            Ask a Question\r\n          </button>\r\n        </div>\r\n\r\n        {/* Modern Ask Question Form */}\r\n        {askQuestionVisible && (\r\n          <div className=\"bg-white rounded-xl sm:rounded-2xl shadow-xl p-4 sm:p-6 lg:p-8 mb-6 sm:mb-8 border border-gray-100\">\r\n            <div className=\"flex items-center mb-4 sm:mb-6\">\r\n              <div className=\"w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center mr-3 sm:mr-4\">\r\n                <i className=\"ri-question-line text-white text-sm sm:text-lg\"></i>\r\n              </div>\r\n              <h2 className=\"text-xl sm:text-2xl font-bold text-gray-900\">Ask a Question</h2>\r\n            </div>\r\n\r\n            <Form form={form} onFinish={handleAskQuestion} layout=\"vertical\" className=\"modern-form\">\r\n              <Form.Item\r\n                name=\"title\"\r\n                label=\"Question Title\"\r\n                rules={[{ required: true, message: \"Please enter a descriptive title\" }]}\r\n              >\r\n                <Input\r\n                  placeholder=\"What would you like to know?\"\r\n                  className=\"h-12 text-lg\"\r\n                />\r\n              </Form.Item>\r\n              <Form.Item\r\n                name=\"body\"\r\n                label=\"Question Details\"\r\n                rules={[{ required: true, message: \"Please provide more details about your question\" }]}\r\n              >\r\n                <Input.TextArea\r\n                  rows={6}\r\n                  placeholder=\"Describe your question in detail. The more information you provide, the better answers you'll receive.\"\r\n                  className=\"text-base\"\r\n                />\r\n              </Form.Item>\r\n              <Form.Item className=\"mb-0\">\r\n                <div className=\"flex items-center space-x-4\">\r\n                  <Button\r\n                    type=\"primary\"\r\n                    htmlType=\"submit\"\r\n                    className=\"h-12 px-8 bg-gradient-to-r from-blue-600 to-indigo-600 border-none rounded-lg font-semibold text-base\"\r\n                  >\r\n                    <i className=\"ri-send-plane-line mr-2\"></i>\r\n                    Post Question\r\n                  </Button>\r\n                  <Button\r\n                    onClick={handleCancelAdd}\r\n                    className=\"h-12 px-6 border-gray-300 rounded-lg font-semibold text-base hover:bg-gray-50\"\r\n                  >\r\n                    Cancel\r\n                  </Button>\r\n                </div>\r\n              </Form.Item>\r\n            </Form>\r\n          </div>\r\n        )}\r\n\r\n        {/* Loading State */}\r\n        {questions.length === 0 && (\r\n          <div className=\"text-center py-12\">\r\n            <div className=\"inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4\">\r\n              <i className=\"ri-loader-4-line text-2xl text-gray-400 animate-spin\"></i>\r\n            </div>\r\n            <p className=\"text-gray-500\">Loading discussions...</p>\r\n          </div>\r\n        )}\r\n\r\n        {/* Questions Grid */}\r\n        <div className=\"space-y-4 sm:space-y-6\">\r\n          {questions.filter(question => question && question.user).map((question) => (\r\n            <div key={question._id} className=\"bg-white rounded-xl sm:rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100\">\r\n              {/* Question Header */}\r\n              <div className=\"p-4 sm:p-6 border-b border-gray-100\">\r\n                <div className=\"flex items-start justify-between\">\r\n                  <div className=\"flex items-center space-x-3 sm:space-x-4\">\r\n                    <div className=\"relative\">\r\n                      <ProfilePicture\r\n                        user={question.user}\r\n                        size=\"sm\"\r\n                        showOnlineStatus={false}\r\n                        style={{\r\n                          width: '32px',\r\n                          height: '32px'\r\n                        }}\r\n                      />\r\n                      {/* Only show online dot if user exists and is actually online */}\r\n                      {question.user && question.user.isOnline && (\r\n                        <div\r\n                          style={{\r\n                            position: 'absolute',\r\n                            bottom: '-2px',\r\n                            right: '-2px',\r\n                            width: '12px',\r\n                            height: '12px',\r\n                            backgroundColor: '#22c55e',\r\n                            borderRadius: '50%',\r\n                            border: '2px solid #ffffff',\r\n                            boxShadow: '0 2px 8px rgba(34, 197, 94, 0.6)',\r\n                            zIndex: 10\r\n                          }}\r\n                          title=\"Online\"\r\n                        />\r\n                      )}\r\n                    </div>\r\n                    <div className=\"min-w-0 flex-1\">\r\n                      <h4 className=\"font-semibold text-gray-900 text-sm sm:text-base truncate\">{question.user?.name || 'Unknown User'}</h4>\r\n                      <p className=\"text-xs sm:text-sm text-gray-500\">\r\n                        {new Date(question.createdAt).toLocaleDateString('en-US', {\r\n                          year: 'numeric',\r\n                          month: 'short',\r\n                          day: 'numeric',\r\n                          hour: '2-digit',\r\n                          minute: '2-digit'\r\n                        })}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Action Buttons */}\r\n                  <div className=\"flex items-center space-x-1 sm:space-x-2 ml-2\">\r\n                    {(userData._id === question.user?._id || userData.isAdmin) && (\r\n                      <>\r\n                        <button\r\n                          onClick={() => handleEdit(question)}\r\n                          className=\"flex items-center px-2 py-2 sm:px-3 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200\"\r\n                        >\r\n                          <FaPencilAlt className=\"w-3 h-3 sm:w-4 sm:h-4\" />\r\n                        </button>\r\n                        <button\r\n                          onClick={() => handleDelete(question)}\r\n                          className=\"flex items-center px-2 py-2 sm:px-3 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200\"\r\n                        >\r\n                          <MdDelete className=\"w-3 h-3 sm:w-4 sm:h-4\" />\r\n                        </button>\r\n                      </>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Question Content */}\r\n              <div className=\"p-4 sm:p-6\">\r\n                <h3 className=\"text-lg sm:text-xl font-bold text-gray-900 mb-3 leading-tight\">{question.title}</h3>\r\n                <p className=\"text-gray-700 leading-relaxed mb-6\">{question.body}</p>\r\n\r\n                {/* Action Bar */}\r\n                <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between pt-3 sm:pt-4 border-t border-gray-100 gap-3 sm:gap-0\">\r\n                  <div className=\"flex items-center space-x-2 sm:space-x-4 overflow-x-auto\">\r\n                    <button\r\n                      onClick={() => toggleReplies(question._id)}\r\n                      className=\"flex items-center px-3 py-2 sm:px-4 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200 text-sm sm:text-base whitespace-nowrap\"\r\n                    >\r\n                      <i className=\"ri-eye-line mr-1 sm:mr-2 text-sm sm:text-base\"></i>\r\n                      {expandedReplies[question._id] ? \"Hide Replies\" : \"View Replies\"}\r\n                    </button>\r\n                    <button\r\n                      onClick={() => handleReply(question._id)}\r\n                      className=\"flex items-center px-3 py-2 sm:px-4 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors duration-200 text-sm sm:text-base whitespace-nowrap\"\r\n                    >\r\n                      <i className=\"ri-reply-line mr-1 sm:mr-2 text-sm sm:text-base\"></i>\r\n                      Reply\r\n                    </button>\r\n                  </div>\r\n\r\n                  <div className=\"flex items-center px-2 py-1.5 sm:px-3 sm:py-2 bg-gray-50 rounded-lg self-start sm:self-auto\">\r\n                    <MdMessage className=\"w-3 h-3 sm:w-4 sm:h-4 text-gray-500 mr-1 sm:mr-2\" />\r\n                    <span className=\"text-xs sm:text-sm font-medium text-gray-700\">{question.replies.length}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Edit Question Form */}\r\n              {editQuestion && editQuestion._id === question._id && (\r\n                <Form\r\n                form={form2}\r\n                onFinish={handleUpdateQuestion}\r\n                layout=\"vertical\"\r\n                initialValues={{\r\n                  title: editQuestion.title,\r\n                  body: editQuestion.body,\r\n                }}\r\n              >\r\n                <Form.Item\r\n                  name=\"title\"\r\n                  label=\"Title\"\r\n                  rules={[\r\n                    { required: true, message: \"Please enter the title\" },\r\n                  ]}\r\n                >\r\n                  <Input style={{ padding: \"18px 12px\" }} />\r\n                </Form.Item>\r\n                <Form.Item\r\n                  name=\"body\"\r\n                  label=\"Body\"\r\n                  rules={[{ required: true, message: \"Please enter the body\" }]}\r\n                >\r\n                  <Input.TextArea />\r\n                </Form.Item>\r\n                <Form.Item>\r\n                  <Button type=\"primary\" htmlType=\"submit\">\r\n                    Update Question\r\n                  </Button>\r\n                  <Button\r\n                    onClick={handleCancelUpdate}\r\n                    style={{ marginLeft: 10 }}\r\n                  >\r\n                    Cancel\r\n                  </Button>\r\n                </Form.Item>\r\n              </Form>\r\n              )}\r\n            {expandedReplies[question._id] && (\r\n              <div className=\"mt-4 sm:mt-6 space-y-3 sm:space-y-4 bg-gray-50 rounded-lg sm:rounded-xl p-3 sm:p-4\">\r\n                <h4 className=\"text-base sm:text-lg font-semibold text-gray-800 mb-3 sm:mb-4 flex items-center\">\r\n                  <i className=\"ri-chat-3-line mr-2 text-blue-600 text-sm sm:text-base\"></i>\r\n                  Replies ({question.replies.filter(reply => reply && reply.user).length})\r\n                </h4>\r\n                {question.replies.filter(reply => reply && reply.user).map((reply, index) => (\r\n                  <div\r\n                    key={reply._id}\r\n                    className={`bg-white rounded-lg p-3 sm:p-4 shadow-sm border-l-4 ${\r\n                      reply.user?.isAdmin\r\n                        ? \"border-purple-500 bg-purple-50\"\r\n                        : reply.isVerified\r\n                        ? \"border-green-500 bg-green-50\"\r\n                        : \"border-gray-300\"\r\n                    }`}\r\n                  >\r\n                    <div className=\"flex items-start space-x-2 sm:space-x-3\">\r\n                      {/* Avatar with Online Status */}\r\n                      <div className=\"flex-shrink-0 relative\">\r\n                        <ProfilePicture\r\n                          user={reply.user}\r\n                          size=\"xs\"\r\n                          showOnlineStatus={false}\r\n                          style={{\r\n                            width: '20px',\r\n                            height: '20px'\r\n                          }}\r\n                          className=\"sm:w-6 sm:h-6\"\r\n                        />\r\n                        {/* Only show online dot if user exists and is actually online */}\r\n                        {reply.user && reply.user.isOnline && (\r\n                          <div\r\n                            style={{\r\n                              position: 'absolute',\r\n                              bottom: '-1px',\r\n                              right: '-1px',\r\n                              width: '6px',\r\n                              height: '6px',\r\n                              backgroundColor: '#22c55e',\r\n                              borderRadius: '50%',\r\n                              border: '1px solid #ffffff',\r\n                              boxShadow: '0 1px 4px rgba(34, 197, 94, 0.6)',\r\n                              zIndex: 10\r\n                            }}\r\n                            className=\"sm:w-2 sm:h-2\"\r\n                            title=\"Online\"\r\n                          />\r\n                        )}\r\n                      </div>\r\n\r\n                      {/* Reply Content */}\r\n                      <div className=\"flex-1 min-w-0\">\r\n                        {/* Header */}\r\n                        <div className=\"flex items-start sm:items-center justify-between mb-2 gap-2\">\r\n                          <div className=\"flex items-center space-x-1 sm:space-x-2 min-w-0 flex-1\">\r\n                            <h5 className=\"font-semibold text-gray-900 text-sm sm:text-base truncate\">{reply.user?.name || 'Unknown User'}</h5>\r\n                            {reply.user?.isAdmin && (\r\n                              <span className=\"px-1.5 py-0.5 sm:px-2 sm:py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full whitespace-nowrap\">\r\n                                Admin\r\n                              </span>\r\n                            )}\r\n                            {reply.isVerified && !reply.user?.isAdmin && (\r\n                              <div className=\"flex items-center space-x-1\">\r\n                                <FaCheck className=\"w-3 h-3 sm:w-4 sm:h-4 text-green-600\" />\r\n                                <span className=\"px-1.5 py-0.5 sm:px-2 sm:py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full whitespace-nowrap\">\r\n                                  Verified\r\n                                </span>\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                          <span className=\"text-xs sm:text-sm text-gray-500 whitespace-nowrap\">\r\n                            {(() => {\r\n                              try {\r\n                                const date = new Date(reply.createdAt);\r\n                                if (isNaN(date.getTime())) {\r\n                                  return 'Invalid date';\r\n                                }\r\n                                return date.toLocaleDateString('en-US', {\r\n                                  month: \"short\",\r\n                                  day: \"numeric\",\r\n                                  hour: \"2-digit\",\r\n                                  minute: \"2-digit\"\r\n                                });\r\n                              } catch (error) {\r\n                                return 'Invalid date';\r\n                              }\r\n                            })()}\r\n                          </span>\r\n                        </div>\r\n\r\n                        {/* Reply Text */}\r\n                        <div className={`leading-relaxed mb-3 text-sm sm:text-base ${\r\n                          reply.isVerified && !reply.user?.isAdmin\r\n                            ? 'text-green-800 font-medium'\r\n                            : reply.user?.isAdmin\r\n                            ? 'text-purple-800 font-medium'\r\n                            : 'text-gray-700'\r\n                        }`}>\r\n                          {reply.text}\r\n                        </div>\r\n\r\n                        {/* Admin Actions */}\r\n                        {isAdmin && !reply.user?.isAdmin && (\r\n                          <div className=\"flex justify-end\">\r\n                            <button\r\n                              onClick={() =>\r\n                                handleUpdateStatus(\r\n                                  question._id,\r\n                                  reply._id,\r\n                                  !reply.isVerified\r\n                                )\r\n                              }\r\n                              className={`px-3 py-1.5 sm:px-4 sm:py-2 text-xs sm:text-sm font-medium rounded-lg transition-colors duration-200 ${\r\n                                reply.isVerified\r\n                                  ? \"bg-red-100 text-red-700 hover:bg-red-200\"\r\n                                  : \"bg-green-100 text-green-700 hover:bg-green-200\"\r\n                              }`}\r\n                            >\r\n                              {reply.isVerified ? \"Disapprove\" : \"Approve\"}\r\n                            </button>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n            <div ref={replyRefs[question._id]} className=\"mt-4 sm:mt-6\">\r\n              {replyQuestionId === question._id && (\r\n                <div className=\"bg-white rounded-lg sm:rounded-xl p-4 sm:p-6 border border-gray-200\">\r\n                  <Form\r\n                    form={form}\r\n                    onFinish={handleReplySubmit}\r\n                    layout=\"vertical\"\r\n                  >\r\n                    <Form.Item\r\n                      name=\"text\"\r\n                      label={<span className=\"text-sm sm:text-base font-medium\">Your Reply</span>}\r\n                      rules={[\r\n                        { required: true, message: \"Please enter your reply\" },\r\n                      ]}\r\n                    >\r\n                      <Input.TextArea\r\n                        rows={3}\r\n                        className=\"text-sm sm:text-base\"\r\n                        placeholder=\"Write your reply here...\"\r\n                      />\r\n                    </Form.Item>\r\n                    <Form.Item className=\"mb-0\">\r\n                      <div className=\"flex flex-col sm:flex-row gap-2 sm:gap-3\">\r\n                        <Button\r\n                          type=\"primary\"\r\n                          htmlType=\"submit\"\r\n                          className=\"w-full sm:w-auto\"\r\n                          size=\"large\"\r\n                        >\r\n                          Submit Reply\r\n                        </Button>\r\n                        <Button\r\n                          onClick={() => setReplyQuestionId(null)}\r\n                          className=\"w-full sm:w-auto\"\r\n                          size=\"large\"\r\n                        >\r\n                          Cancel\r\n                        </Button>\r\n                      </div>\r\n                    </Form.Item>\r\n                  </Form>\r\n                </div>\r\n              )}\r\n            </div>\r\n            </div>\r\n          ))}\r\n\r\n        </div>\r\n\r\n        <Pagination\r\n          current={currentPage}\r\n          total={totalQuestions}\r\n          pageSize={limit}\r\n          onChange={handlePageChange}\r\n          style={{ marginTop: \"20px\", textAlign: \"center\" }}\r\n          showSizeChanger={false}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Forum;\r\n", [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Profile\\index.js", ["679", "680", "681", "682", "683", "684", "685", "686", "687", "688", "689", "690", "691", "692"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\payment.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ModernSidebar.js", ["693", "694", "695", "696", "697"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminNavigation.js", ["698", "699", "700"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\FloatingBrainwaveAI.js", ["701", "702", "703"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\users.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\syllabus.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\aiQuestions.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\notifications.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\PageTitle.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\exams.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reports.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\chat.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\UserRankingList.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ContentRenderer.js", ["704", "705", "706"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\NotificationBell.js", ["707", "708", "709", "710", "711"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\UserRankingCard.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\ProfilePicture.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\OnlineStatusIndicator.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialQuizPlay.js", ["712", "713"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialQuizSelection.js", ["714"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialQuizResult.js", ["715"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Button.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Loading.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Card.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialRegistrationPrompt.js", ["716"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Input.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizTimer.js", ["717"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\LazyImage.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizCard.js", ["718", "719", "720"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizQuestion.js", ["721"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\forum.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\data\\Subjects.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ThemeToggle.js", ["722"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminLayout.js", ["723"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminCard.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditQuestion.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ErrorBoundary.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ResponsiveContainer.js", ["724"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\PerformanceMonitor.js", ["725", "726"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\SubtitleManager.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\AddStudyMaterialForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\plans.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reviews.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\EditStudyMaterialForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\StudyMaterialManager.js", ["727", "728", "729", "730"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\study.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\utils\\quizDataUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\XPResultDisplay.js", ["731"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\SubscriptionExpiredModal\\SubscriptionExpiredModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\TryForFreeModal.js", ["732", "733", "734", "735", "736", "737"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\UpgradeRestrictionModal\\UpgradeRestrictionModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\PDFModal.js", [], ["738"], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\Instructions.js", ["739"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\SubscriptionModal\\SubscriptionModal.jsx", ["740", "741", "742", "743", "744", "745", "746", "747"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminTopNavigation.js", ["748", "749"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\trial.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\subtitles.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\XPProgressBar.js", ["750"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\AchievementBadge.js", ["751", "752", "753"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\EnhancedAchievementBadge.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\LevelBadge.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\contexts\\LanguageContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\localization\\kiswahili.js", ["754"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Skills\\index.js", ["755", "756"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\skills.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Skills\\index.js", ["757", "758", "759"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\PastPaperDiscussion.js", ["760", "761"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\aiResponse.js", [], [], {"ruleId": "762", "severity": 1, "message": "763", "line": 2, "column": 46, "nodeType": "764", "messageId": "765", "endLine": 2, "endColumn": 61}, {"ruleId": "762", "severity": 1, "message": "766", "line": 7, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 7, "endColumn": 21}, {"ruleId": "762", "severity": 1, "message": "767", "line": 7, "column": 23, "nodeType": "764", "messageId": "765", "endLine": 7, "endColumn": 34}, {"ruleId": "762", "severity": 1, "message": "768", "line": 12, "column": 8, "nodeType": "764", "messageId": "765", "endLine": 12, "endColumn": 23}, {"ruleId": "762", "severity": 1, "message": "769", "line": 15, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 15, "endColumn": 16}, {"ruleId": "762", "severity": 1, "message": "770", "line": 15, "column": 18, "nodeType": "764", "messageId": "765", "endLine": 15, "endColumn": 33}, {"ruleId": "762", "severity": 1, "message": "771", "line": 15, "column": 35, "nodeType": "764", "messageId": "765", "endLine": 15, "endColumn": 42}, {"ruleId": "762", "severity": 1, "message": "772", "line": 15, "column": 44, "nodeType": "764", "messageId": "765", "endLine": 15, "endColumn": 47}, {"ruleId": "762", "severity": 1, "message": "773", "line": 15, "column": 49, "nodeType": "764", "messageId": "765", "endLine": 15, "endColumn": 62}, {"ruleId": "762", "severity": 1, "message": "774", "line": 15, "column": 64, "nodeType": "764", "messageId": "765", "endLine": 15, "endColumn": 72}, {"ruleId": "762", "severity": 1, "message": "775", "line": 15, "column": 82, "nodeType": "764", "messageId": "765", "endLine": 15, "endColumn": 92}, {"ruleId": "762", "severity": 1, "message": "776", "line": 15, "column": 94, "nodeType": "764", "messageId": "765", "endLine": 15, "endColumn": 100}, {"ruleId": "762", "severity": 1, "message": "777", "line": 15, "column": 102, "nodeType": "764", "messageId": "765", "endLine": 15, "endColumn": 108}, {"ruleId": "762", "severity": 1, "message": "778", "line": 16, "column": 8, "nodeType": "764", "messageId": "765", "endLine": 16, "endColumn": 29}, {"ruleId": "779", "severity": 1, "message": "780", "line": 120, "column": 6, "nodeType": "781", "endLine": 120, "endColumn": 8, "suggestions": "782"}, {"ruleId": "779", "severity": 1, "message": "783", "line": 189, "column": 6, "nodeType": "781", "endLine": 189, "endColumn": 39, "suggestions": "784"}, {"ruleId": "779", "severity": 1, "message": "785", "line": 200, "column": 6, "nodeType": "781", "endLine": 200, "endColumn": 25, "suggestions": "786"}, {"ruleId": "762", "severity": 1, "message": "787", "line": 231, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 231, "endColumn": 23}, {"ruleId": "762", "severity": 1, "message": "788", "line": 1, "column": 35, "nodeType": "764", "messageId": "765", "endLine": 1, "endColumn": 41}, {"ruleId": "779", "severity": 1, "message": "789", "line": 110, "column": 6, "nodeType": "781", "endLine": 110, "endColumn": 8, "suggestions": "790"}, {"ruleId": "779", "severity": 1, "message": "791", "line": 46, "column": 6, "nodeType": "781", "endLine": 46, "endColumn": 8, "suggestions": "792", "suppressions": "793"}, {"ruleId": "779", "severity": 1, "message": "794", "line": 322, "column": 6, "nodeType": "781", "endLine": 322, "endColumn": 57, "suggestions": "795"}, {"ruleId": "796", "severity": 1, "message": "797", "line": 1077, "column": 29, "nodeType": "798", "endLine": 1086, "endColumn": 31}, {"ruleId": "762", "severity": 1, "message": "799", "line": 13, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 13, "endColumn": 10}, {"ruleId": "762", "severity": 1, "message": "777", "line": 17, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 17, "endColumn": 9}, {"ruleId": "762", "severity": 1, "message": "769", "line": 18, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 18, "endColumn": 9}, {"ruleId": "762", "severity": 1, "message": "800", "line": 19, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 19, "endColumn": 9}, {"ruleId": "762", "severity": 1, "message": "801", "line": 43, "column": 11, "nodeType": "764", "messageId": "765", "endLine": 43, "endColumn": 12}, {"ruleId": "762", "severity": 1, "message": "802", "line": 43, "column": 27, "nodeType": "764", "messageId": "765", "endLine": 43, "endColumn": 39}, {"ruleId": "779", "severity": 1, "message": "803", "line": 251, "column": 6, "nodeType": "781", "endLine": 251, "endColumn": 8, "suggestions": "804"}, {"ruleId": "762", "severity": 1, "message": "802", "line": 500, "column": 24, "nodeType": "764", "messageId": "765", "endLine": 500, "endColumn": 36}, {"ruleId": "762", "severity": 1, "message": "805", "line": 502, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 502, "endColumn": 19}, {"ruleId": "779", "severity": 1, "message": "806", "line": 191, "column": 6, "nodeType": "781", "endLine": 191, "endColumn": 8, "suggestions": "807"}, {"ruleId": "762", "severity": 1, "message": "808", "line": 2, "column": 27, "nodeType": "764", "messageId": "765", "endLine": 2, "endColumn": 32}, {"ruleId": "762", "severity": 1, "message": "809", "line": 2, "column": 34, "nodeType": "764", "messageId": "765", "endLine": 2, "endColumn": 38}, {"ruleId": "779", "severity": 1, "message": "810", "line": 113, "column": 6, "nodeType": "781", "endLine": 113, "endColumn": 8, "suggestions": "811"}, {"ruleId": "762", "severity": 1, "message": "812", "line": 5, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 5, "endColumn": 14}, {"ruleId": "762", "severity": 1, "message": "813", "line": 31, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 31, "endColumn": 17}, {"ruleId": "779", "severity": 1, "message": "814", "line": 241, "column": 6, "nodeType": "781", "endLine": 241, "endColumn": 35, "suggestions": "815"}, {"ruleId": "762", "severity": 1, "message": "816", "line": 12, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 12, "endColumn": 11}, {"ruleId": "762", "severity": 1, "message": "817", "line": 14, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 14, "endColumn": 10}, {"ruleId": "762", "severity": 1, "message": "818", "line": 16, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 16, "endColumn": 8}, {"ruleId": "762", "severity": 1, "message": "819", "line": 40, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 40, "endColumn": 17}, {"ruleId": "779", "severity": 1, "message": "820", "line": 44, "column": 6, "nodeType": "781", "endLine": 44, "endColumn": 8, "suggestions": "821"}, {"ruleId": "762", "severity": 1, "message": "822", "line": 12, "column": 8, "nodeType": "764", "messageId": "765", "endLine": 12, "endColumn": 17}, {"ruleId": "762", "severity": 1, "message": "823", "line": 31, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 31, "endColumn": 9}, {"ruleId": "762", "severity": 1, "message": "824", "line": 43, "column": 19, "nodeType": "764", "messageId": "765", "endLine": 43, "endColumn": 29}, {"ruleId": "779", "severity": 1, "message": "825", "line": 182, "column": 6, "nodeType": "781", "endLine": 182, "endColumn": 8, "suggestions": "826"}, {"ruleId": "762", "severity": 1, "message": "767", "line": 8, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 8, "endColumn": 21}, {"ruleId": "762", "severity": 1, "message": "766", "line": 8, "column": 23, "nodeType": "764", "messageId": "765", "endLine": 8, "endColumn": 34}, {"ruleId": "762", "severity": 1, "message": "827", "line": 28, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 28, "endColumn": 29}, {"ruleId": "762", "severity": 1, "message": "828", "line": 33, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 33, "endColumn": 17}, {"ruleId": "779", "severity": 1, "message": "829", "line": 85, "column": 6, "nodeType": "781", "endLine": 85, "endColumn": 8, "suggestions": "830"}, {"ruleId": "779", "severity": 1, "message": "831", "line": 139, "column": 6, "nodeType": "781", "endLine": 139, "endColumn": 24, "suggestions": "832"}, {"ruleId": "762", "severity": 1, "message": "833", "line": 1, "column": 38, "nodeType": "764", "messageId": "765", "endLine": 1, "endColumn": 46}, {"ruleId": "762", "severity": 1, "message": "834", "line": 8, "column": 12, "nodeType": "764", "messageId": "765", "endLine": 8, "endColumn": 19}, {"ruleId": "779", "severity": 1, "message": "835", "line": 58, "column": 8, "nodeType": "781", "endLine": 58, "endColumn": 10, "suggestions": "836"}, {"ruleId": "762", "severity": 1, "message": "837", "line": 13, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 13, "endColumn": 13}, {"ruleId": "762", "severity": 1, "message": "817", "line": 14, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 14, "endColumn": 10}, {"ruleId": "762", "severity": 1, "message": "838", "line": 15, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 15, "endColumn": 10}, {"ruleId": "762", "severity": 1, "message": "839", "line": 17, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 17, "endColumn": 13}, {"ruleId": "762", "severity": 1, "message": "840", "line": 35, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 35, "endColumn": 18}, {"ruleId": "762", "severity": 1, "message": "841", "line": 35, "column": 20, "nodeType": "764", "messageId": "765", "endLine": 35, "endColumn": 31}, {"ruleId": "779", "severity": 1, "message": "842", "line": 137, "column": 6, "nodeType": "781", "endLine": 137, "endColumn": 8, "suggestions": "843"}, {"ruleId": "779", "severity": 1, "message": "844", "line": 141, "column": 6, "nodeType": "781", "endLine": 141, "endColumn": 60, "suggestions": "845"}, {"ruleId": "762", "severity": 1, "message": "846", "line": 2, "column": 18, "nodeType": "764", "messageId": "765", "endLine": 2, "endColumn": 33}, {"ruleId": "762", "severity": 1, "message": "847", "line": 21, "column": 53, "nodeType": "764", "messageId": "765", "endLine": 21, "endColumn": 67}, {"ruleId": "762", "severity": 1, "message": "778", "line": 24, "column": 8, "nodeType": "764", "messageId": "765", "endLine": 24, "endColumn": 29}, {"ruleId": "762", "severity": 1, "message": "840", "line": 75, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 75, "endColumn": 18}, {"ruleId": "762", "severity": 1, "message": "841", "line": 75, "column": 20, "nodeType": "764", "messageId": "765", "endLine": 75, "endColumn": 31}, {"ruleId": "762", "severity": 1, "message": "848", "line": 76, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 76, "endColumn": 19}, {"ruleId": "762", "severity": 1, "message": "849", "line": 76, "column": 21, "nodeType": "764", "messageId": "765", "endLine": 76, "endColumn": 33}, {"ruleId": "762", "severity": 1, "message": "850", "line": 77, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 77, "endColumn": 24}, {"ruleId": "762", "severity": 1, "message": "851", "line": 80, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 80, "endColumn": 27}, {"ruleId": "762", "severity": 1, "message": "852", "line": 82, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 82, "endColumn": 24}, {"ruleId": "762", "severity": 1, "message": "853", "line": 90, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 90, "endColumn": 18}, {"ruleId": "762", "severity": 1, "message": "854", "line": 91, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 91, "endColumn": 23}, {"ruleId": "779", "severity": 1, "message": "855", "line": 896, "column": 6, "nodeType": "781", "endLine": 896, "endColumn": 8, "suggestions": "856"}, {"ruleId": "762", "severity": 1, "message": "857", "line": 917, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 917, "endColumn": 24}, {"ruleId": "762", "severity": 1, "message": "858", "line": 1072, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 1072, "endColumn": 29}, {"ruleId": "762", "severity": 1, "message": "859", "line": 1560, "column": 39, "nodeType": "764", "messageId": "765", "endLine": 1560, "endColumn": 48}, {"ruleId": "860", "severity": 1, "message": "861", "line": 2155, "column": 27, "nodeType": "862", "messageId": "863", "endLine": 2155, "endColumn": 28}, {"ruleId": "762", "severity": 1, "message": "864", "line": 18, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 18, "endColumn": 16}, {"ruleId": "762", "severity": 1, "message": "865", "line": 19, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 19, "endColumn": 11}, {"ruleId": "762", "severity": 1, "message": "866", "line": 27, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 27, "endColumn": 11}, {"ruleId": "762", "severity": 1, "message": "867", "line": 28, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 28, "endColumn": 11}, {"ruleId": "762", "severity": 1, "message": "868", "line": 29, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 29, "endColumn": 18}, {"ruleId": "762", "severity": 1, "message": "839", "line": 30, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 30, "endColumn": 13}, {"ruleId": "762", "severity": 1, "message": "818", "line": 31, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 31, "endColumn": 8}, {"ruleId": "762", "severity": 1, "message": "837", "line": 32, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 32, "endColumn": 13}, {"ruleId": "762", "severity": 1, "message": "869", "line": 33, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 33, "endColumn": 9}, {"ruleId": "762", "severity": 1, "message": "870", "line": 35, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 35, "endColumn": 14}, {"ruleId": "762", "severity": 1, "message": "772", "line": 36, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 36, "endColumn": 6}, {"ruleId": "762", "severity": 1, "message": "871", "line": 37, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 37, "endColumn": 18}, {"ruleId": "762", "severity": 1, "message": "872", "line": 38, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 38, "endColumn": 15}, {"ruleId": "762", "severity": 1, "message": "873", "line": 39, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 39, "endColumn": 10}, {"ruleId": "762", "severity": 1, "message": "801", "line": 47, "column": 11, "nodeType": "764", "messageId": "765", "endLine": 47, "endColumn": 12}, {"ruleId": "779", "severity": 1, "message": "874", "line": 70, "column": 9, "nodeType": "875", "endLine": 74, "endColumn": 29}, {"ruleId": "779", "severity": 1, "message": "876", "line": 269, "column": 6, "nodeType": "781", "endLine": 269, "endColumn": 48, "suggestions": "877"}, {"ruleId": "762", "severity": 1, "message": "878", "line": 18, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 18, "endColumn": 31}, {"ruleId": "762", "severity": 1, "message": "879", "line": 18, "column": 33, "nodeType": "764", "messageId": "765", "endLine": 18, "endColumn": 43}, {"ruleId": "779", "severity": 1, "message": "880", "line": 779, "column": 6, "nodeType": "781", "endLine": 779, "endColumn": 81, "suggestions": "881"}, {"ruleId": "762", "severity": 1, "message": "882", "line": 3, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 3, "endColumn": 16}, {"ruleId": "762", "severity": 1, "message": "846", "line": 3, "column": 18, "nodeType": "764", "messageId": "765", "endLine": 3, "endColumn": 33}, {"ruleId": "762", "severity": 1, "message": "883", "line": 33, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 33, "endColumn": 11}, {"ruleId": "762", "severity": 1, "message": "884", "line": 34, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 34, "endColumn": 13}, {"ruleId": "762", "severity": 1, "message": "801", "line": 47, "column": 11, "nodeType": "764", "messageId": "765", "endLine": 47, "endColumn": 12}, {"ruleId": "762", "severity": 1, "message": "802", "line": 47, "column": 27, "nodeType": "764", "messageId": "765", "endLine": 47, "endColumn": 39}, {"ruleId": "762", "severity": 1, "message": "885", "line": 72, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 72, "endColumn": 22}, {"ruleId": "762", "severity": 1, "message": "886", "line": 72, "column": 24, "nodeType": "764", "messageId": "765", "endLine": 72, "endColumn": 39}, {"ruleId": "762", "severity": 1, "message": "887", "line": 294, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 294, "endColumn": 23}, {"ruleId": "888", "severity": 2, "message": "889", "line": 327, "column": 21, "nodeType": "764", "messageId": "890", "endLine": 327, "endColumn": 34}, {"ruleId": "888", "severity": 2, "message": "889", "line": 328, "column": 18, "nodeType": "764", "messageId": "890", "endLine": 328, "endColumn": 31}, {"ruleId": "762", "severity": 1, "message": "891", "line": 9, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 9, "endColumn": 9}, {"ruleId": "762", "severity": 1, "message": "892", "line": 15, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 15, "endColumn": 15}, {"ruleId": "762", "severity": 1, "message": "893", "line": 21, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 21, "endColumn": 10}, {"ruleId": "762", "severity": 1, "message": "894", "line": 22, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 22, "endColumn": 15}, {"ruleId": "762", "severity": 1, "message": "801", "line": 29, "column": 11, "nodeType": "764", "messageId": "765", "endLine": 29, "endColumn": 12}, {"ruleId": "762", "severity": 1, "message": "895", "line": 69, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 69, "endColumn": 21}, {"ruleId": null, "fatal": true, "severity": 2, "message": "896", "line": 64, "column": 8, "nodeType": null}, {"ruleId": "762", "severity": 1, "message": "822", "line": 3, "column": 8, "nodeType": "764", "messageId": "765", "endLine": 3, "endColumn": 17}, {"ruleId": "762", "severity": 1, "message": "809", "line": 9, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 9, "endColumn": 14}, {"ruleId": "762", "severity": 1, "message": "808", "line": 9, "column": 32, "nodeType": "764", "messageId": "765", "endLine": 9, "endColumn": 37}, {"ruleId": "762", "severity": 1, "message": "897", "line": 9, "column": 39, "nodeType": "764", "messageId": "765", "endLine": 9, "endColumn": 45}, {"ruleId": "762", "severity": 1, "message": "801", "line": 18, "column": 11, "nodeType": "764", "messageId": "765", "endLine": 18, "endColumn": 12}, {"ruleId": "762", "severity": 1, "message": "802", "line": 18, "column": 27, "nodeType": "764", "messageId": "765", "endLine": 18, "endColumn": 39}, {"ruleId": "762", "severity": 1, "message": "898", "line": 21, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 21, "endColumn": 21}, {"ruleId": "762", "severity": 1, "message": "899", "line": 24, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 24, "endColumn": 22}, {"ruleId": "779", "severity": 1, "message": "900", "line": 113, "column": 6, "nodeType": "781", "endLine": 113, "endColumn": 32, "suggestions": "901"}, {"ruleId": "779", "severity": 1, "message": "902", "line": 147, "column": 6, "nodeType": "781", "endLine": 147, "endColumn": 8, "suggestions": "903"}, {"ruleId": "762", "severity": 1, "message": "904", "line": 257, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 257, "endColumn": 32}, {"ruleId": "762", "severity": 1, "message": "905", "line": 305, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 305, "endColumn": 26}, {"ruleId": "779", "severity": 1, "message": "902", "line": 329, "column": 6, "nodeType": "781", "endLine": 329, "endColumn": 8, "suggestions": "906"}, {"ruleId": "779", "severity": 1, "message": "907", "line": 336, "column": 6, "nodeType": "781", "endLine": 336, "endColumn": 19, "suggestions": "908"}, {"ruleId": "762", "severity": 1, "message": "909", "line": 12, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 12, "endColumn": 10}, {"ruleId": "762", "severity": 1, "message": "910", "line": 17, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 17, "endColumn": 15}, {"ruleId": "762", "severity": 1, "message": "911", "line": 27, "column": 11, "nodeType": "764", "messageId": "765", "endLine": 27, "endColumn": 15}, {"ruleId": "762", "severity": 1, "message": "801", "line": 28, "column": 11, "nodeType": "764", "messageId": "765", "endLine": 28, "endColumn": 12}, {"ruleId": "912", "severity": 1, "message": "913", "line": 274, "column": 21, "nodeType": "914", "messageId": "915", "endLine": 280, "endColumn": 24}, {"ruleId": "762", "severity": 1, "message": "909", "line": 11, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 11, "endColumn": 10}, {"ruleId": "762", "severity": 1, "message": "775", "line": 18, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 18, "endColumn": 13}, {"ruleId": "762", "severity": 1, "message": "828", "line": 29, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 29, "endColumn": 17}, {"ruleId": "762", "severity": 1, "message": "911", "line": 9, "column": 11, "nodeType": "764", "messageId": "765", "endLine": 9, "endColumn": 15}, {"ruleId": "762", "severity": 1, "message": "801", "line": 10, "column": 11, "nodeType": "764", "messageId": "765", "endLine": 10, "endColumn": 12}, {"ruleId": "779", "severity": 1, "message": "916", "line": 40, "column": 6, "nodeType": "781", "endLine": 40, "endColumn": 8, "suggestions": "917"}, {"ruleId": "762", "severity": 1, "message": "918", "line": 19, "column": 11, "nodeType": "764", "messageId": "765", "endLine": 19, "endColumn": 24}, {"ruleId": "919", "severity": 1, "message": "920", "line": 73, "column": 111, "nodeType": "921", "messageId": "922", "endLine": 73, "endColumn": 112, "suggestions": "923"}, {"ruleId": "919", "severity": 1, "message": "920", "line": 95, "column": 89, "nodeType": "921", "messageId": "922", "endLine": 95, "endColumn": 90, "suggestions": "924"}, {"ruleId": "762", "severity": 1, "message": "873", "line": 6, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 6, "endColumn": 10}, {"ruleId": "762", "severity": 1, "message": "772", "line": 7, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 7, "endColumn": 6}, {"ruleId": "762", "severity": 1, "message": "775", "line": 8, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 8, "endColumn": 13}, {"ruleId": "762", "severity": 1, "message": "925", "line": 9, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 9, "endColumn": 10}, {"ruleId": "779", "severity": 1, "message": "926", "line": 77, "column": 6, "nodeType": "781", "endLine": 77, "endColumn": 14, "suggestions": "927"}, {"ruleId": "779", "severity": 1, "message": "928", "line": 30, "column": 6, "nodeType": "781", "endLine": 30, "endColumn": 16, "suggestions": "929"}, {"ruleId": "762", "severity": 1, "message": "930", "line": 166, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 166, "endColumn": 21}, {"ruleId": "779", "severity": 1, "message": "931", "line": 14, "column": 6, "nodeType": "781", "endLine": 14, "endColumn": 21, "suggestions": "932"}, {"ruleId": "762", "severity": 1, "message": "933", "line": 23, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 23, "endColumn": 27}, {"ruleId": "762", "severity": 1, "message": "934", "line": 10, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 10, "endColumn": 10}, {"ruleId": "762", "severity": 1, "message": "935", "line": 56, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 56, "endColumn": 22}, {"ruleId": "762", "severity": 1, "message": "818", "line": 11, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 11, "endColumn": 8}, {"ruleId": "762", "severity": 1, "message": "936", "line": 12, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 12, "endColumn": 10}, {"ruleId": "762", "severity": 1, "message": "937", "line": 13, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 13, "endColumn": 9}, {"ruleId": "762", "severity": 1, "message": "938", "line": 1, "column": 38, "nodeType": "764", "messageId": "765", "endLine": 1, "endColumn": 46}, {"ruleId": "762", "severity": 1, "message": "939", "line": 112, "column": 23, "nodeType": "764", "messageId": "765", "endLine": 112, "endColumn": 34}, {"ruleId": "762", "severity": 1, "message": "911", "line": 7, "column": 11, "nodeType": "764", "messageId": "765", "endLine": 7, "endColumn": 15}, {"ruleId": "762", "severity": 1, "message": "882", "line": 2, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 2, "endColumn": 16}, {"ruleId": "940", "severity": 1, "message": "941", "line": 69, "column": 3, "nodeType": "942", "messageId": "943", "endLine": 90, "endColumn": 5}, {"ruleId": "762", "severity": 1, "message": "944", "line": 198, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 198, "endColumn": 22}, {"ruleId": "762", "severity": 1, "message": "945", "line": 20, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 20, "endColumn": 8}, {"ruleId": "762", "severity": 1, "message": "946", "line": 21, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 21, "endColumn": 11}, {"ruleId": "762", "severity": 1, "message": "865", "line": 22, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 22, "endColumn": 11}, {"ruleId": "779", "severity": 1, "message": "947", "line": 95, "column": 6, "nodeType": "781", "endLine": 95, "endColumn": 15, "suggestions": "948"}, {"ruleId": "762", "severity": 1, "message": "949", "line": 128, "column": 5, "nodeType": "764", "messageId": "765", "endLine": 128, "endColumn": 14}, {"ruleId": "762", "severity": 1, "message": "882", "line": 2, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 2, "endColumn": 16}, {"ruleId": "762", "severity": 1, "message": "846", "line": 2, "column": 18, "nodeType": "764", "messageId": "765", "endLine": 2, "endColumn": 33}, {"ruleId": "762", "severity": 1, "message": "950", "line": 15, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 15, "endColumn": 21}, {"ruleId": "762", "severity": 1, "message": "951", "line": 22, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 22, "endColumn": 21}, {"ruleId": "762", "severity": 1, "message": "952", "line": 66, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 66, "endColumn": 22}, {"ruleId": "762", "severity": 1, "message": "953", "line": 86, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 86, "endColumn": 24}, {"ruleId": "779", "severity": 1, "message": "954", "line": 126, "column": 6, "nodeType": "781", "endLine": 126, "endColumn": 32, "suggestions": "955", "suppressions": "956"}, {"ruleId": "762", "severity": 1, "message": "805", "line": 7, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 7, "endColumn": 19}, {"ruleId": "762", "severity": 1, "message": "957", "line": 6, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 6, "endColumn": 24}, {"ruleId": "762", "severity": 1, "message": "958", "line": 7, "column": 8, "nodeType": "764", "messageId": "765", "endLine": 7, "endColumn": 21}, {"ruleId": "762", "severity": 1, "message": "959", "line": 9, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 9, "endColumn": 17}, {"ruleId": "762", "severity": 1, "message": "827", "line": 21, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 21, "endColumn": 29}, {"ruleId": "762", "severity": 1, "message": "960", "line": 22, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 22, "endColumn": 22}, {"ruleId": "762", "severity": 1, "message": "961", "line": 68, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 68, "endColumn": 35}, {"ruleId": "762", "severity": 1, "message": "962", "line": 78, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 78, "endColumn": 23}, {"ruleId": "762", "severity": 1, "message": "963", "line": 104, "column": 13, "nodeType": "764", "messageId": "765", "endLine": 104, "endColumn": 26}, {"ruleId": "762", "severity": 1, "message": "909", "line": 12, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 12, "endColumn": 10}, {"ruleId": "762", "severity": 1, "message": "775", "line": 14, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 14, "endColumn": 13}, {"ruleId": "762", "severity": 1, "message": "964", "line": 62, "column": 9, "nodeType": "764", "messageId": "765", "endLine": 62, "endColumn": 32}, {"ruleId": "762", "severity": 1, "message": "965", "line": 5, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 5, "endColumn": 10}, {"ruleId": "762", "severity": 1, "message": "838", "line": 12, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 12, "endColumn": 10}, {"ruleId": "762", "severity": 1, "message": "966", "line": 13, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 13, "endColumn": 12}, {"ruleId": "860", "severity": 1, "message": "967", "line": 235, "column": 3, "nodeType": "862", "messageId": "863", "endLine": 235, "endColumn": 17}, {"ruleId": "762", "severity": 1, "message": "801", "line": 23, "column": 11, "nodeType": "764", "messageId": "765", "endLine": 23, "endColumn": 12}, {"ruleId": "762", "severity": 1, "message": "968", "line": 39, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 39, "endColumn": 20}, {"ruleId": "762", "severity": 1, "message": "945", "line": 8, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 8, "endColumn": 8}, {"ruleId": "762", "severity": 1, "message": "969", "line": 10, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 10, "endColumn": 9}, {"ruleId": "762", "severity": 1, "message": "946", "line": 12, "column": 3, "nodeType": "764", "messageId": "765", "endLine": 12, "endColumn": 11}, {"ruleId": "762", "severity": 1, "message": "970", "line": 3, "column": 10, "nodeType": "764", "messageId": "765", "endLine": 3, "endColumn": 17}, {"ruleId": "762", "severity": 1, "message": "801", "line": 25, "column": 11, "nodeType": "764", "messageId": "765", "endLine": 25, "endColumn": 12}, "no-unused-vars", "'startTransition' is defined but never used.", "Identifier", "unusedVar", "'HideLoading' is defined but never used.", "'ShowLoading' is defined but never used.", "'AdminNavigation' is defined but never used.", "'TbHome' is defined but never used.", "'TbBrandTanzania' is defined but never used.", "'TbMenu2' is defined but never used.", "'TbX' is defined but never used.", "'TbChevronDown' is defined but never used.", "'TbLogout' is defined but never used.", "'TbSettings' is defined but never used.", "'TbBell' is defined but never used.", "'TbStar' is defined but never used.", "'OnlineStatusIndicator' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'dispatch', 'getUserData', 'navigate', and 'user'. Either include them or remove the dependency array.", "ArrayExpression", ["971"], "React Hook useEffect has missing dependencies: 'dispatch' and 'verifyPaymentStatus'. Either include them or remove the dependency array.", ["972"], "React Hook useEffect has a missing dependency: 'verifyPaymentStatus'. Either include it or remove the dependency array.", ["973"], "'getButtonClass' is assigned a value but never used.", "'Select' is defined but never used.", "React Hook useEffect has missing dependencies: 'getExamData' and 'params.id'. Either include them or remove the dependency array.", ["974"], "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["975"], ["976"], "React Hook useCallback has missing dependencies: 'quiz.category', 'quiz.name', 'quiz.passingMarks', 'quiz.passingPercentage', 'quiz.subject', and 'submitting'. Either include them or remove the dependency array.", ["977"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'TbBrain' is defined but never used.", "'TbBolt' is defined but never used.", "'t' is assigned a value but never used.", "'getClassName' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'getUserResults' and 'user'. Either include them or remove the dependency array.", ["978"], "'formatTime' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getExamsData'. Either include it or remove the dependency array.", ["979"], "'Input' is defined but never used.", "'Form' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchQuestions'. Either include it or remove the dependency array.", ["980"], "'TbDashboard' is defined but never used.", "'navigate' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'getData' and 'pagination'. Either include them or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["981"], "'TbTarget' is defined but never used.", "'TbClock' is defined but never used.", "'TbEye' is defined but never used.", "'loading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.", ["982"], "'PageTitle' is defined but never used.", "'TbPlus' is defined but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getUsersData'. Either include it or remove the dependency array.", ["983"], "'processingStartTime' is assigned a value but never used.", "'dispatch' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPlans'. Either include it or remove the dependency array.", ["984"], "React Hook useEffect has a missing dependency: 'isSubscriptionExpired'. Either include it or remove the dependency array.", ["985"], "'Suspense' is defined but never used.", "'isAdmin' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'dispatch' and 'getUserData'. Either include them or remove the dependency array.", ["986"], "'TbCalendar' is defined but never used.", "'TbAward' is defined but never used.", "'TbDownload' is defined but never used.", "'viewMode' is assigned a value but never used.", "'setViewMode' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getData'. Either include it or remove the dependency array.", ["987"], "React Hook useEffect has a missing dependency: 'applyFilters'. Either include it or remove the dependency array.", ["988"], "'AnimatePresence' is defined but never used.", "'getUserRanking' is defined but never used.", "'showStats' is assigned a value but never used.", "'setShowStats' is assigned a value but never used.", "'animationPhase' is assigned a value but never used.", "'currentUserLeague' is assigned a value but never used.", "'showLeagueView' is assigned a value but never used.", "'headerRef' is assigned a value but never used.", "'currentUserRef' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchFullUserData', 'fetchRankingData', 'motivationalQuotes', 'rankingData', and 'user'. Either include them or remove the dependency array.", ["989"], "'otherPerformers' is assigned a value but never used.", "'getSubscriptionBadge' is assigned a value but never used.", "'leagueKey' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'y'.", "ObjectExpression", "unexpected", "'FaChevronDown' is defined but never used.", "'FaSearch' is defined but never used.", "'TbSearch' is defined but never used.", "'TbFilter' is defined but never used.", "'TbSortAscending' is defined but never used.", "'TbUser' is defined but never used.", "'TbChevronUp' is defined but never used.", "'TbAlertTriangle' is defined but never used.", "'TbInfoCircle' is defined but never used.", "'TbCheck' is defined but never used.", "The 'allPossibleClasses' conditional could make the dependencies of useCallback Hook (at line 124) change on every render. Move it inside the useCallback callback. Alternatively, wrap the initialization of 'allPossibleClasses' in its own useMemo() Hook.", "VariableDeclarator", "React Hook useMemo has an unnecessary dependency: 'activeTab'. Either exclude it or remove the dependency array.", ["990"], "'extractUserResultData' is defined but never used.", "'safeNumber' is defined but never used.", "React Hook useCallback has a missing dependency: 'startTime'. Either include it or remove the dependency array.", ["991"], "'motion' is defined but never used.", "'FaExpand' is assigned a value but never used.", "'FaCompress' is assigned a value but never used.", "'showComments' is assigned a value but never used.", "'setShowComments' is assigned a value but never used.", "'handleClearAll' is assigned a value but never used.", "no-undef", "'selectedVideo' is not defined.", "undef", "'FaHome' is defined but never used.", "'FaCreditCard' is defined but never used.", "'FaRobot' is defined but never used.", "'FaSignOutAlt' is defined but never used.", "'handleLogout' is assigned a value but never used.", "Parsing error: Identifier 'dispatch' has already been declared. (64:8)", "'Button' is defined but never used.", "'userRanking' is assigned a value but never used.", "'imagePreview' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getUserStats'. Either include it or remove the dependency array.", ["992"], "React Hook useEffect has a missing dependency: 'getUserData'. Either include it or remove the dependency array.", ["993"], "'handleLevelChangeCancel' is assigned a value but never used.", "'handleImageUpload' is assigned a value but never used.", ["994"], "React Hook useEffect has a missing dependency: 'fetchUserRankingData'. Either include it or remove the dependency array.", ["995"], "'TbRobot' is defined but never used.", "'TbCreditCard' is defined but never used.", "'user' is assigned a value but never used.", "react/jsx-no-duplicate-props", "No duplicate props allowed", "JSXAttribute", "noDuplicateProps", "React Hook useEffect has a missing dependency: 'clearChat'. Either include it or remove the dependency array.", ["996"], "'restoredLines' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\[.", "Literal", "unnecessaryEscape", ["997", "998"], ["999", "1000"], "'TbTrash' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchNotifications' and 'notifications.length'. Either include them or remove the dependency array.", ["1001"], "React Hook useEffect has a missing dependency: 'handleSubmitQuiz'. Either include it or remove the dependency array.", ["1002"], "'goToQuestion' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchTrialQuiz'. Either include it or remove the dependency array.", ["1003"], "'animationComplete' is assigned a value but never used.", "'TbUsers' is defined but never used.", "'getTimerColor' is assigned a value but never used.", "'TbPhoto' is defined but never used.", "'TbEdit' is defined but never used.", "'Fragment' is defined but never used.", "'toggleTheme' is assigned a value but never used.", "no-unreachable", "Unreachable code.", "ReturnStatement", "unreachableCode", "'containerRef' is assigned a value but never used.", "'FaEye' is defined but never used.", "'FaFilter' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchMaterials'. Either include it or remove the dependency array.", ["1004"], "'xpAwarded' is assigned a value but never used.", "'levelOptions' is assigned a value but never used.", "'classOptions' is assigned a value but never used.", "'modalVariants' is assigned a value but never used.", "'overlayVariants' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'renderPDF'. Either include it or remove the dependency array.", ["1005"], ["1006"], "'updateUserInfo' is defined but never used.", "'axiosInstance' is defined but never used.", "'SetUser' is defined but never used.", "'showTryAgain' is assigned a value but never used.", "'handleCloseProcessingModal' is assigned a value but never used.", "'handleTryAgain' is assigned a value but never used.", "'tryAgainTimer' is assigned a value but never used.", "'triggerLevelUpAnimation' is assigned a value but never used.", "'TbMedal' is defined but never used.", "'TbDiamond' is defined but never used.", "Duplicate key 'studyMaterials'.", "'videoError' is assigned a value but never used.", "'FaStar' is defined but never used.", "'message' is defined but never used.", {"desc": "1007", "fix": "1008"}, {"desc": "1009", "fix": "1010"}, {"desc": "1011", "fix": "1012"}, {"desc": "1013", "fix": "1014"}, {"desc": "1015", "fix": "1016"}, {"kind": "1017", "justification": "1018"}, {"desc": "1019", "fix": "1020"}, {"desc": "1021", "fix": "1022"}, {"desc": "1023", "fix": "1024"}, {"desc": "1025", "fix": "1026"}, {"desc": "1027", "fix": "1028"}, {"desc": "1029", "fix": "1030"}, {"desc": "1031", "fix": "1032"}, {"desc": "1033", "fix": "1034"}, {"desc": "1035", "fix": "1036"}, {"desc": "1037", "fix": "1038"}, {"desc": "1039", "fix": "1040"}, {"desc": "1041", "fix": "1042"}, {"desc": "1043", "fix": "1044"}, {"desc": "1045", "fix": "1046"}, {"desc": "1047", "fix": "1048"}, {"desc": "1049", "fix": "1050"}, {"desc": "1051", "fix": "1052"}, {"desc": "1051", "fix": "1053"}, {"desc": "1054", "fix": "1055"}, {"desc": "1056", "fix": "1057"}, {"messageId": "1058", "fix": "1059", "desc": "1060"}, {"messageId": "1061", "fix": "1062", "desc": "1063"}, {"messageId": "1058", "fix": "1064", "desc": "1060"}, {"messageId": "1061", "fix": "1065", "desc": "1063"}, {"desc": "1066", "fix": "1067"}, {"desc": "1068", "fix": "1069"}, {"desc": "1070", "fix": "1071"}, {"desc": "1072", "fix": "1073"}, {"desc": "1074", "fix": "1075"}, {"kind": "1017", "justification": "1018"}, "Update the dependencies array to be: [dispatch, getUserData, navigate, user]", {"range": "1076", "text": "1077"}, "Update the dependencies array to be: [dispatch, paymentVerificationNeeded, user, verifyPaymentStatus]", {"range": "1078", "text": "1079"}, "Update the dependencies array to be: [user, activeRoute, verifyPaymentStatus]", {"range": "1080", "text": "1081"}, "Update the dependencies array to be: [getExamData, params.id]", {"range": "1082", "text": "1083"}, "Update the dependencies array to be: [fetchUsers]", {"range": "1084", "text": "1085"}, "directive", "", "Update the dependencies array to be: [submitting, user, startTime, questions, quiz.passingMarks, quiz.passingPercentage, quiz.name, quiz.subject, quiz.category, id, navigate, answers]", {"range": "1086", "text": "1087"}, "Update the dependencies array to be: [getUserResults, user]", {"range": "1088", "text": "1089"}, "Update the dependencies array to be: [getExamsData]", {"range": "1090", "text": "1091"}, "Update the dependencies array to be: [fetchQuestions]", {"range": "1092", "text": "1093"}, "Update the dependencies array to be: [filters, getData, pagination]", {"range": "1094", "text": "1095"}, "Update the dependencies array to be: [fetchDashboardData]", {"range": "1096", "text": "1097"}, "Update the dependencies array to be: [getUsersData]", {"range": "1098", "text": "1099"}, "Update the dependencies array to be: [fetchPlans]", {"range": "1100", "text": "1101"}, "Update the dependencies array to be: [isSubscriptionExpired, subscriptionData]", {"range": "1102", "text": "1103"}, "Update the dependencies array to be: [dispatch, getUserData]", {"range": "1104", "text": "1105"}, "Update the dependencies array to be: [getData]", {"range": "1106", "text": "1107"}, "Update the dependencies array to be: [filterSubject, filterVerdict, dateRange, reportsData, applyFilters]", {"range": "1108", "text": "1109"}, "Update the dependencies array to be: [fetchFullUserData, fetchRankingData, motivationalQuotes, rankingData, user]", {"range": "1110", "text": "1111"}, "Update the dependencies array to be: [materials, searchTerm, sortBy]", {"range": "1112", "text": "1113"}, "Update the dependencies array to be: [user, dispatch, questions, startTime, examData?.duration, examData.passingMarks, params.id, navigate, selectedOptions]", {"range": "1114", "text": "1115"}, "Update the dependencies array to be: [getUserStats, rankingData, userDetails]", {"range": "1116", "text": "1117"}, "Update the dependencies array to be: [getUserData]", {"range": "1118", "text": "1119"}, {"range": "1120", "text": "1119"}, "Update the dependencies array to be: [fetchUserRankingData, userDetails]", {"range": "1121", "text": "1122"}, "Update the dependencies array to be: [clearChat]", {"range": "1123", "text": "1124"}, "removeEscape", {"range": "1125", "text": "1018"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "1126", "text": "1127"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "1128", "text": "1018"}, {"range": "1129", "text": "1127"}, "Update the dependencies array to be: [fetchNotifications, isOpen, notifications.length]", {"range": "1130", "text": "1131"}, "Update the dependencies array to be: [handleSubmitQuiz, timeLeft]", {"range": "1132", "text": "1133"}, "Update the dependencies array to be: [fetchTrialQuiz, trialUserInfo]", {"range": "1134", "text": "1135"}, "Update the dependencies array to be: [fetchMaterials, filters]", {"range": "1136", "text": "1137"}, "Update the dependencies array to be: [modalIsOpen, documentUrl, renderPDF]", {"range": "1138", "text": "1139"}, [4456, 4458], "[dispatch, getUserData, navigate, user]", [7410, 7443], "[dispatch, paymentVerificationNeeded, user, verifyPaymentStatus]", [7953, 7972], "[user, activeRoute, verifyPaymentStatus]", [3399, 3401], "[getExamData, params.id]", [1327, 1329], "[fetchUsers]", [11677, 11728], "[submitting, user, startTime, questions, quiz.passingMarks, quiz.passingPercentage, quiz.name, quiz.subject, quiz.category, id, navigate, answers]", [9239, 9241], "[getUser<PERSON><PERSON><PERSON><PERSON>, user]", [5318, 5320], "[getExamsData]", [3074, 3076], "[fetchQuestions]", [7040, 7069], "[filters, getData, pagination]", [1196, 1198], "[fetchDashboardData]", [5587, 5589], "[getUsersData]", [3149, 3151], "[fetchPlans]", [4869, 4887], "[isSubscriptionExpired, subscriptionData]", [1990, 1992], "[dispatch, getUserData]", [4129, 4131], "[getData]", [4184, 4238], "[filterSubject, filterVerdict, dateRange, reportsData, applyFilters]", [34517, 34519], "[fetchFullUserData, fetchRankingData, motivationalQuotes, rankingData, user]", [8760, 8802], "[materials, searchTerm, sortBy]", [28864, 28939], "[user, dispatch, questions, startTime, examData?.duration, examData.passingMarks, params.id, navigate, selectedOptions]", [3855, 3881], "[getUserStats, rankingData, userDetails]", [4823, 4825], "[getUserData]", [10390, 10392], [10542, 10555], "[fetchUserRankingData, userDetails]", [1586, 1588], "[clearChat]", [3500, 3501], [3500, 3500], "\\", [5011, 5012], [5011, 5011], [2302, 2310], "[fetchNotifications, isOpen, notifications.length]", [1032, 1042], "[handleSubmitQuiz, timeLeft]", [527, 542], "[fetchTrialQuiz, trialUserInfo]", [2411, 2420], "[fetchMaterials, filters]", [3978, 4004], "[modalIsOpen, documentUrl, renderPDF]"]