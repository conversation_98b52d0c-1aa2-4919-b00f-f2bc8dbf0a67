[{"C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\store.js": "4", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ProtectedRoute.js": "5", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\Loader.js": "6", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminProtectedRoute.js": "7", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\contexts\\ThemeContext.js": "8", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\RankingErrorBoundary.js": "9", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\DebugAuth.jsx": "10", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\RankingDemo.js": "11", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\loaderSlice.js": "12", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\usersSlice.js": "13", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\subscriptionSlice.js": "14", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\trial\\TrialPage.js": "15", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\paymentSlice.js": "16", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditExam.js": "17", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Notifications\\AdminNotifications.jsx": "18", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizPlay.js": "19", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizResult.js": "20", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\index.js": "21", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\index.js": "22", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\index.js": "23", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Forum\\index.js": "24", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AdminReports\\index.js": "25", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Dashboard\\index.js": "26", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\index.js": "27", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Users\\index.js": "28", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Subscription\\index.js": "29", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Test\\index.js": "30", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\AboutUs\\index.js": "31", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\UserReports\\index.js": "32", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Ranking\\index.js": "33", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\index.js": "34", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\index.js": "35", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\VideoLessons\\index.js": "36", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Hub\\index.js": "37", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Home\\index.js": "38", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Register\\index.js": "39", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Login\\index.js": "40", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Forum\\index.js": "41", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Profile\\index.js": "42", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\payment.js": "43", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ModernSidebar.js": "44", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminNavigation.js": "45", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\FloatingBrainwaveAI.js": "46", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\users.js": "47", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\syllabus.js": "48", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\aiQuestions.js": "49", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\notifications.js": "50", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\PageTitle.js": "51", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\exams.js": "52", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reports.js": "53", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\chat.js": "54", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\UserRankingList.js": "55", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ContentRenderer.js": "56", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\NotificationBell.js": "57", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\UserRankingCard.js": "58", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\ProfilePicture.js": "59", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\OnlineStatusIndicator.js": "60", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialQuizPlay.js": "61", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialQuizSelection.js": "62", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialQuizResult.js": "63", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Button.js": "64", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Loading.js": "65", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Card.js": "66", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialRegistrationPrompt.js": "67", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Input.js": "68", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizTimer.js": "69", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\LazyImage.js": "70", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizCard.js": "71", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizQuestion.js": "72", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\forum.js": "73", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\data\\Subjects.jsx": "74", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ThemeToggle.js": "75", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminLayout.js": "76", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminCard.js": "77", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditQuestion.js": "78", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ErrorBoundary.js": "79", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ResponsiveContainer.js": "80", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\PerformanceMonitor.js": "81", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\SubtitleManager.js": "82", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\AddStudyMaterialForm.js": "83", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\plans.js": "84", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reviews.js": "85", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\EditStudyMaterialForm.js": "86", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\StudyMaterialManager.js": "87", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\study.js": "88", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\utils\\quizDataUtils.js": "89", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\XPResultDisplay.js": "90", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\SubscriptionExpiredModal\\SubscriptionExpiredModal.jsx": "91", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\TryForFreeModal.js": "92", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\UpgradeRestrictionModal\\UpgradeRestrictionModal.jsx": "93", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\PDFModal.js": "94", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\Instructions.js": "95", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\index.js": "96", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\SubscriptionModal\\SubscriptionModal.jsx": "97", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminTopNavigation.js": "98", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\trial.js": "99", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\subtitles.js": "100", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\XPProgressBar.js": "101", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\AchievementBadge.js": "102", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\EnhancedAchievementBadge.js": "103", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\LevelBadge.js": "104", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\contexts\\LanguageContext.js": "105", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\localization\\kiswahili.js": "106", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Skills\\index.js": "107", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\skills.js": "108", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Skills\\index.js": "109"}, {"size": 395, "mtime": 1696247250000, "results": "110", "hashOfConfig": "111"}, {"size": 13439, "mtime": 1752343277235, "results": "112", "hashOfConfig": "111"}, {"size": 362, "mtime": 1696247250000, "results": "113", "hashOfConfig": "111"}, {"size": 430, "mtime": 1736735017645, "results": "114", "hashOfConfig": "111"}, {"size": 20340, "mtime": 1752145561052, "results": "115", "hashOfConfig": "111"}, {"size": 180, "mtime": 1696247250000, "results": "116", "hashOfConfig": "111"}, {"size": 1601, "mtime": 1751575879221, "results": "117", "hashOfConfig": "111"}, {"size": 1410, "mtime": 1751140352157, "results": "118", "hashOfConfig": "111"}, {"size": 3109, "mtime": 1751260973778, "results": "119", "hashOfConfig": "111"}, {"size": 9770, "mtime": 1751495320007, "results": "120", "hashOfConfig": "111"}, {"size": 12711, "mtime": 1751260271529, "results": "121", "hashOfConfig": "111"}, {"size": 416, "mtime": 1696247250000, "results": "122", "hashOfConfig": "111"}, {"size": 334, "mtime": 1696247250000, "results": "123", "hashOfConfig": "111"}, {"size": 404, "mtime": 1736731932223, "results": "124", "hashOfConfig": "111"}, {"size": 6016, "mtime": 1751638273351, "results": "125", "hashOfConfig": "111"}, {"size": 449, "mtime": 1736732007232, "results": "126", "hashOfConfig": "111"}, {"size": 15139, "mtime": 1751582824879, "results": "127", "hashOfConfig": "111"}, {"size": 10989, "mtime": 1751586090664, "results": "128", "hashOfConfig": "111"}, {"size": 37700, "mtime": 1752198101933, "results": "129", "hashOfConfig": "111"}, {"size": 48118, "mtime": 1752336893747, "results": "130", "hashOfConfig": "111"}, {"size": 1140, "mtime": 1751426583568, "results": "131", "hashOfConfig": "111"}, {"size": 33135, "mtime": 1752338943014, "results": "132", "hashOfConfig": "111"}, {"size": 10464, "mtime": 1752187297118, "results": "133", "hashOfConfig": "111"}, {"size": 12434, "mtime": 1751869385895, "results": "134", "hashOfConfig": "111"}, {"size": 16063, "mtime": 1751870755172, "results": "135", "hashOfConfig": "111"}, {"size": 12035, "mtime": 1751869525840, "results": "136", "hashOfConfig": "111"}, {"size": 8883, "mtime": 1751585554937, "results": "137", "hashOfConfig": "111"}, {"size": 22154, "mtime": 1751585459342, "results": "138", "hashOfConfig": "111"}, {"size": 45883, "mtime": 1752135725326, "results": "139", "hashOfConfig": "111"}, {"size": 1327, "mtime": 1709427669270, "results": "140", "hashOfConfig": "111"}, {"size": 8089, "mtime": 1740446459586, "results": "141", "hashOfConfig": "111"}, {"size": 27236, "mtime": 1751873126836, "results": "142", "hashOfConfig": "111"}, {"size": 136450, "mtime": 1752148102332, "results": "143", "hashOfConfig": "111"}, {"size": 22689, "mtime": 1752338620979, "results": "144", "hashOfConfig": "111"}, {"size": 48284, "mtime": 1752336822163, "results": "145", "hashOfConfig": "111"}, {"size": 40208, "mtime": 1752339499543, "results": "146", "hashOfConfig": "111"}, {"size": 7870, "mtime": 1752338665955, "results": "147", "hashOfConfig": "111"}, {"size": 68001, "mtime": 1752145575965, "results": "148", "hashOfConfig": "111"}, {"size": 9971, "mtime": 1752333866970, "results": "149", "hashOfConfig": "111"}, {"size": 4378, "mtime": 1752193825539, "results": "150", "hashOfConfig": "111"}, {"size": 31275, "mtime": 1752186104888, "results": "151", "hashOfConfig": "111"}, {"size": 47852, "mtime": 1752339297891, "results": "152", "hashOfConfig": "111"}, {"size": 2046, "mtime": 1752102606942, "results": "153", "hashOfConfig": "111"}, {"size": 13836, "mtime": 1752343174543, "results": "154", "hashOfConfig": "111"}, {"size": 15668, "mtime": 1752343935697, "results": "155", "hashOfConfig": "111"}, {"size": 24116, "mtime": 1752336667751, "results": "156", "hashOfConfig": "111"}, {"size": 3191, "mtime": 1751940514617, "results": "157", "hashOfConfig": "111"}, {"size": 7315, "mtime": 1751495843287, "results": "158", "hashOfConfig": "111"}, {"size": 6337, "mtime": 1751558223480, "results": "159", "hashOfConfig": "111"}, {"size": 3632, "mtime": 1751487806125, "results": "160", "hashOfConfig": "111"}, {"size": 388, "mtime": 1703845955779, "results": "161", "hashOfConfig": "111"}, {"size": 2455, "mtime": 1751479784424, "results": "162", "hashOfConfig": "111"}, {"size": 3391, "mtime": 1751304153158, "results": "163", "hashOfConfig": "111"}, {"size": 1104, "mtime": 1749936905424, "results": "164", "hashOfConfig": "111"}, {"size": 29870, "mtime": 1752145669266, "results": "165", "hashOfConfig": "111"}, {"size": 5595, "mtime": 1751164672302, "results": "166", "hashOfConfig": "111"}, {"size": 11831, "mtime": 1752096169929, "results": "167", "hashOfConfig": "111"}, {"size": 18256, "mtime": 1751482855935, "results": "168", "hashOfConfig": "111"}, {"size": 6382, "mtime": 1752146723210, "results": "169", "hashOfConfig": "111"}, {"size": 3307, "mtime": 1751855844189, "results": "170", "hashOfConfig": "111"}, {"size": 24195, "mtime": 1752195775706, "results": "171", "hashOfConfig": "111"}, {"size": 10009, "mtime": 1751649332583, "results": "172", "hashOfConfig": "111"}, {"size": 27796, "mtime": 1751916780076, "results": "173", "hashOfConfig": "111"}, {"size": 2913, "mtime": 1751140370241, "results": "174", "hashOfConfig": "111"}, {"size": 3119, "mtime": 1751164996340, "results": "175", "hashOfConfig": "111"}, {"size": 1857, "mtime": 1751140385464, "results": "176", "hashOfConfig": "111"}, {"size": 10040, "mtime": 1751638250072, "results": "177", "hashOfConfig": "111"}, {"size": 2324, "mtime": 1751140401815, "results": "178", "hashOfConfig": "111"}, {"size": 7165, "mtime": 1752195834207, "results": "179", "hashOfConfig": "111"}, {"size": 2504, "mtime": 1751957740575, "results": "180", "hashOfConfig": "111"}, {"size": 17286, "mtime": 1752193750030, "results": "181", "hashOfConfig": "111"}, {"size": 13299, "mtime": 1751249005755, "results": "182", "hashOfConfig": "111"}, {"size": 1787, "mtime": 1734985908268, "results": "183", "hashOfConfig": "111"}, {"size": 1245, "mtime": 1752333815446, "results": "184", "hashOfConfig": "111"}, {"size": 3904, "mtime": 1751143777976, "results": "185", "hashOfConfig": "111"}, {"size": 2200, "mtime": 1751563008113, "results": "186", "hashOfConfig": "111"}, {"size": 1717, "mtime": 1751561083661, "results": "187", "hashOfConfig": "111"}, {"size": 12864, "mtime": 1751134045332, "results": "188", "hashOfConfig": "111"}, {"size": 5088, "mtime": 1751143254906, "results": "189", "hashOfConfig": "111"}, {"size": 4989, "mtime": 1751143312418, "results": "190", "hashOfConfig": "111"}, {"size": 6304, "mtime": 1751188593099, "results": "191", "hashOfConfig": "111"}, {"size": 9494, "mtime": 1750995979612, "results": "192", "hashOfConfig": "111"}, {"size": 29255, "mtime": 1752333961995, "results": "193", "hashOfConfig": "111"}, {"size": 279, "mtime": 1736719733927, "results": "194", "hashOfConfig": "111"}, {"size": 578, "mtime": 1705434185826, "results": "195", "hashOfConfig": "111"}, {"size": 17375, "mtime": 1751000106093, "results": "196", "hashOfConfig": "111"}, {"size": 11161, "mtime": 1750999560542, "results": "197", "hashOfConfig": "111"}, {"size": 6669, "mtime": 1750999504134, "results": "198", "hashOfConfig": "111"}, {"size": 9114, "mtime": 1751691985112, "results": "199", "hashOfConfig": "111"}, {"size": 16372, "mtime": 1751479340474, "results": "200", "hashOfConfig": "111"}, {"size": 9653, "mtime": 1752084006410, "results": "201", "hashOfConfig": "111"}, {"size": 9006, "mtime": 1752334003537, "results": "202", "hashOfConfig": "111"}, {"size": 6486, "mtime": 1752078868080, "results": "203", "hashOfConfig": "111"}, {"size": 8101, "mtime": 1750963515173, "results": "204", "hashOfConfig": "111"}, {"size": 3835, "mtime": 1751478376207, "results": "205", "hashOfConfig": "111"}, {"size": 3672, "mtime": 1752017325220, "results": "206", "hashOfConfig": "111"}, {"size": 13621, "mtime": 1752105816006, "results": "207", "hashOfConfig": "111"}, {"size": 6575, "mtime": 1752343969641, "results": "208", "hashOfConfig": "111"}, {"size": 1024, "mtime": 1751637471453, "results": "209", "hashOfConfig": "111"}, {"size": 1524, "mtime": 1750994293078, "results": "210", "hashOfConfig": "111"}, {"size": 8429, "mtime": 1751244672688, "results": "211", "hashOfConfig": "111"}, {"size": 11901, "mtime": 1751236424130, "results": "212", "hashOfConfig": "111"}, {"size": 10081, "mtime": 1751244608756, "results": "213", "hashOfConfig": "111"}, {"size": 7685, "mtime": 1751244700154, "results": "214", "hashOfConfig": "111"}, {"size": 3773, "mtime": 1752336238191, "results": "215", "hashOfConfig": "111"}, {"size": 12552, "mtime": 1752343414320, "results": "216", "hashOfConfig": "111"}, {"size": 17717, "mtime": 1752343073846, "results": "217", "hashOfConfig": "111"}, {"size": 2718, "mtime": 1752343803321, "results": "218", "hashOfConfig": "111"}, {"size": 16536, "mtime": 1752343895497, "results": "219", "hashOfConfig": "111"}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ymk59w", {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\store.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ProtectedRoute.js", ["547", "548", "549", "550", "551", "552", "553", "554", "555", "556", "557", "558", "559", "560", "561", "562", "563", "564"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\Loader.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\contexts\\ThemeContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\RankingErrorBoundary.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\DebugAuth.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\RankingDemo.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\loaderSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\usersSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\subscriptionSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\trial\\TrialPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\paymentSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditExam.js", ["565", "566"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Notifications\\AdminNotifications.jsx", [], ["567"], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizPlay.js", ["568"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizResult.js", ["569"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\index.js", ["570", "571", "572", "573", "574", "575", "576", "577", "578"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\index.js", ["579"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Forum\\index.js", ["580", "581", "582"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AdminReports\\index.js", ["583", "584", "585"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Dashboard\\index.js", ["586", "587", "588", "589", "590"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Users\\index.js", ["591", "592", "593", "594"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Subscription\\index.js", ["595", "596", "597", "598", "599", "600"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Test\\index.js", ["601", "602"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\AboutUs\\index.js", ["603"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\UserReports\\index.js", ["604", "605", "606", "607", "608", "609", "610", "611"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Ranking\\index.js", ["612", "613", "614", "615", "616", "617", "618", "619", "620", "621", "622", "623", "624", "625", "626", "627", "628"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\index.js", ["629", "630", "631", "632", "633", "634", "635", "636", "637", "638", "639", "640", "641", "642", "643", "644", "645"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\index.js", ["646", "647", "648"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\VideoLessons\\index.js", ["649", "650", "651", "652", "653", "654", "655", "656", "657"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Hub\\index.js", ["658", "659", "660", "661", "662", "663"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Home\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Register\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Login\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Forum\\index.js", ["664", "665", "666", "667", "668", "669", "670", "671", "672"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Profile\\index.js", ["673", "674", "675", "676", "677", "678", "679", "680", "681", "682", "683", "684", "685", "686"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\payment.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ModernSidebar.js", ["687", "688", "689", "690", "691"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminNavigation.js", ["692", "693", "694"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\FloatingBrainwaveAI.js", ["695", "696", "697"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\users.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\syllabus.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\aiQuestions.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\notifications.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\PageTitle.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\exams.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reports.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\chat.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\UserRankingList.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ContentRenderer.js", ["698", "699", "700"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\NotificationBell.js", ["701", "702", "703", "704", "705"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\UserRankingCard.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\ProfilePicture.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\OnlineStatusIndicator.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialQuizPlay.js", ["706", "707"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialQuizSelection.js", ["708"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialQuizResult.js", ["709"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Button.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Loading.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Card.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialRegistrationPrompt.js", ["710"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Input.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizTimer.js", ["711"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\LazyImage.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizCard.js", ["712", "713", "714"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizQuestion.js", ["715"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\forum.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\data\\Subjects.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ThemeToggle.js", ["716"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminLayout.js", ["717"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminCard.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditQuestion.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ErrorBoundary.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ResponsiveContainer.js", ["718"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\PerformanceMonitor.js", ["719", "720"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\SubtitleManager.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\AddStudyMaterialForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\plans.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reviews.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\EditStudyMaterialForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\StudyMaterialManager.js", ["721", "722", "723", "724"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\study.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\utils\\quizDataUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\XPResultDisplay.js", ["725"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\SubscriptionExpiredModal\\SubscriptionExpiredModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\TryForFreeModal.js", ["726", "727", "728", "729", "730", "731"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\UpgradeRestrictionModal\\UpgradeRestrictionModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\PDFModal.js", [], ["732"], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\Instructions.js", ["733"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\SubscriptionModal\\SubscriptionModal.jsx", ["734", "735", "736", "737", "738", "739", "740", "741"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminTopNavigation.js", ["742", "743"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\trial.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\subtitles.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\XPProgressBar.js", ["744"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\AchievementBadge.js", ["745", "746", "747"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\EnhancedAchievementBadge.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\LevelBadge.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\contexts\\LanguageContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\localization\\kiswahili.js", ["748"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Skills\\index.js", ["749", "750"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\skills.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Skills\\index.js", ["751", "752", "753"], [], {"ruleId": "754", "severity": 1, "message": "755", "line": 2, "column": 46, "nodeType": "756", "messageId": "757", "endLine": 2, "endColumn": 61}, {"ruleId": "754", "severity": 1, "message": "758", "line": 7, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 7, "endColumn": 21}, {"ruleId": "754", "severity": 1, "message": "759", "line": 7, "column": 23, "nodeType": "756", "messageId": "757", "endLine": 7, "endColumn": 34}, {"ruleId": "754", "severity": 1, "message": "760", "line": 12, "column": 8, "nodeType": "756", "messageId": "757", "endLine": 12, "endColumn": 23}, {"ruleId": "754", "severity": 1, "message": "761", "line": 15, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 15, "endColumn": 16}, {"ruleId": "754", "severity": 1, "message": "762", "line": 15, "column": 18, "nodeType": "756", "messageId": "757", "endLine": 15, "endColumn": 33}, {"ruleId": "754", "severity": 1, "message": "763", "line": 15, "column": 35, "nodeType": "756", "messageId": "757", "endLine": 15, "endColumn": 42}, {"ruleId": "754", "severity": 1, "message": "764", "line": 15, "column": 44, "nodeType": "756", "messageId": "757", "endLine": 15, "endColumn": 47}, {"ruleId": "754", "severity": 1, "message": "765", "line": 15, "column": 49, "nodeType": "756", "messageId": "757", "endLine": 15, "endColumn": 62}, {"ruleId": "754", "severity": 1, "message": "766", "line": 15, "column": 64, "nodeType": "756", "messageId": "757", "endLine": 15, "endColumn": 72}, {"ruleId": "754", "severity": 1, "message": "767", "line": 15, "column": 82, "nodeType": "756", "messageId": "757", "endLine": 15, "endColumn": 92}, {"ruleId": "754", "severity": 1, "message": "768", "line": 15, "column": 94, "nodeType": "756", "messageId": "757", "endLine": 15, "endColumn": 100}, {"ruleId": "754", "severity": 1, "message": "769", "line": 15, "column": 102, "nodeType": "756", "messageId": "757", "endLine": 15, "endColumn": 108}, {"ruleId": "754", "severity": 1, "message": "770", "line": 16, "column": 8, "nodeType": "756", "messageId": "757", "endLine": 16, "endColumn": 29}, {"ruleId": "771", "severity": 1, "message": "772", "line": 120, "column": 6, "nodeType": "773", "endLine": 120, "endColumn": 8, "suggestions": "774"}, {"ruleId": "771", "severity": 1, "message": "775", "line": 189, "column": 6, "nodeType": "773", "endLine": 189, "endColumn": 39, "suggestions": "776"}, {"ruleId": "771", "severity": 1, "message": "777", "line": 200, "column": 6, "nodeType": "773", "endLine": 200, "endColumn": 25, "suggestions": "778"}, {"ruleId": "754", "severity": 1, "message": "779", "line": 231, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 231, "endColumn": 23}, {"ruleId": "754", "severity": 1, "message": "780", "line": 1, "column": 35, "nodeType": "756", "messageId": "757", "endLine": 1, "endColumn": 41}, {"ruleId": "771", "severity": 1, "message": "781", "line": 110, "column": 6, "nodeType": "773", "endLine": 110, "endColumn": 8, "suggestions": "782"}, {"ruleId": "771", "severity": 1, "message": "783", "line": 46, "column": 6, "nodeType": "773", "endLine": 46, "endColumn": 8, "suggestions": "784", "suppressions": "785"}, {"ruleId": "771", "severity": 1, "message": "786", "line": 322, "column": 6, "nodeType": "773", "endLine": 322, "endColumn": 57, "suggestions": "787"}, {"ruleId": "788", "severity": 1, "message": "789", "line": 1077, "column": 29, "nodeType": "790", "endLine": 1086, "endColumn": 31}, {"ruleId": "754", "severity": 1, "message": "791", "line": 13, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 13, "endColumn": 10}, {"ruleId": "754", "severity": 1, "message": "769", "line": 17, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 17, "endColumn": 9}, {"ruleId": "754", "severity": 1, "message": "761", "line": 18, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 18, "endColumn": 9}, {"ruleId": "754", "severity": 1, "message": "792", "line": 19, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 19, "endColumn": 9}, {"ruleId": "754", "severity": 1, "message": "793", "line": 43, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 43, "endColumn": 12}, {"ruleId": "754", "severity": 1, "message": "794", "line": 43, "column": 27, "nodeType": "756", "messageId": "757", "endLine": 43, "endColumn": 39}, {"ruleId": "771", "severity": 1, "message": "795", "line": 251, "column": 6, "nodeType": "773", "endLine": 251, "endColumn": 8, "suggestions": "796"}, {"ruleId": "754", "severity": 1, "message": "794", "line": 500, "column": 24, "nodeType": "756", "messageId": "757", "endLine": 500, "endColumn": 36}, {"ruleId": "754", "severity": 1, "message": "797", "line": 502, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 502, "endColumn": 19}, {"ruleId": "771", "severity": 1, "message": "798", "line": 191, "column": 6, "nodeType": "773", "endLine": 191, "endColumn": 8, "suggestions": "799"}, {"ruleId": "754", "severity": 1, "message": "800", "line": 2, "column": 27, "nodeType": "756", "messageId": "757", "endLine": 2, "endColumn": 32}, {"ruleId": "754", "severity": 1, "message": "801", "line": 2, "column": 34, "nodeType": "756", "messageId": "757", "endLine": 2, "endColumn": 38}, {"ruleId": "771", "severity": 1, "message": "802", "line": 113, "column": 6, "nodeType": "773", "endLine": 113, "endColumn": 8, "suggestions": "803"}, {"ruleId": "754", "severity": 1, "message": "804", "line": 5, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 5, "endColumn": 14}, {"ruleId": "754", "severity": 1, "message": "805", "line": 31, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 31, "endColumn": 17}, {"ruleId": "771", "severity": 1, "message": "806", "line": 241, "column": 6, "nodeType": "773", "endLine": 241, "endColumn": 35, "suggestions": "807"}, {"ruleId": "754", "severity": 1, "message": "808", "line": 12, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 12, "endColumn": 11}, {"ruleId": "754", "severity": 1, "message": "809", "line": 14, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 14, "endColumn": 10}, {"ruleId": "754", "severity": 1, "message": "810", "line": 16, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 16, "endColumn": 8}, {"ruleId": "754", "severity": 1, "message": "811", "line": 40, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 40, "endColumn": 17}, {"ruleId": "771", "severity": 1, "message": "812", "line": 44, "column": 6, "nodeType": "773", "endLine": 44, "endColumn": 8, "suggestions": "813"}, {"ruleId": "754", "severity": 1, "message": "814", "line": 12, "column": 8, "nodeType": "756", "messageId": "757", "endLine": 12, "endColumn": 17}, {"ruleId": "754", "severity": 1, "message": "815", "line": 31, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 31, "endColumn": 9}, {"ruleId": "754", "severity": 1, "message": "816", "line": 43, "column": 19, "nodeType": "756", "messageId": "757", "endLine": 43, "endColumn": 29}, {"ruleId": "771", "severity": 1, "message": "817", "line": 182, "column": 6, "nodeType": "773", "endLine": 182, "endColumn": 8, "suggestions": "818"}, {"ruleId": "754", "severity": 1, "message": "759", "line": 8, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 8, "endColumn": 21}, {"ruleId": "754", "severity": 1, "message": "758", "line": 8, "column": 23, "nodeType": "756", "messageId": "757", "endLine": 8, "endColumn": 34}, {"ruleId": "754", "severity": 1, "message": "819", "line": 28, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 28, "endColumn": 29}, {"ruleId": "754", "severity": 1, "message": "820", "line": 33, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 33, "endColumn": 17}, {"ruleId": "771", "severity": 1, "message": "821", "line": 85, "column": 6, "nodeType": "773", "endLine": 85, "endColumn": 8, "suggestions": "822"}, {"ruleId": "771", "severity": 1, "message": "823", "line": 139, "column": 6, "nodeType": "773", "endLine": 139, "endColumn": 24, "suggestions": "824"}, {"ruleId": "754", "severity": 1, "message": "825", "line": 1, "column": 38, "nodeType": "756", "messageId": "757", "endLine": 1, "endColumn": 46}, {"ruleId": "754", "severity": 1, "message": "826", "line": 8, "column": 12, "nodeType": "756", "messageId": "757", "endLine": 8, "endColumn": 19}, {"ruleId": "771", "severity": 1, "message": "827", "line": 58, "column": 8, "nodeType": "773", "endLine": 58, "endColumn": 10, "suggestions": "828"}, {"ruleId": "754", "severity": 1, "message": "829", "line": 13, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 13, "endColumn": 13}, {"ruleId": "754", "severity": 1, "message": "809", "line": 14, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 14, "endColumn": 10}, {"ruleId": "754", "severity": 1, "message": "830", "line": 15, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 15, "endColumn": 10}, {"ruleId": "754", "severity": 1, "message": "831", "line": 17, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 17, "endColumn": 13}, {"ruleId": "754", "severity": 1, "message": "832", "line": 35, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 35, "endColumn": 18}, {"ruleId": "754", "severity": 1, "message": "833", "line": 35, "column": 20, "nodeType": "756", "messageId": "757", "endLine": 35, "endColumn": 31}, {"ruleId": "771", "severity": 1, "message": "834", "line": 137, "column": 6, "nodeType": "773", "endLine": 137, "endColumn": 8, "suggestions": "835"}, {"ruleId": "771", "severity": 1, "message": "836", "line": 141, "column": 6, "nodeType": "773", "endLine": 141, "endColumn": 60, "suggestions": "837"}, {"ruleId": "754", "severity": 1, "message": "838", "line": 2, "column": 18, "nodeType": "756", "messageId": "757", "endLine": 2, "endColumn": 33}, {"ruleId": "754", "severity": 1, "message": "839", "line": 21, "column": 53, "nodeType": "756", "messageId": "757", "endLine": 21, "endColumn": 67}, {"ruleId": "754", "severity": 1, "message": "770", "line": 24, "column": 8, "nodeType": "756", "messageId": "757", "endLine": 24, "endColumn": 29}, {"ruleId": "754", "severity": 1, "message": "832", "line": 75, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 75, "endColumn": 18}, {"ruleId": "754", "severity": 1, "message": "833", "line": 75, "column": 20, "nodeType": "756", "messageId": "757", "endLine": 75, "endColumn": 31}, {"ruleId": "754", "severity": 1, "message": "840", "line": 76, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 76, "endColumn": 19}, {"ruleId": "754", "severity": 1, "message": "841", "line": 76, "column": 21, "nodeType": "756", "messageId": "757", "endLine": 76, "endColumn": 33}, {"ruleId": "754", "severity": 1, "message": "842", "line": 77, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 77, "endColumn": 24}, {"ruleId": "754", "severity": 1, "message": "843", "line": 80, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 80, "endColumn": 27}, {"ruleId": "754", "severity": 1, "message": "844", "line": 82, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 82, "endColumn": 24}, {"ruleId": "754", "severity": 1, "message": "845", "line": 90, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 90, "endColumn": 18}, {"ruleId": "754", "severity": 1, "message": "846", "line": 91, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 91, "endColumn": 23}, {"ruleId": "771", "severity": 1, "message": "847", "line": 896, "column": 6, "nodeType": "773", "endLine": 896, "endColumn": 8, "suggestions": "848"}, {"ruleId": "754", "severity": 1, "message": "849", "line": 917, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 917, "endColumn": 24}, {"ruleId": "754", "severity": 1, "message": "850", "line": 1072, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 1072, "endColumn": 29}, {"ruleId": "754", "severity": 1, "message": "851", "line": 1560, "column": 39, "nodeType": "756", "messageId": "757", "endLine": 1560, "endColumn": 48}, {"ruleId": "852", "severity": 1, "message": "853", "line": 2155, "column": 27, "nodeType": "854", "messageId": "855", "endLine": 2155, "endColumn": 28}, {"ruleId": "754", "severity": 1, "message": "856", "line": 17, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 17, "endColumn": 16}, {"ruleId": "754", "severity": 1, "message": "857", "line": 18, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 18, "endColumn": 11}, {"ruleId": "754", "severity": 1, "message": "858", "line": 25, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 25, "endColumn": 11}, {"ruleId": "754", "severity": 1, "message": "859", "line": 26, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 26, "endColumn": 11}, {"ruleId": "754", "severity": 1, "message": "860", "line": 27, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 27, "endColumn": 18}, {"ruleId": "754", "severity": 1, "message": "831", "line": 28, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 28, "endColumn": 13}, {"ruleId": "754", "severity": 1, "message": "810", "line": 29, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 29, "endColumn": 8}, {"ruleId": "754", "severity": 1, "message": "829", "line": 30, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 30, "endColumn": 13}, {"ruleId": "754", "severity": 1, "message": "861", "line": 31, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 31, "endColumn": 9}, {"ruleId": "754", "severity": 1, "message": "862", "line": 33, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 33, "endColumn": 14}, {"ruleId": "754", "severity": 1, "message": "764", "line": 34, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 34, "endColumn": 6}, {"ruleId": "754", "severity": 1, "message": "863", "line": 35, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 35, "endColumn": 18}, {"ruleId": "754", "severity": 1, "message": "864", "line": 36, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 36, "endColumn": 15}, {"ruleId": "754", "severity": 1, "message": "865", "line": 37, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 37, "endColumn": 10}, {"ruleId": "754", "severity": 1, "message": "793", "line": 45, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 45, "endColumn": 12}, {"ruleId": "771", "severity": 1, "message": "866", "line": 68, "column": 9, "nodeType": "867", "endLine": 72, "endColumn": 29}, {"ruleId": "771", "severity": 1, "message": "868", "line": 251, "column": 6, "nodeType": "773", "endLine": 251, "endColumn": 48, "suggestions": "869"}, {"ruleId": "754", "severity": 1, "message": "870", "line": 18, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 18, "endColumn": 31}, {"ruleId": "754", "severity": 1, "message": "871", "line": 18, "column": 33, "nodeType": "756", "messageId": "757", "endLine": 18, "endColumn": 43}, {"ruleId": "771", "severity": 1, "message": "872", "line": 779, "column": 6, "nodeType": "773", "endLine": 779, "endColumn": 81, "suggestions": "873"}, {"ruleId": "754", "severity": 1, "message": "874", "line": 3, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 3, "endColumn": 16}, {"ruleId": "754", "severity": 1, "message": "838", "line": 3, "column": 18, "nodeType": "756", "messageId": "757", "endLine": 3, "endColumn": 33}, {"ruleId": "754", "severity": 1, "message": "875", "line": 32, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 32, "endColumn": 11}, {"ruleId": "754", "severity": 1, "message": "876", "line": 33, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 33, "endColumn": 13}, {"ruleId": "754", "severity": 1, "message": "793", "line": 46, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 46, "endColumn": 12}, {"ruleId": "754", "severity": 1, "message": "794", "line": 46, "column": 27, "nodeType": "756", "messageId": "757", "endLine": 46, "endColumn": 39}, {"ruleId": "754", "severity": 1, "message": "877", "line": 71, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 71, "endColumn": 22}, {"ruleId": "754", "severity": 1, "message": "878", "line": 71, "column": 24, "nodeType": "756", "messageId": "757", "endLine": 71, "endColumn": 39}, {"ruleId": "754", "severity": 1, "message": "879", "line": 293, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 293, "endColumn": 23}, {"ruleId": "754", "severity": 1, "message": "880", "line": 9, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 9, "endColumn": 9}, {"ruleId": "754", "severity": 1, "message": "881", "line": 15, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 15, "endColumn": 15}, {"ruleId": "754", "severity": 1, "message": "882", "line": 21, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 21, "endColumn": 10}, {"ruleId": "754", "severity": 1, "message": "883", "line": 22, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 22, "endColumn": 15}, {"ruleId": "754", "severity": 1, "message": "793", "line": 29, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 29, "endColumn": 12}, {"ruleId": "754", "severity": 1, "message": "884", "line": 69, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 69, "endColumn": 21}, {"ruleId": "754", "severity": 1, "message": "885", "line": 1, "column": 38, "nodeType": "756", "messageId": "757", "endLine": 1, "endColumn": 44}, {"ruleId": "754", "severity": 1, "message": "886", "line": 4, "column": 40, "nodeType": "756", "messageId": "757", "endLine": 4, "endColumn": 46}, {"ruleId": "754", "severity": 1, "message": "814", "line": 5, "column": 8, "nodeType": "756", "messageId": "757", "endLine": 5, "endColumn": 17}, {"ruleId": "754", "severity": 1, "message": "770", "line": 9, "column": 8, "nodeType": "756", "messageId": "757", "endLine": 9, "endColumn": 29}, {"ruleId": "754", "severity": 1, "message": "887", "line": 18, "column": 8, "nodeType": "756", "messageId": "757", "endLine": 18, "endColumn": 13}, {"ruleId": "754", "severity": 1, "message": "888", "line": 63, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 63, "endColumn": 20}, {"ruleId": "771", "severity": 1, "message": "889", "line": 139, "column": 6, "nodeType": "773", "endLine": 139, "endColumn": 26, "suggestions": "890"}, {"ruleId": "771", "severity": 1, "message": "891", "line": 171, "column": 6, "nodeType": "773", "endLine": 171, "endColumn": 8, "suggestions": "892"}, {"ruleId": "771", "severity": 1, "message": "893", "line": 295, "column": 6, "nodeType": "773", "endLine": 295, "endColumn": 20, "suggestions": "894"}, {"ruleId": "754", "severity": 1, "message": "814", "line": 3, "column": 8, "nodeType": "756", "messageId": "757", "endLine": 3, "endColumn": 17}, {"ruleId": "754", "severity": 1, "message": "801", "line": 9, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 9, "endColumn": 14}, {"ruleId": "754", "severity": 1, "message": "800", "line": 9, "column": 32, "nodeType": "756", "messageId": "757", "endLine": 9, "endColumn": 37}, {"ruleId": "754", "severity": 1, "message": "895", "line": 9, "column": 39, "nodeType": "756", "messageId": "757", "endLine": 9, "endColumn": 45}, {"ruleId": "754", "severity": 1, "message": "793", "line": 18, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 18, "endColumn": 12}, {"ruleId": "754", "severity": 1, "message": "794", "line": 18, "column": 27, "nodeType": "756", "messageId": "757", "endLine": 18, "endColumn": 39}, {"ruleId": "754", "severity": 1, "message": "896", "line": 21, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 21, "endColumn": 21}, {"ruleId": "754", "severity": 1, "message": "897", "line": 24, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 24, "endColumn": 22}, {"ruleId": "771", "severity": 1, "message": "898", "line": 113, "column": 6, "nodeType": "773", "endLine": 113, "endColumn": 32, "suggestions": "899"}, {"ruleId": "771", "severity": 1, "message": "891", "line": 147, "column": 6, "nodeType": "773", "endLine": 147, "endColumn": 8, "suggestions": "900"}, {"ruleId": "754", "severity": 1, "message": "901", "line": 257, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 257, "endColumn": 32}, {"ruleId": "754", "severity": 1, "message": "902", "line": 305, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 305, "endColumn": 26}, {"ruleId": "771", "severity": 1, "message": "891", "line": 329, "column": 6, "nodeType": "773", "endLine": 329, "endColumn": 8, "suggestions": "903"}, {"ruleId": "771", "severity": 1, "message": "904", "line": 336, "column": 6, "nodeType": "773", "endLine": 336, "endColumn": 19, "suggestions": "905"}, {"ruleId": "754", "severity": 1, "message": "906", "line": 12, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 12, "endColumn": 10}, {"ruleId": "754", "severity": 1, "message": "907", "line": 17, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 17, "endColumn": 15}, {"ruleId": "754", "severity": 1, "message": "908", "line": 27, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 27, "endColumn": 15}, {"ruleId": "754", "severity": 1, "message": "793", "line": 28, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 28, "endColumn": 12}, {"ruleId": "909", "severity": 1, "message": "910", "line": 274, "column": 21, "nodeType": "911", "messageId": "912", "endLine": 280, "endColumn": 24}, {"ruleId": "754", "severity": 1, "message": "906", "line": 11, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 11, "endColumn": 10}, {"ruleId": "754", "severity": 1, "message": "767", "line": 18, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 18, "endColumn": 13}, {"ruleId": "754", "severity": 1, "message": "820", "line": 29, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 29, "endColumn": 17}, {"ruleId": "754", "severity": 1, "message": "908", "line": 9, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 9, "endColumn": 15}, {"ruleId": "754", "severity": 1, "message": "793", "line": 10, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 10, "endColumn": 12}, {"ruleId": "771", "severity": 1, "message": "913", "line": 40, "column": 6, "nodeType": "773", "endLine": 40, "endColumn": 8, "suggestions": "914"}, {"ruleId": "754", "severity": 1, "message": "915", "line": 19, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 19, "endColumn": 24}, {"ruleId": "916", "severity": 1, "message": "917", "line": 73, "column": 111, "nodeType": "918", "messageId": "919", "endLine": 73, "endColumn": 112, "suggestions": "920"}, {"ruleId": "916", "severity": 1, "message": "917", "line": 95, "column": 89, "nodeType": "918", "messageId": "919", "endLine": 95, "endColumn": 90, "suggestions": "921"}, {"ruleId": "754", "severity": 1, "message": "865", "line": 6, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 6, "endColumn": 10}, {"ruleId": "754", "severity": 1, "message": "764", "line": 7, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 7, "endColumn": 6}, {"ruleId": "754", "severity": 1, "message": "767", "line": 8, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 8, "endColumn": 13}, {"ruleId": "754", "severity": 1, "message": "922", "line": 9, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 9, "endColumn": 10}, {"ruleId": "771", "severity": 1, "message": "923", "line": 77, "column": 6, "nodeType": "773", "endLine": 77, "endColumn": 14, "suggestions": "924"}, {"ruleId": "771", "severity": 1, "message": "925", "line": 30, "column": 6, "nodeType": "773", "endLine": 30, "endColumn": 16, "suggestions": "926"}, {"ruleId": "754", "severity": 1, "message": "927", "line": 166, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 166, "endColumn": 21}, {"ruleId": "771", "severity": 1, "message": "928", "line": 14, "column": 6, "nodeType": "773", "endLine": 14, "endColumn": 21, "suggestions": "929"}, {"ruleId": "754", "severity": 1, "message": "930", "line": 23, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 23, "endColumn": 27}, {"ruleId": "754", "severity": 1, "message": "931", "line": 10, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 10, "endColumn": 10}, {"ruleId": "754", "severity": 1, "message": "932", "line": 56, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 56, "endColumn": 22}, {"ruleId": "754", "severity": 1, "message": "810", "line": 11, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 11, "endColumn": 8}, {"ruleId": "754", "severity": 1, "message": "933", "line": 12, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 12, "endColumn": 10}, {"ruleId": "754", "severity": 1, "message": "934", "line": 13, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 13, "endColumn": 9}, {"ruleId": "754", "severity": 1, "message": "935", "line": 1, "column": 38, "nodeType": "756", "messageId": "757", "endLine": 1, "endColumn": 46}, {"ruleId": "754", "severity": 1, "message": "936", "line": 112, "column": 23, "nodeType": "756", "messageId": "757", "endLine": 112, "endColumn": 34}, {"ruleId": "754", "severity": 1, "message": "908", "line": 7, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 7, "endColumn": 15}, {"ruleId": "754", "severity": 1, "message": "874", "line": 2, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 2, "endColumn": 16}, {"ruleId": "937", "severity": 1, "message": "938", "line": 69, "column": 3, "nodeType": "939", "messageId": "940", "endLine": 90, "endColumn": 5}, {"ruleId": "754", "severity": 1, "message": "941", "line": 198, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 198, "endColumn": 22}, {"ruleId": "754", "severity": 1, "message": "942", "line": 20, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 20, "endColumn": 8}, {"ruleId": "754", "severity": 1, "message": "943", "line": 21, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 21, "endColumn": 11}, {"ruleId": "754", "severity": 1, "message": "857", "line": 22, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 22, "endColumn": 11}, {"ruleId": "771", "severity": 1, "message": "944", "line": 95, "column": 6, "nodeType": "773", "endLine": 95, "endColumn": 15, "suggestions": "945"}, {"ruleId": "754", "severity": 1, "message": "946", "line": 128, "column": 5, "nodeType": "756", "messageId": "757", "endLine": 128, "endColumn": 14}, {"ruleId": "754", "severity": 1, "message": "874", "line": 2, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 2, "endColumn": 16}, {"ruleId": "754", "severity": 1, "message": "838", "line": 2, "column": 18, "nodeType": "756", "messageId": "757", "endLine": 2, "endColumn": 33}, {"ruleId": "754", "severity": 1, "message": "947", "line": 15, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 15, "endColumn": 21}, {"ruleId": "754", "severity": 1, "message": "948", "line": 22, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 22, "endColumn": 21}, {"ruleId": "754", "severity": 1, "message": "949", "line": 66, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 66, "endColumn": 22}, {"ruleId": "754", "severity": 1, "message": "950", "line": 86, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 86, "endColumn": 24}, {"ruleId": "771", "severity": 1, "message": "951", "line": 126, "column": 6, "nodeType": "773", "endLine": 126, "endColumn": 32, "suggestions": "952", "suppressions": "953"}, {"ruleId": "754", "severity": 1, "message": "797", "line": 7, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 7, "endColumn": 19}, {"ruleId": "754", "severity": 1, "message": "954", "line": 6, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 6, "endColumn": 24}, {"ruleId": "754", "severity": 1, "message": "955", "line": 7, "column": 8, "nodeType": "756", "messageId": "757", "endLine": 7, "endColumn": 21}, {"ruleId": "754", "severity": 1, "message": "956", "line": 9, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 9, "endColumn": 17}, {"ruleId": "754", "severity": 1, "message": "819", "line": 21, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 21, "endColumn": 29}, {"ruleId": "754", "severity": 1, "message": "957", "line": 22, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 22, "endColumn": 22}, {"ruleId": "754", "severity": 1, "message": "958", "line": 68, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 68, "endColumn": 35}, {"ruleId": "754", "severity": 1, "message": "959", "line": 78, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 78, "endColumn": 23}, {"ruleId": "754", "severity": 1, "message": "960", "line": 104, "column": 13, "nodeType": "756", "messageId": "757", "endLine": 104, "endColumn": 26}, {"ruleId": "754", "severity": 1, "message": "906", "line": 12, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 12, "endColumn": 10}, {"ruleId": "754", "severity": 1, "message": "767", "line": 14, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 14, "endColumn": 13}, {"ruleId": "754", "severity": 1, "message": "961", "line": 62, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 62, "endColumn": 32}, {"ruleId": "754", "severity": 1, "message": "962", "line": 5, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 5, "endColumn": 10}, {"ruleId": "754", "severity": 1, "message": "830", "line": 12, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 12, "endColumn": 10}, {"ruleId": "754", "severity": 1, "message": "963", "line": 13, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 13, "endColumn": 12}, {"ruleId": "852", "severity": 1, "message": "964", "line": 235, "column": 3, "nodeType": "854", "messageId": "855", "endLine": 235, "endColumn": 17}, {"ruleId": "754", "severity": 1, "message": "793", "line": 23, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 23, "endColumn": 12}, {"ruleId": "754", "severity": 1, "message": "965", "line": 39, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 39, "endColumn": 20}, {"ruleId": "754", "severity": 1, "message": "942", "line": 8, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 8, "endColumn": 8}, {"ruleId": "754", "severity": 1, "message": "966", "line": 10, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 10, "endColumn": 9}, {"ruleId": "754", "severity": 1, "message": "943", "line": 12, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 12, "endColumn": 11}, "no-unused-vars", "'startTransition' is defined but never used.", "Identifier", "unusedVar", "'HideLoading' is defined but never used.", "'ShowLoading' is defined but never used.", "'AdminNavigation' is defined but never used.", "'TbHome' is defined but never used.", "'TbBrandTanzania' is defined but never used.", "'TbMenu2' is defined but never used.", "'TbX' is defined but never used.", "'TbChevronDown' is defined but never used.", "'TbLogout' is defined but never used.", "'TbSettings' is defined but never used.", "'TbBell' is defined but never used.", "'TbStar' is defined but never used.", "'OnlineStatusIndicator' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'dispatch', 'getUserData', 'navigate', and 'user'. Either include them or remove the dependency array.", "ArrayExpression", ["967"], "React Hook useEffect has missing dependencies: 'dispatch' and 'verifyPaymentStatus'. Either include them or remove the dependency array.", ["968"], "React Hook useEffect has a missing dependency: 'verifyPaymentStatus'. Either include it or remove the dependency array.", ["969"], "'getButtonClass' is assigned a value but never used.", "'Select' is defined but never used.", "React Hook useEffect has missing dependencies: 'getExamData' and 'params.id'. Either include them or remove the dependency array.", ["970"], "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["971"], ["972"], "React Hook useCallback has missing dependencies: 'quiz.category', 'quiz.name', 'quiz.passingMarks', 'quiz.passingPercentage', 'quiz.subject', and 'submitting'. Either include them or remove the dependency array.", ["973"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'TbBrain' is defined but never used.", "'TbBolt' is defined but never used.", "'t' is assigned a value but never used.", "'getClassName' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'getUserResults' and 'user'. Either include them or remove the dependency array.", ["974"], "'formatTime' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getExamsData'. Either include it or remove the dependency array.", ["975"], "'Input' is defined but never used.", "'Form' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchQuestions'. Either include it or remove the dependency array.", ["976"], "'TbDashboard' is defined but never used.", "'navigate' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'getData' and 'pagination'. Either include them or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["977"], "'TbTarget' is defined but never used.", "'TbClock' is defined but never used.", "'TbEye' is defined but never used.", "'loading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.", ["978"], "'PageTitle' is defined but never used.", "'TbPlus' is defined but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getUsersData'. Either include it or remove the dependency array.", ["979"], "'processingStartTime' is assigned a value but never used.", "'dispatch' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPlans'. Either include it or remove the dependency array.", ["980"], "React Hook useEffect has a missing dependency: 'isSubscriptionExpired'. Either include it or remove the dependency array.", ["981"], "'Suspense' is defined but never used.", "'isAdmin' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'dispatch' and 'getUserData'. Either include them or remove the dependency array.", ["982"], "'TbCalendar' is defined but never used.", "'TbAward' is defined but never used.", "'TbDownload' is defined but never used.", "'viewMode' is assigned a value but never used.", "'setViewMode' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getData'. Either include it or remove the dependency array.", ["983"], "React Hook useEffect has a missing dependency: 'applyFilters'. Either include it or remove the dependency array.", ["984"], "'AnimatePresence' is defined but never used.", "'getUserRanking' is defined but never used.", "'showStats' is assigned a value but never used.", "'setShowStats' is assigned a value but never used.", "'animationPhase' is assigned a value but never used.", "'currentUserLeague' is assigned a value but never used.", "'showLeagueView' is assigned a value but never used.", "'headerRef' is assigned a value but never used.", "'currentUserRef' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchFullUserData', 'fetchRankingData', 'motivationalQuotes', 'rankingData', and 'user'. Either include them or remove the dependency array.", ["985"], "'otherPerformers' is assigned a value but never used.", "'getSubscriptionBadge' is assigned a value but never used.", "'leagueKey' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'y'.", "ObjectExpression", "unexpected", "'FaChevronDown' is defined but never used.", "'FaSearch' is defined but never used.", "'TbSearch' is defined but never used.", "'TbFilter' is defined but never used.", "'TbSortAscending' is defined but never used.", "'TbUser' is defined but never used.", "'TbChevronUp' is defined but never used.", "'TbAlertTriangle' is defined but never used.", "'TbInfoCircle' is defined but never used.", "'TbCheck' is defined but never used.", "The 'allPossibleClasses' conditional could make the dependencies of useCallback Hook (at line 118) change on every render. Move it inside the useCallback callback. Alternatively, wrap the initialization of 'allPossibleClasses' in its own useMemo() Hook.", "VariableDeclarator", "React Hook useMemo has an unnecessary dependency: 'activeTab'. Either exclude it or remove the dependency array.", ["986"], "'extractUserResultData' is defined but never used.", "'safeNumber' is defined but never used.", "React Hook useCallback has a missing dependency: 'startTime'. Either include it or remove the dependency array.", ["987"], "'motion' is defined but never used.", "'FaExpand' is assigned a value but never used.", "'FaCompress' is assigned a value but never used.", "'showComments' is assigned a value but never used.", "'setShowComments' is assigned a value but never used.", "'handleClearAll' is assigned a value but never used.", "'FaHome' is defined but never used.", "'FaCreditCard' is defined but never used.", "'FaRobot' is defined but never used.", "'FaSignOutAlt' is defined but never used.", "'handleLogout' is assigned a value but never used.", "'useRef' is defined but never used.", "'Avatar' is defined but never used.", "'image' is defined but never used.", "'totalPages' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'clearAllForumCaches' and 'fetchQuestions'. Either include them or remove the dependency array.", ["988"], "React Hook useEffect has a missing dependency: 'getUserData'. Either include it or remove the dependency array.", ["989"], "React Hook useEffect has a missing dependency: 'form2'. Either include it or remove the dependency array.", ["990"], "'Button' is defined but never used.", "'userRanking' is assigned a value but never used.", "'imagePreview' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getUserStats'. Either include it or remove the dependency array.", ["991"], ["992"], "'handleLevelChangeCancel' is assigned a value but never used.", "'handleImageUpload' is assigned a value but never used.", ["993"], "React Hook useEffect has a missing dependency: 'fetchUserRankingData'. Either include it or remove the dependency array.", ["994"], "'TbRobot' is defined but never used.", "'TbCreditCard' is defined but never used.", "'user' is assigned a value but never used.", "react/jsx-no-duplicate-props", "No duplicate props allowed", "JSXAttribute", "noDuplicateProps", "React Hook useEffect has a missing dependency: 'clearChat'. Either include it or remove the dependency array.", ["995"], "'restoredLines' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\[.", "Literal", "unnecessaryEscape", ["996", "997"], ["998", "999"], "'TbTrash' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchNotifications' and 'notifications.length'. Either include them or remove the dependency array.", ["1000"], "React Hook useEffect has a missing dependency: 'handleSubmitQuiz'. Either include it or remove the dependency array.", ["1001"], "'goToQuestion' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchTrialQuiz'. Either include it or remove the dependency array.", ["1002"], "'animationComplete' is assigned a value but never used.", "'TbUsers' is defined but never used.", "'getTimerColor' is assigned a value but never used.", "'TbPhoto' is defined but never used.", "'TbEdit' is defined but never used.", "'Fragment' is defined but never used.", "'toggleTheme' is assigned a value but never used.", "no-unreachable", "Unreachable code.", "ReturnStatement", "unreachableCode", "'containerRef' is assigned a value but never used.", "'FaEye' is defined but never used.", "'FaFilter' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchMaterials'. Either include it or remove the dependency array.", ["1003"], "'xpAwarded' is assigned a value but never used.", "'levelOptions' is assigned a value but never used.", "'classOptions' is assigned a value but never used.", "'modalVariants' is assigned a value but never used.", "'overlayVariants' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'renderPDF'. Either include it or remove the dependency array.", ["1004"], ["1005"], "'updateUserInfo' is defined but never used.", "'axiosInstance' is defined but never used.", "'SetUser' is defined but never used.", "'showTryAgain' is assigned a value but never used.", "'handleCloseProcessingModal' is assigned a value but never used.", "'handleTryAgain' is assigned a value but never used.", "'tryAgainTimer' is assigned a value but never used.", "'triggerLevelUpAnimation' is assigned a value but never used.", "'TbMedal' is defined but never used.", "'TbDiamond' is defined but never used.", "Duplicate key 'studyMaterials'.", "'videoError' is assigned a value but never used.", "'FaStar' is defined but never used.", {"desc": "1006", "fix": "1007"}, {"desc": "1008", "fix": "1009"}, {"desc": "1010", "fix": "1011"}, {"desc": "1012", "fix": "1013"}, {"desc": "1014", "fix": "1015"}, {"kind": "1016", "justification": "1017"}, {"desc": "1018", "fix": "1019"}, {"desc": "1020", "fix": "1021"}, {"desc": "1022", "fix": "1023"}, {"desc": "1024", "fix": "1025"}, {"desc": "1026", "fix": "1027"}, {"desc": "1028", "fix": "1029"}, {"desc": "1030", "fix": "1031"}, {"desc": "1032", "fix": "1033"}, {"desc": "1034", "fix": "1035"}, {"desc": "1036", "fix": "1037"}, {"desc": "1038", "fix": "1039"}, {"desc": "1040", "fix": "1041"}, {"desc": "1042", "fix": "1043"}, {"desc": "1044", "fix": "1045"}, {"desc": "1046", "fix": "1047"}, {"desc": "1048", "fix": "1049"}, {"desc": "1050", "fix": "1051"}, {"desc": "1052", "fix": "1053"}, {"desc": "1054", "fix": "1055"}, {"desc": "1050", "fix": "1056"}, {"desc": "1050", "fix": "1057"}, {"desc": "1058", "fix": "1059"}, {"desc": "1060", "fix": "1061"}, {"messageId": "1062", "fix": "1063", "desc": "1064"}, {"messageId": "1065", "fix": "1066", "desc": "1067"}, {"messageId": "1062", "fix": "1068", "desc": "1064"}, {"messageId": "1065", "fix": "1069", "desc": "1067"}, {"desc": "1070", "fix": "1071"}, {"desc": "1072", "fix": "1073"}, {"desc": "1074", "fix": "1075"}, {"desc": "1076", "fix": "1077"}, {"desc": "1078", "fix": "1079"}, {"kind": "1016", "justification": "1017"}, "Update the dependencies array to be: [dispatch, getUserData, navigate, user]", {"range": "1080", "text": "1081"}, "Update the dependencies array to be: [dispatch, paymentVerificationNeeded, user, verifyPaymentStatus]", {"range": "1082", "text": "1083"}, "Update the dependencies array to be: [user, activeRoute, verifyPaymentStatus]", {"range": "1084", "text": "1085"}, "Update the dependencies array to be: [getExamData, params.id]", {"range": "1086", "text": "1087"}, "Update the dependencies array to be: [fetchUsers]", {"range": "1088", "text": "1089"}, "directive", "", "Update the dependencies array to be: [submitting, user, startTime, questions, quiz.passingMarks, quiz.passingPercentage, quiz.name, quiz.subject, quiz.category, id, navigate, answers]", {"range": "1090", "text": "1091"}, "Update the dependencies array to be: [getUserResults, user]", {"range": "1092", "text": "1093"}, "Update the dependencies array to be: [getExamsData]", {"range": "1094", "text": "1095"}, "Update the dependencies array to be: [fetchQuestions]", {"range": "1096", "text": "1097"}, "Update the dependencies array to be: [filters, getData, pagination]", {"range": "1098", "text": "1099"}, "Update the dependencies array to be: [fetchDashboardData]", {"range": "1100", "text": "1101"}, "Update the dependencies array to be: [getUsersData]", {"range": "1102", "text": "1103"}, "Update the dependencies array to be: [fetchPlans]", {"range": "1104", "text": "1105"}, "Update the dependencies array to be: [isSubscriptionExpired, subscriptionData]", {"range": "1106", "text": "1107"}, "Update the dependencies array to be: [dispatch, getUserData]", {"range": "1108", "text": "1109"}, "Update the dependencies array to be: [getData]", {"range": "1110", "text": "1111"}, "Update the dependencies array to be: [filterSubject, filterVerdict, dateRange, reportsData, applyFilters]", {"range": "1112", "text": "1113"}, "Update the dependencies array to be: [fetchFullUserData, fetchRankingData, motivationalQuotes, rankingData, user]", {"range": "1114", "text": "1115"}, "Update the dependencies array to be: [materials, searchTerm, sortBy]", {"range": "1116", "text": "1117"}, "Update the dependencies array to be: [user, dispatch, questions, startTime, examData?.duration, examData.passingMarks, params.id, navigate, selectedOptions]", {"range": "1118", "text": "1119"}, "Update the dependencies array to be: [clearAllForumCaches, currentPage, fetchQuestions, limit]", {"range": "1120", "text": "1121"}, "Update the dependencies array to be: [getUserData]", {"range": "1122", "text": "1123"}, "Update the dependencies array to be: [editQuestion, form2]", {"range": "1124", "text": "1125"}, "Update the dependencies array to be: [getUserStats, rankingData, userDetails]", {"range": "1126", "text": "1127"}, {"range": "1128", "text": "1123"}, {"range": "1129", "text": "1123"}, "Update the dependencies array to be: [fetchUserRankingData, userDetails]", {"range": "1130", "text": "1131"}, "Update the dependencies array to be: [clearChat]", {"range": "1132", "text": "1133"}, "removeEscape", {"range": "1134", "text": "1017"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "1135", "text": "1136"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "1137", "text": "1017"}, {"range": "1138", "text": "1136"}, "Update the dependencies array to be: [fetchNotifications, isOpen, notifications.length]", {"range": "1139", "text": "1140"}, "Update the dependencies array to be: [handleSubmitQuiz, timeLeft]", {"range": "1141", "text": "1142"}, "Update the dependencies array to be: [fetchTrialQuiz, trialUserInfo]", {"range": "1143", "text": "1144"}, "Update the dependencies array to be: [fetchMaterials, filters]", {"range": "1145", "text": "1146"}, "Update the dependencies array to be: [modalIsOpen, documentUrl, renderPDF]", {"range": "1147", "text": "1148"}, [4456, 4458], "[dispatch, getUserData, navigate, user]", [7410, 7443], "[dispatch, paymentVerificationNeeded, user, verifyPaymentStatus]", [7953, 7972], "[user, activeRoute, verifyPaymentStatus]", [3399, 3401], "[getExamData, params.id]", [1327, 1329], "[fetchUsers]", [11677, 11728], "[submitting, user, startTime, questions, quiz.passingMarks, quiz.passingPercentage, quiz.name, quiz.subject, quiz.category, id, navigate, answers]", [9239, 9241], "[getUser<PERSON><PERSON><PERSON><PERSON>, user]", [5318, 5320], "[getExamsData]", [3074, 3076], "[fetchQuestions]", [7040, 7069], "[filters, getData, pagination]", [1196, 1198], "[fetchDashboardData]", [5587, 5589], "[getUsersData]", [3149, 3151], "[fetchPlans]", [4869, 4887], "[isSubscriptionExpired, subscriptionData]", [1990, 1992], "[dispatch, getUserData]", [4129, 4131], "[getData]", [4184, 4238], "[filterSubject, filterVerdict, dateRange, reportsData, applyFilters]", [34517, 34519], "[fetchFullUserData, fetchRankingData, motivationalQuotes, rankingData, user]", [8198, 8240], "[materials, searchTerm, sortBy]", [28864, 28939], "[user, dispatch, questions, startTime, examData?.duration, examData.passingMarks, params.id, navigate, selectedOptions]", [5683, 5703], "[clearAllForumCaches, currentPage, fetchQuestions, limit]", [6451, 6453], "[getUserData]", [9851, 9865], "[editQuestion, form2]", [3855, 3881], "[getUserStats, rankingData, userDetails]", [4823, 4825], [10390, 10392], [10542, 10555], "[fetchUserRankingData, userDetails]", [1586, 1588], "[clearChat]", [3500, 3501], [3500, 3500], "\\", [5011, 5012], [5011, 5011], [2302, 2310], "[fetchNotifications, isOpen, notifications.length]", [1032, 1042], "[handleSubmitQuiz, timeLeft]", [527, 542], "[fetchTrialQuiz, trialUserInfo]", [2411, 2420], "[fetchMaterials, filters]", [3978, 4004], "[modalIsOpen, documentUrl, renderPDF]"]