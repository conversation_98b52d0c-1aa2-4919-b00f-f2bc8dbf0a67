[{"C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\store.js": "4", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ProtectedRoute.js": "5", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\Loader.js": "6", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminProtectedRoute.js": "7", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\contexts\\ThemeContext.js": "8", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\RankingErrorBoundary.js": "9", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\DebugAuth.jsx": "10", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\RankingDemo.js": "11", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\loaderSlice.js": "12", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\usersSlice.js": "13", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\subscriptionSlice.js": "14", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\trial\\TrialPage.js": "15", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\paymentSlice.js": "16", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditExam.js": "17", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Notifications\\AdminNotifications.jsx": "18", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizPlay.js": "19", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizResult.js": "20", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\index.js": "21", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\index.js": "22", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\index.js": "23", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Forum\\index.js": "24", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AdminReports\\index.js": "25", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Dashboard\\index.js": "26", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\index.js": "27", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Users\\index.js": "28", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Subscription\\index.js": "29", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Test\\index.js": "30", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\AboutUs\\index.js": "31", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\UserReports\\index.js": "32", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Ranking\\index.js": "33", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\index.js": "34", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\index.js": "35", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\VideoLessons\\index.js": "36", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Hub\\index.js": "37", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Home\\index.js": "38", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Register\\index.js": "39", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Login\\index.js": "40", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Forum\\index.js": "41", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Profile\\index.js": "42", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\payment.js": "43", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ModernSidebar.js": "44", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminNavigation.js": "45", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\FloatingBrainwaveAI.js": "46", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\users.js": "47", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\syllabus.js": "48", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\aiQuestions.js": "49", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\notifications.js": "50", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\PageTitle.js": "51", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\exams.js": "52", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reports.js": "53", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\chat.js": "54", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\UserRankingList.js": "55", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ContentRenderer.js": "56", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\NotificationBell.js": "57", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\UserRankingCard.js": "58", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\ProfilePicture.js": "59", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\OnlineStatusIndicator.js": "60", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialQuizPlay.js": "61", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialQuizSelection.js": "62", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialQuizResult.js": "63", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Button.js": "64", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Loading.js": "65", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Card.js": "66", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialRegistrationPrompt.js": "67", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Input.js": "68", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizTimer.js": "69", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\LazyImage.js": "70", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizCard.js": "71", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizQuestion.js": "72", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\forum.js": "73", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\data\\Subjects.jsx": "74", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ThemeToggle.js": "75", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminLayout.js": "76", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminCard.js": "77", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditQuestion.js": "78", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ErrorBoundary.js": "79", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ResponsiveContainer.js": "80", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\PerformanceMonitor.js": "81", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\SubtitleManager.js": "82", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\AddStudyMaterialForm.js": "83", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\plans.js": "84", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reviews.js": "85", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\EditStudyMaterialForm.js": "86", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\StudyMaterialManager.js": "87", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\study.js": "88", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\utils\\quizDataUtils.js": "89", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\XPResultDisplay.js": "90", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\SubscriptionExpiredModal\\SubscriptionExpiredModal.jsx": "91", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\TryForFreeModal.js": "92", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\UpgradeRestrictionModal\\UpgradeRestrictionModal.jsx": "93", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\PDFModal.js": "94", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\Instructions.js": "95", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\index.js": "96", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\SubscriptionModal\\SubscriptionModal.jsx": "97", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminTopNavigation.js": "98", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\trial.js": "99", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\subtitles.js": "100", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\XPProgressBar.js": "101", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\AchievementBadge.js": "102", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\EnhancedAchievementBadge.js": "103", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\LevelBadge.js": "104", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\contexts\\LanguageContext.js": "105", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\localization\\kiswahili.js": "106", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Skills\\index.js": "107", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\skills.js": "108", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Skills\\index.js": "109", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\PastPaperDiscussion.js": "110", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\aiResponse.js": "111"}, {"size": 395, "mtime": 1696247250000, "results": "112", "hashOfConfig": "113"}, {"size": 13439, "mtime": 1752343277235, "results": "114", "hashOfConfig": "113"}, {"size": 362, "mtime": 1696247250000, "results": "115", "hashOfConfig": "113"}, {"size": 430, "mtime": 1736735017645, "results": "116", "hashOfConfig": "113"}, {"size": 20340, "mtime": 1752145561052, "results": "117", "hashOfConfig": "113"}, {"size": 180, "mtime": 1696247250000, "results": "118", "hashOfConfig": "113"}, {"size": 1601, "mtime": 1751575879221, "results": "119", "hashOfConfig": "113"}, {"size": 1410, "mtime": 1751140352157, "results": "120", "hashOfConfig": "113"}, {"size": 3109, "mtime": 1751260973778, "results": "121", "hashOfConfig": "113"}, {"size": 9770, "mtime": 1751495320007, "results": "122", "hashOfConfig": "113"}, {"size": 12711, "mtime": 1751260271529, "results": "123", "hashOfConfig": "113"}, {"size": 416, "mtime": 1696247250000, "results": "124", "hashOfConfig": "113"}, {"size": 334, "mtime": 1696247250000, "results": "125", "hashOfConfig": "113"}, {"size": 404, "mtime": 1736731932223, "results": "126", "hashOfConfig": "113"}, {"size": 6016, "mtime": 1751638273351, "results": "127", "hashOfConfig": "113"}, {"size": 449, "mtime": 1736732007232, "results": "128", "hashOfConfig": "113"}, {"size": 15139, "mtime": 1751582824879, "results": "129", "hashOfConfig": "113"}, {"size": 10989, "mtime": 1751586090664, "results": "130", "hashOfConfig": "113"}, {"size": 37700, "mtime": 1752198101933, "results": "131", "hashOfConfig": "113"}, {"size": 48118, "mtime": 1752336893747, "results": "132", "hashOfConfig": "113"}, {"size": 1140, "mtime": 1751426583568, "results": "133", "hashOfConfig": "113"}, {"size": 33135, "mtime": 1752338943014, "results": "134", "hashOfConfig": "113"}, {"size": 10464, "mtime": 1752187297118, "results": "135", "hashOfConfig": "113"}, {"size": 12434, "mtime": 1751869385895, "results": "136", "hashOfConfig": "113"}, {"size": 16063, "mtime": 1751870755172, "results": "137", "hashOfConfig": "113"}, {"size": 12035, "mtime": 1751869525840, "results": "138", "hashOfConfig": "113"}, {"size": 8883, "mtime": 1751585554937, "results": "139", "hashOfConfig": "113"}, {"size": 22154, "mtime": 1751585459342, "results": "140", "hashOfConfig": "113"}, {"size": 45883, "mtime": 1752135725326, "results": "141", "hashOfConfig": "113"}, {"size": 1327, "mtime": 1709427669270, "results": "142", "hashOfConfig": "113"}, {"size": 8089, "mtime": 1740446459586, "results": "143", "hashOfConfig": "113"}, {"size": 27236, "mtime": 1751873126836, "results": "144", "hashOfConfig": "113"}, {"size": 136450, "mtime": 1752148102332, "results": "145", "hashOfConfig": "113"}, {"size": 22777, "mtime": 1752370030612, "results": "146", "hashOfConfig": "113"}, {"size": 48284, "mtime": 1752336822163, "results": "147", "hashOfConfig": "113"}, {"size": 41275, "mtime": 1752371048449, "results": "148", "hashOfConfig": "113"}, {"size": 7870, "mtime": 1752338665955, "results": "149", "hashOfConfig": "113"}, {"size": 68001, "mtime": 1752145575965, "results": "150", "hashOfConfig": "113"}, {"size": 9971, "mtime": 1752333866970, "results": "151", "hashOfConfig": "113"}, {"size": 4378, "mtime": 1752193825539, "results": "152", "hashOfConfig": "113"}, {"size": 33933, "mtime": 1752371144786, "results": "153", "hashOfConfig": "113"}, {"size": 47852, "mtime": 1752339297891, "results": "154", "hashOfConfig": "113"}, {"size": 2046, "mtime": 1752102606942, "results": "155", "hashOfConfig": "113"}, {"size": 13836, "mtime": 1752343174543, "results": "156", "hashOfConfig": "113"}, {"size": 15668, "mtime": 1752343935697, "results": "157", "hashOfConfig": "113"}, {"size": 32082, "mtime": 1752372892909, "results": "158", "hashOfConfig": "113"}, {"size": 3191, "mtime": 1751940514617, "results": "159", "hashOfConfig": "113"}, {"size": 7315, "mtime": 1751495843287, "results": "160", "hashOfConfig": "113"}, {"size": 6337, "mtime": 1751558223480, "results": "161", "hashOfConfig": "113"}, {"size": 3632, "mtime": 1751487806125, "results": "162", "hashOfConfig": "113"}, {"size": 388, "mtime": 1703845955779, "results": "163", "hashOfConfig": "113"}, {"size": 2455, "mtime": 1751479784424, "results": "164", "hashOfConfig": "113"}, {"size": 3391, "mtime": 1751304153158, "results": "165", "hashOfConfig": "113"}, {"size": 1104, "mtime": 1749936905424, "results": "166", "hashOfConfig": "113"}, {"size": 29870, "mtime": 1752145669266, "results": "167", "hashOfConfig": "113"}, {"size": 5595, "mtime": 1751164672302, "results": "168", "hashOfConfig": "113"}, {"size": 11831, "mtime": 1752096169929, "results": "169", "hashOfConfig": "113"}, {"size": 18256, "mtime": 1751482855935, "results": "170", "hashOfConfig": "113"}, {"size": 6382, "mtime": 1752146723210, "results": "171", "hashOfConfig": "113"}, {"size": 3307, "mtime": 1751855844189, "results": "172", "hashOfConfig": "113"}, {"size": 24195, "mtime": 1752195775706, "results": "173", "hashOfConfig": "113"}, {"size": 10009, "mtime": 1751649332583, "results": "174", "hashOfConfig": "113"}, {"size": 27796, "mtime": 1751916780076, "results": "175", "hashOfConfig": "113"}, {"size": 2913, "mtime": 1751140370241, "results": "176", "hashOfConfig": "113"}, {"size": 3119, "mtime": 1751164996340, "results": "177", "hashOfConfig": "113"}, {"size": 1857, "mtime": 1751140385464, "results": "178", "hashOfConfig": "113"}, {"size": 10040, "mtime": 1751638250072, "results": "179", "hashOfConfig": "113"}, {"size": 2324, "mtime": 1751140401815, "results": "180", "hashOfConfig": "113"}, {"size": 7165, "mtime": 1752195834207, "results": "181", "hashOfConfig": "113"}, {"size": 2504, "mtime": 1751957740575, "results": "182", "hashOfConfig": "113"}, {"size": 17286, "mtime": 1752193750030, "results": "183", "hashOfConfig": "113"}, {"size": 13299, "mtime": 1751249005755, "results": "184", "hashOfConfig": "113"}, {"size": 1787, "mtime": 1734985908268, "results": "185", "hashOfConfig": "113"}, {"size": 1245, "mtime": 1752333815446, "results": "186", "hashOfConfig": "113"}, {"size": 3904, "mtime": 1751143777976, "results": "187", "hashOfConfig": "113"}, {"size": 2200, "mtime": 1751563008113, "results": "188", "hashOfConfig": "113"}, {"size": 1717, "mtime": 1751561083661, "results": "189", "hashOfConfig": "113"}, {"size": 12864, "mtime": 1751134045332, "results": "190", "hashOfConfig": "113"}, {"size": 5088, "mtime": 1751143254906, "results": "191", "hashOfConfig": "113"}, {"size": 4989, "mtime": 1751143312418, "results": "192", "hashOfConfig": "113"}, {"size": 6304, "mtime": 1751188593099, "results": "193", "hashOfConfig": "113"}, {"size": 9494, "mtime": 1750995979612, "results": "194", "hashOfConfig": "113"}, {"size": 29255, "mtime": 1752333961995, "results": "195", "hashOfConfig": "113"}, {"size": 279, "mtime": 1736719733927, "results": "196", "hashOfConfig": "113"}, {"size": 578, "mtime": 1705434185826, "results": "197", "hashOfConfig": "113"}, {"size": 17375, "mtime": 1751000106093, "results": "198", "hashOfConfig": "113"}, {"size": 11161, "mtime": 1750999560542, "results": "199", "hashOfConfig": "113"}, {"size": 6669, "mtime": 1750999504134, "results": "200", "hashOfConfig": "113"}, {"size": 9114, "mtime": 1751691985112, "results": "201", "hashOfConfig": "113"}, {"size": 16372, "mtime": 1751479340474, "results": "202", "hashOfConfig": "113"}, {"size": 9653, "mtime": 1752084006410, "results": "203", "hashOfConfig": "113"}, {"size": 9006, "mtime": 1752334003537, "results": "204", "hashOfConfig": "113"}, {"size": 6486, "mtime": 1752078868080, "results": "205", "hashOfConfig": "113"}, {"size": 17784, "mtime": 1752372874060, "results": "206", "hashOfConfig": "113"}, {"size": 3835, "mtime": 1751478376207, "results": "207", "hashOfConfig": "113"}, {"size": 3672, "mtime": 1752017325220, "results": "208", "hashOfConfig": "113"}, {"size": 13621, "mtime": 1752105816006, "results": "209", "hashOfConfig": "113"}, {"size": 6575, "mtime": 1752343969641, "results": "210", "hashOfConfig": "113"}, {"size": 1024, "mtime": 1751637471453, "results": "211", "hashOfConfig": "113"}, {"size": 1524, "mtime": 1750994293078, "results": "212", "hashOfConfig": "113"}, {"size": 8429, "mtime": 1751244672688, "results": "213", "hashOfConfig": "113"}, {"size": 11901, "mtime": 1751236424130, "results": "214", "hashOfConfig": "113"}, {"size": 10081, "mtime": 1751244608756, "results": "215", "hashOfConfig": "113"}, {"size": 7685, "mtime": 1751244700154, "results": "216", "hashOfConfig": "113"}, {"size": 3773, "mtime": 1752336238191, "results": "217", "hashOfConfig": "113"}, {"size": 13619, "mtime": 1752346804847, "results": "218", "hashOfConfig": "113"}, {"size": 17717, "mtime": 1752343073846, "results": "219", "hashOfConfig": "113"}, {"size": 2718, "mtime": 1752343803321, "results": "220", "hashOfConfig": "113"}, {"size": 16536, "mtime": 1752343895497, "results": "221", "hashOfConfig": "113"}, {"size": 7929, "mtime": 1752345739823, "results": "222", "hashOfConfig": "113"}, {"size": 1790, "mtime": 1752345695021, "results": "223", "hashOfConfig": "113"}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ymk59w", {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "542", "messages": "543", "suppressedMessages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\store.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ProtectedRoute.js", ["557", "558", "559", "560", "561", "562", "563", "564", "565", "566", "567", "568", "569", "570", "571", "572", "573", "574"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\Loader.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\contexts\\ThemeContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\RankingErrorBoundary.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\DebugAuth.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\RankingDemo.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\loaderSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\usersSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\subscriptionSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\trial\\TrialPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\paymentSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditExam.js", ["575", "576"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Notifications\\AdminNotifications.jsx", [], ["577"], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizPlay.js", ["578"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizResult.js", ["579"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\index.js", ["580", "581", "582", "583", "584", "585", "586", "587", "588"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\index.js", ["589"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Forum\\index.js", ["590", "591", "592"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AdminReports\\index.js", ["593", "594", "595"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Dashboard\\index.js", ["596", "597", "598", "599", "600"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Users\\index.js", ["601", "602", "603", "604"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Subscription\\index.js", ["605", "606", "607", "608", "609", "610"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Test\\index.js", ["611", "612"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\AboutUs\\index.js", ["613"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\UserReports\\index.js", ["614", "615", "616", "617", "618", "619", "620", "621"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Ranking\\index.js", ["622", "623", "624", "625", "626", "627", "628", "629", "630", "631", "632", "633", "634", "635", "636", "637", "638"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\index.js", ["639", "640", "641", "642", "643", "644", "645", "646", "647", "648", "649", "650", "651", "652", "653", "654", "655"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\index.js", ["656", "657", "658"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\VideoLessons\\index.js", ["659", "660", "661", "662", "663", "664", "665", "666", "667"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Hub\\index.js", ["668", "669", "670", "671", "672", "673"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Home\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Register\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Login\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Forum\\index.js", ["674", "675", "676", "677", "678", "679", "680", "681", "682", "683", "684", "685"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Profile\\index.js", ["686", "687", "688", "689", "690", "691", "692", "693", "694", "695", "696", "697", "698", "699"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\payment.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ModernSidebar.js", ["700", "701", "702", "703", "704"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminNavigation.js", ["705", "706", "707"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\FloatingBrainwaveAI.js", ["708", "709", "710", "711", "712"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\users.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\syllabus.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\aiQuestions.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\notifications.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\PageTitle.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\exams.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reports.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\chat.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\UserRankingList.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ContentRenderer.js", ["713", "714", "715"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\NotificationBell.js", ["716", "717", "718", "719", "720"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\UserRankingCard.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\ProfilePicture.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\OnlineStatusIndicator.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialQuizPlay.js", ["721", "722"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialQuizSelection.js", ["723"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialQuizResult.js", ["724"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Button.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Loading.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Card.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\trial\\TrialRegistrationPrompt.js", ["725"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Input.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizTimer.js", ["726"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\LazyImage.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizCard.js", ["727", "728", "729"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizQuestion.js", ["730"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\forum.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\data\\Subjects.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ThemeToggle.js", ["731"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminLayout.js", ["732"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminCard.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditQuestion.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ErrorBoundary.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ResponsiveContainer.js", ["733"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\PerformanceMonitor.js", ["734", "735"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\SubtitleManager.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\AddStudyMaterialForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\plans.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reviews.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\EditStudyMaterialForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\StudyMaterialManager.js", ["736", "737", "738", "739"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\study.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\utils\\quizDataUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\XPResultDisplay.js", ["740"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\SubscriptionExpiredModal\\SubscriptionExpiredModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\common\\TryForFreeModal.js", ["741", "742", "743", "744", "745", "746"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\UpgradeRestrictionModal\\UpgradeRestrictionModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\PDFModal.js", ["747"], ["748"], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\Instructions.js", ["749"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\SubscriptionModal\\SubscriptionModal.jsx", ["750", "751", "752", "753", "754", "755", "756", "757"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AdminTopNavigation.js", ["758", "759"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\trial.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\subtitles.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\XPProgressBar.js", ["760"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\AchievementBadge.js", ["761", "762", "763"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\EnhancedAchievementBadge.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\LevelBadge.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\contexts\\LanguageContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\localization\\kiswahili.js", ["764"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Skills\\index.js", ["765", "766"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\skills.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Skills\\index.js", ["767", "768", "769"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\PastPaperDiscussion.js", ["770", "771"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\aiResponse.js", [], [], {"ruleId": "772", "severity": 1, "message": "773", "line": 2, "column": 46, "nodeType": "774", "messageId": "775", "endLine": 2, "endColumn": 61}, {"ruleId": "772", "severity": 1, "message": "776", "line": 7, "column": 10, "nodeType": "774", "messageId": "775", "endLine": 7, "endColumn": 21}, {"ruleId": "772", "severity": 1, "message": "777", "line": 7, "column": 23, "nodeType": "774", "messageId": "775", "endLine": 7, "endColumn": 34}, {"ruleId": "772", "severity": 1, "message": "778", "line": 12, "column": 8, "nodeType": "774", "messageId": "775", "endLine": 12, "endColumn": 23}, {"ruleId": "772", "severity": 1, "message": "779", "line": 15, "column": 10, "nodeType": "774", "messageId": "775", "endLine": 15, "endColumn": 16}, {"ruleId": "772", "severity": 1, "message": "780", "line": 15, "column": 18, "nodeType": "774", "messageId": "775", "endLine": 15, "endColumn": 33}, {"ruleId": "772", "severity": 1, "message": "781", "line": 15, "column": 35, "nodeType": "774", "messageId": "775", "endLine": 15, "endColumn": 42}, {"ruleId": "772", "severity": 1, "message": "782", "line": 15, "column": 44, "nodeType": "774", "messageId": "775", "endLine": 15, "endColumn": 47}, {"ruleId": "772", "severity": 1, "message": "783", "line": 15, "column": 49, "nodeType": "774", "messageId": "775", "endLine": 15, "endColumn": 62}, {"ruleId": "772", "severity": 1, "message": "784", "line": 15, "column": 64, "nodeType": "774", "messageId": "775", "endLine": 15, "endColumn": 72}, {"ruleId": "772", "severity": 1, "message": "785", "line": 15, "column": 82, "nodeType": "774", "messageId": "775", "endLine": 15, "endColumn": 92}, {"ruleId": "772", "severity": 1, "message": "786", "line": 15, "column": 94, "nodeType": "774", "messageId": "775", "endLine": 15, "endColumn": 100}, {"ruleId": "772", "severity": 1, "message": "787", "line": 15, "column": 102, "nodeType": "774", "messageId": "775", "endLine": 15, "endColumn": 108}, {"ruleId": "772", "severity": 1, "message": "788", "line": 16, "column": 8, "nodeType": "774", "messageId": "775", "endLine": 16, "endColumn": 29}, {"ruleId": "789", "severity": 1, "message": "790", "line": 120, "column": 6, "nodeType": "791", "endLine": 120, "endColumn": 8, "suggestions": "792"}, {"ruleId": "789", "severity": 1, "message": "793", "line": 189, "column": 6, "nodeType": "791", "endLine": 189, "endColumn": 39, "suggestions": "794"}, {"ruleId": "789", "severity": 1, "message": "795", "line": 200, "column": 6, "nodeType": "791", "endLine": 200, "endColumn": 25, "suggestions": "796"}, {"ruleId": "772", "severity": 1, "message": "797", "line": 231, "column": 9, "nodeType": "774", "messageId": "775", "endLine": 231, "endColumn": 23}, {"ruleId": "772", "severity": 1, "message": "798", "line": 1, "column": 35, "nodeType": "774", "messageId": "775", "endLine": 1, "endColumn": 41}, {"ruleId": "789", "severity": 1, "message": "799", "line": 110, "column": 6, "nodeType": "791", "endLine": 110, "endColumn": 8, "suggestions": "800"}, {"ruleId": "789", "severity": 1, "message": "801", "line": 46, "column": 6, "nodeType": "791", "endLine": 46, "endColumn": 8, "suggestions": "802", "suppressions": "803"}, {"ruleId": "789", "severity": 1, "message": "804", "line": 322, "column": 6, "nodeType": "791", "endLine": 322, "endColumn": 57, "suggestions": "805"}, {"ruleId": "806", "severity": 1, "message": "807", "line": 1077, "column": 29, "nodeType": "808", "endLine": 1086, "endColumn": 31}, {"ruleId": "772", "severity": 1, "message": "809", "line": 13, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 13, "endColumn": 10}, {"ruleId": "772", "severity": 1, "message": "787", "line": 17, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 17, "endColumn": 9}, {"ruleId": "772", "severity": 1, "message": "779", "line": 18, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 18, "endColumn": 9}, {"ruleId": "772", "severity": 1, "message": "810", "line": 19, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 19, "endColumn": 9}, {"ruleId": "772", "severity": 1, "message": "811", "line": 43, "column": 11, "nodeType": "774", "messageId": "775", "endLine": 43, "endColumn": 12}, {"ruleId": "772", "severity": 1, "message": "812", "line": 43, "column": 27, "nodeType": "774", "messageId": "775", "endLine": 43, "endColumn": 39}, {"ruleId": "789", "severity": 1, "message": "813", "line": 251, "column": 6, "nodeType": "791", "endLine": 251, "endColumn": 8, "suggestions": "814"}, {"ruleId": "772", "severity": 1, "message": "812", "line": 500, "column": 24, "nodeType": "774", "messageId": "775", "endLine": 500, "endColumn": 36}, {"ruleId": "772", "severity": 1, "message": "815", "line": 502, "column": 9, "nodeType": "774", "messageId": "775", "endLine": 502, "endColumn": 19}, {"ruleId": "789", "severity": 1, "message": "816", "line": 191, "column": 6, "nodeType": "791", "endLine": 191, "endColumn": 8, "suggestions": "817"}, {"ruleId": "772", "severity": 1, "message": "818", "line": 2, "column": 27, "nodeType": "774", "messageId": "775", "endLine": 2, "endColumn": 32}, {"ruleId": "772", "severity": 1, "message": "819", "line": 2, "column": 34, "nodeType": "774", "messageId": "775", "endLine": 2, "endColumn": 38}, {"ruleId": "789", "severity": 1, "message": "820", "line": 113, "column": 6, "nodeType": "791", "endLine": 113, "endColumn": 8, "suggestions": "821"}, {"ruleId": "772", "severity": 1, "message": "822", "line": 5, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 5, "endColumn": 14}, {"ruleId": "772", "severity": 1, "message": "823", "line": 31, "column": 9, "nodeType": "774", "messageId": "775", "endLine": 31, "endColumn": 17}, {"ruleId": "789", "severity": 1, "message": "824", "line": 241, "column": 6, "nodeType": "791", "endLine": 241, "endColumn": 35, "suggestions": "825"}, {"ruleId": "772", "severity": 1, "message": "826", "line": 12, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 12, "endColumn": 11}, {"ruleId": "772", "severity": 1, "message": "827", "line": 14, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 14, "endColumn": 10}, {"ruleId": "772", "severity": 1, "message": "828", "line": 16, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 16, "endColumn": 8}, {"ruleId": "772", "severity": 1, "message": "829", "line": 40, "column": 10, "nodeType": "774", "messageId": "775", "endLine": 40, "endColumn": 17}, {"ruleId": "789", "severity": 1, "message": "830", "line": 44, "column": 6, "nodeType": "791", "endLine": 44, "endColumn": 8, "suggestions": "831"}, {"ruleId": "772", "severity": 1, "message": "832", "line": 12, "column": 8, "nodeType": "774", "messageId": "775", "endLine": 12, "endColumn": 17}, {"ruleId": "772", "severity": 1, "message": "833", "line": 31, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 31, "endColumn": 9}, {"ruleId": "772", "severity": 1, "message": "834", "line": 43, "column": 19, "nodeType": "774", "messageId": "775", "endLine": 43, "endColumn": 29}, {"ruleId": "789", "severity": 1, "message": "835", "line": 182, "column": 6, "nodeType": "791", "endLine": 182, "endColumn": 8, "suggestions": "836"}, {"ruleId": "772", "severity": 1, "message": "777", "line": 8, "column": 10, "nodeType": "774", "messageId": "775", "endLine": 8, "endColumn": 21}, {"ruleId": "772", "severity": 1, "message": "776", "line": 8, "column": 23, "nodeType": "774", "messageId": "775", "endLine": 8, "endColumn": 34}, {"ruleId": "772", "severity": 1, "message": "837", "line": 28, "column": 10, "nodeType": "774", "messageId": "775", "endLine": 28, "endColumn": 29}, {"ruleId": "772", "severity": 1, "message": "838", "line": 33, "column": 9, "nodeType": "774", "messageId": "775", "endLine": 33, "endColumn": 17}, {"ruleId": "789", "severity": 1, "message": "839", "line": 85, "column": 6, "nodeType": "791", "endLine": 85, "endColumn": 8, "suggestions": "840"}, {"ruleId": "789", "severity": 1, "message": "841", "line": 139, "column": 6, "nodeType": "791", "endLine": 139, "endColumn": 24, "suggestions": "842"}, {"ruleId": "772", "severity": 1, "message": "843", "line": 1, "column": 38, "nodeType": "774", "messageId": "775", "endLine": 1, "endColumn": 46}, {"ruleId": "772", "severity": 1, "message": "844", "line": 8, "column": 12, "nodeType": "774", "messageId": "775", "endLine": 8, "endColumn": 19}, {"ruleId": "789", "severity": 1, "message": "845", "line": 58, "column": 8, "nodeType": "791", "endLine": 58, "endColumn": 10, "suggestions": "846"}, {"ruleId": "772", "severity": 1, "message": "847", "line": 13, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 13, "endColumn": 13}, {"ruleId": "772", "severity": 1, "message": "827", "line": 14, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 14, "endColumn": 10}, {"ruleId": "772", "severity": 1, "message": "848", "line": 15, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 15, "endColumn": 10}, {"ruleId": "772", "severity": 1, "message": "849", "line": 17, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 17, "endColumn": 13}, {"ruleId": "772", "severity": 1, "message": "850", "line": 35, "column": 10, "nodeType": "774", "messageId": "775", "endLine": 35, "endColumn": 18}, {"ruleId": "772", "severity": 1, "message": "851", "line": 35, "column": 20, "nodeType": "774", "messageId": "775", "endLine": 35, "endColumn": 31}, {"ruleId": "789", "severity": 1, "message": "852", "line": 137, "column": 6, "nodeType": "791", "endLine": 137, "endColumn": 8, "suggestions": "853"}, {"ruleId": "789", "severity": 1, "message": "854", "line": 141, "column": 6, "nodeType": "791", "endLine": 141, "endColumn": 60, "suggestions": "855"}, {"ruleId": "772", "severity": 1, "message": "856", "line": 2, "column": 18, "nodeType": "774", "messageId": "775", "endLine": 2, "endColumn": 33}, {"ruleId": "772", "severity": 1, "message": "857", "line": 21, "column": 53, "nodeType": "774", "messageId": "775", "endLine": 21, "endColumn": 67}, {"ruleId": "772", "severity": 1, "message": "788", "line": 24, "column": 8, "nodeType": "774", "messageId": "775", "endLine": 24, "endColumn": 29}, {"ruleId": "772", "severity": 1, "message": "850", "line": 75, "column": 10, "nodeType": "774", "messageId": "775", "endLine": 75, "endColumn": 18}, {"ruleId": "772", "severity": 1, "message": "851", "line": 75, "column": 20, "nodeType": "774", "messageId": "775", "endLine": 75, "endColumn": 31}, {"ruleId": "772", "severity": 1, "message": "858", "line": 76, "column": 10, "nodeType": "774", "messageId": "775", "endLine": 76, "endColumn": 19}, {"ruleId": "772", "severity": 1, "message": "859", "line": 76, "column": 21, "nodeType": "774", "messageId": "775", "endLine": 76, "endColumn": 33}, {"ruleId": "772", "severity": 1, "message": "860", "line": 77, "column": 10, "nodeType": "774", "messageId": "775", "endLine": 77, "endColumn": 24}, {"ruleId": "772", "severity": 1, "message": "861", "line": 80, "column": 10, "nodeType": "774", "messageId": "775", "endLine": 80, "endColumn": 27}, {"ruleId": "772", "severity": 1, "message": "862", "line": 82, "column": 10, "nodeType": "774", "messageId": "775", "endLine": 82, "endColumn": 24}, {"ruleId": "772", "severity": 1, "message": "863", "line": 90, "column": 9, "nodeType": "774", "messageId": "775", "endLine": 90, "endColumn": 18}, {"ruleId": "772", "severity": 1, "message": "864", "line": 91, "column": 9, "nodeType": "774", "messageId": "775", "endLine": 91, "endColumn": 23}, {"ruleId": "789", "severity": 1, "message": "865", "line": 896, "column": 6, "nodeType": "791", "endLine": 896, "endColumn": 8, "suggestions": "866"}, {"ruleId": "772", "severity": 1, "message": "867", "line": 917, "column": 9, "nodeType": "774", "messageId": "775", "endLine": 917, "endColumn": 24}, {"ruleId": "772", "severity": 1, "message": "868", "line": 1072, "column": 9, "nodeType": "774", "messageId": "775", "endLine": 1072, "endColumn": 29}, {"ruleId": "772", "severity": 1, "message": "869", "line": 1560, "column": 39, "nodeType": "774", "messageId": "775", "endLine": 1560, "endColumn": 48}, {"ruleId": "870", "severity": 1, "message": "871", "line": 2155, "column": 27, "nodeType": "872", "messageId": "873", "endLine": 2155, "endColumn": 28}, {"ruleId": "772", "severity": 1, "message": "874", "line": 17, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 17, "endColumn": 16}, {"ruleId": "772", "severity": 1, "message": "875", "line": 18, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 18, "endColumn": 11}, {"ruleId": "772", "severity": 1, "message": "876", "line": 25, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 25, "endColumn": 11}, {"ruleId": "772", "severity": 1, "message": "877", "line": 26, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 26, "endColumn": 11}, {"ruleId": "772", "severity": 1, "message": "878", "line": 27, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 27, "endColumn": 18}, {"ruleId": "772", "severity": 1, "message": "849", "line": 28, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 28, "endColumn": 13}, {"ruleId": "772", "severity": 1, "message": "828", "line": 29, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 29, "endColumn": 8}, {"ruleId": "772", "severity": 1, "message": "847", "line": 30, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 30, "endColumn": 13}, {"ruleId": "772", "severity": 1, "message": "879", "line": 31, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 31, "endColumn": 9}, {"ruleId": "772", "severity": 1, "message": "880", "line": 33, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 33, "endColumn": 14}, {"ruleId": "772", "severity": 1, "message": "782", "line": 34, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 34, "endColumn": 6}, {"ruleId": "772", "severity": 1, "message": "881", "line": 35, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 35, "endColumn": 18}, {"ruleId": "772", "severity": 1, "message": "882", "line": 36, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 36, "endColumn": 15}, {"ruleId": "772", "severity": 1, "message": "883", "line": 37, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 37, "endColumn": 10}, {"ruleId": "772", "severity": 1, "message": "811", "line": 45, "column": 11, "nodeType": "774", "messageId": "775", "endLine": 45, "endColumn": 12}, {"ruleId": "789", "severity": 1, "message": "884", "line": 68, "column": 9, "nodeType": "885", "endLine": 72, "endColumn": 29}, {"ruleId": "789", "severity": 1, "message": "886", "line": 251, "column": 6, "nodeType": "791", "endLine": 251, "endColumn": 48, "suggestions": "887"}, {"ruleId": "772", "severity": 1, "message": "888", "line": 18, "column": 10, "nodeType": "774", "messageId": "775", "endLine": 18, "endColumn": 31}, {"ruleId": "772", "severity": 1, "message": "889", "line": 18, "column": 33, "nodeType": "774", "messageId": "775", "endLine": 18, "endColumn": 43}, {"ruleId": "789", "severity": 1, "message": "890", "line": 779, "column": 6, "nodeType": "791", "endLine": 779, "endColumn": 81, "suggestions": "891"}, {"ruleId": "772", "severity": 1, "message": "892", "line": 3, "column": 10, "nodeType": "774", "messageId": "775", "endLine": 3, "endColumn": 16}, {"ruleId": "772", "severity": 1, "message": "856", "line": 3, "column": 18, "nodeType": "774", "messageId": "775", "endLine": 3, "endColumn": 33}, {"ruleId": "772", "severity": 1, "message": "893", "line": 32, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 32, "endColumn": 11}, {"ruleId": "772", "severity": 1, "message": "894", "line": 33, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 33, "endColumn": 13}, {"ruleId": "772", "severity": 1, "message": "811", "line": 46, "column": 11, "nodeType": "774", "messageId": "775", "endLine": 46, "endColumn": 12}, {"ruleId": "772", "severity": 1, "message": "812", "line": 46, "column": 27, "nodeType": "774", "messageId": "775", "endLine": 46, "endColumn": 39}, {"ruleId": "772", "severity": 1, "message": "895", "line": 71, "column": 10, "nodeType": "774", "messageId": "775", "endLine": 71, "endColumn": 22}, {"ruleId": "772", "severity": 1, "message": "896", "line": 71, "column": 24, "nodeType": "774", "messageId": "775", "endLine": 71, "endColumn": 39}, {"ruleId": "772", "severity": 1, "message": "897", "line": 312, "column": 9, "nodeType": "774", "messageId": "775", "endLine": 312, "endColumn": 23}, {"ruleId": "772", "severity": 1, "message": "898", "line": 9, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 9, "endColumn": 9}, {"ruleId": "772", "severity": 1, "message": "899", "line": 15, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 15, "endColumn": 15}, {"ruleId": "772", "severity": 1, "message": "900", "line": 21, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 21, "endColumn": 10}, {"ruleId": "772", "severity": 1, "message": "901", "line": 22, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 22, "endColumn": 15}, {"ruleId": "772", "severity": 1, "message": "811", "line": 29, "column": 11, "nodeType": "774", "messageId": "775", "endLine": 29, "endColumn": 12}, {"ruleId": "772", "severity": 1, "message": "902", "line": 69, "column": 9, "nodeType": "774", "messageId": "775", "endLine": 69, "endColumn": 21}, {"ruleId": "772", "severity": 1, "message": "903", "line": 1, "column": 38, "nodeType": "774", "messageId": "775", "endLine": 1, "endColumn": 44}, {"ruleId": "772", "severity": 1, "message": "904", "line": 4, "column": 40, "nodeType": "774", "messageId": "775", "endLine": 4, "endColumn": 46}, {"ruleId": "772", "severity": 1, "message": "832", "line": 5, "column": 8, "nodeType": "774", "messageId": "775", "endLine": 5, "endColumn": 17}, {"ruleId": "772", "severity": 1, "message": "788", "line": 9, "column": 8, "nodeType": "774", "messageId": "775", "endLine": 9, "endColumn": 29}, {"ruleId": "772", "severity": 1, "message": "905", "line": 19, "column": 8, "nodeType": "774", "messageId": "775", "endLine": 19, "endColumn": 13}, {"ruleId": "772", "severity": 1, "message": "906", "line": 25, "column": 11, "nodeType": "774", "messageId": "775", "endLine": 25, "endColumn": 15}, {"ruleId": "772", "severity": 1, "message": "811", "line": 26, "column": 11, "nodeType": "774", "messageId": "775", "endLine": 26, "endColumn": 12}, {"ruleId": "772", "severity": 1, "message": "907", "line": 26, "column": 14, "nodeType": "774", "messageId": "775", "endLine": 26, "endColumn": 25}, {"ruleId": "772", "severity": 1, "message": "908", "line": 66, "column": 10, "nodeType": "774", "messageId": "775", "endLine": 66, "endColumn": 20}, {"ruleId": "789", "severity": 1, "message": "909", "line": 142, "column": 6, "nodeType": "791", "endLine": 142, "endColumn": 26, "suggestions": "910"}, {"ruleId": "789", "severity": 1, "message": "911", "line": 174, "column": 6, "nodeType": "791", "endLine": 174, "endColumn": 8, "suggestions": "912"}, {"ruleId": "789", "severity": 1, "message": "913", "line": 377, "column": 6, "nodeType": "791", "endLine": 377, "endColumn": 20, "suggestions": "914"}, {"ruleId": "772", "severity": 1, "message": "832", "line": 3, "column": 8, "nodeType": "774", "messageId": "775", "endLine": 3, "endColumn": 17}, {"ruleId": "772", "severity": 1, "message": "819", "line": 9, "column": 10, "nodeType": "774", "messageId": "775", "endLine": 9, "endColumn": 14}, {"ruleId": "772", "severity": 1, "message": "818", "line": 9, "column": 32, "nodeType": "774", "messageId": "775", "endLine": 9, "endColumn": 37}, {"ruleId": "772", "severity": 1, "message": "915", "line": 9, "column": 39, "nodeType": "774", "messageId": "775", "endLine": 9, "endColumn": 45}, {"ruleId": "772", "severity": 1, "message": "811", "line": 18, "column": 11, "nodeType": "774", "messageId": "775", "endLine": 18, "endColumn": 12}, {"ruleId": "772", "severity": 1, "message": "812", "line": 18, "column": 27, "nodeType": "774", "messageId": "775", "endLine": 18, "endColumn": 39}, {"ruleId": "772", "severity": 1, "message": "916", "line": 21, "column": 10, "nodeType": "774", "messageId": "775", "endLine": 21, "endColumn": 21}, {"ruleId": "772", "severity": 1, "message": "917", "line": 24, "column": 10, "nodeType": "774", "messageId": "775", "endLine": 24, "endColumn": 22}, {"ruleId": "789", "severity": 1, "message": "918", "line": 113, "column": 6, "nodeType": "791", "endLine": 113, "endColumn": 32, "suggestions": "919"}, {"ruleId": "789", "severity": 1, "message": "911", "line": 147, "column": 6, "nodeType": "791", "endLine": 147, "endColumn": 8, "suggestions": "920"}, {"ruleId": "772", "severity": 1, "message": "921", "line": 257, "column": 9, "nodeType": "774", "messageId": "775", "endLine": 257, "endColumn": 32}, {"ruleId": "772", "severity": 1, "message": "922", "line": 305, "column": 9, "nodeType": "774", "messageId": "775", "endLine": 305, "endColumn": 26}, {"ruleId": "789", "severity": 1, "message": "911", "line": 329, "column": 6, "nodeType": "791", "endLine": 329, "endColumn": 8, "suggestions": "923"}, {"ruleId": "789", "severity": 1, "message": "924", "line": 336, "column": 6, "nodeType": "791", "endLine": 336, "endColumn": 19, "suggestions": "925"}, {"ruleId": "772", "severity": 1, "message": "926", "line": 12, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 12, "endColumn": 10}, {"ruleId": "772", "severity": 1, "message": "927", "line": 17, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 17, "endColumn": 15}, {"ruleId": "772", "severity": 1, "message": "906", "line": 27, "column": 11, "nodeType": "774", "messageId": "775", "endLine": 27, "endColumn": 15}, {"ruleId": "772", "severity": 1, "message": "811", "line": 28, "column": 11, "nodeType": "774", "messageId": "775", "endLine": 28, "endColumn": 12}, {"ruleId": "928", "severity": 1, "message": "929", "line": 274, "column": 21, "nodeType": "930", "messageId": "931", "endLine": 280, "endColumn": 24}, {"ruleId": "772", "severity": 1, "message": "926", "line": 11, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 11, "endColumn": 10}, {"ruleId": "772", "severity": 1, "message": "785", "line": 18, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 18, "endColumn": 13}, {"ruleId": "772", "severity": 1, "message": "838", "line": 29, "column": 9, "nodeType": "774", "messageId": "775", "endLine": 29, "endColumn": 17}, {"ruleId": "772", "severity": 1, "message": "906", "line": 9, "column": 11, "nodeType": "774", "messageId": "775", "endLine": 9, "endColumn": 15}, {"ruleId": "772", "severity": 1, "message": "811", "line": 10, "column": 11, "nodeType": "774", "messageId": "775", "endLine": 10, "endColumn": 12}, {"ruleId": "789", "severity": 1, "message": "932", "line": 40, "column": 6, "nodeType": "791", "endLine": 40, "endColumn": 8, "suggestions": "933"}, {"ruleId": "789", "severity": 1, "message": "934", "line": 164, "column": 6, "nodeType": "791", "endLine": 164, "endColumn": 18, "suggestions": "935"}, {"ruleId": "789", "severity": 1, "message": "936", "line": 172, "column": 6, "nodeType": "791", "endLine": 172, "endColumn": 31, "suggestions": "937"}, {"ruleId": "772", "severity": 1, "message": "938", "line": 19, "column": 11, "nodeType": "774", "messageId": "775", "endLine": 19, "endColumn": 24}, {"ruleId": "939", "severity": 1, "message": "940", "line": 73, "column": 111, "nodeType": "941", "messageId": "942", "endLine": 73, "endColumn": 112, "suggestions": "943"}, {"ruleId": "939", "severity": 1, "message": "940", "line": 95, "column": 89, "nodeType": "941", "messageId": "942", "endLine": 95, "endColumn": 90, "suggestions": "944"}, {"ruleId": "772", "severity": 1, "message": "883", "line": 6, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 6, "endColumn": 10}, {"ruleId": "772", "severity": 1, "message": "782", "line": 7, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 7, "endColumn": 6}, {"ruleId": "772", "severity": 1, "message": "785", "line": 8, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 8, "endColumn": 13}, {"ruleId": "772", "severity": 1, "message": "945", "line": 9, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 9, "endColumn": 10}, {"ruleId": "789", "severity": 1, "message": "946", "line": 77, "column": 6, "nodeType": "791", "endLine": 77, "endColumn": 14, "suggestions": "947"}, {"ruleId": "789", "severity": 1, "message": "948", "line": 30, "column": 6, "nodeType": "791", "endLine": 30, "endColumn": 16, "suggestions": "949"}, {"ruleId": "772", "severity": 1, "message": "950", "line": 166, "column": 9, "nodeType": "774", "messageId": "775", "endLine": 166, "endColumn": 21}, {"ruleId": "789", "severity": 1, "message": "951", "line": 14, "column": 6, "nodeType": "791", "endLine": 14, "endColumn": 21, "suggestions": "952"}, {"ruleId": "772", "severity": 1, "message": "953", "line": 23, "column": 10, "nodeType": "774", "messageId": "775", "endLine": 23, "endColumn": 27}, {"ruleId": "772", "severity": 1, "message": "954", "line": 10, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 10, "endColumn": 10}, {"ruleId": "772", "severity": 1, "message": "955", "line": 56, "column": 9, "nodeType": "774", "messageId": "775", "endLine": 56, "endColumn": 22}, {"ruleId": "772", "severity": 1, "message": "828", "line": 11, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 11, "endColumn": 8}, {"ruleId": "772", "severity": 1, "message": "956", "line": 12, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 12, "endColumn": 10}, {"ruleId": "772", "severity": 1, "message": "957", "line": 13, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 13, "endColumn": 9}, {"ruleId": "772", "severity": 1, "message": "958", "line": 1, "column": 38, "nodeType": "774", "messageId": "775", "endLine": 1, "endColumn": 46}, {"ruleId": "772", "severity": 1, "message": "959", "line": 112, "column": 23, "nodeType": "774", "messageId": "775", "endLine": 112, "endColumn": 34}, {"ruleId": "772", "severity": 1, "message": "906", "line": 7, "column": 11, "nodeType": "774", "messageId": "775", "endLine": 7, "endColumn": 15}, {"ruleId": "772", "severity": 1, "message": "892", "line": 2, "column": 10, "nodeType": "774", "messageId": "775", "endLine": 2, "endColumn": 16}, {"ruleId": "960", "severity": 1, "message": "961", "line": 69, "column": 3, "nodeType": "962", "messageId": "963", "endLine": 90, "endColumn": 5}, {"ruleId": "772", "severity": 1, "message": "964", "line": 198, "column": 10, "nodeType": "774", "messageId": "775", "endLine": 198, "endColumn": 22}, {"ruleId": "772", "severity": 1, "message": "965", "line": 20, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 20, "endColumn": 8}, {"ruleId": "772", "severity": 1, "message": "966", "line": 21, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 21, "endColumn": 11}, {"ruleId": "772", "severity": 1, "message": "875", "line": 22, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 22, "endColumn": 11}, {"ruleId": "789", "severity": 1, "message": "967", "line": 95, "column": 6, "nodeType": "791", "endLine": 95, "endColumn": 15, "suggestions": "968"}, {"ruleId": "772", "severity": 1, "message": "969", "line": 128, "column": 5, "nodeType": "774", "messageId": "775", "endLine": 128, "endColumn": 14}, {"ruleId": "772", "severity": 1, "message": "892", "line": 2, "column": 10, "nodeType": "774", "messageId": "775", "endLine": 2, "endColumn": 16}, {"ruleId": "772", "severity": 1, "message": "856", "line": 2, "column": 18, "nodeType": "774", "messageId": "775", "endLine": 2, "endColumn": 33}, {"ruleId": "772", "severity": 1, "message": "970", "line": 15, "column": 9, "nodeType": "774", "messageId": "775", "endLine": 15, "endColumn": 21}, {"ruleId": "772", "severity": 1, "message": "971", "line": 22, "column": 9, "nodeType": "774", "messageId": "775", "endLine": 22, "endColumn": 21}, {"ruleId": "772", "severity": 1, "message": "972", "line": 66, "column": 9, "nodeType": "774", "messageId": "775", "endLine": 66, "endColumn": 22}, {"ruleId": "772", "severity": 1, "message": "973", "line": 86, "column": 9, "nodeType": "774", "messageId": "775", "endLine": 86, "endColumn": 24}, {"ruleId": "789", "severity": 1, "message": "974", "line": 270, "column": 6, "nodeType": "791", "endLine": 270, "endColumn": 19, "suggestions": "975"}, {"ruleId": "789", "severity": 1, "message": "976", "line": 251, "column": 6, "nodeType": "791", "endLine": 251, "endColumn": 32, "suggestions": "977", "suppressions": "978"}, {"ruleId": "772", "severity": 1, "message": "815", "line": 7, "column": 9, "nodeType": "774", "messageId": "775", "endLine": 7, "endColumn": 19}, {"ruleId": "772", "severity": 1, "message": "979", "line": 6, "column": 10, "nodeType": "774", "messageId": "775", "endLine": 6, "endColumn": 24}, {"ruleId": "772", "severity": 1, "message": "980", "line": 7, "column": 8, "nodeType": "774", "messageId": "775", "endLine": 7, "endColumn": 21}, {"ruleId": "772", "severity": 1, "message": "981", "line": 9, "column": 10, "nodeType": "774", "messageId": "775", "endLine": 9, "endColumn": 17}, {"ruleId": "772", "severity": 1, "message": "837", "line": 21, "column": 10, "nodeType": "774", "messageId": "775", "endLine": 21, "endColumn": 29}, {"ruleId": "772", "severity": 1, "message": "982", "line": 22, "column": 10, "nodeType": "774", "messageId": "775", "endLine": 22, "endColumn": 22}, {"ruleId": "772", "severity": 1, "message": "983", "line": 68, "column": 9, "nodeType": "774", "messageId": "775", "endLine": 68, "endColumn": 35}, {"ruleId": "772", "severity": 1, "message": "984", "line": 78, "column": 9, "nodeType": "774", "messageId": "775", "endLine": 78, "endColumn": 23}, {"ruleId": "772", "severity": 1, "message": "985", "line": 104, "column": 13, "nodeType": "774", "messageId": "775", "endLine": 104, "endColumn": 26}, {"ruleId": "772", "severity": 1, "message": "926", "line": 12, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 12, "endColumn": 10}, {"ruleId": "772", "severity": 1, "message": "785", "line": 14, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 14, "endColumn": 13}, {"ruleId": "772", "severity": 1, "message": "986", "line": 62, "column": 9, "nodeType": "774", "messageId": "775", "endLine": 62, "endColumn": 32}, {"ruleId": "772", "severity": 1, "message": "987", "line": 5, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 5, "endColumn": 10}, {"ruleId": "772", "severity": 1, "message": "848", "line": 12, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 12, "endColumn": 10}, {"ruleId": "772", "severity": 1, "message": "988", "line": 13, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 13, "endColumn": 12}, {"ruleId": "870", "severity": 1, "message": "989", "line": 235, "column": 3, "nodeType": "872", "messageId": "873", "endLine": 235, "endColumn": 17}, {"ruleId": "772", "severity": 1, "message": "811", "line": 23, "column": 11, "nodeType": "774", "messageId": "775", "endLine": 23, "endColumn": 12}, {"ruleId": "772", "severity": 1, "message": "990", "line": 39, "column": 10, "nodeType": "774", "messageId": "775", "endLine": 39, "endColumn": 20}, {"ruleId": "772", "severity": 1, "message": "965", "line": 8, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 8, "endColumn": 8}, {"ruleId": "772", "severity": 1, "message": "991", "line": 10, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 10, "endColumn": 9}, {"ruleId": "772", "severity": 1, "message": "966", "line": 12, "column": 3, "nodeType": "774", "messageId": "775", "endLine": 12, "endColumn": 11}, {"ruleId": "772", "severity": 1, "message": "992", "line": 3, "column": 10, "nodeType": "774", "messageId": "775", "endLine": 3, "endColumn": 17}, {"ruleId": "772", "severity": 1, "message": "811", "line": 25, "column": 11, "nodeType": "774", "messageId": "775", "endLine": 25, "endColumn": 12}, "no-unused-vars", "'startTransition' is defined but never used.", "Identifier", "unusedVar", "'HideLoading' is defined but never used.", "'ShowLoading' is defined but never used.", "'AdminNavigation' is defined but never used.", "'TbHome' is defined but never used.", "'TbBrandTanzania' is defined but never used.", "'TbMenu2' is defined but never used.", "'TbX' is defined but never used.", "'TbChevronDown' is defined but never used.", "'TbLogout' is defined but never used.", "'TbSettings' is defined but never used.", "'TbBell' is defined but never used.", "'TbStar' is defined but never used.", "'OnlineStatusIndicator' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'dispatch', 'getUserData', 'navigate', and 'user'. Either include them or remove the dependency array.", "ArrayExpression", ["993"], "React Hook useEffect has missing dependencies: 'dispatch' and 'verifyPaymentStatus'. Either include them or remove the dependency array.", ["994"], "React Hook useEffect has a missing dependency: 'verifyPaymentStatus'. Either include it or remove the dependency array.", ["995"], "'getButtonClass' is assigned a value but never used.", "'Select' is defined but never used.", "React Hook useEffect has missing dependencies: 'getExamData' and 'params.id'. Either include them or remove the dependency array.", ["996"], "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["997"], ["998"], "React Hook useCallback has missing dependencies: 'quiz.category', 'quiz.name', 'quiz.passingMarks', 'quiz.passingPercentage', 'quiz.subject', and 'submitting'. Either include them or remove the dependency array.", ["999"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'TbBrain' is defined but never used.", "'TbBolt' is defined but never used.", "'t' is assigned a value but never used.", "'getClassName' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'getUserResults' and 'user'. Either include them or remove the dependency array.", ["1000"], "'formatTime' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getExamsData'. Either include it or remove the dependency array.", ["1001"], "'Input' is defined but never used.", "'Form' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchQuestions'. Either include it or remove the dependency array.", ["1002"], "'TbDashboard' is defined but never used.", "'navigate' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'getData' and 'pagination'. Either include them or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["1003"], "'TbTarget' is defined but never used.", "'TbClock' is defined but never used.", "'TbEye' is defined but never used.", "'loading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.", ["1004"], "'PageTitle' is defined but never used.", "'TbPlus' is defined but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getUsersData'. Either include it or remove the dependency array.", ["1005"], "'processingStartTime' is assigned a value but never used.", "'dispatch' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPlans'. Either include it or remove the dependency array.", ["1006"], "React Hook useEffect has a missing dependency: 'isSubscriptionExpired'. Either include it or remove the dependency array.", ["1007"], "'Suspense' is defined but never used.", "'isAdmin' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'dispatch' and 'getUserData'. Either include them or remove the dependency array.", ["1008"], "'TbCalendar' is defined but never used.", "'TbAward' is defined but never used.", "'TbDownload' is defined but never used.", "'viewMode' is assigned a value but never used.", "'setViewMode' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getData'. Either include it or remove the dependency array.", ["1009"], "React Hook useEffect has a missing dependency: 'applyFilters'. Either include it or remove the dependency array.", ["1010"], "'AnimatePresence' is defined but never used.", "'getUserRanking' is defined but never used.", "'showStats' is assigned a value but never used.", "'setShowStats' is assigned a value but never used.", "'animationPhase' is assigned a value but never used.", "'currentUserLeague' is assigned a value but never used.", "'showLeagueView' is assigned a value but never used.", "'headerRef' is assigned a value but never used.", "'currentUserRef' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchFullUserData', 'fetchRankingData', 'motivationalQuotes', 'rankingData', and 'user'. Either include them or remove the dependency array.", ["1011"], "'otherPerformers' is assigned a value but never used.", "'getSubscriptionBadge' is assigned a value but never used.", "'leagueKey' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'y'.", "ObjectExpression", "unexpected", "'FaChevronDown' is defined but never used.", "'FaSearch' is defined but never used.", "'TbSearch' is defined but never used.", "'TbFilter' is defined but never used.", "'TbSortAscending' is defined but never used.", "'TbUser' is defined but never used.", "'TbChevronUp' is defined but never used.", "'TbAlertTriangle' is defined but never used.", "'TbInfoCircle' is defined but never used.", "'TbCheck' is defined but never used.", "The 'allPossibleClasses' conditional could make the dependencies of useCallback Hook (at line 118) change on every render. Move it inside the useCallback callback. Alternatively, wrap the initialization of 'allPossibleClasses' in its own useMemo() Hook.", "VariableDeclarator", "React Hook useMemo has an unnecessary dependency: 'activeTab'. Either exclude it or remove the dependency array.", ["1012"], "'extractUserResultData' is defined but never used.", "'safeNumber' is defined but never used.", "React Hook useCallback has a missing dependency: 'startTime'. Either include it or remove the dependency array.", ["1013"], "'motion' is defined but never used.", "'FaExpand' is assigned a value but never used.", "'FaCompress' is assigned a value but never used.", "'showComments' is assigned a value but never used.", "'setShowComments' is assigned a value but never used.", "'handleClearAll' is assigned a value but never used.", "'FaHome' is defined but never used.", "'FaCreditCard' is defined but never used.", "'FaRobot' is defined but never used.", "'FaSignOutAlt' is defined but never used.", "'handleLogout' is assigned a value but never used.", "'useRef' is defined but never used.", "'Avatar' is defined but never used.", "'image' is defined but never used.", "'user' is assigned a value but never used.", "'isKiswahili' is assigned a value but never used.", "'totalPages' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'clearAllForumCaches' and 'fetchQuestions'. Either include them or remove the dependency array.", ["1014"], "React Hook useEffect has a missing dependency: 'getUserData'. Either include it or remove the dependency array.", ["1015"], "React Hook useEffect has a missing dependency: 'form2'. Either include it or remove the dependency array.", ["1016"], "'Button' is defined but never used.", "'userRanking' is assigned a value but never used.", "'imagePreview' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getUserStats'. Either include it or remove the dependency array.", ["1017"], ["1018"], "'handleLevelChangeCancel' is assigned a value but never used.", "'handleImageUpload' is assigned a value but never used.", ["1019"], "React Hook useEffect has a missing dependency: 'fetchUserRankingData'. Either include it or remove the dependency array.", ["1020"], "'TbRobot' is defined but never used.", "'TbCreditCard' is defined but never used.", "react/jsx-no-duplicate-props", "No duplicate props allowed", "JSXAttribute", "noDuplicateProps", "React Hook useEffect has a missing dependency: 'clearChat'. Either include it or remove the dependency array.", ["1021"], "React Hook React.useEffect has a missing dependency: 'isOpen'. Either include it or remove the dependency array.", ["1022"], "React Hook React.useEffect has a missing dependency: 'getInitialMessage'. Either include it or remove the dependency array.", ["1023"], "'restoredLines' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\[.", "Literal", "unnecessaryEscape", ["1024", "1025"], ["1026", "1027"], "'TbTrash' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchNotifications' and 'notifications.length'. Either include them or remove the dependency array.", ["1028"], "React Hook useEffect has a missing dependency: 'handleSubmitQuiz'. Either include it or remove the dependency array.", ["1029"], "'goToQuestion' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchTrialQuiz'. Either include it or remove the dependency array.", ["1030"], "'animationComplete' is assigned a value but never used.", "'TbUsers' is defined but never used.", "'getTimerColor' is assigned a value but never used.", "'TbPhoto' is defined but never used.", "'TbEdit' is defined but never used.", "'Fragment' is defined but never used.", "'toggleTheme' is assigned a value but never used.", "no-unreachable", "Unreachable code.", "ReturnStatement", "unreachableCode", "'containerRef' is assigned a value but never used.", "'FaEye' is defined but never used.", "'FaFilter' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchMaterials'. Either include it or remove the dependency array.", ["1031"], "'xpAwarded' is assigned a value but never used.", "'levelOptions' is assigned a value but never used.", "'classOptions' is assigned a value but never used.", "'modalVariants' is assigned a value but never used.", "'overlayVariants' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleTextSelection'. Either include it or remove the dependency array.", ["1032"], "React Hook useEffect has a missing dependency: 'renderPDF'. Either include it or remove the dependency array.", ["1033"], ["1034"], "'updateUserInfo' is defined but never used.", "'axiosInstance' is defined but never used.", "'SetUser' is defined but never used.", "'showTryAgain' is assigned a value but never used.", "'handleCloseProcessingModal' is assigned a value but never used.", "'handleTryAgain' is assigned a value but never used.", "'tryAgainTimer' is assigned a value but never used.", "'triggerLevelUpAnimation' is assigned a value but never used.", "'TbMedal' is defined but never used.", "'TbDiamond' is defined but never used.", "Duplicate key 'studyMaterials'.", "'videoError' is assigned a value but never used.", "'FaStar' is defined but never used.", "'message' is defined but never used.", {"desc": "1035", "fix": "1036"}, {"desc": "1037", "fix": "1038"}, {"desc": "1039", "fix": "1040"}, {"desc": "1041", "fix": "1042"}, {"desc": "1043", "fix": "1044"}, {"kind": "1045", "justification": "1046"}, {"desc": "1047", "fix": "1048"}, {"desc": "1049", "fix": "1050"}, {"desc": "1051", "fix": "1052"}, {"desc": "1053", "fix": "1054"}, {"desc": "1055", "fix": "1056"}, {"desc": "1057", "fix": "1058"}, {"desc": "1059", "fix": "1060"}, {"desc": "1061", "fix": "1062"}, {"desc": "1063", "fix": "1064"}, {"desc": "1065", "fix": "1066"}, {"desc": "1067", "fix": "1068"}, {"desc": "1069", "fix": "1070"}, {"desc": "1071", "fix": "1072"}, {"desc": "1073", "fix": "1074"}, {"desc": "1075", "fix": "1076"}, {"desc": "1077", "fix": "1078"}, {"desc": "1079", "fix": "1080"}, {"desc": "1081", "fix": "1082"}, {"desc": "1083", "fix": "1084"}, {"desc": "1079", "fix": "1085"}, {"desc": "1079", "fix": "1086"}, {"desc": "1087", "fix": "1088"}, {"desc": "1089", "fix": "1090"}, {"desc": "1091", "fix": "1092"}, {"desc": "1093", "fix": "1094"}, {"messageId": "1095", "fix": "1096", "desc": "1097"}, {"messageId": "1098", "fix": "1099", "desc": "1100"}, {"messageId": "1095", "fix": "1101", "desc": "1097"}, {"messageId": "1098", "fix": "1102", "desc": "1100"}, {"desc": "1103", "fix": "1104"}, {"desc": "1105", "fix": "1106"}, {"desc": "1107", "fix": "1108"}, {"desc": "1109", "fix": "1110"}, {"desc": "1111", "fix": "1112"}, {"desc": "1113", "fix": "1114"}, {"kind": "1045", "justification": "1046"}, "Update the dependencies array to be: [dispatch, getUserData, navigate, user]", {"range": "1115", "text": "1116"}, "Update the dependencies array to be: [dispatch, paymentVerificationNeeded, user, verifyPaymentStatus]", {"range": "1117", "text": "1118"}, "Update the dependencies array to be: [user, activeRoute, verifyPaymentStatus]", {"range": "1119", "text": "1120"}, "Update the dependencies array to be: [getExamData, params.id]", {"range": "1121", "text": "1122"}, "Update the dependencies array to be: [fetchUsers]", {"range": "1123", "text": "1124"}, "directive", "", "Update the dependencies array to be: [submitting, user, startTime, questions, quiz.passingMarks, quiz.passingPercentage, quiz.name, quiz.subject, quiz.category, id, navigate, answers]", {"range": "1125", "text": "1126"}, "Update the dependencies array to be: [getUserResults, user]", {"range": "1127", "text": "1128"}, "Update the dependencies array to be: [getExamsData]", {"range": "1129", "text": "1130"}, "Update the dependencies array to be: [fetchQuestions]", {"range": "1131", "text": "1132"}, "Update the dependencies array to be: [filters, getData, pagination]", {"range": "1133", "text": "1134"}, "Update the dependencies array to be: [fetchDashboardData]", {"range": "1135", "text": "1136"}, "Update the dependencies array to be: [getUsersData]", {"range": "1137", "text": "1138"}, "Update the dependencies array to be: [fetchPlans]", {"range": "1139", "text": "1140"}, "Update the dependencies array to be: [isSubscriptionExpired, subscriptionData]", {"range": "1141", "text": "1142"}, "Update the dependencies array to be: [dispatch, getUserData]", {"range": "1143", "text": "1144"}, "Update the dependencies array to be: [getData]", {"range": "1145", "text": "1146"}, "Update the dependencies array to be: [filterSubject, filterVerdict, dateRange, reportsData, applyFilters]", {"range": "1147", "text": "1148"}, "Update the dependencies array to be: [fetchFullUserData, fetchRankingData, motivationalQuotes, rankingData, user]", {"range": "1149", "text": "1150"}, "Update the dependencies array to be: [materials, searchTerm, sortBy]", {"range": "1151", "text": "1152"}, "Update the dependencies array to be: [user, dispatch, questions, startTime, examData?.duration, examData.passingMarks, params.id, navigate, selectedOptions]", {"range": "1153", "text": "1154"}, "Update the dependencies array to be: [clearAllForumCaches, currentPage, fetchQuestions, limit]", {"range": "1155", "text": "1156"}, "Update the dependencies array to be: [getUserData]", {"range": "1157", "text": "1158"}, "Update the dependencies array to be: [editQuestion, form2]", {"range": "1159", "text": "1160"}, "Update the dependencies array to be: [getUserStats, rankingData, userDetails]", {"range": "1161", "text": "1162"}, {"range": "1163", "text": "1158"}, {"range": "1164", "text": "1158"}, "Update the dependencies array to be: [fetchUserRankingData, userDetails]", {"range": "1165", "text": "1166"}, "Update the dependencies array to be: [clearChat]", {"range": "1167", "text": "1168"}, "Update the dependencies array to be: [isOpen, pdfContext]", {"range": "1169", "text": "1170"}, "Update the dependencies array to be: [pdfContext, isKiswahili, getInitialMessage]", {"range": "1171", "text": "1172"}, "removeEscape", {"range": "1173", "text": "1046"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "1174", "text": "1175"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "1176", "text": "1046"}, {"range": "1177", "text": "1175"}, "Update the dependencies array to be: [fetchNotifications, isOpen, notifications.length]", {"range": "1178", "text": "1179"}, "Update the dependencies array to be: [handleSubmitQuiz, timeLeft]", {"range": "1180", "text": "1181"}, "Update the dependencies array to be: [fetchTrialQuiz, trialUserInfo]", {"range": "1182", "text": "1183"}, "Update the dependencies array to be: [fetchMaterials, filters]", {"range": "1184", "text": "1185"}, "Update the dependencies array to be: [handleTextSelection, modalIsOpen]", {"range": "1186", "text": "1187"}, "Update the dependencies array to be: [modalIsOpen, documentUrl, renderPDF]", {"range": "1188", "text": "1189"}, [4456, 4458], "[dispatch, getUserData, navigate, user]", [7410, 7443], "[dispatch, paymentVerificationNeeded, user, verifyPaymentStatus]", [7953, 7972], "[user, activeRoute, verifyPaymentStatus]", [3399, 3401], "[getExamData, params.id]", [1327, 1329], "[fetchUsers]", [11677, 11728], "[submitting, user, startTime, questions, quiz.passingMarks, quiz.passingPercentage, quiz.name, quiz.subject, quiz.category, id, navigate, answers]", [9239, 9241], "[getUser<PERSON><PERSON><PERSON><PERSON>, user]", [5318, 5320], "[getExamsData]", [3074, 3076], "[fetchQuestions]", [7040, 7069], "[filters, getData, pagination]", [1196, 1198], "[fetchDashboardData]", [5587, 5589], "[getUsersData]", [3149, 3151], "[fetchPlans]", [4869, 4887], "[isSubscriptionExpired, subscriptionData]", [1990, 1992], "[dispatch, getUserData]", [4129, 4131], "[getData]", [4184, 4238], "[filterSubject, filterVerdict, dateRange, reportsData, applyFilters]", [34517, 34519], "[fetchFullUserData, fetchRankingData, motivationalQuotes, rankingData, user]", [8198, 8240], "[materials, searchTerm, sortBy]", [28864, 28939], "[user, dispatch, questions, startTime, examData?.duration, examData.passingMarks, params.id, navigate, selectedOptions]", [5863, 5883], "[clearAllForumCaches, currentPage, fetchQuestions, limit]", [6631, 6633], "[getUserData]", [12509, 12523], "[editQuestion, form2]", [3855, 3881], "[getUserStats, rankingData, userDetails]", [4823, 4825], [10390, 10392], [10542, 10555], "[fetchUserRankingData, userDetails]", [1628, 1630], "[clearChat]", [5789, 5801], "[is<PERSON><PERSON>, pdfContext]", [6050, 6075], "[pdfContext, isKiswahili, getInitialMessage]", [3500, 3501], [3500, 3500], "\\", [5011, 5012], [5011, 5011], [2302, 2310], "[fetchNotifications, isOpen, notifications.length]", [1032, 1042], "[handleSubmitQuiz, timeLeft]", [527, 542], "[fetchTrialQuiz, trialUserInfo]", [2411, 2420], "[fetchMaterials, filters]", [9166, 9179], "[handleTextSelection, modalIsOpen]", [8539, 8565], "[modalIsOpen, documentUrl, renderPDF]"]