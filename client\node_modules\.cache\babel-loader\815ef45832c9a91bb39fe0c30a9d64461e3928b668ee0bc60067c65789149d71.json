{"ast": null, "code": "import React from 'react';\nimport ReactDOM from 'react-dom';\nexport function isDOM(node) {\n  // https://developer.mozilla.org/en-US/docs/Web/API/Element\n  // Since XULElement is also subclass of Element, we only need HTMLElement and SVGElement\n  return node instanceof HTMLElement || node instanceof SVGElement;\n}\n\n/**\n * Return if a node is a DOM node. Else will return by `findDOMNode`\n */\nexport default function findDOMNode(node) {\n  if (isDOM(node)) {\n    return node;\n  }\n  if (node instanceof React.Component) {\n    return ReactDOM.findDOMNode(node);\n  }\n  return null;\n}", "map": {"version": 3, "names": ["React", "ReactDOM", "isDOM", "node", "HTMLElement", "SVGElement", "findDOMNode", "Component"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-util/es/Dom/findDOMNode.js"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom';\nexport function isDOM(node) {\n  // https://developer.mozilla.org/en-US/docs/Web/API/Element\n  // Since XULElement is also subclass of Element, we only need HTMLElement and SVGElement\n  return node instanceof HTMLElement || node instanceof SVGElement;\n}\n\n/**\n * Return if a node is a DOM node. Else will return by `findDOMNode`\n */\nexport default function findDOMNode(node) {\n  if (isDOM(node)) {\n    return node;\n  }\n  if (node instanceof React.Component) {\n    return ReactDOM.findDOMNode(node);\n  }\n  return null;\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,WAAW;AAChC,OAAO,SAASC,KAAKA,CAACC,IAAI,EAAE;EAC1B;EACA;EACA,OAAOA,IAAI,YAAYC,WAAW,IAAID,IAAI,YAAYE,UAAU;AAClE;;AAEA;AACA;AACA;AACA,eAAe,SAASC,WAAWA,CAACH,IAAI,EAAE;EACxC,IAAID,KAAK,CAACC,IAAI,CAAC,EAAE;IACf,OAAOA,IAAI;EACb;EACA,IAAIA,IAAI,YAAYH,KAAK,CAACO,SAAS,EAAE;IACnC,OAAON,QAAQ,CAACK,WAAW,CAACH,IAAI,CAAC;EACnC;EACA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}