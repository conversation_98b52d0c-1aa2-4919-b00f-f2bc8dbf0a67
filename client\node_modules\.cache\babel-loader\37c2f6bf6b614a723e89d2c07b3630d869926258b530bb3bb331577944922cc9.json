{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\contexts\\\\LanguageContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { useSelector } from 'react-redux';\nimport { getKiswahiliTranslation, isKiswahiliMode } from '../localization/kiswahili';\n\n// Create Language Context\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LanguageContext = /*#__PURE__*/createContext();\n\n// Language Provider Component\nexport const LanguageProvider = ({\n  children\n}) => {\n  _s();\n  const userState = useSelector(state => state.users || {});\n  const user = userState.user || null;\n  const [currentLanguage, setCurrentLanguage] = useState('en');\n  const [isKiswahili, setIsKiswahili] = useState(false);\n\n  // Update language based on user level\n  useEffect(() => {\n    if (user && user.level) {\n      const kiswahiliMode = isKiswahiliMode(user.level);\n      setIsKiswahili(kiswahiliMode);\n      setCurrentLanguage(kiswahiliMode ? 'sw' : 'en');\n    } else {\n      // Default to English when no user is logged in\n      setIsKiswahili(false);\n      setCurrentLanguage('en');\n    }\n  }, [user]);\n\n  // Translation function\n  const t = (key, fallback = key) => {\n    if (isKiswahili) {\n      return getKiswahiliTranslation(key, fallback);\n    }\n    return fallback;\n  };\n\n  // Get subject name based on language\n  const getSubjectName = subject => {\n    if (isKiswahili) {\n      return getKiswahiliTranslation(`subjects.${subject}`, subject);\n    }\n    return subject;\n  };\n\n  // Get class name based on language\n  const getClassName = classNumber => {\n    if (isKiswahili) {\n      return getKiswahiliTranslation(`classes.${classNumber}`, `Darasa la ${classNumber}`);\n    }\n    return `Class ${classNumber}`;\n  };\n\n  // Format numbers in Kiswahili if needed\n  const formatNumber = number => {\n    if (isKiswahili && number <= 10) {\n      return getKiswahiliTranslation(`numbers.${number}`, number.toString());\n    }\n    return number.toString();\n  };\n  const value = {\n    currentLanguage,\n    isKiswahili,\n    t,\n    getSubjectName,\n    getClassName,\n    formatNumber,\n    setCurrentLanguage\n  };\n  return /*#__PURE__*/_jsxDEV(LanguageContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this);\n};\n\n// Custom hook to use language context\n_s(LanguageProvider, \"kdq+WY8SJV8h/NWX816Vz2OmQ6g=\", false, function () {\n  return [useSelector];\n});\n_c = LanguageProvider;\nexport const useLanguage = () => {\n  _s2();\n  const context = useContext(LanguageContext);\n  if (!context) {\n    throw new Error('useLanguage must be used within a LanguageProvider');\n  }\n  return context;\n};\n_s2(useLanguage, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default LanguageContext;\nvar _c;\n$RefreshReg$(_c, \"LanguageProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "useSelector", "getKiswahiliTranslation", "isKiswahiliMode", "jsxDEV", "_jsxDEV", "LanguageContext", "LanguageProvider", "children", "_s", "userState", "state", "users", "user", "currentLanguage", "setCurrentLanguage", "isKiswahili", "setIsKiswahili", "level", "kiswahiliMode", "t", "key", "fallback", "getSubjectName", "subject", "getClassName", "classNumber", "formatNumber", "number", "toString", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useLanguage", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/contexts/LanguageContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport { useSelector } from 'react-redux';\nimport { getKiswahiliTranslation, isKiswahiliMode } from '../localization/kiswahili';\n\n// Create Language Context\nconst LanguageContext = createContext();\n\n// Language Provider Component\nexport const LanguageProvider = ({ children }) => {\n  const userState = useSelector((state) => state.users || {});\n  const user = userState.user || null;\n  const [currentLanguage, setCurrentLanguage] = useState('en');\n  const [isKiswahili, setIsKiswahili] = useState(false);\n\n  // Update language based on user level\n  useEffect(() => {\n    if (user && user.level) {\n      const kiswahiliMode = isKiswahiliMode(user.level);\n      setIsKiswahili(kiswahiliMode);\n      setCurrentLanguage(kiswahiliMode ? 'sw' : 'en');\n    } else {\n      // Default to English when no user is logged in\n      setIsKiswahili(false);\n      setCurrentLanguage('en');\n    }\n  }, [user]);\n\n  // Translation function\n  const t = (key, fallback = key) => {\n    if (isKiswahili) {\n      return getKiswahiliTranslation(key, fallback);\n    }\n    return fallback;\n  };\n\n  // Get subject name based on language\n  const getSubjectName = (subject) => {\n    if (isKiswahili) {\n      return getKiswahiliTranslation(`subjects.${subject}`, subject);\n    }\n    return subject;\n  };\n\n  // Get class name based on language\n  const getClassName = (classNumber) => {\n    if (isKiswahili) {\n      return getKiswahiliTranslation(`classes.${classNumber}`, `Darasa la ${classNumber}`);\n    }\n    return `Class ${classNumber}`;\n  };\n\n  // Format numbers in Kiswahili if needed\n  const formatNumber = (number) => {\n    if (isKiswahili && number <= 10) {\n      return getKiswahiliTranslation(`numbers.${number}`, number.toString());\n    }\n    return number.toString();\n  };\n\n  const value = {\n    currentLanguage,\n    isKiswahili,\n    t,\n    getSubjectName,\n    getClassName,\n    formatNumber,\n    setCurrentLanguage\n  };\n\n  return (\n    <LanguageContext.Provider value={value}>\n      {children}\n    </LanguageContext.Provider>\n  );\n};\n\n// Custom hook to use language context\nexport const useLanguage = () => {\n  const context = useContext(LanguageContext);\n  if (!context) {\n    throw new Error('useLanguage must be used within a LanguageProvider');\n  }\n  return context;\n};\n\nexport default LanguageContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,uBAAuB,EAAEC,eAAe,QAAQ,2BAA2B;;AAEpF;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,eAAe,gBAAGT,aAAa,CAAC,CAAC;;AAEvC;AACA,OAAO,MAAMU,gBAAgB,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAChD,MAAMC,SAAS,GAAGT,WAAW,CAAEU,KAAK,IAAKA,KAAK,CAACC,KAAK,IAAI,CAAC,CAAC,CAAC;EAC3D,MAAMC,IAAI,GAAGH,SAAS,CAACG,IAAI,IAAI,IAAI;EACnC,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACiB,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACAC,SAAS,CAAC,MAAM;IACd,IAAIa,IAAI,IAAIA,IAAI,CAACK,KAAK,EAAE;MACtB,MAAMC,aAAa,GAAGhB,eAAe,CAACU,IAAI,CAACK,KAAK,CAAC;MACjDD,cAAc,CAACE,aAAa,CAAC;MAC7BJ,kBAAkB,CAACI,aAAa,GAAG,IAAI,GAAG,IAAI,CAAC;IACjD,CAAC,MAAM;MACL;MACAF,cAAc,CAAC,KAAK,CAAC;MACrBF,kBAAkB,CAAC,IAAI,CAAC;IAC1B;EACF,CAAC,EAAE,CAACF,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMO,CAAC,GAAGA,CAACC,GAAG,EAAEC,QAAQ,GAAGD,GAAG,KAAK;IACjC,IAAIL,WAAW,EAAE;MACf,OAAOd,uBAAuB,CAACmB,GAAG,EAAEC,QAAQ,CAAC;IAC/C;IACA,OAAOA,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMC,cAAc,GAAIC,OAAO,IAAK;IAClC,IAAIR,WAAW,EAAE;MACf,OAAOd,uBAAuB,CAAE,YAAWsB,OAAQ,EAAC,EAAEA,OAAO,CAAC;IAChE;IACA,OAAOA,OAAO;EAChB,CAAC;;EAED;EACA,MAAMC,YAAY,GAAIC,WAAW,IAAK;IACpC,IAAIV,WAAW,EAAE;MACf,OAAOd,uBAAuB,CAAE,WAAUwB,WAAY,EAAC,EAAG,aAAYA,WAAY,EAAC,CAAC;IACtF;IACA,OAAQ,SAAQA,WAAY,EAAC;EAC/B,CAAC;;EAED;EACA,MAAMC,YAAY,GAAIC,MAAM,IAAK;IAC/B,IAAIZ,WAAW,IAAIY,MAAM,IAAI,EAAE,EAAE;MAC/B,OAAO1B,uBAAuB,CAAE,WAAU0B,MAAO,EAAC,EAAEA,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC;IACxE;IACA,OAAOD,MAAM,CAACC,QAAQ,CAAC,CAAC;EAC1B,CAAC;EAED,MAAMC,KAAK,GAAG;IACZhB,eAAe;IACfE,WAAW;IACXI,CAAC;IACDG,cAAc;IACdE,YAAY;IACZE,YAAY;IACZZ;EACF,CAAC;EAED,oBACEV,OAAA,CAACC,eAAe,CAACyB,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAtB,QAAA,EACpCA;EAAQ;IAAAwB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACe,CAAC;AAE/B,CAAC;;AAED;AAAA1B,EAAA,CApEaF,gBAAgB;EAAA,QACTN,WAAW;AAAA;AAAAmC,EAAA,GADlB7B,gBAAgB;AAqE7B,OAAO,MAAM8B,WAAW,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC/B,MAAMC,OAAO,GAAGzC,UAAU,CAACQ,eAAe,CAAC;EAC3C,IAAI,CAACiC,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,oDAAoD,CAAC;EACvE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,WAAW;AAQxB,eAAe/B,eAAe;AAAC,IAAA8B,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}