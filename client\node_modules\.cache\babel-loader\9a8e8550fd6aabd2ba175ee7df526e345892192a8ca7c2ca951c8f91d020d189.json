{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nfunction renderExpandIcon(locale) {\n  return function expandIcon(_ref) {\n    let {\n      prefixCls,\n      onExpand,\n      record,\n      expanded,\n      expandable\n    } = _ref;\n    const iconPrefix = \"\".concat(prefixCls, \"-row-expand-icon\");\n    return /*#__PURE__*/React.createElement(\"button\", {\n      type: \"button\",\n      onClick: e => {\n        onExpand(record, e);\n        e.stopPropagation();\n      },\n      className: classNames(iconPrefix, {\n        [\"\".concat(iconPrefix, \"-spaced\")]: !expandable,\n        [\"\".concat(iconPrefix, \"-expanded\")]: expandable && expanded,\n        [\"\".concat(iconPrefix, \"-collapsed\")]: expandable && !expanded\n      }),\n      \"aria-label\": expanded ? locale.collapse : locale.expand,\n      \"aria-expanded\": expanded\n    });\n  };\n}\nexport default renderExpandIcon;", "map": {"version": 3, "names": ["classNames", "React", "renderExpandIcon", "locale", "expandIcon", "_ref", "prefixCls", "onExpand", "record", "expanded", "expandable", "iconPrefix", "concat", "createElement", "type", "onClick", "e", "stopPropagation", "className", "collapse", "expand"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/table/ExpandIcon.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nfunction renderExpandIcon(locale) {\n  return function expandIcon(_ref) {\n    let {\n      prefixCls,\n      onExpand,\n      record,\n      expanded,\n      expandable\n    } = _ref;\n    const iconPrefix = `${prefixCls}-row-expand-icon`;\n    return /*#__PURE__*/React.createElement(\"button\", {\n      type: \"button\",\n      onClick: e => {\n        onExpand(record, e);\n        e.stopPropagation();\n      },\n      className: classNames(iconPrefix, {\n        [`${iconPrefix}-spaced`]: !expandable,\n        [`${iconPrefix}-expanded`]: expandable && expanded,\n        [`${iconPrefix}-collapsed`]: expandable && !expanded\n      }),\n      \"aria-label\": expanded ? locale.collapse : locale.expand,\n      \"aria-expanded\": expanded\n    });\n  };\n}\nexport default renderExpandIcon;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,gBAAgBA,CAACC,MAAM,EAAE;EAChC,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAE;IAC/B,IAAI;MACFC,SAAS;MACTC,QAAQ;MACRC,MAAM;MACNC,QAAQ;MACRC;IACF,CAAC,GAAGL,IAAI;IACR,MAAMM,UAAU,MAAAC,MAAA,CAAMN,SAAS,qBAAkB;IACjD,OAAO,aAAaL,KAAK,CAACY,aAAa,CAAC,QAAQ,EAAE;MAChDC,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAEC,CAAC,IAAI;QACZT,QAAQ,CAACC,MAAM,EAAEQ,CAAC,CAAC;QACnBA,CAAC,CAACC,eAAe,CAAC,CAAC;MACrB,CAAC;MACDC,SAAS,EAAElB,UAAU,CAACW,UAAU,EAAE;QAChC,IAAAC,MAAA,CAAID,UAAU,eAAY,CAACD,UAAU;QACrC,IAAAE,MAAA,CAAID,UAAU,iBAAcD,UAAU,IAAID,QAAQ;QAClD,IAAAG,MAAA,CAAID,UAAU,kBAAeD,UAAU,IAAI,CAACD;MAC9C,CAAC,CAAC;MACF,YAAY,EAAEA,QAAQ,GAAGN,MAAM,CAACgB,QAAQ,GAAGhB,MAAM,CAACiB,MAAM;MACxD,eAAe,EAAEX;IACnB,CAAC,CAAC;EACJ,CAAC;AACH;AACA,eAAeP,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}