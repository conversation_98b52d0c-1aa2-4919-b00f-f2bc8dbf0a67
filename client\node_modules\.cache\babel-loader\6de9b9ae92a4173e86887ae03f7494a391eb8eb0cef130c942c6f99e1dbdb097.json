{"ast": null, "code": "import * as React from 'react';\nvar PanelContext = /*#__PURE__*/React.createContext({});\nexport default PanelContext;", "map": {"version": 3, "names": ["React", "PanelContext", "createContext"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-picker/es/PanelContext.js"], "sourcesContent": ["import * as React from 'react';\nvar PanelContext = /*#__PURE__*/React.createContext({});\nexport default PanelContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,YAAY,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,CAAC,CAAC,CAAC;AACvD,eAAeD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}