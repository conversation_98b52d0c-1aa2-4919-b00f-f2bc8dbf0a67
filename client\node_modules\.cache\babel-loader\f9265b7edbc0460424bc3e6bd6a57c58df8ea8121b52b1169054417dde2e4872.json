{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport warning from \"rc-util/es/warning\";\nimport { toArray } from \"./valueUtil\";\nfunction warningProps(props) {\n  var searchPlaceholder = props.searchPlaceholder,\n    treeCheckStrictly = props.treeCheckStrictly,\n    treeCheckable = props.treeCheckable,\n    labelInValue = props.labelInValue,\n    value = props.value,\n    multiple = props.multiple;\n  warning(!searchPlaceholder, '`searchPlaceholder` has been removed.');\n  if (treeCheckStrictly && labelInValue === false) {\n    warning(false, '`treeCheckStrictly` will force set `labelInValue` to `true`.');\n  }\n  if (labelInValue || treeCheckStrictly) {\n    warning(toArray(value).every(function (val) {\n      return val && _typeof(val) === 'object' && 'value' in val;\n    }), 'Invalid prop `value` supplied to `TreeSelect`. You should use { label: string, value: string | number } or [{ label: string, value: string | number }] instead.');\n  }\n  if (treeCheckStrictly || multiple || treeCheckable) {\n    warning(!value || Array.isArray(value), '`value` should be an array when `TreeSelect` is checkable or multiple.');\n  } else {\n    warning(!Array.isArray(value), '`value` should not be array when `TreeSelect` is single mode.');\n  }\n}\nexport default warningProps;", "map": {"version": 3, "names": ["_typeof", "warning", "toArray", "warningProps", "props", "searchPlaceholder", "treeCheckStrictly", "treeCheckable", "labelInValue", "value", "multiple", "every", "val", "Array", "isArray"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-tree-select/es/utils/warningPropsUtil.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport warning from \"rc-util/es/warning\";\nimport { toArray } from \"./valueUtil\";\nfunction warningProps(props) {\n  var searchPlaceholder = props.searchPlaceholder,\n    treeCheckStrictly = props.treeCheckStrictly,\n    treeCheckable = props.treeCheckable,\n    labelInValue = props.labelInValue,\n    value = props.value,\n    multiple = props.multiple;\n  warning(!searchPlaceholder, '`searchPlaceholder` has been removed.');\n  if (treeCheckStrictly && labelInValue === false) {\n    warning(false, '`treeCheckStrictly` will force set `labelInValue` to `true`.');\n  }\n  if (labelInValue || treeCheckStrictly) {\n    warning(toArray(value).every(function (val) {\n      return val && _typeof(val) === 'object' && 'value' in val;\n    }), 'Invalid prop `value` supplied to `TreeSelect`. You should use { label: string, value: string | number } or [{ label: string, value: string | number }] instead.');\n  }\n  if (treeCheckStrictly || multiple || treeCheckable) {\n    warning(!value || Array.isArray(value), '`value` should be an array when `TreeSelect` is checkable or multiple.');\n  } else {\n    warning(!Array.isArray(value), '`value` should not be array when `TreeSelect` is single mode.');\n  }\n}\nexport default warningProps;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,OAAO,MAAM,oBAAoB;AACxC,SAASC,OAAO,QAAQ,aAAa;AACrC,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B,IAAIC,iBAAiB,GAAGD,KAAK,CAACC,iBAAiB;IAC7CC,iBAAiB,GAAGF,KAAK,CAACE,iBAAiB;IAC3CC,aAAa,GAAGH,KAAK,CAACG,aAAa;IACnCC,YAAY,GAAGJ,KAAK,CAACI,YAAY;IACjCC,KAAK,GAAGL,KAAK,CAACK,KAAK;IACnBC,QAAQ,GAAGN,KAAK,CAACM,QAAQ;EAC3BT,OAAO,CAAC,CAACI,iBAAiB,EAAE,uCAAuC,CAAC;EACpE,IAAIC,iBAAiB,IAAIE,YAAY,KAAK,KAAK,EAAE;IAC/CP,OAAO,CAAC,KAAK,EAAE,8DAA8D,CAAC;EAChF;EACA,IAAIO,YAAY,IAAIF,iBAAiB,EAAE;IACrCL,OAAO,CAACC,OAAO,CAACO,KAAK,CAAC,CAACE,KAAK,CAAC,UAAUC,GAAG,EAAE;MAC1C,OAAOA,GAAG,IAAIZ,OAAO,CAACY,GAAG,CAAC,KAAK,QAAQ,IAAI,OAAO,IAAIA,GAAG;IAC3D,CAAC,CAAC,EAAE,iKAAiK,CAAC;EACxK;EACA,IAAIN,iBAAiB,IAAII,QAAQ,IAAIH,aAAa,EAAE;IAClDN,OAAO,CAAC,CAACQ,KAAK,IAAII,KAAK,CAACC,OAAO,CAACL,KAAK,CAAC,EAAE,wEAAwE,CAAC;EACnH,CAAC,MAAM;IACLR,OAAO,CAAC,CAACY,KAAK,CAACC,OAAO,CAACL,KAAK,CAAC,EAAE,+DAA+D,CAAC;EACjG;AACF;AACA,eAAeN,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}