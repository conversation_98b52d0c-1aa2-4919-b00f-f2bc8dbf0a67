{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport classNames from 'classnames';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport React, { cloneElement, isValidElement } from 'react';\nimport KEYCODE from './KeyCode';\nimport LOCALE from './locale/zh_CN';\nimport Options from './Options';\nimport Pager from './Pager';\nfunction noop() {}\nfunction isInteger(v) {\n  var value = Number(v);\n  return (\n    // eslint-disable-next-line no-restricted-globals\n    typeof value === 'number' && !Number.isNaN(value) && isFinite(value) && Math.floor(value) === value\n  );\n}\nvar defaultItemRender = function defaultItemRender(page, type, element) {\n  return element;\n};\nfunction calculatePage(p, state, props) {\n  var pageSize = typeof p === 'undefined' ? state.pageSize : p;\n  return Math.floor((props.total - 1) / pageSize) + 1;\n}\nvar Pagination = /*#__PURE__*/function (_React$Component) {\n  _inherits(Pagination, _React$Component);\n  var _super = _createSuper(Pagination);\n  function Pagination(props) {\n    var _this;\n    _classCallCheck(this, Pagination);\n    _this = _super.call(this, props);\n    _this.paginationNode = /*#__PURE__*/React.createRef();\n    _this.getJumpPrevPage = function () {\n      return Math.max(1, _this.state.current - (_this.props.showLessItems ? 3 : 5));\n    };\n    _this.getJumpNextPage = function () {\n      return Math.min(calculatePage(undefined, _this.state, _this.props), _this.state.current + (_this.props.showLessItems ? 3 : 5));\n    };\n    _this.getItemIcon = function (icon, label) {\n      var prefixCls = _this.props.prefixCls;\n      var iconNode = icon || /*#__PURE__*/React.createElement(\"button\", {\n        type: \"button\",\n        \"aria-label\": label,\n        className: \"\".concat(prefixCls, \"-item-link\")\n      });\n      if (typeof icon === 'function') {\n        iconNode = /*#__PURE__*/React.createElement(icon, _objectSpread({}, _this.props));\n      }\n      return iconNode;\n    };\n    _this.isValid = function (page) {\n      var total = _this.props.total;\n      return isInteger(page) && page !== _this.state.current && isInteger(total) && total > 0;\n    };\n    _this.shouldDisplayQuickJumper = function () {\n      var _this$props = _this.props,\n        showQuickJumper = _this$props.showQuickJumper,\n        total = _this$props.total;\n      var pageSize = _this.state.pageSize;\n      if (total <= pageSize) {\n        return false;\n      }\n      return showQuickJumper;\n    };\n    _this.handleKeyDown = function (e) {\n      if (e.keyCode === KEYCODE.ARROW_UP || e.keyCode === KEYCODE.ARROW_DOWN) {\n        e.preventDefault();\n      }\n    };\n    _this.handleKeyUp = function (e) {\n      var value = _this.getValidValue(e);\n      var currentInputValue = _this.state.currentInputValue;\n      if (value !== currentInputValue) {\n        _this.setState({\n          currentInputValue: value\n        });\n      }\n      if (e.keyCode === KEYCODE.ENTER) {\n        _this.handleChange(value);\n      } else if (e.keyCode === KEYCODE.ARROW_UP) {\n        _this.handleChange(value - 1);\n      } else if (e.keyCode === KEYCODE.ARROW_DOWN) {\n        _this.handleChange(value + 1);\n      }\n    };\n    _this.handleBlur = function (e) {\n      var value = _this.getValidValue(e);\n      _this.handleChange(value);\n    };\n    _this.changePageSize = function (size) {\n      var current = _this.state.current;\n      var newCurrent = calculatePage(size, _this.state, _this.props);\n      current = current > newCurrent ? newCurrent : current;\n      // fix the issue:\n      // Once 'total' is 0, 'current' in 'onShowSizeChange' is 0, which is not correct.\n      if (newCurrent === 0) {\n        // eslint-disable-next-line prefer-destructuring\n        current = _this.state.current;\n      }\n      if (typeof size === 'number') {\n        if (!('pageSize' in _this.props)) {\n          _this.setState({\n            pageSize: size\n          });\n        }\n        if (!('current' in _this.props)) {\n          _this.setState({\n            current: current,\n            currentInputValue: current\n          });\n        }\n      }\n      _this.props.onShowSizeChange(current, size);\n      if ('onChange' in _this.props && _this.props.onChange) {\n        _this.props.onChange(current, size);\n      }\n    };\n    _this.handleChange = function (page) {\n      var _this$props2 = _this.props,\n        disabled = _this$props2.disabled,\n        onChange = _this$props2.onChange;\n      var _this$state = _this.state,\n        pageSize = _this$state.pageSize,\n        current = _this$state.current,\n        currentInputValue = _this$state.currentInputValue;\n      if (_this.isValid(page) && !disabled) {\n        var currentPage = calculatePage(undefined, _this.state, _this.props);\n        var newPage = page;\n        if (page > currentPage) {\n          newPage = currentPage;\n        } else if (page < 1) {\n          newPage = 1;\n        }\n        if (!('current' in _this.props)) {\n          _this.setState({\n            current: newPage\n          });\n        }\n        if (newPage !== currentInputValue) {\n          _this.setState({\n            currentInputValue: newPage\n          });\n        }\n        onChange(newPage, pageSize);\n        return newPage;\n      }\n      return current;\n    };\n    _this.prev = function () {\n      if (_this.hasPrev()) {\n        _this.handleChange(_this.state.current - 1);\n      }\n    };\n    _this.next = function () {\n      if (_this.hasNext()) {\n        _this.handleChange(_this.state.current + 1);\n      }\n    };\n    _this.jumpPrev = function () {\n      _this.handleChange(_this.getJumpPrevPage());\n    };\n    _this.jumpNext = function () {\n      _this.handleChange(_this.getJumpNextPage());\n    };\n    _this.hasPrev = function () {\n      return _this.state.current > 1;\n    };\n    _this.hasNext = function () {\n      return _this.state.current < calculatePage(undefined, _this.state, _this.props);\n    };\n    _this.runIfEnter = function (event, callback) {\n      if (event.key === 'Enter' || event.charCode === 13) {\n        for (var _len = arguments.length, restParams = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n          restParams[_key - 2] = arguments[_key];\n        }\n        callback.apply(void 0, restParams);\n      }\n    };\n    _this.runIfEnterPrev = function (e) {\n      _this.runIfEnter(e, _this.prev);\n    };\n    _this.runIfEnterNext = function (e) {\n      _this.runIfEnter(e, _this.next);\n    };\n    _this.runIfEnterJumpPrev = function (e) {\n      _this.runIfEnter(e, _this.jumpPrev);\n    };\n    _this.runIfEnterJumpNext = function (e) {\n      _this.runIfEnter(e, _this.jumpNext);\n    };\n    _this.handleGoTO = function (e) {\n      if (e.keyCode === KEYCODE.ENTER || e.type === 'click') {\n        _this.handleChange(_this.state.currentInputValue);\n      }\n    };\n    _this.renderPrev = function (prevPage) {\n      var _this$props3 = _this.props,\n        prevIcon = _this$props3.prevIcon,\n        itemRender = _this$props3.itemRender;\n      var prevButton = itemRender(prevPage, 'prev', _this.getItemIcon(prevIcon, 'prev page'));\n      var disabled = !_this.hasPrev();\n      return /*#__PURE__*/isValidElement(prevButton) ? /*#__PURE__*/cloneElement(prevButton, {\n        disabled: disabled\n      }) : prevButton;\n    };\n    _this.renderNext = function (nextPage) {\n      var _this$props4 = _this.props,\n        nextIcon = _this$props4.nextIcon,\n        itemRender = _this$props4.itemRender;\n      var nextButton = itemRender(nextPage, 'next', _this.getItemIcon(nextIcon, 'next page'));\n      var disabled = !_this.hasNext();\n      return /*#__PURE__*/isValidElement(nextButton) ? /*#__PURE__*/cloneElement(nextButton, {\n        disabled: disabled\n      }) : nextButton;\n    };\n    var hasOnChange = props.onChange !== noop;\n    var hasCurrent = ('current' in props);\n    if (hasCurrent && !hasOnChange) {\n      // eslint-disable-next-line no-console\n      console.warn('Warning: You provided a `current` prop to a Pagination component without an `onChange` handler. This will render a read-only component.');\n    }\n    var _current = props.defaultCurrent;\n    if ('current' in props) {\n      // eslint-disable-next-line prefer-destructuring\n      _current = props.current;\n    }\n    var _pageSize = props.defaultPageSize;\n    if ('pageSize' in props) {\n      // eslint-disable-next-line prefer-destructuring\n      _pageSize = props.pageSize;\n    }\n    _current = Math.min(_current, calculatePage(_pageSize, undefined, props));\n    _this.state = {\n      current: _current,\n      currentInputValue: _current,\n      pageSize: _pageSize\n    };\n    return _this;\n  }\n  _createClass(Pagination, [{\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(_, prevState) {\n      // When current page change, fix focused style of prev item\n      // A hacky solution of https://github.com/ant-design/ant-design/issues/8948\n      var prefixCls = this.props.prefixCls;\n      if (prevState.current !== this.state.current && this.paginationNode.current) {\n        var lastCurrentNode = this.paginationNode.current.querySelector(\".\".concat(prefixCls, \"-item-\").concat(prevState.current));\n        if (lastCurrentNode && document.activeElement === lastCurrentNode) {\n          var _lastCurrentNode$blur;\n          lastCurrentNode === null || lastCurrentNode === void 0 ? void 0 : (_lastCurrentNode$blur = lastCurrentNode.blur) === null || _lastCurrentNode$blur === void 0 ? void 0 : _lastCurrentNode$blur.call(lastCurrentNode);\n        }\n      }\n    }\n  }, {\n    key: \"getValidValue\",\n    value: function getValidValue(e) {\n      var inputValue = e.target.value;\n      var allPages = calculatePage(undefined, this.state, this.props);\n      var currentInputValue = this.state.currentInputValue;\n      var value;\n      if (inputValue === '') {\n        value = inputValue;\n        // eslint-disable-next-line no-restricted-globals\n      } else if (Number.isNaN(Number(inputValue))) {\n        value = currentInputValue;\n      } else if (inputValue >= allPages) {\n        value = allPages;\n      } else {\n        value = Number(inputValue);\n      }\n      return value;\n    }\n  }, {\n    key: \"getShowSizeChanger\",\n    value: function getShowSizeChanger() {\n      var _this$props5 = this.props,\n        showSizeChanger = _this$props5.showSizeChanger,\n        total = _this$props5.total,\n        totalBoundaryShowSizeChanger = _this$props5.totalBoundaryShowSizeChanger;\n      if (typeof showSizeChanger !== 'undefined') {\n        return showSizeChanger;\n      }\n      return total > totalBoundaryShowSizeChanger;\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props6 = this.props,\n        prefixCls = _this$props6.prefixCls,\n        className = _this$props6.className,\n        style = _this$props6.style,\n        disabled = _this$props6.disabled,\n        hideOnSinglePage = _this$props6.hideOnSinglePage,\n        total = _this$props6.total,\n        locale = _this$props6.locale,\n        showQuickJumper = _this$props6.showQuickJumper,\n        showLessItems = _this$props6.showLessItems,\n        showTitle = _this$props6.showTitle,\n        showTotal = _this$props6.showTotal,\n        simple = _this$props6.simple,\n        itemRender = _this$props6.itemRender,\n        showPrevNextJumpers = _this$props6.showPrevNextJumpers,\n        jumpPrevIcon = _this$props6.jumpPrevIcon,\n        jumpNextIcon = _this$props6.jumpNextIcon,\n        selectComponentClass = _this$props6.selectComponentClass,\n        selectPrefixCls = _this$props6.selectPrefixCls,\n        pageSizeOptions = _this$props6.pageSizeOptions;\n      var _this$state2 = this.state,\n        current = _this$state2.current,\n        pageSize = _this$state2.pageSize,\n        currentInputValue = _this$state2.currentInputValue;\n      // When hideOnSinglePage is true and there is only 1 page, hide the pager\n      if (hideOnSinglePage === true && total <= pageSize) {\n        return null;\n      }\n      var allPages = calculatePage(undefined, this.state, this.props);\n      var pagerList = [];\n      var jumpPrev = null;\n      var jumpNext = null;\n      var firstPager = null;\n      var lastPager = null;\n      var gotoButton = null;\n      var goButton = showQuickJumper && showQuickJumper.goButton;\n      var pageBufferSize = showLessItems ? 1 : 2;\n      var prevPage = current - 1 > 0 ? current - 1 : 0;\n      var nextPage = current + 1 < allPages ? current + 1 : allPages;\n      var dataOrAriaAttributeProps = pickAttrs(this.props, {\n        aria: true,\n        data: true\n      });\n      var totalText = showTotal && /*#__PURE__*/React.createElement(\"li\", {\n        className: \"\".concat(prefixCls, \"-total-text\")\n      }, showTotal(total, [total === 0 ? 0 : (current - 1) * pageSize + 1, current * pageSize > total ? total : current * pageSize]));\n      if (simple) {\n        if (goButton) {\n          if (typeof goButton === 'boolean') {\n            gotoButton = /*#__PURE__*/React.createElement(\"button\", {\n              type: \"button\",\n              onClick: this.handleGoTO,\n              onKeyUp: this.handleGoTO\n            }, locale.jump_to_confirm);\n          } else {\n            gotoButton = /*#__PURE__*/React.createElement(\"span\", {\n              onClick: this.handleGoTO,\n              onKeyUp: this.handleGoTO\n            }, goButton);\n          }\n          gotoButton = /*#__PURE__*/React.createElement(\"li\", {\n            title: showTitle ? \"\".concat(locale.jump_to).concat(current, \"/\").concat(allPages) : null,\n            className: \"\".concat(prefixCls, \"-simple-pager\")\n          }, gotoButton);\n        }\n        return /*#__PURE__*/React.createElement(\"ul\", _extends({\n          className: classNames(prefixCls, \"\".concat(prefixCls, \"-simple\"), _defineProperty({}, \"\".concat(prefixCls, \"-disabled\"), disabled), className),\n          style: style,\n          ref: this.paginationNode\n        }, dataOrAriaAttributeProps), totalText, /*#__PURE__*/React.createElement(\"li\", {\n          title: showTitle ? locale.prev_page : null,\n          onClick: this.prev,\n          tabIndex: this.hasPrev() ? 0 : null,\n          onKeyPress: this.runIfEnterPrev,\n          className: classNames(\"\".concat(prefixCls, \"-prev\"), _defineProperty({}, \"\".concat(prefixCls, \"-disabled\"), !this.hasPrev())),\n          \"aria-disabled\": !this.hasPrev()\n        }, this.renderPrev(prevPage)), /*#__PURE__*/React.createElement(\"li\", {\n          title: showTitle ? \"\".concat(current, \"/\").concat(allPages) : null,\n          className: \"\".concat(prefixCls, \"-simple-pager\")\n        }, /*#__PURE__*/React.createElement(\"input\", {\n          type: \"text\",\n          value: currentInputValue,\n          disabled: disabled,\n          onKeyDown: this.handleKeyDown,\n          onKeyUp: this.handleKeyUp,\n          onChange: this.handleKeyUp,\n          onBlur: this.handleBlur,\n          size: 3\n        }), /*#__PURE__*/React.createElement(\"span\", {\n          className: \"\".concat(prefixCls, \"-slash\")\n        }, \"/\"), allPages), /*#__PURE__*/React.createElement(\"li\", {\n          title: showTitle ? locale.next_page : null,\n          onClick: this.next,\n          tabIndex: this.hasPrev() ? 0 : null,\n          onKeyPress: this.runIfEnterNext,\n          className: classNames(\"\".concat(prefixCls, \"-next\"), _defineProperty({}, \"\".concat(prefixCls, \"-disabled\"), !this.hasNext())),\n          \"aria-disabled\": !this.hasNext()\n        }, this.renderNext(nextPage)), gotoButton);\n      }\n      if (allPages <= 3 + pageBufferSize * 2) {\n        var pagerProps = {\n          locale: locale,\n          rootPrefixCls: prefixCls,\n          onClick: this.handleChange,\n          onKeyPress: this.runIfEnter,\n          showTitle: showTitle,\n          itemRender: itemRender\n        };\n        if (!allPages) {\n          pagerList.push( /*#__PURE__*/React.createElement(Pager, _extends({}, pagerProps, {\n            key: \"noPager\",\n            page: 1,\n            className: \"\".concat(prefixCls, \"-item-disabled\")\n          })));\n        }\n        for (var i = 1; i <= allPages; i += 1) {\n          var active = current === i;\n          pagerList.push( /*#__PURE__*/React.createElement(Pager, _extends({}, pagerProps, {\n            key: i,\n            page: i,\n            active: active\n          })));\n        }\n      } else {\n        var prevItemTitle = showLessItems ? locale.prev_3 : locale.prev_5;\n        var nextItemTitle = showLessItems ? locale.next_3 : locale.next_5;\n        if (showPrevNextJumpers) {\n          jumpPrev = /*#__PURE__*/React.createElement(\"li\", {\n            title: showTitle ? prevItemTitle : null,\n            key: \"prev\",\n            onClick: this.jumpPrev,\n            tabIndex: 0,\n            onKeyPress: this.runIfEnterJumpPrev,\n            className: classNames(\"\".concat(prefixCls, \"-jump-prev\"), _defineProperty({}, \"\".concat(prefixCls, \"-jump-prev-custom-icon\"), !!jumpPrevIcon))\n          }, itemRender(this.getJumpPrevPage(), 'jump-prev', this.getItemIcon(jumpPrevIcon, 'prev page')));\n          jumpNext = /*#__PURE__*/React.createElement(\"li\", {\n            title: showTitle ? nextItemTitle : null,\n            key: \"next\",\n            tabIndex: 0,\n            onClick: this.jumpNext,\n            onKeyPress: this.runIfEnterJumpNext,\n            className: classNames(\"\".concat(prefixCls, \"-jump-next\"), _defineProperty({}, \"\".concat(prefixCls, \"-jump-next-custom-icon\"), !!jumpNextIcon))\n          }, itemRender(this.getJumpNextPage(), 'jump-next', this.getItemIcon(jumpNextIcon, 'next page')));\n        }\n        lastPager = /*#__PURE__*/React.createElement(Pager, {\n          locale: locale,\n          last: true,\n          rootPrefixCls: prefixCls,\n          onClick: this.handleChange,\n          onKeyPress: this.runIfEnter,\n          key: allPages,\n          page: allPages,\n          active: false,\n          showTitle: showTitle,\n          itemRender: itemRender\n        });\n        firstPager = /*#__PURE__*/React.createElement(Pager, {\n          locale: locale,\n          rootPrefixCls: prefixCls,\n          onClick: this.handleChange,\n          onKeyPress: this.runIfEnter,\n          key: 1,\n          page: 1,\n          active: false,\n          showTitle: showTitle,\n          itemRender: itemRender\n        });\n        var left = Math.max(1, current - pageBufferSize);\n        var right = Math.min(current + pageBufferSize, allPages);\n        if (current - 1 <= pageBufferSize) {\n          right = 1 + pageBufferSize * 2;\n        }\n        if (allPages - current <= pageBufferSize) {\n          left = allPages - pageBufferSize * 2;\n        }\n        for (var _i = left; _i <= right; _i += 1) {\n          var _active = current === _i;\n          pagerList.push( /*#__PURE__*/React.createElement(Pager, {\n            locale: locale,\n            rootPrefixCls: prefixCls,\n            onClick: this.handleChange,\n            onKeyPress: this.runIfEnter,\n            key: _i,\n            page: _i,\n            active: _active,\n            showTitle: showTitle,\n            itemRender: itemRender\n          }));\n        }\n        if (current - 1 >= pageBufferSize * 2 && current !== 1 + 2) {\n          pagerList[0] = /*#__PURE__*/cloneElement(pagerList[0], {\n            className: \"\".concat(prefixCls, \"-item-after-jump-prev\")\n          });\n          pagerList.unshift(jumpPrev);\n        }\n        if (allPages - current >= pageBufferSize * 2 && current !== allPages - 2) {\n          pagerList[pagerList.length - 1] = /*#__PURE__*/cloneElement(pagerList[pagerList.length - 1], {\n            className: \"\".concat(prefixCls, \"-item-before-jump-next\")\n          });\n          pagerList.push(jumpNext);\n        }\n        if (left !== 1) {\n          pagerList.unshift(firstPager);\n        }\n        if (right !== allPages) {\n          pagerList.push(lastPager);\n        }\n      }\n      var prevDisabled = !this.hasPrev() || !allPages;\n      var nextDisabled = !this.hasNext() || !allPages;\n      return /*#__PURE__*/React.createElement(\"ul\", _extends({\n        className: classNames(prefixCls, className, _defineProperty({}, \"\".concat(prefixCls, \"-disabled\"), disabled)),\n        style: style,\n        ref: this.paginationNode\n      }, dataOrAriaAttributeProps), totalText, /*#__PURE__*/React.createElement(\"li\", {\n        title: showTitle ? locale.prev_page : null,\n        onClick: this.prev,\n        tabIndex: prevDisabled ? null : 0,\n        onKeyPress: this.runIfEnterPrev,\n        className: classNames(\"\".concat(prefixCls, \"-prev\"), _defineProperty({}, \"\".concat(prefixCls, \"-disabled\"), prevDisabled)),\n        \"aria-disabled\": prevDisabled\n      }, this.renderPrev(prevPage)), pagerList, /*#__PURE__*/React.createElement(\"li\", {\n        title: showTitle ? locale.next_page : null,\n        onClick: this.next,\n        tabIndex: nextDisabled ? null : 0,\n        onKeyPress: this.runIfEnterNext,\n        className: classNames(\"\".concat(prefixCls, \"-next\"), _defineProperty({}, \"\".concat(prefixCls, \"-disabled\"), nextDisabled)),\n        \"aria-disabled\": nextDisabled\n      }, this.renderNext(nextPage)), /*#__PURE__*/React.createElement(Options, {\n        disabled: disabled,\n        locale: locale,\n        rootPrefixCls: prefixCls,\n        selectComponentClass: selectComponentClass,\n        selectPrefixCls: selectPrefixCls,\n        changeSize: this.getShowSizeChanger() ? this.changePageSize : null,\n        current: current,\n        pageSize: pageSize,\n        pageSizeOptions: pageSizeOptions,\n        quickGo: this.shouldDisplayQuickJumper() ? this.handleChange : null,\n        goButton: goButton\n      }));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(props, prevState) {\n      var newState = {};\n      if ('current' in props) {\n        newState.current = props.current;\n        if (props.current !== prevState.current) {\n          newState.currentInputValue = newState.current;\n        }\n      }\n      if ('pageSize' in props && props.pageSize !== prevState.pageSize) {\n        var current = prevState.current;\n        var newCurrent = calculatePage(props.pageSize, prevState, props);\n        current = current > newCurrent ? newCurrent : current;\n        if (!('current' in props)) {\n          newState.current = current;\n          newState.currentInputValue = current;\n        }\n        newState.pageSize = props.pageSize;\n      }\n      return newState;\n    }\n  }]);\n  return Pagination;\n}(React.Component);\nPagination.defaultProps = {\n  defaultCurrent: 1,\n  total: 0,\n  defaultPageSize: 10,\n  onChange: noop,\n  className: '',\n  selectPrefixCls: 'rc-select',\n  prefixCls: 'rc-pagination',\n  selectComponentClass: null,\n  hideOnSinglePage: false,\n  showPrevNextJumpers: true,\n  showQuickJumper: false,\n  showLessItems: false,\n  showTitle: true,\n  onShowSizeChange: noop,\n  locale: LOCALE,\n  style: {},\n  itemRender: defaultItemRender,\n  totalBoundaryShowSizeChanger: 50\n};\nexport default Pagination;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_objectSpread", "_classCallCheck", "_createClass", "_inherits", "_createSuper", "classNames", "pickAttrs", "React", "cloneElement", "isValidElement", "KEYCODE", "LOCALE", "Options", "Pager", "noop", "isInteger", "v", "value", "Number", "isNaN", "isFinite", "Math", "floor", "defaultItemRender", "page", "type", "element", "calculatePage", "p", "state", "props", "pageSize", "total", "Pagination", "_React$Component", "_super", "_this", "call", "paginationNode", "createRef", "getJumpPrevPage", "max", "current", "showLessItems", "getJumpNextPage", "min", "undefined", "getItemIcon", "icon", "label", "prefixCls", "iconNode", "createElement", "className", "concat", "<PERSON><PERSON><PERSON><PERSON>", "shouldDisplayQuickJumper", "_this$props", "showQuickJumper", "handleKeyDown", "e", "keyCode", "ARROW_UP", "ARROW_DOWN", "preventDefault", "handleKeyUp", "getValidValue", "currentInputValue", "setState", "ENTER", "handleChange", "handleBlur", "changePageSize", "size", "newCurrent", "onShowSizeChange", "onChange", "_this$props2", "disabled", "_this$state", "currentPage", "newPage", "prev", "has<PERSON>rev", "next", "hasNext", "jump<PERSON>rev", "jumpNext", "runIfEnter", "event", "callback", "key", "charCode", "_len", "arguments", "length", "restParams", "Array", "_key", "apply", "runIfEnterPrev", "runIfEnterNext", "runIfEnterJumpPrev", "runIfEnterJumpNext", "handleGoTO", "renderPrev", "prevPage", "_this$props3", "prevIcon", "itemRender", "prevButton", "renderNext", "nextPage", "_this$props4", "nextIcon", "nextButton", "hasOnChange", "has<PERSON><PERSON>rent", "console", "warn", "_current", "defaultCurrent", "_pageSize", "defaultPageSize", "componentDidUpdate", "_", "prevState", "lastCurrentNode", "querySelector", "document", "activeElement", "_lastCurrentNode$blur", "blur", "inputValue", "target", "allPages", "getShowSizeChanger", "_this$props5", "showSizeChanger", "totalBoundaryShowSizeChanger", "render", "_this$props6", "style", "hideOnSinglePage", "locale", "showTitle", "showTotal", "simple", "showPrevNextJumpers", "jumpPrevIcon", "jumpNextIcon", "selectComponentClass", "selectPrefixCls", "pageSizeOptions", "_this$state2", "pagerList", "firstPager", "lastPager", "gotoButton", "goButton", "pageBufferSize", "dataOrAriaAttributeProps", "aria", "data", "totalText", "onClick", "onKeyUp", "jump_to_confirm", "title", "jump_to", "ref", "prev_page", "tabIndex", "onKeyPress", "onKeyDown", "onBlur", "next_page", "pagerProps", "rootPrefixCls", "push", "i", "active", "prevItemTitle", "prev_3", "prev_5", "nextItemTitle", "next_3", "next_5", "last", "left", "right", "_i", "_active", "unshift", "prevDisabled", "nextDisabled", "changeSize", "quickGo", "getDerivedStateFromProps", "newState", "Component", "defaultProps"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-pagination/es/Pagination.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport classNames from 'classnames';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport React, { cloneElement, isValidElement } from 'react';\nimport KEYCODE from './KeyCode';\nimport LOCALE from './locale/zh_CN';\nimport Options from './Options';\nimport Pager from './Pager';\nfunction noop() {}\nfunction isInteger(v) {\n  var value = Number(v);\n  return (\n    // eslint-disable-next-line no-restricted-globals\n    typeof value === 'number' && !Number.isNaN(value) && isFinite(value) && Math.floor(value) === value\n  );\n}\nvar defaultItemRender = function defaultItemRender(page, type, element) {\n  return element;\n};\nfunction calculatePage(p, state, props) {\n  var pageSize = typeof p === 'undefined' ? state.pageSize : p;\n  return Math.floor((props.total - 1) / pageSize) + 1;\n}\nvar Pagination = /*#__PURE__*/function (_React$Component) {\n  _inherits(Pagination, _React$Component);\n  var _super = _createSuper(Pagination);\n  function Pagination(props) {\n    var _this;\n    _classCallCheck(this, Pagination);\n    _this = _super.call(this, props);\n    _this.paginationNode = /*#__PURE__*/React.createRef();\n    _this.getJumpPrevPage = function () {\n      return Math.max(1, _this.state.current - (_this.props.showLessItems ? 3 : 5));\n    };\n    _this.getJumpNextPage = function () {\n      return Math.min(calculatePage(undefined, _this.state, _this.props), _this.state.current + (_this.props.showLessItems ? 3 : 5));\n    };\n    _this.getItemIcon = function (icon, label) {\n      var prefixCls = _this.props.prefixCls;\n      var iconNode = icon || /*#__PURE__*/React.createElement(\"button\", {\n        type: \"button\",\n        \"aria-label\": label,\n        className: \"\".concat(prefixCls, \"-item-link\")\n      });\n      if (typeof icon === 'function') {\n        iconNode = /*#__PURE__*/React.createElement(icon, _objectSpread({}, _this.props));\n      }\n      return iconNode;\n    };\n    _this.isValid = function (page) {\n      var total = _this.props.total;\n      return isInteger(page) && page !== _this.state.current && isInteger(total) && total > 0;\n    };\n    _this.shouldDisplayQuickJumper = function () {\n      var _this$props = _this.props,\n        showQuickJumper = _this$props.showQuickJumper,\n        total = _this$props.total;\n      var pageSize = _this.state.pageSize;\n      if (total <= pageSize) {\n        return false;\n      }\n      return showQuickJumper;\n    };\n    _this.handleKeyDown = function (e) {\n      if (e.keyCode === KEYCODE.ARROW_UP || e.keyCode === KEYCODE.ARROW_DOWN) {\n        e.preventDefault();\n      }\n    };\n    _this.handleKeyUp = function (e) {\n      var value = _this.getValidValue(e);\n      var currentInputValue = _this.state.currentInputValue;\n      if (value !== currentInputValue) {\n        _this.setState({\n          currentInputValue: value\n        });\n      }\n      if (e.keyCode === KEYCODE.ENTER) {\n        _this.handleChange(value);\n      } else if (e.keyCode === KEYCODE.ARROW_UP) {\n        _this.handleChange(value - 1);\n      } else if (e.keyCode === KEYCODE.ARROW_DOWN) {\n        _this.handleChange(value + 1);\n      }\n    };\n    _this.handleBlur = function (e) {\n      var value = _this.getValidValue(e);\n      _this.handleChange(value);\n    };\n    _this.changePageSize = function (size) {\n      var current = _this.state.current;\n      var newCurrent = calculatePage(size, _this.state, _this.props);\n      current = current > newCurrent ? newCurrent : current;\n      // fix the issue:\n      // Once 'total' is 0, 'current' in 'onShowSizeChange' is 0, which is not correct.\n      if (newCurrent === 0) {\n        // eslint-disable-next-line prefer-destructuring\n        current = _this.state.current;\n      }\n      if (typeof size === 'number') {\n        if (!('pageSize' in _this.props)) {\n          _this.setState({\n            pageSize: size\n          });\n        }\n        if (!('current' in _this.props)) {\n          _this.setState({\n            current: current,\n            currentInputValue: current\n          });\n        }\n      }\n      _this.props.onShowSizeChange(current, size);\n      if ('onChange' in _this.props && _this.props.onChange) {\n        _this.props.onChange(current, size);\n      }\n    };\n    _this.handleChange = function (page) {\n      var _this$props2 = _this.props,\n        disabled = _this$props2.disabled,\n        onChange = _this$props2.onChange;\n      var _this$state = _this.state,\n        pageSize = _this$state.pageSize,\n        current = _this$state.current,\n        currentInputValue = _this$state.currentInputValue;\n      if (_this.isValid(page) && !disabled) {\n        var currentPage = calculatePage(undefined, _this.state, _this.props);\n        var newPage = page;\n        if (page > currentPage) {\n          newPage = currentPage;\n        } else if (page < 1) {\n          newPage = 1;\n        }\n        if (!('current' in _this.props)) {\n          _this.setState({\n            current: newPage\n          });\n        }\n        if (newPage !== currentInputValue) {\n          _this.setState({\n            currentInputValue: newPage\n          });\n        }\n        onChange(newPage, pageSize);\n        return newPage;\n      }\n      return current;\n    };\n    _this.prev = function () {\n      if (_this.hasPrev()) {\n        _this.handleChange(_this.state.current - 1);\n      }\n    };\n    _this.next = function () {\n      if (_this.hasNext()) {\n        _this.handleChange(_this.state.current + 1);\n      }\n    };\n    _this.jumpPrev = function () {\n      _this.handleChange(_this.getJumpPrevPage());\n    };\n    _this.jumpNext = function () {\n      _this.handleChange(_this.getJumpNextPage());\n    };\n    _this.hasPrev = function () {\n      return _this.state.current > 1;\n    };\n    _this.hasNext = function () {\n      return _this.state.current < calculatePage(undefined, _this.state, _this.props);\n    };\n    _this.runIfEnter = function (event, callback) {\n      if (event.key === 'Enter' || event.charCode === 13) {\n        for (var _len = arguments.length, restParams = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n          restParams[_key - 2] = arguments[_key];\n        }\n        callback.apply(void 0, restParams);\n      }\n    };\n    _this.runIfEnterPrev = function (e) {\n      _this.runIfEnter(e, _this.prev);\n    };\n    _this.runIfEnterNext = function (e) {\n      _this.runIfEnter(e, _this.next);\n    };\n    _this.runIfEnterJumpPrev = function (e) {\n      _this.runIfEnter(e, _this.jumpPrev);\n    };\n    _this.runIfEnterJumpNext = function (e) {\n      _this.runIfEnter(e, _this.jumpNext);\n    };\n    _this.handleGoTO = function (e) {\n      if (e.keyCode === KEYCODE.ENTER || e.type === 'click') {\n        _this.handleChange(_this.state.currentInputValue);\n      }\n    };\n    _this.renderPrev = function (prevPage) {\n      var _this$props3 = _this.props,\n        prevIcon = _this$props3.prevIcon,\n        itemRender = _this$props3.itemRender;\n      var prevButton = itemRender(prevPage, 'prev', _this.getItemIcon(prevIcon, 'prev page'));\n      var disabled = !_this.hasPrev();\n      return /*#__PURE__*/isValidElement(prevButton) ? /*#__PURE__*/cloneElement(prevButton, {\n        disabled: disabled\n      }) : prevButton;\n    };\n    _this.renderNext = function (nextPage) {\n      var _this$props4 = _this.props,\n        nextIcon = _this$props4.nextIcon,\n        itemRender = _this$props4.itemRender;\n      var nextButton = itemRender(nextPage, 'next', _this.getItemIcon(nextIcon, 'next page'));\n      var disabled = !_this.hasNext();\n      return /*#__PURE__*/isValidElement(nextButton) ? /*#__PURE__*/cloneElement(nextButton, {\n        disabled: disabled\n      }) : nextButton;\n    };\n    var hasOnChange = props.onChange !== noop;\n    var hasCurrent = ('current' in props);\n    if (hasCurrent && !hasOnChange) {\n      // eslint-disable-next-line no-console\n      console.warn('Warning: You provided a `current` prop to a Pagination component without an `onChange` handler. This will render a read-only component.');\n    }\n    var _current = props.defaultCurrent;\n    if ('current' in props) {\n      // eslint-disable-next-line prefer-destructuring\n      _current = props.current;\n    }\n    var _pageSize = props.defaultPageSize;\n    if ('pageSize' in props) {\n      // eslint-disable-next-line prefer-destructuring\n      _pageSize = props.pageSize;\n    }\n    _current = Math.min(_current, calculatePage(_pageSize, undefined, props));\n    _this.state = {\n      current: _current,\n      currentInputValue: _current,\n      pageSize: _pageSize\n    };\n    return _this;\n  }\n  _createClass(Pagination, [{\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(_, prevState) {\n      // When current page change, fix focused style of prev item\n      // A hacky solution of https://github.com/ant-design/ant-design/issues/8948\n      var prefixCls = this.props.prefixCls;\n      if (prevState.current !== this.state.current && this.paginationNode.current) {\n        var lastCurrentNode = this.paginationNode.current.querySelector(\".\".concat(prefixCls, \"-item-\").concat(prevState.current));\n        if (lastCurrentNode && document.activeElement === lastCurrentNode) {\n          var _lastCurrentNode$blur;\n          lastCurrentNode === null || lastCurrentNode === void 0 ? void 0 : (_lastCurrentNode$blur = lastCurrentNode.blur) === null || _lastCurrentNode$blur === void 0 ? void 0 : _lastCurrentNode$blur.call(lastCurrentNode);\n        }\n      }\n    }\n  }, {\n    key: \"getValidValue\",\n    value: function getValidValue(e) {\n      var inputValue = e.target.value;\n      var allPages = calculatePage(undefined, this.state, this.props);\n      var currentInputValue = this.state.currentInputValue;\n      var value;\n      if (inputValue === '') {\n        value = inputValue;\n        // eslint-disable-next-line no-restricted-globals\n      } else if (Number.isNaN(Number(inputValue))) {\n        value = currentInputValue;\n      } else if (inputValue >= allPages) {\n        value = allPages;\n      } else {\n        value = Number(inputValue);\n      }\n      return value;\n    }\n  }, {\n    key: \"getShowSizeChanger\",\n    value: function getShowSizeChanger() {\n      var _this$props5 = this.props,\n        showSizeChanger = _this$props5.showSizeChanger,\n        total = _this$props5.total,\n        totalBoundaryShowSizeChanger = _this$props5.totalBoundaryShowSizeChanger;\n      if (typeof showSizeChanger !== 'undefined') {\n        return showSizeChanger;\n      }\n      return total > totalBoundaryShowSizeChanger;\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props6 = this.props,\n        prefixCls = _this$props6.prefixCls,\n        className = _this$props6.className,\n        style = _this$props6.style,\n        disabled = _this$props6.disabled,\n        hideOnSinglePage = _this$props6.hideOnSinglePage,\n        total = _this$props6.total,\n        locale = _this$props6.locale,\n        showQuickJumper = _this$props6.showQuickJumper,\n        showLessItems = _this$props6.showLessItems,\n        showTitle = _this$props6.showTitle,\n        showTotal = _this$props6.showTotal,\n        simple = _this$props6.simple,\n        itemRender = _this$props6.itemRender,\n        showPrevNextJumpers = _this$props6.showPrevNextJumpers,\n        jumpPrevIcon = _this$props6.jumpPrevIcon,\n        jumpNextIcon = _this$props6.jumpNextIcon,\n        selectComponentClass = _this$props6.selectComponentClass,\n        selectPrefixCls = _this$props6.selectPrefixCls,\n        pageSizeOptions = _this$props6.pageSizeOptions;\n      var _this$state2 = this.state,\n        current = _this$state2.current,\n        pageSize = _this$state2.pageSize,\n        currentInputValue = _this$state2.currentInputValue;\n      // When hideOnSinglePage is true and there is only 1 page, hide the pager\n      if (hideOnSinglePage === true && total <= pageSize) {\n        return null;\n      }\n      var allPages = calculatePage(undefined, this.state, this.props);\n      var pagerList = [];\n      var jumpPrev = null;\n      var jumpNext = null;\n      var firstPager = null;\n      var lastPager = null;\n      var gotoButton = null;\n      var goButton = showQuickJumper && showQuickJumper.goButton;\n      var pageBufferSize = showLessItems ? 1 : 2;\n      var prevPage = current - 1 > 0 ? current - 1 : 0;\n      var nextPage = current + 1 < allPages ? current + 1 : allPages;\n      var dataOrAriaAttributeProps = pickAttrs(this.props, {\n        aria: true,\n        data: true\n      });\n      var totalText = showTotal && /*#__PURE__*/React.createElement(\"li\", {\n        className: \"\".concat(prefixCls, \"-total-text\")\n      }, showTotal(total, [total === 0 ? 0 : (current - 1) * pageSize + 1, current * pageSize > total ? total : current * pageSize]));\n      if (simple) {\n        if (goButton) {\n          if (typeof goButton === 'boolean') {\n            gotoButton = /*#__PURE__*/React.createElement(\"button\", {\n              type: \"button\",\n              onClick: this.handleGoTO,\n              onKeyUp: this.handleGoTO\n            }, locale.jump_to_confirm);\n          } else {\n            gotoButton = /*#__PURE__*/React.createElement(\"span\", {\n              onClick: this.handleGoTO,\n              onKeyUp: this.handleGoTO\n            }, goButton);\n          }\n          gotoButton = /*#__PURE__*/React.createElement(\"li\", {\n            title: showTitle ? \"\".concat(locale.jump_to).concat(current, \"/\").concat(allPages) : null,\n            className: \"\".concat(prefixCls, \"-simple-pager\")\n          }, gotoButton);\n        }\n        return /*#__PURE__*/React.createElement(\"ul\", _extends({\n          className: classNames(prefixCls, \"\".concat(prefixCls, \"-simple\"), _defineProperty({}, \"\".concat(prefixCls, \"-disabled\"), disabled), className),\n          style: style,\n          ref: this.paginationNode\n        }, dataOrAriaAttributeProps), totalText, /*#__PURE__*/React.createElement(\"li\", {\n          title: showTitle ? locale.prev_page : null,\n          onClick: this.prev,\n          tabIndex: this.hasPrev() ? 0 : null,\n          onKeyPress: this.runIfEnterPrev,\n          className: classNames(\"\".concat(prefixCls, \"-prev\"), _defineProperty({}, \"\".concat(prefixCls, \"-disabled\"), !this.hasPrev())),\n          \"aria-disabled\": !this.hasPrev()\n        }, this.renderPrev(prevPage)), /*#__PURE__*/React.createElement(\"li\", {\n          title: showTitle ? \"\".concat(current, \"/\").concat(allPages) : null,\n          className: \"\".concat(prefixCls, \"-simple-pager\")\n        }, /*#__PURE__*/React.createElement(\"input\", {\n          type: \"text\",\n          value: currentInputValue,\n          disabled: disabled,\n          onKeyDown: this.handleKeyDown,\n          onKeyUp: this.handleKeyUp,\n          onChange: this.handleKeyUp,\n          onBlur: this.handleBlur,\n          size: 3\n        }), /*#__PURE__*/React.createElement(\"span\", {\n          className: \"\".concat(prefixCls, \"-slash\")\n        }, \"/\"), allPages), /*#__PURE__*/React.createElement(\"li\", {\n          title: showTitle ? locale.next_page : null,\n          onClick: this.next,\n          tabIndex: this.hasPrev() ? 0 : null,\n          onKeyPress: this.runIfEnterNext,\n          className: classNames(\"\".concat(prefixCls, \"-next\"), _defineProperty({}, \"\".concat(prefixCls, \"-disabled\"), !this.hasNext())),\n          \"aria-disabled\": !this.hasNext()\n        }, this.renderNext(nextPage)), gotoButton);\n      }\n      if (allPages <= 3 + pageBufferSize * 2) {\n        var pagerProps = {\n          locale: locale,\n          rootPrefixCls: prefixCls,\n          onClick: this.handleChange,\n          onKeyPress: this.runIfEnter,\n          showTitle: showTitle,\n          itemRender: itemRender\n        };\n        if (!allPages) {\n          pagerList.push( /*#__PURE__*/React.createElement(Pager, _extends({}, pagerProps, {\n            key: \"noPager\",\n            page: 1,\n            className: \"\".concat(prefixCls, \"-item-disabled\")\n          })));\n        }\n        for (var i = 1; i <= allPages; i += 1) {\n          var active = current === i;\n          pagerList.push( /*#__PURE__*/React.createElement(Pager, _extends({}, pagerProps, {\n            key: i,\n            page: i,\n            active: active\n          })));\n        }\n      } else {\n        var prevItemTitle = showLessItems ? locale.prev_3 : locale.prev_5;\n        var nextItemTitle = showLessItems ? locale.next_3 : locale.next_5;\n        if (showPrevNextJumpers) {\n          jumpPrev = /*#__PURE__*/React.createElement(\"li\", {\n            title: showTitle ? prevItemTitle : null,\n            key: \"prev\",\n            onClick: this.jumpPrev,\n            tabIndex: 0,\n            onKeyPress: this.runIfEnterJumpPrev,\n            className: classNames(\"\".concat(prefixCls, \"-jump-prev\"), _defineProperty({}, \"\".concat(prefixCls, \"-jump-prev-custom-icon\"), !!jumpPrevIcon))\n          }, itemRender(this.getJumpPrevPage(), 'jump-prev', this.getItemIcon(jumpPrevIcon, 'prev page')));\n          jumpNext = /*#__PURE__*/React.createElement(\"li\", {\n            title: showTitle ? nextItemTitle : null,\n            key: \"next\",\n            tabIndex: 0,\n            onClick: this.jumpNext,\n            onKeyPress: this.runIfEnterJumpNext,\n            className: classNames(\"\".concat(prefixCls, \"-jump-next\"), _defineProperty({}, \"\".concat(prefixCls, \"-jump-next-custom-icon\"), !!jumpNextIcon))\n          }, itemRender(this.getJumpNextPage(), 'jump-next', this.getItemIcon(jumpNextIcon, 'next page')));\n        }\n        lastPager = /*#__PURE__*/React.createElement(Pager, {\n          locale: locale,\n          last: true,\n          rootPrefixCls: prefixCls,\n          onClick: this.handleChange,\n          onKeyPress: this.runIfEnter,\n          key: allPages,\n          page: allPages,\n          active: false,\n          showTitle: showTitle,\n          itemRender: itemRender\n        });\n        firstPager = /*#__PURE__*/React.createElement(Pager, {\n          locale: locale,\n          rootPrefixCls: prefixCls,\n          onClick: this.handleChange,\n          onKeyPress: this.runIfEnter,\n          key: 1,\n          page: 1,\n          active: false,\n          showTitle: showTitle,\n          itemRender: itemRender\n        });\n        var left = Math.max(1, current - pageBufferSize);\n        var right = Math.min(current + pageBufferSize, allPages);\n        if (current - 1 <= pageBufferSize) {\n          right = 1 + pageBufferSize * 2;\n        }\n        if (allPages - current <= pageBufferSize) {\n          left = allPages - pageBufferSize * 2;\n        }\n        for (var _i = left; _i <= right; _i += 1) {\n          var _active = current === _i;\n          pagerList.push( /*#__PURE__*/React.createElement(Pager, {\n            locale: locale,\n            rootPrefixCls: prefixCls,\n            onClick: this.handleChange,\n            onKeyPress: this.runIfEnter,\n            key: _i,\n            page: _i,\n            active: _active,\n            showTitle: showTitle,\n            itemRender: itemRender\n          }));\n        }\n        if (current - 1 >= pageBufferSize * 2 && current !== 1 + 2) {\n          pagerList[0] = /*#__PURE__*/cloneElement(pagerList[0], {\n            className: \"\".concat(prefixCls, \"-item-after-jump-prev\")\n          });\n          pagerList.unshift(jumpPrev);\n        }\n        if (allPages - current >= pageBufferSize * 2 && current !== allPages - 2) {\n          pagerList[pagerList.length - 1] = /*#__PURE__*/cloneElement(pagerList[pagerList.length - 1], {\n            className: \"\".concat(prefixCls, \"-item-before-jump-next\")\n          });\n          pagerList.push(jumpNext);\n        }\n        if (left !== 1) {\n          pagerList.unshift(firstPager);\n        }\n        if (right !== allPages) {\n          pagerList.push(lastPager);\n        }\n      }\n      var prevDisabled = !this.hasPrev() || !allPages;\n      var nextDisabled = !this.hasNext() || !allPages;\n      return /*#__PURE__*/React.createElement(\"ul\", _extends({\n        className: classNames(prefixCls, className, _defineProperty({}, \"\".concat(prefixCls, \"-disabled\"), disabled)),\n        style: style,\n        ref: this.paginationNode\n      }, dataOrAriaAttributeProps), totalText, /*#__PURE__*/React.createElement(\"li\", {\n        title: showTitle ? locale.prev_page : null,\n        onClick: this.prev,\n        tabIndex: prevDisabled ? null : 0,\n        onKeyPress: this.runIfEnterPrev,\n        className: classNames(\"\".concat(prefixCls, \"-prev\"), _defineProperty({}, \"\".concat(prefixCls, \"-disabled\"), prevDisabled)),\n        \"aria-disabled\": prevDisabled\n      }, this.renderPrev(prevPage)), pagerList, /*#__PURE__*/React.createElement(\"li\", {\n        title: showTitle ? locale.next_page : null,\n        onClick: this.next,\n        tabIndex: nextDisabled ? null : 0,\n        onKeyPress: this.runIfEnterNext,\n        className: classNames(\"\".concat(prefixCls, \"-next\"), _defineProperty({}, \"\".concat(prefixCls, \"-disabled\"), nextDisabled)),\n        \"aria-disabled\": nextDisabled\n      }, this.renderNext(nextPage)), /*#__PURE__*/React.createElement(Options, {\n        disabled: disabled,\n        locale: locale,\n        rootPrefixCls: prefixCls,\n        selectComponentClass: selectComponentClass,\n        selectPrefixCls: selectPrefixCls,\n        changeSize: this.getShowSizeChanger() ? this.changePageSize : null,\n        current: current,\n        pageSize: pageSize,\n        pageSizeOptions: pageSizeOptions,\n        quickGo: this.shouldDisplayQuickJumper() ? this.handleChange : null,\n        goButton: goButton\n      }));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(props, prevState) {\n      var newState = {};\n      if ('current' in props) {\n        newState.current = props.current;\n        if (props.current !== prevState.current) {\n          newState.currentInputValue = newState.current;\n        }\n      }\n      if ('pageSize' in props && props.pageSize !== prevState.pageSize) {\n        var current = prevState.current;\n        var newCurrent = calculatePage(props.pageSize, prevState, props);\n        current = current > newCurrent ? newCurrent : current;\n        if (!('current' in props)) {\n          newState.current = current;\n          newState.currentInputValue = current;\n        }\n        newState.pageSize = props.pageSize;\n      }\n      return newState;\n    }\n  }]);\n  return Pagination;\n}(React.Component);\nPagination.defaultProps = {\n  defaultCurrent: 1,\n  total: 0,\n  defaultPageSize: 10,\n  onChange: noop,\n  className: '',\n  selectPrefixCls: 'rc-select',\n  prefixCls: 'rc-pagination',\n  selectComponentClass: null,\n  hideOnSinglePage: false,\n  showPrevNextJumpers: true,\n  showQuickJumper: false,\n  showLessItems: false,\n  showTitle: true,\n  onShowSizeChange: noop,\n  locale: LOCALE,\n  style: {},\n  itemRender: defaultItemRender,\n  totalBoundaryShowSizeChanger: 50\n};\nexport default Pagination;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,KAAK,IAAIC,YAAY,EAAEC,cAAc,QAAQ,OAAO;AAC3D,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,IAAIA,CAAA,EAAG,CAAC;AACjB,SAASC,SAASA,CAACC,CAAC,EAAE;EACpB,IAAIC,KAAK,GAAGC,MAAM,CAACF,CAAC,CAAC;EACrB;IACE;IACA,OAAOC,KAAK,KAAK,QAAQ,IAAI,CAACC,MAAM,CAACC,KAAK,CAACF,KAAK,CAAC,IAAIG,QAAQ,CAACH,KAAK,CAAC,IAAII,IAAI,CAACC,KAAK,CAACL,KAAK,CAAC,KAAKA;EAAK;AAEvG;AACA,IAAIM,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,IAAI,EAAEC,IAAI,EAAEC,OAAO,EAAE;EACtE,OAAOA,OAAO;AAChB,CAAC;AACD,SAASC,aAAaA,CAACC,CAAC,EAAEC,KAAK,EAAEC,KAAK,EAAE;EACtC,IAAIC,QAAQ,GAAG,OAAOH,CAAC,KAAK,WAAW,GAAGC,KAAK,CAACE,QAAQ,GAAGH,CAAC;EAC5D,OAAOP,IAAI,CAACC,KAAK,CAAC,CAACQ,KAAK,CAACE,KAAK,GAAG,CAAC,IAAID,QAAQ,CAAC,GAAG,CAAC;AACrD;AACA,IAAIE,UAAU,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EACxD/B,SAAS,CAAC8B,UAAU,EAAEC,gBAAgB,CAAC;EACvC,IAAIC,MAAM,GAAG/B,YAAY,CAAC6B,UAAU,CAAC;EACrC,SAASA,UAAUA,CAACH,KAAK,EAAE;IACzB,IAAIM,KAAK;IACTnC,eAAe,CAAC,IAAI,EAAEgC,UAAU,CAAC;IACjCG,KAAK,GAAGD,MAAM,CAACE,IAAI,CAAC,IAAI,EAAEP,KAAK,CAAC;IAChCM,KAAK,CAACE,cAAc,GAAG,aAAa/B,KAAK,CAACgC,SAAS,CAAC,CAAC;IACrDH,KAAK,CAACI,eAAe,GAAG,YAAY;MAClC,OAAOnB,IAAI,CAACoB,GAAG,CAAC,CAAC,EAAEL,KAAK,CAACP,KAAK,CAACa,OAAO,IAAIN,KAAK,CAACN,KAAK,CAACa,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAC/E,CAAC;IACDP,KAAK,CAACQ,eAAe,GAAG,YAAY;MAClC,OAAOvB,IAAI,CAACwB,GAAG,CAAClB,aAAa,CAACmB,SAAS,EAAEV,KAAK,CAACP,KAAK,EAAEO,KAAK,CAACN,KAAK,CAAC,EAAEM,KAAK,CAACP,KAAK,CAACa,OAAO,IAAIN,KAAK,CAACN,KAAK,CAACa,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAChI,CAAC;IACDP,KAAK,CAACW,WAAW,GAAG,UAAUC,IAAI,EAAEC,KAAK,EAAE;MACzC,IAAIC,SAAS,GAAGd,KAAK,CAACN,KAAK,CAACoB,SAAS;MACrC,IAAIC,QAAQ,GAAGH,IAAI,IAAI,aAAazC,KAAK,CAAC6C,aAAa,CAAC,QAAQ,EAAE;QAChE3B,IAAI,EAAE,QAAQ;QACd,YAAY,EAAEwB,KAAK;QACnBI,SAAS,EAAE,EAAE,CAACC,MAAM,CAACJ,SAAS,EAAE,YAAY;MAC9C,CAAC,CAAC;MACF,IAAI,OAAOF,IAAI,KAAK,UAAU,EAAE;QAC9BG,QAAQ,GAAG,aAAa5C,KAAK,CAAC6C,aAAa,CAACJ,IAAI,EAAEhD,aAAa,CAAC,CAAC,CAAC,EAAEoC,KAAK,CAACN,KAAK,CAAC,CAAC;MACnF;MACA,OAAOqB,QAAQ;IACjB,CAAC;IACDf,KAAK,CAACmB,OAAO,GAAG,UAAU/B,IAAI,EAAE;MAC9B,IAAIQ,KAAK,GAAGI,KAAK,CAACN,KAAK,CAACE,KAAK;MAC7B,OAAOjB,SAAS,CAACS,IAAI,CAAC,IAAIA,IAAI,KAAKY,KAAK,CAACP,KAAK,CAACa,OAAO,IAAI3B,SAAS,CAACiB,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC;IACzF,CAAC;IACDI,KAAK,CAACoB,wBAAwB,GAAG,YAAY;MAC3C,IAAIC,WAAW,GAAGrB,KAAK,CAACN,KAAK;QAC3B4B,eAAe,GAAGD,WAAW,CAACC,eAAe;QAC7C1B,KAAK,GAAGyB,WAAW,CAACzB,KAAK;MAC3B,IAAID,QAAQ,GAAGK,KAAK,CAACP,KAAK,CAACE,QAAQ;MACnC,IAAIC,KAAK,IAAID,QAAQ,EAAE;QACrB,OAAO,KAAK;MACd;MACA,OAAO2B,eAAe;IACxB,CAAC;IACDtB,KAAK,CAACuB,aAAa,GAAG,UAAUC,CAAC,EAAE;MACjC,IAAIA,CAAC,CAACC,OAAO,KAAKnD,OAAO,CAACoD,QAAQ,IAAIF,CAAC,CAACC,OAAO,KAAKnD,OAAO,CAACqD,UAAU,EAAE;QACtEH,CAAC,CAACI,cAAc,CAAC,CAAC;MACpB;IACF,CAAC;IACD5B,KAAK,CAAC6B,WAAW,GAAG,UAAUL,CAAC,EAAE;MAC/B,IAAI3C,KAAK,GAAGmB,KAAK,CAAC8B,aAAa,CAACN,CAAC,CAAC;MAClC,IAAIO,iBAAiB,GAAG/B,KAAK,CAACP,KAAK,CAACsC,iBAAiB;MACrD,IAAIlD,KAAK,KAAKkD,iBAAiB,EAAE;QAC/B/B,KAAK,CAACgC,QAAQ,CAAC;UACbD,iBAAiB,EAAElD;QACrB,CAAC,CAAC;MACJ;MACA,IAAI2C,CAAC,CAACC,OAAO,KAAKnD,OAAO,CAAC2D,KAAK,EAAE;QAC/BjC,KAAK,CAACkC,YAAY,CAACrD,KAAK,CAAC;MAC3B,CAAC,MAAM,IAAI2C,CAAC,CAACC,OAAO,KAAKnD,OAAO,CAACoD,QAAQ,EAAE;QACzC1B,KAAK,CAACkC,YAAY,CAACrD,KAAK,GAAG,CAAC,CAAC;MAC/B,CAAC,MAAM,IAAI2C,CAAC,CAACC,OAAO,KAAKnD,OAAO,CAACqD,UAAU,EAAE;QAC3C3B,KAAK,CAACkC,YAAY,CAACrD,KAAK,GAAG,CAAC,CAAC;MAC/B;IACF,CAAC;IACDmB,KAAK,CAACmC,UAAU,GAAG,UAAUX,CAAC,EAAE;MAC9B,IAAI3C,KAAK,GAAGmB,KAAK,CAAC8B,aAAa,CAACN,CAAC,CAAC;MAClCxB,KAAK,CAACkC,YAAY,CAACrD,KAAK,CAAC;IAC3B,CAAC;IACDmB,KAAK,CAACoC,cAAc,GAAG,UAAUC,IAAI,EAAE;MACrC,IAAI/B,OAAO,GAAGN,KAAK,CAACP,KAAK,CAACa,OAAO;MACjC,IAAIgC,UAAU,GAAG/C,aAAa,CAAC8C,IAAI,EAAErC,KAAK,CAACP,KAAK,EAAEO,KAAK,CAACN,KAAK,CAAC;MAC9DY,OAAO,GAAGA,OAAO,GAAGgC,UAAU,GAAGA,UAAU,GAAGhC,OAAO;MACrD;MACA;MACA,IAAIgC,UAAU,KAAK,CAAC,EAAE;QACpB;QACAhC,OAAO,GAAGN,KAAK,CAACP,KAAK,CAACa,OAAO;MAC/B;MACA,IAAI,OAAO+B,IAAI,KAAK,QAAQ,EAAE;QAC5B,IAAI,EAAE,UAAU,IAAIrC,KAAK,CAACN,KAAK,CAAC,EAAE;UAChCM,KAAK,CAACgC,QAAQ,CAAC;YACbrC,QAAQ,EAAE0C;UACZ,CAAC,CAAC;QACJ;QACA,IAAI,EAAE,SAAS,IAAIrC,KAAK,CAACN,KAAK,CAAC,EAAE;UAC/BM,KAAK,CAACgC,QAAQ,CAAC;YACb1B,OAAO,EAAEA,OAAO;YAChByB,iBAAiB,EAAEzB;UACrB,CAAC,CAAC;QACJ;MACF;MACAN,KAAK,CAACN,KAAK,CAAC6C,gBAAgB,CAACjC,OAAO,EAAE+B,IAAI,CAAC;MAC3C,IAAI,UAAU,IAAIrC,KAAK,CAACN,KAAK,IAAIM,KAAK,CAACN,KAAK,CAAC8C,QAAQ,EAAE;QACrDxC,KAAK,CAACN,KAAK,CAAC8C,QAAQ,CAAClC,OAAO,EAAE+B,IAAI,CAAC;MACrC;IACF,CAAC;IACDrC,KAAK,CAACkC,YAAY,GAAG,UAAU9C,IAAI,EAAE;MACnC,IAAIqD,YAAY,GAAGzC,KAAK,CAACN,KAAK;QAC5BgD,QAAQ,GAAGD,YAAY,CAACC,QAAQ;QAChCF,QAAQ,GAAGC,YAAY,CAACD,QAAQ;MAClC,IAAIG,WAAW,GAAG3C,KAAK,CAACP,KAAK;QAC3BE,QAAQ,GAAGgD,WAAW,CAAChD,QAAQ;QAC/BW,OAAO,GAAGqC,WAAW,CAACrC,OAAO;QAC7ByB,iBAAiB,GAAGY,WAAW,CAACZ,iBAAiB;MACnD,IAAI/B,KAAK,CAACmB,OAAO,CAAC/B,IAAI,CAAC,IAAI,CAACsD,QAAQ,EAAE;QACpC,IAAIE,WAAW,GAAGrD,aAAa,CAACmB,SAAS,EAAEV,KAAK,CAACP,KAAK,EAAEO,KAAK,CAACN,KAAK,CAAC;QACpE,IAAImD,OAAO,GAAGzD,IAAI;QAClB,IAAIA,IAAI,GAAGwD,WAAW,EAAE;UACtBC,OAAO,GAAGD,WAAW;QACvB,CAAC,MAAM,IAAIxD,IAAI,GAAG,CAAC,EAAE;UACnByD,OAAO,GAAG,CAAC;QACb;QACA,IAAI,EAAE,SAAS,IAAI7C,KAAK,CAACN,KAAK,CAAC,EAAE;UAC/BM,KAAK,CAACgC,QAAQ,CAAC;YACb1B,OAAO,EAAEuC;UACX,CAAC,CAAC;QACJ;QACA,IAAIA,OAAO,KAAKd,iBAAiB,EAAE;UACjC/B,KAAK,CAACgC,QAAQ,CAAC;YACbD,iBAAiB,EAAEc;UACrB,CAAC,CAAC;QACJ;QACAL,QAAQ,CAACK,OAAO,EAAElD,QAAQ,CAAC;QAC3B,OAAOkD,OAAO;MAChB;MACA,OAAOvC,OAAO;IAChB,CAAC;IACDN,KAAK,CAAC8C,IAAI,GAAG,YAAY;MACvB,IAAI9C,KAAK,CAAC+C,OAAO,CAAC,CAAC,EAAE;QACnB/C,KAAK,CAACkC,YAAY,CAAClC,KAAK,CAACP,KAAK,CAACa,OAAO,GAAG,CAAC,CAAC;MAC7C;IACF,CAAC;IACDN,KAAK,CAACgD,IAAI,GAAG,YAAY;MACvB,IAAIhD,KAAK,CAACiD,OAAO,CAAC,CAAC,EAAE;QACnBjD,KAAK,CAACkC,YAAY,CAAClC,KAAK,CAACP,KAAK,CAACa,OAAO,GAAG,CAAC,CAAC;MAC7C;IACF,CAAC;IACDN,KAAK,CAACkD,QAAQ,GAAG,YAAY;MAC3BlD,KAAK,CAACkC,YAAY,CAAClC,KAAK,CAACI,eAAe,CAAC,CAAC,CAAC;IAC7C,CAAC;IACDJ,KAAK,CAACmD,QAAQ,GAAG,YAAY;MAC3BnD,KAAK,CAACkC,YAAY,CAAClC,KAAK,CAACQ,eAAe,CAAC,CAAC,CAAC;IAC7C,CAAC;IACDR,KAAK,CAAC+C,OAAO,GAAG,YAAY;MAC1B,OAAO/C,KAAK,CAACP,KAAK,CAACa,OAAO,GAAG,CAAC;IAChC,CAAC;IACDN,KAAK,CAACiD,OAAO,GAAG,YAAY;MAC1B,OAAOjD,KAAK,CAACP,KAAK,CAACa,OAAO,GAAGf,aAAa,CAACmB,SAAS,EAAEV,KAAK,CAACP,KAAK,EAAEO,KAAK,CAACN,KAAK,CAAC;IACjF,CAAC;IACDM,KAAK,CAACoD,UAAU,GAAG,UAAUC,KAAK,EAAEC,QAAQ,EAAE;MAC5C,IAAID,KAAK,CAACE,GAAG,KAAK,OAAO,IAAIF,KAAK,CAACG,QAAQ,KAAK,EAAE,EAAE;QAClD,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,UAAU,GAAG,IAAIC,KAAK,CAACJ,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;UAChHF,UAAU,CAACE,IAAI,GAAG,CAAC,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;QACxC;QACAR,QAAQ,CAACS,KAAK,CAAC,KAAK,CAAC,EAAEH,UAAU,CAAC;MACpC;IACF,CAAC;IACD5D,KAAK,CAACgE,cAAc,GAAG,UAAUxC,CAAC,EAAE;MAClCxB,KAAK,CAACoD,UAAU,CAAC5B,CAAC,EAAExB,KAAK,CAAC8C,IAAI,CAAC;IACjC,CAAC;IACD9C,KAAK,CAACiE,cAAc,GAAG,UAAUzC,CAAC,EAAE;MAClCxB,KAAK,CAACoD,UAAU,CAAC5B,CAAC,EAAExB,KAAK,CAACgD,IAAI,CAAC;IACjC,CAAC;IACDhD,KAAK,CAACkE,kBAAkB,GAAG,UAAU1C,CAAC,EAAE;MACtCxB,KAAK,CAACoD,UAAU,CAAC5B,CAAC,EAAExB,KAAK,CAACkD,QAAQ,CAAC;IACrC,CAAC;IACDlD,KAAK,CAACmE,kBAAkB,GAAG,UAAU3C,CAAC,EAAE;MACtCxB,KAAK,CAACoD,UAAU,CAAC5B,CAAC,EAAExB,KAAK,CAACmD,QAAQ,CAAC;IACrC,CAAC;IACDnD,KAAK,CAACoE,UAAU,GAAG,UAAU5C,CAAC,EAAE;MAC9B,IAAIA,CAAC,CAACC,OAAO,KAAKnD,OAAO,CAAC2D,KAAK,IAAIT,CAAC,CAACnC,IAAI,KAAK,OAAO,EAAE;QACrDW,KAAK,CAACkC,YAAY,CAAClC,KAAK,CAACP,KAAK,CAACsC,iBAAiB,CAAC;MACnD;IACF,CAAC;IACD/B,KAAK,CAACqE,UAAU,GAAG,UAAUC,QAAQ,EAAE;MACrC,IAAIC,YAAY,GAAGvE,KAAK,CAACN,KAAK;QAC5B8E,QAAQ,GAAGD,YAAY,CAACC,QAAQ;QAChCC,UAAU,GAAGF,YAAY,CAACE,UAAU;MACtC,IAAIC,UAAU,GAAGD,UAAU,CAACH,QAAQ,EAAE,MAAM,EAAEtE,KAAK,CAACW,WAAW,CAAC6D,QAAQ,EAAE,WAAW,CAAC,CAAC;MACvF,IAAI9B,QAAQ,GAAG,CAAC1C,KAAK,CAAC+C,OAAO,CAAC,CAAC;MAC/B,OAAO,aAAa1E,cAAc,CAACqG,UAAU,CAAC,GAAG,aAAatG,YAAY,CAACsG,UAAU,EAAE;QACrFhC,QAAQ,EAAEA;MACZ,CAAC,CAAC,GAAGgC,UAAU;IACjB,CAAC;IACD1E,KAAK,CAAC2E,UAAU,GAAG,UAAUC,QAAQ,EAAE;MACrC,IAAIC,YAAY,GAAG7E,KAAK,CAACN,KAAK;QAC5BoF,QAAQ,GAAGD,YAAY,CAACC,QAAQ;QAChCL,UAAU,GAAGI,YAAY,CAACJ,UAAU;MACtC,IAAIM,UAAU,GAAGN,UAAU,CAACG,QAAQ,EAAE,MAAM,EAAE5E,KAAK,CAACW,WAAW,CAACmE,QAAQ,EAAE,WAAW,CAAC,CAAC;MACvF,IAAIpC,QAAQ,GAAG,CAAC1C,KAAK,CAACiD,OAAO,CAAC,CAAC;MAC/B,OAAO,aAAa5E,cAAc,CAAC0G,UAAU,CAAC,GAAG,aAAa3G,YAAY,CAAC2G,UAAU,EAAE;QACrFrC,QAAQ,EAAEA;MACZ,CAAC,CAAC,GAAGqC,UAAU;IACjB,CAAC;IACD,IAAIC,WAAW,GAAGtF,KAAK,CAAC8C,QAAQ,KAAK9D,IAAI;IACzC,IAAIuG,UAAU,IAAI,SAAS,IAAIvF,KAAK,CAAC;IACrC,IAAIuF,UAAU,IAAI,CAACD,WAAW,EAAE;MAC9B;MACAE,OAAO,CAACC,IAAI,CAAC,yIAAyI,CAAC;IACzJ;IACA,IAAIC,QAAQ,GAAG1F,KAAK,CAAC2F,cAAc;IACnC,IAAI,SAAS,IAAI3F,KAAK,EAAE;MACtB;MACA0F,QAAQ,GAAG1F,KAAK,CAACY,OAAO;IAC1B;IACA,IAAIgF,SAAS,GAAG5F,KAAK,CAAC6F,eAAe;IACrC,IAAI,UAAU,IAAI7F,KAAK,EAAE;MACvB;MACA4F,SAAS,GAAG5F,KAAK,CAACC,QAAQ;IAC5B;IACAyF,QAAQ,GAAGnG,IAAI,CAACwB,GAAG,CAAC2E,QAAQ,EAAE7F,aAAa,CAAC+F,SAAS,EAAE5E,SAAS,EAAEhB,KAAK,CAAC,CAAC;IACzEM,KAAK,CAACP,KAAK,GAAG;MACZa,OAAO,EAAE8E,QAAQ;MACjBrD,iBAAiB,EAAEqD,QAAQ;MAC3BzF,QAAQ,EAAE2F;IACZ,CAAC;IACD,OAAOtF,KAAK;EACd;EACAlC,YAAY,CAAC+B,UAAU,EAAE,CAAC;IACxB0D,GAAG,EAAE,oBAAoB;IACzB1E,KAAK,EAAE,SAAS2G,kBAAkBA,CAACC,CAAC,EAAEC,SAAS,EAAE;MAC/C;MACA;MACA,IAAI5E,SAAS,GAAG,IAAI,CAACpB,KAAK,CAACoB,SAAS;MACpC,IAAI4E,SAAS,CAACpF,OAAO,KAAK,IAAI,CAACb,KAAK,CAACa,OAAO,IAAI,IAAI,CAACJ,cAAc,CAACI,OAAO,EAAE;QAC3E,IAAIqF,eAAe,GAAG,IAAI,CAACzF,cAAc,CAACI,OAAO,CAACsF,aAAa,CAAC,GAAG,CAAC1E,MAAM,CAACJ,SAAS,EAAE,QAAQ,CAAC,CAACI,MAAM,CAACwE,SAAS,CAACpF,OAAO,CAAC,CAAC;QAC1H,IAAIqF,eAAe,IAAIE,QAAQ,CAACC,aAAa,KAAKH,eAAe,EAAE;UACjE,IAAII,qBAAqB;UACzBJ,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACI,qBAAqB,GAAGJ,eAAe,CAACK,IAAI,MAAM,IAAI,IAAID,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAAC9F,IAAI,CAAC0F,eAAe,CAAC;QACtN;MACF;IACF;EACF,CAAC,EAAE;IACDpC,GAAG,EAAE,eAAe;IACpB1E,KAAK,EAAE,SAASiD,aAAaA,CAACN,CAAC,EAAE;MAC/B,IAAIyE,UAAU,GAAGzE,CAAC,CAAC0E,MAAM,CAACrH,KAAK;MAC/B,IAAIsH,QAAQ,GAAG5G,aAAa,CAACmB,SAAS,EAAE,IAAI,CAACjB,KAAK,EAAE,IAAI,CAACC,KAAK,CAAC;MAC/D,IAAIqC,iBAAiB,GAAG,IAAI,CAACtC,KAAK,CAACsC,iBAAiB;MACpD,IAAIlD,KAAK;MACT,IAAIoH,UAAU,KAAK,EAAE,EAAE;QACrBpH,KAAK,GAAGoH,UAAU;QAClB;MACF,CAAC,MAAM,IAAInH,MAAM,CAACC,KAAK,CAACD,MAAM,CAACmH,UAAU,CAAC,CAAC,EAAE;QAC3CpH,KAAK,GAAGkD,iBAAiB;MAC3B,CAAC,MAAM,IAAIkE,UAAU,IAAIE,QAAQ,EAAE;QACjCtH,KAAK,GAAGsH,QAAQ;MAClB,CAAC,MAAM;QACLtH,KAAK,GAAGC,MAAM,CAACmH,UAAU,CAAC;MAC5B;MACA,OAAOpH,KAAK;IACd;EACF,CAAC,EAAE;IACD0E,GAAG,EAAE,oBAAoB;IACzB1E,KAAK,EAAE,SAASuH,kBAAkBA,CAAA,EAAG;MACnC,IAAIC,YAAY,GAAG,IAAI,CAAC3G,KAAK;QAC3B4G,eAAe,GAAGD,YAAY,CAACC,eAAe;QAC9C1G,KAAK,GAAGyG,YAAY,CAACzG,KAAK;QAC1B2G,4BAA4B,GAAGF,YAAY,CAACE,4BAA4B;MAC1E,IAAI,OAAOD,eAAe,KAAK,WAAW,EAAE;QAC1C,OAAOA,eAAe;MACxB;MACA,OAAO1G,KAAK,GAAG2G,4BAA4B;IAC7C;EACF,CAAC,EAAE;IACDhD,GAAG,EAAE,QAAQ;IACb1E,KAAK,EAAE,SAAS2H,MAAMA,CAAA,EAAG;MACvB,IAAIC,YAAY,GAAG,IAAI,CAAC/G,KAAK;QAC3BoB,SAAS,GAAG2F,YAAY,CAAC3F,SAAS;QAClCG,SAAS,GAAGwF,YAAY,CAACxF,SAAS;QAClCyF,KAAK,GAAGD,YAAY,CAACC,KAAK;QAC1BhE,QAAQ,GAAG+D,YAAY,CAAC/D,QAAQ;QAChCiE,gBAAgB,GAAGF,YAAY,CAACE,gBAAgB;QAChD/G,KAAK,GAAG6G,YAAY,CAAC7G,KAAK;QAC1BgH,MAAM,GAAGH,YAAY,CAACG,MAAM;QAC5BtF,eAAe,GAAGmF,YAAY,CAACnF,eAAe;QAC9Cf,aAAa,GAAGkG,YAAY,CAAClG,aAAa;QAC1CsG,SAAS,GAAGJ,YAAY,CAACI,SAAS;QAClCC,SAAS,GAAGL,YAAY,CAACK,SAAS;QAClCC,MAAM,GAAGN,YAAY,CAACM,MAAM;QAC5BtC,UAAU,GAAGgC,YAAY,CAAChC,UAAU;QACpCuC,mBAAmB,GAAGP,YAAY,CAACO,mBAAmB;QACtDC,YAAY,GAAGR,YAAY,CAACQ,YAAY;QACxCC,YAAY,GAAGT,YAAY,CAACS,YAAY;QACxCC,oBAAoB,GAAGV,YAAY,CAACU,oBAAoB;QACxDC,eAAe,GAAGX,YAAY,CAACW,eAAe;QAC9CC,eAAe,GAAGZ,YAAY,CAACY,eAAe;MAChD,IAAIC,YAAY,GAAG,IAAI,CAAC7H,KAAK;QAC3Ba,OAAO,GAAGgH,YAAY,CAAChH,OAAO;QAC9BX,QAAQ,GAAG2H,YAAY,CAAC3H,QAAQ;QAChCoC,iBAAiB,GAAGuF,YAAY,CAACvF,iBAAiB;MACpD;MACA,IAAI4E,gBAAgB,KAAK,IAAI,IAAI/G,KAAK,IAAID,QAAQ,EAAE;QAClD,OAAO,IAAI;MACb;MACA,IAAIwG,QAAQ,GAAG5G,aAAa,CAACmB,SAAS,EAAE,IAAI,CAACjB,KAAK,EAAE,IAAI,CAACC,KAAK,CAAC;MAC/D,IAAI6H,SAAS,GAAG,EAAE;MAClB,IAAIrE,QAAQ,GAAG,IAAI;MACnB,IAAIC,QAAQ,GAAG,IAAI;MACnB,IAAIqE,UAAU,GAAG,IAAI;MACrB,IAAIC,SAAS,GAAG,IAAI;MACpB,IAAIC,UAAU,GAAG,IAAI;MACrB,IAAIC,QAAQ,GAAGrG,eAAe,IAAIA,eAAe,CAACqG,QAAQ;MAC1D,IAAIC,cAAc,GAAGrH,aAAa,GAAG,CAAC,GAAG,CAAC;MAC1C,IAAI+D,QAAQ,GAAGhE,OAAO,GAAG,CAAC,GAAG,CAAC,GAAGA,OAAO,GAAG,CAAC,GAAG,CAAC;MAChD,IAAIsE,QAAQ,GAAGtE,OAAO,GAAG,CAAC,GAAG6F,QAAQ,GAAG7F,OAAO,GAAG,CAAC,GAAG6F,QAAQ;MAC9D,IAAI0B,wBAAwB,GAAG3J,SAAS,CAAC,IAAI,CAACwB,KAAK,EAAE;QACnDoI,IAAI,EAAE,IAAI;QACVC,IAAI,EAAE;MACR,CAAC,CAAC;MACF,IAAIC,SAAS,GAAGlB,SAAS,IAAI,aAAa3I,KAAK,CAAC6C,aAAa,CAAC,IAAI,EAAE;QAClEC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACJ,SAAS,EAAE,aAAa;MAC/C,CAAC,EAAEgG,SAAS,CAAClH,KAAK,EAAE,CAACA,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG,CAACU,OAAO,GAAG,CAAC,IAAIX,QAAQ,GAAG,CAAC,EAAEW,OAAO,GAAGX,QAAQ,GAAGC,KAAK,GAAGA,KAAK,GAAGU,OAAO,GAAGX,QAAQ,CAAC,CAAC,CAAC;MAC/H,IAAIoH,MAAM,EAAE;QACV,IAAIY,QAAQ,EAAE;UACZ,IAAI,OAAOA,QAAQ,KAAK,SAAS,EAAE;YACjCD,UAAU,GAAG,aAAavJ,KAAK,CAAC6C,aAAa,CAAC,QAAQ,EAAE;cACtD3B,IAAI,EAAE,QAAQ;cACd4I,OAAO,EAAE,IAAI,CAAC7D,UAAU;cACxB8D,OAAO,EAAE,IAAI,CAAC9D;YAChB,CAAC,EAAEwC,MAAM,CAACuB,eAAe,CAAC;UAC5B,CAAC,MAAM;YACLT,UAAU,GAAG,aAAavJ,KAAK,CAAC6C,aAAa,CAAC,MAAM,EAAE;cACpDiH,OAAO,EAAE,IAAI,CAAC7D,UAAU;cACxB8D,OAAO,EAAE,IAAI,CAAC9D;YAChB,CAAC,EAAEuD,QAAQ,CAAC;UACd;UACAD,UAAU,GAAG,aAAavJ,KAAK,CAAC6C,aAAa,CAAC,IAAI,EAAE;YAClDoH,KAAK,EAAEvB,SAAS,GAAG,EAAE,CAAC3F,MAAM,CAAC0F,MAAM,CAACyB,OAAO,CAAC,CAACnH,MAAM,CAACZ,OAAO,EAAE,GAAG,CAAC,CAACY,MAAM,CAACiF,QAAQ,CAAC,GAAG,IAAI;YACzFlF,SAAS,EAAE,EAAE,CAACC,MAAM,CAACJ,SAAS,EAAE,eAAe;UACjD,CAAC,EAAE4G,UAAU,CAAC;QAChB;QACA,OAAO,aAAavJ,KAAK,CAAC6C,aAAa,CAAC,IAAI,EAAEtD,QAAQ,CAAC;UACrDuD,SAAS,EAAEhD,UAAU,CAAC6C,SAAS,EAAE,EAAE,CAACI,MAAM,CAACJ,SAAS,EAAE,SAAS,CAAC,EAAEnD,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACuD,MAAM,CAACJ,SAAS,EAAE,WAAW,CAAC,EAAE4B,QAAQ,CAAC,EAAEzB,SAAS,CAAC;UAC9IyF,KAAK,EAAEA,KAAK;UACZ4B,GAAG,EAAE,IAAI,CAACpI;QACZ,CAAC,EAAE2H,wBAAwB,CAAC,EAAEG,SAAS,EAAE,aAAa7J,KAAK,CAAC6C,aAAa,CAAC,IAAI,EAAE;UAC9EoH,KAAK,EAAEvB,SAAS,GAAGD,MAAM,CAAC2B,SAAS,GAAG,IAAI;UAC1CN,OAAO,EAAE,IAAI,CAACnF,IAAI;UAClB0F,QAAQ,EAAE,IAAI,CAACzF,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI;UACnC0F,UAAU,EAAE,IAAI,CAACzE,cAAc;UAC/B/C,SAAS,EAAEhD,UAAU,CAAC,EAAE,CAACiD,MAAM,CAACJ,SAAS,EAAE,OAAO,CAAC,EAAEnD,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACuD,MAAM,CAACJ,SAAS,EAAE,WAAW,CAAC,EAAE,CAAC,IAAI,CAACiC,OAAO,CAAC,CAAC,CAAC,CAAC;UAC7H,eAAe,EAAE,CAAC,IAAI,CAACA,OAAO,CAAC;QACjC,CAAC,EAAE,IAAI,CAACsB,UAAU,CAACC,QAAQ,CAAC,CAAC,EAAE,aAAanG,KAAK,CAAC6C,aAAa,CAAC,IAAI,EAAE;UACpEoH,KAAK,EAAEvB,SAAS,GAAG,EAAE,CAAC3F,MAAM,CAACZ,OAAO,EAAE,GAAG,CAAC,CAACY,MAAM,CAACiF,QAAQ,CAAC,GAAG,IAAI;UAClElF,SAAS,EAAE,EAAE,CAACC,MAAM,CAACJ,SAAS,EAAE,eAAe;QACjD,CAAC,EAAE,aAAa3C,KAAK,CAAC6C,aAAa,CAAC,OAAO,EAAE;UAC3C3B,IAAI,EAAE,MAAM;UACZR,KAAK,EAAEkD,iBAAiB;UACxBW,QAAQ,EAAEA,QAAQ;UAClBgG,SAAS,EAAE,IAAI,CAACnH,aAAa;UAC7B2G,OAAO,EAAE,IAAI,CAACrG,WAAW;UACzBW,QAAQ,EAAE,IAAI,CAACX,WAAW;UAC1B8G,MAAM,EAAE,IAAI,CAACxG,UAAU;UACvBE,IAAI,EAAE;QACR,CAAC,CAAC,EAAE,aAAalE,KAAK,CAAC6C,aAAa,CAAC,MAAM,EAAE;UAC3CC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACJ,SAAS,EAAE,QAAQ;QAC1C,CAAC,EAAE,GAAG,CAAC,EAAEqF,QAAQ,CAAC,EAAE,aAAahI,KAAK,CAAC6C,aAAa,CAAC,IAAI,EAAE;UACzDoH,KAAK,EAAEvB,SAAS,GAAGD,MAAM,CAACgC,SAAS,GAAG,IAAI;UAC1CX,OAAO,EAAE,IAAI,CAACjF,IAAI;UAClBwF,QAAQ,EAAE,IAAI,CAACzF,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI;UACnC0F,UAAU,EAAE,IAAI,CAACxE,cAAc;UAC/BhD,SAAS,EAAEhD,UAAU,CAAC,EAAE,CAACiD,MAAM,CAACJ,SAAS,EAAE,OAAO,CAAC,EAAEnD,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACuD,MAAM,CAACJ,SAAS,EAAE,WAAW,CAAC,EAAE,CAAC,IAAI,CAACmC,OAAO,CAAC,CAAC,CAAC,CAAC;UAC7H,eAAe,EAAE,CAAC,IAAI,CAACA,OAAO,CAAC;QACjC,CAAC,EAAE,IAAI,CAAC0B,UAAU,CAACC,QAAQ,CAAC,CAAC,EAAE8C,UAAU,CAAC;MAC5C;MACA,IAAIvB,QAAQ,IAAI,CAAC,GAAGyB,cAAc,GAAG,CAAC,EAAE;QACtC,IAAIiB,UAAU,GAAG;UACfjC,MAAM,EAAEA,MAAM;UACdkC,aAAa,EAAEhI,SAAS;UACxBmH,OAAO,EAAE,IAAI,CAAC/F,YAAY;UAC1BuG,UAAU,EAAE,IAAI,CAACrF,UAAU;UAC3ByD,SAAS,EAAEA,SAAS;UACpBpC,UAAU,EAAEA;QACd,CAAC;QACD,IAAI,CAAC0B,QAAQ,EAAE;UACboB,SAAS,CAACwB,IAAI,EAAE,aAAa5K,KAAK,CAAC6C,aAAa,CAACvC,KAAK,EAAEf,QAAQ,CAAC,CAAC,CAAC,EAAEmL,UAAU,EAAE;YAC/EtF,GAAG,EAAE,SAAS;YACdnE,IAAI,EAAE,CAAC;YACP6B,SAAS,EAAE,EAAE,CAACC,MAAM,CAACJ,SAAS,EAAE,gBAAgB;UAClD,CAAC,CAAC,CAAC,CAAC;QACN;QACA,KAAK,IAAIkI,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI7C,QAAQ,EAAE6C,CAAC,IAAI,CAAC,EAAE;UACrC,IAAIC,MAAM,GAAG3I,OAAO,KAAK0I,CAAC;UAC1BzB,SAAS,CAACwB,IAAI,EAAE,aAAa5K,KAAK,CAAC6C,aAAa,CAACvC,KAAK,EAAEf,QAAQ,CAAC,CAAC,CAAC,EAAEmL,UAAU,EAAE;YAC/EtF,GAAG,EAAEyF,CAAC;YACN5J,IAAI,EAAE4J,CAAC;YACPC,MAAM,EAAEA;UACV,CAAC,CAAC,CAAC,CAAC;QACN;MACF,CAAC,MAAM;QACL,IAAIC,aAAa,GAAG3I,aAAa,GAAGqG,MAAM,CAACuC,MAAM,GAAGvC,MAAM,CAACwC,MAAM;QACjE,IAAIC,aAAa,GAAG9I,aAAa,GAAGqG,MAAM,CAAC0C,MAAM,GAAG1C,MAAM,CAAC2C,MAAM;QACjE,IAAIvC,mBAAmB,EAAE;UACvB9D,QAAQ,GAAG,aAAa/E,KAAK,CAAC6C,aAAa,CAAC,IAAI,EAAE;YAChDoH,KAAK,EAAEvB,SAAS,GAAGqC,aAAa,GAAG,IAAI;YACvC3F,GAAG,EAAE,MAAM;YACX0E,OAAO,EAAE,IAAI,CAAC/E,QAAQ;YACtBsF,QAAQ,EAAE,CAAC;YACXC,UAAU,EAAE,IAAI,CAACvE,kBAAkB;YACnCjD,SAAS,EAAEhD,UAAU,CAAC,EAAE,CAACiD,MAAM,CAACJ,SAAS,EAAE,YAAY,CAAC,EAAEnD,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACuD,MAAM,CAACJ,SAAS,EAAE,wBAAwB,CAAC,EAAE,CAAC,CAACmG,YAAY,CAAC;UAC/I,CAAC,EAAExC,UAAU,CAAC,IAAI,CAACrE,eAAe,CAAC,CAAC,EAAE,WAAW,EAAE,IAAI,CAACO,WAAW,CAACsG,YAAY,EAAE,WAAW,CAAC,CAAC,CAAC;UAChG9D,QAAQ,GAAG,aAAahF,KAAK,CAAC6C,aAAa,CAAC,IAAI,EAAE;YAChDoH,KAAK,EAAEvB,SAAS,GAAGwC,aAAa,GAAG,IAAI;YACvC9F,GAAG,EAAE,MAAM;YACXiF,QAAQ,EAAE,CAAC;YACXP,OAAO,EAAE,IAAI,CAAC9E,QAAQ;YACtBsF,UAAU,EAAE,IAAI,CAACtE,kBAAkB;YACnClD,SAAS,EAAEhD,UAAU,CAAC,EAAE,CAACiD,MAAM,CAACJ,SAAS,EAAE,YAAY,CAAC,EAAEnD,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACuD,MAAM,CAACJ,SAAS,EAAE,wBAAwB,CAAC,EAAE,CAAC,CAACoG,YAAY,CAAC;UAC/I,CAAC,EAAEzC,UAAU,CAAC,IAAI,CAACjE,eAAe,CAAC,CAAC,EAAE,WAAW,EAAE,IAAI,CAACG,WAAW,CAACuG,YAAY,EAAE,WAAW,CAAC,CAAC,CAAC;QAClG;QACAO,SAAS,GAAG,aAAatJ,KAAK,CAAC6C,aAAa,CAACvC,KAAK,EAAE;UAClDmI,MAAM,EAAEA,MAAM;UACd4C,IAAI,EAAE,IAAI;UACVV,aAAa,EAAEhI,SAAS;UACxBmH,OAAO,EAAE,IAAI,CAAC/F,YAAY;UAC1BuG,UAAU,EAAE,IAAI,CAACrF,UAAU;UAC3BG,GAAG,EAAE4C,QAAQ;UACb/G,IAAI,EAAE+G,QAAQ;UACd8C,MAAM,EAAE,KAAK;UACbpC,SAAS,EAAEA,SAAS;UACpBpC,UAAU,EAAEA;QACd,CAAC,CAAC;QACF+C,UAAU,GAAG,aAAarJ,KAAK,CAAC6C,aAAa,CAACvC,KAAK,EAAE;UACnDmI,MAAM,EAAEA,MAAM;UACdkC,aAAa,EAAEhI,SAAS;UACxBmH,OAAO,EAAE,IAAI,CAAC/F,YAAY;UAC1BuG,UAAU,EAAE,IAAI,CAACrF,UAAU;UAC3BG,GAAG,EAAE,CAAC;UACNnE,IAAI,EAAE,CAAC;UACP6J,MAAM,EAAE,KAAK;UACbpC,SAAS,EAAEA,SAAS;UACpBpC,UAAU,EAAEA;QACd,CAAC,CAAC;QACF,IAAIgF,IAAI,GAAGxK,IAAI,CAACoB,GAAG,CAAC,CAAC,EAAEC,OAAO,GAAGsH,cAAc,CAAC;QAChD,IAAI8B,KAAK,GAAGzK,IAAI,CAACwB,GAAG,CAACH,OAAO,GAAGsH,cAAc,EAAEzB,QAAQ,CAAC;QACxD,IAAI7F,OAAO,GAAG,CAAC,IAAIsH,cAAc,EAAE;UACjC8B,KAAK,GAAG,CAAC,GAAG9B,cAAc,GAAG,CAAC;QAChC;QACA,IAAIzB,QAAQ,GAAG7F,OAAO,IAAIsH,cAAc,EAAE;UACxC6B,IAAI,GAAGtD,QAAQ,GAAGyB,cAAc,GAAG,CAAC;QACtC;QACA,KAAK,IAAI+B,EAAE,GAAGF,IAAI,EAAEE,EAAE,IAAID,KAAK,EAAEC,EAAE,IAAI,CAAC,EAAE;UACxC,IAAIC,OAAO,GAAGtJ,OAAO,KAAKqJ,EAAE;UAC5BpC,SAAS,CAACwB,IAAI,EAAE,aAAa5K,KAAK,CAAC6C,aAAa,CAACvC,KAAK,EAAE;YACtDmI,MAAM,EAAEA,MAAM;YACdkC,aAAa,EAAEhI,SAAS;YACxBmH,OAAO,EAAE,IAAI,CAAC/F,YAAY;YAC1BuG,UAAU,EAAE,IAAI,CAACrF,UAAU;YAC3BG,GAAG,EAAEoG,EAAE;YACPvK,IAAI,EAAEuK,EAAE;YACRV,MAAM,EAAEW,OAAO;YACf/C,SAAS,EAAEA,SAAS;YACpBpC,UAAU,EAAEA;UACd,CAAC,CAAC,CAAC;QACL;QACA,IAAInE,OAAO,GAAG,CAAC,IAAIsH,cAAc,GAAG,CAAC,IAAItH,OAAO,KAAK,CAAC,GAAG,CAAC,EAAE;UAC1DiH,SAAS,CAAC,CAAC,CAAC,GAAG,aAAanJ,YAAY,CAACmJ,SAAS,CAAC,CAAC,CAAC,EAAE;YACrDtG,SAAS,EAAE,EAAE,CAACC,MAAM,CAACJ,SAAS,EAAE,uBAAuB;UACzD,CAAC,CAAC;UACFyG,SAAS,CAACsC,OAAO,CAAC3G,QAAQ,CAAC;QAC7B;QACA,IAAIiD,QAAQ,GAAG7F,OAAO,IAAIsH,cAAc,GAAG,CAAC,IAAItH,OAAO,KAAK6F,QAAQ,GAAG,CAAC,EAAE;UACxEoB,SAAS,CAACA,SAAS,CAAC5D,MAAM,GAAG,CAAC,CAAC,GAAG,aAAavF,YAAY,CAACmJ,SAAS,CAACA,SAAS,CAAC5D,MAAM,GAAG,CAAC,CAAC,EAAE;YAC3F1C,SAAS,EAAE,EAAE,CAACC,MAAM,CAACJ,SAAS,EAAE,wBAAwB;UAC1D,CAAC,CAAC;UACFyG,SAAS,CAACwB,IAAI,CAAC5F,QAAQ,CAAC;QAC1B;QACA,IAAIsG,IAAI,KAAK,CAAC,EAAE;UACdlC,SAAS,CAACsC,OAAO,CAACrC,UAAU,CAAC;QAC/B;QACA,IAAIkC,KAAK,KAAKvD,QAAQ,EAAE;UACtBoB,SAAS,CAACwB,IAAI,CAACtB,SAAS,CAAC;QAC3B;MACF;MACA,IAAIqC,YAAY,GAAG,CAAC,IAAI,CAAC/G,OAAO,CAAC,CAAC,IAAI,CAACoD,QAAQ;MAC/C,IAAI4D,YAAY,GAAG,CAAC,IAAI,CAAC9G,OAAO,CAAC,CAAC,IAAI,CAACkD,QAAQ;MAC/C,OAAO,aAAahI,KAAK,CAAC6C,aAAa,CAAC,IAAI,EAAEtD,QAAQ,CAAC;QACrDuD,SAAS,EAAEhD,UAAU,CAAC6C,SAAS,EAAEG,SAAS,EAAEtD,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACuD,MAAM,CAACJ,SAAS,EAAE,WAAW,CAAC,EAAE4B,QAAQ,CAAC,CAAC;QAC7GgE,KAAK,EAAEA,KAAK;QACZ4B,GAAG,EAAE,IAAI,CAACpI;MACZ,CAAC,EAAE2H,wBAAwB,CAAC,EAAEG,SAAS,EAAE,aAAa7J,KAAK,CAAC6C,aAAa,CAAC,IAAI,EAAE;QAC9EoH,KAAK,EAAEvB,SAAS,GAAGD,MAAM,CAAC2B,SAAS,GAAG,IAAI;QAC1CN,OAAO,EAAE,IAAI,CAACnF,IAAI;QAClB0F,QAAQ,EAAEsB,YAAY,GAAG,IAAI,GAAG,CAAC;QACjCrB,UAAU,EAAE,IAAI,CAACzE,cAAc;QAC/B/C,SAAS,EAAEhD,UAAU,CAAC,EAAE,CAACiD,MAAM,CAACJ,SAAS,EAAE,OAAO,CAAC,EAAEnD,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACuD,MAAM,CAACJ,SAAS,EAAE,WAAW,CAAC,EAAEgJ,YAAY,CAAC,CAAC;QAC1H,eAAe,EAAEA;MACnB,CAAC,EAAE,IAAI,CAACzF,UAAU,CAACC,QAAQ,CAAC,CAAC,EAAEiD,SAAS,EAAE,aAAapJ,KAAK,CAAC6C,aAAa,CAAC,IAAI,EAAE;QAC/EoH,KAAK,EAAEvB,SAAS,GAAGD,MAAM,CAACgC,SAAS,GAAG,IAAI;QAC1CX,OAAO,EAAE,IAAI,CAACjF,IAAI;QAClBwF,QAAQ,EAAEuB,YAAY,GAAG,IAAI,GAAG,CAAC;QACjCtB,UAAU,EAAE,IAAI,CAACxE,cAAc;QAC/BhD,SAAS,EAAEhD,UAAU,CAAC,EAAE,CAACiD,MAAM,CAACJ,SAAS,EAAE,OAAO,CAAC,EAAEnD,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACuD,MAAM,CAACJ,SAAS,EAAE,WAAW,CAAC,EAAEiJ,YAAY,CAAC,CAAC;QAC1H,eAAe,EAAEA;MACnB,CAAC,EAAE,IAAI,CAACpF,UAAU,CAACC,QAAQ,CAAC,CAAC,EAAE,aAAazG,KAAK,CAAC6C,aAAa,CAACxC,OAAO,EAAE;QACvEkE,QAAQ,EAAEA,QAAQ;QAClBkE,MAAM,EAAEA,MAAM;QACdkC,aAAa,EAAEhI,SAAS;QACxBqG,oBAAoB,EAAEA,oBAAoB;QAC1CC,eAAe,EAAEA,eAAe;QAChC4C,UAAU,EAAE,IAAI,CAAC5D,kBAAkB,CAAC,CAAC,GAAG,IAAI,CAAChE,cAAc,GAAG,IAAI;QAClE9B,OAAO,EAAEA,OAAO;QAChBX,QAAQ,EAAEA,QAAQ;QAClB0H,eAAe,EAAEA,eAAe;QAChC4C,OAAO,EAAE,IAAI,CAAC7I,wBAAwB,CAAC,CAAC,GAAG,IAAI,CAACc,YAAY,GAAG,IAAI;QACnEyF,QAAQ,EAAEA;MACZ,CAAC,CAAC,CAAC;IACL;EACF,CAAC,CAAC,EAAE,CAAC;IACHpE,GAAG,EAAE,0BAA0B;IAC/B1E,KAAK,EAAE,SAASqL,wBAAwBA,CAACxK,KAAK,EAAEgG,SAAS,EAAE;MACzD,IAAIyE,QAAQ,GAAG,CAAC,CAAC;MACjB,IAAI,SAAS,IAAIzK,KAAK,EAAE;QACtByK,QAAQ,CAAC7J,OAAO,GAAGZ,KAAK,CAACY,OAAO;QAChC,IAAIZ,KAAK,CAACY,OAAO,KAAKoF,SAAS,CAACpF,OAAO,EAAE;UACvC6J,QAAQ,CAACpI,iBAAiB,GAAGoI,QAAQ,CAAC7J,OAAO;QAC/C;MACF;MACA,IAAI,UAAU,IAAIZ,KAAK,IAAIA,KAAK,CAACC,QAAQ,KAAK+F,SAAS,CAAC/F,QAAQ,EAAE;QAChE,IAAIW,OAAO,GAAGoF,SAAS,CAACpF,OAAO;QAC/B,IAAIgC,UAAU,GAAG/C,aAAa,CAACG,KAAK,CAACC,QAAQ,EAAE+F,SAAS,EAAEhG,KAAK,CAAC;QAChEY,OAAO,GAAGA,OAAO,GAAGgC,UAAU,GAAGA,UAAU,GAAGhC,OAAO;QACrD,IAAI,EAAE,SAAS,IAAIZ,KAAK,CAAC,EAAE;UACzByK,QAAQ,CAAC7J,OAAO,GAAGA,OAAO;UAC1B6J,QAAQ,CAACpI,iBAAiB,GAAGzB,OAAO;QACtC;QACA6J,QAAQ,CAACxK,QAAQ,GAAGD,KAAK,CAACC,QAAQ;MACpC;MACA,OAAOwK,QAAQ;IACjB;EACF,CAAC,CAAC,CAAC;EACH,OAAOtK,UAAU;AACnB,CAAC,CAAC1B,KAAK,CAACiM,SAAS,CAAC;AAClBvK,UAAU,CAACwK,YAAY,GAAG;EACxBhF,cAAc,EAAE,CAAC;EACjBzF,KAAK,EAAE,CAAC;EACR2F,eAAe,EAAE,EAAE;EACnB/C,QAAQ,EAAE9D,IAAI;EACduC,SAAS,EAAE,EAAE;EACbmG,eAAe,EAAE,WAAW;EAC5BtG,SAAS,EAAE,eAAe;EAC1BqG,oBAAoB,EAAE,IAAI;EAC1BR,gBAAgB,EAAE,KAAK;EACvBK,mBAAmB,EAAE,IAAI;EACzB1F,eAAe,EAAE,KAAK;EACtBf,aAAa,EAAE,KAAK;EACpBsG,SAAS,EAAE,IAAI;EACftE,gBAAgB,EAAE7D,IAAI;EACtBkI,MAAM,EAAErI,MAAM;EACdmI,KAAK,EAAE,CAAC,CAAC;EACTjC,UAAU,EAAEtF,iBAAiB;EAC7BoH,4BAA4B,EAAE;AAChC,CAAC;AACD,eAAe1G,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}