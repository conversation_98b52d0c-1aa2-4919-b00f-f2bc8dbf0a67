{"ast": null, "code": "import PickerButton from '../PickerButton';\nconst Components = {\n  button: PickerButton\n};\nexport default Components;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Components", "button"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/date-picker/generatePicker/Components.js"], "sourcesContent": ["import PickerButton from '../PickerButton';\nconst Components = {\n  button: PickerButton\n};\nexport default Components;"], "mappings": "AAAA,OAAOA,YAAY,MAAM,iBAAiB;AAC1C,MAAMC,UAAU,GAAG;EACjBC,MAAM,EAAEF;AACV,CAAC;AACD,eAAeC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}