{"ast": null, "code": "const genLayoutLightStyle = token => {\n  const {\n    componentCls,\n    colorBgContainer,\n    colorBgBody,\n    colorText\n  } = token;\n  return {\n    [\"\".concat(componentCls, \"-sider-light\")]: {\n      background: colorBgContainer,\n      [\"\".concat(componentCls, \"-sider-trigger\")]: {\n        color: colorText,\n        background: colorBgContainer\n      },\n      [\"\".concat(componentCls, \"-sider-zero-width-trigger\")]: {\n        color: colorText,\n        background: colorBgContainer,\n        border: \"1px solid \".concat(colorBgBody),\n        borderInlineStart: 0\n      }\n    }\n  };\n};\nexport default genLayoutLightStyle;", "map": {"version": 3, "names": ["genLayoutLightStyle", "token", "componentCls", "colorBgContainer", "colorBgBody", "colorText", "concat", "background", "color", "border", "borderInlineStart"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/layout/style/light.js"], "sourcesContent": ["const genLayoutLightStyle = token => {\n  const {\n    componentCls,\n    colorBgContainer,\n    colorBgBody,\n    colorText\n  } = token;\n  return {\n    [`${componentCls}-sider-light`]: {\n      background: colorBgContainer,\n      [`${componentCls}-sider-trigger`]: {\n        color: colorText,\n        background: colorBgContainer\n      },\n      [`${componentCls}-sider-zero-width-trigger`]: {\n        color: colorText,\n        background: colorBgContainer,\n        border: `1px solid ${colorBgBody}`,\n        borderInlineStart: 0\n      }\n    }\n  };\n};\nexport default genLayoutLightStyle;"], "mappings": "AAAA,MAAMA,mBAAmB,GAAGC,KAAK,IAAI;EACnC,MAAM;IACJC,YAAY;IACZC,gBAAgB;IAChBC,WAAW;IACXC;EACF,CAAC,GAAGJ,KAAK;EACT,OAAO;IACL,IAAAK,MAAA,CAAIJ,YAAY,oBAAiB;MAC/BK,UAAU,EAAEJ,gBAAgB;MAC5B,IAAAG,MAAA,CAAIJ,YAAY,sBAAmB;QACjCM,KAAK,EAAEH,SAAS;QAChBE,UAAU,EAAEJ;MACd,CAAC;MACD,IAAAG,MAAA,CAAIJ,YAAY,iCAA8B;QAC5CM,KAAK,EAAEH,SAAS;QAChBE,UAAU,EAAEJ,gBAAgB;QAC5BM,MAAM,eAAAH,MAAA,CAAeF,WAAW,CAAE;QAClCM,iBAAiB,EAAE;MACrB;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeV,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}