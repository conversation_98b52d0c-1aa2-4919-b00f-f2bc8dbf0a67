{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { getValue, updateValues } from \"../utils/miscUtil\";\nimport { getClosingViewDate, isSameYear, isSameMonth, isSameDecade } from \"../utils/dateUtil\";\nfunction getStartEndDistance(startDate, endDate, picker, generateConfig) {\n  var startNext = getClosingViewDate(startDate, picker, generateConfig, 1);\n  function getDistance(compareFunc) {\n    if (compareFunc(startDate, endDate)) {\n      return 'same';\n    }\n    if (compareFunc(startNext, endDate)) {\n      return 'closing';\n    }\n    return 'far';\n  }\n  switch (picker) {\n    case 'year':\n      return getDistance(function (start, end) {\n        return isSameDecade(generateConfig, start, end);\n      });\n    case 'quarter':\n    case 'month':\n      return getDistance(function (start, end) {\n        return isSameYear(generateConfig, start, end);\n      });\n    default:\n      return getDistance(function (start, end) {\n        return isSameMonth(generateConfig, start, end);\n      });\n  }\n}\nfunction getRangeViewDate(values, index, picker, generateConfig) {\n  var startDate = getValue(values, 0);\n  var endDate = getValue(values, 1);\n  if (index === 0) {\n    return startDate;\n  }\n  if (startDate && endDate) {\n    var distance = getStartEndDistance(startDate, endDate, picker, generateConfig);\n    switch (distance) {\n      case 'same':\n        return startDate;\n      case 'closing':\n        return startDate;\n      default:\n        return getClosingViewDate(endDate, picker, generateConfig, -1);\n    }\n  }\n  return startDate;\n}\nexport default function useRangeViewDates(_ref) {\n  var values = _ref.values,\n    picker = _ref.picker,\n    defaultDates = _ref.defaultDates,\n    generateConfig = _ref.generateConfig;\n  var _React$useState = React.useState(function () {\n      return [getValue(defaultDates, 0), getValue(defaultDates, 1)];\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    defaultViewDates = _React$useState2[0],\n    setDefaultViewDates = _React$useState2[1];\n  var _React$useState3 = React.useState(null),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    viewDates = _React$useState4[0],\n    setInternalViewDates = _React$useState4[1];\n  var startDate = getValue(values, 0);\n  var endDate = getValue(values, 1);\n  function getViewDate(index) {\n    // If set default view date, use it\n    if (defaultViewDates[index]) {\n      return defaultViewDates[index];\n    }\n    return getValue(viewDates, index) || getRangeViewDate(values, index, picker, generateConfig) || startDate || endDate || generateConfig.getNow();\n  }\n  function setViewDate(viewDate, index) {\n    if (viewDate) {\n      var newViewDates = updateValues(viewDates, viewDate, index);\n      // Set view date will clean up default one\n      setDefaultViewDates(\n      // Should always be an array\n      updateValues(defaultViewDates, null, index) || [null, null]);\n\n      // Reset another one when not have value\n      var anotherIndex = (index + 1) % 2;\n      if (!getValue(values, anotherIndex)) {\n        newViewDates = updateValues(newViewDates, viewDate, anotherIndex);\n      }\n      setInternalViewDates(newViewDates);\n    } else if (startDate || endDate) {\n      // Reset all when has values when `viewDate` is `null` which means from open trigger\n      setInternalViewDates(null);\n    }\n  }\n  return [getViewDate, setViewDate];\n}", "map": {"version": 3, "names": ["_slicedToArray", "React", "getValue", "updateValues", "getClosingViewDate", "isSameYear", "isSameMonth", "isSameDecade", "getStartEndDistance", "startDate", "endDate", "picker", "generateConfig", "startNext", "getDistance", "compareFunc", "start", "end", "getRangeViewDate", "values", "index", "distance", "useRangeViewDates", "_ref", "defaultDates", "_React$useState", "useState", "_React$useState2", "defaultViewDates", "setDefaultViewDates", "_React$useState3", "_React$useState4", "viewDates", "setInternalViewDates", "getViewDate", "getNow", "setViewDate", "viewDate", "newViewDates", "anotherIndex"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-picker/es/hooks/useRangeViewDates.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { getValue, updateValues } from \"../utils/miscUtil\";\nimport { getClosingViewDate, isSameYear, isSameMonth, isSameDecade } from \"../utils/dateUtil\";\nfunction getStartEndDistance(startDate, endDate, picker, generateConfig) {\n  var startNext = getClosingViewDate(startDate, picker, generateConfig, 1);\n  function getDistance(compareFunc) {\n    if (compareFunc(startDate, endDate)) {\n      return 'same';\n    }\n    if (compareFunc(startNext, endDate)) {\n      return 'closing';\n    }\n    return 'far';\n  }\n  switch (picker) {\n    case 'year':\n      return getDistance(function (start, end) {\n        return isSameDecade(generateConfig, start, end);\n      });\n    case 'quarter':\n    case 'month':\n      return getDistance(function (start, end) {\n        return isSameYear(generateConfig, start, end);\n      });\n    default:\n      return getDistance(function (start, end) {\n        return isSameMonth(generateConfig, start, end);\n      });\n  }\n}\nfunction getRangeViewDate(values, index, picker, generateConfig) {\n  var startDate = getValue(values, 0);\n  var endDate = getValue(values, 1);\n  if (index === 0) {\n    return startDate;\n  }\n  if (startDate && endDate) {\n    var distance = getStartEndDistance(startDate, endDate, picker, generateConfig);\n    switch (distance) {\n      case 'same':\n        return startDate;\n      case 'closing':\n        return startDate;\n      default:\n        return getClosingViewDate(endDate, picker, generateConfig, -1);\n    }\n  }\n  return startDate;\n}\nexport default function useRangeViewDates(_ref) {\n  var values = _ref.values,\n    picker = _ref.picker,\n    defaultDates = _ref.defaultDates,\n    generateConfig = _ref.generateConfig;\n  var _React$useState = React.useState(function () {\n      return [getValue(defaultDates, 0), getValue(defaultDates, 1)];\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    defaultViewDates = _React$useState2[0],\n    setDefaultViewDates = _React$useState2[1];\n  var _React$useState3 = React.useState(null),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    viewDates = _React$useState4[0],\n    setInternalViewDates = _React$useState4[1];\n  var startDate = getValue(values, 0);\n  var endDate = getValue(values, 1);\n  function getViewDate(index) {\n    // If set default view date, use it\n    if (defaultViewDates[index]) {\n      return defaultViewDates[index];\n    }\n    return getValue(viewDates, index) || getRangeViewDate(values, index, picker, generateConfig) || startDate || endDate || generateConfig.getNow();\n  }\n  function setViewDate(viewDate, index) {\n    if (viewDate) {\n      var newViewDates = updateValues(viewDates, viewDate, index);\n      // Set view date will clean up default one\n      setDefaultViewDates(\n      // Should always be an array\n      updateValues(defaultViewDates, null, index) || [null, null]);\n\n      // Reset another one when not have value\n      var anotherIndex = (index + 1) % 2;\n      if (!getValue(values, anotherIndex)) {\n        newViewDates = updateValues(newViewDates, viewDate, anotherIndex);\n      }\n      setInternalViewDates(newViewDates);\n    } else if (startDate || endDate) {\n      // Reset all when has values when `viewDate` is `null` which means from open trigger\n      setInternalViewDates(null);\n    }\n  }\n  return [getViewDate, setViewDate];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,YAAY,QAAQ,mBAAmB;AAC1D,SAASC,kBAAkB,EAAEC,UAAU,EAAEC,WAAW,EAAEC,YAAY,QAAQ,mBAAmB;AAC7F,SAASC,mBAAmBA,CAACC,SAAS,EAAEC,OAAO,EAAEC,MAAM,EAAEC,cAAc,EAAE;EACvE,IAAIC,SAAS,GAAGT,kBAAkB,CAACK,SAAS,EAAEE,MAAM,EAAEC,cAAc,EAAE,CAAC,CAAC;EACxE,SAASE,WAAWA,CAACC,WAAW,EAAE;IAChC,IAAIA,WAAW,CAACN,SAAS,EAAEC,OAAO,CAAC,EAAE;MACnC,OAAO,MAAM;IACf;IACA,IAAIK,WAAW,CAACF,SAAS,EAAEH,OAAO,CAAC,EAAE;MACnC,OAAO,SAAS;IAClB;IACA,OAAO,KAAK;EACd;EACA,QAAQC,MAAM;IACZ,KAAK,MAAM;MACT,OAAOG,WAAW,CAAC,UAAUE,KAAK,EAAEC,GAAG,EAAE;QACvC,OAAOV,YAAY,CAACK,cAAc,EAAEI,KAAK,EAAEC,GAAG,CAAC;MACjD,CAAC,CAAC;IACJ,KAAK,SAAS;IACd,KAAK,OAAO;MACV,OAAOH,WAAW,CAAC,UAAUE,KAAK,EAAEC,GAAG,EAAE;QACvC,OAAOZ,UAAU,CAACO,cAAc,EAAEI,KAAK,EAAEC,GAAG,CAAC;MAC/C,CAAC,CAAC;IACJ;MACE,OAAOH,WAAW,CAAC,UAAUE,KAAK,EAAEC,GAAG,EAAE;QACvC,OAAOX,WAAW,CAACM,cAAc,EAAEI,KAAK,EAAEC,GAAG,CAAC;MAChD,CAAC,CAAC;EACN;AACF;AACA,SAASC,gBAAgBA,CAACC,MAAM,EAAEC,KAAK,EAAET,MAAM,EAAEC,cAAc,EAAE;EAC/D,IAAIH,SAAS,GAAGP,QAAQ,CAACiB,MAAM,EAAE,CAAC,CAAC;EACnC,IAAIT,OAAO,GAAGR,QAAQ,CAACiB,MAAM,EAAE,CAAC,CAAC;EACjC,IAAIC,KAAK,KAAK,CAAC,EAAE;IACf,OAAOX,SAAS;EAClB;EACA,IAAIA,SAAS,IAAIC,OAAO,EAAE;IACxB,IAAIW,QAAQ,GAAGb,mBAAmB,CAACC,SAAS,EAAEC,OAAO,EAAEC,MAAM,EAAEC,cAAc,CAAC;IAC9E,QAAQS,QAAQ;MACd,KAAK,MAAM;QACT,OAAOZ,SAAS;MAClB,KAAK,SAAS;QACZ,OAAOA,SAAS;MAClB;QACE,OAAOL,kBAAkB,CAACM,OAAO,EAAEC,MAAM,EAAEC,cAAc,EAAE,CAAC,CAAC,CAAC;IAClE;EACF;EACA,OAAOH,SAAS;AAClB;AACA,eAAe,SAASa,iBAAiBA,CAACC,IAAI,EAAE;EAC9C,IAAIJ,MAAM,GAAGI,IAAI,CAACJ,MAAM;IACtBR,MAAM,GAAGY,IAAI,CAACZ,MAAM;IACpBa,YAAY,GAAGD,IAAI,CAACC,YAAY;IAChCZ,cAAc,GAAGW,IAAI,CAACX,cAAc;EACtC,IAAIa,eAAe,GAAGxB,KAAK,CAACyB,QAAQ,CAAC,YAAY;MAC7C,OAAO,CAACxB,QAAQ,CAACsB,YAAY,EAAE,CAAC,CAAC,EAAEtB,QAAQ,CAACsB,YAAY,EAAE,CAAC,CAAC,CAAC;IAC/D,CAAC,CAAC;IACFG,gBAAgB,GAAG3B,cAAc,CAACyB,eAAe,EAAE,CAAC,CAAC;IACrDG,gBAAgB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACtCE,mBAAmB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC3C,IAAIG,gBAAgB,GAAG7B,KAAK,CAACyB,QAAQ,CAAC,IAAI,CAAC;IACzCK,gBAAgB,GAAG/B,cAAc,CAAC8B,gBAAgB,EAAE,CAAC,CAAC;IACtDE,SAAS,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC/BE,oBAAoB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC5C,IAAItB,SAAS,GAAGP,QAAQ,CAACiB,MAAM,EAAE,CAAC,CAAC;EACnC,IAAIT,OAAO,GAAGR,QAAQ,CAACiB,MAAM,EAAE,CAAC,CAAC;EACjC,SAASe,WAAWA,CAACd,KAAK,EAAE;IAC1B;IACA,IAAIQ,gBAAgB,CAACR,KAAK,CAAC,EAAE;MAC3B,OAAOQ,gBAAgB,CAACR,KAAK,CAAC;IAChC;IACA,OAAOlB,QAAQ,CAAC8B,SAAS,EAAEZ,KAAK,CAAC,IAAIF,gBAAgB,CAACC,MAAM,EAAEC,KAAK,EAAET,MAAM,EAAEC,cAAc,CAAC,IAAIH,SAAS,IAAIC,OAAO,IAAIE,cAAc,CAACuB,MAAM,CAAC,CAAC;EACjJ;EACA,SAASC,WAAWA,CAACC,QAAQ,EAAEjB,KAAK,EAAE;IACpC,IAAIiB,QAAQ,EAAE;MACZ,IAAIC,YAAY,GAAGnC,YAAY,CAAC6B,SAAS,EAAEK,QAAQ,EAAEjB,KAAK,CAAC;MAC3D;MACAS,mBAAmB;MACnB;MACA1B,YAAY,CAACyB,gBAAgB,EAAE,IAAI,EAAER,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;;MAE5D;MACA,IAAImB,YAAY,GAAG,CAACnB,KAAK,GAAG,CAAC,IAAI,CAAC;MAClC,IAAI,CAAClB,QAAQ,CAACiB,MAAM,EAAEoB,YAAY,CAAC,EAAE;QACnCD,YAAY,GAAGnC,YAAY,CAACmC,YAAY,EAAED,QAAQ,EAAEE,YAAY,CAAC;MACnE;MACAN,oBAAoB,CAACK,YAAY,CAAC;IACpC,CAAC,MAAM,IAAI7B,SAAS,IAAIC,OAAO,EAAE;MAC/B;MACAuB,oBAAoB,CAAC,IAAI,CAAC;IAC5B;EACF;EACA,OAAO,CAACC,WAAW,EAAEE,WAAW,CAAC;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}