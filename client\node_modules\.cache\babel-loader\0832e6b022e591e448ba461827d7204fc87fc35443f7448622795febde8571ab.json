{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\StudyMaterial\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useMemo } from \"react\";\nimport \"./index.css\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getStudyMaterial } from \"../../../apicalls/study\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { useLanguage } from \"../../../contexts/LanguageContext\";\n// import { Card, Button, Input, Loading } from \"../../../components/modern\";\n\nimport PDFModal from \"./PDFModal\";\nimport PastPaperDiscussion from \"../../../components/PastPaperDiscussion\";\nimport { FaBook, FaFileAlt, FaGraduationCap, FaDownload, FaEye, FaChevronDown, FaSearch, FaTimes, FaRobot } from \"react-icons/fa\";\nimport { TbFileText, TbBook as TbBookIcon, TbSchool, TbSearch, TbFilter, TbSortAscending, TbDownload, TbEye, TbCalendar, TbUser, TbChevronDown as TbChevronDownIcon, TbChevronUp, TbX, TbAlertTriangle, TbInfoCircle, TbCheck, TbBooks, TbCertificate } from \"react-icons/tb\";\nimport { primarySubjects, primaryKiswahiliSubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects.jsx\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction StudyMaterial() {\n  _s();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    t,\n    isKiswahili,\n    getSubjectName\n  } = useLanguage();\n  const dispatch = useDispatch();\n\n  // Get user level and subjects list (case-insensitive)\n  const userLevel = (user === null || user === void 0 ? void 0 : user.level) || 'Primary';\n  const userLevelLower = userLevel.toLowerCase();\n  const subjectsList = userLevelLower === 'primary' ? primarySubjects : userLevelLower === 'primary_kiswahili' ? primaryKiswahiliSubjects : userLevelLower === 'secondary' ? secondarySubjects : advanceSubjects;\n\n  // Debug: Log current level and subjects\n  useEffect(() => {\n    console.log('📚 Study Materials - User Level:', userLevel);\n    console.log('📚 Study Materials - User Level (lowercase):', userLevelLower);\n    console.log('📚 Study Materials - Subjects List:', subjectsList);\n    console.log('📚 Study Materials - User Data:', user);\n  }, [userLevel, userLevelLower, subjectsList, user]);\n\n  // Define all possible classes for each level\n  const allPossibleClasses = userLevelLower === 'primary' ? ['1', '2', '3', '4', '5', '6', '7'] : userLevelLower === 'secondary' ? ['Form-1', 'Form-2', 'Form-3', 'Form-4'] : ['Form-5', 'Form-6'];\n\n  // Simplified state management - initialize with user's class if available\n  const [activeTab, setActiveTab] = useState(\"study-notes\");\n  const [selectedClass, setSelectedClass] = useState((user === null || user === void 0 ? void 0 : user.class) || (user === null || user === void 0 ? void 0 : user.className) || \"all\");\n  const [selectedSubject, setSelectedSubject] = useState(\"all\");\n\n  // Get user's current class for highlighting\n  const userCurrentClass = (user === null || user === void 0 ? void 0 : user.class) || (user === null || user === void 0 ? void 0 : user.className);\n  const [materials, setMaterials] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [modalIsOpen, setModalIsOpen] = useState(false);\n  const [documentUrl, setDocumentUrl] = useState(\"\");\n  const [availableClasses, setAvailableClasses] = useState([]);\n  const [showClassSelector, setShowClassSelector] = useState(false);\n\n  // Unified search and sort states\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sortBy, setSortBy] = useState(\"newest\");\n\n  // Update selectedClass when user data becomes available\n  useEffect(() => {\n    const userClass = (user === null || user === void 0 ? void 0 : user.class) || (user === null || user === void 0 ? void 0 : user.className);\n    if (userClass && selectedClass === \"all\" && !availableClasses.length) {\n      setSelectedClass(userClass);\n    }\n  }, [user, selectedClass, availableClasses.length]);\n\n  // Reset subject selection when user level changes\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.level) {\n      // Check if current selected subject is valid for the new level\n      const isValidSubject = subjectsList.includes(selectedSubject);\n      if (!isValidSubject && selectedSubject !== \"all\") {\n        console.log('📚 Resetting subject selection due to level change');\n        setSelectedSubject(\"all\");\n      }\n    }\n  }, [user === null || user === void 0 ? void 0 : user.level, subjectsList, selectedSubject]);\n\n  // Set available classes based on user level (show all possible classes)\n  const setAvailableClassesForLevel = useCallback(() => {\n    setAvailableClasses(allPossibleClasses);\n  }, [allPossibleClasses]);\n\n  // Simplified fetch function\n  const fetchMaterials = useCallback(async () => {\n    if (!activeTab || selectedClass === \"default\") {\n      return;\n    }\n    setIsLoading(true);\n    setError(null);\n    dispatch(ShowLoading());\n    try {\n      // Normalize className for backend - remove \"Form-\" prefix if present\n      const normalizedClassName = selectedClass === \"all\" ? \"all\" : selectedClass.toString().replace(\"Form-\", \"\");\n      const data = {\n        content: activeTab,\n        className: normalizedClassName,\n        subject: selectedSubject // This can be \"all\" or a specific subject\n      };\n\n      if (userLevel) {\n        data.level = userLevel;\n      }\n      const res = await getStudyMaterial(data);\n      if (res.status === 200 && res.data.success) {\n        const materials = res.data.data === \"empty\" ? [] : res.data.data;\n        setMaterials(materials);\n      } else {\n        setMaterials([]);\n        setError(`Failed to fetch ${activeTab}. Please try again.`);\n      }\n    } catch (error) {\n      console.error(\"Error fetching study material:\", error);\n      setMaterials([]);\n      setError(`Unable to load ${activeTab}. Please check your connection and try again.`);\n    } finally {\n      setIsLoading(false);\n      dispatch(HideLoading());\n    }\n  }, [activeTab, selectedClass, selectedSubject, userLevel, dispatch]);\n\n  // Set available classes when component mounts\n  useEffect(() => {\n    if (user && userLevel) {\n      setAvailableClassesForLevel();\n    }\n  }, [user, userLevel, setAvailableClassesForLevel]);\n\n  // Fetch materials when filters change or component mounts\n  useEffect(() => {\n    // Only fetch if we have a valid activeTab, selectedClass, and user\n    if (user && userLevel && activeTab && selectedClass && selectedClass !== \"default\") {\n      fetchMaterials();\n    }\n  }, [user, userLevel, activeTab, selectedClass, selectedSubject, fetchMaterials]);\n\n  // Handler functions\n  const handleTabChange = tab => {\n    setMaterials([]);\n    setActiveTab(tab);\n    setSearchTerm(\"\");\n    setSortBy(\"newest\");\n  };\n  const handleSubjectChange = subject => {\n    setMaterials([]);\n    setSelectedSubject(subject);\n    setSearchTerm(\"\");\n  };\n  const handleClassChange = className => {\n    setMaterials([]);\n    setSelectedClass(className);\n    setShowClassSelector(false);\n  };\n  const toggleClassSelector = () => {\n    setShowClassSelector(!showClassSelector);\n  };\n\n  // Unified filtering and sorting logic\n  const filteredAndSortedMaterials = useMemo(() => {\n    if (!materials || materials.length === 0) {\n      return [];\n    }\n    let filtered = materials;\n\n    // Filter by search term (title, subject, or year)\n    if (searchTerm.trim()) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(material => material.title.toLowerCase().includes(searchLower) || material.subject.toLowerCase().includes(searchLower) || material.year && material.year.toLowerCase().includes(searchLower));\n    }\n\n    // Sort by year, creation date, or title\n    filtered.sort((a, b) => {\n      if (sortBy === \"newest\") {\n        // For materials with year field (books, past papers)\n        if (a.year && b.year) {\n          return parseInt(b.year) - parseInt(a.year);\n        }\n\n        // Fallback: materials with year come first\n        else if (a.year && !b.year) return -1;else if (!a.year && b.year) return 1;else return 0;\n      } else if (sortBy === \"oldest\") {\n        // For materials with year field\n        if (a.year && b.year) {\n          return parseInt(a.year) - parseInt(b.year);\n        }\n\n        // Fallback: materials with year come first\n        else if (a.year && !b.year) return -1;else if (!a.year && b.year) return 1;else return 0;\n      } else {\n        // Sort by title alphabetically\n        return a.title.localeCompare(b.title);\n      }\n    });\n    return filtered;\n  }, [materials, searchTerm, sortBy, activeTab]);\n\n  // Document handlers\n  const handleDocumentDownload = documentUrl => {\n    // Use proxy endpoint to handle CORS issues\n    const proxyUrl = `${process.env.REACT_APP_SERVER_DOMAIN}/api/study/document-proxy?url=${encodeURIComponent(documentUrl)}`;\n    fetch(proxyUrl, {\n      method: 'GET',\n      headers: {\n        'Authorization': `Bearer ${localStorage.getItem('token')}`\n      }\n    }).then(response => {\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      return response.blob();\n    }).then(blob => {\n      const url = window.URL.createObjectURL(blob);\n      const a = document.createElement(\"a\");\n      a.href = url;\n      a.download = documentUrl.split(\"/\").pop();\n      document.body.appendChild(a);\n      a.click();\n      document.body.removeChild(a);\n      window.URL.revokeObjectURL(url);\n    }).catch(error => {\n      console.error(\"Error downloading the file:\", error);\n      // Fallback to direct download if proxy fails\n      window.open(documentUrl, '_blank');\n    });\n  };\n  const handleDocumentPreview = documentUrl => {\n    setDocumentUrl(documentUrl);\n    setModalIsOpen(true);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50\",\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: -20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      className: \"bg-gradient-to-r from-primary-600 to-blue-600 text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container-modern py-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(TbBooks, {\n                className: \"w-8 h-8 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-4xl font-bold mb-2\",\n                children: \"Study Materials\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xl text-blue-100\",\n                children: [\"Access comprehensive learning resources for \", userLevel, \" education\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:block\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/20 backdrop-blur-sm rounded-xl px-6 py-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-blue-100 mb-1\",\n                children: \"Current Level\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-lg font-bold\",\n                children: userLevel === null || userLevel === void 0 ? void 0 : userLevel.toUpperCase()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-modern py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"study-tabs\",\n          children: [{\n            key: 'study-notes',\n            label: isKiswahili ? 'Maelezo' : 'Notes',\n            icon: TbFileText\n          }, {\n            key: 'past-papers',\n            label: isKiswahili ? 'Karatasi za Zamani' : 'Past Papers',\n            icon: TbCertificate\n          }, {\n            key: 'books',\n            label: isKiswahili ? 'Vitabu' : 'Books',\n            icon: TbBookIcon\n          }].map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `study-tab ${activeTab === tab.key ? 'active' : ''}`,\n            onClick: () => handleTabChange(tab.key),\n            children: [/*#__PURE__*/_jsxDEV(tab.icon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: tab.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 17\n            }, this)]\n          }, tab.key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.4\n        },\n        className: \"mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col lg:flex-row gap-6 items-end\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: isKiswahili ? 'Tafuta Vifaa' : 'Search Materials'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                placeholder: isKiswahili ? `Tafuta ${activeTab === 'study-notes' ? 'maelezo' : activeTab === 'past-papers' ? 'karatasi za zamani' : 'vitabu'}...` : `Search ${activeTab.replace('-', ' ')}...`,\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value),\n                className: \"form-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full lg:w-64\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: [isKiswahili ? 'Chuja kwa Darasa' : 'Filter by Class', userCurrentClass && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 text-xs text-primary-600 font-medium\",\n                  children: [\"(\", isKiswahili ? 'Darasa lako: ' : 'Your class: ', userLevelLower === 'primary' || userLevelLower === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${userCurrentClass}` : `Class ${userCurrentClass}` : `Form ${userCurrentClass}`, \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: toggleClassSelector,\n                  className: \"w-full input-modern flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(TbSchool, {\n                      className: \"w-4 h-4 text-gray-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 406,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: selectedClass === 'all' ? 'All Classes' : userLevelLower === 'primary' ? `Class ${selectedClass}` : `Form ${selectedClass}`\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 407,\n                      columnNumber: 23\n                    }, this), selectedClass === userCurrentClass && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"badge-primary text-xs\",\n                      children: \"Current\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 415,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 405,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TbChevronDownIcon, {\n                    className: `w-4 h-4 text-gray-400 transition-transform ${showClassSelector ? 'rotate-180' : ''}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 418,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n                  children: showClassSelector && /*#__PURE__*/_jsxDEV(motion.div, {\n                    initial: {\n                      opacity: 0,\n                      y: -10\n                    },\n                    animate: {\n                      opacity: 1,\n                      y: 0\n                    },\n                    exit: {\n                      opacity: 0,\n                      y: -10\n                    },\n                    className: \"absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: `w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors ${selectedClass === 'all' ? 'bg-primary-50 text-primary-700 font-medium' : 'text-gray-700'}`,\n                      onClick: () => handleClassChange('all'),\n                      children: \"All Classes\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 429,\n                      columnNumber: 25\n                    }, this), availableClasses.map((className, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: `w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors flex items-center justify-between ${selectedClass === className ? 'bg-primary-50 text-primary-700 font-medium' : 'text-gray-700'}`,\n                      onClick: () => handleClassChange(className),\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: userLevelLower === 'primary' ? `Class ${className}` : `Form ${className}`\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 445,\n                        columnNumber: 29\n                      }, this), className === userCurrentClass && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"badge-success text-xs\",\n                        children: \"Your Class\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 449,\n                        columnNumber: 31\n                      }, this)]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 438,\n                      columnNumber: 27\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 423,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full lg:w-64\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: isKiswahili ? 'Chuja kwa Somo' : 'Filter by Subject'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 461,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: selectedSubject,\n                onChange: e => handleSubjectChange(e.target.value),\n                className: \"input-modern\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: isKiswahili ? 'Masomo Yote' : 'All Subjects'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 19\n                }, this), subjectsList.map((subject, index) => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: subject,\n                  children: getSubjectName(subject)\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 471,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full lg:w-48\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: isKiswahili ? 'Panga kwa' : 'Sort by'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: sortBy,\n                onChange: e => setSortBy(e.target.value),\n                className: \"input-modern\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"newest\",\n                  children: \"Newest First\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 488,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"oldest\",\n                  children: \"Oldest First\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"title\",\n                  children: \"By Title\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 490,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-secondary\",\n              onClick: () => {\n                setSearchTerm(\"\");\n                setSelectedClass(\"all\");\n                setSelectedSubject(\"all\");\n                setSortBy(\"newest\");\n              },\n              children: \"Clear Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 495,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this), (searchTerm || selectedClass !== \"all\" || selectedSubject !== \"all\") && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 pt-4 border-t border-gray-100\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: [\"Showing \", filteredAndSortedMaterials.length, \" of \", materials.length, \" \", activeTab.replace('-', ' ')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"materials-section\",\n        children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-state\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Loading materials...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 524,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 522,\n          columnNumber: 11\n        }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-state\",\n          children: [/*#__PURE__*/_jsxDEV(FaTimes, {\n            className: \"error-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 528,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Error Loading Materials\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 529,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"retry-btn\",\n            onClick: () => {\n              setError(null);\n              fetchMaterials();\n            },\n            children: \"Try Again\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 531,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 527,\n          columnNumber: 11\n        }, this) : filteredAndSortedMaterials.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n          children: filteredAndSortedMaterials.map((material, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"study-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"study-card-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"study-card-meta\",\n                children: [activeTab === 'study-notes' && /*#__PURE__*/_jsxDEV(FaFileAlt, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 547,\n                  columnNumber: 53\n                }, this), activeTab === 'past-papers' && /*#__PURE__*/_jsxDEV(FaFileAlt, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 548,\n                  columnNumber: 53\n                }, this), activeTab === 'books' && /*#__PURE__*/_jsxDEV(FaBook, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 549,\n                  columnNumber: 47\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: activeTab === 'study-notes' ? isKiswahili ? 'Maelezo' : 'Note' : activeTab === 'past-papers' ? isKiswahili ? 'Karatasi ya Zamani' : 'Past Paper' : isKiswahili ? 'Kitabu' : 'Book'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 550,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 546,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"study-card-title\",\n                children: material.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 557,\n                columnNumber: 19\n              }, this), material.year && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"badge badge-secondary mt-2\",\n                children: material.year\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 563,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 545,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"material-title\",\n                children: material.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 570,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"material-meta\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"material-subject\",\n                  children: material.subject\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 572,\n                  columnNumber: 21\n                }, this), material.className && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"material-class\",\n                  children: userLevelLower === 'primary' ? `Class ${material.className}` : `Form ${material.className}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 574,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 571,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-actions\",\n              children: material.documentUrl ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"action-btn secondary\",\n                  onClick: () => handleDocumentPreview(material.documentUrl),\n                  children: [/*#__PURE__*/_jsxDEV(FaEye, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 588,\n                    columnNumber: 25\n                  }, this), \" View\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 584,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"action-btn primary\",\n                  onClick: () => handleDocumentDownload(material.documentUrl),\n                  children: [/*#__PURE__*/_jsxDEV(FaDownload, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 594,\n                    columnNumber: 25\n                  }, this), \" Download\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 590,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"unavailable\",\n                children: \"Not available\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 598,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 11\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"empty-state\",\n          children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n            className: \"empty-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 606,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"No Materials Found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 607,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"No study materials are available for your current selection.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 608,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"suggestion\",\n            children: \"Try selecting a different class or subject.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 605,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 520,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(PDFModal, {\n        modalIsOpen: modalIsOpen,\n        closeModal: () => {\n          setModalIsOpen(false);\n          setDocumentUrl(\"\");\n        },\n        documentUrl: documentUrl\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 618,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 347,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 317,\n    columnNumber: 5\n  }, this);\n}\n_s(StudyMaterial, \"xHlXQDSQ7v9rJPLlHoD4sl4wJJA=\", false, function () {\n  return [useSelector, useLanguage, useDispatch];\n});\n_c = StudyMaterial;\nexport default StudyMaterial;\nvar _c;\n$RefreshReg$(_c, \"StudyMaterial\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "motion", "AnimatePresence", "getStudyMaterial", "useDispatch", "useSelector", "HideLoading", "ShowLoading", "useLanguage", "PDFModal", "PastPaperDiscussion", "FaBook", "FaFileAlt", "FaGraduationCap", "FaDownload", "FaEye", "FaChevronDown", "FaSearch", "FaTimes", "FaRobot", "TbFileText", "TbBook", "TbBookIcon", "TbSchool", "TbSearch", "Tb<PERSON><PERSON>er", "TbSortAscending", "TbDownload", "TbEye", "TbCalendar", "TbUser", "TbChevronDown", "TbChevronDownIcon", "TbChevronUp", "TbX", "TbAlertTriangle", "TbInfoCircle", "TbCheck", "TbBooks", "TbCertificate", "primarySubjects", "primaryKiswahiliSubjects", "secondarySubjects", "advanceSubjects", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "StudyMaterial", "_s", "user", "state", "t", "isKiswahili", "getSubjectName", "dispatch", "userLevel", "level", "userLevelLower", "toLowerCase", "subjectsList", "console", "log", "allPossibleClasses", "activeTab", "setActiveTab", "selectedClass", "setSelectedClass", "class", "className", "selectedSubject", "setSelectedSubject", "userCurrentClass", "materials", "setMaterials", "isLoading", "setIsLoading", "error", "setError", "modalIsOpen", "setModalIsOpen", "documentUrl", "setDocumentUrl", "availableClasses", "setAvailableClasses", "showClassSelector", "setShowClassSelector", "searchTerm", "setSearchTerm", "sortBy", "setSortBy", "userClass", "length", "isValidSubject", "includes", "setAvailableClassesForLevel", "fetchMaterials", "normalizedClassName", "toString", "replace", "data", "content", "subject", "res", "status", "success", "handleTabChange", "tab", "handleSubjectChange", "handleClassChange", "toggleClassSelector", "filteredAndSortedMaterials", "filtered", "trim", "searchLower", "filter", "material", "title", "year", "sort", "a", "b", "parseInt", "localeCompare", "handleDocumentDownload", "proxyUrl", "process", "env", "REACT_APP_SERVER_DOMAIN", "encodeURIComponent", "fetch", "method", "headers", "localStorage", "getItem", "then", "response", "ok", "Error", "blob", "url", "window", "URL", "createObjectURL", "document", "createElement", "href", "download", "split", "pop", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "catch", "open", "handleDocumentPreview", "children", "div", "initial", "opacity", "y", "animate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toUpperCase", "key", "label", "icon", "map", "onClick", "transition", "delay", "placeholder", "value", "onChange", "e", "target", "exit", "index", "closeModal", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/StudyMaterial/index.js"], "sourcesContent": ["import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from \"react\";\nimport \"./index.css\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getStudyMaterial } from \"../../../apicalls/study\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { useLanguage } from \"../../../contexts/LanguageContext\";\n// import { Card, Button, Input, Loading } from \"../../../components/modern\";\n\nimport PDFModal from \"./PDFModal\";\nimport PastPaperDiscussion from \"../../../components/PastPaperDiscussion\";\nimport {\n  FaBook,\n  FaFileAlt,\n  FaGraduationCap,\n  FaDownload,\n  FaEye,\n  FaChevronDown,\n  FaSearch,\n  FaTimes,\n  FaRobot,\n} from \"react-icons/fa\";\nimport {\n  TbFileText,\n  Tb<PERSON><PERSON> as Tb<PERSON><PERSON>Icon,\n  <PERSON><PERSON><PERSON><PERSON>ol,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON>b<PERSON>ilter,\n  TbSortAscending,\n  TbDownload,\n  TbEye,\n  TbCalendar,\n  TbUser,\n  TbChevronDown as TbChevronDownIcon,\n  TbChevronUp,\n  TbX,\n  TbAlertTriangle,\n  TbInfoCircle,\n  TbCheck,\n  TbBooks,\n  TbCertificate\n} from \"react-icons/tb\";\nimport { primarySubjects, primaryKiswahiliSubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects.jsx\";\n\nfunction StudyMaterial() {\n  const { user } = useSelector((state) => state.user);\n  const { t, isKiswahili, getSubjectName } = useLanguage();\n  const dispatch = useDispatch();\n\n  // Get user level and subjects list (case-insensitive)\n  const userLevel = user?.level || 'Primary';\n  const userLevelLower = userLevel.toLowerCase();\n  const subjectsList = userLevelLower === 'primary'\n    ? primarySubjects\n    : userLevelLower === 'primary_kiswahili'\n      ? primaryKiswahiliSubjects\n      : userLevelLower === 'secondary'\n        ? secondarySubjects\n        : advanceSubjects;\n\n  // Debug: Log current level and subjects\n  useEffect(() => {\n    console.log('📚 Study Materials - User Level:', userLevel);\n    console.log('📚 Study Materials - User Level (lowercase):', userLevelLower);\n    console.log('📚 Study Materials - Subjects List:', subjectsList);\n    console.log('📚 Study Materials - User Data:', user);\n  }, [userLevel, userLevelLower, subjectsList, user]);\n\n  // Define all possible classes for each level\n  const allPossibleClasses = userLevelLower === 'primary'\n    ? ['1', '2', '3', '4', '5', '6', '7']\n    : userLevelLower === 'secondary'\n      ? ['Form-1', 'Form-2', 'Form-3', 'Form-4']\n      : ['Form-5', 'Form-6'];\n\n  // Simplified state management - initialize with user's class if available\n  const [activeTab, setActiveTab] = useState(\"study-notes\");\n  const [selectedClass, setSelectedClass] = useState(user?.class || user?.className || \"all\");\n  const [selectedSubject, setSelectedSubject] = useState(\"all\");\n\n  // Get user's current class for highlighting\n  const userCurrentClass = user?.class || user?.className;\n  const [materials, setMaterials] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  const [modalIsOpen, setModalIsOpen] = useState(false);\n  const [documentUrl, setDocumentUrl] = useState(\"\");\n  const [availableClasses, setAvailableClasses] = useState([]);\n  const [showClassSelector, setShowClassSelector] = useState(false);\n\n\n  // Unified search and sort states\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sortBy, setSortBy] = useState(\"newest\");\n\n  // Update selectedClass when user data becomes available\n  useEffect(() => {\n    const userClass = user?.class || user?.className;\n    if (userClass && selectedClass === \"all\" && !availableClasses.length) {\n      setSelectedClass(userClass);\n    }\n  }, [user, selectedClass, availableClasses.length]);\n\n  // Reset subject selection when user level changes\n  useEffect(() => {\n    if (user?.level) {\n      // Check if current selected subject is valid for the new level\n      const isValidSubject = subjectsList.includes(selectedSubject);\n      if (!isValidSubject && selectedSubject !== \"all\") {\n        console.log('📚 Resetting subject selection due to level change');\n        setSelectedSubject(\"all\");\n      }\n    }\n  }, [user?.level, subjectsList, selectedSubject]);\n\n  // Set available classes based on user level (show all possible classes)\n  const setAvailableClassesForLevel = useCallback(() => {\n    setAvailableClasses(allPossibleClasses);\n  }, [allPossibleClasses]);\n\n  // Simplified fetch function\n  const fetchMaterials = useCallback(async () => {\n    if (!activeTab || selectedClass === \"default\") {\n      return;\n    }\n\n    setIsLoading(true);\n    setError(null);\n    dispatch(ShowLoading());\n\n    try {\n      // Normalize className for backend - remove \"Form-\" prefix if present\n      const normalizedClassName = selectedClass === \"all\" ? \"all\" :\n        selectedClass.toString().replace(\"Form-\", \"\");\n\n      const data = {\n        content: activeTab,\n        className: normalizedClassName,\n        subject: selectedSubject, // This can be \"all\" or a specific subject\n      };\n      if (userLevel) {\n        data.level = userLevel;\n      }\n\n      const res = await getStudyMaterial(data);\n\n      if (res.status === 200 && res.data.success) {\n        const materials = res.data.data === \"empty\" ? [] : res.data.data;\n        setMaterials(materials);\n      } else {\n        setMaterials([]);\n        setError(`Failed to fetch ${activeTab}. Please try again.`);\n      }\n    } catch (error) {\n      console.error(\"Error fetching study material:\", error);\n      setMaterials([]);\n      setError(`Unable to load ${activeTab}. Please check your connection and try again.`);\n    } finally {\n      setIsLoading(false);\n      dispatch(HideLoading());\n    }\n  }, [activeTab, selectedClass, selectedSubject, userLevel, dispatch]);\n\n  // Set available classes when component mounts\n  useEffect(() => {\n    if (user && userLevel) {\n      setAvailableClassesForLevel();\n    }\n  }, [user, userLevel, setAvailableClassesForLevel]);\n\n  // Fetch materials when filters change or component mounts\n  useEffect(() => {\n    // Only fetch if we have a valid activeTab, selectedClass, and user\n    if (user && userLevel && activeTab && selectedClass && selectedClass !== \"default\") {\n      fetchMaterials();\n    }\n  }, [user, userLevel, activeTab, selectedClass, selectedSubject, fetchMaterials]);\n\n  // Handler functions\n  const handleTabChange = (tab) => {\n    setMaterials([]);\n    setActiveTab(tab);\n    setSearchTerm(\"\");\n    setSortBy(\"newest\");\n  };\n\n  const handleSubjectChange = (subject) => {\n    setMaterials([]);\n    setSelectedSubject(subject);\n    setSearchTerm(\"\");\n  };\n\n  const handleClassChange = (className) => {\n    setMaterials([]);\n    setSelectedClass(className);\n    setShowClassSelector(false);\n  };\n\n  const toggleClassSelector = () => {\n    setShowClassSelector(!showClassSelector);\n  };\n\n  // Unified filtering and sorting logic\n  const filteredAndSortedMaterials = useMemo(() => {\n    if (!materials || materials.length === 0) {\n      return [];\n    }\n\n    let filtered = materials;\n\n    // Filter by search term (title, subject, or year)\n    if (searchTerm.trim()) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(material =>\n        material.title.toLowerCase().includes(searchLower) ||\n        material.subject.toLowerCase().includes(searchLower) ||\n        (material.year && material.year.toLowerCase().includes(searchLower))\n      );\n    }\n\n    // Sort by year, creation date, or title\n    filtered.sort((a, b) => {\n      if (sortBy === \"newest\") {\n        // For materials with year field (books, past papers)\n        if (a.year && b.year) {\n          return parseInt(b.year) - parseInt(a.year);\n        }\n\n        // Fallback: materials with year come first\n        else if (a.year && !b.year) return -1;\n        else if (!a.year && b.year) return 1;\n        else return 0;\n      } else if (sortBy === \"oldest\") {\n        // For materials with year field\n        if (a.year && b.year) {\n          return parseInt(a.year) - parseInt(b.year);\n        }\n\n        // Fallback: materials with year come first\n        else if (a.year && !b.year) return -1;\n        else if (!a.year && b.year) return 1;\n        else return 0;\n      } else {\n        // Sort by title alphabetically\n        return a.title.localeCompare(b.title);\n      }\n    });\n\n\n\n    return filtered;\n  }, [materials, searchTerm, sortBy, activeTab]);\n\n  // Document handlers\n  const handleDocumentDownload = (documentUrl) => {\n    // Use proxy endpoint to handle CORS issues\n    const proxyUrl = `${process.env.REACT_APP_SERVER_DOMAIN}/api/study/document-proxy?url=${encodeURIComponent(documentUrl)}`;\n\n    fetch(proxyUrl, {\n      method: 'GET',\n      headers: {\n        'Authorization': `Bearer ${localStorage.getItem('token')}`,\n      }\n    })\n      .then((response) => {\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        return response.blob();\n      })\n      .then((blob) => {\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = documentUrl.split(\"/\").pop();\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        window.URL.revokeObjectURL(url);\n      })\n      .catch((error) => {\n        console.error(\"Error downloading the file:\", error);\n        // Fallback to direct download if proxy fails\n        window.open(documentUrl, '_blank');\n      });\n  };\n\n  const handleDocumentPreview = (documentUrl) => {\n    setDocumentUrl(documentUrl);\n    setModalIsOpen(true);\n  };\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50\">\n      {/* Modern Header */}\n      <motion.div\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        className=\"bg-gradient-to-r from-primary-600 to-blue-600 text-white\"\n      >\n        <div className=\"container-modern py-12\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-6\">\n              <div className=\"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center\">\n                <TbBooks className=\"w-8 h-8 text-white\" />\n              </div>\n              <div>\n                <h1 className=\"text-4xl font-bold mb-2\">Study Materials</h1>\n                <p className=\"text-xl text-blue-100\">\n                  Access comprehensive learning resources for {userLevel} education\n                </p>\n              </div>\n            </div>\n            <div className=\"hidden md:block\">\n              <div className=\"bg-white/20 backdrop-blur-sm rounded-xl px-6 py-3\">\n                <div className=\"text-sm text-blue-100 mb-1\">Current Level</div>\n                <div className=\"text-lg font-bold\">{userLevel?.toUpperCase()}</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </motion.div>\n\n      <div className=\"container-modern py-8\">\n        {/* Study Material Tabs */}\n        <div className=\"mb-6\">\n          <div className=\"study-tabs\">\n            {[\n              { key: 'study-notes', label: isKiswahili ? 'Maelezo' : 'Notes', icon: TbFileText },\n              { key: 'past-papers', label: isKiswahili ? 'Karatasi za Zamani' : 'Past Papers', icon: TbCertificate },\n              { key: 'books', label: isKiswahili ? 'Vitabu' : 'Books', icon: TbBookIcon }\n            ].map((tab) => (\n              <button\n                key={tab.key}\n                className={`study-tab ${activeTab === tab.key ? 'active' : ''}`}\n                onClick={() => handleTabChange(tab.key)}\n              >\n                <tab.icon />\n                <span>{tab.label}</span>\n              </button>\n            ))}\n          </div>\n        </div>\n\n        {/* Modern Filters Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.4 }}\n          className=\"mb-8\"\n        >\n          <div className=\"card p-6\">\n            <div className=\"flex flex-col lg:flex-row gap-6 items-end\">\n              {/* Search */}\n              <div className=\"flex-1\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  {isKiswahili ? 'Tafuta Vifaa' : 'Search Materials'}\n                </label>\n                <input\n                  placeholder={isKiswahili ? `Tafuta ${activeTab === 'study-notes' ? 'maelezo' : activeTab === 'past-papers' ? 'karatasi za zamani' : 'vitabu'}...` : `Search ${activeTab.replace('-', ' ')}...`}\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"form-input\"\n                />\n              </div>\n\n              {/* Class Filter */}\n              <div className=\"w-full lg:w-64\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  {isKiswahili ? 'Chuja kwa Darasa' : 'Filter by Class'}\n                  {userCurrentClass && (\n                    <span className=\"ml-2 text-xs text-primary-600 font-medium\">\n                      ({isKiswahili ? 'Darasa lako: ' : 'Your class: '}{userLevelLower === 'primary' || userLevelLower === 'primary_kiswahili' ? (isKiswahili ? `Darasa la ${userCurrentClass}` : `Class ${userCurrentClass}`) : `Form ${userCurrentClass}`})\n                    </span>\n                  )}\n                </label>\n                <div className=\"relative\">\n                  <button\n                    onClick={toggleClassSelector}\n                    className=\"w-full input-modern flex items-center justify-between\"\n                  >\n                    <span className=\"flex items-center space-x-2\">\n                      <TbSchool className=\"w-4 h-4 text-gray-400\" />\n                      <span>\n                        {selectedClass === 'all' ? 'All Classes' :\n                          userLevelLower === 'primary'\n                            ? `Class ${selectedClass}`\n                            : `Form ${selectedClass}`\n                        }\n                      </span>\n                      {selectedClass === userCurrentClass && (\n                        <span className=\"badge-primary text-xs\">Current</span>\n                      )}\n                    </span>\n                    <TbChevronDownIcon className={`w-4 h-4 text-gray-400 transition-transform ${showClassSelector ? 'rotate-180' : ''}`} />\n                  </button>\n\n                  <AnimatePresence>\n                    {showClassSelector && (\n                      <motion.div\n                        initial={{ opacity: 0, y: -10 }}\n                        animate={{ opacity: 1, y: 0 }}\n                        exit={{ opacity: 0, y: -10 }}\n                        className=\"absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto\"\n                      >\n                        <button\n                          className={`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors ${\n                            selectedClass === 'all' ? 'bg-primary-50 text-primary-700 font-medium' : 'text-gray-700'\n                          }`}\n                          onClick={() => handleClassChange('all')}\n                        >\n                          All Classes\n                        </button>\n                        {availableClasses.map((className, index) => (\n                          <button\n                            key={index}\n                            className={`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors flex items-center justify-between ${\n                              selectedClass === className ? 'bg-primary-50 text-primary-700 font-medium' : 'text-gray-700'\n                            }`}\n                            onClick={() => handleClassChange(className)}\n                          >\n                            <span>\n                              {userLevelLower === 'primary' ? `Class ${className}` : `Form ${className}`}\n                            </span>\n                            {className === userCurrentClass && (\n                              <span className=\"badge-success text-xs\">Your Class</span>\n                            )}\n                          </button>\n                        ))}\n                      </motion.div>\n                    )}\n                  </AnimatePresence>\n                </div>\n              </div>\n\n              {/* Subject Filter */}\n              <div className=\"w-full lg:w-64\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  {isKiswahili ? 'Chuja kwa Somo' : 'Filter by Subject'}\n                </label>\n                <select\n                  value={selectedSubject}\n                  onChange={(e) => handleSubjectChange(e.target.value)}\n                  className=\"input-modern\"\n                >\n                  <option value=\"all\">{isKiswahili ? 'Masomo Yote' : 'All Subjects'}</option>\n                  {subjectsList.map((subject, index) => (\n                    <option key={index} value={subject}>\n                      {getSubjectName(subject)}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              {/* Sort */}\n              <div className=\"w-full lg:w-48\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  {isKiswahili ? 'Panga kwa' : 'Sort by'}\n                </label>\n                <select\n                  value={sortBy}\n                  onChange={(e) => setSortBy(e.target.value)}\n                  className=\"input-modern\"\n                >\n                  <option value=\"newest\">Newest First</option>\n                  <option value=\"oldest\">Oldest First</option>\n                  <option value=\"title\">By Title</option>\n                </select>\n              </div>\n\n              {/* Clear Filters */}\n              <button\n                className=\"btn btn-secondary\"\n                onClick={() => {\n                  setSearchTerm(\"\");\n                  setSelectedClass(\"all\");\n                  setSelectedSubject(\"all\");\n                  setSortBy(\"newest\");\n                }}\n              >\n                Clear Filters\n              </button>\n            </div>\n\n            {/* Results Count */}\n            {(searchTerm || selectedClass !== \"all\" || selectedSubject !== \"all\") && (\n              <div className=\"mt-4 pt-4 border-t border-gray-100\">\n                <span className=\"text-sm text-gray-600\">\n                  Showing {filteredAndSortedMaterials.length} of {materials.length} {activeTab.replace('-', ' ')}\n                </span>\n              </div>\n            )}\n          </div>\n        </motion.div>\n\n      {/* Materials Display */}\n      <div className=\"materials-section\">\n        {isLoading ? (\n          <div className=\"loading-state\">\n            <div className=\"loading-spinner\"></div>\n            <p>Loading materials...</p>\n          </div>\n        ) : error ? (\n          <div className=\"error-state\">\n            <FaTimes className=\"error-icon\" />\n            <h3>Error Loading Materials</h3>\n            <p>{error}</p>\n            <button\n              className=\"retry-btn\"\n              onClick={() => {\n                setError(null);\n                fetchMaterials();\n              }}\n            >\n              Try Again\n            </button>\n          </div>\n        ) : filteredAndSortedMaterials.length > 0 ? (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n            {filteredAndSortedMaterials.map((material, index) => (\n              <div key={index} className=\"study-card\">\n                <div className=\"study-card-header\">\n                  <div className=\"study-card-meta\">\n                    {activeTab === 'study-notes' && <FaFileAlt />}\n                    {activeTab === 'past-papers' && <FaFileAlt />}\n                    {activeTab === 'books' && <FaBook />}\n                    <span>\n                      {activeTab === 'study-notes' ? (isKiswahili ? 'Maelezo' : 'Note') :\n                       activeTab === 'past-papers' ? (isKiswahili ? 'Karatasi ya Zamani' : 'Past Paper') :\n                       (isKiswahili ? 'Kitabu' : 'Book')}\n                    </span>\n                  </div>\n\n                  <div className=\"study-card-title\">\n                    {material.title}\n                  </div>\n\n\n                  {material.year && (\n                    <span className=\"badge badge-secondary mt-2\">{material.year}</span>\n                  )}\n                </div>\n\n\n\n                <div className=\"card-content\">\n                  <h3 className=\"material-title\">{material.title}</h3>\n                  <div className=\"material-meta\">\n                    <span className=\"material-subject\">{material.subject}</span>\n                    {material.className && (\n                      <span className=\"material-class\">\n                        {userLevelLower === 'primary' ? `Class ${material.className}` : `Form ${material.className}`}\n                      </span>\n                    )}\n                  </div>\n                </div>\n\n                <div className=\"card-actions\">\n                  {material.documentUrl ? (\n                    <>\n                      <button\n                        className=\"action-btn secondary\"\n                        onClick={() => handleDocumentPreview(material.documentUrl)}\n                      >\n                        <FaEye /> View\n                      </button>\n                      <button\n                        className=\"action-btn primary\"\n                        onClick={() => handleDocumentDownload(material.documentUrl)}\n                      >\n                        <FaDownload /> Download\n                      </button>\n                    </>\n                  ) : (\n                    <span className=\"unavailable\">Not available</span>\n                  )}\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"empty-state\">\n            <FaGraduationCap className=\"empty-icon\" />\n            <h3>No Materials Found</h3>\n            <p>No study materials are available for your current selection.</p>\n            <p className=\"suggestion\">Try selecting a different class or subject.</p>\n          </div>\n        )}\n      </div>\n\n\n\n\n      {/* PDF Modal */}\n      <PDFModal\n        modalIsOpen={modalIsOpen}\n        closeModal={() => {\n          setModalIsOpen(false);\n          setDocumentUrl(\"\");\n        }}\n        documentUrl={documentUrl}\n      />\n      </div>\n    </div>\n  );\n}\n\nexport default StudyMaterial;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACxE,OAAO,aAAa;AACpB,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,WAAW,QAAQ,mCAAmC;AAC/D;;AAEA,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,mBAAmB,MAAM,yCAAyC;AACzE,SACEC,MAAM,EACNC,SAAS,EACTC,eAAe,EACfC,UAAU,EACVC,KAAK,EACLC,aAAa,EACbC,QAAQ,EACRC,OAAO,EACPC,OAAO,QACF,gBAAgB;AACvB,SACEC,UAAU,EACVC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,EACRC,QAAQ,EACRC,QAAQ,EACRC,eAAe,EACfC,UAAU,EACVC,KAAK,EACLC,UAAU,EACVC,MAAM,EACNC,aAAa,IAAIC,iBAAiB,EAClCC,WAAW,EACXC,GAAG,EACHC,eAAe,EACfC,YAAY,EACZC,OAAO,EACPC,OAAO,EACPC,aAAa,QACR,gBAAgB;AACvB,SAASC,eAAe,EAAEC,wBAAwB,EAAEC,iBAAiB,EAAEC,eAAe,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE3H,SAASC,aAAaA,CAAA,EAAG;EAAAC,EAAA;EACvB,MAAM;IAAEC;EAAK,CAAC,GAAG7C,WAAW,CAAE8C,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE,CAAC;IAAEC,WAAW;IAAEC;EAAe,CAAC,GAAG9C,WAAW,CAAC,CAAC;EACxD,MAAM+C,QAAQ,GAAGnD,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMoD,SAAS,GAAG,CAAAN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,KAAK,KAAI,SAAS;EAC1C,MAAMC,cAAc,GAAGF,SAAS,CAACG,WAAW,CAAC,CAAC;EAC9C,MAAMC,YAAY,GAAGF,cAAc,KAAK,SAAS,GAC7ClB,eAAe,GACfkB,cAAc,KAAK,mBAAmB,GACpCjB,wBAAwB,GACxBiB,cAAc,KAAK,WAAW,GAC5BhB,iBAAiB,GACjBC,eAAe;;EAEvB;EACA7C,SAAS,CAAC,MAAM;IACd+D,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEN,SAAS,CAAC;IAC1DK,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEJ,cAAc,CAAC;IAC3EG,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEF,YAAY,CAAC;IAChEC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEZ,IAAI,CAAC;EACtD,CAAC,EAAE,CAACM,SAAS,EAAEE,cAAc,EAAEE,YAAY,EAAEV,IAAI,CAAC,CAAC;;EAEnD;EACA,MAAMa,kBAAkB,GAAGL,cAAc,KAAK,SAAS,GACnD,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,GACnCA,cAAc,KAAK,WAAW,GAC5B,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,GACxC,CAAC,QAAQ,EAAE,QAAQ,CAAC;;EAE1B;EACA,MAAM,CAACM,SAAS,EAAEC,YAAY,CAAC,GAAGpE,QAAQ,CAAC,aAAa,CAAC;EACzD,MAAM,CAACqE,aAAa,EAAEC,gBAAgB,CAAC,GAAGtE,QAAQ,CAAC,CAAAqD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,KAAK,MAAIlB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB,SAAS,KAAI,KAAK,CAAC;EAC3F,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM2E,gBAAgB,GAAG,CAAAtB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,KAAK,MAAIlB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB,SAAS;EACvD,MAAM,CAACI,SAAS,EAAEC,YAAY,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC8E,SAAS,EAAEC,YAAY,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgF,KAAK,EAAEC,QAAQ,CAAC,GAAGjF,QAAQ,CAAC,IAAI,CAAC;EAExC,MAAM,CAACkF,WAAW,EAAEC,cAAc,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACoF,WAAW,EAAEC,cAAc,CAAC,GAAGrF,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACsF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvF,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACwF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzF,QAAQ,CAAC,KAAK,CAAC;;EAGjE;EACA,MAAM,CAAC0F,UAAU,EAAEC,aAAa,CAAC,GAAG3F,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4F,MAAM,EAAEC,SAAS,CAAC,GAAG7F,QAAQ,CAAC,QAAQ,CAAC;;EAE9C;EACAC,SAAS,CAAC,MAAM;IACd,MAAM6F,SAAS,GAAG,CAAAzC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,KAAK,MAAIlB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB,SAAS;IAChD,IAAIsB,SAAS,IAAIzB,aAAa,KAAK,KAAK,IAAI,CAACiB,gBAAgB,CAACS,MAAM,EAAE;MACpEzB,gBAAgB,CAACwB,SAAS,CAAC;IAC7B;EACF,CAAC,EAAE,CAACzC,IAAI,EAAEgB,aAAa,EAAEiB,gBAAgB,CAACS,MAAM,CAAC,CAAC;;EAElD;EACA9F,SAAS,CAAC,MAAM;IACd,IAAIoD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEO,KAAK,EAAE;MACf;MACA,MAAMoC,cAAc,GAAGjC,YAAY,CAACkC,QAAQ,CAACxB,eAAe,CAAC;MAC7D,IAAI,CAACuB,cAAc,IAAIvB,eAAe,KAAK,KAAK,EAAE;QAChDT,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;QACjES,kBAAkB,CAAC,KAAK,CAAC;MAC3B;IACF;EACF,CAAC,EAAE,CAACrB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,KAAK,EAAEG,YAAY,EAAEU,eAAe,CAAC,CAAC;;EAEhD;EACA,MAAMyB,2BAA2B,GAAGhG,WAAW,CAAC,MAAM;IACpDqF,mBAAmB,CAACrB,kBAAkB,CAAC;EACzC,CAAC,EAAE,CAACA,kBAAkB,CAAC,CAAC;;EAExB;EACA,MAAMiC,cAAc,GAAGjG,WAAW,CAAC,YAAY;IAC7C,IAAI,CAACiE,SAAS,IAAIE,aAAa,KAAK,SAAS,EAAE;MAC7C;IACF;IAEAU,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IACdvB,QAAQ,CAAChD,WAAW,CAAC,CAAC,CAAC;IAEvB,IAAI;MACF;MACA,MAAM0F,mBAAmB,GAAG/B,aAAa,KAAK,KAAK,GAAG,KAAK,GACzDA,aAAa,CAACgC,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;MAE/C,MAAMC,IAAI,GAAG;QACXC,OAAO,EAAErC,SAAS;QAClBK,SAAS,EAAE4B,mBAAmB;QAC9BK,OAAO,EAAEhC,eAAe,CAAE;MAC5B,CAAC;;MACD,IAAId,SAAS,EAAE;QACb4C,IAAI,CAAC3C,KAAK,GAAGD,SAAS;MACxB;MAEA,MAAM+C,GAAG,GAAG,MAAMpG,gBAAgB,CAACiG,IAAI,CAAC;MAExC,IAAIG,GAAG,CAACC,MAAM,KAAK,GAAG,IAAID,GAAG,CAACH,IAAI,CAACK,OAAO,EAAE;QAC1C,MAAMhC,SAAS,GAAG8B,GAAG,CAACH,IAAI,CAACA,IAAI,KAAK,OAAO,GAAG,EAAE,GAAGG,GAAG,CAACH,IAAI,CAACA,IAAI;QAChE1B,YAAY,CAACD,SAAS,CAAC;MACzB,CAAC,MAAM;QACLC,YAAY,CAAC,EAAE,CAAC;QAChBI,QAAQ,CAAE,mBAAkBd,SAAU,qBAAoB,CAAC;MAC7D;IACF,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdhB,OAAO,CAACgB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDH,YAAY,CAAC,EAAE,CAAC;MAChBI,QAAQ,CAAE,kBAAiBd,SAAU,+CAA8C,CAAC;IACtF,CAAC,SAAS;MACRY,YAAY,CAAC,KAAK,CAAC;MACnBrB,QAAQ,CAACjD,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAAC0D,SAAS,EAAEE,aAAa,EAAEI,eAAe,EAAEd,SAAS,EAAED,QAAQ,CAAC,CAAC;;EAEpE;EACAzD,SAAS,CAAC,MAAM;IACd,IAAIoD,IAAI,IAAIM,SAAS,EAAE;MACrBuC,2BAA2B,CAAC,CAAC;IAC/B;EACF,CAAC,EAAE,CAAC7C,IAAI,EAAEM,SAAS,EAAEuC,2BAA2B,CAAC,CAAC;;EAElD;EACAjG,SAAS,CAAC,MAAM;IACd;IACA,IAAIoD,IAAI,IAAIM,SAAS,IAAIQ,SAAS,IAAIE,aAAa,IAAIA,aAAa,KAAK,SAAS,EAAE;MAClF8B,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAC9C,IAAI,EAAEM,SAAS,EAAEQ,SAAS,EAAEE,aAAa,EAAEI,eAAe,EAAE0B,cAAc,CAAC,CAAC;;EAEhF;EACA,MAAMU,eAAe,GAAIC,GAAG,IAAK;IAC/BjC,YAAY,CAAC,EAAE,CAAC;IAChBT,YAAY,CAAC0C,GAAG,CAAC;IACjBnB,aAAa,CAAC,EAAE,CAAC;IACjBE,SAAS,CAAC,QAAQ,CAAC;EACrB,CAAC;EAED,MAAMkB,mBAAmB,GAAIN,OAAO,IAAK;IACvC5B,YAAY,CAAC,EAAE,CAAC;IAChBH,kBAAkB,CAAC+B,OAAO,CAAC;IAC3Bd,aAAa,CAAC,EAAE,CAAC;EACnB,CAAC;EAED,MAAMqB,iBAAiB,GAAIxC,SAAS,IAAK;IACvCK,YAAY,CAAC,EAAE,CAAC;IAChBP,gBAAgB,CAACE,SAAS,CAAC;IAC3BiB,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;EAED,MAAMwB,mBAAmB,GAAGA,CAAA,KAAM;IAChCxB,oBAAoB,CAAC,CAACD,iBAAiB,CAAC;EAC1C,CAAC;;EAED;EACA,MAAM0B,0BAA0B,GAAG/G,OAAO,CAAC,MAAM;IAC/C,IAAI,CAACyE,SAAS,IAAIA,SAAS,CAACmB,MAAM,KAAK,CAAC,EAAE;MACxC,OAAO,EAAE;IACX;IAEA,IAAIoB,QAAQ,GAAGvC,SAAS;;IAExB;IACA,IAAIc,UAAU,CAAC0B,IAAI,CAAC,CAAC,EAAE;MACrB,MAAMC,WAAW,GAAG3B,UAAU,CAAC5B,WAAW,CAAC,CAAC;MAC5CqD,QAAQ,GAAGA,QAAQ,CAACG,MAAM,CAACC,QAAQ,IACjCA,QAAQ,CAACC,KAAK,CAAC1D,WAAW,CAAC,CAAC,CAACmC,QAAQ,CAACoB,WAAW,CAAC,IAClDE,QAAQ,CAACd,OAAO,CAAC3C,WAAW,CAAC,CAAC,CAACmC,QAAQ,CAACoB,WAAW,CAAC,IACnDE,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAAC3D,WAAW,CAAC,CAAC,CAACmC,QAAQ,CAACoB,WAAW,CACpE,CAAC;IACH;;IAEA;IACAF,QAAQ,CAACO,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACtB,IAAIhC,MAAM,KAAK,QAAQ,EAAE;QACvB;QACA,IAAI+B,CAAC,CAACF,IAAI,IAAIG,CAAC,CAACH,IAAI,EAAE;UACpB,OAAOI,QAAQ,CAACD,CAAC,CAACH,IAAI,CAAC,GAAGI,QAAQ,CAACF,CAAC,CAACF,IAAI,CAAC;QAC5C;;QAEA;QAAA,KACK,IAAIE,CAAC,CAACF,IAAI,IAAI,CAACG,CAAC,CAACH,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,KACjC,IAAI,CAACE,CAAC,CAACF,IAAI,IAAIG,CAAC,CAACH,IAAI,EAAE,OAAO,CAAC,CAAC,KAChC,OAAO,CAAC;MACf,CAAC,MAAM,IAAI7B,MAAM,KAAK,QAAQ,EAAE;QAC9B;QACA,IAAI+B,CAAC,CAACF,IAAI,IAAIG,CAAC,CAACH,IAAI,EAAE;UACpB,OAAOI,QAAQ,CAACF,CAAC,CAACF,IAAI,CAAC,GAAGI,QAAQ,CAACD,CAAC,CAACH,IAAI,CAAC;QAC5C;;QAEA;QAAA,KACK,IAAIE,CAAC,CAACF,IAAI,IAAI,CAACG,CAAC,CAACH,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,KACjC,IAAI,CAACE,CAAC,CAACF,IAAI,IAAIG,CAAC,CAACH,IAAI,EAAE,OAAO,CAAC,CAAC,KAChC,OAAO,CAAC;MACf,CAAC,MAAM;QACL;QACA,OAAOE,CAAC,CAACH,KAAK,CAACM,aAAa,CAACF,CAAC,CAACJ,KAAK,CAAC;MACvC;IACF,CAAC,CAAC;IAIF,OAAOL,QAAQ;EACjB,CAAC,EAAE,CAACvC,SAAS,EAAEc,UAAU,EAAEE,MAAM,EAAEzB,SAAS,CAAC,CAAC;;EAE9C;EACA,MAAM4D,sBAAsB,GAAI3C,WAAW,IAAK;IAC9C;IACA,MAAM4C,QAAQ,GAAI,GAAEC,OAAO,CAACC,GAAG,CAACC,uBAAwB,iCAAgCC,kBAAkB,CAAChD,WAAW,CAAE,EAAC;IAEzHiD,KAAK,CAACL,QAAQ,EAAE;MACdM,MAAM,EAAE,KAAK;MACbC,OAAO,EAAE;QACP,eAAe,EAAG,UAASC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAE;MAC3D;IACF,CAAC,CAAC,CACCC,IAAI,CAAEC,QAAQ,IAAK;MAClB,IAAI,CAACA,QAAQ,CAACC,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAE,uBAAsBF,QAAQ,CAAChC,MAAO,EAAC,CAAC;MAC3D;MACA,OAAOgC,QAAQ,CAACG,IAAI,CAAC,CAAC;IACxB,CAAC,CAAC,CACDJ,IAAI,CAAEI,IAAI,IAAK;MACd,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;MAC5C,MAAMnB,CAAC,GAAGwB,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACrCzB,CAAC,CAAC0B,IAAI,GAAGN,GAAG;MACZpB,CAAC,CAAC2B,QAAQ,GAAGlE,WAAW,CAACmE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;MACzCL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAAC/B,CAAC,CAAC;MAC5BA,CAAC,CAACgC,KAAK,CAAC,CAAC;MACTR,QAAQ,CAACM,IAAI,CAACG,WAAW,CAACjC,CAAC,CAAC;MAC5BqB,MAAM,CAACC,GAAG,CAACY,eAAe,CAACd,GAAG,CAAC;IACjC,CAAC,CAAC,CACDe,KAAK,CAAE9E,KAAK,IAAK;MAChBhB,OAAO,CAACgB,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD;MACAgE,MAAM,CAACe,IAAI,CAAC3E,WAAW,EAAE,QAAQ,CAAC;IACpC,CAAC,CAAC;EACN,CAAC;EAED,MAAM4E,qBAAqB,GAAI5E,WAAW,IAAK;IAC7CC,cAAc,CAACD,WAAW,CAAC;IAC3BD,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAwBD,oBACEnC,OAAA;IAAKwB,SAAS,EAAC,wDAAwD;IAAAyF,QAAA,gBAErEjH,OAAA,CAAC5C,MAAM,CAAC8J,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;MAAG,CAAE;MAChCC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9B7F,SAAS,EAAC,0DAA0D;MAAAyF,QAAA,eAEpEjH,OAAA;QAAKwB,SAAS,EAAC,wBAAwB;QAAAyF,QAAA,eACrCjH,OAAA;UAAKwB,SAAS,EAAC,mCAAmC;UAAAyF,QAAA,gBAChDjH,OAAA;YAAKwB,SAAS,EAAC,6BAA6B;YAAAyF,QAAA,gBAC1CjH,OAAA;cAAKwB,SAAS,EAAC,qEAAqE;cAAAyF,QAAA,eAClFjH,OAAA,CAACP,OAAO;gBAAC+B,SAAS,EAAC;cAAoB;gBAAA+F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACN1H,OAAA;cAAAiH,QAAA,gBACEjH,OAAA;gBAAIwB,SAAS,EAAC,yBAAyB;gBAAAyF,QAAA,EAAC;cAAe;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5D1H,OAAA;gBAAGwB,SAAS,EAAC,uBAAuB;gBAAAyF,QAAA,GAAC,8CACS,EAACtG,SAAS,EAAC,YACzD;cAAA;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN1H,OAAA;YAAKwB,SAAS,EAAC,iBAAiB;YAAAyF,QAAA,eAC9BjH,OAAA;cAAKwB,SAAS,EAAC,mDAAmD;cAAAyF,QAAA,gBAChEjH,OAAA;gBAAKwB,SAAS,EAAC,4BAA4B;gBAAAyF,QAAA,EAAC;cAAa;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/D1H,OAAA;gBAAKwB,SAAS,EAAC,mBAAmB;gBAAAyF,QAAA,EAAEtG,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEgH,WAAW,CAAC;cAAC;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAEb1H,OAAA;MAAKwB,SAAS,EAAC,uBAAuB;MAAAyF,QAAA,gBAEpCjH,OAAA;QAAKwB,SAAS,EAAC,MAAM;QAAAyF,QAAA,eACnBjH,OAAA;UAAKwB,SAAS,EAAC,YAAY;UAAAyF,QAAA,EACxB,CACC;YAAEW,GAAG,EAAE,aAAa;YAAEC,KAAK,EAAErH,WAAW,GAAG,SAAS,GAAG,OAAO;YAAEsH,IAAI,EAAEvJ;UAAW,CAAC,EAClF;YAAEqJ,GAAG,EAAE,aAAa;YAAEC,KAAK,EAAErH,WAAW,GAAG,oBAAoB,GAAG,aAAa;YAAEsH,IAAI,EAAEpI;UAAc,CAAC,EACtG;YAAEkI,GAAG,EAAE,OAAO;YAAEC,KAAK,EAAErH,WAAW,GAAG,QAAQ,GAAG,OAAO;YAAEsH,IAAI,EAAErJ;UAAW,CAAC,CAC5E,CAACsJ,GAAG,CAAEjE,GAAG,iBACR9D,OAAA;YAEEwB,SAAS,EAAG,aAAYL,SAAS,KAAK2C,GAAG,CAAC8D,GAAG,GAAG,QAAQ,GAAG,EAAG,EAAE;YAChEI,OAAO,EAAEA,CAAA,KAAMnE,eAAe,CAACC,GAAG,CAAC8D,GAAG,CAAE;YAAAX,QAAA,gBAExCjH,OAAA,CAAC8D,GAAG,CAACgE,IAAI;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACZ1H,OAAA;cAAAiH,QAAA,EAAOnD,GAAG,CAAC+D;YAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GALnB5D,GAAG,CAAC8D,GAAG;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMN,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN1H,OAAA,CAAC5C,MAAM,CAAC8J,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BY,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3B1G,SAAS,EAAC,MAAM;QAAAyF,QAAA,eAEhBjH,OAAA;UAAKwB,SAAS,EAAC,UAAU;UAAAyF,QAAA,gBACvBjH,OAAA;YAAKwB,SAAS,EAAC,2CAA2C;YAAAyF,QAAA,gBAExDjH,OAAA;cAAKwB,SAAS,EAAC,QAAQ;cAAAyF,QAAA,gBACrBjH,OAAA;gBAAOwB,SAAS,EAAC,8CAA8C;gBAAAyF,QAAA,EAC5DzG,WAAW,GAAG,cAAc,GAAG;cAAkB;gBAAA+G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACR1H,OAAA;gBACEmI,WAAW,EAAE3H,WAAW,GAAI,UAASW,SAAS,KAAK,aAAa,GAAG,SAAS,GAAGA,SAAS,KAAK,aAAa,GAAG,oBAAoB,GAAG,QAAS,KAAI,GAAI,UAASA,SAAS,CAACmC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAE,KAAK;gBAC/L8E,KAAK,EAAE1F,UAAW;gBAClB2F,QAAQ,EAAGC,CAAC,IAAK3F,aAAa,CAAC2F,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAC/C5G,SAAS,EAAC;cAAY;gBAAA+F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGN1H,OAAA;cAAKwB,SAAS,EAAC,gBAAgB;cAAAyF,QAAA,gBAC7BjH,OAAA;gBAAOwB,SAAS,EAAC,8CAA8C;gBAAAyF,QAAA,GAC5DzG,WAAW,GAAG,kBAAkB,GAAG,iBAAiB,EACpDmB,gBAAgB,iBACf3B,OAAA;kBAAMwB,SAAS,EAAC,2CAA2C;kBAAAyF,QAAA,GAAC,GACzD,EAACzG,WAAW,GAAG,eAAe,GAAG,cAAc,EAAEK,cAAc,KAAK,SAAS,IAAIA,cAAc,KAAK,mBAAmB,GAAIL,WAAW,GAAI,aAAYmB,gBAAiB,EAAC,GAAI,SAAQA,gBAAiB,EAAC,GAAK,QAAOA,gBAAiB,EAAC,EAAC,GACxO;gBAAA;kBAAA4F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eACR1H,OAAA;gBAAKwB,SAAS,EAAC,UAAU;gBAAAyF,QAAA,gBACvBjH,OAAA;kBACEgI,OAAO,EAAE/D,mBAAoB;kBAC7BzC,SAAS,EAAC,uDAAuD;kBAAAyF,QAAA,gBAEjEjH,OAAA;oBAAMwB,SAAS,EAAC,6BAA6B;oBAAAyF,QAAA,gBAC3CjH,OAAA,CAACtB,QAAQ;sBAAC8C,SAAS,EAAC;oBAAuB;sBAAA+F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC9C1H,OAAA;sBAAAiH,QAAA,EACG5F,aAAa,KAAK,KAAK,GAAG,aAAa,GACtCR,cAAc,KAAK,SAAS,GACvB,SAAQQ,aAAc,EAAC,GACvB,QAAOA,aAAc;oBAAC;sBAAAkG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEzB,CAAC,EACNrG,aAAa,KAAKM,gBAAgB,iBACjC3B,OAAA;sBAAMwB,SAAS,EAAC,uBAAuB;sBAAAyF,QAAA,EAAC;oBAAO;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CACtD;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC,eACP1H,OAAA,CAACb,iBAAiB;oBAACqC,SAAS,EAAG,8CAA6CgB,iBAAiB,GAAG,YAAY,GAAG,EAAG;kBAAE;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjH,CAAC,eAET1H,OAAA,CAAC3C,eAAe;kBAAA4J,QAAA,EACbzE,iBAAiB,iBAChBxC,OAAA,CAAC5C,MAAM,CAAC8J,GAAG;oBACTC,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE,CAAC;oBAAG,CAAE;oBAChCC,OAAO,EAAE;sBAAEF,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE;oBAAE,CAAE;oBAC9BmB,IAAI,EAAE;sBAAEpB,OAAO,EAAE,CAAC;sBAAEC,CAAC,EAAE,CAAC;oBAAG,CAAE;oBAC7B7F,SAAS,EAAC,0HAA0H;oBAAAyF,QAAA,gBAEpIjH,OAAA;sBACEwB,SAAS,EAAG,iEACVH,aAAa,KAAK,KAAK,GAAG,4CAA4C,GAAG,eAC1E,EAAE;sBACH2G,OAAO,EAAEA,CAAA,KAAMhE,iBAAiB,CAAC,KAAK,CAAE;sBAAAiD,QAAA,EACzC;oBAED;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EACRpF,gBAAgB,CAACyF,GAAG,CAAC,CAACvG,SAAS,EAAEiH,KAAK,kBACrCzI,OAAA;sBAEEwB,SAAS,EAAG,mGACVH,aAAa,KAAKG,SAAS,GAAG,4CAA4C,GAAG,eAC9E,EAAE;sBACHwG,OAAO,EAAEA,CAAA,KAAMhE,iBAAiB,CAACxC,SAAS,CAAE;sBAAAyF,QAAA,gBAE5CjH,OAAA;wBAAAiH,QAAA,EACGpG,cAAc,KAAK,SAAS,GAAI,SAAQW,SAAU,EAAC,GAAI,QAAOA,SAAU;sBAAC;wBAAA+F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtE,CAAC,EACNlG,SAAS,KAAKG,gBAAgB,iBAC7B3B,OAAA;wBAAMwB,SAAS,EAAC,uBAAuB;wBAAAyF,QAAA,EAAC;sBAAU;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CACzD;oBAAA,GAXIe,KAAK;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAYJ,CACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ;gBACb;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACc,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN1H,OAAA;cAAKwB,SAAS,EAAC,gBAAgB;cAAAyF,QAAA,gBAC7BjH,OAAA;gBAAOwB,SAAS,EAAC,8CAA8C;gBAAAyF,QAAA,EAC5DzG,WAAW,GAAG,gBAAgB,GAAG;cAAmB;gBAAA+G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACR1H,OAAA;gBACEoI,KAAK,EAAE3G,eAAgB;gBACvB4G,QAAQ,EAAGC,CAAC,IAAKvE,mBAAmB,CAACuE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBACrD5G,SAAS,EAAC,cAAc;gBAAAyF,QAAA,gBAExBjH,OAAA;kBAAQoI,KAAK,EAAC,KAAK;kBAAAnB,QAAA,EAAEzG,WAAW,GAAG,aAAa,GAAG;gBAAc;kBAAA+G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,EAC1E3G,YAAY,CAACgH,GAAG,CAAC,CAACtE,OAAO,EAAEgF,KAAK,kBAC/BzI,OAAA;kBAAoBoI,KAAK,EAAE3E,OAAQ;kBAAAwD,QAAA,EAChCxG,cAAc,CAACgD,OAAO;gBAAC,GADbgF,KAAK;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEV,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGN1H,OAAA;cAAKwB,SAAS,EAAC,gBAAgB;cAAAyF,QAAA,gBAC7BjH,OAAA;gBAAOwB,SAAS,EAAC,8CAA8C;gBAAAyF,QAAA,EAC5DzG,WAAW,GAAG,WAAW,GAAG;cAAS;gBAAA+G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACR1H,OAAA;gBACEoI,KAAK,EAAExF,MAAO;gBACdyF,QAAQ,EAAGC,CAAC,IAAKzF,SAAS,CAACyF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAC3C5G,SAAS,EAAC,cAAc;gBAAAyF,QAAA,gBAExBjH,OAAA;kBAAQoI,KAAK,EAAC,QAAQ;kBAAAnB,QAAA,EAAC;gBAAY;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5C1H,OAAA;kBAAQoI,KAAK,EAAC,QAAQ;kBAAAnB,QAAA,EAAC;gBAAY;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5C1H,OAAA;kBAAQoI,KAAK,EAAC,OAAO;kBAAAnB,QAAA,EAAC;gBAAQ;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGN1H,OAAA;cACEwB,SAAS,EAAC,mBAAmB;cAC7BwG,OAAO,EAAEA,CAAA,KAAM;gBACbrF,aAAa,CAAC,EAAE,CAAC;gBACjBrB,gBAAgB,CAAC,KAAK,CAAC;gBACvBI,kBAAkB,CAAC,KAAK,CAAC;gBACzBmB,SAAS,CAAC,QAAQ,CAAC;cACrB,CAAE;cAAAoE,QAAA,EACH;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAGL,CAAChF,UAAU,IAAIrB,aAAa,KAAK,KAAK,IAAII,eAAe,KAAK,KAAK,kBAClEzB,OAAA;YAAKwB,SAAS,EAAC,oCAAoC;YAAAyF,QAAA,eACjDjH,OAAA;cAAMwB,SAAS,EAAC,uBAAuB;cAAAyF,QAAA,GAAC,UAC9B,EAAC/C,0BAA0B,CAACnB,MAAM,EAAC,MAAI,EAACnB,SAAS,CAACmB,MAAM,EAAC,GAAC,EAAC5B,SAAS,CAACmC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;YAAA;cAAAiE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGf1H,OAAA;QAAKwB,SAAS,EAAC,mBAAmB;QAAAyF,QAAA,EAC/BnF,SAAS,gBACR9B,OAAA;UAAKwB,SAAS,EAAC,eAAe;UAAAyF,QAAA,gBAC5BjH,OAAA;YAAKwB,SAAS,EAAC;UAAiB;YAAA+F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvC1H,OAAA;YAAAiH,QAAA,EAAG;UAAoB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,GACJ1F,KAAK,gBACPhC,OAAA;UAAKwB,SAAS,EAAC,aAAa;UAAAyF,QAAA,gBAC1BjH,OAAA,CAAC3B,OAAO;YAACmD,SAAS,EAAC;UAAY;YAAA+F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClC1H,OAAA;YAAAiH,QAAA,EAAI;UAAuB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChC1H,OAAA;YAAAiH,QAAA,EAAIjF;UAAK;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACd1H,OAAA;YACEwB,SAAS,EAAC,WAAW;YACrBwG,OAAO,EAAEA,CAAA,KAAM;cACb/F,QAAQ,CAAC,IAAI,CAAC;cACdkB,cAAc,CAAC,CAAC;YAClB,CAAE;YAAA8D,QAAA,EACH;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,GACJxD,0BAA0B,CAACnB,MAAM,GAAG,CAAC,gBACvC/C,OAAA;UAAKwB,SAAS,EAAC,sDAAsD;UAAAyF,QAAA,EAClE/C,0BAA0B,CAAC6D,GAAG,CAAC,CAACxD,QAAQ,EAAEkE,KAAK,kBAC9CzI,OAAA;YAAiBwB,SAAS,EAAC,YAAY;YAAAyF,QAAA,gBACrCjH,OAAA;cAAKwB,SAAS,EAAC,mBAAmB;cAAAyF,QAAA,gBAChCjH,OAAA;gBAAKwB,SAAS,EAAC,iBAAiB;gBAAAyF,QAAA,GAC7B9F,SAAS,KAAK,aAAa,iBAAInB,OAAA,CAACjC,SAAS;kBAAAwJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAC5CvG,SAAS,KAAK,aAAa,iBAAInB,OAAA,CAACjC,SAAS;kBAAAwJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAC5CvG,SAAS,KAAK,OAAO,iBAAInB,OAAA,CAAClC,MAAM;kBAAAyJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpC1H,OAAA;kBAAAiH,QAAA,EACG9F,SAAS,KAAK,aAAa,GAAIX,WAAW,GAAG,SAAS,GAAG,MAAM,GAC/DW,SAAS,KAAK,aAAa,GAAIX,WAAW,GAAG,oBAAoB,GAAG,YAAY,GAC/EA,WAAW,GAAG,QAAQ,GAAG;gBAAO;kBAAA+G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eAEN1H,OAAA;gBAAKwB,SAAS,EAAC,kBAAkB;gBAAAyF,QAAA,EAC9B1C,QAAQ,CAACC;cAAK;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,EAGLnD,QAAQ,CAACE,IAAI,iBACZzE,OAAA;gBAAMwB,SAAS,EAAC,4BAA4B;gBAAAyF,QAAA,EAAE1C,QAAQ,CAACE;cAAI;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CACnE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAIN1H,OAAA;cAAKwB,SAAS,EAAC,cAAc;cAAAyF,QAAA,gBAC3BjH,OAAA;gBAAIwB,SAAS,EAAC,gBAAgB;gBAAAyF,QAAA,EAAE1C,QAAQ,CAACC;cAAK;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpD1H,OAAA;gBAAKwB,SAAS,EAAC,eAAe;gBAAAyF,QAAA,gBAC5BjH,OAAA;kBAAMwB,SAAS,EAAC,kBAAkB;kBAAAyF,QAAA,EAAE1C,QAAQ,CAACd;gBAAO;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAC3DnD,QAAQ,CAAC/C,SAAS,iBACjBxB,OAAA;kBAAMwB,SAAS,EAAC,gBAAgB;kBAAAyF,QAAA,EAC7BpG,cAAc,KAAK,SAAS,GAAI,SAAQ0D,QAAQ,CAAC/C,SAAU,EAAC,GAAI,QAAO+C,QAAQ,CAAC/C,SAAU;gBAAC;kBAAA+F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN1H,OAAA;cAAKwB,SAAS,EAAC,cAAc;cAAAyF,QAAA,EAC1B1C,QAAQ,CAACnC,WAAW,gBACnBpC,OAAA,CAAAE,SAAA;gBAAA+G,QAAA,gBACEjH,OAAA;kBACEwB,SAAS,EAAC,sBAAsB;kBAChCwG,OAAO,EAAEA,CAAA,KAAMhB,qBAAqB,CAACzC,QAAQ,CAACnC,WAAW,CAAE;kBAAA6E,QAAA,gBAE3DjH,OAAA,CAAC9B,KAAK;oBAAAqJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,SACX;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT1H,OAAA;kBACEwB,SAAS,EAAC,oBAAoB;kBAC9BwG,OAAO,EAAEA,CAAA,KAAMjD,sBAAsB,CAACR,QAAQ,CAACnC,WAAW,CAAE;kBAAA6E,QAAA,gBAE5DjH,OAAA,CAAC/B,UAAU;oBAAAsJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,aAChB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA,eACT,CAAC,gBAEH1H,OAAA;gBAAMwB,SAAS,EAAC,aAAa;gBAAAyF,QAAA,EAAC;cAAa;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAClD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA,GAxDEe,KAAK;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyDV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,gBAEN1H,OAAA;UAAKwB,SAAS,EAAC,aAAa;UAAAyF,QAAA,gBAC1BjH,OAAA,CAAChC,eAAe;YAACwD,SAAS,EAAC;UAAY;YAAA+F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1C1H,OAAA;YAAAiH,QAAA,EAAI;UAAkB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3B1H,OAAA;YAAAiH,QAAA,EAAG;UAA4D;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACnE1H,OAAA;YAAGwB,SAAS,EAAC,YAAY;YAAAyF,QAAA,EAAC;UAA2C;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAMN1H,OAAA,CAACpC,QAAQ;QACPsE,WAAW,EAAEA,WAAY;QACzBwG,UAAU,EAAEA,CAAA,KAAM;UAChBvG,cAAc,CAAC,KAAK,CAAC;UACrBE,cAAc,CAAC,EAAE,CAAC;QACpB,CAAE;QACFD,WAAW,EAAEA;MAAY;QAAAmF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACtH,EAAA,CAxkBQD,aAAa;EAAA,QACH3C,WAAW,EACeG,WAAW,EACrCJ,WAAW;AAAA;AAAAoL,EAAA,GAHrBxI,aAAa;AA0kBtB,eAAeA,aAAa;AAAC,IAAAwI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}