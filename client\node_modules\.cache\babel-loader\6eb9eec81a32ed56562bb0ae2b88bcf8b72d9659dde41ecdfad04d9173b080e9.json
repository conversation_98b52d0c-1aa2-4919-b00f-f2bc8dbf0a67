{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport StyleContext from \"../StyleContext\";\nimport useCompatibleInsertionEffect from \"./useCompatibleInsertionEffect\";\nimport useEffectCleanupRegister from \"./useEffectCleanupRegister\";\nimport useHMR from \"./useHMR\";\nexport default function useGlobalCache(prefix, keyPath, cacheFn, onCacheRemove,\n// Add additional effect trigger by `useInsertionEffect`\nonCacheEffect) {\n  var _React$useContext = React.useContext(StyleContext),\n    globalCache = _React$useContext.cache;\n  var fullPath = [prefix].concat(_toConsumableArray(keyPath));\n  var deps = fullPath.join('_');\n  var register = useEffectCleanupRegister([deps]);\n  var HMRUpdate = useHMR();\n  var buildCache = function buildCache(updater) {\n    globalCache.update(fullPath, function (prevCache) {\n      var _ref = prevCache || [],\n        _ref2 = _slicedToArray(_ref, 2),\n        _ref2$ = _ref2[0],\n        times = _ref2$ === void 0 ? 0 : _ref2$,\n        cache = _ref2[1];\n\n      // HMR should always ignore cache since developer may change it\n      var tmpCache = cache;\n      if (process.env.NODE_ENV !== 'production' && cache && HMRUpdate) {\n        onCacheRemove === null || onCacheRemove === void 0 ? void 0 : onCacheRemove(tmpCache, HMRUpdate);\n        tmpCache = null;\n      }\n      var mergedCache = tmpCache || cacheFn();\n      var data = [times, mergedCache];\n\n      // Call updater if need additional logic\n      return updater ? updater(data) : data;\n    });\n  };\n\n  // Create cache\n  React.useMemo(function () {\n    buildCache();\n  }, /* eslint-disable react-hooks/exhaustive-deps */\n  [deps]\n  /* eslint-enable */);\n\n  var cacheEntity = globalCache.get(fullPath);\n\n  // HMR clean the cache but not trigger `useMemo` again\n  // Let's fallback of this\n  // ref https://github.com/ant-design/cssinjs/issues/127\n  if (process.env.NODE_ENV !== 'production' && !cacheEntity) {\n    buildCache();\n    cacheEntity = globalCache.get(fullPath);\n  }\n  var cacheContent = cacheEntity[1];\n\n  // Remove if no need anymore\n  useCompatibleInsertionEffect(function () {\n    onCacheEffect === null || onCacheEffect === void 0 ? void 0 : onCacheEffect(cacheContent);\n  }, function (polyfill) {\n    // It's bad to call build again in effect.\n    // But we have to do this since StrictMode will call effect twice\n    // which will clear cache on the first time.\n    buildCache(function (_ref3) {\n      var _ref4 = _slicedToArray(_ref3, 2),\n        times = _ref4[0],\n        cache = _ref4[1];\n      if (polyfill && times === 0) {\n        onCacheEffect === null || onCacheEffect === void 0 ? void 0 : onCacheEffect(cacheContent);\n      }\n      return [times + 1, cache];\n    });\n    return function () {\n      globalCache.update(fullPath, function (prevCache) {\n        var _ref5 = prevCache || [],\n          _ref6 = _slicedToArray(_ref5, 2),\n          _ref6$ = _ref6[0],\n          times = _ref6$ === void 0 ? 0 : _ref6$,\n          cache = _ref6[1];\n        var nextCount = times - 1;\n        if (nextCount === 0) {\n          // Always remove styles in useEffect callback\n          register(function () {\n            return onCacheRemove === null || onCacheRemove === void 0 ? void 0 : onCacheRemove(cache, false);\n          });\n          return null;\n        }\n        return [times - 1, cache];\n      });\n    };\n  }, [deps]);\n  return cacheContent;\n}", "map": {"version": 3, "names": ["_slicedToArray", "_toConsumableArray", "React", "StyleContext", "useCompatibleInsertionEffect", "useEffectCleanupRegister", "useHMR", "useGlobalCache", "prefix", "keyP<PERSON>", "cacheFn", "onCacheRemove", "onCacheEffect", "_React$useContext", "useContext", "globalCache", "cache", "fullPath", "concat", "deps", "join", "register", "HMRUpdate", "buildCache", "updater", "update", "prevCache", "_ref", "_ref2", "_ref2$", "times", "tmpCache", "process", "env", "NODE_ENV", "mergedCache", "data", "useMemo", "cacheEntity", "get", "cacheContent", "polyfill", "_ref3", "_ref4", "_ref5", "_ref6", "_ref6$", "nextCount"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/@ant-design/cssinjs/es/hooks/useGlobalCache.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport StyleContext from \"../StyleContext\";\nimport useCompatibleInsertionEffect from \"./useCompatibleInsertionEffect\";\nimport useEffectCleanupRegister from \"./useEffectCleanupRegister\";\nimport useHMR from \"./useHMR\";\nexport default function useGlobalCache(prefix, keyPath, cacheFn, onCacheRemove,\n// Add additional effect trigger by `useInsertionEffect`\nonCacheEffect) {\n  var _React$useContext = React.useContext(StyleContext),\n    globalCache = _React$useContext.cache;\n  var fullPath = [prefix].concat(_toConsumableArray(keyPath));\n  var deps = fullPath.join('_');\n  var register = useEffectCleanupRegister([deps]);\n  var HMRUpdate = useHMR();\n  var buildCache = function buildCache(updater) {\n    globalCache.update(fullPath, function (prevCache) {\n      var _ref = prevCache || [],\n        _ref2 = _slicedToArray(_ref, 2),\n        _ref2$ = _ref2[0],\n        times = _ref2$ === void 0 ? 0 : _ref2$,\n        cache = _ref2[1];\n\n      // HMR should always ignore cache since developer may change it\n      var tmpCache = cache;\n      if (process.env.NODE_ENV !== 'production' && cache && HMRUpdate) {\n        onCacheRemove === null || onCacheRemove === void 0 ? void 0 : onCacheRemove(tmpCache, HMRUpdate);\n        tmpCache = null;\n      }\n      var mergedCache = tmpCache || cacheFn();\n      var data = [times, mergedCache];\n\n      // Call updater if need additional logic\n      return updater ? updater(data) : data;\n    });\n  };\n\n  // Create cache\n  React.useMemo(function () {\n    buildCache();\n  }, /* eslint-disable react-hooks/exhaustive-deps */\n  [deps]\n  /* eslint-enable */);\n\n  var cacheEntity = globalCache.get(fullPath);\n\n  // HMR clean the cache but not trigger `useMemo` again\n  // Let's fallback of this\n  // ref https://github.com/ant-design/cssinjs/issues/127\n  if (process.env.NODE_ENV !== 'production' && !cacheEntity) {\n    buildCache();\n    cacheEntity = globalCache.get(fullPath);\n  }\n  var cacheContent = cacheEntity[1];\n\n  // Remove if no need anymore\n  useCompatibleInsertionEffect(function () {\n    onCacheEffect === null || onCacheEffect === void 0 ? void 0 : onCacheEffect(cacheContent);\n  }, function (polyfill) {\n    // It's bad to call build again in effect.\n    // But we have to do this since StrictMode will call effect twice\n    // which will clear cache on the first time.\n    buildCache(function (_ref3) {\n      var _ref4 = _slicedToArray(_ref3, 2),\n        times = _ref4[0],\n        cache = _ref4[1];\n      if (polyfill && times === 0) {\n        onCacheEffect === null || onCacheEffect === void 0 ? void 0 : onCacheEffect(cacheContent);\n      }\n      return [times + 1, cache];\n    });\n    return function () {\n      globalCache.update(fullPath, function (prevCache) {\n        var _ref5 = prevCache || [],\n          _ref6 = _slicedToArray(_ref5, 2),\n          _ref6$ = _ref6[0],\n          times = _ref6$ === void 0 ? 0 : _ref6$,\n          cache = _ref6[1];\n        var nextCount = times - 1;\n        if (nextCount === 0) {\n          // Always remove styles in useEffect callback\n          register(function () {\n            return onCacheRemove === null || onCacheRemove === void 0 ? void 0 : onCacheRemove(cache, false);\n          });\n          return null;\n        }\n        return [times - 1, cache];\n      });\n    };\n  }, [deps]);\n  return cacheContent;\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,YAAY,MAAM,iBAAiB;AAC1C,OAAOC,4BAA4B,MAAM,gCAAgC;AACzE,OAAOC,wBAAwB,MAAM,4BAA4B;AACjE,OAAOC,MAAM,MAAM,UAAU;AAC7B,eAAe,SAASC,cAAcA,CAACC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,aAAa;AAC9E;AACAC,aAAa,EAAE;EACb,IAAIC,iBAAiB,GAAGX,KAAK,CAACY,UAAU,CAACX,YAAY,CAAC;IACpDY,WAAW,GAAGF,iBAAiB,CAACG,KAAK;EACvC,IAAIC,QAAQ,GAAG,CAACT,MAAM,CAAC,CAACU,MAAM,CAACjB,kBAAkB,CAACQ,OAAO,CAAC,CAAC;EAC3D,IAAIU,IAAI,GAAGF,QAAQ,CAACG,IAAI,CAAC,GAAG,CAAC;EAC7B,IAAIC,QAAQ,GAAGhB,wBAAwB,CAAC,CAACc,IAAI,CAAC,CAAC;EAC/C,IAAIG,SAAS,GAAGhB,MAAM,CAAC,CAAC;EACxB,IAAIiB,UAAU,GAAG,SAASA,UAAUA,CAACC,OAAO,EAAE;IAC5CT,WAAW,CAACU,MAAM,CAACR,QAAQ,EAAE,UAAUS,SAAS,EAAE;MAChD,IAAIC,IAAI,GAAGD,SAAS,IAAI,EAAE;QACxBE,KAAK,GAAG5B,cAAc,CAAC2B,IAAI,EAAE,CAAC,CAAC;QAC/BE,MAAM,GAAGD,KAAK,CAAC,CAAC,CAAC;QACjBE,KAAK,GAAGD,MAAM,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,MAAM;QACtCb,KAAK,GAAGY,KAAK,CAAC,CAAC,CAAC;;MAElB;MACA,IAAIG,QAAQ,GAAGf,KAAK;MACpB,IAAIgB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIlB,KAAK,IAAIM,SAAS,EAAE;QAC/DX,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACoB,QAAQ,EAAET,SAAS,CAAC;QAChGS,QAAQ,GAAG,IAAI;MACjB;MACA,IAAII,WAAW,GAAGJ,QAAQ,IAAIrB,OAAO,CAAC,CAAC;MACvC,IAAI0B,IAAI,GAAG,CAACN,KAAK,EAAEK,WAAW,CAAC;;MAE/B;MACA,OAAOX,OAAO,GAAGA,OAAO,CAACY,IAAI,CAAC,GAAGA,IAAI;IACvC,CAAC,CAAC;EACJ,CAAC;;EAED;EACAlC,KAAK,CAACmC,OAAO,CAAC,YAAY;IACxBd,UAAU,CAAC,CAAC;EACd,CAAC,EAAE;EACH,CAACJ,IAAI;EACL,mBAAmB,CAAC;;EAEpB,IAAImB,WAAW,GAAGvB,WAAW,CAACwB,GAAG,CAACtB,QAAQ,CAAC;;EAE3C;EACA;EACA;EACA,IAAIe,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAACI,WAAW,EAAE;IACzDf,UAAU,CAAC,CAAC;IACZe,WAAW,GAAGvB,WAAW,CAACwB,GAAG,CAACtB,QAAQ,CAAC;EACzC;EACA,IAAIuB,YAAY,GAAGF,WAAW,CAAC,CAAC,CAAC;;EAEjC;EACAlC,4BAA4B,CAAC,YAAY;IACvCQ,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC4B,YAAY,CAAC;EAC3F,CAAC,EAAE,UAAUC,QAAQ,EAAE;IACrB;IACA;IACA;IACAlB,UAAU,CAAC,UAAUmB,KAAK,EAAE;MAC1B,IAAIC,KAAK,GAAG3C,cAAc,CAAC0C,KAAK,EAAE,CAAC,CAAC;QAClCZ,KAAK,GAAGa,KAAK,CAAC,CAAC,CAAC;QAChB3B,KAAK,GAAG2B,KAAK,CAAC,CAAC,CAAC;MAClB,IAAIF,QAAQ,IAAIX,KAAK,KAAK,CAAC,EAAE;QAC3BlB,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC4B,YAAY,CAAC;MAC3F;MACA,OAAO,CAACV,KAAK,GAAG,CAAC,EAAEd,KAAK,CAAC;IAC3B,CAAC,CAAC;IACF,OAAO,YAAY;MACjBD,WAAW,CAACU,MAAM,CAACR,QAAQ,EAAE,UAAUS,SAAS,EAAE;QAChD,IAAIkB,KAAK,GAAGlB,SAAS,IAAI,EAAE;UACzBmB,KAAK,GAAG7C,cAAc,CAAC4C,KAAK,EAAE,CAAC,CAAC;UAChCE,MAAM,GAAGD,KAAK,CAAC,CAAC,CAAC;UACjBf,KAAK,GAAGgB,MAAM,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,MAAM;UACtC9B,KAAK,GAAG6B,KAAK,CAAC,CAAC,CAAC;QAClB,IAAIE,SAAS,GAAGjB,KAAK,GAAG,CAAC;QACzB,IAAIiB,SAAS,KAAK,CAAC,EAAE;UACnB;UACA1B,QAAQ,CAAC,YAAY;YACnB,OAAOV,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACK,KAAK,EAAE,KAAK,CAAC;UAClG,CAAC,CAAC;UACF,OAAO,IAAI;QACb;QACA,OAAO,CAACc,KAAK,GAAG,CAAC,EAAEd,KAAK,CAAC;MAC3B,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,EAAE,CAACG,IAAI,CAAC,CAAC;EACV,OAAOqB,YAAY;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}