{"ast": null, "code": "import { lintWarning } from \"./utils\";\nvar linter = function linter(key, value, info) {\n  if (key === 'animation') {\n    if (info.hashId && value !== 'none') {\n      lintWarning(\"You seem to be using hashed animation '\".concat(value, \"', in which case 'animationName' with Keyframe as value is recommended.\"), info);\n    }\n  }\n};\nexport default linter;", "map": {"version": 3, "names": ["lintWarning", "linter", "key", "value", "info", "hashId", "concat"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/@ant-design/cssinjs/es/linters/hashedAnimationLinter.js"], "sourcesContent": ["import { lintWarning } from \"./utils\";\nvar linter = function linter(key, value, info) {\n  if (key === 'animation') {\n    if (info.hashId && value !== 'none') {\n      lintWarning(\"You seem to be using hashed animation '\".concat(value, \"', in which case 'animationName' with Keyframe as value is recommended.\"), info);\n    }\n  }\n};\nexport default linter;"], "mappings": "AAAA,SAASA,WAAW,QAAQ,SAAS;AACrC,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,GAAG,EAAEC,KAAK,EAAEC,IAAI,EAAE;EAC7C,IAAIF,GAAG,KAAK,WAAW,EAAE;IACvB,IAAIE,IAAI,CAACC,MAAM,IAAIF,KAAK,KAAK,MAAM,EAAE;MACnCH,WAAW,CAAC,yCAAyC,CAACM,MAAM,CAACH,KAAK,EAAE,yEAAyE,CAAC,EAAEC,IAAI,CAAC;IACvJ;EACF;AACF,CAAC;AACD,eAAeH,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}