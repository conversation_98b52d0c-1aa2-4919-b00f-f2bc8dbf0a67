{"ast": null, "code": "import * as React from 'react';\nexport default function useRefs() {\n  var nodeRef = React.useRef({});\n  function getRef(index) {\n    return nodeRef.current[index];\n  }\n  function setRef(index) {\n    return function (node) {\n      nodeRef.current[index] = node;\n    };\n  }\n  return [getRef, setRef];\n}", "map": {"version": 3, "names": ["React", "useRefs", "nodeRef", "useRef", "getRef", "index", "current", "setRef", "node"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-rate/es/useRefs.js"], "sourcesContent": ["import * as React from 'react';\nexport default function useRefs() {\n  var nodeRef = React.useRef({});\n  function getRef(index) {\n    return nodeRef.current[index];\n  }\n  function setRef(index) {\n    return function (node) {\n      nodeRef.current[index] = node;\n    };\n  }\n  return [getRef, setRef];\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,OAAOA,CAAA,EAAG;EAChC,IAAIC,OAAO,GAAGF,KAAK,CAACG,MAAM,CAAC,CAAC,CAAC,CAAC;EAC9B,SAASC,MAAMA,CAACC,KAAK,EAAE;IACrB,OAAOH,OAAO,CAACI,OAAO,CAACD,KAAK,CAAC;EAC/B;EACA,SAASE,MAAMA,CAACF,KAAK,EAAE;IACrB,OAAO,UAAUG,IAAI,EAAE;MACrBN,OAAO,CAACI,OAAO,CAACD,KAAK,CAAC,GAAGG,IAAI;IAC/B,CAAC;EACH;EACA,OAAO,CAACJ,MAAM,EAAEG,MAAM,CAAC;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}