import React, { useState, useRef, useEffect } from 'react';
import { TbRobot } from 'react-icons/tb';
import { chatWithChatGPT, uploadImg } from '../apicalls/chat';
import { useLanguage } from '../contexts/LanguageContext';
import { useSelector } from 'react-redux';
import ContentRenderer from './ContentRenderer';

const FloatingBrainwaveAI = ({ pdfContext = null, selectedText = null }) => {
  const { user } = useSelector((state) => state.user);
  const { t, isKiswahili } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [isMaximized, setIsMaximized] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);
  const [isTablet, setIsTablet] = useState(window.innerWidth > 768 && window.innerWidth <= 1024);

  // Handle responsive design
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
      setIsTablet(window.innerWidth > 768 && window.innerWidth <= 1024);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Clear chat when user logs out (listen for storage changes)
  useEffect(() => {
    const handleStorageChange = (e) => {
      if (e.key === 'token' && !e.newValue) {
        // Token was removed, user logged out
        clearChat();
        setIsOpen(false);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  // Clear chat function
  const clearChat = () => {
    const welcomeMessage = isKiswahili
      ? 'Hujambo! Mimi ni Brainwave AI, msaidizi wako wa masomo. Ninawezaje kukusaidia leo?'
      : 'Hi! I am Brainwave AI, your study assistant. How can I help you today?';

    setMessages([
      { role: 'assistant', content: welcomeMessage }
    ]);
    setInput('');
    removeImage();
  };

  // Clear chat when closing
  const handleClose = () => {
    clearChat();
    setIsOpen(false);
    setIsMinimized(false);
    setIsMaximized(false);
  };
  // Generate context-aware initial message
  const getInitialMessage = () => {
    if (pdfContext) {
      return isKiswahili
        ? `Hujambo! Mimi ni Brainwave AI. Nina ufikiaji wa maudhui ya "${pdfContext.title}" na ninaweza kukusaidia na:\n\n📄 Muhtasari wa PDF\n💡 Kueleza maudhui\n❓ Kujibu maswali\n🔍 Kufafanua vipengele\n\n💡 KIDOKEZO: Andika tu namba ya swali (kama "1", "2a", "Q3") na nitakupa jibu moja kwa moja!`
        : `Hi! I'm Brainwave AI. I have access to the content of "${pdfContext.title}" and can help you with:\n\n📄 PDF summaries\n💡 Content explanations\n❓ Answering questions\n🔍 Clarifying concepts\n\n💡 TIP: Just type a question number (like "1", "2a", "Q3") and I'll give you the answer directly!`;
    }
    return isKiswahili
      ? 'Hujambo! Mimi ni Brainwave AI, msaidizi wako wa masomo. Ninawezaje kukusaidia leo?'
      : 'Hi! I am Brainwave AI, your study assistant. How can I help you today?';
  };

  const [messages, setMessages] = useState([
    { role: 'assistant', content: getInitialMessage() }
  ]);
  const [input, setInput] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);

  const messagesEndRef = useRef(null);
  const fileInputRef = useRef(null);
  const inputRef = useRef(null);

  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  const handleImageSelect = (event) => {
    const file = event.target.files[0];
    if (file && file.type.startsWith('image/')) {
      setSelectedImage(file);
      const reader = new FileReader();
      reader.onload = (e) => setImagePreview(e.target.result);
      reader.readAsDataURL(file);
    }
  };

  const removeImage = () => {
    setSelectedImage(null);
    setImagePreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };





  // Auto-open when PDF context is provided
  React.useEffect(() => {
    if (pdfContext && !isOpen) {
      setIsOpen(true);
    }
  }, [pdfContext]);

  // Update initial message when PDF context changes
  React.useEffect(() => {
    if (pdfContext) {
      const newInitialMessage = getInitialMessage();
      setMessages([{ role: 'assistant', content: newInitialMessage }]);
    }
  }, [pdfContext, isKiswahili]);

  // Listen for auto-fill input events
  React.useEffect(() => {
    const handleAutoFillInput = (event) => {
      if (event.detail && event.detail.text) {
        setInput(event.detail.text);
        if (inputRef.current) {
          inputRef.current.focus();
        }
      }
    };

    window.addEventListener('autoFillAIInput', handleAutoFillInput);
    return () => {
      window.removeEventListener('autoFillAIInput', handleAutoFillInput);
    };
  }, []);

  // Smart question detection for PDF context
  const enhanceQuestionInput = (userInput) => {
    if (!pdfContext) return userInput;

    // Check if input looks like a question number
    const questionPatterns = [
      /^(\d+)$/,                    // Just "1", "2", etc.
      /^(\d+[a-z])$/i,             // "1a", "2b", etc.
      /^q\s*(\d+[a-z]?)$/i,        // "Q1", "q 2", "Q1a", etc.
      /^question\s*(\d+[a-z]?)$/i, // "Question 1", "Question 2a", etc.
      /^(\d+)\.$/,                 // "1.", "2.", etc.
      /^(\d+[a-z]?)\.$/i,          // "1a.", "2b.", etc.
      /^swali\s*(\d+[a-z]?)$/i,    // "Swali 1", "Swali 2a", etc. (Kiswahili)
    ];

    for (const pattern of questionPatterns) {
      const match = userInput.match(pattern);
      if (match) {
        const questionNum = match[1];
        return isKiswahili
          ? `Katika PDF hii, tafuta na ujibu swali namba ${questionNum}. Soma maudhui ya PDF na utoe jibu kamili.`
          : `In this PDF, find and answer question number ${questionNum}. Read the PDF content and provide a complete answer.`;
      }
    }

    return userInput;
  };

  const sendMessage = async () => {
    if (!input.trim() && !selectedImage) return;

    const originalInput = input.trim();
    const enhancedInput = enhanceQuestionInput(originalInput);
    const userMessage = enhancedInput;
    const imageFile = selectedImage;

    setInput('');
    removeImage();
    setIsTyping(true);

    try {
      let imageUrl = null;

      if (imageFile) {
        const formData = new FormData();
        formData.append("image", imageFile);
        const uploadResult = await uploadImg(formData);

        if (uploadResult?.success) {
          imageUrl = uploadResult.url;
        }
      }

      const newUserMessage = imageUrl
        ? {
            role: "user",
            content: [
              { type: "text", text: userMessage },
              { type: "image_url", image_url: { url: imageUrl } }
            ]
          }
        : { role: "user", content: userMessage };

      setMessages(prev => [...prev, newUserMessage]);

      // Add language preference and PDF context for enhanced responses
      let systemPrompt = '';

      if (pdfContext) {
        systemPrompt = isKiswahili
          ? `Wewe ni msaidizi wa masomo wa Tanzania. Mtumiaji anaangalia PDF iitwayo "${pdfContext.title}".

MAUDHUI YA PDF YALIYOPO:
${pdfContext.extractedText || 'Hakuna maudhui yaliyopatikana'}

MUHIMU:
- Una ufikiaji kamili wa maudhui ya PDF hapo juu
- Wakati mtumiaji anaandika namba ya swali (kama "1", "2a", "Swali 3", nk), tafuta swali hilo katika maudhui ya PDF hapo juu na ujibu moja kwa moja
- USIMWAMBIE mtumiaji abandike swali - wewe unaweza kusoma maudhui ya PDF
- Kama anaandika "1" au "Q1", tafuta "Question 1" au "1." katika maudhui na ujibu
- Toa majibu kamili kulingana na maudhui ya PDF yaliyopo hapo juu
- Tumia lugha ya Kiswahili rahisi na ya kielimu.`
          : `You are an educational assistant. The user is viewing a PDF titled "${pdfContext.title}".

PDF CONTENT AVAILABLE:
${pdfContext.extractedText || 'No content extracted'}

IMPORTANT:
- You have full access to the PDF content shown above
- When user types a question number (like "1", "2a", "Question 3", etc.), find that question in the PDF content above and answer it directly
- DO NOT say you cannot access the PDF - you can see the content above
- If they type "1" or "Q1", look for "Question 1" or "1." in the content and provide the answer
- Give complete answers based on the PDF content shown above
- Be helpful and educational.`;
      } else {
        systemPrompt = isKiswahili
          ? 'Jibu kwa lugha ya Kiswahili tu. Wewe ni msaidizi wa masomo wa Tanzania. Tumia lugha rahisi na ya kielimu.'
          : 'You are an educational assistant. Be helpful and provide clear explanations.';
      }

      const chatPayload = {
        messages: [...messages, newUserMessage],
        ...(isKiswahili && { language: 'kiswahili' }),
        systemPrompt: systemPrompt,
        ...(pdfContext && {
          pdfContext: {
            title: pdfContext.title,
            subject: pdfContext.subject,
            type: 'pdf_document',
            content: pdfContext.extractedText || '',
            hasContent: true
          }
        }),
        ...(selectedText && {
          selectedText: selectedText,
          contextType: 'text_selection'
        })
      };

      // Debug logging
      if (pdfContext) {
        console.log('Sending PDF context to AI:', {
          title: pdfContext.title,
          contentLength: pdfContext.extractedText?.length || 0,
          contentPreview: pdfContext.extractedText?.substring(0, 200) + '...'
        });
      }

      const response = await chatWithChatGPT(chatPayload);

      if (response?.success && response?.data) {
        setMessages(prev => [...prev, {
          role: 'assistant',
          content: response.data
        }]);
      } else {
        const errorMessage = isKiswahili
          ? "Samahani, sikuweza kuchakata ombi lako sasa hivi. Tafadhali jaribu tena baada ya muda."
          : "I'm sorry, I couldn't process your request right now. Please try again in a moment.";

        setMessages(prev => [...prev, {
          role: 'assistant',
          content: errorMessage
        }]);
      }
    } catch (error) {
      const errorMessage = isKiswahili
        ? "Nina matatizo ya kiufundi. Tafadhali jaribu tena baadaye."
        : "I'm experiencing some technical difficulties. Please try again later.";

      setMessages(prev => [...prev, {
        role: 'assistant',
        content: errorMessage
      }]);
    } finally {
      setIsTyping(false);
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  if (!isOpen) {
    return (
      <div
        onClick={() => setIsOpen(true)}
        style={{
          position: 'fixed',
          bottom: isMobile ? '15px' : '20px',
          right: isMobile ? '15px' : '20px',
          width: isMobile ? '50px' : '60px',
          height: isMobile ? '50px' : '60px',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer',
          boxShadow: '0 8px 32px rgba(102, 126, 234, 0.4)',
          zIndex: 1000,
          transition: 'all 0.3s ease',
          animation: 'pulse 2s infinite'
        }}
      >
        <TbRobot style={{ color: 'white', fontSize: isMobile ? '24px' : '28px' }} />
        <style>{`
          @keyframes pulse {
            0% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4); }
            50% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.8); }
            100% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4); }
          }
        `}</style>
      </div>
    );
  }

  // Get responsive dimensions
  const getResponsiveDimensions = () => {
    if (isMobile) {
      return {
        width: isMinimized ? '280px' : isMaximized ? 'calc(100vw - 10px)' : '320px',
        height: isMinimized ? '50px' : isMaximized ? 'calc(100vh - 10px)' : '450px',
        bottom: isMaximized ? '5px' : '15px',
        right: isMaximized ? '5px' : '15px',
        top: isMaximized ? '5px' : 'auto',
        left: isMaximized ? '5px' : 'auto'
      };
    } else if (isTablet) {
      return {
        width: isMinimized ? '320px' : isMaximized ? 'calc(100vw - 15px)' : '360px',
        height: isMinimized ? '55px' : isMaximized ? 'calc(100vh - 15px)' : '480px',
        bottom: isMaximized ? '7px' : '18px',
        right: isMaximized ? '7px' : '18px',
        top: isMaximized ? '7px' : 'auto',
        left: isMaximized ? '7px' : 'auto'
      };
    } else {
      return {
        width: isMinimized ? '300px' : isMaximized ? 'calc(100vw - 20px)' : '380px',
        height: isMinimized ? '60px' : isMaximized ? 'calc(100vh - 20px)' : '500px',
        bottom: isMaximized ? '10px' : '20px',
        right: isMaximized ? '10px' : '20px',
        top: isMaximized ? '10px' : 'auto',
        left: isMaximized ? '10px' : 'auto'
      };
    }
  };

  const dimensions = getResponsiveDimensions();

  return (
    <div
      style={{
        position: 'fixed',
        ...dimensions,
        background: 'rgba(255, 255, 255, 0.95)',
        backdropFilter: 'blur(20px)',
        borderRadius: isMaximized ? '15px' : isMobile ? '15px' : '20px',
        boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        zIndex: 1000,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
        transition: 'all 0.3s ease'
      }}
      onWheel={(e) => {
        // Only prevent background scrolling when AI chat is open and not minimized
        if (isOpen && !isMinimized) {
          e.stopPropagation();
        }
      }}
      onTouchMove={(e) => {
        // Only prevent background scrolling when AI chat is open and not minimized
        if (isOpen && !isMinimized) {
          e.stopPropagation();
        }
      }}
    >
      <div
        style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          padding: isMobile ? '12px 16px' : '16px 20px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          borderRadius: isMaximized ? '15px 15px 0 0' : isMobile ? '15px 15px 0 0' : '20px 20px 0 0',
          minHeight: isMinimized ? (isMobile ? '50px' : '60px') : 'auto'
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: isMobile ? '8px' : '12px' }}>
          <div style={{
            width: isMobile ? '28px' : '32px',
            height: isMobile ? '28px' : '32px',
            background: 'rgba(255, 255, 255, 0.2)',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <TbRobot style={{ color: 'white', fontSize: isMobile ? '16px' : '18px' }} />
          </div>
          {!isMinimized && (
            <div>
              <h3 style={{ margin: 0, color: 'white', fontSize: isMobile ? '14px' : '16px', fontWeight: '600' }}>
                {isKiswahili ? 'Akili ya Brainwave' : 'Brainwave AI'}
              </h3>
              <p style={{ margin: 0, color: 'rgba(255, 255, 255, 0.8)', fontSize: isMobile ? '10px' : '12px' }}>
                {isKiswahili ? 'Daima hapa kukusaidia' : 'Always here to help'}
              </p>
            </div>
          )}
          {isMinimized && (
            <div>
              <h3 style={{ margin: 0, color: 'white', fontSize: isMobile ? '12px' : '14px', fontWeight: '600' }}>Brainwave AI</h3>
            </div>
          )}
        </div>

        <div style={{ display: 'flex', gap: isMobile ? '4px' : '8px' }}>
          <button
            onClick={() => setIsMinimized(!isMinimized)}
            style={{
              background: isMinimized ? 'rgba(255, 255, 255, 0.4)' : 'rgba(255, 255, 255, 0.25)',
              border: 'none',
              borderRadius: isMobile ? '6px' : '8px',
              width: isMobile ? '28px' : '32px',
              height: isMobile ? '28px' : '32px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              transition: 'all 0.2s ease',
              backdropFilter: 'blur(10px)',
              boxShadow: isMinimized ? '0 2px 8px rgba(255, 255, 255, 0.3)' : 'none'
            }}
          >
            <span style={{ color: 'white', fontSize: isMobile ? '14px' : '16px', fontWeight: 'bold', textShadow: '0 1px 2px rgba(0,0,0,0.5)' }}>
              {isMinimized ? '⬆' : '➖'}
            </span>
          </button>

          {!isMinimized && (
            <button
              onClick={() => { setIsMaximized(!isMaximized); if (isMinimized) setIsMinimized(false); }}
              style={{
                background: isMaximized ? 'rgba(255, 255, 255, 0.4)' : 'rgba(255, 255, 255, 0.25)',
                border: 'none',
                borderRadius: isMobile ? '6px' : '8px',
                width: isMobile ? '28px' : '32px',
                height: isMobile ? '28px' : '32px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                backdropFilter: 'blur(10px)',
                boxShadow: isMaximized ? '0 2px 8px rgba(255, 255, 255, 0.3)' : 'none'
              }}
            >
              <span style={{ color: 'white', fontSize: isMobile ? '14px' : '16px', fontWeight: 'bold', textShadow: '0 1px 2px rgba(0,0,0,0.5)' }}>
                {isMaximized ? '⬇' : '⬆'}
              </span>
            </button>
          )}

          <button
            onClick={handleClose}
            style={{
              background: 'rgba(255, 255, 255, 0.25)',
              border: 'none',
              borderRadius: isMobile ? '6px' : '8px',
              width: isMobile ? '28px' : '32px',
              height: isMobile ? '28px' : '32px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              transition: 'all 0.2s ease',
              backdropFilter: 'blur(10px)'
            }}
            onMouseEnter={(e) => {
              // Use CSS classes instead of direct style manipulation
              if (e.target) {
                e.target.classList.add('hover-close-button');
              }
            }}
            onMouseLeave={(e) => {
              // Use CSS classes instead of direct style manipulation
              if (e.target) {
                e.target.classList.remove('hover-close-button');
              }
            }}
          >
            <span style={{ color: 'white', fontSize: isMobile ? '14px' : '16px', fontWeight: 'bold', textShadow: '0 1px 2px rgba(0,0,0,0.5)' }}>✕</span>
          </button>
        </div>
      </div>

      {!isMinimized && (
        <>
          <div
            style={{
              flex: 1,
              padding: '20px 20px 0 20px',
              overflowY: 'auto',
              display: 'flex',
              flexDirection: 'column',
              gap: '16px',
              scrollBehavior: 'smooth',
              scrollbarWidth: 'thin',
              scrollbarColor: '#cbd5e1 transparent'
            }}
            onWheel={(e) => {
              const element = e.currentTarget;
              const { scrollTop, scrollHeight, clientHeight } = element;

              // Allow scrolling within the messages container
              if (scrollTop > 0 && scrollTop + clientHeight < scrollHeight) {
                // We're in the middle, allow normal scrolling
                return;
              }

              // At the boundaries, prevent propagation to background
              if ((scrollTop === 0 && e.deltaY < 0) ||
                  (scrollTop + clientHeight >= scrollHeight && e.deltaY > 0)) {
                e.preventDefault();
                e.stopPropagation();
              }
            }}
            className="custom-scrollbar"
          >
            {messages.map((msg, index) => (
              <div key={index} style={{ display: 'flex', justifyContent: msg.role === 'user' ? 'flex-end' : 'flex-start' }}>
                <div style={{ maxWidth: '85%', padding: '12px 16px', borderRadius: msg.role === 'user' ? '18px 18px 4px 18px' : '18px 18px 18px 4px', background: msg.role === 'user' ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : '#f8fafc', color: msg.role === 'user' ? 'white' : '#334155', fontSize: '14px', lineHeight: '1.5', boxShadow: msg.role === 'user' ? '0 4px 12px rgba(102, 126, 234, 0.3)' : '0 2px 8px rgba(0, 0, 0, 0.1)', wordWrap: 'break-word' }}>
                  {typeof msg.content === 'string' ? (
                    msg.role === 'assistant' ? (
                      <ContentRenderer text={msg.content} />
                    ) : (
                      <div style={{ whiteSpace: 'pre-wrap' }}>{msg.content}</div>
                    )
                  ) : Array.isArray(msg.content) ? (
                    msg.content.map((item, idx) => (
                      <div key={idx}>
                        {item.type === 'text' && (
                          <div style={{ marginBottom: item.text ? '8px' : '0' }}>
                            {msg.role === 'assistant' ? (
                              <ContentRenderer text={item.text} />
                            ) : (
                              <div style={{ whiteSpace: 'pre-wrap' }}>{item.text}</div>
                            )}
                          </div>
                        )}
                        {item.type === 'image_url' && <img src={item.image_url.url} alt="User upload" style={{ maxWidth: '100%', height: 'auto', borderRadius: '12px', maxHeight: '200px', objectFit: 'cover', border: '2px solid rgba(255, 255, 255, 0.2)', marginTop: '4px' }} />}
                      </div>
                    ))
                  ) : (
                    <div>Invalid message format</div>
                  )}
                </div>
              </div>
            ))}

            {isTyping && (
              <div style={{ display: 'flex', justifyContent: 'flex-start' }}>
                <div style={{ padding: '12px 16px', borderRadius: '18px 18px 18px 4px', background: '#f8fafc', boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)' }}>
                  <div style={{ display: 'flex', gap: '4px', alignItems: 'center' }}>
                    <div style={{ width: '6px', height: '6px', borderRadius: '50%', background: '#94a3b8', animation: 'bounce 1.4s infinite ease-in-out' }} />
                    <div style={{ width: '6px', height: '6px', borderRadius: '50%', background: '#94a3b8', animation: 'bounce 1.4s infinite ease-in-out 0.16s' }} />
                    <div style={{ width: '6px', height: '6px', borderRadius: '50%', background: '#94a3b8', animation: 'bounce 1.4s infinite ease-in-out 0.32s' }} />
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>

          <div style={{ position: 'sticky', bottom: 0, background: 'rgba(255, 255, 255, 0.98)', backdropFilter: 'blur(20px)', borderTop: '1px solid rgba(0, 0, 0, 0.08)', padding: isMobile ? '12px 16px 16px' : '16px 20px 20px', zIndex: 10 }}>
            {imagePreview && (
              <div style={{ marginBottom: '12px', padding: '12px', background: '#f1f5f9', borderRadius: '12px', border: '1px solid #e2e8f0' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <div style={{ position: 'relative' }}>
                    <img src={imagePreview} alt="Preview" style={{ width: '60px', height: '60px', objectFit: 'cover', borderRadius: '8px', border: '2px solid #e2e8f0' }} />
                    <button onClick={removeImage} style={{ position: 'absolute', top: '-6px', right: '-6px', width: '20px', height: '20px', background: '#ef4444', color: 'white', borderRadius: '50%', border: 'none', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: '12px', fontWeight: 'bold' }}>×</button>
                  </div>
                  <div style={{ flex: 1 }}>
                    <p style={{ fontSize: '13px', fontWeight: '600', color: '#374151', margin: 0 }}>Image attached</p>
                    <p style={{ fontSize: '11px', color: '#6b7280', margin: 0 }}>{selectedImage?.name}</p>
                  </div>
                </div>
              </div>
            )}

            <div style={{ display: 'flex', gap: isMobile ? '6px' : '8px', background: '#f8fafc', borderRadius: isMobile ? '12px' : '16px', padding: isMobile ? '6px' : '8px', border: '2px solid #e2e8f0' }}>
              <button
                onClick={() => fileInputRef.current?.click()}
                style={{
                  background: '#3b82f6',
                  border: 'none',
                  borderRadius: '6px',
                  width: '24px',
                  height: '24px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  boxShadow: '0 1px 3px rgba(59, 130, 246, 0.3)'
                }}
                title="Attach image"
              >
                <span style={{ color: 'white', fontSize: '14px', fontWeight: 'bold' }}>+</span>
              </button>

              <textarea
                ref={inputRef}
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder={isKiswahili ? "Uliza chochote..." : "Ask me anything..."}
                rows={1}
                style={{
                  flex: 1,
                  border: 'none',
                  background: 'transparent',
                  outline: 'none',
                  fontSize: isMobile ? '12px' : '14px',
                  color: '#334155',
                  padding: isMobile ? '10px 12px' : '12px 16px',
                  fontFamily: 'inherit'
                }}
              />

              {/* PDF Quick Actions - ChatGPT style small buttons */}
              {pdfContext && (
                <>
                  <button
                    onClick={() => setInput(isKiswahili ? "Nipe muhtasari wa PDF hii" : "Give me a summary of this PDF")}
                    style={{
                      background: '#8b5cf6',
                      border: 'none',
                      borderRadius: '6px',
                      width: '24px',
                      height: '24px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      boxShadow: '0 1px 3px rgba(139, 92, 246, 0.3)'
                    }}
                    title={isKiswahili ? "Pata muhtasari wa PDF" : "Get PDF summary"}
                  >
                    <span style={{ color: 'white', fontSize: '12px' }}>📄</span>
                  </button>

                  <button
                    onClick={() => setInput(isKiswahili ? "Nieleze maudhui muhimu ya PDF hii" : "Explain the key points of this PDF")}
                    style={{
                      background: '#f59e0b',
                      border: 'none',
                      borderRadius: '6px',
                      width: '24px',
                      height: '24px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      boxShadow: '0 1px 3px rgba(245, 158, 11, 0.3)'
                    }}
                    title={isKiswahili ? "Eleza mambo muhimu" : "Explain key points"}
                  >
                    <span style={{ color: 'white', fontSize: '12px' }}>💡</span>
                  </button>
                </>
              )}

              {/* Selected Text Quick Action */}
              {selectedText && (
                <button
                  onClick={() => setInput(isKiswahili ? `Nieleze kuhusu: "${selectedText}"` : `Explain about: "${selectedText}"`)}
                  style={{
                    background: '#ef4444',
                    border: 'none',
                    borderRadius: '6px',
                    width: '24px',
                    height: '24px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease',
                    boxShadow: '0 1px 3px rgba(239, 68, 68, 0.3)'
                  }}
                  title={isKiswahili ? "Eleza maandishi yaliyochaguliwa" : "Explain selected text"}
                >
                  <span style={{ color: 'white', fontSize: '12px' }}>🔍</span>
                </button>
              )}



              <button
                onClick={sendMessage}
                disabled={!input.trim() && !selectedImage}
                style={{
                  background: (input.trim() || selectedImage) ? '#3b82f6' : '#e2e8f0',
                  border: 'none',
                  borderRadius: '6px',
                  width: '24px',
                  height: '24px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  cursor: (input.trim() || selectedImage) ? 'pointer' : 'not-allowed',
                  transition: 'all 0.2s ease',
                  boxShadow: (input.trim() || selectedImage) ? '0 1px 3px rgba(59, 130, 246, 0.3)' : 'none'
                }}
              >
                <span style={{ color: (input.trim() || selectedImage) ? 'white' : '#94a3b8', fontSize: '14px', fontWeight: 'bold' }}>➤</span>
              </button>
            </div>

            <input ref={fileInputRef} type="file" accept="image/*" onChange={handleImageSelect} style={{ display: 'none' }} />

            <p style={{ fontSize: isMobile ? '9px' : '11px', color: '#94a3b8', textAlign: 'center', margin: isMobile ? '6px 0 0 0' : '8px 0 0 0' }}>
              {isMobile ? 'Enter to send • Attach images' : 'Press Enter to send • Attach images for analysis'}
            </p>
          </div>
        </>
      )}

      <style>{`
        @keyframes bounce {
          0%, 80%, 100% { transform: scale(0); }
          40% { transform: scale(1); }
        }

        .custom-scrollbar::-webkit-scrollbar {
          width: 6px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
          background: transparent;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: #cbd5e1;
          border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: #94a3b8;
        }

        .hover-close-button {
          background: rgba(255, 60, 60, 0.4) !important;
        }
      `}</style>
    </div>
  );
};

export default FloatingBrainwaveAI;