{"ast": null, "code": "import * as React from 'react';\nexport default function useLazyKVMap(data, childrenColumnName, getRowKey) {\n  const mapCacheRef = React.useRef({});\n  function getRecordByKey(key) {\n    if (!mapCacheRef.current || mapCacheRef.current.data !== data || mapCacheRef.current.childrenColumnName !== childrenColumnName || mapCacheRef.current.getRowKey !== getRowKey) {\n      const kvMap = new Map();\n      /* eslint-disable no-inner-declarations */\n      function dig(records) {\n        records.forEach((record, index) => {\n          const rowKey = getRowKey(record, index);\n          kvMap.set(rowKey, record);\n          if (record && typeof record === 'object' && childrenColumnName in record) {\n            dig(record[childrenColumnName] || []);\n          }\n        });\n      }\n      /* eslint-enable */\n      dig(data);\n      mapCacheRef.current = {\n        data,\n        childrenColumnName,\n        kvMap,\n        getRowKey\n      };\n    }\n    return mapCacheRef.current.kvMap.get(key);\n  }\n  return [getRecordByKey];\n}", "map": {"version": 3, "names": ["React", "useLazyKVMap", "data", "childrenColumnName", "getRowKey", "mapCacheRef", "useRef", "getRecordByKey", "key", "current", "kvMap", "Map", "dig", "records", "for<PERSON>ach", "record", "index", "<PERSON><PERSON><PERSON>", "set", "get"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/table/hooks/useLazyKVMap.js"], "sourcesContent": ["import * as React from 'react';\nexport default function useLazyKVMap(data, childrenColumnName, getRowKey) {\n  const mapCacheRef = React.useRef({});\n  function getRecordByKey(key) {\n    if (!mapCacheRef.current || mapCacheRef.current.data !== data || mapCacheRef.current.childrenColumnName !== childrenColumnName || mapCacheRef.current.getRowKey !== getRowKey) {\n      const kvMap = new Map();\n      /* eslint-disable no-inner-declarations */\n      function dig(records) {\n        records.forEach((record, index) => {\n          const rowKey = getRowKey(record, index);\n          kvMap.set(rowKey, record);\n          if (record && typeof record === 'object' && childrenColumnName in record) {\n            dig(record[childrenColumnName] || []);\n          }\n        });\n      }\n      /* eslint-enable */\n      dig(data);\n      mapCacheRef.current = {\n        data,\n        childrenColumnName,\n        kvMap,\n        getRowKey\n      };\n    }\n    return mapCacheRef.current.kvMap.get(key);\n  }\n  return [getRecordByKey];\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,YAAYA,CAACC,IAAI,EAAEC,kBAAkB,EAAEC,SAAS,EAAE;EACxE,MAAMC,WAAW,GAAGL,KAAK,CAACM,MAAM,CAAC,CAAC,CAAC,CAAC;EACpC,SAASC,cAAcA,CAACC,GAAG,EAAE;IAC3B,IAAI,CAACH,WAAW,CAACI,OAAO,IAAIJ,WAAW,CAACI,OAAO,CAACP,IAAI,KAAKA,IAAI,IAAIG,WAAW,CAACI,OAAO,CAACN,kBAAkB,KAAKA,kBAAkB,IAAIE,WAAW,CAACI,OAAO,CAACL,SAAS,KAAKA,SAAS,EAAE;MAC7K,MAAMM,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;MACvB;MACA,SAASC,GAAGA,CAACC,OAAO,EAAE;QACpBA,OAAO,CAACC,OAAO,CAAC,CAACC,MAAM,EAAEC,KAAK,KAAK;UACjC,MAAMC,MAAM,GAAGb,SAAS,CAACW,MAAM,EAAEC,KAAK,CAAC;UACvCN,KAAK,CAACQ,GAAG,CAACD,MAAM,EAAEF,MAAM,CAAC;UACzB,IAAIA,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAIZ,kBAAkB,IAAIY,MAAM,EAAE;YACxEH,GAAG,CAACG,MAAM,CAACZ,kBAAkB,CAAC,IAAI,EAAE,CAAC;UACvC;QACF,CAAC,CAAC;MACJ;MACA;MACAS,GAAG,CAACV,IAAI,CAAC;MACTG,WAAW,CAACI,OAAO,GAAG;QACpBP,IAAI;QACJC,kBAAkB;QAClBO,KAAK;QACLN;MACF,CAAC;IACH;IACA,OAAOC,WAAW,CAACI,OAAO,CAACC,KAAK,CAACS,GAAG,CAACX,GAAG,CAAC;EAC3C;EACA,OAAO,CAACD,cAAc,CAAC;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}