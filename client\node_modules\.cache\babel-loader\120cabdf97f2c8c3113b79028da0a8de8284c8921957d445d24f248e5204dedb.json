{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.canUseDOM = exports.SafeNodeList = exports.SafeHTMLCollection = undefined;\nvar _exenv = require(\"exenv\");\nvar _exenv2 = _interopRequireDefault(_exenv);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar EE = _exenv2.default;\nvar SafeHTMLElement = EE.canUseDOM ? window.HTMLElement : {};\nvar SafeHTMLCollection = exports.SafeHTMLCollection = EE.canUseDOM ? window.HTMLCollection : {};\nvar SafeNodeList = exports.SafeNodeList = EE.canUseDOM ? window.NodeList : {};\nvar canUseDOM = exports.canUseDOM = EE.canUseDOM;\nexports.default = SafeHTMLElement;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "canUseDOM", "SafeNodeList", "SafeHTMLCollection", "undefined", "_exenv", "require", "_exenv2", "_interopRequireDefault", "obj", "__esModule", "default", "EE", "SafeHTMLElement", "window", "HTMLElement", "HTMLCollection", "NodeList"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/react-modal/lib/helpers/safeHTMLElement.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.canUseDOM = exports.SafeNodeList = exports.SafeHTMLCollection = undefined;\n\nvar _exenv = require(\"exenv\");\n\nvar _exenv2 = _interopRequireDefault(_exenv);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar EE = _exenv2.default;\n\nvar SafeHTMLElement = EE.canUseDOM ? window.HTMLElement : {};\n\nvar SafeHTMLCollection = exports.SafeHTMLCollection = EE.canUseDOM ? window.HTMLCollection : {};\n\nvar SafeNodeList = exports.SafeNodeList = EE.canUseDOM ? window.NodeList : {};\n\nvar canUseDOM = exports.canUseDOM = EE.canUseDOM;\n\nexports.default = SafeHTMLElement;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,SAAS,GAAGF,OAAO,CAACG,YAAY,GAAGH,OAAO,CAACI,kBAAkB,GAAGC,SAAS;AAEjF,IAAIC,MAAM,GAAGC,OAAO,CAAC,OAAO,CAAC;AAE7B,IAAIC,OAAO,GAAGC,sBAAsB,CAACH,MAAM,CAAC;AAE5C,SAASG,sBAAsBA,CAACC,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEE,OAAO,EAAEF;EAAI,CAAC;AAAE;AAE9F,IAAIG,EAAE,GAAGL,OAAO,CAACI,OAAO;AAExB,IAAIE,eAAe,GAAGD,EAAE,CAACX,SAAS,GAAGa,MAAM,CAACC,WAAW,GAAG,CAAC,CAAC;AAE5D,IAAIZ,kBAAkB,GAAGJ,OAAO,CAACI,kBAAkB,GAAGS,EAAE,CAACX,SAAS,GAAGa,MAAM,CAACE,cAAc,GAAG,CAAC,CAAC;AAE/F,IAAId,YAAY,GAAGH,OAAO,CAACG,YAAY,GAAGU,EAAE,CAACX,SAAS,GAAGa,MAAM,CAACG,QAAQ,GAAG,CAAC,CAAC;AAE7E,IAAIhB,SAAS,GAAGF,OAAO,CAACE,SAAS,GAAGW,EAAE,CAACX,SAAS;AAEhDF,OAAO,CAACY,OAAO,GAAGE,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}