{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nimport warning from \"rc-util/es/warning\";\n// Convert `showSearch` to unique config\nexport default function useSearchConfig(showSearch) {\n  return React.useMemo(function () {\n    if (!showSearch) {\n      return [false, {}];\n    }\n    var searchConfig = {\n      matchInputWidth: true,\n      limit: 50\n    };\n    if (showSearch && _typeof(showSearch) === 'object') {\n      searchConfig = _objectSpread(_objectSpread({}, searchConfig), showSearch);\n    }\n    if (searchConfig.limit <= 0) {\n      delete searchConfig.limit;\n      if (process.env.NODE_ENV !== 'production') {\n        warning(false, \"'limit' of showSearch should be positive number or false.\");\n      }\n    }\n    return [true, searchConfig];\n  }, [showSearch]);\n}", "map": {"version": 3, "names": ["_objectSpread", "_typeof", "React", "warning", "useSearchConfig", "showSearch", "useMemo", "searchConfig", "matchInputWidth", "limit", "process", "env", "NODE_ENV"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-cascader/es/hooks/useSearchConfig.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nimport warning from \"rc-util/es/warning\";\n// Convert `showSearch` to unique config\nexport default function useSearchConfig(showSearch) {\n  return React.useMemo(function () {\n    if (!showSearch) {\n      return [false, {}];\n    }\n    var searchConfig = {\n      matchInputWidth: true,\n      limit: 50\n    };\n    if (showSearch && _typeof(showSearch) === 'object') {\n      searchConfig = _objectSpread(_objectSpread({}, searchConfig), showSearch);\n    }\n    if (searchConfig.limit <= 0) {\n      delete searchConfig.limit;\n      if (process.env.NODE_ENV !== 'production') {\n        warning(false, \"'limit' of showSearch should be positive number or false.\");\n      }\n    }\n    return [true, searchConfig];\n  }, [showSearch]);\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,oBAAoB;AACxC;AACA,eAAe,SAASC,eAAeA,CAACC,UAAU,EAAE;EAClD,OAAOH,KAAK,CAACI,OAAO,CAAC,YAAY;IAC/B,IAAI,CAACD,UAAU,EAAE;MACf,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IACpB;IACA,IAAIE,YAAY,GAAG;MACjBC,eAAe,EAAE,IAAI;MACrBC,KAAK,EAAE;IACT,CAAC;IACD,IAAIJ,UAAU,IAAIJ,OAAO,CAACI,UAAU,CAAC,KAAK,QAAQ,EAAE;MAClDE,YAAY,GAAGP,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEO,YAAY,CAAC,EAAEF,UAAU,CAAC;IAC3E;IACA,IAAIE,YAAY,CAACE,KAAK,IAAI,CAAC,EAAE;MAC3B,OAAOF,YAAY,CAACE,KAAK;MACzB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCT,OAAO,CAAC,KAAK,EAAE,2DAA2D,CAAC;MAC7E;IACF;IACA,OAAO,CAAC,IAAI,EAAEI,YAAY,CAAC;EAC7B,CAAC,EAAE,CAACF,UAAU,CAAC,CAAC;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}