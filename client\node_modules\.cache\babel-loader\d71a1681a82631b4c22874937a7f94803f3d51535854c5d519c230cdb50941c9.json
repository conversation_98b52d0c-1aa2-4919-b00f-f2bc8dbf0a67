{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\n// [times, realValue]\n\nvar SPLIT = '%';\nvar Entity = /*#__PURE__*/function () {\n  function Entity(instanceId) {\n    _classCallCheck(this, Entity);\n    _defineProperty(this, \"instanceId\", void 0);\n    /** @private Internal cache map. Do not access this directly */\n    _defineProperty(this, \"cache\", new Map());\n    this.instanceId = instanceId;\n  }\n  _createClass(Entity, [{\n    key: \"get\",\n    value: function get(keys) {\n      return this.cache.get(keys.join(SPLIT)) || null;\n    }\n  }, {\n    key: \"update\",\n    value: function update(keys, valueFn) {\n      var path = keys.join(SPLIT);\n      var prevValue = this.cache.get(path);\n      var nextValue = valueFn(prevValue);\n      if (nextValue === null) {\n        this.cache.delete(path);\n      } else {\n        this.cache.set(path, nextValue);\n      }\n    }\n  }]);\n  return Entity;\n}();\nexport default Entity;", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_defineProperty", "SPLIT", "Entity", "instanceId", "Map", "key", "value", "get", "keys", "cache", "join", "update", "valueFn", "path", "prevValue", "nextValue", "delete", "set"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/@ant-design/cssinjs/es/Cache.js"], "sourcesContent": ["import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\n// [times, realValue]\n\nvar SPLIT = '%';\nvar Entity = /*#__PURE__*/function () {\n  function Entity(instanceId) {\n    _classCallCheck(this, Entity);\n    _defineProperty(this, \"instanceId\", void 0);\n    /** @private Internal cache map. Do not access this directly */\n    _defineProperty(this, \"cache\", new Map());\n    this.instanceId = instanceId;\n  }\n  _createClass(Entity, [{\n    key: \"get\",\n    value: function get(keys) {\n      return this.cache.get(keys.join(SPLIT)) || null;\n    }\n  }, {\n    key: \"update\",\n    value: function update(keys, valueFn) {\n      var path = keys.join(SPLIT);\n      var prevValue = this.cache.get(path);\n      var nextValue = valueFn(prevValue);\n      if (nextValue === null) {\n        this.cache.delete(path);\n      } else {\n        this.cache.set(path, nextValue);\n      }\n    }\n  }]);\n  return Entity;\n}();\nexport default Entity;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,eAAe,MAAM,2CAA2C;AACvE;;AAEA,IAAIC,KAAK,GAAG,GAAG;AACf,IAAIC,MAAM,GAAG,aAAa,YAAY;EACpC,SAASA,MAAMA,CAACC,UAAU,EAAE;IAC1BL,eAAe,CAAC,IAAI,EAAEI,MAAM,CAAC;IAC7BF,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;IAC3C;IACAA,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,IAAII,GAAG,CAAC,CAAC,CAAC;IACzC,IAAI,CAACD,UAAU,GAAGA,UAAU;EAC9B;EACAJ,YAAY,CAACG,MAAM,EAAE,CAAC;IACpBG,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE,SAASC,GAAGA,CAACC,IAAI,EAAE;MACxB,OAAO,IAAI,CAACC,KAAK,CAACF,GAAG,CAACC,IAAI,CAACE,IAAI,CAACT,KAAK,CAAC,CAAC,IAAI,IAAI;IACjD;EACF,CAAC,EAAE;IACDI,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASK,MAAMA,CAACH,IAAI,EAAEI,OAAO,EAAE;MACpC,IAAIC,IAAI,GAAGL,IAAI,CAACE,IAAI,CAACT,KAAK,CAAC;MAC3B,IAAIa,SAAS,GAAG,IAAI,CAACL,KAAK,CAACF,GAAG,CAACM,IAAI,CAAC;MACpC,IAAIE,SAAS,GAAGH,OAAO,CAACE,SAAS,CAAC;MAClC,IAAIC,SAAS,KAAK,IAAI,EAAE;QACtB,IAAI,CAACN,KAAK,CAACO,MAAM,CAACH,IAAI,CAAC;MACzB,CAAC,MAAM;QACL,IAAI,CAACJ,KAAK,CAACQ,GAAG,CAACJ,IAAI,EAAEE,SAAS,CAAC;MACjC;IACF;EACF,CAAC,CAAC,CAAC;EACH,OAAOb,MAAM;AACf,CAAC,CAAC,CAAC;AACH,eAAeA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}