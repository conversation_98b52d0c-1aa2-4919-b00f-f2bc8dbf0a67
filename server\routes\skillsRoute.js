const router = require("express").Router();
const Skill = require("../models/skillModel");
const authMiddleware = require("../middlewares/authMiddleware");
const adminMiddleware = require("../middlewares/adminMiddleware");

// Get all skills (public route with filtering)
router.get("/", async (req, res) => {
  try {
    const {
      level,
      category,
      targetAudience,
      search,
      featured,
      limit = 50,
      page = 1,
      sortBy = "createdAt",
      sortOrder = "desc"
    } = req.query;

    // Build filter object
    const filters = { isActive: true };
    
    if (level) filters.level = level;
    if (category && category !== "all") filters.category = category;
    if (targetAudience && targetAudience !== "all") {
      filters.targetAudience = { $in: [targetAudience, "all"] };
    }
    if (featured === "true") filters.isFeatured = true;

    // Build query
    let query = Skill.find(filters);

    // Add text search if provided
    if (search) {
      query = Skill.find({
        ...filters,
        $text: { $search: search }
      });
    }

    // Add sorting
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === "desc" ? -1 : 1;
    if (search) {
      sortOptions.score = { $meta: "textScore" };
    }
    query = query.sort(sortOptions);

    // Add pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    query = query.skip(skip).limit(parseInt(limit));

    // Populate creator info
    query = query.populate("createdBy", "name username");

    const skills = await query;
    const total = await Skill.countDocuments(filters);

    res.send({
      success: true,
      data: skills,
      pagination: {
        current: parseInt(page),
        total: Math.ceil(total / parseInt(limit)),
        count: skills.length,
        totalItems: total
      }
    });
  } catch (error) {
    console.error("Error fetching skills:", error);
    res.status(500).send({
      message: "Error fetching skills",
      success: false,
      error: error.message
    });
  }
});

// Get skill by ID (public route)
router.get("/:id", async (req, res) => {
  try {
    const skill = await Skill.findById(req.params.id)
      .populate("createdBy", "name username")
      .populate("lastModifiedBy", "name username");

    if (!skill) {
      return res.status(404).send({
        message: "Skill not found",
        success: false
      });
    }

    // Increment view count
    await skill.incrementView();

    res.send({
      success: true,
      data: skill
    });
  } catch (error) {
    console.error("Error fetching skill:", error);
    res.status(500).send({
      message: "Error fetching skill",
      success: false,
      error: error.message
    });
  }
});

// Get skills by level (public route)
router.get("/level/:level", async (req, res) => {
  try {
    const { level } = req.params;
    const { limit = 20 } = req.query;

    const skills = await Skill.getSkillsByLevel(level)
      .limit(parseInt(limit))
      .populate("createdBy", "name username");

    res.send({
      success: true,
      data: skills
    });
  } catch (error) {
    console.error("Error fetching skills by level:", error);
    res.status(500).send({
      message: "Error fetching skills by level",
      success: false,
      error: error.message
    });
  }
});

// Get featured skills (public route)
router.get("/featured/list", async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    
    const skills = await Skill.getFeaturedSkills(parseInt(limit))
      .populate("createdBy", "name username");

    res.send({
      success: true,
      data: skills
    });
  } catch (error) {
    console.error("Error fetching featured skills:", error);
    res.status(500).send({
      message: "Error fetching featured skills",
      success: false,
      error: error.message
    });
  }
});

// Search skills (public route)
router.post("/search", async (req, res) => {
  try {
    const { query, filters = {} } = req.body;
    
    const skills = await Skill.searchSkills(query, filters)
      .populate("createdBy", "name username");

    res.send({
      success: true,
      data: skills
    });
  } catch (error) {
    console.error("Error searching skills:", error);
    res.status(500).send({
      message: "Error searching skills",
      success: false,
      error: error.message
    });
  }
});

// Mark skill as completed (authenticated route)
router.post("/:id/complete", authMiddleware, async (req, res) => {
  try {
    const skill = await Skill.findById(req.params.id);
    
    if (!skill) {
      return res.status(404).send({
        message: "Skill not found",
        success: false
      });
    }

    await skill.incrementCompletion();

    res.send({
      success: true,
      message: "Skill marked as completed",
      data: skill
    });
  } catch (error) {
    console.error("Error marking skill as completed:", error);
    res.status(500).send({
      message: "Error marking skill as completed",
      success: false,
      error: error.message
    });
  }
});

// ADMIN ROUTES (require admin authentication)

// Create new skill (admin only)
router.post("/admin/create", authMiddleware, adminMiddleware, async (req, res) => {
  try {
    const skillData = {
      ...req.body,
      createdBy: req.body.userId
    };

    const skill = new Skill(skillData);
    await skill.save();

    const populatedSkill = await Skill.findById(skill._id)
      .populate("createdBy", "name username");

    res.send({
      success: true,
      message: "Skill created successfully",
      data: populatedSkill
    });
  } catch (error) {
    console.error("Error creating skill:", error);
    res.status(500).send({
      message: "Error creating skill",
      success: false,
      error: error.message
    });
  }
});

// Update skill (admin only)
router.put("/admin/:id", authMiddleware, adminMiddleware, async (req, res) => {
  try {
    const updateData = {
      ...req.body,
      lastModifiedBy: req.body.userId
    };

    const skill = await Skill.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    ).populate("createdBy", "name username")
     .populate("lastModifiedBy", "name username");

    if (!skill) {
      return res.status(404).send({
        message: "Skill not found",
        success: false
      });
    }

    res.send({
      success: true,
      message: "Skill updated successfully",
      data: skill
    });
  } catch (error) {
    console.error("Error updating skill:", error);
    res.status(500).send({
      message: "Error updating skill",
      success: false,
      error: error.message
    });
  }
});

// Delete skill (admin only)
router.delete("/admin/:id", authMiddleware, adminMiddleware, async (req, res) => {
  try {
    const skill = await Skill.findByIdAndDelete(req.params.id);

    if (!skill) {
      return res.status(404).send({
        message: "Skill not found",
        success: false
      });
    }

    res.send({
      success: true,
      message: "Skill deleted successfully"
    });
  } catch (error) {
    console.error("Error deleting skill:", error);
    res.status(500).send({
      message: "Error deleting skill",
      success: false,
      error: error.message
    });
  }
});

// Get all skills for admin (includes inactive)
router.get("/admin/all", authMiddleware, adminMiddleware, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      sortBy = "createdAt",
      sortOrder = "desc"
    } = req.query;

    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === "desc" ? -1 : 1;

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const skills = await Skill.find({})
      .sort(sortOptions)
      .skip(skip)
      .limit(parseInt(limit))
      .populate("createdBy", "name username")
      .populate("lastModifiedBy", "name username");

    const total = await Skill.countDocuments({});

    res.send({
      success: true,
      data: skills,
      pagination: {
        current: parseInt(page),
        total: Math.ceil(total / parseInt(limit)),
        count: skills.length,
        totalItems: total
      }
    });
  } catch (error) {
    console.error("Error fetching all skills for admin:", error);
    res.status(500).send({
      message: "Error fetching skills",
      success: false,
      error: error.message
    });
  }
});

module.exports = router;
