{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\index.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, startTransition } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { useLanguage } from '../../../contexts/LanguageContext';\nimport { TbSearch, TbFilter, TbClock, TbQuestionMark, TbTrophy, TbPlayerPlay, TbBrain, TbTarget, TbCheck, TbX, TbStar, TbHome, TbBolt } from 'react-icons/tb';\nimport { getAllExams } from '../../../apicalls/exams';\nimport { getAllReportsByUser } from '../../../apicalls/reports';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport './animations.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Quiz = () => {\n  _s();\n  const [exams, setExams] = useState([]);\n  const [filteredExams, setFilteredExams] = useState([]);\n  const [searchTerm, setSearchTerm] = useState(() => {\n    // Restore search term from localStorage\n    return localStorage.getItem('quiz-search-term') || '';\n  });\n  const [selectedClass, setSelectedClass] = useState(() => {\n    // Restore selected class from localStorage\n    return localStorage.getItem('quiz-selected-class') || '';\n  });\n  const [userResults, setUserResults] = useState({});\n  const [loading, setLoading] = useState(true);\n  const [lastRefresh, setLastRefresh] = useState(null);\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    t,\n    isKiswahili,\n    getClassName\n  } = useLanguage();\n\n  // Function to clear all quiz caches\n  const clearAllQuizCaches = () => {\n    const allLevels = ['primary', 'secondary', 'advance'];\n    allLevels.forEach(level => {\n      localStorage.removeItem(`user_exams_cache_${level}`);\n      localStorage.removeItem(`user_exams_cache_time_${level}`);\n    });\n    // Also clear old cache keys for backward compatibility\n    localStorage.removeItem('user_exams_cache');\n    localStorage.removeItem('user_exams_cache_time');\n  };\n\n  // Remove white space on component mount and handle cleanup\n  useEffect(() => {\n    // Add class to body to remove spacing\n    document.body.classList.add('quiz-page-active');\n\n    // Cleanup on unmount (when user navigates away or logs out)\n    return () => {\n      document.body.classList.remove('quiz-page-active');\n      // Note: We don't clear localStorage here to maintain search persistence\n      // Search is only cleared on manual refresh or explicit clear action\n    };\n  }, []);\n\n  // Clear search when user changes (logout scenario)\n  useEffect(() => {\n    if (!user) {\n      clearSearchAndFilters();\n    }\n  }, [user]);\n\n  // Handle search term change with localStorage persistence\n  const handleSearchChange = value => {\n    setSearchTerm(value);\n    localStorage.setItem('quiz-search-term', value);\n  };\n\n  // Handle class selection change with localStorage persistence\n  const handleClassChange = value => {\n    setSelectedClass(value);\n    localStorage.setItem('quiz-selected-class', value);\n  };\n\n  // Clear search and filters (for manual refresh)\n  const clearSearchAndFilters = () => {\n    setSearchTerm('');\n    setSelectedClass('');\n    localStorage.removeItem('quiz-search-term');\n    localStorage.removeItem('quiz-selected-class');\n  };\n  const getUserResults = useCallback(async () => {\n    try {\n      if (!(user !== null && user !== void 0 && user._id)) return;\n      const response = await getAllReportsByUser({\n        userId: user._id\n      });\n      if (response.success) {\n        const resultsMap = {};\n        response.data.forEach(report => {\n          var _report$exam;\n          const examId = (_report$exam = report.exam) === null || _report$exam === void 0 ? void 0 : _report$exam._id;\n          if (!examId || !report.result) return;\n\n          // Extract data from the result object\n          const result = report.result;\n          if (!resultsMap[examId] || new Date(report.createdAt) > new Date(resultsMap[examId].createdAt)) {\n            resultsMap[examId] = {\n              verdict: result.verdict,\n              percentage: result.percentage,\n              correctAnswers: result.correctAnswers,\n              wrongAnswers: result.wrongAnswers,\n              totalQuestions: result.totalQuestions,\n              obtainedMarks: result.obtainedMarks,\n              totalMarks: result.totalMarks,\n              score: result.score,\n              points: result.points,\n              xpEarned: result.xpEarned || result.points || result.xpGained || 0,\n              timeTaken: report.timeTaken,\n              completedAt: report.createdAt\n            };\n          }\n        });\n        setUserResults(resultsMap);\n      }\n    } catch (error) {\n      console.error('Error fetching user results:', error);\n    }\n  }, [user === null || user === void 0 ? void 0 : user._id]);\n\n  // Define getExams function to load exams once\n  const getExams = useCallback(async () => {\n    try {\n      // Safety check: ensure user exists before proceeding\n      if (!user) {\n        console.log(\"User not loaded yet, skipping exam fetch\");\n        return;\n      }\n\n      // Level-specific cache to prevent cross-level contamination\n      const userLevel = (user === null || user === void 0 ? void 0 : user.level) || 'primary';\n      const cacheKey = `user_exams_cache_${userLevel}`;\n      const cacheTimeKey = `user_exams_cache_time_${userLevel}`;\n\n      // Clear caches for other levels\n      const allLevels = ['primary', 'secondary', 'advance'];\n      allLevels.forEach(level => {\n        if (level !== userLevel) {\n          localStorage.removeItem(`user_exams_cache_${level}`);\n          localStorage.removeItem(`user_exams_cache_time_${level}`);\n        }\n      });\n\n      // Check level-specific cache first\n      const cachedExams = localStorage.getItem(cacheKey);\n      const cacheTime = localStorage.getItem(cacheTimeKey);\n      const now = Date.now();\n\n      // Use cache if less than 10 minutes old (increased cache time)\n      if (cachedExams && cacheTime && now - parseInt(cacheTime) < 600000) {\n        const cached = JSON.parse(cachedExams);\n        setExams(cached);\n        setLastRefresh(new Date(parseInt(cacheTime)));\n        setLoading(false);\n        console.log(`📋 Using cached exams for ${userLevel} level:`, cached.length);\n        return;\n      }\n      dispatch(ShowLoading());\n      const response = await getAllExams();\n      dispatch(HideLoading());\n      if (response.success) {\n        console.log('Raw exams from API:', response.data.length);\n        console.log('User level:', user === null || user === void 0 ? void 0 : user.level);\n\n        // Filter exams by user's level with proper null checks\n        const userLevelExams = response.data.filter(exam => {\n          if (!exam.level || !user || !user.level) return false;\n          return exam.level.toLowerCase() === user.level.toLowerCase();\n        });\n        console.log('User level exams after filtering:', userLevelExams.length);\n        const sortedExams = userLevelExams.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n        setExams(sortedExams);\n        setLastRefresh(new Date());\n\n        // Cache the exams data with level-specific key\n        localStorage.setItem(cacheKey, JSON.stringify(sortedExams));\n        localStorage.setItem(cacheTimeKey, Date.now().toString());\n\n        // Set default class filter to user's class\n        if (user !== null && user !== void 0 && user.class) {\n          setSelectedClass(String(user.class));\n        }\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    } finally {\n      setLoading(false);\n    }\n  }, [dispatch, user]);\n  useEffect(() => {\n    // Clear ALL caches when component mounts to ensure fresh data\n    clearAllQuizCaches();\n    getExams(); // Initial load only\n    getUserResults();\n  }, [getExams, getUserResults]);\n\n  // Real-time updates for quiz completion and new exams\n  useEffect(() => {\n    // Listen for real-time updates from quiz completion\n    const handleRankingUpdate = () => {\n      console.log('🔄 Quiz listing - refreshing data after quiz completion...');\n      getUserResults(); // Refresh user results to show updated XP\n    };\n\n    // Listen for new exam creation events\n    const handleNewExam = () => {\n      console.log('🆕 New exam created - refreshing user results only...');\n      if (user) {\n        getUserResults(); // Only refresh user results, keep filters intact\n      }\n    };\n\n    // Listen for window focus to refresh user results only (not exams)\n    const handleWindowFocus = () => {\n      console.log('🎯 Quiz listing - window focused, refreshing user results...');\n      getUserResults(); // Only refresh user results, keep filters intact\n    };\n\n    window.addEventListener('rankingUpdate', handleRankingUpdate);\n    window.addEventListener('focus', handleWindowFocus);\n    window.addEventListener('newExamCreated', handleNewExam);\n    return () => {\n      window.removeEventListener('rankingUpdate', handleRankingUpdate);\n      window.removeEventListener('focus', handleWindowFocus);\n      window.removeEventListener('newExamCreated', handleNewExam);\n    };\n  }, []);\n  useEffect(() => {\n    console.log('Filtering exams:', {\n      exams: exams.length,\n      searchTerm,\n      selectedClass\n    });\n    let filtered = exams;\n    if (searchTerm) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(exam => {\n        // Search in multiple fields for comprehensive results\n        const searchableFields = [exam.name, exam.subject, exam.topic, exam.description, exam.category, exam.level,\n        // Search in questions if available\n        ...(exam.questions || []).map(q => q.questionText), ...(exam.questions || []).map(q => q.subject), ...(exam.questions || []).map(q => q.topic)];\n        return searchableFields.some(field => field && field.toString().toLowerCase().includes(searchLower));\n      });\n    }\n    if (selectedClass) {\n      filtered = filtered.filter(exam => String(exam.class) === String(selectedClass));\n    }\n    filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n    console.log('Filtered exams result:', filtered.length);\n    setFilteredExams(filtered);\n  }, [exams, searchTerm, selectedClass]);\n  const availableClasses = [...new Set(exams.map(e => e.class).filter(Boolean))].sort();\n  const handleQuizStart = quiz => {\n    if (!quiz || !quiz._id) {\n      message.error('Invalid quiz selected. Please try again.');\n      return;\n    }\n\n    // Validate MongoDB ObjectId format (24 character hex string)\n    const objectIdRegex = /^[0-9a-fA-F]{24}$/;\n    if (!objectIdRegex.test(quiz._id)) {\n      message.error('Invalid quiz ID format. Please try again.');\n      return;\n    }\n    startTransition(() => {\n      navigate(`/quiz/${quiz._id}/play`);\n    });\n  };\n  const handleQuizView = quiz => {\n    if (!quiz || !quiz._id) {\n      message.error('Invalid quiz selected. Please try again.');\n      return;\n    }\n    // Check if user has attempted this quiz\n    const userResult = userResults[quiz._id];\n    if (!userResult) {\n      message.info('You need to attempt this quiz first to view results.');\n      return;\n    }\n    startTransition(() => {\n      navigate(`/quiz/${quiz._id}/result`);\n    });\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: isKiswahili ? 'Inapakia mitihani...' : 'Loading quizzes...'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      global: true,\n      children: `\n        /* Completely remove all white space from ProtectedRoute main wrapper */\n        body.quiz-page-active main {\n          padding: 0 !important;\n          margin: 0 !important;\n        }\n\n        /* Remove all spacing from layout containers */\n        body.quiz-page-active .safe-content-animation {\n          padding: 0 !important;\n          margin: 0 !important;\n        }\n\n        /* Remove any inherited spacing from all parent containers */\n        body.quiz-page-active main > div,\n        body.quiz-page-active main * {\n          margin-top: 0 !important;\n        }\n\n        /* Ensure Quiz container starts immediately */\n        .quiz-container-immediate {\n          margin-top: 0 !important;\n          padding-top: 0 !important;\n        }\n        .quiz-grid {\n          gap: 1rem !important;\n          margin-top: 1rem !important;\n        }\n        @media (min-width: 640px) {\n          .quiz-grid {\n            gap: 1.25rem !important;\n            margin-top: 1rem !important;\n          }\n        }\n        @media (min-width: 1024px) {\n          .quiz-grid {\n            gap: 1.5rem !important;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 quiz-container-immediate\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-3 sm:px-4 lg:px-8 pt-3 pb-3 sm:pb-4 lg:pb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-4xl mx-auto mb-3 sm:mb-4 opacity-100\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-xl shadow-md p-3 sm:p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col sm:flex-row items-center justify-between mb-3 pb-3 border-b border-gray-100\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-4 text-sm text-gray-600\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2.5 h-2.5 bg-blue-500 rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 394,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium\",\n                    children: [\"Level: \", (user === null || user === void 0 ? void 0 : user.level) || 'All Levels']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 395,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2.5 h-2.5 bg-green-500 rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 398,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [filteredExams.length, \" Available Quizzes\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 399,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 15\n              }, this), lastRefresh && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-2 text-xs text-gray-400 mt-2 sm:mt-0\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-2 h-2 bg-gray-400 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Updated: \", lastRefresh.toLocaleTimeString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col sm:flex-row gap-2 sm:gap-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1 relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                  children: /*#__PURE__*/_jsxDEV(TbSearch, {\n                    className: \"h-4 w-4 text-gray-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Search quizzes by subject, topic, or name...\",\n                  value: searchTerm,\n                  onChange: e => handleSearchChange(e.target.value),\n                  className: \"block w-full pl-9 pr-3 py-2 border border-gray-300 rounded-lg bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all text-sm\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-36 sm:w-40\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                    children: /*#__PURE__*/_jsxDEV(TbFilter, {\n                      className: \"h-4 w-4 text-gray-400\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 427,\n                      columnNumber: 21\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 426,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: selectedClass,\n                    onChange: e => handleClassChange(e.target.value),\n                    className: \"block w-full pl-9 pr-8 py-2 border border-gray-300 rounded-lg bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all appearance-none text-sm\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"All Classes\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 434,\n                      columnNumber: 21\n                    }, this), availableClasses.map(className => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: className,\n                      children: (user === null || user === void 0 ? void 0 : user.level) === 'primary' ? `Class ${className}` : className\n                    }, className, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 436,\n                      columnNumber: 23\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 429,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 15\n              }, this), (searchTerm || selectedClass) && /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: clearSearchAndFilters,\n                className: \"flex items-center justify-center px-3 py-2 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-lg hover:from-gray-600 hover:to-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-all shadow-md\",\n                title: \"Clear all search and filters\",\n                children: [/*#__PURE__*/_jsxDEV(TbX, {\n                  className: \"h-4 w-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 451,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-1.5 hidden sm:inline text-sm\",\n                  children: \"Clear\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 452,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"opacity-100\",\n          style: {\n            marginTop: '0.5rem'\n          },\n          children: filteredExams.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8 sm:py-12\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-2xl shadow-lg p-6 sm:p-8 max-w-md mx-auto\",\n              children: [/*#__PURE__*/_jsxDEV(TbTarget, {\n                className: \"w-12 h-12 sm:w-16 sm:h-16 text-gray-400 mx-auto mb-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg sm:text-xl font-semibold text-gray-900 mb-2\",\n                children: \"No Quizzes Found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 text-sm sm:text-base\",\n                children: searchTerm || selectedClass ? \"Try adjusting your search or filter criteria.\" : \"No quizzes are available for your level at the moment.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 13\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"quiz-grid\",\n            children: filteredExams.map((quiz, index) => /*#__PURE__*/_jsxDEV(QuizCard, {\n              quiz: quiz,\n              userResult: userResults[quiz._id],\n              showResults: true,\n              onStart: handleQuizStart,\n              onView: () => handleQuizView(quiz),\n              index: index\n            }, quiz._id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 383,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// Simple QuizCard component without Framer Motion\n_s(Quiz, \"qe8dMDzVl+cIxG6Lzwb8f8qIbyo=\", false, function () {\n  return [useNavigate, useDispatch, useSelector, useLanguage];\n});\n_c = Quiz;\nconst QuizCard = ({\n  quiz,\n  userResult,\n  onStart,\n  onView,\n  index\n}) => {\n  _s2();\n  var _quiz$questions;\n  const {\n    isKiswahili,\n    getClassName\n  } = useLanguage();\n  const formatTime = seconds => {\n    if (!seconds) return 'N/A';\n    const minutes = Math.round(seconds / 60);\n    return `${minutes} ${isKiswahili ? 'dak' : 'min'}`;\n  };\n  const formatCompletionTime = timeInSeconds => {\n    // Handle different possible time formats\n    if (!timeInSeconds && timeInSeconds !== 0) return '0s';\n    let totalSeconds = timeInSeconds;\n\n    // If it's a string, try to parse it\n    if (typeof timeInSeconds === 'string') {\n      totalSeconds = parseInt(timeInSeconds, 10);\n    }\n\n    // If it's still not a valid number, return 0s\n    if (isNaN(totalSeconds) || totalSeconds < 0) return '0s';\n    const minutes = Math.floor(totalSeconds / 60);\n    const seconds = totalSeconds % 60;\n    if (minutes > 0) {\n      return `${minutes}m ${seconds}s`;\n    }\n    return `${timeInSeconds}s`;\n  };\n\n  // Safety checks for quiz object\n  if (!quiz || typeof quiz !== 'object') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-2xl shadow-lg p-6 border border-gray-100\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500\",\n        children: \"Invalid quiz data\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 535,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 534,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-3 transform hover:scale-105 opacity-100 relative flex flex-col\",\n    style: {\n      background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%)',\n      border: userResult ? userResult.verdict === 'Pass' ? '2px solid #10b981' : '2px solid #ef4444' : '2px solid #3b82f6',\n      boxShadow: userResult ? userResult.verdict === 'Pass' ? '0 8px 20px rgba(16, 185, 129, 0.3)' : '0 8px 20px rgba(239, 68, 68, 0.3)' : '0 8px 20px rgba(59, 130, 246, 0.3)',\n      minHeight: window.innerWidth <= 768 ? '240px' : '320px',\n      height: 'auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-2 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"font-bold mb-1 line-clamp-2\",\n        style: {\n          color: '#1f2937',\n          textShadow: '0 1px 2px rgba(0,0,0,0.1)',\n          lineHeight: '1.1',\n          fontSize: window.innerWidth <= 768 ? '14px' : '16px'\n        },\n        children: typeof quiz.name === 'string' ? quiz.name : 'Untitled Quiz'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 559,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 558,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-2 text-center\",\n      children: userResult ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center gap-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-2 py-1 rounded-full text-xs font-bold text-white shadow-md\",\n          style: {\n            backgroundColor: userResult.verdict === 'Pass' ? '#10b981' : '#ef4444',\n            fontSize: window.innerWidth <= 768 ? '9px' : '10px'\n          },\n          children: userResult.verdict === 'Pass' ? '✅ PASSED' : '❌ FAILED'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 576,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-2 py-1 rounded-full text-xs font-bold text-center shadow-md\",\n          style: {\n            backgroundColor: '#ffffff',\n            color: '#1f2937',\n            fontSize: window.innerWidth <= 768 ? '9px' : '10px'\n          },\n          children: [typeof userResult.percentage === 'number' ? userResult.percentage : 0, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 585,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 575,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-2 py-1 rounded-full text-xs font-bold text-white shadow-md\",\n        style: {\n          backgroundColor: '#3b82f6',\n          fontSize: window.innerWidth <= 768 ? '9px' : '10px'\n        },\n        children: \"\\uD83C\\uDD95 NOT ATTEMPTED\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 597,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 573,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center mb-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-1 mb-2 justify-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-1 rounded-lg py-1 px-2 border shadow-sm\",\n            style: {\n              background: 'linear-gradient(to right, #eff6ff, #e0e7ff)',\n              borderColor: '#bfdbfe'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TbQuestionMark, {\n              className: \"w-3 h-3\",\n              style: {\n                color: '#2563eb'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 621,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-bold\",\n              style: {\n                color: '#1e40af',\n                fontSize: window.innerWidth <= 768 ? '11px' : '12px'\n              },\n              children: Array.isArray(quiz.questions) ? quiz.questions.length : 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 622,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 614,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-1 rounded-lg py-1 px-2 border shadow-sm\",\n            style: {\n              background: 'linear-gradient(to right, #fdf4ff, #fce7f3)',\n              borderColor: '#e9d5ff'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TbClock, {\n              className: \"w-3 h-3\",\n              style: {\n                color: '#9333ea'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 639,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-bold\",\n              style: {\n                color: '#7c3aed',\n                fontSize: window.innerWidth <= 768 ? '11px' : '12px'\n              },\n              children: \"3m\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 640,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 632,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 613,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center gap-1 flex-wrap mb-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-block px-1 py-1 font-bold rounded-full text-white shadow-sm\",\n            style: {\n              background: 'linear-gradient(to right, #8b5cf6, #7c3aed)',\n              fontSize: window.innerWidth <= 768 ? '8px' : '10px'\n            },\n            children: [\"\\uD83C\\uDFAF\", quiz.level || 'Primary']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 656,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-block px-1 py-1 font-bold rounded-full text-white shadow-sm\",\n            style: {\n              background: 'linear-gradient(to right, #4ade80, #3b82f6)',\n              fontSize: window.innerWidth <= 768 ? '8px' : '10px'\n            },\n            children: [\"\\uD83D\\uDCD6\", typeof quiz.class === 'string' || typeof quiz.class === 'number' ? quiz.level === 'primary' || quiz.level === 'primary_kiswahili' ? isKiswahili ? `Darasa la ${quiz.class}` : `Class ${quiz.class}` : quiz.class : 'N/A']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 667,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-block px-1 py-1 font-bold rounded-full text-white shadow-sm\",\n            style: {\n              background: 'linear-gradient(to right, #f97316, #ea580c)',\n              fontSize: window.innerWidth <= 768 ? '8px' : '10px'\n            },\n            children: [\"\\uD83D\\uDCC2\", quiz.category || 'General']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 681,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"inline-block px-1 py-1 font-bold rounded-full text-white shadow-sm\",\n            style: {\n              background: quiz.topic && quiz.topic !== 'General' && quiz.topic !== '' ? 'linear-gradient(to right, #10b981, #059669)' : 'linear-gradient(to right, #6b7280, #4b5563)',\n              fontSize: window.innerWidth <= 768 ? '8px' : '10px'\n            },\n            children: [\"\\uD83D\\uDCDA\", quiz.topic || (isKiswahili ? 'Jumla' : 'General')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 692,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 654,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 610,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 609,\n      columnNumber: 7\n    }, this), userResult && typeof userResult === 'object' && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-2 p-2 rounded-lg border shadow-md\",\n      style: {\n        background: userResult.verdict === 'Pass' ? 'linear-gradient(to bottom right, #f0fdf4, #ecfdf5)' : 'linear-gradient(to bottom right, #fef2f2, #fdf2f8)',\n        borderColor: userResult.verdict === 'Pass' ? '#86efac' : '#fca5a5'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3\",\n          children: [userResult.verdict === 'Pass' ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-10 h-10 rounded-full flex items-center justify-center shadow-lg border-2 animate-pulse\",\n            style: {\n              background: 'linear-gradient(to right, #10b981, #059669)',\n              borderColor: '#86efac'\n            },\n            children: /*#__PURE__*/_jsxDEV(TbCheck, {\n              className: \"w-6 h-6 font-bold\",\n              style: {\n                color: '#ffffff'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 729,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 722,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-10 h-10 rounded-full flex items-center justify-center shadow-lg border-2 animate-pulse\",\n            style: {\n              background: 'linear-gradient(to right, #ef4444, #dc2626)',\n              borderColor: '#fca5a5'\n            },\n            children: /*#__PURE__*/_jsxDEV(TbX, {\n              className: \"w-6 h-6 font-bold\",\n              style: {\n                color: '#ffffff'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 739,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 732,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg font-bold\",\n              style: {\n                color: '#1f2937'\n              },\n              children: \"\\uD83C\\uDFC6 Last Result\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 743,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm\",\n              style: {\n                color: '#6b7280'\n              },\n              children: new Date(userResult.completedAt || userResult.createdAt || Date.now()).toLocaleDateString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 744,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 742,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 720,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-3xl font-bold shadow-lg\",\n          style: {\n            color: userResult.verdict === 'Pass' ? '#059669' : '#dc2626'\n          },\n          children: [typeof userResult.percentage === 'number' ? userResult.percentage : 0, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 749,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 719,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex gap-1 justify-center flex-wrap\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-1 rounded-lg py-1 px-2 border shadow-md\",\n          style: {\n            background: 'linear-gradient(to right, #dcfce7, #fecaca)',\n            borderColor: '#86efac'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-1\",\n            children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n              className: \"w-3 h-3\",\n              style: {\n                color: '#16a34a'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 770,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-bold\",\n              style: {\n                color: '#15803d'\n              },\n              children: typeof userResult.correctAnswers === 'number' ? userResult.correctAnswers : 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 771,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 769,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-1\",\n            children: [/*#__PURE__*/_jsxDEV(TbX, {\n              className: \"w-3 h-3\",\n              style: {\n                color: '#dc2626'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 776,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-bold\",\n              style: {\n                color: '#b91c1c'\n              },\n              children: (((_quiz$questions = quiz.questions) === null || _quiz$questions === void 0 ? void 0 : _quiz$questions.length) || 0) - (typeof userResult.correctAnswers === 'number' ? userResult.correctAnswers : 0)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 777,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 775,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 762,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-1 rounded-lg py-1 px-2 border shadow-md\",\n          style: {\n            background: 'linear-gradient(to bottom right, #fef3c7, #fed7aa)',\n            borderColor: '#fde047'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm\",\n            children: \"\\u2B50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 791,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-bold\",\n            style: {\n              color: '#92400e'\n            },\n            children: userResult.xpEarned || userResult.points || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 792,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 784,\n          columnNumber: 13\n        }, this), userResult.timeTaken && userResult.timeTaken > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-1 rounded-lg py-1 px-2 border shadow-md\",\n          style: {\n            background: 'linear-gradient(to bottom right, #e9d5ff, #f3e8ff)',\n            borderColor: '#c4b5fd'\n          },\n          children: [/*#__PURE__*/_jsxDEV(TbClock, {\n            className: \"w-3 h-3\",\n            style: {\n              color: '#9333ea'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 806,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-bold\",\n            style: {\n              color: '#7c3aed'\n            },\n            children: formatCompletionTime(userResult.timeTaken)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 807,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 799,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 760,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 710,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 817,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex gap-2 mt-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => onStart(quiz),\n        className: \"flex-1 flex items-center justify-center gap-1 px-3 py-2 rounded-lg font-bold transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105 active:scale-95 text-white\",\n        style: {\n          background: userResult ? 'linear-gradient(to right, #f97316, #ef4444)' : 'linear-gradient(to right, #3b82f6, #8b5cf6)',\n          fontSize: '13px',\n          minHeight: '36px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(TbPlayerPlay, {\n          className: \"w-3 h-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 832,\n          columnNumber: 11\n        }, this), userResult ? isKiswahili ? '🔄 Rudia Mtihani' : '🔄 Retake Quiz' : isKiswahili ? '🚀 Anza Mtihani' : '🚀 Start Quiz']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 821,\n        columnNumber: 9\n      }, this), userResult && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => onView(quiz),\n        className: \"px-3 py-2 rounded-lg transition-all duration-200 font-bold transform hover:scale-105 active:scale-95 shadow-md hover:shadow-lg text-white\",\n        style: {\n          background: 'linear-gradient(to right, #fbbf24, #f97316)',\n          fontSize: '13px',\n          minHeight: '36px'\n        },\n        title: \"View Results\",\n        children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n          className: \"w-3 h-3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 848,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 838,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 819,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 541,\n    columnNumber: 5\n  }, this);\n};\n_s2(QuizCard, \"2MgnpFXjRQGKuiASnJR9tgxmyQ8=\", false, function () {\n  return [useLanguage];\n});\n_c2 = QuizCard;\nexport default Quiz;\nvar _c, _c2;\n$RefreshReg$(_c, \"Quiz\");\n$RefreshReg$(_c2, \"QuizCard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "startTransition", "useNavigate", "useDispatch", "useSelector", "message", "useLanguage", "TbSearch", "Tb<PERSON><PERSON>er", "TbClock", "TbQuestionMark", "TbTrophy", "TbPlayerPlay", "TbBrain", "TbTarget", "TbCheck", "TbX", "TbStar", "TbHome", "TbBolt", "getAllExams", "getAllReportsByUser", "HideLoading", "ShowLoading", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Quiz", "_s", "exams", "setExams", "filteredExams", "setFilteredExams", "searchTerm", "setSearchTerm", "localStorage", "getItem", "selectedClass", "setSelectedClass", "userResults", "setUserResults", "loading", "setLoading", "lastRefresh", "setLastRefresh", "navigate", "dispatch", "user", "state", "t", "isKiswahili", "getClassName", "clearAllQuizCaches", "allLevels", "for<PERSON>ach", "level", "removeItem", "document", "body", "classList", "add", "remove", "clearSearchAndFilters", "handleSearchChange", "value", "setItem", "handleClassChange", "getUserResults", "_id", "response", "userId", "success", "resultsMap", "data", "report", "_report$exam", "examId", "exam", "result", "Date", "createdAt", "verdict", "percentage", "correctAnswers", "wrongAnswers", "totalQuestions", "obtainedMarks", "totalMarks", "score", "points", "xpEarned", "xpGained", "timeTaken", "completedAt", "error", "console", "getExams", "log", "userLevel", "cache<PERSON>ey", "cacheTimeKey", "cachedExams", "cacheTime", "now", "parseInt", "cached", "JSON", "parse", "length", "userLevelExams", "filter", "toLowerCase", "sortedExams", "sort", "a", "b", "stringify", "toString", "class", "String", "handleRankingUpdate", "handleNewExam", "handleWindowFocus", "window", "addEventListener", "removeEventListener", "filtered", "searchLower", "searchableFields", "name", "subject", "topic", "description", "category", "questions", "map", "q", "questionText", "some", "field", "includes", "availableClasses", "Set", "e", "Boolean", "handleQuizStart", "quiz", "objectIdRegex", "test", "handleQuizView", "userResult", "info", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "jsx", "global", "toLocaleTimeString", "type", "placeholder", "onChange", "target", "onClick", "title", "style", "marginTop", "index", "QuizCard", "showResults", "onStart", "onView", "_c", "_s2", "_quiz$questions", "formatTime", "seconds", "minutes", "Math", "round", "formatCompletionTime", "timeInSeconds", "totalSeconds", "isNaN", "floor", "background", "border", "boxShadow", "minHeight", "innerWidth", "height", "color", "textShadow", "lineHeight", "fontSize", "backgroundColor", "borderColor", "Array", "isArray", "toLocaleDateString", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/index.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, startTransition } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { message } from 'antd';\r\nimport { useLanguage } from '../../../contexts/LanguageContext';\r\nimport {\r\n  TbSearch,\r\n  TbFilter,\r\n  TbClock,\r\n  TbQuestionMark,\r\n  TbTrophy,\r\n  TbPlayerPlay,\r\n  TbBrain,\r\n  TbTarget,\r\n  TbCheck,\r\n  TbX,\r\n  TbStar,\r\n  TbHome,\r\n  TbBolt\r\n} from 'react-icons/tb';\r\nimport { getAllExams } from '../../../apicalls/exams';\r\nimport { getAllReportsByUser } from '../../../apicalls/reports';\r\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\r\nimport './animations.css';\r\n\r\nconst Quiz = () => {\r\n  const [exams, setExams] = useState([]);\r\n  const [filteredExams, setFilteredExams] = useState([]);\r\n  const [searchTerm, setSearchTerm] = useState(() => {\r\n    // Restore search term from localStorage\r\n    return localStorage.getItem('quiz-search-term') || '';\r\n  });\r\n  const [selectedClass, setSelectedClass] = useState(() => {\r\n    // Restore selected class from localStorage\r\n    return localStorage.getItem('quiz-selected-class') || '';\r\n  });\r\n  const [userResults, setUserResults] = useState({});\r\n  const [loading, setLoading] = useState(true);\r\n  const [lastRefresh, setLastRefresh] = useState(null);\r\n  const navigate = useNavigate();\r\n  const dispatch = useDispatch();\r\n  const { user } = useSelector((state) => state.user);\r\n  const { t, isKiswahili, getClassName } = useLanguage();\r\n\r\n  // Function to clear all quiz caches\r\n  const clearAllQuizCaches = () => {\r\n    const allLevels = ['primary', 'secondary', 'advance'];\r\n    allLevels.forEach(level => {\r\n      localStorage.removeItem(`user_exams_cache_${level}`);\r\n      localStorage.removeItem(`user_exams_cache_time_${level}`);\r\n    });\r\n    // Also clear old cache keys for backward compatibility\r\n    localStorage.removeItem('user_exams_cache');\r\n    localStorage.removeItem('user_exams_cache_time');\r\n  };\r\n\r\n  // Remove white space on component mount and handle cleanup\r\n  useEffect(() => {\r\n    // Add class to body to remove spacing\r\n    document.body.classList.add('quiz-page-active');\r\n\r\n    // Cleanup on unmount (when user navigates away or logs out)\r\n    return () => {\r\n      document.body.classList.remove('quiz-page-active');\r\n      // Note: We don't clear localStorage here to maintain search persistence\r\n      // Search is only cleared on manual refresh or explicit clear action\r\n    };\r\n  }, []);\r\n\r\n  // Clear search when user changes (logout scenario)\r\n  useEffect(() => {\r\n    if (!user) {\r\n      clearSearchAndFilters();\r\n    }\r\n  }, [user]);\r\n\r\n  // Handle search term change with localStorage persistence\r\n  const handleSearchChange = (value) => {\r\n    setSearchTerm(value);\r\n    localStorage.setItem('quiz-search-term', value);\r\n  };\r\n\r\n  // Handle class selection change with localStorage persistence\r\n  const handleClassChange = (value) => {\r\n    setSelectedClass(value);\r\n    localStorage.setItem('quiz-selected-class', value);\r\n  };\r\n\r\n  // Clear search and filters (for manual refresh)\r\n  const clearSearchAndFilters = () => {\r\n    setSearchTerm('');\r\n    setSelectedClass('');\r\n    localStorage.removeItem('quiz-search-term');\r\n    localStorage.removeItem('quiz-selected-class');\r\n  };\r\n\r\n  const getUserResults = useCallback(async () => {\r\n    try {\r\n      if (!user?._id) return;\r\n\r\n      const response = await getAllReportsByUser({ userId: user._id });\r\n\r\n      if (response.success) {\r\n        const resultsMap = {};\r\n        response.data.forEach(report => {\r\n          const examId = report.exam?._id;\r\n          if (!examId || !report.result) return;\r\n\r\n          // Extract data from the result object\r\n          const result = report.result;\r\n\r\n          if (!resultsMap[examId] || new Date(report.createdAt) > new Date(resultsMap[examId].createdAt)) {\r\n            resultsMap[examId] = {\r\n              verdict: result.verdict,\r\n              percentage: result.percentage,\r\n              correctAnswers: result.correctAnswers,\r\n              wrongAnswers: result.wrongAnswers,\r\n              totalQuestions: result.totalQuestions,\r\n              obtainedMarks: result.obtainedMarks,\r\n              totalMarks: result.totalMarks,\r\n              score: result.score,\r\n              points: result.points,\r\n              xpEarned: result.xpEarned || result.points || result.xpGained || 0,\r\n              timeTaken: report.timeTaken,\r\n              completedAt: report.createdAt,\r\n            };\r\n          }\r\n        });\r\n        setUserResults(resultsMap);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching user results:', error);\r\n    }\r\n  }, [user?._id]);\r\n\r\n  // Define getExams function to load exams once\r\n  const getExams = useCallback(async () => {\r\n      try {\r\n        // Safety check: ensure user exists before proceeding\r\n        if (!user) {\r\n          console.log(\"User not loaded yet, skipping exam fetch\");\r\n          return;\r\n        }\r\n\r\n        // Level-specific cache to prevent cross-level contamination\r\n        const userLevel = user?.level || 'primary';\r\n        const cacheKey = `user_exams_cache_${userLevel}`;\r\n        const cacheTimeKey = `user_exams_cache_time_${userLevel}`;\r\n\r\n        // Clear caches for other levels\r\n        const allLevels = ['primary', 'secondary', 'advance'];\r\n        allLevels.forEach(level => {\r\n          if (level !== userLevel) {\r\n            localStorage.removeItem(`user_exams_cache_${level}`);\r\n            localStorage.removeItem(`user_exams_cache_time_${level}`);\r\n          }\r\n        });\r\n\r\n        // Check level-specific cache first\r\n        const cachedExams = localStorage.getItem(cacheKey);\r\n        const cacheTime = localStorage.getItem(cacheTimeKey);\r\n        const now = Date.now();\r\n\r\n        // Use cache if less than 10 minutes old (increased cache time)\r\n        if (cachedExams && cacheTime && (now - parseInt(cacheTime)) < 600000) {\r\n          const cached = JSON.parse(cachedExams);\r\n          setExams(cached);\r\n          setLastRefresh(new Date(parseInt(cacheTime)));\r\n          setLoading(false);\r\n          console.log(`📋 Using cached exams for ${userLevel} level:`, cached.length);\r\n          return;\r\n        }\r\n\r\n        dispatch(ShowLoading());\r\n        const response = await getAllExams();\r\n        dispatch(HideLoading());\r\n\r\n        if (response.success) {\r\n          console.log('Raw exams from API:', response.data.length);\r\n          console.log('User level:', user?.level);\r\n\r\n          // Filter exams by user's level with proper null checks\r\n          const userLevelExams = response.data.filter(exam => {\r\n            if (!exam.level || !user || !user.level) return false;\r\n            return exam.level.toLowerCase() === user.level.toLowerCase();\r\n          });\r\n\r\n          console.log('User level exams after filtering:', userLevelExams.length);\r\n          const sortedExams = userLevelExams.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\r\n          setExams(sortedExams);\r\n          setLastRefresh(new Date());\r\n\r\n          // Cache the exams data with level-specific key\r\n          localStorage.setItem(cacheKey, JSON.stringify(sortedExams));\r\n          localStorage.setItem(cacheTimeKey, Date.now().toString());\r\n\r\n          // Set default class filter to user's class\r\n          if (user?.class) {\r\n            setSelectedClass(String(user.class));\r\n          }\r\n        } else {\r\n          message.error(response.message);\r\n        }\r\n      } catch (error) {\r\n        dispatch(HideLoading());\r\n        message.error(error.message);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n  }, [dispatch, user]);\r\n\r\n  useEffect(() => {\r\n    // Clear ALL caches when component mounts to ensure fresh data\r\n    clearAllQuizCaches();\r\n\r\n    getExams(); // Initial load only\r\n    getUserResults();\r\n  }, [getExams, getUserResults]);\r\n\r\n  // Real-time updates for quiz completion and new exams\r\n  useEffect(() => {\r\n    // Listen for real-time updates from quiz completion\r\n    const handleRankingUpdate = () => {\r\n      console.log('🔄 Quiz listing - refreshing data after quiz completion...');\r\n      getUserResults(); // Refresh user results to show updated XP\r\n    };\r\n\r\n    // Listen for new exam creation events\r\n    const handleNewExam = () => {\r\n      console.log('🆕 New exam created - refreshing user results only...');\r\n      if (user) {\r\n        getUserResults(); // Only refresh user results, keep filters intact\r\n      }\r\n    };\r\n\r\n    // Listen for window focus to refresh user results only (not exams)\r\n    const handleWindowFocus = () => {\r\n      console.log('🎯 Quiz listing - window focused, refreshing user results...');\r\n      getUserResults(); // Only refresh user results, keep filters intact\r\n    };\r\n\r\n    window.addEventListener('rankingUpdate', handleRankingUpdate);\r\n    window.addEventListener('focus', handleWindowFocus);\r\n    window.addEventListener('newExamCreated', handleNewExam);\r\n\r\n    return () => {\r\n      window.removeEventListener('rankingUpdate', handleRankingUpdate);\r\n      window.removeEventListener('focus', handleWindowFocus);\r\n      window.removeEventListener('newExamCreated', handleNewExam);\r\n    };\r\n  }, []);\r\n\r\n\r\n\r\n  useEffect(() => {\r\n    console.log('Filtering exams:', { exams: exams.length, searchTerm, selectedClass });\r\n    let filtered = exams;\r\n    if (searchTerm) {\r\n      const searchLower = searchTerm.toLowerCase();\r\n      filtered = filtered.filter(exam => {\r\n        // Search in multiple fields for comprehensive results\r\n        const searchableFields = [\r\n          exam.name,\r\n          exam.subject,\r\n          exam.topic,\r\n          exam.description,\r\n          exam.category,\r\n          exam.level,\r\n          // Search in questions if available\r\n          ...(exam.questions || []).map(q => q.questionText),\r\n          ...(exam.questions || []).map(q => q.subject),\r\n          ...(exam.questions || []).map(q => q.topic)\r\n        ];\r\n\r\n        return searchableFields.some(field =>\r\n          field && field.toString().toLowerCase().includes(searchLower)\r\n        );\r\n      });\r\n    }\r\n    if (selectedClass) {\r\n      filtered = filtered.filter(exam => String(exam.class) === String(selectedClass));\r\n    }\r\n    filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\r\n    console.log('Filtered exams result:', filtered.length);\r\n    setFilteredExams(filtered);\r\n  }, [exams, searchTerm, selectedClass]);\r\n\r\n  const availableClasses = [...new Set(exams.map(e => e.class).filter(Boolean))].sort();\r\n\r\n  const handleQuizStart = (quiz) => {\r\n    if (!quiz || !quiz._id) {\r\n      message.error('Invalid quiz selected. Please try again.');\r\n      return;\r\n    }\r\n\r\n    // Validate MongoDB ObjectId format (24 character hex string)\r\n    const objectIdRegex = /^[0-9a-fA-F]{24}$/;\r\n    if (!objectIdRegex.test(quiz._id)) {\r\n      message.error('Invalid quiz ID format. Please try again.');\r\n      return;\r\n    }\r\n\r\n    startTransition(() => {\r\n      navigate(`/quiz/${quiz._id}/play`);\r\n    });\r\n  };\r\n\r\n\r\n\r\n  const handleQuizView = (quiz) => {\r\n    if (!quiz || !quiz._id) {\r\n      message.error('Invalid quiz selected. Please try again.');\r\n      return;\r\n    }\r\n    // Check if user has attempted this quiz\r\n    const userResult = userResults[quiz._id];\r\n    if (!userResult) {\r\n      message.info('You need to attempt this quiz first to view results.');\r\n      return;\r\n    }\r\n    startTransition(() => {\r\n      navigate(`/quiz/${quiz._id}/result`);\r\n    });\r\n  };\r\n\r\n\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"></div>\r\n          <p className=\"text-gray-600\">{isKiswahili ? 'Inapakia mitihani...' : 'Loading quizzes...'}</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {/* Custom CSS for compact header and optimized layout */}\r\n      <style jsx global>{`\r\n        /* Completely remove all white space from ProtectedRoute main wrapper */\r\n        body.quiz-page-active main {\r\n          padding: 0 !important;\r\n          margin: 0 !important;\r\n        }\r\n\r\n        /* Remove all spacing from layout containers */\r\n        body.quiz-page-active .safe-content-animation {\r\n          padding: 0 !important;\r\n          margin: 0 !important;\r\n        }\r\n\r\n        /* Remove any inherited spacing from all parent containers */\r\n        body.quiz-page-active main > div,\r\n        body.quiz-page-active main * {\r\n          margin-top: 0 !important;\r\n        }\r\n\r\n        /* Ensure Quiz container starts immediately */\r\n        .quiz-container-immediate {\r\n          margin-top: 0 !important;\r\n          padding-top: 0 !important;\r\n        }\r\n        .quiz-grid {\r\n          gap: 1rem !important;\r\n          margin-top: 1rem !important;\r\n        }\r\n        @media (min-width: 640px) {\r\n          .quiz-grid {\r\n            gap: 1.25rem !important;\r\n            margin-top: 1rem !important;\r\n          }\r\n        }\r\n        @media (min-width: 1024px) {\r\n          .quiz-grid {\r\n            gap: 1.5rem !important;\r\n          }\r\n        }\r\n      `}</style>\r\n\r\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 quiz-container-immediate\">\r\n        <div className=\"container mx-auto px-3 sm:px-4 lg:px-8 pt-3 pb-3 sm:pb-4 lg:pb-6\">\r\n\r\n\r\n        {/* Compact Search and Filter with User Level */}\r\n        <div className=\"max-w-4xl mx-auto mb-3 sm:mb-4 opacity-100\">\r\n          <div className=\"bg-white rounded-xl shadow-md p-3 sm:p-4\">\r\n            {/* User Level and Quiz Count */}\r\n            <div className=\"flex flex-col sm:flex-row items-center justify-between mb-3 pb-3 border-b border-gray-100\">\r\n              <div className=\"flex items-center gap-4 text-sm text-gray-600\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <div className=\"w-2.5 h-2.5 bg-blue-500 rounded-full\"></div>\r\n                  <span className=\"font-medium\">Level: {user?.level || 'All Levels'}</span>\r\n                </div>\r\n                <div className=\"flex items-center gap-2\">\r\n                  <div className=\"w-2.5 h-2.5 bg-green-500 rounded-full\"></div>\r\n                  <span>{filteredExams.length} Available Quizzes</span>\r\n                </div>\r\n              </div>\r\n              {lastRefresh && (\r\n                <div className=\"flex items-center gap-2 text-xs text-gray-400 mt-2 sm:mt-0\">\r\n                  <div className=\"w-2 h-2 bg-gray-400 rounded-full\"></div>\r\n                  <span>Updated: {lastRefresh.toLocaleTimeString()}</span>\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Search, Filter, and Refresh Controls */}\r\n            <div className=\"flex flex-col sm:flex-row gap-2 sm:gap-3\">\r\n              <div className=\"flex-1 relative\">\r\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                  <TbSearch className=\"h-4 w-4 text-gray-400\" />\r\n                </div>\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"Search quizzes by subject, topic, or name...\"\r\n                  value={searchTerm}\r\n                  onChange={(e) => handleSearchChange(e.target.value)}\r\n                  className=\"block w-full pl-9 pr-3 py-2 border border-gray-300 rounded-lg bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all text-sm\"\r\n                />\r\n              </div>\r\n              <div className=\"w-36 sm:w-40\">\r\n                <div className=\"relative\">\r\n                  <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                    <TbFilter className=\"h-4 w-4 text-gray-400\" />\r\n                  </div>\r\n                  <select\r\n                    value={selectedClass}\r\n                    onChange={(e) => handleClassChange(e.target.value)}\r\n                    className=\"block w-full pl-9 pr-8 py-2 border border-gray-300 rounded-lg bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all appearance-none text-sm\"\r\n                  >\r\n                    <option value=\"\">All Classes</option>\r\n                    {availableClasses.map((className) => (\r\n                      <option key={className} value={className}>\r\n                        {user?.level === 'primary' ? `Class ${className}` : className}\r\n                      </option>\r\n                    ))}\r\n                  </select>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Clear Filters Button */}\r\n              {(searchTerm || selectedClass) && (\r\n                <button\r\n                  onClick={clearSearchAndFilters}\r\n                  className=\"flex items-center justify-center px-3 py-2 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-lg hover:from-gray-600 hover:to-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-all shadow-md\"\r\n                  title=\"Clear all search and filters\"\r\n                >\r\n                  <TbX className=\"h-4 w-4\" />\r\n                  <span className=\"ml-1.5 hidden sm:inline text-sm\">Clear</span>\r\n                </button>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Quiz Grid */}\r\n        <div className=\"opacity-100\" style={{ marginTop: '0.5rem' }}>\r\n\r\n\r\n          {filteredExams.length === 0 ? (\r\n            <div className=\"text-center py-8 sm:py-12\">\r\n              <div className=\"bg-white rounded-2xl shadow-lg p-6 sm:p-8 max-w-md mx-auto\">\r\n                <TbTarget className=\"w-12 h-12 sm:w-16 sm:h-16 text-gray-400 mx-auto mb-4\" />\r\n                <h3 className=\"text-lg sm:text-xl font-semibold text-gray-900 mb-2\">No Quizzes Found</h3>\r\n                <p className=\"text-gray-600 text-sm sm:text-base\">\r\n                  {searchTerm || selectedClass\r\n                    ? \"Try adjusting your search or filter criteria.\"\r\n                    : \"No quizzes are available for your level at the moment.\"\r\n                  }\r\n                </p>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"quiz-grid\">\r\n              {filteredExams.map((quiz, index) => (\r\n                <QuizCard\r\n                  key={quiz._id}\r\n                  quiz={quiz}\r\n                  userResult={userResults[quiz._id]}\r\n                  showResults={true}\r\n                  onStart={handleQuizStart}\r\n                  onView={() => handleQuizView(quiz)}\r\n                  index={index}\r\n                />\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n    </>\r\n  );\r\n};\r\n\r\n// Simple QuizCard component without Framer Motion\r\nconst QuizCard = ({ quiz, userResult, onStart, onView, index }) => {\r\n  const { isKiswahili, getClassName } = useLanguage();\r\n\r\n  const formatTime = (seconds) => {\r\n    if (!seconds) return 'N/A';\r\n    const minutes = Math.round(seconds / 60);\r\n    return `${minutes} ${isKiswahili ? 'dak' : 'min'}`;\r\n  };\r\n\r\n  const formatCompletionTime = (timeInSeconds) => {\r\n    // Handle different possible time formats\r\n    if (!timeInSeconds && timeInSeconds !== 0) return '0s';\r\n\r\n    let totalSeconds = timeInSeconds;\r\n\r\n    // If it's a string, try to parse it\r\n    if (typeof timeInSeconds === 'string') {\r\n      totalSeconds = parseInt(timeInSeconds, 10);\r\n    }\r\n\r\n    // If it's still not a valid number, return 0s\r\n    if (isNaN(totalSeconds) || totalSeconds < 0) return '0s';\r\n\r\n    const minutes = Math.floor(totalSeconds / 60);\r\n    const seconds = totalSeconds % 60;\r\n\r\n    if (minutes > 0) {\r\n      return `${minutes}m ${seconds}s`;\r\n    }\r\n    return `${timeInSeconds}s`;\r\n  };\r\n\r\n  // Safety checks for quiz object\r\n  if (!quiz || typeof quiz !== 'object') {\r\n    return (\r\n      <div className=\"bg-white rounded-2xl shadow-lg p-6 border border-gray-100\">\r\n        <p className=\"text-gray-500\">Invalid quiz data</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className=\"rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-3 transform hover:scale-105 opacity-100 relative flex flex-col\"\r\n      style={{\r\n        background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%)',\r\n        border: userResult\r\n          ? (userResult.verdict === 'Pass' ? '2px solid #10b981' : '2px solid #ef4444')\r\n          : '2px solid #3b82f6',\r\n        boxShadow: userResult\r\n          ? (userResult.verdict === 'Pass'\r\n              ? '0 8px 20px rgba(16, 185, 129, 0.3)'\r\n              : '0 8px 20px rgba(239, 68, 68, 0.3)')\r\n          : '0 8px 20px rgba(59, 130, 246, 0.3)',\r\n        minHeight: window.innerWidth <= 768 ? '240px' : '320px',\r\n        height: 'auto'\r\n      }}\r\n    >\r\n      {/* Quiz Title - At Top */}\r\n      <div className=\"mb-2 text-center\">\r\n        <h3\r\n          className=\"font-bold mb-1 line-clamp-2\"\r\n          style={{\r\n            color: '#1f2937',\r\n            textShadow: '0 1px 2px rgba(0,0,0,0.1)',\r\n            lineHeight: '1.1',\r\n            fontSize: window.innerWidth <= 768 ? '14px' : '16px'\r\n          }}\r\n        >\r\n          {typeof quiz.name === 'string' ? quiz.name : 'Untitled Quiz'}\r\n        </h3>\r\n      </div>\r\n\r\n      {/* Status Tags - Centered */}\r\n      <div className=\"mb-2 text-center\">\r\n        {userResult ? (\r\n          <div className=\"flex items-center justify-center gap-1\">\r\n            <div\r\n              className=\"px-2 py-1 rounded-full text-xs font-bold text-white shadow-md\"\r\n              style={{\r\n                backgroundColor: userResult.verdict === 'Pass' ? '#10b981' : '#ef4444',\r\n                fontSize: window.innerWidth <= 768 ? '9px' : '10px'\r\n              }}\r\n            >\r\n              {userResult.verdict === 'Pass' ? '✅ PASSED' : '❌ FAILED'}\r\n            </div>\r\n            <div\r\n              className=\"px-2 py-1 rounded-full text-xs font-bold text-center shadow-md\"\r\n              style={{\r\n                backgroundColor: '#ffffff',\r\n                color: '#1f2937',\r\n                fontSize: window.innerWidth <= 768 ? '9px' : '10px'\r\n              }}\r\n            >\r\n              {typeof userResult.percentage === 'number' ? userResult.percentage : 0}%\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          <div\r\n            className=\"px-2 py-1 rounded-full text-xs font-bold text-white shadow-md\"\r\n            style={{\r\n              backgroundColor: '#3b82f6',\r\n              fontSize: window.innerWidth <= 768 ? '9px' : '10px'\r\n            }}\r\n          >\r\n            🆕 NOT ATTEMPTED\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      <div className=\"text-center mb-6\">\r\n        <div className=\"flex-1\">\r\n\r\n          {/* Questions and Duration - Horizontal */}\r\n          <div className=\"flex gap-1 mb-2 justify-center\">\r\n            <div\r\n              className=\"flex items-center gap-1 rounded-lg py-1 px-2 border shadow-sm\"\r\n              style={{\r\n                background: 'linear-gradient(to right, #eff6ff, #e0e7ff)',\r\n                borderColor: '#bfdbfe'\r\n              }}\r\n            >\r\n              <TbQuestionMark className=\"w-3 h-3\" style={{ color: '#2563eb' }} />\r\n              <span\r\n                className=\"font-bold\"\r\n                style={{\r\n                  color: '#1e40af',\r\n                  fontSize: window.innerWidth <= 768 ? '11px' : '12px'\r\n                }}\r\n              >\r\n                {Array.isArray(quiz.questions) ? quiz.questions.length : 0}\r\n              </span>\r\n            </div>\r\n            <div\r\n              className=\"flex items-center gap-1 rounded-lg py-1 px-2 border shadow-sm\"\r\n              style={{\r\n                background: 'linear-gradient(to right, #fdf4ff, #fce7f3)',\r\n                borderColor: '#e9d5ff'\r\n              }}\r\n            >\r\n              <TbClock className=\"w-3 h-3\" style={{ color: '#9333ea' }} />\r\n              <span\r\n                className=\"font-bold\"\r\n                style={{\r\n                  color: '#7c3aed',\r\n                  fontSize: window.innerWidth <= 768 ? '11px' : '12px'\r\n                }}\r\n              >\r\n                3m\r\n              </span>\r\n            </div>\r\n          </div>\r\n\r\n\r\n\r\n          <div className=\"flex items-center justify-center gap-1 flex-wrap mb-2\">\r\n            {/* Level Tag */}\r\n            <span\r\n              className=\"inline-block px-1 py-1 font-bold rounded-full text-white shadow-sm\"\r\n              style={{\r\n                background: 'linear-gradient(to right, #8b5cf6, #7c3aed)',\r\n                fontSize: window.innerWidth <= 768 ? '8px' : '10px'\r\n              }}\r\n            >\r\n              🎯{quiz.level || 'Primary'}\r\n            </span>\r\n\r\n            {/* Class Tag */}\r\n            <span\r\n              className=\"inline-block px-1 py-1 font-bold rounded-full text-white shadow-sm\"\r\n              style={{\r\n                background: 'linear-gradient(to right, #4ade80, #3b82f6)',\r\n                fontSize: window.innerWidth <= 768 ? '8px' : '10px'\r\n              }}\r\n            >\r\n              📖{typeof quiz.class === 'string' || typeof quiz.class === 'number' ?\r\n                (quiz.level === 'primary' || quiz.level === 'primary_kiswahili' ?\r\n                  (isKiswahili ? `Darasa la ${quiz.class}` : `Class ${quiz.class}`) :\r\n                  quiz.class) : 'N/A'}\r\n            </span>\r\n\r\n            {/* Category Tag */}\r\n            <span\r\n              className=\"inline-block px-1 py-1 font-bold rounded-full text-white shadow-sm\"\r\n              style={{\r\n                background: 'linear-gradient(to right, #f97316, #ea580c)',\r\n                fontSize: window.innerWidth <= 768 ? '8px' : '10px'\r\n              }}\r\n            >\r\n              📂{quiz.category || 'General'}\r\n            </span>\r\n\r\n            {/* Topic Tag - Show actual topic or \"General\" if not defined */}\r\n            <span\r\n              className=\"inline-block px-1 py-1 font-bold rounded-full text-white shadow-sm\"\r\n              style={{\r\n                background: quiz.topic && quiz.topic !== 'General' && quiz.topic !== ''\r\n                  ? 'linear-gradient(to right, #10b981, #059669)'\r\n                  : 'linear-gradient(to right, #6b7280, #4b5563)',\r\n                fontSize: window.innerWidth <= 768 ? '8px' : '10px'\r\n              }}\r\n            >\r\n              📚{quiz.topic || (isKiswahili ? 'Jumla' : 'General')}\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n\r\n\r\n      {userResult && typeof userResult === 'object' && (\r\n        <div\r\n          className=\"mb-2 p-2 rounded-lg border shadow-md\"\r\n          style={{\r\n            background: userResult.verdict === 'Pass'\r\n              ? 'linear-gradient(to bottom right, #f0fdf4, #ecfdf5)'\r\n              : 'linear-gradient(to bottom right, #fef2f2, #fdf2f8)',\r\n            borderColor: userResult.verdict === 'Pass' ? '#86efac' : '#fca5a5'\r\n          }}\r\n        >\r\n          <div className=\"flex items-center justify-between mb-3\">\r\n            <div className=\"flex items-center gap-3\">\r\n              {userResult.verdict === 'Pass' ? (\r\n                <div\r\n                  className=\"w-10 h-10 rounded-full flex items-center justify-center shadow-lg border-2 animate-pulse\"\r\n                  style={{\r\n                    background: 'linear-gradient(to right, #10b981, #059669)',\r\n                    borderColor: '#86efac'\r\n                  }}\r\n                >\r\n                  <TbCheck className=\"w-6 h-6 font-bold\" style={{ color: '#ffffff' }} />\r\n                </div>\r\n              ) : (\r\n                <div\r\n                  className=\"w-10 h-10 rounded-full flex items-center justify-center shadow-lg border-2 animate-pulse\"\r\n                  style={{\r\n                    background: 'linear-gradient(to right, #ef4444, #dc2626)',\r\n                    borderColor: '#fca5a5'\r\n                  }}\r\n                >\r\n                  <TbX className=\"w-6 h-6 font-bold\" style={{ color: '#ffffff' }} />\r\n                </div>\r\n              )}\r\n              <div>\r\n                <span className=\"text-lg font-bold\" style={{ color: '#1f2937' }}>🏆 Last Result</span>\r\n                <div className=\"text-sm\" style={{ color: '#6b7280' }}>\r\n                  {new Date(userResult.completedAt || userResult.createdAt || Date.now()).toLocaleDateString()}\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <span\r\n              className=\"text-3xl font-bold shadow-lg\"\r\n              style={{\r\n                color: userResult.verdict === 'Pass' ? '#059669' : '#dc2626'\r\n              }}\r\n            >\r\n              {typeof userResult.percentage === 'number' ? userResult.percentage : 0}%\r\n            </span>\r\n          </div>\r\n\r\n          {/* Horizontal Layout for Results */}\r\n          <div className=\"flex gap-1 justify-center flex-wrap\">\r\n            {/* Correct/Wrong - Horizontal */}\r\n            <div\r\n              className=\"flex items-center gap-1 rounded-lg py-1 px-2 border shadow-md\"\r\n              style={{\r\n                background: 'linear-gradient(to right, #dcfce7, #fecaca)',\r\n                borderColor: '#86efac'\r\n              }}\r\n            >\r\n              <div className=\"flex items-center gap-1\">\r\n                <TbCheck className=\"w-3 h-3\" style={{ color: '#16a34a' }} />\r\n                <span className=\"text-sm font-bold\" style={{ color: '#15803d' }}>\r\n                  {typeof userResult.correctAnswers === 'number' ? userResult.correctAnswers : 0}\r\n                </span>\r\n              </div>\r\n              <div className=\"flex items-center gap-1\">\r\n                <TbX className=\"w-3 h-3\" style={{ color: '#dc2626' }} />\r\n                <span className=\"text-sm font-bold\" style={{ color: '#b91c1c' }}>\r\n                  {(quiz.questions?.length || 0) - (typeof userResult.correctAnswers === 'number' ? userResult.correctAnswers : 0)}\r\n                </span>\r\n              </div>\r\n            </div>\r\n\r\n            {/* XP */}\r\n            <div\r\n              className=\"flex items-center gap-1 rounded-lg py-1 px-2 border shadow-md\"\r\n              style={{\r\n                background: 'linear-gradient(to bottom right, #fef3c7, #fed7aa)',\r\n                borderColor: '#fde047'\r\n              }}\r\n            >\r\n              <span className=\"text-sm\">⭐</span>\r\n              <span className=\"text-sm font-bold\" style={{ color: '#92400e' }}>\r\n                {userResult.xpEarned || userResult.points || 0}\r\n              </span>\r\n            </div>\r\n\r\n            {/* Time - Horizontal if available */}\r\n            {userResult.timeTaken && userResult.timeTaken > 0 && (\r\n              <div\r\n                className=\"flex items-center gap-1 rounded-lg py-1 px-2 border shadow-md\"\r\n                style={{\r\n                  background: 'linear-gradient(to bottom right, #e9d5ff, #f3e8ff)',\r\n                  borderColor: '#c4b5fd'\r\n                }}\r\n              >\r\n                <TbClock className=\"w-3 h-3\" style={{ color: '#9333ea' }} />\r\n                <span className=\"text-sm font-bold\" style={{ color: '#7c3aed' }}>\r\n                  {formatCompletionTime(userResult.timeTaken)}\r\n                </span>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Spacer to push buttons to bottom */}\r\n      <div className=\"flex-1\"></div>\r\n\r\n      <div className=\"flex gap-2 mt-3\">\r\n        {/* Main Action Button - Bigger for retake */}\r\n        <button\r\n          onClick={() => onStart(quiz)}\r\n          className=\"flex-1 flex items-center justify-center gap-1 px-3 py-2 rounded-lg font-bold transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105 active:scale-95 text-white\"\r\n          style={{\r\n            background: userResult\r\n              ? 'linear-gradient(to right, #f97316, #ef4444)'\r\n              : 'linear-gradient(to right, #3b82f6, #8b5cf6)',\r\n            fontSize: '13px',\r\n            minHeight: '36px'\r\n          }}\r\n        >\r\n          <TbPlayerPlay className=\"w-3 h-3\" />\r\n          {userResult ? (isKiswahili ? '🔄 Rudia Mtihani' : '🔄 Retake Quiz') : (isKiswahili ? '🚀 Anza Mtihani' : '🚀 Start Quiz')}\r\n        </button>\r\n\r\n        {/* Small Trophy Button - Only show when there are results */}\r\n        {userResult && (\r\n          <button\r\n            onClick={() => onView(quiz)}\r\n            className=\"px-3 py-2 rounded-lg transition-all duration-200 font-bold transform hover:scale-105 active:scale-95 shadow-md hover:shadow-lg text-white\"\r\n            style={{\r\n              background: 'linear-gradient(to right, #fbbf24, #f97316)',\r\n              fontSize: '13px',\r\n              minHeight: '36px'\r\n            }}\r\n            title=\"View Results\"\r\n          >\r\n            <TbTrophy className=\"w-3 h-3\" />\r\n          </button>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Quiz;\r\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,eAAe,QAAQ,OAAO;AAChF,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,SACEC,QAAQ,EACRC,QAAQ,EACRC,OAAO,EACPC,cAAc,EACdC,QAAQ,EACRC,YAAY,EACZC,OAAO,EACPC,QAAQ,EACRC,OAAO,EACPC,GAAG,EACHC,MAAM,EACNC,MAAM,EACNC,MAAM,QACD,gBAAgB;AACvB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,mBAAmB,QAAQ,2BAA2B;AAC/D,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACoC,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAC,MAAM;IACjD;IACA,OAAOsC,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC,IAAI,EAAE;EACvD,CAAC,CAAC;EACF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzC,QAAQ,CAAC,MAAM;IACvD;IACA,OAAOsC,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC,IAAI,EAAE;EAC1D,CAAC,CAAC;EACF,MAAM,CAACG,WAAW,EAAEC,cAAc,CAAC,GAAG3C,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD,MAAM,CAAC4C,OAAO,EAAEC,UAAU,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC8C,WAAW,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAMgD,QAAQ,GAAG5C,WAAW,CAAC,CAAC;EAC9B,MAAM6C,QAAQ,GAAG5C,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE6C;EAAK,CAAC,GAAG5C,WAAW,CAAE6C,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE,CAAC;IAAEC,WAAW;IAAEC;EAAa,CAAC,GAAG9C,WAAW,CAAC,CAAC;;EAEtD;EACA,MAAM+C,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,SAAS,GAAG,CAAC,SAAS,EAAE,WAAW,EAAE,SAAS,CAAC;IACrDA,SAAS,CAACC,OAAO,CAACC,KAAK,IAAI;MACzBpB,YAAY,CAACqB,UAAU,CAAE,oBAAmBD,KAAM,EAAC,CAAC;MACpDpB,YAAY,CAACqB,UAAU,CAAE,yBAAwBD,KAAM,EAAC,CAAC;IAC3D,CAAC,CAAC;IACF;IACApB,YAAY,CAACqB,UAAU,CAAC,kBAAkB,CAAC;IAC3CrB,YAAY,CAACqB,UAAU,CAAC,uBAAuB,CAAC;EAClD,CAAC;;EAED;EACA1D,SAAS,CAAC,MAAM;IACd;IACA2D,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,kBAAkB,CAAC;;IAE/C;IACA,OAAO,MAAM;MACXH,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,kBAAkB,CAAC;MAClD;MACA;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA/D,SAAS,CAAC,MAAM;IACd,IAAI,CAACiD,IAAI,EAAE;MACTe,qBAAqB,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAACf,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMgB,kBAAkB,GAAIC,KAAK,IAAK;IACpC9B,aAAa,CAAC8B,KAAK,CAAC;IACpB7B,YAAY,CAAC8B,OAAO,CAAC,kBAAkB,EAAED,KAAK,CAAC;EACjD,CAAC;;EAED;EACA,MAAME,iBAAiB,GAAIF,KAAK,IAAK;IACnC1B,gBAAgB,CAAC0B,KAAK,CAAC;IACvB7B,YAAY,CAAC8B,OAAO,CAAC,qBAAqB,EAAED,KAAK,CAAC;EACpD,CAAC;;EAED;EACA,MAAMF,qBAAqB,GAAGA,CAAA,KAAM;IAClC5B,aAAa,CAAC,EAAE,CAAC;IACjBI,gBAAgB,CAAC,EAAE,CAAC;IACpBH,YAAY,CAACqB,UAAU,CAAC,kBAAkB,CAAC;IAC3CrB,YAAY,CAACqB,UAAU,CAAC,qBAAqB,CAAC;EAChD,CAAC;EAED,MAAMW,cAAc,GAAGpE,WAAW,CAAC,YAAY;IAC7C,IAAI;MACF,IAAI,EAACgD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEqB,GAAG,GAAE;MAEhB,MAAMC,QAAQ,GAAG,MAAMjD,mBAAmB,CAAC;QAAEkD,MAAM,EAAEvB,IAAI,CAACqB;MAAI,CAAC,CAAC;MAEhE,IAAIC,QAAQ,CAACE,OAAO,EAAE;QACpB,MAAMC,UAAU,GAAG,CAAC,CAAC;QACrBH,QAAQ,CAACI,IAAI,CAACnB,OAAO,CAACoB,MAAM,IAAI;UAAA,IAAAC,YAAA;UAC9B,MAAMC,MAAM,IAAAD,YAAA,GAAGD,MAAM,CAACG,IAAI,cAAAF,YAAA,uBAAXA,YAAA,CAAaP,GAAG;UAC/B,IAAI,CAACQ,MAAM,IAAI,CAACF,MAAM,CAACI,MAAM,EAAE;;UAE/B;UACA,MAAMA,MAAM,GAAGJ,MAAM,CAACI,MAAM;UAE5B,IAAI,CAACN,UAAU,CAACI,MAAM,CAAC,IAAI,IAAIG,IAAI,CAACL,MAAM,CAACM,SAAS,CAAC,GAAG,IAAID,IAAI,CAACP,UAAU,CAACI,MAAM,CAAC,CAACI,SAAS,CAAC,EAAE;YAC9FR,UAAU,CAACI,MAAM,CAAC,GAAG;cACnBK,OAAO,EAAEH,MAAM,CAACG,OAAO;cACvBC,UAAU,EAAEJ,MAAM,CAACI,UAAU;cAC7BC,cAAc,EAAEL,MAAM,CAACK,cAAc;cACrCC,YAAY,EAAEN,MAAM,CAACM,YAAY;cACjCC,cAAc,EAAEP,MAAM,CAACO,cAAc;cACrCC,aAAa,EAAER,MAAM,CAACQ,aAAa;cACnCC,UAAU,EAAET,MAAM,CAACS,UAAU;cAC7BC,KAAK,EAAEV,MAAM,CAACU,KAAK;cACnBC,MAAM,EAAEX,MAAM,CAACW,MAAM;cACrBC,QAAQ,EAAEZ,MAAM,CAACY,QAAQ,IAAIZ,MAAM,CAACW,MAAM,IAAIX,MAAM,CAACa,QAAQ,IAAI,CAAC;cAClEC,SAAS,EAAElB,MAAM,CAACkB,SAAS;cAC3BC,WAAW,EAAEnB,MAAM,CAACM;YACtB,CAAC;UACH;QACF,CAAC,CAAC;QACFxC,cAAc,CAACgC,UAAU,CAAC;MAC5B;IACF,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC,EAAE,CAAC/C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqB,GAAG,CAAC,CAAC;;EAEf;EACA,MAAM4B,QAAQ,GAAGjG,WAAW,CAAC,YAAY;IACrC,IAAI;MACF;MACA,IAAI,CAACgD,IAAI,EAAE;QACTgD,OAAO,CAACE,GAAG,CAAC,0CAA0C,CAAC;QACvD;MACF;;MAEA;MACA,MAAMC,SAAS,GAAG,CAAAnD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,KAAK,KAAI,SAAS;MAC1C,MAAM4C,QAAQ,GAAI,oBAAmBD,SAAU,EAAC;MAChD,MAAME,YAAY,GAAI,yBAAwBF,SAAU,EAAC;;MAEzD;MACA,MAAM7C,SAAS,GAAG,CAAC,SAAS,EAAE,WAAW,EAAE,SAAS,CAAC;MACrDA,SAAS,CAACC,OAAO,CAACC,KAAK,IAAI;QACzB,IAAIA,KAAK,KAAK2C,SAAS,EAAE;UACvB/D,YAAY,CAACqB,UAAU,CAAE,oBAAmBD,KAAM,EAAC,CAAC;UACpDpB,YAAY,CAACqB,UAAU,CAAE,yBAAwBD,KAAM,EAAC,CAAC;QAC3D;MACF,CAAC,CAAC;;MAEF;MACA,MAAM8C,WAAW,GAAGlE,YAAY,CAACC,OAAO,CAAC+D,QAAQ,CAAC;MAClD,MAAMG,SAAS,GAAGnE,YAAY,CAACC,OAAO,CAACgE,YAAY,CAAC;MACpD,MAAMG,GAAG,GAAGxB,IAAI,CAACwB,GAAG,CAAC,CAAC;;MAEtB;MACA,IAAIF,WAAW,IAAIC,SAAS,IAAKC,GAAG,GAAGC,QAAQ,CAACF,SAAS,CAAC,GAAI,MAAM,EAAE;QACpE,MAAMG,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACN,WAAW,CAAC;QACtCvE,QAAQ,CAAC2E,MAAM,CAAC;QAChB7D,cAAc,CAAC,IAAImC,IAAI,CAACyB,QAAQ,CAACF,SAAS,CAAC,CAAC,CAAC;QAC7C5D,UAAU,CAAC,KAAK,CAAC;QACjBqD,OAAO,CAACE,GAAG,CAAE,6BAA4BC,SAAU,SAAQ,EAAEO,MAAM,CAACG,MAAM,CAAC;QAC3E;MACF;MAEA9D,QAAQ,CAACxB,WAAW,CAAC,CAAC,CAAC;MACvB,MAAM+C,QAAQ,GAAG,MAAMlD,WAAW,CAAC,CAAC;MACpC2B,QAAQ,CAACzB,WAAW,CAAC,CAAC,CAAC;MAEvB,IAAIgD,QAAQ,CAACE,OAAO,EAAE;QACpBwB,OAAO,CAACE,GAAG,CAAC,qBAAqB,EAAE5B,QAAQ,CAACI,IAAI,CAACmC,MAAM,CAAC;QACxDb,OAAO,CAACE,GAAG,CAAC,aAAa,EAAElD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,KAAK,CAAC;;QAEvC;QACA,MAAMsD,cAAc,GAAGxC,QAAQ,CAACI,IAAI,CAACqC,MAAM,CAACjC,IAAI,IAAI;UAClD,IAAI,CAACA,IAAI,CAACtB,KAAK,IAAI,CAACR,IAAI,IAAI,CAACA,IAAI,CAACQ,KAAK,EAAE,OAAO,KAAK;UACrD,OAAOsB,IAAI,CAACtB,KAAK,CAACwD,WAAW,CAAC,CAAC,KAAKhE,IAAI,CAACQ,KAAK,CAACwD,WAAW,CAAC,CAAC;QAC9D,CAAC,CAAC;QAEFhB,OAAO,CAACE,GAAG,CAAC,mCAAmC,EAAEY,cAAc,CAACD,MAAM,CAAC;QACvE,MAAMI,WAAW,GAAGH,cAAc,CAACI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIpC,IAAI,CAACoC,CAAC,CAACnC,SAAS,CAAC,GAAG,IAAID,IAAI,CAACmC,CAAC,CAAClC,SAAS,CAAC,CAAC;QAChGlD,QAAQ,CAACkF,WAAW,CAAC;QACrBpE,cAAc,CAAC,IAAImC,IAAI,CAAC,CAAC,CAAC;;QAE1B;QACA5C,YAAY,CAAC8B,OAAO,CAACkC,QAAQ,EAAEO,IAAI,CAACU,SAAS,CAACJ,WAAW,CAAC,CAAC;QAC3D7E,YAAY,CAAC8B,OAAO,CAACmC,YAAY,EAAErB,IAAI,CAACwB,GAAG,CAAC,CAAC,CAACc,QAAQ,CAAC,CAAC,CAAC;;QAEzD;QACA,IAAItE,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEuE,KAAK,EAAE;UACfhF,gBAAgB,CAACiF,MAAM,CAACxE,IAAI,CAACuE,KAAK,CAAC,CAAC;QACtC;MACF,CAAC,MAAM;QACLlH,OAAO,CAAC0F,KAAK,CAACzB,QAAQ,CAACjE,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO0F,KAAK,EAAE;MACdhD,QAAQ,CAACzB,WAAW,CAAC,CAAC,CAAC;MACvBjB,OAAO,CAAC0F,KAAK,CAACA,KAAK,CAAC1F,OAAO,CAAC;IAC9B,CAAC,SAAS;MACRsC,UAAU,CAAC,KAAK,CAAC;IACnB;EACJ,CAAC,EAAE,CAACI,QAAQ,EAAEC,IAAI,CAAC,CAAC;EAEpBjD,SAAS,CAAC,MAAM;IACd;IACAsD,kBAAkB,CAAC,CAAC;IAEpB4C,QAAQ,CAAC,CAAC,CAAC,CAAC;IACZ7B,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAC6B,QAAQ,EAAE7B,cAAc,CAAC,CAAC;;EAE9B;EACArE,SAAS,CAAC,MAAM;IACd;IACA,MAAM0H,mBAAmB,GAAGA,CAAA,KAAM;MAChCzB,OAAO,CAACE,GAAG,CAAC,4DAA4D,CAAC;MACzE9B,cAAc,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;;IAED;IACA,MAAMsD,aAAa,GAAGA,CAAA,KAAM;MAC1B1B,OAAO,CAACE,GAAG,CAAC,uDAAuD,CAAC;MACpE,IAAIlD,IAAI,EAAE;QACRoB,cAAc,CAAC,CAAC,CAAC,CAAC;MACpB;IACF,CAAC;;IAED;IACA,MAAMuD,iBAAiB,GAAGA,CAAA,KAAM;MAC9B3B,OAAO,CAACE,GAAG,CAAC,8DAA8D,CAAC;MAC3E9B,cAAc,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;;IAEDwD,MAAM,CAACC,gBAAgB,CAAC,eAAe,EAAEJ,mBAAmB,CAAC;IAC7DG,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAEF,iBAAiB,CAAC;IACnDC,MAAM,CAACC,gBAAgB,CAAC,gBAAgB,EAAEH,aAAa,CAAC;IAExD,OAAO,MAAM;MACXE,MAAM,CAACE,mBAAmB,CAAC,eAAe,EAAEL,mBAAmB,CAAC;MAChEG,MAAM,CAACE,mBAAmB,CAAC,OAAO,EAAEH,iBAAiB,CAAC;MACtDC,MAAM,CAACE,mBAAmB,CAAC,gBAAgB,EAAEJ,aAAa,CAAC;IAC7D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAIN3H,SAAS,CAAC,MAAM;IACdiG,OAAO,CAACE,GAAG,CAAC,kBAAkB,EAAE;MAAEpE,KAAK,EAAEA,KAAK,CAAC+E,MAAM;MAAE3E,UAAU;MAAEI;IAAc,CAAC,CAAC;IACnF,IAAIyF,QAAQ,GAAGjG,KAAK;IACpB,IAAII,UAAU,EAAE;MACd,MAAM8F,WAAW,GAAG9F,UAAU,CAAC8E,WAAW,CAAC,CAAC;MAC5Ce,QAAQ,GAAGA,QAAQ,CAAChB,MAAM,CAACjC,IAAI,IAAI;QACjC;QACA,MAAMmD,gBAAgB,GAAG,CACvBnD,IAAI,CAACoD,IAAI,EACTpD,IAAI,CAACqD,OAAO,EACZrD,IAAI,CAACsD,KAAK,EACVtD,IAAI,CAACuD,WAAW,EAChBvD,IAAI,CAACwD,QAAQ,EACbxD,IAAI,CAACtB,KAAK;QACV;QACA,GAAG,CAACsB,IAAI,CAACyD,SAAS,IAAI,EAAE,EAAEC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,YAAY,CAAC,EAClD,GAAG,CAAC5D,IAAI,CAACyD,SAAS,IAAI,EAAE,EAAEC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACN,OAAO,CAAC,EAC7C,GAAG,CAACrD,IAAI,CAACyD,SAAS,IAAI,EAAE,EAAEC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACL,KAAK,CAAC,CAC5C;QAED,OAAOH,gBAAgB,CAACU,IAAI,CAACC,KAAK,IAChCA,KAAK,IAAIA,KAAK,CAACtB,QAAQ,CAAC,CAAC,CAACN,WAAW,CAAC,CAAC,CAAC6B,QAAQ,CAACb,WAAW,CAC9D,CAAC;MACH,CAAC,CAAC;IACJ;IACA,IAAI1F,aAAa,EAAE;MACjByF,QAAQ,GAAGA,QAAQ,CAAChB,MAAM,CAACjC,IAAI,IAAI0C,MAAM,CAAC1C,IAAI,CAACyC,KAAK,CAAC,KAAKC,MAAM,CAAClF,aAAa,CAAC,CAAC;IAClF;IACAyF,QAAQ,CAACb,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIpC,IAAI,CAACoC,CAAC,CAACnC,SAAS,CAAC,GAAG,IAAID,IAAI,CAACmC,CAAC,CAAClC,SAAS,CAAC,CAAC;IACtEe,OAAO,CAACE,GAAG,CAAC,wBAAwB,EAAE6B,QAAQ,CAAClB,MAAM,CAAC;IACtD5E,gBAAgB,CAAC8F,QAAQ,CAAC;EAC5B,CAAC,EAAE,CAACjG,KAAK,EAAEI,UAAU,EAAEI,aAAa,CAAC,CAAC;EAEtC,MAAMwG,gBAAgB,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACjH,KAAK,CAAC0G,GAAG,CAACQ,CAAC,IAAIA,CAAC,CAACzB,KAAK,CAAC,CAACR,MAAM,CAACkC,OAAO,CAAC,CAAC,CAAC,CAAC/B,IAAI,CAAC,CAAC;EAErF,MAAMgC,eAAe,GAAIC,IAAI,IAAK;IAChC,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAAC9E,GAAG,EAAE;MACtBhE,OAAO,CAAC0F,KAAK,CAAC,0CAA0C,CAAC;MACzD;IACF;;IAEA;IACA,MAAMqD,aAAa,GAAG,mBAAmB;IACzC,IAAI,CAACA,aAAa,CAACC,IAAI,CAACF,IAAI,CAAC9E,GAAG,CAAC,EAAE;MACjChE,OAAO,CAAC0F,KAAK,CAAC,2CAA2C,CAAC;MAC1D;IACF;IAEA9F,eAAe,CAAC,MAAM;MACpB6C,QAAQ,CAAE,SAAQqG,IAAI,CAAC9E,GAAI,OAAM,CAAC;IACpC,CAAC,CAAC;EACJ,CAAC;EAID,MAAMiF,cAAc,GAAIH,IAAI,IAAK;IAC/B,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAAC9E,GAAG,EAAE;MACtBhE,OAAO,CAAC0F,KAAK,CAAC,0CAA0C,CAAC;MACzD;IACF;IACA;IACA,MAAMwD,UAAU,GAAG/G,WAAW,CAAC2G,IAAI,CAAC9E,GAAG,CAAC;IACxC,IAAI,CAACkF,UAAU,EAAE;MACflJ,OAAO,CAACmJ,IAAI,CAAC,sDAAsD,CAAC;MACpE;IACF;IACAvJ,eAAe,CAAC,MAAM;MACpB6C,QAAQ,CAAE,SAAQqG,IAAI,CAAC9E,GAAI,SAAQ,CAAC;IACtC,CAAC,CAAC;EACJ,CAAC;EAID,IAAI3B,OAAO,EAAE;IACX,oBACEjB,OAAA;MAAKgI,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGjI,OAAA;QAAKgI,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BjI,OAAA;UAAKgI,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnGrI,OAAA;UAAGgI,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAEvG,WAAW,GAAG,sBAAsB,GAAG;QAAoB;UAAAwG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3F;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACErI,OAAA,CAAAE,SAAA;IAAA+H,QAAA,gBAEEjI,OAAA;MAAOsI,GAAG;MAACC,MAAM;MAAAN,QAAA,EAAG;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAEVrI,OAAA;MAAKgI,SAAS,EAAC,oFAAoF;MAAAC,QAAA,eACjGjI,OAAA;QAAKgI,SAAS,EAAC,kEAAkE;QAAAC,QAAA,gBAIjFjI,OAAA;UAAKgI,SAAS,EAAC,4CAA4C;UAAAC,QAAA,eACzDjI,OAAA;YAAKgI,SAAS,EAAC,0CAA0C;YAAAC,QAAA,gBAEvDjI,OAAA;cAAKgI,SAAS,EAAC,2FAA2F;cAAAC,QAAA,gBACxGjI,OAAA;gBAAKgI,SAAS,EAAC,+CAA+C;gBAAAC,QAAA,gBAC5DjI,OAAA;kBAAKgI,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtCjI,OAAA;oBAAKgI,SAAS,EAAC;kBAAsC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5DrI,OAAA;oBAAMgI,SAAS,EAAC,aAAa;oBAAAC,QAAA,GAAC,SAAO,EAAC,CAAA1G,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,KAAK,KAAI,YAAY;kBAAA;oBAAAmG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC,eACNrI,OAAA;kBAAKgI,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtCjI,OAAA;oBAAKgI,SAAS,EAAC;kBAAuC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7DrI,OAAA;oBAAAiI,QAAA,GAAO1H,aAAa,CAAC6E,MAAM,EAAC,oBAAkB;kBAAA;oBAAA8C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACLlH,WAAW,iBACVnB,OAAA;gBAAKgI,SAAS,EAAC,4DAA4D;gBAAAC,QAAA,gBACzEjI,OAAA;kBAAKgI,SAAS,EAAC;gBAAkC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxDrI,OAAA;kBAAAiI,QAAA,GAAM,WAAS,EAAC9G,WAAW,CAACqH,kBAAkB,CAAC,CAAC;gBAAA;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNrI,OAAA;cAAKgI,SAAS,EAAC,0CAA0C;cAAAC,QAAA,gBACvDjI,OAAA;gBAAKgI,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BjI,OAAA;kBAAKgI,SAAS,EAAC,sEAAsE;kBAAAC,QAAA,eACnFjI,OAAA,CAAClB,QAAQ;oBAACkJ,SAAS,EAAC;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACNrI,OAAA;kBACEyI,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,8CAA8C;kBAC1DlG,KAAK,EAAE/B,UAAW;kBAClBkI,QAAQ,EAAGpB,CAAC,IAAKhF,kBAAkB,CAACgF,CAAC,CAACqB,MAAM,CAACpG,KAAK,CAAE;kBACpDwF,SAAS,EAAC;gBAA0L;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrI,OAAA;gBAAKgI,SAAS,EAAC,cAAc;gBAAAC,QAAA,eAC3BjI,OAAA;kBAAKgI,SAAS,EAAC,UAAU;kBAAAC,QAAA,gBACvBjI,OAAA;oBAAKgI,SAAS,EAAC,sEAAsE;oBAAAC,QAAA,eACnFjI,OAAA,CAACjB,QAAQ;sBAACiJ,SAAS,EAAC;oBAAuB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC,eACNrI,OAAA;oBACEwC,KAAK,EAAE3B,aAAc;oBACrB8H,QAAQ,EAAGpB,CAAC,IAAK7E,iBAAiB,CAAC6E,CAAC,CAACqB,MAAM,CAACpG,KAAK,CAAE;oBACnDwF,SAAS,EAAC,0MAA0M;oBAAAC,QAAA,gBAEpNjI,OAAA;sBAAQwC,KAAK,EAAC,EAAE;sBAAAyF,QAAA,EAAC;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EACpChB,gBAAgB,CAACN,GAAG,CAAEiB,SAAS,iBAC9BhI,OAAA;sBAAwBwC,KAAK,EAAEwF,SAAU;sBAAAC,QAAA,EACtC,CAAA1G,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,KAAK,MAAK,SAAS,GAAI,SAAQiG,SAAU,EAAC,GAAGA;oBAAS,GADlDA,SAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEd,CACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGL,CAAC5H,UAAU,IAAII,aAAa,kBAC3Bb,OAAA;gBACE6I,OAAO,EAAEvG,qBAAsB;gBAC/B0F,SAAS,EAAC,gOAAgO;gBAC1Oc,KAAK,EAAC,8BAA8B;gBAAAb,QAAA,gBAEpCjI,OAAA,CAACT,GAAG;kBAACyI,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC3BrI,OAAA;kBAAMgI,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CACT;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNrI,OAAA;UAAKgI,SAAS,EAAC,aAAa;UAACe,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAS,CAAE;UAAAf,QAAA,EAGzD1H,aAAa,CAAC6E,MAAM,KAAK,CAAC,gBACzBpF,OAAA;YAAKgI,SAAS,EAAC,2BAA2B;YAAAC,QAAA,eACxCjI,OAAA;cAAKgI,SAAS,EAAC,4DAA4D;cAAAC,QAAA,gBACzEjI,OAAA,CAACX,QAAQ;gBAAC2I,SAAS,EAAC;cAAsD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7ErI,OAAA;gBAAIgI,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzFrI,OAAA;gBAAGgI,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAC9CxH,UAAU,IAAII,aAAa,GACxB,+CAA+C,GAC/C;cAAwD;gBAAAqH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAE3D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAENrI,OAAA;YAAKgI,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvB1H,aAAa,CAACwG,GAAG,CAAC,CAACW,IAAI,EAAEuB,KAAK,kBAC7BjJ,OAAA,CAACkJ,QAAQ;cAEPxB,IAAI,EAAEA,IAAK;cACXI,UAAU,EAAE/G,WAAW,CAAC2G,IAAI,CAAC9E,GAAG,CAAE;cAClCuG,WAAW,EAAE,IAAK;cAClBC,OAAO,EAAE3B,eAAgB;cACzB4B,MAAM,EAAEA,CAAA,KAAMxB,cAAc,CAACH,IAAI,CAAE;cACnCuB,KAAK,EAAEA;YAAM,GANRvB,IAAI,CAAC9E,GAAG;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOd,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACJ,CAAC;AAEP,CAAC;;AAED;AAAAjI,EAAA,CAxdMD,IAAI;EAAA,QAcS1B,WAAW,EACXC,WAAW,EACXC,WAAW,EACaE,WAAW;AAAA;AAAAyK,EAAA,GAjBhDnJ,IAAI;AAydV,MAAM+I,QAAQ,GAAGA,CAAC;EAAExB,IAAI;EAAEI,UAAU;EAAEsB,OAAO;EAAEC,MAAM;EAAEJ;AAAM,CAAC,KAAK;EAAAM,GAAA;EAAA,IAAAC,eAAA;EACjE,MAAM;IAAE9H,WAAW;IAAEC;EAAa,CAAC,GAAG9C,WAAW,CAAC,CAAC;EAEnD,MAAM4K,UAAU,GAAIC,OAAO,IAAK;IAC9B,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;IAC1B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACxC,OAAQ,GAAEC,OAAQ,IAAGjI,WAAW,GAAG,KAAK,GAAG,KAAM,EAAC;EACpD,CAAC;EAED,MAAMoI,oBAAoB,GAAIC,aAAa,IAAK;IAC9C;IACA,IAAI,CAACA,aAAa,IAAIA,aAAa,KAAK,CAAC,EAAE,OAAO,IAAI;IAEtD,IAAIC,YAAY,GAAGD,aAAa;;IAEhC;IACA,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;MACrCC,YAAY,GAAGhF,QAAQ,CAAC+E,aAAa,EAAE,EAAE,CAAC;IAC5C;;IAEA;IACA,IAAIE,KAAK,CAACD,YAAY,CAAC,IAAIA,YAAY,GAAG,CAAC,EAAE,OAAO,IAAI;IAExD,MAAML,OAAO,GAAGC,IAAI,CAACM,KAAK,CAACF,YAAY,GAAG,EAAE,CAAC;IAC7C,MAAMN,OAAO,GAAGM,YAAY,GAAG,EAAE;IAEjC,IAAIL,OAAO,GAAG,CAAC,EAAE;MACf,OAAQ,GAAEA,OAAQ,KAAID,OAAQ,GAAE;IAClC;IACA,OAAQ,GAAEK,aAAc,GAAE;EAC5B,CAAC;;EAED;EACA,IAAI,CAACrC,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IACrC,oBACE1H,OAAA;MAAKgI,SAAS,EAAC,2DAA2D;MAAAC,QAAA,eACxEjI,OAAA;QAAGgI,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CAAC;EAEV;EAEA,oBACErI,OAAA;IACEgI,SAAS,EAAC,mIAAmI;IAC7Ie,KAAK,EAAE;MACLoB,UAAU,EAAE,gEAAgE;MAC5EC,MAAM,EAAEtC,UAAU,GACbA,UAAU,CAACrE,OAAO,KAAK,MAAM,GAAG,mBAAmB,GAAG,mBAAmB,GAC1E,mBAAmB;MACvB4G,SAAS,EAAEvC,UAAU,GAChBA,UAAU,CAACrE,OAAO,KAAK,MAAM,GAC1B,oCAAoC,GACpC,mCAAmC,GACvC,oCAAoC;MACxC6G,SAAS,EAAEnE,MAAM,CAACoE,UAAU,IAAI,GAAG,GAAG,OAAO,GAAG,OAAO;MACvDC,MAAM,EAAE;IACV,CAAE;IAAAvC,QAAA,gBAGFjI,OAAA;MAAKgI,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BjI,OAAA;QACEgI,SAAS,EAAC,6BAA6B;QACvCe,KAAK,EAAE;UACL0B,KAAK,EAAE,SAAS;UAChBC,UAAU,EAAE,2BAA2B;UACvCC,UAAU,EAAE,KAAK;UACjBC,QAAQ,EAAEzE,MAAM,CAACoE,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;QAChD,CAAE;QAAAtC,QAAA,EAED,OAAOP,IAAI,CAACjB,IAAI,KAAK,QAAQ,GAAGiB,IAAI,CAACjB,IAAI,GAAG;MAAe;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGNrI,OAAA;MAAKgI,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAC9BH,UAAU,gBACT9H,OAAA;QAAKgI,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDjI,OAAA;UACEgI,SAAS,EAAC,+DAA+D;UACzEe,KAAK,EAAE;YACL8B,eAAe,EAAE/C,UAAU,CAACrE,OAAO,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS;YACtEmH,QAAQ,EAAEzE,MAAM,CAACoE,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG;UAC/C,CAAE;UAAAtC,QAAA,EAEDH,UAAU,CAACrE,OAAO,KAAK,MAAM,GAAG,UAAU,GAAG;QAAU;UAAAyE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eACNrI,OAAA;UACEgI,SAAS,EAAC,gEAAgE;UAC1Ee,KAAK,EAAE;YACL8B,eAAe,EAAE,SAAS;YAC1BJ,KAAK,EAAE,SAAS;YAChBG,QAAQ,EAAEzE,MAAM,CAACoE,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG;UAC/C,CAAE;UAAAtC,QAAA,GAED,OAAOH,UAAU,CAACpE,UAAU,KAAK,QAAQ,GAAGoE,UAAU,CAACpE,UAAU,GAAG,CAAC,EAAC,GACzE;QAAA;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,gBAENrI,OAAA;QACEgI,SAAS,EAAC,+DAA+D;QACzEe,KAAK,EAAE;UACL8B,eAAe,EAAE,SAAS;UAC1BD,QAAQ,EAAEzE,MAAM,CAACoE,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG;QAC/C,CAAE;QAAAtC,QAAA,EACH;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENrI,OAAA;MAAKgI,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BjI,OAAA;QAAKgI,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBAGrBjI,OAAA;UAAKgI,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBAC7CjI,OAAA;YACEgI,SAAS,EAAC,+DAA+D;YACzEe,KAAK,EAAE;cACLoB,UAAU,EAAE,6CAA6C;cACzDW,WAAW,EAAE;YACf,CAAE;YAAA7C,QAAA,gBAEFjI,OAAA,CAACf,cAAc;cAAC+I,SAAS,EAAC,SAAS;cAACe,KAAK,EAAE;gBAAE0B,KAAK,EAAE;cAAU;YAAE;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnErI,OAAA;cACEgI,SAAS,EAAC,WAAW;cACrBe,KAAK,EAAE;gBACL0B,KAAK,EAAE,SAAS;gBAChBG,QAAQ,EAAEzE,MAAM,CAACoE,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;cAChD,CAAE;cAAAtC,QAAA,EAED8C,KAAK,CAACC,OAAO,CAACtD,IAAI,CAACZ,SAAS,CAAC,GAAGY,IAAI,CAACZ,SAAS,CAAC1B,MAAM,GAAG;YAAC;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNrI,OAAA;YACEgI,SAAS,EAAC,+DAA+D;YACzEe,KAAK,EAAE;cACLoB,UAAU,EAAE,6CAA6C;cACzDW,WAAW,EAAE;YACf,CAAE;YAAA7C,QAAA,gBAEFjI,OAAA,CAAChB,OAAO;cAACgJ,SAAS,EAAC,SAAS;cAACe,KAAK,EAAE;gBAAE0B,KAAK,EAAE;cAAU;YAAE;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5DrI,OAAA;cACEgI,SAAS,EAAC,WAAW;cACrBe,KAAK,EAAE;gBACL0B,KAAK,EAAE,SAAS;gBAChBG,QAAQ,EAAEzE,MAAM,CAACoE,UAAU,IAAI,GAAG,GAAG,MAAM,GAAG;cAChD,CAAE;cAAAtC,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAINrI,OAAA;UAAKgI,SAAS,EAAC,uDAAuD;UAAAC,QAAA,gBAEpEjI,OAAA;YACEgI,SAAS,EAAC,oEAAoE;YAC9Ee,KAAK,EAAE;cACLoB,UAAU,EAAE,6CAA6C;cACzDS,QAAQ,EAAEzE,MAAM,CAACoE,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG;YAC/C,CAAE;YAAAtC,QAAA,GACH,cACG,EAACP,IAAI,CAAC3F,KAAK,IAAI,SAAS;UAAA;YAAAmG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eAGPrI,OAAA;YACEgI,SAAS,EAAC,oEAAoE;YAC9Ee,KAAK,EAAE;cACLoB,UAAU,EAAE,6CAA6C;cACzDS,QAAQ,EAAEzE,MAAM,CAACoE,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG;YAC/C,CAAE;YAAAtC,QAAA,GACH,cACG,EAAC,OAAOP,IAAI,CAAC5B,KAAK,KAAK,QAAQ,IAAI,OAAO4B,IAAI,CAAC5B,KAAK,KAAK,QAAQ,GAChE4B,IAAI,CAAC3F,KAAK,KAAK,SAAS,IAAI2F,IAAI,CAAC3F,KAAK,KAAK,mBAAmB,GAC5DL,WAAW,GAAI,aAAYgG,IAAI,CAAC5B,KAAM,EAAC,GAAI,SAAQ4B,IAAI,CAAC5B,KAAM,EAAC,GAChE4B,IAAI,CAAC5B,KAAK,GAAI,KAAK;UAAA;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eAGPrI,OAAA;YACEgI,SAAS,EAAC,oEAAoE;YAC9Ee,KAAK,EAAE;cACLoB,UAAU,EAAE,6CAA6C;cACzDS,QAAQ,EAAEzE,MAAM,CAACoE,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG;YAC/C,CAAE;YAAAtC,QAAA,GACH,cACG,EAACP,IAAI,CAACb,QAAQ,IAAI,SAAS;UAAA;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eAGPrI,OAAA;YACEgI,SAAS,EAAC,oEAAoE;YAC9Ee,KAAK,EAAE;cACLoB,UAAU,EAAEzC,IAAI,CAACf,KAAK,IAAIe,IAAI,CAACf,KAAK,KAAK,SAAS,IAAIe,IAAI,CAACf,KAAK,KAAK,EAAE,GACnE,6CAA6C,GAC7C,6CAA6C;cACjDiE,QAAQ,EAAEzE,MAAM,CAACoE,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG;YAC/C,CAAE;YAAAtC,QAAA,GACH,cACG,EAACP,IAAI,CAACf,KAAK,KAAKjF,WAAW,GAAG,OAAO,GAAG,SAAS,CAAC;UAAA;YAAAwG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAILP,UAAU,IAAI,OAAOA,UAAU,KAAK,QAAQ,iBAC3C9H,OAAA;MACEgI,SAAS,EAAC,sCAAsC;MAChDe,KAAK,EAAE;QACLoB,UAAU,EAAErC,UAAU,CAACrE,OAAO,KAAK,MAAM,GACrC,oDAAoD,GACpD,oDAAoD;QACxDqH,WAAW,EAAEhD,UAAU,CAACrE,OAAO,KAAK,MAAM,GAAG,SAAS,GAAG;MAC3D,CAAE;MAAAwE,QAAA,gBAEFjI,OAAA;QAAKgI,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDjI,OAAA;UAAKgI,SAAS,EAAC,yBAAyB;UAAAC,QAAA,GACrCH,UAAU,CAACrE,OAAO,KAAK,MAAM,gBAC5BzD,OAAA;YACEgI,SAAS,EAAC,0FAA0F;YACpGe,KAAK,EAAE;cACLoB,UAAU,EAAE,6CAA6C;cACzDW,WAAW,EAAE;YACf,CAAE;YAAA7C,QAAA,eAEFjI,OAAA,CAACV,OAAO;cAAC0I,SAAS,EAAC,mBAAmB;cAACe,KAAK,EAAE;gBAAE0B,KAAK,EAAE;cAAU;YAAE;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,gBAENrI,OAAA;YACEgI,SAAS,EAAC,0FAA0F;YACpGe,KAAK,EAAE;cACLoB,UAAU,EAAE,6CAA6C;cACzDW,WAAW,EAAE;YACf,CAAE;YAAA7C,QAAA,eAEFjI,OAAA,CAACT,GAAG;cAACyI,SAAS,EAAC,mBAAmB;cAACe,KAAK,EAAE;gBAAE0B,KAAK,EAAE;cAAU;YAAE;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CACN,eACDrI,OAAA;YAAAiI,QAAA,gBACEjI,OAAA;cAAMgI,SAAS,EAAC,mBAAmB;cAACe,KAAK,EAAE;gBAAE0B,KAAK,EAAE;cAAU,CAAE;cAAAxC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtFrI,OAAA;cAAKgI,SAAS,EAAC,SAAS;cAACe,KAAK,EAAE;gBAAE0B,KAAK,EAAE;cAAU,CAAE;cAAAxC,QAAA,EAClD,IAAI1E,IAAI,CAACuE,UAAU,CAACzD,WAAW,IAAIyD,UAAU,CAACtE,SAAS,IAAID,IAAI,CAACwB,GAAG,CAAC,CAAC,CAAC,CAACkG,kBAAkB,CAAC;YAAC;cAAA/C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNrI,OAAA;UACEgI,SAAS,EAAC,8BAA8B;UACxCe,KAAK,EAAE;YACL0B,KAAK,EAAE3C,UAAU,CAACrE,OAAO,KAAK,MAAM,GAAG,SAAS,GAAG;UACrD,CAAE;UAAAwE,QAAA,GAED,OAAOH,UAAU,CAACpE,UAAU,KAAK,QAAQ,GAAGoE,UAAU,CAACpE,UAAU,GAAG,CAAC,EAAC,GACzE;QAAA;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNrI,OAAA;QAAKgI,SAAS,EAAC,qCAAqC;QAAAC,QAAA,gBAElDjI,OAAA;UACEgI,SAAS,EAAC,+DAA+D;UACzEe,KAAK,EAAE;YACLoB,UAAU,EAAE,6CAA6C;YACzDW,WAAW,EAAE;UACf,CAAE;UAAA7C,QAAA,gBAEFjI,OAAA;YAAKgI,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCjI,OAAA,CAACV,OAAO;cAAC0I,SAAS,EAAC,SAAS;cAACe,KAAK,EAAE;gBAAE0B,KAAK,EAAE;cAAU;YAAE;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5DrI,OAAA;cAAMgI,SAAS,EAAC,mBAAmB;cAACe,KAAK,EAAE;gBAAE0B,KAAK,EAAE;cAAU,CAAE;cAAAxC,QAAA,EAC7D,OAAOH,UAAU,CAACnE,cAAc,KAAK,QAAQ,GAAGmE,UAAU,CAACnE,cAAc,GAAG;YAAC;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNrI,OAAA;YAAKgI,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCjI,OAAA,CAACT,GAAG;cAACyI,SAAS,EAAC,SAAS;cAACe,KAAK,EAAE;gBAAE0B,KAAK,EAAE;cAAU;YAAE;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxDrI,OAAA;cAAMgI,SAAS,EAAC,mBAAmB;cAACe,KAAK,EAAE;gBAAE0B,KAAK,EAAE;cAAU,CAAE;cAAAxC,QAAA,EAC7D,CAAC,EAAAuB,eAAA,GAAA9B,IAAI,CAACZ,SAAS,cAAA0C,eAAA,uBAAdA,eAAA,CAAgBpE,MAAM,KAAI,CAAC,KAAK,OAAO0C,UAAU,CAACnE,cAAc,KAAK,QAAQ,GAAGmE,UAAU,CAACnE,cAAc,GAAG,CAAC;YAAC;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5G,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNrI,OAAA;UACEgI,SAAS,EAAC,+DAA+D;UACzEe,KAAK,EAAE;YACLoB,UAAU,EAAE,oDAAoD;YAChEW,WAAW,EAAE;UACf,CAAE;UAAA7C,QAAA,gBAEFjI,OAAA;YAAMgI,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClCrI,OAAA;YAAMgI,SAAS,EAAC,mBAAmB;YAACe,KAAK,EAAE;cAAE0B,KAAK,EAAE;YAAU,CAAE;YAAAxC,QAAA,EAC7DH,UAAU,CAAC5D,QAAQ,IAAI4D,UAAU,CAAC7D,MAAM,IAAI;UAAC;YAAAiE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EAGLP,UAAU,CAAC1D,SAAS,IAAI0D,UAAU,CAAC1D,SAAS,GAAG,CAAC,iBAC/CpE,OAAA;UACEgI,SAAS,EAAC,+DAA+D;UACzEe,KAAK,EAAE;YACLoB,UAAU,EAAE,oDAAoD;YAChEW,WAAW,EAAE;UACf,CAAE;UAAA7C,QAAA,gBAEFjI,OAAA,CAAChB,OAAO;YAACgJ,SAAS,EAAC,SAAS;YAACe,KAAK,EAAE;cAAE0B,KAAK,EAAE;YAAU;UAAE;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5DrI,OAAA;YAAMgI,SAAS,EAAC,mBAAmB;YAACe,KAAK,EAAE;cAAE0B,KAAK,EAAE;YAAU,CAAE;YAAAxC,QAAA,EAC7D6B,oBAAoB,CAAChC,UAAU,CAAC1D,SAAS;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDrI,OAAA;MAAKgI,SAAS,EAAC;IAAQ;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAE9BrI,OAAA;MAAKgI,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAE9BjI,OAAA;QACE6I,OAAO,EAAEA,CAAA,KAAMO,OAAO,CAAC1B,IAAI,CAAE;QAC7BM,SAAS,EAAC,yLAAyL;QACnMe,KAAK,EAAE;UACLoB,UAAU,EAAErC,UAAU,GAClB,6CAA6C,GAC7C,6CAA6C;UACjD8C,QAAQ,EAAE,MAAM;UAChBN,SAAS,EAAE;QACb,CAAE;QAAArC,QAAA,gBAEFjI,OAAA,CAACb,YAAY;UAAC6I,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACnCP,UAAU,GAAIpG,WAAW,GAAG,kBAAkB,GAAG,gBAAgB,GAAKA,WAAW,GAAG,iBAAiB,GAAG,eAAgB;MAAA;QAAAwG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnH,CAAC,EAGRP,UAAU,iBACT9H,OAAA;QACE6I,OAAO,EAAEA,CAAA,KAAMQ,MAAM,CAAC3B,IAAI,CAAE;QAC5BM,SAAS,EAAC,2IAA2I;QACrJe,KAAK,EAAE;UACLoB,UAAU,EAAE,6CAA6C;UACzDS,QAAQ,EAAE,MAAM;UAChBN,SAAS,EAAE;QACb,CAAE;QACFxB,KAAK,EAAC,cAAc;QAAAb,QAAA,eAEpBjI,OAAA,CAACd,QAAQ;UAAC8I,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACkB,GAAA,CAnWIL,QAAQ;EAAA,QAC0BrK,WAAW;AAAA;AAAAqM,GAAA,GAD7ChC,QAAQ;AAqWd,eAAe/I,IAAI;AAAC,IAAAmJ,EAAA,EAAA4B,GAAA;AAAAC,YAAA,CAAA7B,EAAA;AAAA6B,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}