{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\FloatingBrainwaveAI.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { TbRobot } from 'react-icons/tb';\nimport { chatWithChatGPT, uploadImg } from '../apicalls/chat';\nimport { useLanguage } from '../contexts/LanguageContext';\nimport { useSelector } from 'react-redux';\nimport ContentRenderer from './ContentRenderer';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst FloatingBrainwaveAI = () => {\n  _s();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    t,\n    isKiswahili\n  } = useLanguage();\n  const [isOpen, setIsOpen] = useState(false);\n  const [isMinimized, setIsMinimized] = useState(false);\n  const [isMaximized, setIsMaximized] = useState(false);\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);\n  const [isTablet, setIsTablet] = useState(window.innerWidth > 768 && window.innerWidth <= 1024);\n\n  // Handle responsive design\n  useEffect(() => {\n    const handleResize = () => {\n      setIsMobile(window.innerWidth <= 768);\n      setIsTablet(window.innerWidth > 768 && window.innerWidth <= 1024);\n    };\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  // Clear chat when user logs out (listen for storage changes)\n  useEffect(() => {\n    const handleStorageChange = e => {\n      if (e.key === 'token' && !e.newValue) {\n        // Token was removed, user logged out\n        clearChat();\n        setIsOpen(false);\n      }\n    };\n    window.addEventListener('storage', handleStorageChange);\n    return () => window.removeEventListener('storage', handleStorageChange);\n  }, []);\n\n  // Clear chat function\n  const clearChat = () => {\n    const welcomeMessage = isKiswahili ? 'Hujambo! Mimi ni Brainwave AI, msaidizi wako wa masomo. Ninawezaje kukusaidia leo?' : 'Hi! I am Brainwave AI, your study assistant. How can I help you today?';\n    setMessages([{\n      role: 'assistant',\n      content: welcomeMessage\n    }]);\n    setInput('');\n    removeImage();\n  };\n\n  // Clear chat when closing\n  const handleClose = () => {\n    clearChat();\n    setIsOpen(false);\n    setIsMinimized(false);\n    setIsMaximized(false);\n  };\n  const [messages, setMessages] = useState([{\n    role: 'assistant',\n    content: isKiswahili ? 'Hujambo! Mimi ni Brainwave AI, msaidizi wako wa masomo. Ninawezaje kukusaidia leo?' : 'Hi! I am Brainwave AI, your study assistant. How can I help you today?'\n  }]);\n  const [input, setInput] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const [selectedImage, setSelectedImage] = useState(null);\n  const [imagePreview, setImagePreview] = useState(null);\n  const [showPasteButton, setShowPasteButton] = useState(false);\n  const messagesEndRef = useRef(null);\n  const fileInputRef = useRef(null);\n  const inputRef = useRef(null);\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({\n        behavior: 'smooth'\n      });\n    }\n  }, [messages]);\n  const handleImageSelect = event => {\n    const file = event.target.files[0];\n    if (file && file.type.startsWith('image/')) {\n      setSelectedImage(file);\n      const reader = new FileReader();\n      reader.onload = e => setImagePreview(e.target.result);\n      reader.readAsDataURL(file);\n    }\n  };\n  const removeImage = () => {\n    setSelectedImage(null);\n    setImagePreview(null);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = '';\n    }\n  };\n\n  // Check if clipboard has text content\n  const checkClipboard = async () => {\n    try {\n      if (navigator.clipboard && navigator.clipboard.readText) {\n        const text = await navigator.clipboard.readText();\n        setShowPasteButton(text.trim().length > 0);\n      }\n    } catch (err) {\n      // Clipboard access denied or not available\n      setShowPasteButton(false);\n    }\n  };\n\n  // Paste from clipboard\n  const pasteFromClipboard = async () => {\n    try {\n      if (navigator.clipboard && navigator.clipboard.readText) {\n        const text = await navigator.clipboard.readText();\n        if (text.trim()) {\n          setInput(prevInput => {\n            const newInput = prevInput + (prevInput ? '\\n' : '') + text.trim();\n            return newInput;\n          });\n          setShowPasteButton(false);\n          // Focus the input after pasting\n          if (inputRef.current) {\n            inputRef.current.focus();\n          }\n        }\n      }\n    } catch (err) {\n      console.warn('Could not paste from clipboard:', err);\n      // Fallback: show alert to manually paste\n      alert(isKiswahili ? 'Tumia Ctrl+V au Cmd+V kubandika maandishi' : 'Use Ctrl+V or Cmd+V to paste text');\n    }\n  };\n\n  // Check clipboard when component opens\n  React.useEffect(() => {\n    if (isOpen) {\n      checkClipboard();\n      // Check clipboard periodically when chat is open\n      const interval = setInterval(checkClipboard, 2000);\n      return () => clearInterval(interval);\n    }\n  }, [isOpen]);\n  const sendMessage = async () => {\n    if (!input.trim() && !selectedImage) return;\n    const userMessage = input.trim();\n    const imageFile = selectedImage;\n    setInput('');\n    removeImage();\n    setIsTyping(true);\n    try {\n      let imageUrl = null;\n      if (imageFile) {\n        const formData = new FormData();\n        formData.append(\"image\", imageFile);\n        const uploadResult = await uploadImg(formData);\n        if (uploadResult !== null && uploadResult !== void 0 && uploadResult.success) {\n          imageUrl = uploadResult.url;\n        }\n      }\n      const newUserMessage = imageUrl ? {\n        role: \"user\",\n        content: [{\n          type: \"text\",\n          text: userMessage\n        }, {\n          type: \"image_url\",\n          image_url: {\n            url: imageUrl\n          }\n        }]\n      } : {\n        role: \"user\",\n        content: userMessage\n      };\n      setMessages(prev => [...prev, newUserMessage]);\n\n      // Add language preference for Kiswahili users\n      const chatPayload = {\n        messages: [...messages, newUserMessage],\n        ...(isKiswahili && {\n          language: 'kiswahili',\n          systemPrompt: 'Jibu kwa lugha ya Kiswahili tu. Wewe ni msaidizi wa masomo wa Tanzania. Tumia lugha rahisi na ya kielimu.'\n        })\n      };\n      const response = await chatWithChatGPT(chatPayload);\n      if (response !== null && response !== void 0 && response.success && response !== null && response !== void 0 && response.data) {\n        setMessages(prev => [...prev, {\n          role: 'assistant',\n          content: response.data\n        }]);\n      } else {\n        const errorMessage = isKiswahili ? \"Samahani, sikuweza kuchakata ombi lako sasa hivi. Tafadhali jaribu tena baada ya muda.\" : \"I'm sorry, I couldn't process your request right now. Please try again in a moment.\";\n        setMessages(prev => [...prev, {\n          role: 'assistant',\n          content: errorMessage\n        }]);\n      }\n    } catch (error) {\n      const errorMessage = isKiswahili ? \"Nina matatizo ya kiufundi. Tafadhali jaribu tena baadaye.\" : \"I'm experiencing some technical difficulties. Please try again later.\";\n      setMessages(prev => [...prev, {\n        role: 'assistant',\n        content: errorMessage\n      }]);\n    } finally {\n      setIsTyping(false);\n    }\n  };\n  const handleKeyDown = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      sendMessage();\n    }\n  };\n  if (!isOpen) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      onClick: () => setIsOpen(true),\n      style: {\n        position: 'fixed',\n        bottom: isMobile ? '15px' : '20px',\n        right: isMobile ? '15px' : '20px',\n        width: isMobile ? '50px' : '60px',\n        height: isMobile ? '50px' : '60px',\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        borderRadius: '50%',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        cursor: 'pointer',\n        boxShadow: '0 8px 32px rgba(102, 126, 234, 0.4)',\n        zIndex: 1000,\n        transition: 'all 0.3s ease',\n        animation: 'pulse 2s infinite'\n      },\n      children: [/*#__PURE__*/_jsxDEV(TbRobot, {\n        style: {\n          color: 'white',\n          fontSize: isMobile ? '24px' : '28px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n        children: `\n          @keyframes pulse {\n            0% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4); }\n            50% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.8); }\n            100% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4); }\n          }\n        `\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 233,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Get responsive dimensions\n  const getResponsiveDimensions = () => {\n    if (isMobile) {\n      return {\n        width: isMinimized ? '280px' : isMaximized ? 'calc(100vw - 10px)' : '320px',\n        height: isMinimized ? '50px' : isMaximized ? 'calc(100vh - 10px)' : '450px',\n        bottom: isMaximized ? '5px' : '15px',\n        right: isMaximized ? '5px' : '15px',\n        top: isMaximized ? '5px' : 'auto',\n        left: isMaximized ? '5px' : 'auto'\n      };\n    } else if (isTablet) {\n      return {\n        width: isMinimized ? '320px' : isMaximized ? 'calc(100vw - 15px)' : '360px',\n        height: isMinimized ? '55px' : isMaximized ? 'calc(100vh - 15px)' : '480px',\n        bottom: isMaximized ? '7px' : '18px',\n        right: isMaximized ? '7px' : '18px',\n        top: isMaximized ? '7px' : 'auto',\n        left: isMaximized ? '7px' : 'auto'\n      };\n    } else {\n      return {\n        width: isMinimized ? '300px' : isMaximized ? 'calc(100vw - 20px)' : '380px',\n        height: isMinimized ? '60px' : isMaximized ? 'calc(100vh - 20px)' : '500px',\n        bottom: isMaximized ? '10px' : '20px',\n        right: isMaximized ? '10px' : '20px',\n        top: isMaximized ? '10px' : 'auto',\n        left: isMaximized ? '10px' : 'auto'\n      };\n    }\n  };\n  const dimensions = getResponsiveDimensions();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'fixed',\n      ...dimensions,\n      background: 'rgba(255, 255, 255, 0.95)',\n      backdropFilter: 'blur(20px)',\n      borderRadius: isMaximized ? '15px' : isMobile ? '15px' : '20px',\n      boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',\n      border: '1px solid rgba(255, 255, 255, 0.2)',\n      zIndex: 1000,\n      display: 'flex',\n      flexDirection: 'column',\n      overflow: 'hidden',\n      transition: 'all 0.3s ease'\n    },\n    onWheel: e => {\n      // Only prevent background scrolling when AI chat is open and not minimized\n      if (isOpen && !isMinimized) {\n        e.stopPropagation();\n      }\n    },\n    onTouchMove: e => {\n      // Only prevent background scrolling when AI chat is open and not minimized\n      if (isOpen && !isMinimized) {\n        e.stopPropagation();\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        padding: isMobile ? '12px 16px' : '16px 20px',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        borderRadius: isMaximized ? '15px 15px 0 0' : isMobile ? '15px 15px 0 0' : '20px 20px 0 0',\n        minHeight: isMinimized ? isMobile ? '50px' : '60px' : 'auto'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: isMobile ? '8px' : '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: isMobile ? '28px' : '32px',\n            height: isMobile ? '28px' : '32px',\n            background: 'rgba(255, 255, 255, 0.2)',\n            borderRadius: '50%',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(TbRobot, {\n            style: {\n              color: 'white',\n              fontSize: isMobile ? '16px' : '18px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 11\n        }, this), !isMinimized && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              margin: 0,\n              color: 'white',\n              fontSize: isMobile ? '14px' : '16px',\n              fontWeight: '600'\n            },\n            children: isKiswahili ? 'Akili ya Brainwave' : 'Brainwave AI'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: 0,\n              color: 'rgba(255, 255, 255, 0.8)',\n              fontSize: isMobile ? '10px' : '12px'\n            },\n            children: isKiswahili ? 'Daima hapa kukusaidia' : 'Always here to help'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 13\n        }, this), isMinimized && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              margin: 0,\n              color: 'white',\n              fontSize: isMobile ? '12px' : '14px',\n              fontWeight: '600'\n            },\n            children: \"Brainwave AI\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: isMobile ? '4px' : '8px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setIsMinimized(!isMinimized),\n          style: {\n            background: isMinimized ? 'rgba(255, 255, 255, 0.4)' : 'rgba(255, 255, 255, 0.25)',\n            border: 'none',\n            borderRadius: isMobile ? '6px' : '8px',\n            width: isMobile ? '28px' : '32px',\n            height: isMobile ? '28px' : '32px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            cursor: 'pointer',\n            transition: 'all 0.2s ease',\n            backdropFilter: 'blur(10px)',\n            boxShadow: isMinimized ? '0 2px 8px rgba(255, 255, 255, 0.3)' : 'none'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: 'white',\n              fontSize: isMobile ? '14px' : '16px',\n              fontWeight: 'bold',\n              textShadow: '0 1px 2px rgba(0,0,0,0.5)'\n            },\n            children: isMinimized ? '⬆' : '➖'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this), !isMinimized && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            setIsMaximized(!isMaximized);\n            if (isMinimized) setIsMinimized(false);\n          },\n          style: {\n            background: isMaximized ? 'rgba(255, 255, 255, 0.4)' : 'rgba(255, 255, 255, 0.25)',\n            border: 'none',\n            borderRadius: isMobile ? '6px' : '8px',\n            width: isMobile ? '28px' : '32px',\n            height: isMobile ? '28px' : '32px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            cursor: 'pointer',\n            transition: 'all 0.2s ease',\n            backdropFilter: 'blur(10px)',\n            boxShadow: isMaximized ? '0 2px 8px rgba(255, 255, 255, 0.3)' : 'none'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: 'white',\n              fontSize: isMobile ? '14px' : '16px',\n              fontWeight: 'bold',\n              textShadow: '0 1px 2px rgba(0,0,0,0.5)'\n            },\n            children: isMaximized ? '⬇' : '⬆'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleClose,\n          style: {\n            background: 'rgba(255, 255, 255, 0.25)',\n            border: 'none',\n            borderRadius: isMobile ? '6px' : '8px',\n            width: isMobile ? '28px' : '32px',\n            height: isMobile ? '28px' : '32px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            cursor: 'pointer',\n            transition: 'all 0.2s ease',\n            backdropFilter: 'blur(10px)'\n          },\n          onMouseEnter: e => {\n            // Use CSS classes instead of direct style manipulation\n            if (e.target) {\n              e.target.classList.add('hover-close-button');\n            }\n          },\n          onMouseLeave: e => {\n            // Use CSS classes instead of direct style manipulation\n            if (e.target) {\n              e.target.classList.remove('hover-close-button');\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: 'white',\n              fontSize: isMobile ? '14px' : '16px',\n              fontWeight: 'bold',\n              textShadow: '0 1px 2px rgba(0,0,0,0.5)'\n            },\n            children: \"\\u2715\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 415,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 7\n    }, this), !isMinimized && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          padding: '20px 20px 0 20px',\n          overflowY: 'auto',\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '16px',\n          scrollBehavior: 'smooth',\n          scrollbarWidth: 'thin',\n          scrollbarColor: '#cbd5e1 transparent'\n        },\n        onWheel: e => {\n          const element = e.currentTarget;\n          const {\n            scrollTop,\n            scrollHeight,\n            clientHeight\n          } = element;\n\n          // Allow scrolling within the messages container\n          if (scrollTop > 0 && scrollTop + clientHeight < scrollHeight) {\n            // We're in the middle, allow normal scrolling\n            return;\n          }\n\n          // At the boundaries, prevent propagation to background\n          if (scrollTop === 0 && e.deltaY < 0 || scrollTop + clientHeight >= scrollHeight && e.deltaY > 0) {\n            e.preventDefault();\n            e.stopPropagation();\n          }\n        },\n        className: \"custom-scrollbar\",\n        children: [messages.map((msg, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: msg.role === 'user' ? 'flex-end' : 'flex-start'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              maxWidth: '85%',\n              padding: '12px 16px',\n              borderRadius: msg.role === 'user' ? '18px 18px 4px 18px' : '18px 18px 18px 4px',\n              background: msg.role === 'user' ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : '#f8fafc',\n              color: msg.role === 'user' ? 'white' : '#334155',\n              fontSize: '14px',\n              lineHeight: '1.5',\n              boxShadow: msg.role === 'user' ? '0 4px 12px rgba(102, 126, 234, 0.3)' : '0 2px 8px rgba(0, 0, 0, 0.1)',\n              wordWrap: 'break-word'\n            },\n            children: typeof msg.content === 'string' ? msg.role === 'assistant' ? /*#__PURE__*/_jsxDEV(ContentRenderer, {\n              text: msg.content\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 23\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                whiteSpace: 'pre-wrap'\n              },\n              children: msg.content\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 23\n            }, this) : Array.isArray(msg.content) ? msg.content.map((item, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [item.type === 'text' && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: item.text ? '8px' : '0'\n                },\n                children: msg.role === 'assistant' ? /*#__PURE__*/_jsxDEV(ContentRenderer, {\n                  text: item.text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 496,\n                  columnNumber: 31\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    whiteSpace: 'pre-wrap'\n                  },\n                  children: item.text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 498,\n                  columnNumber: 31\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 494,\n                columnNumber: 27\n              }, this), item.type === 'image_url' && /*#__PURE__*/_jsxDEV(\"img\", {\n                src: item.image_url.url,\n                alt: \"User upload\",\n                style: {\n                  maxWidth: '100%',\n                  height: 'auto',\n                  borderRadius: '12px',\n                  maxHeight: '200px',\n                  objectFit: 'cover',\n                  border: '2px solid rgba(255, 255, 255, 0.2)',\n                  marginTop: '4px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 55\n              }, this)]\n            }, idx, true, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 23\n            }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n              children: \"Invalid message format\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 483,\n            columnNumber: 17\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 482,\n          columnNumber: 15\n        }, this)), isTyping && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'flex-start'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '12px 16px',\n              borderRadius: '18px 18px 18px 4px',\n              background: '#f8fafc',\n              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                gap: '4px',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '6px',\n                  height: '6px',\n                  borderRadius: '50%',\n                  background: '#94a3b8',\n                  animation: 'bounce 1.4s infinite ease-in-out'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '6px',\n                  height: '6px',\n                  borderRadius: '50%',\n                  background: '#94a3b8',\n                  animation: 'bounce 1.4s infinite ease-in-out 0.16s'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '6px',\n                  height: '6px',\n                  borderRadius: '50%',\n                  background: '#94a3b8',\n                  animation: 'bounce 1.4s infinite ease-in-out 0.32s'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 518,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 513,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: messagesEndRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 524,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 450,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'sticky',\n          bottom: 0,\n          background: 'rgba(255, 255, 255, 0.98)',\n          backdropFilter: 'blur(20px)',\n          borderTop: '1px solid rgba(0, 0, 0, 0.08)',\n          padding: isMobile ? '12px 16px 16px' : '16px 20px 20px',\n          zIndex: 10\n        },\n        children: [imagePreview && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '12px',\n            padding: '12px',\n            background: '#f1f5f9',\n            borderRadius: '12px',\n            border: '1px solid #e2e8f0'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '12px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'relative'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: imagePreview,\n                alt: \"Preview\",\n                style: {\n                  width: '60px',\n                  height: '60px',\n                  objectFit: 'cover',\n                  borderRadius: '8px',\n                  border: '2px solid #e2e8f0'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: removeImage,\n                style: {\n                  position: 'absolute',\n                  top: '-6px',\n                  right: '-6px',\n                  width: '20px',\n                  height: '20px',\n                  background: '#ef4444',\n                  color: 'white',\n                  borderRadius: '50%',\n                  border: 'none',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  fontSize: '12px',\n                  fontWeight: 'bold'\n                },\n                children: \"\\xD7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 531,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                flex: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '13px',\n                  fontWeight: '600',\n                  color: '#374151',\n                  margin: 0\n                },\n                children: \"Image attached\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  fontSize: '11px',\n                  color: '#6b7280',\n                  margin: 0\n                },\n                children: selectedImage === null || selectedImage === void 0 ? void 0 : selectedImage.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 529,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: isMobile ? '6px' : '8px',\n            background: '#f8fafc',\n            borderRadius: isMobile ? '12px' : '16px',\n            padding: isMobile ? '6px' : '8px',\n            border: '2px solid #e2e8f0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              var _fileInputRef$current;\n              return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n            },\n            style: {\n              background: '#3b82f6',\n              border: 'none',\n              borderRadius: isMobile ? '8px' : '12px',\n              width: isMobile ? '36px' : '40px',\n              height: isMobile ? '36px' : '40px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              cursor: 'pointer',\n              transition: 'all 0.2s ease',\n              boxShadow: '0 2px 8px rgba(59, 130, 246, 0.3)'\n            },\n            title: \"Attach image\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: 'white',\n                fontSize: isMobile ? '18px' : '20px',\n                fontWeight: 'bold'\n              },\n              children: \"+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 561,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            ref: inputRef,\n            value: input,\n            onChange: e => setInput(e.target.value),\n            onKeyDown: handleKeyDown,\n            placeholder: isKiswahili ? \"Uliza chochote...\" : \"Ask me anything...\",\n            rows: 1,\n            style: {\n              flex: 1,\n              border: 'none',\n              background: 'transparent',\n              outline: 'none',\n              fontSize: isMobile ? '12px' : '14px',\n              color: '#334155',\n              padding: isMobile ? '10px 12px' : '12px 16px',\n              fontFamily: 'inherit'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 564,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: sendMessage,\n            disabled: !input.trim() && !selectedImage,\n            style: {\n              background: input.trim() || selectedImage ? '#3b82f6' : '#e2e8f0',\n              border: 'none',\n              borderRadius: isMobile ? '8px' : '12px',\n              width: isMobile ? '36px' : '40px',\n              height: isMobile ? '36px' : '40px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              cursor: input.trim() || selectedImage ? 'pointer' : 'not-allowed',\n              transition: 'all 0.2s ease',\n              boxShadow: input.trim() || selectedImage ? '0 2px 8px rgba(59, 130, 246, 0.3)' : 'none'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: input.trim() || selectedImage ? 'white' : '#94a3b8',\n                fontSize: isMobile ? '16px' : '18px',\n                fontWeight: 'bold'\n              },\n              children: \"\\u27A4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 600,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 583,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 543,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          ref: fileInputRef,\n          type: \"file\",\n          accept: \"image/*\",\n          onChange: handleImageSelect,\n          style: {\n            display: 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 604,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            fontSize: isMobile ? '9px' : '11px',\n            color: '#94a3b8',\n            textAlign: 'center',\n            margin: isMobile ? '6px 0 0 0' : '8px 0 0 0'\n          },\n          children: isMobile ? 'Enter to send • Attach images' : 'Press Enter to send • Attach images for analysis'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 606,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 527,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n        @keyframes bounce {\n          0%, 80%, 100% { transform: scale(0); }\n          40% { transform: scale(1); }\n        }\n\n        .custom-scrollbar::-webkit-scrollbar {\n          width: 6px;\n        }\n\n        .custom-scrollbar::-webkit-scrollbar-track {\n          background: transparent;\n        }\n\n        .custom-scrollbar::-webkit-scrollbar-thumb {\n          background: #cbd5e1;\n          border-radius: 3px;\n        }\n\n        .custom-scrollbar::-webkit-scrollbar-thumb:hover {\n          background: #94a3b8;\n        }\n\n        .hover-close-button {\n          background: rgba(255, 60, 60, 0.4) !important;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 613,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 300,\n    columnNumber: 5\n  }, this);\n};\n_s(FloatingBrainwaveAI, \"Ox6DHe539XxxitvjCyThPxfgkSY=\", false, function () {\n  return [useSelector, useLanguage];\n});\n_c = FloatingBrainwaveAI;\nexport default FloatingBrainwaveAI;\nvar _c;\n$RefreshReg$(_c, \"FloatingBrainwaveAI\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "TbRobot", "chatWithChatGPT", "uploadImg", "useLanguage", "useSelector", "Content<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "FloatingBrainwaveAI", "_s", "user", "state", "t", "isKiswahili", "isOpen", "setIsOpen", "isMinimized", "setIsMinimized", "isMaximized", "setIsMaximized", "isMobile", "setIsMobile", "window", "innerWidth", "isTablet", "setIsTablet", "handleResize", "addEventListener", "removeEventListener", "handleStorageChange", "e", "key", "newValue", "clearChat", "welcomeMessage", "setMessages", "role", "content", "setInput", "removeImage", "handleClose", "messages", "input", "isTyping", "setIsTyping", "selectedImage", "setSelectedImage", "imagePreview", "setImagePreview", "showPasteButton", "setShowPasteButton", "messagesEndRef", "fileInputRef", "inputRef", "current", "scrollIntoView", "behavior", "handleImageSelect", "event", "file", "target", "files", "type", "startsWith", "reader", "FileReader", "onload", "result", "readAsDataURL", "value", "checkClipboard", "navigator", "clipboard", "readText", "text", "trim", "length", "err", "pasteFromClipboard", "prevInput", "newInput", "focus", "console", "warn", "alert", "interval", "setInterval", "clearInterval", "sendMessage", "userMessage", "imageFile", "imageUrl", "formData", "FormData", "append", "uploadResult", "success", "url", "newUserMessage", "image_url", "prev", "chatPayload", "language", "systemPrompt", "response", "data", "errorMessage", "error", "handleKeyDown", "shift<PERSON>ey", "preventDefault", "onClick", "style", "position", "bottom", "right", "width", "height", "background", "borderRadius", "display", "alignItems", "justifyContent", "cursor", "boxShadow", "zIndex", "transition", "animation", "children", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getResponsiveDimensions", "top", "left", "dimensions", "<PERSON><PERSON>ilter", "border", "flexDirection", "overflow", "onWheel", "stopPropagation", "onTouchMove", "padding", "minHeight", "gap", "margin", "fontWeight", "textShadow", "onMouseEnter", "classList", "add", "onMouseLeave", "remove", "flex", "overflowY", "scroll<PERSON>eh<PERSON>or", "scrollbarWidth", "scrollbarColor", "element", "currentTarget", "scrollTop", "scrollHeight", "clientHeight", "deltaY", "className", "map", "msg", "index", "max<PERSON><PERSON><PERSON>", "lineHeight", "wordWrap", "whiteSpace", "Array", "isArray", "item", "idx", "marginBottom", "src", "alt", "maxHeight", "objectFit", "marginTop", "ref", "borderTop", "name", "_fileInputRef$current", "click", "title", "onChange", "onKeyDown", "placeholder", "rows", "outline", "fontFamily", "disabled", "accept", "textAlign", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/FloatingBrainwaveAI.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport { TbRobot } from 'react-icons/tb';\r\nimport { chatWithChatGPT, uploadImg } from '../apicalls/chat';\r\nimport { useLanguage } from '../contexts/LanguageContext';\r\nimport { useSelector } from 'react-redux';\r\nimport ContentRenderer from './ContentRenderer';\r\n\r\nconst FloatingBrainwaveAI = () => {\r\n  const { user } = useSelector((state) => state.user);\r\n  const { t, isKiswahili } = useLanguage();\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [isMinimized, setIsMinimized] = useState(false);\r\n  const [isMaximized, setIsMaximized] = useState(false);\r\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);\r\n  const [isTablet, setIsTablet] = useState(window.innerWidth > 768 && window.innerWidth <= 1024);\r\n\r\n  // Handle responsive design\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsMobile(window.innerWidth <= 768);\r\n      setIsTablet(window.innerWidth > 768 && window.innerWidth <= 1024);\r\n    };\r\n\r\n    window.addEventListener('resize', handleResize);\r\n    return () => window.removeEventListener('resize', handleResize);\r\n  }, []);\r\n\r\n  // Clear chat when user logs out (listen for storage changes)\r\n  useEffect(() => {\r\n    const handleStorageChange = (e) => {\r\n      if (e.key === 'token' && !e.newValue) {\r\n        // Token was removed, user logged out\r\n        clearChat();\r\n        setIsOpen(false);\r\n      }\r\n    };\r\n\r\n    window.addEventListener('storage', handleStorageChange);\r\n    return () => window.removeEventListener('storage', handleStorageChange);\r\n  }, []);\r\n\r\n  // Clear chat function\r\n  const clearChat = () => {\r\n    const welcomeMessage = isKiswahili\r\n      ? 'Hujambo! Mimi ni Brainwave AI, msaidizi wako wa masomo. Ninawezaje kukusaidia leo?'\r\n      : 'Hi! I am Brainwave AI, your study assistant. How can I help you today?';\r\n\r\n    setMessages([\r\n      { role: 'assistant', content: welcomeMessage }\r\n    ]);\r\n    setInput('');\r\n    removeImage();\r\n  };\r\n\r\n  // Clear chat when closing\r\n  const handleClose = () => {\r\n    clearChat();\r\n    setIsOpen(false);\r\n    setIsMinimized(false);\r\n    setIsMaximized(false);\r\n  };\r\n  const [messages, setMessages] = useState([\r\n    { role: 'assistant', content: isKiswahili\r\n      ? 'Hujambo! Mimi ni Brainwave AI, msaidizi wako wa masomo. Ninawezaje kukusaidia leo?'\r\n      : 'Hi! I am Brainwave AI, your study assistant. How can I help you today?' }\r\n  ]);\r\n  const [input, setInput] = useState('');\r\n  const [isTyping, setIsTyping] = useState(false);\r\n  const [selectedImage, setSelectedImage] = useState(null);\r\n  const [imagePreview, setImagePreview] = useState(null);\r\n  const [showPasteButton, setShowPasteButton] = useState(false);\r\n  const messagesEndRef = useRef(null);\r\n  const fileInputRef = useRef(null);\r\n  const inputRef = useRef(null);\r\n\r\n  useEffect(() => {\r\n    if (messagesEndRef.current) {\r\n      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });\r\n    }\r\n  }, [messages]);\r\n\r\n  const handleImageSelect = (event) => {\r\n    const file = event.target.files[0];\r\n    if (file && file.type.startsWith('image/')) {\r\n      setSelectedImage(file);\r\n      const reader = new FileReader();\r\n      reader.onload = (e) => setImagePreview(e.target.result);\r\n      reader.readAsDataURL(file);\r\n    }\r\n  };\r\n\r\n  const removeImage = () => {\r\n    setSelectedImage(null);\r\n    setImagePreview(null);\r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.value = '';\r\n    }\r\n  };\r\n\r\n  // Check if clipboard has text content\r\n  const checkClipboard = async () => {\r\n    try {\r\n      if (navigator.clipboard && navigator.clipboard.readText) {\r\n        const text = await navigator.clipboard.readText();\r\n        setShowPasteButton(text.trim().length > 0);\r\n      }\r\n    } catch (err) {\r\n      // Clipboard access denied or not available\r\n      setShowPasteButton(false);\r\n    }\r\n  };\r\n\r\n  // Paste from clipboard\r\n  const pasteFromClipboard = async () => {\r\n    try {\r\n      if (navigator.clipboard && navigator.clipboard.readText) {\r\n        const text = await navigator.clipboard.readText();\r\n        if (text.trim()) {\r\n          setInput(prevInput => {\r\n            const newInput = prevInput + (prevInput ? '\\n' : '') + text.trim();\r\n            return newInput;\r\n          });\r\n          setShowPasteButton(false);\r\n          // Focus the input after pasting\r\n          if (inputRef.current) {\r\n            inputRef.current.focus();\r\n          }\r\n        }\r\n      }\r\n    } catch (err) {\r\n      console.warn('Could not paste from clipboard:', err);\r\n      // Fallback: show alert to manually paste\r\n      alert(isKiswahili\r\n        ? 'Tumia Ctrl+V au Cmd+V kubandika maandishi'\r\n        : 'Use Ctrl+V or Cmd+V to paste text');\r\n    }\r\n  };\r\n\r\n  // Check clipboard when component opens\r\n  React.useEffect(() => {\r\n    if (isOpen) {\r\n      checkClipboard();\r\n      // Check clipboard periodically when chat is open\r\n      const interval = setInterval(checkClipboard, 2000);\r\n      return () => clearInterval(interval);\r\n    }\r\n  }, [isOpen]);\r\n\r\n  const sendMessage = async () => {\r\n    if (!input.trim() && !selectedImage) return;\r\n\r\n    const userMessage = input.trim();\r\n    const imageFile = selectedImage;\r\n\r\n    setInput('');\r\n    removeImage();\r\n    setIsTyping(true);\r\n\r\n    try {\r\n      let imageUrl = null;\r\n\r\n      if (imageFile) {\r\n        const formData = new FormData();\r\n        formData.append(\"image\", imageFile);\r\n        const uploadResult = await uploadImg(formData);\r\n\r\n        if (uploadResult?.success) {\r\n          imageUrl = uploadResult.url;\r\n        }\r\n      }\r\n\r\n      const newUserMessage = imageUrl\r\n        ? {\r\n            role: \"user\",\r\n            content: [\r\n              { type: \"text\", text: userMessage },\r\n              { type: \"image_url\", image_url: { url: imageUrl } }\r\n            ]\r\n          }\r\n        : { role: \"user\", content: userMessage };\r\n\r\n      setMessages(prev => [...prev, newUserMessage]);\r\n\r\n      // Add language preference for Kiswahili users\r\n      const chatPayload = {\r\n        messages: [...messages, newUserMessage],\r\n        ...(isKiswahili && {\r\n          language: 'kiswahili',\r\n          systemPrompt: 'Jibu kwa lugha ya Kiswahili tu. Wewe ni msaidizi wa masomo wa Tanzania. Tumia lugha rahisi na ya kielimu.'\r\n        })\r\n      };\r\n\r\n      const response = await chatWithChatGPT(chatPayload);\r\n\r\n      if (response?.success && response?.data) {\r\n        setMessages(prev => [...prev, {\r\n          role: 'assistant',\r\n          content: response.data\r\n        }]);\r\n      } else {\r\n        const errorMessage = isKiswahili\r\n          ? \"Samahani, sikuweza kuchakata ombi lako sasa hivi. Tafadhali jaribu tena baada ya muda.\"\r\n          : \"I'm sorry, I couldn't process your request right now. Please try again in a moment.\";\r\n\r\n        setMessages(prev => [...prev, {\r\n          role: 'assistant',\r\n          content: errorMessage\r\n        }]);\r\n      }\r\n    } catch (error) {\r\n      const errorMessage = isKiswahili\r\n        ? \"Nina matatizo ya kiufundi. Tafadhali jaribu tena baadaye.\"\r\n        : \"I'm experiencing some technical difficulties. Please try again later.\";\r\n\r\n      setMessages(prev => [...prev, {\r\n        role: 'assistant',\r\n        content: errorMessage\r\n      }]);\r\n    } finally {\r\n      setIsTyping(false);\r\n    }\r\n  };\r\n\r\n  const handleKeyDown = (e) => {\r\n    if (e.key === 'Enter' && !e.shiftKey) {\r\n      e.preventDefault();\r\n      sendMessage();\r\n    }\r\n  };\r\n\r\n  if (!isOpen) {\r\n    return (\r\n      <div\r\n        onClick={() => setIsOpen(true)}\r\n        style={{\r\n          position: 'fixed',\r\n          bottom: isMobile ? '15px' : '20px',\r\n          right: isMobile ? '15px' : '20px',\r\n          width: isMobile ? '50px' : '60px',\r\n          height: isMobile ? '50px' : '60px',\r\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n          borderRadius: '50%',\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          justifyContent: 'center',\r\n          cursor: 'pointer',\r\n          boxShadow: '0 8px 32px rgba(102, 126, 234, 0.4)',\r\n          zIndex: 1000,\r\n          transition: 'all 0.3s ease',\r\n          animation: 'pulse 2s infinite'\r\n        }}\r\n      >\r\n        <TbRobot style={{ color: 'white', fontSize: isMobile ? '24px' : '28px' }} />\r\n        <style>{`\r\n          @keyframes pulse {\r\n            0% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4); }\r\n            50% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.8); }\r\n            100% { box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4); }\r\n          }\r\n        `}</style>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Get responsive dimensions\r\n  const getResponsiveDimensions = () => {\r\n    if (isMobile) {\r\n      return {\r\n        width: isMinimized ? '280px' : isMaximized ? 'calc(100vw - 10px)' : '320px',\r\n        height: isMinimized ? '50px' : isMaximized ? 'calc(100vh - 10px)' : '450px',\r\n        bottom: isMaximized ? '5px' : '15px',\r\n        right: isMaximized ? '5px' : '15px',\r\n        top: isMaximized ? '5px' : 'auto',\r\n        left: isMaximized ? '5px' : 'auto'\r\n      };\r\n    } else if (isTablet) {\r\n      return {\r\n        width: isMinimized ? '320px' : isMaximized ? 'calc(100vw - 15px)' : '360px',\r\n        height: isMinimized ? '55px' : isMaximized ? 'calc(100vh - 15px)' : '480px',\r\n        bottom: isMaximized ? '7px' : '18px',\r\n        right: isMaximized ? '7px' : '18px',\r\n        top: isMaximized ? '7px' : 'auto',\r\n        left: isMaximized ? '7px' : 'auto'\r\n      };\r\n    } else {\r\n      return {\r\n        width: isMinimized ? '300px' : isMaximized ? 'calc(100vw - 20px)' : '380px',\r\n        height: isMinimized ? '60px' : isMaximized ? 'calc(100vh - 20px)' : '500px',\r\n        bottom: isMaximized ? '10px' : '20px',\r\n        right: isMaximized ? '10px' : '20px',\r\n        top: isMaximized ? '10px' : 'auto',\r\n        left: isMaximized ? '10px' : 'auto'\r\n      };\r\n    }\r\n  };\r\n\r\n  const dimensions = getResponsiveDimensions();\r\n\r\n  return (\r\n    <div\r\n      style={{\r\n        position: 'fixed',\r\n        ...dimensions,\r\n        background: 'rgba(255, 255, 255, 0.95)',\r\n        backdropFilter: 'blur(20px)',\r\n        borderRadius: isMaximized ? '15px' : isMobile ? '15px' : '20px',\r\n        boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',\r\n        border: '1px solid rgba(255, 255, 255, 0.2)',\r\n        zIndex: 1000,\r\n        display: 'flex',\r\n        flexDirection: 'column',\r\n        overflow: 'hidden',\r\n        transition: 'all 0.3s ease'\r\n      }}\r\n      onWheel={(e) => {\r\n        // Only prevent background scrolling when AI chat is open and not minimized\r\n        if (isOpen && !isMinimized) {\r\n          e.stopPropagation();\r\n        }\r\n      }}\r\n      onTouchMove={(e) => {\r\n        // Only prevent background scrolling when AI chat is open and not minimized\r\n        if (isOpen && !isMinimized) {\r\n          e.stopPropagation();\r\n        }\r\n      }}\r\n    >\r\n      <div\r\n        style={{\r\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\r\n          padding: isMobile ? '12px 16px' : '16px 20px',\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          justifyContent: 'space-between',\r\n          borderRadius: isMaximized ? '15px 15px 0 0' : isMobile ? '15px 15px 0 0' : '20px 20px 0 0',\r\n          minHeight: isMinimized ? (isMobile ? '50px' : '60px') : 'auto'\r\n        }}\r\n      >\r\n        <div style={{ display: 'flex', alignItems: 'center', gap: isMobile ? '8px' : '12px' }}>\r\n          <div style={{\r\n            width: isMobile ? '28px' : '32px',\r\n            height: isMobile ? '28px' : '32px',\r\n            background: 'rgba(255, 255, 255, 0.2)',\r\n            borderRadius: '50%',\r\n            display: 'flex',\r\n            alignItems: 'center',\r\n            justifyContent: 'center'\r\n          }}>\r\n            <TbRobot style={{ color: 'white', fontSize: isMobile ? '16px' : '18px' }} />\r\n          </div>\r\n          {!isMinimized && (\r\n            <div>\r\n              <h3 style={{ margin: 0, color: 'white', fontSize: isMobile ? '14px' : '16px', fontWeight: '600' }}>\r\n                {isKiswahili ? 'Akili ya Brainwave' : 'Brainwave AI'}\r\n              </h3>\r\n              <p style={{ margin: 0, color: 'rgba(255, 255, 255, 0.8)', fontSize: isMobile ? '10px' : '12px' }}>\r\n                {isKiswahili ? 'Daima hapa kukusaidia' : 'Always here to help'}\r\n              </p>\r\n            </div>\r\n          )}\r\n          {isMinimized && (\r\n            <div>\r\n              <h3 style={{ margin: 0, color: 'white', fontSize: isMobile ? '12px' : '14px', fontWeight: '600' }}>Brainwave AI</h3>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        <div style={{ display: 'flex', gap: isMobile ? '4px' : '8px' }}>\r\n          <button\r\n            onClick={() => setIsMinimized(!isMinimized)}\r\n            style={{\r\n              background: isMinimized ? 'rgba(255, 255, 255, 0.4)' : 'rgba(255, 255, 255, 0.25)',\r\n              border: 'none',\r\n              borderRadius: isMobile ? '6px' : '8px',\r\n              width: isMobile ? '28px' : '32px',\r\n              height: isMobile ? '28px' : '32px',\r\n              display: 'flex',\r\n              alignItems: 'center',\r\n              justifyContent: 'center',\r\n              cursor: 'pointer',\r\n              transition: 'all 0.2s ease',\r\n              backdropFilter: 'blur(10px)',\r\n              boxShadow: isMinimized ? '0 2px 8px rgba(255, 255, 255, 0.3)' : 'none'\r\n            }}\r\n          >\r\n            <span style={{ color: 'white', fontSize: isMobile ? '14px' : '16px', fontWeight: 'bold', textShadow: '0 1px 2px rgba(0,0,0,0.5)' }}>\r\n              {isMinimized ? '⬆' : '➖'}\r\n            </span>\r\n          </button>\r\n\r\n          {!isMinimized && (\r\n            <button\r\n              onClick={() => { setIsMaximized(!isMaximized); if (isMinimized) setIsMinimized(false); }}\r\n              style={{\r\n                background: isMaximized ? 'rgba(255, 255, 255, 0.4)' : 'rgba(255, 255, 255, 0.25)',\r\n                border: 'none',\r\n                borderRadius: isMobile ? '6px' : '8px',\r\n                width: isMobile ? '28px' : '32px',\r\n                height: isMobile ? '28px' : '32px',\r\n                display: 'flex',\r\n                alignItems: 'center',\r\n                justifyContent: 'center',\r\n                cursor: 'pointer',\r\n                transition: 'all 0.2s ease',\r\n                backdropFilter: 'blur(10px)',\r\n                boxShadow: isMaximized ? '0 2px 8px rgba(255, 255, 255, 0.3)' : 'none'\r\n              }}\r\n            >\r\n              <span style={{ color: 'white', fontSize: isMobile ? '14px' : '16px', fontWeight: 'bold', textShadow: '0 1px 2px rgba(0,0,0,0.5)' }}>\r\n                {isMaximized ? '⬇' : '⬆'}\r\n              </span>\r\n            </button>\r\n          )}\r\n\r\n          <button\r\n            onClick={handleClose}\r\n            style={{\r\n              background: 'rgba(255, 255, 255, 0.25)',\r\n              border: 'none',\r\n              borderRadius: isMobile ? '6px' : '8px',\r\n              width: isMobile ? '28px' : '32px',\r\n              height: isMobile ? '28px' : '32px',\r\n              display: 'flex',\r\n              alignItems: 'center',\r\n              justifyContent: 'center',\r\n              cursor: 'pointer',\r\n              transition: 'all 0.2s ease',\r\n              backdropFilter: 'blur(10px)'\r\n            }}\r\n            onMouseEnter={(e) => {\r\n              // Use CSS classes instead of direct style manipulation\r\n              if (e.target) {\r\n                e.target.classList.add('hover-close-button');\r\n              }\r\n            }}\r\n            onMouseLeave={(e) => {\r\n              // Use CSS classes instead of direct style manipulation\r\n              if (e.target) {\r\n                e.target.classList.remove('hover-close-button');\r\n              }\r\n            }}\r\n          >\r\n            <span style={{ color: 'white', fontSize: isMobile ? '14px' : '16px', fontWeight: 'bold', textShadow: '0 1px 2px rgba(0,0,0,0.5)' }}>✕</span>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {!isMinimized && (\r\n        <>\r\n          <div\r\n            style={{\r\n              flex: 1,\r\n              padding: '20px 20px 0 20px',\r\n              overflowY: 'auto',\r\n              display: 'flex',\r\n              flexDirection: 'column',\r\n              gap: '16px',\r\n              scrollBehavior: 'smooth',\r\n              scrollbarWidth: 'thin',\r\n              scrollbarColor: '#cbd5e1 transparent'\r\n            }}\r\n            onWheel={(e) => {\r\n              const element = e.currentTarget;\r\n              const { scrollTop, scrollHeight, clientHeight } = element;\r\n\r\n              // Allow scrolling within the messages container\r\n              if (scrollTop > 0 && scrollTop + clientHeight < scrollHeight) {\r\n                // We're in the middle, allow normal scrolling\r\n                return;\r\n              }\r\n\r\n              // At the boundaries, prevent propagation to background\r\n              if ((scrollTop === 0 && e.deltaY < 0) ||\r\n                  (scrollTop + clientHeight >= scrollHeight && e.deltaY > 0)) {\r\n                e.preventDefault();\r\n                e.stopPropagation();\r\n              }\r\n            }}\r\n            className=\"custom-scrollbar\"\r\n          >\r\n            {messages.map((msg, index) => (\r\n              <div key={index} style={{ display: 'flex', justifyContent: msg.role === 'user' ? 'flex-end' : 'flex-start' }}>\r\n                <div style={{ maxWidth: '85%', padding: '12px 16px', borderRadius: msg.role === 'user' ? '18px 18px 4px 18px' : '18px 18px 18px 4px', background: msg.role === 'user' ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : '#f8fafc', color: msg.role === 'user' ? 'white' : '#334155', fontSize: '14px', lineHeight: '1.5', boxShadow: msg.role === 'user' ? '0 4px 12px rgba(102, 126, 234, 0.3)' : '0 2px 8px rgba(0, 0, 0, 0.1)', wordWrap: 'break-word' }}>\r\n                  {typeof msg.content === 'string' ? (\r\n                    msg.role === 'assistant' ? (\r\n                      <ContentRenderer text={msg.content} />\r\n                    ) : (\r\n                      <div style={{ whiteSpace: 'pre-wrap' }}>{msg.content}</div>\r\n                    )\r\n                  ) : Array.isArray(msg.content) ? (\r\n                    msg.content.map((item, idx) => (\r\n                      <div key={idx}>\r\n                        {item.type === 'text' && (\r\n                          <div style={{ marginBottom: item.text ? '8px' : '0' }}>\r\n                            {msg.role === 'assistant' ? (\r\n                              <ContentRenderer text={item.text} />\r\n                            ) : (\r\n                              <div style={{ whiteSpace: 'pre-wrap' }}>{item.text}</div>\r\n                            )}\r\n                          </div>\r\n                        )}\r\n                        {item.type === 'image_url' && <img src={item.image_url.url} alt=\"User upload\" style={{ maxWidth: '100%', height: 'auto', borderRadius: '12px', maxHeight: '200px', objectFit: 'cover', border: '2px solid rgba(255, 255, 255, 0.2)', marginTop: '4px' }} />}\r\n                      </div>\r\n                    ))\r\n                  ) : (\r\n                    <div>Invalid message format</div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            ))}\r\n\r\n            {isTyping && (\r\n              <div style={{ display: 'flex', justifyContent: 'flex-start' }}>\r\n                <div style={{ padding: '12px 16px', borderRadius: '18px 18px 18px 4px', background: '#f8fafc', boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)' }}>\r\n                  <div style={{ display: 'flex', gap: '4px', alignItems: 'center' }}>\r\n                    <div style={{ width: '6px', height: '6px', borderRadius: '50%', background: '#94a3b8', animation: 'bounce 1.4s infinite ease-in-out' }} />\r\n                    <div style={{ width: '6px', height: '6px', borderRadius: '50%', background: '#94a3b8', animation: 'bounce 1.4s infinite ease-in-out 0.16s' }} />\r\n                    <div style={{ width: '6px', height: '6px', borderRadius: '50%', background: '#94a3b8', animation: 'bounce 1.4s infinite ease-in-out 0.32s' }} />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            <div ref={messagesEndRef} />\r\n          </div>\r\n\r\n          <div style={{ position: 'sticky', bottom: 0, background: 'rgba(255, 255, 255, 0.98)', backdropFilter: 'blur(20px)', borderTop: '1px solid rgba(0, 0, 0, 0.08)', padding: isMobile ? '12px 16px 16px' : '16px 20px 20px', zIndex: 10 }}>\r\n            {imagePreview && (\r\n              <div style={{ marginBottom: '12px', padding: '12px', background: '#f1f5f9', borderRadius: '12px', border: '1px solid #e2e8f0' }}>\r\n                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\r\n                  <div style={{ position: 'relative' }}>\r\n                    <img src={imagePreview} alt=\"Preview\" style={{ width: '60px', height: '60px', objectFit: 'cover', borderRadius: '8px', border: '2px solid #e2e8f0' }} />\r\n                    <button onClick={removeImage} style={{ position: 'absolute', top: '-6px', right: '-6px', width: '20px', height: '20px', background: '#ef4444', color: 'white', borderRadius: '50%', border: 'none', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: '12px', fontWeight: 'bold' }}>×</button>\r\n                  </div>\r\n                  <div style={{ flex: 1 }}>\r\n                    <p style={{ fontSize: '13px', fontWeight: '600', color: '#374151', margin: 0 }}>Image attached</p>\r\n                    <p style={{ fontSize: '11px', color: '#6b7280', margin: 0 }}>{selectedImage?.name}</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            <div style={{ display: 'flex', gap: isMobile ? '6px' : '8px', background: '#f8fafc', borderRadius: isMobile ? '12px' : '16px', padding: isMobile ? '6px' : '8px', border: '2px solid #e2e8f0' }}>\r\n              <button\r\n                onClick={() => fileInputRef.current?.click()}\r\n                style={{\r\n                  background: '#3b82f6',\r\n                  border: 'none',\r\n                  borderRadius: isMobile ? '8px' : '12px',\r\n                  width: isMobile ? '36px' : '40px',\r\n                  height: isMobile ? '36px' : '40px',\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  justifyContent: 'center',\r\n                  cursor: 'pointer',\r\n                  transition: 'all 0.2s ease',\r\n                  boxShadow: '0 2px 8px rgba(59, 130, 246, 0.3)'\r\n                }}\r\n                title=\"Attach image\"\r\n              >\r\n                <span style={{ color: 'white', fontSize: isMobile ? '18px' : '20px', fontWeight: 'bold' }}>+</span>\r\n              </button>\r\n\r\n              <textarea\r\n                ref={inputRef}\r\n                value={input}\r\n                onChange={(e) => setInput(e.target.value)}\r\n                onKeyDown={handleKeyDown}\r\n                placeholder={isKiswahili ? \"Uliza chochote...\" : \"Ask me anything...\"}\r\n                rows={1}\r\n                style={{\r\n                  flex: 1,\r\n                  border: 'none',\r\n                  background: 'transparent',\r\n                  outline: 'none',\r\n                  fontSize: isMobile ? '12px' : '14px',\r\n                  color: '#334155',\r\n                  padding: isMobile ? '10px 12px' : '12px 16px',\r\n                  fontFamily: 'inherit'\r\n                }}\r\n              />\r\n\r\n              <button\r\n                onClick={sendMessage}\r\n                disabled={!input.trim() && !selectedImage}\r\n                style={{\r\n                  background: (input.trim() || selectedImage) ? '#3b82f6' : '#e2e8f0',\r\n                  border: 'none',\r\n                  borderRadius: isMobile ? '8px' : '12px',\r\n                  width: isMobile ? '36px' : '40px',\r\n                  height: isMobile ? '36px' : '40px',\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  justifyContent: 'center',\r\n                  cursor: (input.trim() || selectedImage) ? 'pointer' : 'not-allowed',\r\n                  transition: 'all 0.2s ease',\r\n                  boxShadow: (input.trim() || selectedImage) ? '0 2px 8px rgba(59, 130, 246, 0.3)' : 'none'\r\n                }}\r\n              >\r\n                <span style={{ color: (input.trim() || selectedImage) ? 'white' : '#94a3b8', fontSize: isMobile ? '16px' : '18px', fontWeight: 'bold' }}>➤</span>\r\n              </button>\r\n            </div>\r\n\r\n            <input ref={fileInputRef} type=\"file\" accept=\"image/*\" onChange={handleImageSelect} style={{ display: 'none' }} />\r\n\r\n            <p style={{ fontSize: isMobile ? '9px' : '11px', color: '#94a3b8', textAlign: 'center', margin: isMobile ? '6px 0 0 0' : '8px 0 0 0' }}>\r\n              {isMobile ? 'Enter to send • Attach images' : 'Press Enter to send • Attach images for analysis'}\r\n            </p>\r\n          </div>\r\n        </>\r\n      )}\r\n\r\n      <style>{`\r\n        @keyframes bounce {\r\n          0%, 80%, 100% { transform: scale(0); }\r\n          40% { transform: scale(1); }\r\n        }\r\n\r\n        .custom-scrollbar::-webkit-scrollbar {\r\n          width: 6px;\r\n        }\r\n\r\n        .custom-scrollbar::-webkit-scrollbar-track {\r\n          background: transparent;\r\n        }\r\n\r\n        .custom-scrollbar::-webkit-scrollbar-thumb {\r\n          background: #cbd5e1;\r\n          border-radius: 3px;\r\n        }\r\n\r\n        .custom-scrollbar::-webkit-scrollbar-thumb:hover {\r\n          background: #94a3b8;\r\n        }\r\n\r\n        .hover-close-button {\r\n          background: rgba(255, 60, 60, 0.4) !important;\r\n        }\r\n      `}</style>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FloatingBrainwaveAI;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,OAAO,QAAQ,gBAAgB;AACxC,SAASC,eAAe,EAAEC,SAAS,QAAQ,kBAAkB;AAC7D,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,WAAW,QAAQ,aAAa;AACzC,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEhD,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM;IAAEC;EAAK,CAAC,GAAGR,WAAW,CAAES,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE,CAAC;IAAEC;EAAY,CAAC,GAAGZ,WAAW,CAAC,CAAC;EACxC,MAAM,CAACa,MAAM,EAAEC,SAAS,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACqB,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC2B,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;EAClE,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAC2B,MAAM,CAACC,UAAU,GAAG,GAAG,IAAID,MAAM,CAACC,UAAU,IAAI,IAAI,CAAC;;EAE9F;EACA1B,SAAS,CAAC,MAAM;IACd,MAAM6B,YAAY,GAAGA,CAAA,KAAM;MACzBL,WAAW,CAACC,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;MACrCE,WAAW,CAACH,MAAM,CAACC,UAAU,GAAG,GAAG,IAAID,MAAM,CAACC,UAAU,IAAI,IAAI,CAAC;IACnE,CAAC;IAEDD,MAAM,CAACK,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAMJ,MAAM,CAACM,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA7B,SAAS,CAAC,MAAM;IACd,MAAMgC,mBAAmB,GAAIC,CAAC,IAAK;MACjC,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;QACpC;QACAC,SAAS,CAAC,CAAC;QACXlB,SAAS,CAAC,KAAK,CAAC;MAClB;IACF,CAAC;IAEDO,MAAM,CAACK,gBAAgB,CAAC,SAAS,EAAEE,mBAAmB,CAAC;IACvD,OAAO,MAAMP,MAAM,CAACM,mBAAmB,CAAC,SAAS,EAAEC,mBAAmB,CAAC;EACzE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMI,SAAS,GAAGA,CAAA,KAAM;IACtB,MAAMC,cAAc,GAAGrB,WAAW,GAC9B,oFAAoF,GACpF,wEAAwE;IAE5EsB,WAAW,CAAC,CACV;MAAEC,IAAI,EAAE,WAAW;MAAEC,OAAO,EAAEH;IAAe,CAAC,CAC/C,CAAC;IACFI,QAAQ,CAAC,EAAE,CAAC;IACZC,WAAW,CAAC,CAAC;EACf,CAAC;;EAED;EACA,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxBP,SAAS,CAAC,CAAC;IACXlB,SAAS,CAAC,KAAK,CAAC;IAChBE,cAAc,CAAC,KAAK,CAAC;IACrBE,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;EACD,MAAM,CAACsB,QAAQ,EAAEN,WAAW,CAAC,GAAGxC,QAAQ,CAAC,CACvC;IAAEyC,IAAI,EAAE,WAAW;IAAEC,OAAO,EAAExB,WAAW,GACrC,oFAAoF,GACpF;EAAyE,CAAC,CAC/E,CAAC;EACF,MAAM,CAAC6B,KAAK,EAAEJ,QAAQ,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACgD,QAAQ,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACkD,aAAa,EAAEC,gBAAgB,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACoD,YAAY,EAAEC,eAAe,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACsD,eAAe,EAAEC,kBAAkB,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAMwD,cAAc,GAAGvD,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMwD,YAAY,GAAGxD,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMyD,QAAQ,GAAGzD,MAAM,CAAC,IAAI,CAAC;EAE7BC,SAAS,CAAC,MAAM;IACd,IAAIsD,cAAc,CAACG,OAAO,EAAE;MAC1BH,cAAc,CAACG,OAAO,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAC/D;EACF,CAAC,EAAE,CAACf,QAAQ,CAAC,CAAC;EAEd,MAAMgB,iBAAiB,GAAIC,KAAK,IAAK;IACnC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,IAAIA,IAAI,CAACG,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;MAC1CjB,gBAAgB,CAACa,IAAI,CAAC;MACtB,MAAMK,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIpC,CAAC,IAAKkB,eAAe,CAAClB,CAAC,CAAC8B,MAAM,CAACO,MAAM,CAAC;MACvDH,MAAM,CAACI,aAAa,CAACT,IAAI,CAAC;IAC5B;EACF,CAAC;EAED,MAAMpB,WAAW,GAAGA,CAAA,KAAM;IACxBO,gBAAgB,CAAC,IAAI,CAAC;IACtBE,eAAe,CAAC,IAAI,CAAC;IACrB,IAAII,YAAY,CAACE,OAAO,EAAE;MACxBF,YAAY,CAACE,OAAO,CAACe,KAAK,GAAG,EAAE;IACjC;EACF,CAAC;;EAED;EACA,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,IAAIC,SAAS,CAACC,SAAS,IAAID,SAAS,CAACC,SAAS,CAACC,QAAQ,EAAE;QACvD,MAAMC,IAAI,GAAG,MAAMH,SAAS,CAACC,SAAS,CAACC,QAAQ,CAAC,CAAC;QACjDvB,kBAAkB,CAACwB,IAAI,CAACC,IAAI,CAAC,CAAC,CAACC,MAAM,GAAG,CAAC,CAAC;MAC5C;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZ;MACA3B,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAM4B,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,IAAIP,SAAS,CAACC,SAAS,IAAID,SAAS,CAACC,SAAS,CAACC,QAAQ,EAAE;QACvD,MAAMC,IAAI,GAAG,MAAMH,SAAS,CAACC,SAAS,CAACC,QAAQ,CAAC,CAAC;QACjD,IAAIC,IAAI,CAACC,IAAI,CAAC,CAAC,EAAE;UACfrC,QAAQ,CAACyC,SAAS,IAAI;YACpB,MAAMC,QAAQ,GAAGD,SAAS,IAAIA,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC,GAAGL,IAAI,CAACC,IAAI,CAAC,CAAC;YAClE,OAAOK,QAAQ;UACjB,CAAC,CAAC;UACF9B,kBAAkB,CAAC,KAAK,CAAC;UACzB;UACA,IAAIG,QAAQ,CAACC,OAAO,EAAE;YACpBD,QAAQ,CAACC,OAAO,CAAC2B,KAAK,CAAC,CAAC;UAC1B;QACF;MACF;IACF,CAAC,CAAC,OAAOJ,GAAG,EAAE;MACZK,OAAO,CAACC,IAAI,CAAC,iCAAiC,EAAEN,GAAG,CAAC;MACpD;MACAO,KAAK,CAACvE,WAAW,GACb,2CAA2C,GAC3C,mCAAmC,CAAC;IAC1C;EACF,CAAC;;EAED;EACAnB,KAAK,CAACG,SAAS,CAAC,MAAM;IACpB,IAAIiB,MAAM,EAAE;MACVwD,cAAc,CAAC,CAAC;MAChB;MACA,MAAMe,QAAQ,GAAGC,WAAW,CAAChB,cAAc,EAAE,IAAI,CAAC;MAClD,OAAO,MAAMiB,aAAa,CAACF,QAAQ,CAAC;IACtC;EACF,CAAC,EAAE,CAACvE,MAAM,CAAC,CAAC;EAEZ,MAAM0E,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAAC9C,KAAK,CAACiC,IAAI,CAAC,CAAC,IAAI,CAAC9B,aAAa,EAAE;IAErC,MAAM4C,WAAW,GAAG/C,KAAK,CAACiC,IAAI,CAAC,CAAC;IAChC,MAAMe,SAAS,GAAG7C,aAAa;IAE/BP,QAAQ,CAAC,EAAE,CAAC;IACZC,WAAW,CAAC,CAAC;IACbK,WAAW,CAAC,IAAI,CAAC;IAEjB,IAAI;MACF,IAAI+C,QAAQ,GAAG,IAAI;MAEnB,IAAID,SAAS,EAAE;QACb,MAAME,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEJ,SAAS,CAAC;QACnC,MAAMK,YAAY,GAAG,MAAM/F,SAAS,CAAC4F,QAAQ,CAAC;QAE9C,IAAIG,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEC,OAAO,EAAE;UACzBL,QAAQ,GAAGI,YAAY,CAACE,GAAG;QAC7B;MACF;MAEA,MAAMC,cAAc,GAAGP,QAAQ,GAC3B;QACEvD,IAAI,EAAE,MAAM;QACZC,OAAO,EAAE,CACP;UAAEyB,IAAI,EAAE,MAAM;UAAEY,IAAI,EAAEe;QAAY,CAAC,EACnC;UAAE3B,IAAI,EAAE,WAAW;UAAEqC,SAAS,EAAE;YAAEF,GAAG,EAAEN;UAAS;QAAE,CAAC;MAEvD,CAAC,GACD;QAAEvD,IAAI,EAAE,MAAM;QAAEC,OAAO,EAAEoD;MAAY,CAAC;MAE1CtD,WAAW,CAACiE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEF,cAAc,CAAC,CAAC;;MAE9C;MACA,MAAMG,WAAW,GAAG;QAClB5D,QAAQ,EAAE,CAAC,GAAGA,QAAQ,EAAEyD,cAAc,CAAC;QACvC,IAAIrF,WAAW,IAAI;UACjByF,QAAQ,EAAE,WAAW;UACrBC,YAAY,EAAE;QAChB,CAAC;MACH,CAAC;MAED,MAAMC,QAAQ,GAAG,MAAMzG,eAAe,CAACsG,WAAW,CAAC;MAEnD,IAAIG,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAER,OAAO,IAAIQ,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEC,IAAI,EAAE;QACvCtE,WAAW,CAACiE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;UAC5BhE,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAEmE,QAAQ,CAACC;QACpB,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL,MAAMC,YAAY,GAAG7F,WAAW,GAC5B,wFAAwF,GACxF,qFAAqF;QAEzFsB,WAAW,CAACiE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;UAC5BhE,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAEqE;QACX,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd,MAAMD,YAAY,GAAG7F,WAAW,GAC5B,2DAA2D,GAC3D,uEAAuE;MAE3EsB,WAAW,CAACiE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAC5BhE,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAEqE;MACX,CAAC,CAAC,CAAC;IACL,CAAC,SAAS;MACR9D,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAMgE,aAAa,GAAI9E,CAAC,IAAK;IAC3B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAAC+E,QAAQ,EAAE;MACpC/E,CAAC,CAACgF,cAAc,CAAC,CAAC;MAClBtB,WAAW,CAAC,CAAC;IACf;EACF,CAAC;EAED,IAAI,CAAC1E,MAAM,EAAE;IACX,oBACET,OAAA;MACE0G,OAAO,EAAEA,CAAA,KAAMhG,SAAS,CAAC,IAAI,CAAE;MAC/BiG,KAAK,EAAE;QACLC,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAE9F,QAAQ,GAAG,MAAM,GAAG,MAAM;QAClC+F,KAAK,EAAE/F,QAAQ,GAAG,MAAM,GAAG,MAAM;QACjCgG,KAAK,EAAEhG,QAAQ,GAAG,MAAM,GAAG,MAAM;QACjCiG,MAAM,EAAEjG,QAAQ,GAAG,MAAM,GAAG,MAAM;QAClCkG,UAAU,EAAE,mDAAmD;QAC/DC,YAAY,EAAE,KAAK;QACnBC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,MAAM,EAAE,SAAS;QACjBC,SAAS,EAAE,qCAAqC;QAChDC,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE,eAAe;QAC3BC,SAAS,EAAE;MACb,CAAE;MAAAC,QAAA,gBAEF3H,OAAA,CAACP,OAAO;QAACkH,KAAK,EAAE;UAAEiB,KAAK,EAAE,OAAO;UAAEC,QAAQ,EAAE9G,QAAQ,GAAG,MAAM,GAAG;QAAO;MAAE;QAAA+G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5EjI,OAAA;QAAA2H,QAAA,EAAS;AACjB;AACA;AACA;AACA;AACA;AACA;MAAS;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAEV;;EAEA;EACA,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAInH,QAAQ,EAAE;MACZ,OAAO;QACLgG,KAAK,EAAEpG,WAAW,GAAG,OAAO,GAAGE,WAAW,GAAG,oBAAoB,GAAG,OAAO;QAC3EmG,MAAM,EAAErG,WAAW,GAAG,MAAM,GAAGE,WAAW,GAAG,oBAAoB,GAAG,OAAO;QAC3EgG,MAAM,EAAEhG,WAAW,GAAG,KAAK,GAAG,MAAM;QACpCiG,KAAK,EAAEjG,WAAW,GAAG,KAAK,GAAG,MAAM;QACnCsH,GAAG,EAAEtH,WAAW,GAAG,KAAK,GAAG,MAAM;QACjCuH,IAAI,EAAEvH,WAAW,GAAG,KAAK,GAAG;MAC9B,CAAC;IACH,CAAC,MAAM,IAAIM,QAAQ,EAAE;MACnB,OAAO;QACL4F,KAAK,EAAEpG,WAAW,GAAG,OAAO,GAAGE,WAAW,GAAG,oBAAoB,GAAG,OAAO;QAC3EmG,MAAM,EAAErG,WAAW,GAAG,MAAM,GAAGE,WAAW,GAAG,oBAAoB,GAAG,OAAO;QAC3EgG,MAAM,EAAEhG,WAAW,GAAG,KAAK,GAAG,MAAM;QACpCiG,KAAK,EAAEjG,WAAW,GAAG,KAAK,GAAG,MAAM;QACnCsH,GAAG,EAAEtH,WAAW,GAAG,KAAK,GAAG,MAAM;QACjCuH,IAAI,EAAEvH,WAAW,GAAG,KAAK,GAAG;MAC9B,CAAC;IACH,CAAC,MAAM;MACL,OAAO;QACLkG,KAAK,EAAEpG,WAAW,GAAG,OAAO,GAAGE,WAAW,GAAG,oBAAoB,GAAG,OAAO;QAC3EmG,MAAM,EAAErG,WAAW,GAAG,MAAM,GAAGE,WAAW,GAAG,oBAAoB,GAAG,OAAO;QAC3EgG,MAAM,EAAEhG,WAAW,GAAG,MAAM,GAAG,MAAM;QACrCiG,KAAK,EAAEjG,WAAW,GAAG,MAAM,GAAG,MAAM;QACpCsH,GAAG,EAAEtH,WAAW,GAAG,MAAM,GAAG,MAAM;QAClCuH,IAAI,EAAEvH,WAAW,GAAG,MAAM,GAAG;MAC/B,CAAC;IACH;EACF,CAAC;EAED,MAAMwH,UAAU,GAAGH,uBAAuB,CAAC,CAAC;EAE5C,oBACElI,OAAA;IACE2G,KAAK,EAAE;MACLC,QAAQ,EAAE,OAAO;MACjB,GAAGyB,UAAU;MACbpB,UAAU,EAAE,2BAA2B;MACvCqB,cAAc,EAAE,YAAY;MAC5BpB,YAAY,EAAErG,WAAW,GAAG,MAAM,GAAGE,QAAQ,GAAG,MAAM,GAAG,MAAM;MAC/DwG,SAAS,EAAE,iCAAiC;MAC5CgB,MAAM,EAAE,oCAAoC;MAC5Cf,MAAM,EAAE,IAAI;MACZL,OAAO,EAAE,MAAM;MACfqB,aAAa,EAAE,QAAQ;MACvBC,QAAQ,EAAE,QAAQ;MAClBhB,UAAU,EAAE;IACd,CAAE;IACFiB,OAAO,EAAGjH,CAAC,IAAK;MACd;MACA,IAAIhB,MAAM,IAAI,CAACE,WAAW,EAAE;QAC1Bc,CAAC,CAACkH,eAAe,CAAC,CAAC;MACrB;IACF,CAAE;IACFC,WAAW,EAAGnH,CAAC,IAAK;MAClB;MACA,IAAIhB,MAAM,IAAI,CAACE,WAAW,EAAE;QAC1Bc,CAAC,CAACkH,eAAe,CAAC,CAAC;MACrB;IACF,CAAE;IAAAhB,QAAA,gBAEF3H,OAAA;MACE2G,KAAK,EAAE;QACLM,UAAU,EAAE,mDAAmD;QAC/D4B,OAAO,EAAE9H,QAAQ,GAAG,WAAW,GAAG,WAAW;QAC7CoG,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,eAAe;QAC/BH,YAAY,EAAErG,WAAW,GAAG,eAAe,GAAGE,QAAQ,GAAG,eAAe,GAAG,eAAe;QAC1F+H,SAAS,EAAEnI,WAAW,GAAII,QAAQ,GAAG,MAAM,GAAG,MAAM,GAAI;MAC1D,CAAE;MAAA4G,QAAA,gBAEF3H,OAAA;QAAK2G,KAAK,EAAE;UAAEQ,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAE2B,GAAG,EAAEhI,QAAQ,GAAG,KAAK,GAAG;QAAO,CAAE;QAAA4G,QAAA,gBACpF3H,OAAA;UAAK2G,KAAK,EAAE;YACVI,KAAK,EAAEhG,QAAQ,GAAG,MAAM,GAAG,MAAM;YACjCiG,MAAM,EAAEjG,QAAQ,GAAG,MAAM,GAAG,MAAM;YAClCkG,UAAU,EAAE,0BAA0B;YACtCC,YAAY,EAAE,KAAK;YACnBC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAM,QAAA,eACA3H,OAAA,CAACP,OAAO;YAACkH,KAAK,EAAE;cAAEiB,KAAK,EAAE,OAAO;cAAEC,QAAQ,EAAE9G,QAAQ,GAAG,MAAM,GAAG;YAAO;UAAE;YAAA+G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC,EACL,CAACtH,WAAW,iBACXX,OAAA;UAAA2H,QAAA,gBACE3H,OAAA;YAAI2G,KAAK,EAAE;cAAEqC,MAAM,EAAE,CAAC;cAAEpB,KAAK,EAAE,OAAO;cAAEC,QAAQ,EAAE9G,QAAQ,GAAG,MAAM,GAAG,MAAM;cAAEkI,UAAU,EAAE;YAAM,CAAE;YAAAtB,QAAA,EAC/FnH,WAAW,GAAG,oBAAoB,GAAG;UAAc;YAAAsH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACLjI,OAAA;YAAG2G,KAAK,EAAE;cAAEqC,MAAM,EAAE,CAAC;cAAEpB,KAAK,EAAE,0BAA0B;cAAEC,QAAQ,EAAE9G,QAAQ,GAAG,MAAM,GAAG;YAAO,CAAE;YAAA4G,QAAA,EAC9FnH,WAAW,GAAG,uBAAuB,GAAG;UAAqB;YAAAsH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN,EACAtH,WAAW,iBACVX,OAAA;UAAA2H,QAAA,eACE3H,OAAA;YAAI2G,KAAK,EAAE;cAAEqC,MAAM,EAAE,CAAC;cAAEpB,KAAK,EAAE,OAAO;cAAEC,QAAQ,EAAE9G,QAAQ,GAAG,MAAM,GAAG,MAAM;cAAEkI,UAAU,EAAE;YAAM,CAAE;YAAAtB,QAAA,EAAC;UAAY;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENjI,OAAA;QAAK2G,KAAK,EAAE;UAAEQ,OAAO,EAAE,MAAM;UAAE4B,GAAG,EAAEhI,QAAQ,GAAG,KAAK,GAAG;QAAM,CAAE;QAAA4G,QAAA,gBAC7D3H,OAAA;UACE0G,OAAO,EAAEA,CAAA,KAAM9F,cAAc,CAAC,CAACD,WAAW,CAAE;UAC5CgG,KAAK,EAAE;YACLM,UAAU,EAAEtG,WAAW,GAAG,0BAA0B,GAAG,2BAA2B;YAClF4H,MAAM,EAAE,MAAM;YACdrB,YAAY,EAAEnG,QAAQ,GAAG,KAAK,GAAG,KAAK;YACtCgG,KAAK,EAAEhG,QAAQ,GAAG,MAAM,GAAG,MAAM;YACjCiG,MAAM,EAAEjG,QAAQ,GAAG,MAAM,GAAG,MAAM;YAClCoG,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBC,MAAM,EAAE,SAAS;YACjBG,UAAU,EAAE,eAAe;YAC3Ba,cAAc,EAAE,YAAY;YAC5Bf,SAAS,EAAE5G,WAAW,GAAG,oCAAoC,GAAG;UAClE,CAAE;UAAAgH,QAAA,eAEF3H,OAAA;YAAM2G,KAAK,EAAE;cAAEiB,KAAK,EAAE,OAAO;cAAEC,QAAQ,EAAE9G,QAAQ,GAAG,MAAM,GAAG,MAAM;cAAEkI,UAAU,EAAE,MAAM;cAAEC,UAAU,EAAE;YAA4B,CAAE;YAAAvB,QAAA,EAChIhH,WAAW,GAAG,GAAG,GAAG;UAAG;YAAAmH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EAER,CAACtH,WAAW,iBACXX,OAAA;UACE0G,OAAO,EAAEA,CAAA,KAAM;YAAE5F,cAAc,CAAC,CAACD,WAAW,CAAC;YAAE,IAAIF,WAAW,EAAEC,cAAc,CAAC,KAAK,CAAC;UAAE,CAAE;UACzF+F,KAAK,EAAE;YACLM,UAAU,EAAEpG,WAAW,GAAG,0BAA0B,GAAG,2BAA2B;YAClF0H,MAAM,EAAE,MAAM;YACdrB,YAAY,EAAEnG,QAAQ,GAAG,KAAK,GAAG,KAAK;YACtCgG,KAAK,EAAEhG,QAAQ,GAAG,MAAM,GAAG,MAAM;YACjCiG,MAAM,EAAEjG,QAAQ,GAAG,MAAM,GAAG,MAAM;YAClCoG,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBC,MAAM,EAAE,SAAS;YACjBG,UAAU,EAAE,eAAe;YAC3Ba,cAAc,EAAE,YAAY;YAC5Bf,SAAS,EAAE1G,WAAW,GAAG,oCAAoC,GAAG;UAClE,CAAE;UAAA8G,QAAA,eAEF3H,OAAA;YAAM2G,KAAK,EAAE;cAAEiB,KAAK,EAAE,OAAO;cAAEC,QAAQ,EAAE9G,QAAQ,GAAG,MAAM,GAAG,MAAM;cAAEkI,UAAU,EAAE,MAAM;cAAEC,UAAU,EAAE;YAA4B,CAAE;YAAAvB,QAAA,EAChI9G,WAAW,GAAG,GAAG,GAAG;UAAG;YAAAiH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACT,eAEDjI,OAAA;UACE0G,OAAO,EAAEvE,WAAY;UACrBwE,KAAK,EAAE;YACLM,UAAU,EAAE,2BAA2B;YACvCsB,MAAM,EAAE,MAAM;YACdrB,YAAY,EAAEnG,QAAQ,GAAG,KAAK,GAAG,KAAK;YACtCgG,KAAK,EAAEhG,QAAQ,GAAG,MAAM,GAAG,MAAM;YACjCiG,MAAM,EAAEjG,QAAQ,GAAG,MAAM,GAAG,MAAM;YAClCoG,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBC,MAAM,EAAE,SAAS;YACjBG,UAAU,EAAE,eAAe;YAC3Ba,cAAc,EAAE;UAClB,CAAE;UACFa,YAAY,EAAG1H,CAAC,IAAK;YACnB;YACA,IAAIA,CAAC,CAAC8B,MAAM,EAAE;cACZ9B,CAAC,CAAC8B,MAAM,CAAC6F,SAAS,CAACC,GAAG,CAAC,oBAAoB,CAAC;YAC9C;UACF,CAAE;UACFC,YAAY,EAAG7H,CAAC,IAAK;YACnB;YACA,IAAIA,CAAC,CAAC8B,MAAM,EAAE;cACZ9B,CAAC,CAAC8B,MAAM,CAAC6F,SAAS,CAACG,MAAM,CAAC,oBAAoB,CAAC;YACjD;UACF,CAAE;UAAA5B,QAAA,eAEF3H,OAAA;YAAM2G,KAAK,EAAE;cAAEiB,KAAK,EAAE,OAAO;cAAEC,QAAQ,EAAE9G,QAAQ,GAAG,MAAM,GAAG,MAAM;cAAEkI,UAAU,EAAE,MAAM;cAAEC,UAAU,EAAE;YAA4B,CAAE;YAAAvB,QAAA,EAAC;UAAC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL,CAACtH,WAAW,iBACXX,OAAA,CAAAE,SAAA;MAAAyH,QAAA,gBACE3H,OAAA;QACE2G,KAAK,EAAE;UACL6C,IAAI,EAAE,CAAC;UACPX,OAAO,EAAE,kBAAkB;UAC3BY,SAAS,EAAE,MAAM;UACjBtC,OAAO,EAAE,MAAM;UACfqB,aAAa,EAAE,QAAQ;UACvBO,GAAG,EAAE,MAAM;UACXW,cAAc,EAAE,QAAQ;UACxBC,cAAc,EAAE,MAAM;UACtBC,cAAc,EAAE;QAClB,CAAE;QACFlB,OAAO,EAAGjH,CAAC,IAAK;UACd,MAAMoI,OAAO,GAAGpI,CAAC,CAACqI,aAAa;UAC/B,MAAM;YAAEC,SAAS;YAAEC,YAAY;YAAEC;UAAa,CAAC,GAAGJ,OAAO;;UAEzD;UACA,IAAIE,SAAS,GAAG,CAAC,IAAIA,SAAS,GAAGE,YAAY,GAAGD,YAAY,EAAE;YAC5D;YACA;UACF;;UAEA;UACA,IAAKD,SAAS,KAAK,CAAC,IAAItI,CAAC,CAACyI,MAAM,GAAG,CAAC,IAC/BH,SAAS,GAAGE,YAAY,IAAID,YAAY,IAAIvI,CAAC,CAACyI,MAAM,GAAG,CAAE,EAAE;YAC9DzI,CAAC,CAACgF,cAAc,CAAC,CAAC;YAClBhF,CAAC,CAACkH,eAAe,CAAC,CAAC;UACrB;QACF,CAAE;QACFwB,SAAS,EAAC,kBAAkB;QAAAxC,QAAA,GAE3BvF,QAAQ,CAACgI,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACvBtK,OAAA;UAAiB2G,KAAK,EAAE;YAAEQ,OAAO,EAAE,MAAM;YAAEE,cAAc,EAAEgD,GAAG,CAACtI,IAAI,KAAK,MAAM,GAAG,UAAU,GAAG;UAAa,CAAE;UAAA4F,QAAA,eAC3G3H,OAAA;YAAK2G,KAAK,EAAE;cAAE4D,QAAQ,EAAE,KAAK;cAAE1B,OAAO,EAAE,WAAW;cAAE3B,YAAY,EAAEmD,GAAG,CAACtI,IAAI,KAAK,MAAM,GAAG,oBAAoB,GAAG,oBAAoB;cAAEkF,UAAU,EAAEoD,GAAG,CAACtI,IAAI,KAAK,MAAM,GAAG,mDAAmD,GAAG,SAAS;cAAE6F,KAAK,EAAEyC,GAAG,CAACtI,IAAI,KAAK,MAAM,GAAG,OAAO,GAAG,SAAS;cAAE8F,QAAQ,EAAE,MAAM;cAAE2C,UAAU,EAAE,KAAK;cAAEjD,SAAS,EAAE8C,GAAG,CAACtI,IAAI,KAAK,MAAM,GAAG,qCAAqC,GAAG,8BAA8B;cAAE0I,QAAQ,EAAE;YAAa,CAAE;YAAA9C,QAAA,EAC/b,OAAO0C,GAAG,CAACrI,OAAO,KAAK,QAAQ,GAC9BqI,GAAG,CAACtI,IAAI,KAAK,WAAW,gBACtB/B,OAAA,CAACF,eAAe;cAACuE,IAAI,EAAEgG,GAAG,CAACrI;YAAQ;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAEtCjI,OAAA;cAAK2G,KAAK,EAAE;gBAAE+D,UAAU,EAAE;cAAW,CAAE;cAAA/C,QAAA,EAAE0C,GAAG,CAACrI;YAAO;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAC3D,GACC0C,KAAK,CAACC,OAAO,CAACP,GAAG,CAACrI,OAAO,CAAC,GAC5BqI,GAAG,CAACrI,OAAO,CAACoI,GAAG,CAAC,CAACS,IAAI,EAAEC,GAAG,kBACxB9K,OAAA;cAAA2H,QAAA,GACGkD,IAAI,CAACpH,IAAI,KAAK,MAAM,iBACnBzD,OAAA;gBAAK2G,KAAK,EAAE;kBAAEoE,YAAY,EAAEF,IAAI,CAACxG,IAAI,GAAG,KAAK,GAAG;gBAAI,CAAE;gBAAAsD,QAAA,EACnD0C,GAAG,CAACtI,IAAI,KAAK,WAAW,gBACvB/B,OAAA,CAACF,eAAe;kBAACuE,IAAI,EAAEwG,IAAI,CAACxG;gBAAK;kBAAAyD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAEpCjI,OAAA;kBAAK2G,KAAK,EAAE;oBAAE+D,UAAU,EAAE;kBAAW,CAAE;kBAAA/C,QAAA,EAAEkD,IAAI,CAACxG;gBAAI;kBAAAyD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cACzD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACN,EACA4C,IAAI,CAACpH,IAAI,KAAK,WAAW,iBAAIzD,OAAA;gBAAKgL,GAAG,EAAEH,IAAI,CAAC/E,SAAS,CAACF,GAAI;gBAACqF,GAAG,EAAC,aAAa;gBAACtE,KAAK,EAAE;kBAAE4D,QAAQ,EAAE,MAAM;kBAAEvD,MAAM,EAAE,MAAM;kBAAEE,YAAY,EAAE,MAAM;kBAAEgE,SAAS,EAAE,OAAO;kBAAEC,SAAS,EAAE,OAAO;kBAAE5C,MAAM,EAAE,oCAAoC;kBAAE6C,SAAS,EAAE;gBAAM;cAAE;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA,GAVnP6C,GAAG;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAWR,CACN,CAAC,gBAEFjI,OAAA;cAAA2H,QAAA,EAAK;YAAsB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UACjC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC,GA1BEqC,KAAK;UAAAxC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2BV,CACN,CAAC,EAED3F,QAAQ,iBACPtC,OAAA;UAAK2G,KAAK,EAAE;YAAEQ,OAAO,EAAE,MAAM;YAAEE,cAAc,EAAE;UAAa,CAAE;UAAAM,QAAA,eAC5D3H,OAAA;YAAK2G,KAAK,EAAE;cAAEkC,OAAO,EAAE,WAAW;cAAE3B,YAAY,EAAE,oBAAoB;cAAED,UAAU,EAAE,SAAS;cAAEM,SAAS,EAAE;YAA+B,CAAE;YAAAI,QAAA,eACzI3H,OAAA;cAAK2G,KAAK,EAAE;gBAAEQ,OAAO,EAAE,MAAM;gBAAE4B,GAAG,EAAE,KAAK;gBAAE3B,UAAU,EAAE;cAAS,CAAE;cAAAO,QAAA,gBAChE3H,OAAA;gBAAK2G,KAAK,EAAE;kBAAEI,KAAK,EAAE,KAAK;kBAAEC,MAAM,EAAE,KAAK;kBAAEE,YAAY,EAAE,KAAK;kBAAED,UAAU,EAAE,SAAS;kBAAES,SAAS,EAAE;gBAAmC;cAAE;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1IjI,OAAA;gBAAK2G,KAAK,EAAE;kBAAEI,KAAK,EAAE,KAAK;kBAAEC,MAAM,EAAE,KAAK;kBAAEE,YAAY,EAAE,KAAK;kBAAED,UAAU,EAAE,SAAS;kBAAES,SAAS,EAAE;gBAAyC;cAAE;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChJjI,OAAA;gBAAK2G,KAAK,EAAE;kBAAEI,KAAK,EAAE,KAAK;kBAAEC,MAAM,EAAE,KAAK;kBAAEE,YAAY,EAAE,KAAK;kBAAED,UAAU,EAAE,SAAS;kBAAES,SAAS,EAAE;gBAAyC;cAAE;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7I;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAEDjI,OAAA;UAAKqL,GAAG,EAAEvI;QAAe;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eAENjI,OAAA;QAAK2G,KAAK,EAAE;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,MAAM,EAAE,CAAC;UAAEI,UAAU,EAAE,2BAA2B;UAAEqB,cAAc,EAAE,YAAY;UAAEgD,SAAS,EAAE,+BAA+B;UAAEzC,OAAO,EAAE9H,QAAQ,GAAG,gBAAgB,GAAG,gBAAgB;UAAEyG,MAAM,EAAE;QAAG,CAAE;QAAAG,QAAA,GACnOjF,YAAY,iBACX1C,OAAA;UAAK2G,KAAK,EAAE;YAAEoE,YAAY,EAAE,MAAM;YAAElC,OAAO,EAAE,MAAM;YAAE5B,UAAU,EAAE,SAAS;YAAEC,YAAY,EAAE,MAAM;YAAEqB,MAAM,EAAE;UAAoB,CAAE;UAAAZ,QAAA,eAC9H3H,OAAA;YAAK2G,KAAK,EAAE;cAAEQ,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAE2B,GAAG,EAAE;YAAO,CAAE;YAAApB,QAAA,gBACjE3H,OAAA;cAAK2G,KAAK,EAAE;gBAAEC,QAAQ,EAAE;cAAW,CAAE;cAAAe,QAAA,gBACnC3H,OAAA;gBAAKgL,GAAG,EAAEtI,YAAa;gBAACuI,GAAG,EAAC,SAAS;gBAACtE,KAAK,EAAE;kBAAEI,KAAK,EAAE,MAAM;kBAAEC,MAAM,EAAE,MAAM;kBAAEmE,SAAS,EAAE,OAAO;kBAAEjE,YAAY,EAAE,KAAK;kBAAEqB,MAAM,EAAE;gBAAoB;cAAE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxJjI,OAAA;gBAAQ0G,OAAO,EAAExE,WAAY;gBAACyE,KAAK,EAAE;kBAAEC,QAAQ,EAAE,UAAU;kBAAEuB,GAAG,EAAE,MAAM;kBAAErB,KAAK,EAAE,MAAM;kBAAEC,KAAK,EAAE,MAAM;kBAAEC,MAAM,EAAE,MAAM;kBAAEC,UAAU,EAAE,SAAS;kBAAEW,KAAK,EAAE,OAAO;kBAAEV,YAAY,EAAE,KAAK;kBAAEqB,MAAM,EAAE,MAAM;kBAAEjB,MAAM,EAAE,SAAS;kBAAEH,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEC,cAAc,EAAE,QAAQ;kBAAEQ,QAAQ,EAAE,MAAM;kBAAEoB,UAAU,EAAE;gBAAO,CAAE;gBAAAtB,QAAA,EAAC;cAAC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvU,CAAC,eACNjI,OAAA;cAAK2G,KAAK,EAAE;gBAAE6C,IAAI,EAAE;cAAE,CAAE;cAAA7B,QAAA,gBACtB3H,OAAA;gBAAG2G,KAAK,EAAE;kBAAEkB,QAAQ,EAAE,MAAM;kBAAEoB,UAAU,EAAE,KAAK;kBAAErB,KAAK,EAAE,SAAS;kBAAEoB,MAAM,EAAE;gBAAE,CAAE;gBAAArB,QAAA,EAAC;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAClGjI,OAAA;gBAAG2G,KAAK,EAAE;kBAAEkB,QAAQ,EAAE,MAAM;kBAAED,KAAK,EAAE,SAAS;kBAAEoB,MAAM,EAAE;gBAAE,CAAE;gBAAArB,QAAA,EAAEnF,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE+I;cAAI;gBAAAzD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAEDjI,OAAA;UAAK2G,KAAK,EAAE;YAAEQ,OAAO,EAAE,MAAM;YAAE4B,GAAG,EAAEhI,QAAQ,GAAG,KAAK,GAAG,KAAK;YAAEkG,UAAU,EAAE,SAAS;YAAEC,YAAY,EAAEnG,QAAQ,GAAG,MAAM,GAAG,MAAM;YAAE8H,OAAO,EAAE9H,QAAQ,GAAG,KAAK,GAAG,KAAK;YAAEwH,MAAM,EAAE;UAAoB,CAAE;UAAAZ,QAAA,gBAC9L3H,OAAA;YACE0G,OAAO,EAAEA,CAAA;cAAA,IAAA8E,qBAAA;cAAA,QAAAA,qBAAA,GAAMzI,YAAY,CAACE,OAAO,cAAAuI,qBAAA,uBAApBA,qBAAA,CAAsBC,KAAK,CAAC,CAAC;YAAA,CAAC;YAC7C9E,KAAK,EAAE;cACLM,UAAU,EAAE,SAAS;cACrBsB,MAAM,EAAE,MAAM;cACdrB,YAAY,EAAEnG,QAAQ,GAAG,KAAK,GAAG,MAAM;cACvCgG,KAAK,EAAEhG,QAAQ,GAAG,MAAM,GAAG,MAAM;cACjCiG,MAAM,EAAEjG,QAAQ,GAAG,MAAM,GAAG,MAAM;cAClCoG,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBC,MAAM,EAAE,SAAS;cACjBG,UAAU,EAAE,eAAe;cAC3BF,SAAS,EAAE;YACb,CAAE;YACFmE,KAAK,EAAC,cAAc;YAAA/D,QAAA,eAEpB3H,OAAA;cAAM2G,KAAK,EAAE;gBAAEiB,KAAK,EAAE,OAAO;gBAAEC,QAAQ,EAAE9G,QAAQ,GAAG,MAAM,GAAG,MAAM;gBAAEkI,UAAU,EAAE;cAAO,CAAE;cAAAtB,QAAA,EAAC;YAAC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7F,CAAC,eAETjI,OAAA;YACEqL,GAAG,EAAErI,QAAS;YACdgB,KAAK,EAAE3B,KAAM;YACbsJ,QAAQ,EAAGlK,CAAC,IAAKQ,QAAQ,CAACR,CAAC,CAAC8B,MAAM,CAACS,KAAK,CAAE;YAC1C4H,SAAS,EAAErF,aAAc;YACzBsF,WAAW,EAAErL,WAAW,GAAG,mBAAmB,GAAG,oBAAqB;YACtEsL,IAAI,EAAE,CAAE;YACRnF,KAAK,EAAE;cACL6C,IAAI,EAAE,CAAC;cACPjB,MAAM,EAAE,MAAM;cACdtB,UAAU,EAAE,aAAa;cACzB8E,OAAO,EAAE,MAAM;cACflE,QAAQ,EAAE9G,QAAQ,GAAG,MAAM,GAAG,MAAM;cACpC6G,KAAK,EAAE,SAAS;cAChBiB,OAAO,EAAE9H,QAAQ,GAAG,WAAW,GAAG,WAAW;cAC7CiL,UAAU,EAAE;YACd;UAAE;YAAAlE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEFjI,OAAA;YACE0G,OAAO,EAAEvB,WAAY;YACrB8G,QAAQ,EAAE,CAAC5J,KAAK,CAACiC,IAAI,CAAC,CAAC,IAAI,CAAC9B,aAAc;YAC1CmE,KAAK,EAAE;cACLM,UAAU,EAAG5E,KAAK,CAACiC,IAAI,CAAC,CAAC,IAAI9B,aAAa,GAAI,SAAS,GAAG,SAAS;cACnE+F,MAAM,EAAE,MAAM;cACdrB,YAAY,EAAEnG,QAAQ,GAAG,KAAK,GAAG,MAAM;cACvCgG,KAAK,EAAEhG,QAAQ,GAAG,MAAM,GAAG,MAAM;cACjCiG,MAAM,EAAEjG,QAAQ,GAAG,MAAM,GAAG,MAAM;cAClCoG,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBC,MAAM,EAAGjF,KAAK,CAACiC,IAAI,CAAC,CAAC,IAAI9B,aAAa,GAAI,SAAS,GAAG,aAAa;cACnEiF,UAAU,EAAE,eAAe;cAC3BF,SAAS,EAAGlF,KAAK,CAACiC,IAAI,CAAC,CAAC,IAAI9B,aAAa,GAAI,mCAAmC,GAAG;YACrF,CAAE;YAAAmF,QAAA,eAEF3H,OAAA;cAAM2G,KAAK,EAAE;gBAAEiB,KAAK,EAAGvF,KAAK,CAACiC,IAAI,CAAC,CAAC,IAAI9B,aAAa,GAAI,OAAO,GAAG,SAAS;gBAAEqF,QAAQ,EAAE9G,QAAQ,GAAG,MAAM,GAAG,MAAM;gBAAEkI,UAAU,EAAE;cAAO,CAAE;cAAAtB,QAAA,EAAC;YAAC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3I,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENjI,OAAA;UAAOqL,GAAG,EAAEtI,YAAa;UAACU,IAAI,EAAC,MAAM;UAACyI,MAAM,EAAC,SAAS;UAACP,QAAQ,EAAEvI,iBAAkB;UAACuD,KAAK,EAAE;YAAEQ,OAAO,EAAE;UAAO;QAAE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAElHjI,OAAA;UAAG2G,KAAK,EAAE;YAAEkB,QAAQ,EAAE9G,QAAQ,GAAG,KAAK,GAAG,MAAM;YAAE6G,KAAK,EAAE,SAAS;YAAEuE,SAAS,EAAE,QAAQ;YAAEnD,MAAM,EAAEjI,QAAQ,GAAG,WAAW,GAAG;UAAY,CAAE;UAAA4G,QAAA,EACpI5G,QAAQ,GAAG,+BAA+B,GAAG;QAAkD;UAAA+G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/F,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA,eACN,CACH,eAEDjI,OAAA;MAAA2H,QAAA,EAAS;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC7H,EAAA,CA1nBID,mBAAmB;EAAA,QACNN,WAAW,EACDD,WAAW;AAAA;AAAAwM,EAAA,GAFlCjM,mBAAmB;AA4nBzB,eAAeA,mBAAmB;AAAC,IAAAiM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}