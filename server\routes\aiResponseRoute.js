const router = require("express").Router();
const authMiddleware = require("../middlewares/authMiddleware");
const axios = require("axios");

// Auto AI Response Service
class AutoAIResponseService {
  constructor() {
    this.openaiApiKey = process.env.OPENAI_API_KEY;
    this.baseURL = "https://api.openai.com/v1/chat/completions";
  }

  // Generate context-aware AI response
  async generateResponse(content, context, userLevel, language = 'english') {
    try {
      const systemPrompt = this.getSystemPrompt(context, userLevel, language);
      const userPrompt = this.formatUserPrompt(content, context);

      const response = await axios.post(
        this.baseURL,
        {
          model: "gpt-3.5-turbo",
          messages: [
            { role: "system", content: systemPrompt },
            { role: "user", content: userPrompt }
          ],
          max_tokens: 500,
          temperature: 0.7,
          presence_penalty: 0.1,
          frequency_penalty: 0.1
        },
        {
          headers: {
            "Authorization": `Bearer ${this.openaiApiKey}`,
            "Content-Type": "application/json"
          },
          timeout: 30000
        }
      );

      return {
        success: true,
        response: response.data.choices[0].message.content.trim(),
        usage: response.data.usage
      };
    } catch (error) {
      console.error("AI Response Error:", error.message);
      return {
        success: false,
        error: error.message,
        response: this.getFallbackResponse(language)
      };
    }
  }

  // Get system prompt based on context and user level
  getSystemPrompt(context, userLevel, language) {
    const isKiswahili = language === 'kiswahili';
    
    let basePrompt = isKiswahili 
      ? "Wewe ni Brainwave AI, msaidizi wa masomo wa Tanzania. Jibu kwa lugha ya Kiswahili tu. Tumia lugha rahisi na ya kielimu."
      : "You are Brainwave AI, an educational assistant for Tanzanian students. Provide helpful, educational responses.";

    // Add context-specific instructions
    switch (context.type) {
      case 'forum':
        basePrompt += isKiswahili
          ? " Msaada huu ni kwa swali la mjadala. Toa jibu la kielimu na la msaada."
          : " This is a forum question. Provide educational and helpful answers.";
        break;
      case 'video_comment':
        basePrompt += isKiswahili
          ? " Hii ni maoni ya video ya masomo. Toa maoni ya kielimu na ya kuongeza maarifa."
          : " This is a video lesson comment. Provide educational insights and additional learning value.";
        break;
      case 'past_paper':
        basePrompt += isKiswahili
          ? " Huu ni mjadala kuhusu karatasi ya mtihani wa zamani. Msaada wa moja kwa moja kuhusu maswali na majibu."
          : " This is a past paper discussion. Provide direct help with questions and answers.";
        break;
    }

    // Add level-specific instructions
    if (userLevel) {
      const levelInstructions = {
        'primary': isKiswahili ? 'Tumia lugha rahisi kwa wanafunzi wa msingi.' : 'Use simple language for primary students.',
        'primary_kiswahili': 'Tumia lugha ya Kiswahili rahisi kwa wanafunzi wa msingi.',
        'secondary': isKiswahili ? 'Tumia lugha ya kiwango cha sekondari.' : 'Use secondary level language.',
        'advance': isKiswahili ? 'Tumia lugha ya kiwango cha juu.' : 'Use advanced level language.'
      };
      
      basePrompt += " " + (levelInstructions[userLevel] || levelInstructions['primary']);
    }

    return basePrompt;
  }

  // Format user prompt with context
  formatUserPrompt(content, context) {
    let prompt = content;

    if (context.subject) {
      prompt = `Subject: ${context.subject}\n${prompt}`;
    }

    if (context.topic) {
      prompt = `Topic: ${context.topic}\n${prompt}`;
    }

    if (context.pastPaperTitle) {
      prompt = `Past Paper: ${context.pastPaperTitle}\n${prompt}`;
    }

    return prompt;
  }

  // Get fallback response when AI fails
  getFallbackResponse(language) {
    return language === 'kiswahili'
      ? "Samahani, sina uwezo wa kujibu swali hili kwa sasa. Jaribu kuuliza tena au wasiliana na mwalimu."
      : "Sorry, I'm unable to answer this question right now. Please try asking again or contact your teacher.";
  }
}

const aiService = new AutoAIResponseService();

// Auto-respond to forum questions
router.post("/forum-response", authMiddleware, async (req, res) => {
  try {
    const { questionContent, subject, topic, userLevel, language } = req.body;

    if (!questionContent) {
      return res.status(400).send({
        success: false,
        message: "Question content is required"
      });
    }

    const context = {
      type: 'forum',
      subject,
      topic
    };

    const aiResponse = await aiService.generateResponse(
      questionContent,
      context,
      userLevel,
      language
    );

    res.send({
      success: true,
      data: {
        response: aiResponse.response,
        isAI: true,
        respondedAt: new Date(),
        context: 'forum'
      }
    });

  } catch (error) {
    console.error("Forum AI Response Error:", error);
    res.status(500).send({
      success: false,
      message: "Error generating AI response",
      error: error.message
    });
  }
});

// Auto-respond to video comments
router.post("/video-comment-response", authMiddleware, async (req, res) => {
  try {
    const { commentContent, videoTitle, subject, userLevel, language } = req.body;

    if (!commentContent) {
      return res.status(400).send({
        success: false,
        message: "Comment content is required"
      });
    }

    const context = {
      type: 'video_comment',
      subject,
      topic: videoTitle
    };

    const aiResponse = await aiService.generateResponse(
      commentContent,
      context,
      userLevel,
      language
    );

    res.send({
      success: true,
      data: {
        response: aiResponse.response,
        isAI: true,
        respondedAt: new Date(),
        context: 'video_comment'
      }
    });

  } catch (error) {
    console.error("Video Comment AI Response Error:", error);
    res.status(500).send({
      success: false,
      message: "Error generating AI response",
      error: error.message
    });
  }
});

// Past paper discussion
router.post("/past-paper-discussion", authMiddleware, async (req, res) => {
  try {
    const { question, pastPaperTitle, subject, class: userClass, userLevel, language } = req.body;

    if (!question) {
      return res.status(400).send({
        success: false,
        message: "Question is required"
      });
    }

    const context = {
      type: 'past_paper',
      subject,
      pastPaperTitle,
      class: userClass
    };

    const aiResponse = await aiService.generateResponse(
      question,
      context,
      userLevel,
      language
    );

    res.send({
      success: true,
      data: {
        response: aiResponse.response,
        isAI: true,
        respondedAt: new Date(),
        context: 'past_paper',
        pastPaperTitle
      }
    });

  } catch (error) {
    console.error("Past Paper AI Response Error:", error);
    res.status(500).send({
      success: false,
      message: "Error generating AI response",
      error: error.message
    });
  }
});

// Get AI conversation history for a specific context
router.get("/conversation/:contextType/:contextId", authMiddleware, async (req, res) => {
  try {
    const { contextType, contextId } = req.params;
    const userId = req.body.userId;

    // This would typically fetch from a database
    // For now, return empty array as this is a new feature
    res.send({
      success: true,
      data: {
        conversations: [],
        contextType,
        contextId
      }
    });

  } catch (error) {
    console.error("Get Conversation Error:", error);
    res.status(500).send({
      success: false,
      message: "Error fetching conversation",
      error: error.message
    });
  }
});

// Health check for AI service
router.get("/health", async (req, res) => {
  try {
    const isHealthy = !!process.env.OPENAI_API_KEY;
    
    res.send({
      success: true,
      data: {
        aiServiceHealthy: isHealthy,
        timestamp: new Date()
      }
    });
  } catch (error) {
    res.status(500).send({
      success: false,
      message: "AI service health check failed"
    });
  }
});

module.exports = router;
