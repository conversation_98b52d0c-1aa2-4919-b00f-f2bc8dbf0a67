const mongoose = require("mongoose");

const skillSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: true,
      trim: true,
      maxlength: 200
    },
    description: {
      type: String,
      trim: true,
      maxlength: 1000
    },
    level: {
      type: String,
      required: true,
      enum: ["beginner", "amateur", "professional", "expert"],
      default: "beginner"
    },
    category: {
      type: String,
      required: true,
      trim: true,
      default: "General"
    },
    subject: {
      type: String,
      trim: true,
      default: "General Skills"
    },
    // Video content
    videoUrl: {
      type: String,
      trim: true
    },
    videoID: {
      type: String,
      trim: true
    },
    signedVideoUrl: {
      type: String,
      trim: true
    },
    thumbnailUrl: {
      type: String,
      trim: true
    },
    duration: {
      type: String,
      trim: true
    },
    // Educational metadata
    targetAudience: {
      type: String,
      enum: ["primary", "primary_kiswahili", "secondary", "advance", "all"],
      default: "all"
    },
    prerequisites: [{
      type: String,
      trim: true
    }],
    learningOutcomes: [{
      type: String,
      trim: true
    }],
    tags: [{
      type: String,
      trim: true
    }],
    // Content organization
    isActive: {
      type: Boolean,
      default: true
    },
    isFeatured: {
      type: Boolean,
      default: false
    },
    difficulty: {
      type: Number,
      min: 1,
      max: 5,
      default: 1
    },
    estimatedTime: {
      type: String,
      trim: true // e.g., "30 minutes", "1 hour"
    },
    // Analytics
    viewCount: {
      type: Number,
      default: 0
    },
    completionCount: {
      type: Number,
      default: 0
    },
    averageRating: {
      type: Number,
      min: 0,
      max: 5,
      default: 0
    },
    // Admin metadata
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "users",
      required: true
    },
    lastModifiedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "users"
    },
    // File information (if uploaded)
    fileSize: {
      type: Number // in bytes
    },
    fileFormat: {
      type: String,
      trim: true
    },
    // Subtitles support
    subtitles: [{
      language: {
        type: String,
        required: true
      },
      languageName: {
        type: String,
        required: true
      },
      url: {
        type: String,
        required: true
      },
      isDefault: {
        type: Boolean,
        default: false
      }
    }],
    // Access control
    isPublic: {
      type: Boolean,
      default: true
    },
    requiredSubscription: {
      type: Boolean,
      default: false
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Indexes for better performance
skillSchema.index({ title: "text", description: "text", tags: "text" });
skillSchema.index({ level: 1, category: 1 });
skillSchema.index({ targetAudience: 1, isActive: 1 });
skillSchema.index({ createdAt: -1 });
skillSchema.index({ isFeatured: -1, createdAt: -1 });

// Virtual for formatted level display
skillSchema.virtual('levelDisplay').get(function() {
  const levelMap = {
    'beginner': 'Beginner',
    'amateur': 'Amateur', 
    'professional': 'Professional',
    'expert': 'Expert'
  };
  return levelMap[this.level] || this.level;
});

// Virtual for skill difficulty badge
skillSchema.virtual('difficultyBadge').get(function() {
  const badges = {
    1: '⭐',
    2: '⭐⭐', 
    3: '⭐⭐⭐',
    4: '⭐⭐⭐⭐',
    5: '⭐⭐⭐⭐⭐'
  };
  return badges[this.difficulty] || '⭐';
});

// Pre-save middleware
skillSchema.pre('save', function(next) {
  // Auto-generate tags from title and description
  if (this.isModified('title') || this.isModified('description')) {
    const words = (this.title + ' ' + (this.description || '')).toLowerCase()
      .split(/\s+/)
      .filter(word => word.length > 3)
      .slice(0, 10);
    
    this.tags = [...new Set([...this.tags, ...words])];
  }
  
  next();
});

// Static methods
skillSchema.statics.getSkillsByLevel = function(level) {
  return this.find({ level, isActive: true }).sort({ createdAt: -1 });
};

skillSchema.statics.getFeaturedSkills = function(limit = 10) {
  return this.find({ isFeatured: true, isActive: true })
    .sort({ createdAt: -1 })
    .limit(limit);
};

skillSchema.statics.searchSkills = function(query, filters = {}) {
  const searchQuery = {
    isActive: true,
    ...filters
  };
  
  if (query) {
    searchQuery.$text = { $search: query };
  }
  
  return this.find(searchQuery).sort({ score: { $meta: "textScore" }, createdAt: -1 });
};

// Instance methods
skillSchema.methods.incrementView = function() {
  this.viewCount += 1;
  return this.save();
};

skillSchema.methods.incrementCompletion = function() {
  this.completionCount += 1;
  return this.save();
};

const Skill = mongoose.model("skills", skillSchema);

module.exports = Skill;
