{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\n/**\n * This function will try to call requestIdleCallback if available to save performance.\n * No need `getLabel` here since already fetch on `rawLabeledValue`.\n */\nexport default (function (values) {\n  var cacheRef = React.useRef({\n    valueLabels: new Map()\n  });\n  return React.useMemo(function () {\n    var valueLabels = cacheRef.current.valueLabels;\n    var valueLabelsCache = new Map();\n    var filledValues = values.map(function (item) {\n      var _item$label;\n      var value = item.value;\n      var mergedLabel = (_item$label = item.label) !== null && _item$label !== void 0 ? _item$label : valueLabels.get(value);\n\n      // Save in cache\n      valueLabelsCache.set(value, mergedLabel);\n      return _objectSpread(_objectSpread({}, item), {}, {\n        label: mergedLabel\n      });\n    });\n    cacheRef.current.valueLabels = valueLabelsCache;\n    return [filledValues];\n  }, [values]);\n});", "map": {"version": 3, "names": ["_objectSpread", "React", "values", "cacheRef", "useRef", "valueLabels", "Map", "useMemo", "current", "valueLabelsCache", "filled<PERSON><PERSON><PERSON>", "map", "item", "_item$label", "value", "mergedLabel", "label", "get", "set"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-tree-select/es/hooks/useCache.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\n/**\n * This function will try to call requestIdleCallback if available to save performance.\n * No need `getLabel` here since already fetch on `rawLabeledValue`.\n */\nexport default (function (values) {\n  var cacheRef = React.useRef({\n    valueLabels: new Map()\n  });\n  return React.useMemo(function () {\n    var valueLabels = cacheRef.current.valueLabels;\n    var valueLabelsCache = new Map();\n    var filledValues = values.map(function (item) {\n      var _item$label;\n      var value = item.value;\n      var mergedLabel = (_item$label = item.label) !== null && _item$label !== void 0 ? _item$label : valueLabels.get(value);\n\n      // Save in cache\n      valueLabelsCache.set(value, mergedLabel);\n      return _objectSpread(_objectSpread({}, item), {}, {\n        label: mergedLabel\n      });\n    });\n    cacheRef.current.valueLabels = valueLabelsCache;\n    return [filledValues];\n  }, [values]);\n});"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B;AACA;AACA;AACA;AACA,gBAAgB,UAAUC,MAAM,EAAE;EAChC,IAAIC,QAAQ,GAAGF,KAAK,CAACG,MAAM,CAAC;IAC1BC,WAAW,EAAE,IAAIC,GAAG,CAAC;EACvB,CAAC,CAAC;EACF,OAAOL,KAAK,CAACM,OAAO,CAAC,YAAY;IAC/B,IAAIF,WAAW,GAAGF,QAAQ,CAACK,OAAO,CAACH,WAAW;IAC9C,IAAII,gBAAgB,GAAG,IAAIH,GAAG,CAAC,CAAC;IAChC,IAAII,YAAY,GAAGR,MAAM,CAACS,GAAG,CAAC,UAAUC,IAAI,EAAE;MAC5C,IAAIC,WAAW;MACf,IAAIC,KAAK,GAAGF,IAAI,CAACE,KAAK;MACtB,IAAIC,WAAW,GAAG,CAACF,WAAW,GAAGD,IAAI,CAACI,KAAK,MAAM,IAAI,IAAIH,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAGR,WAAW,CAACY,GAAG,CAACH,KAAK,CAAC;;MAEtH;MACAL,gBAAgB,CAACS,GAAG,CAACJ,KAAK,EAAEC,WAAW,CAAC;MACxC,OAAOf,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEY,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QAChDI,KAAK,EAAED;MACT,CAAC,CAAC;IACJ,CAAC,CAAC;IACFZ,QAAQ,CAACK,OAAO,CAACH,WAAW,GAAGI,gBAAgB;IAC/C,OAAO,CAACC,YAAY,CAAC;EACvB,CAAC,EAAE,CAACR,MAAM,CAAC,CAAC;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}