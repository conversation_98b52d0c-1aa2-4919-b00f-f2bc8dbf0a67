{"ast": null, "code": "import * as React from 'react';\nimport { convertChildrenToData } from \"../utils/legacyUtil\";\n\n/**\n * Parse `children` to `options` if `options` is not provided.\n * Then flatten the `options`.\n */\nexport default function useOptions(options, children, fieldNames, optionFilterProp, optionLabelProp) {\n  return React.useMemo(function () {\n    var mergedOptions = options;\n    var childrenAsData = !options;\n    if (childrenAsData) {\n      mergedOptions = convertChildrenToData(children);\n    }\n    var valueOptions = new Map();\n    var labelOptions = new Map();\n    var setLabelOptions = function setLabelOptions(labelOptionsMap, option, key) {\n      if (key && typeof key === 'string') {\n        labelOptionsMap.set(option[key], option);\n      }\n    };\n    function dig(optionList) {\n      var isChildren = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      // for loop to speed up collection speed\n      for (var i = 0; i < optionList.length; i += 1) {\n        var option = optionList[i];\n        if (!option[fieldNames.options] || isChildren) {\n          valueOptions.set(option[fieldNames.value], option);\n          setLabelOptions(labelOptions, option, fieldNames.label);\n          // https://github.com/ant-design/ant-design/issues/35304\n          setLabelOptions(labelOptions, option, optionFilterProp);\n          setLabelOptions(labelOptions, option, optionLabelProp);\n        } else {\n          dig(option[fieldNames.options], true);\n        }\n      }\n    }\n    dig(mergedOptions);\n    return {\n      options: mergedOptions,\n      valueOptions: valueOptions,\n      labelOptions: labelOptions\n    };\n  }, [options, children, fieldNames, optionFilterProp, optionLabelProp]);\n}", "map": {"version": 3, "names": ["React", "convertChildrenToData", "useOptions", "options", "children", "fieldNames", "optionFilterProp", "optionLabelProp", "useMemo", "mergedOptions", "childrenAsData", "valueOptions", "Map", "labelOptions", "setLabelOptions", "labelOptionsMap", "option", "key", "set", "dig", "optionList", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arguments", "length", "undefined", "i", "value", "label"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-select/es/hooks/useOptions.js"], "sourcesContent": ["import * as React from 'react';\nimport { convertChildrenToData } from \"../utils/legacyUtil\";\n\n/**\n * Parse `children` to `options` if `options` is not provided.\n * Then flatten the `options`.\n */\nexport default function useOptions(options, children, fieldNames, optionFilterProp, optionLabelProp) {\n  return React.useMemo(function () {\n    var mergedOptions = options;\n    var childrenAsData = !options;\n    if (childrenAsData) {\n      mergedOptions = convertChildrenToData(children);\n    }\n    var valueOptions = new Map();\n    var labelOptions = new Map();\n    var setLabelOptions = function setLabelOptions(labelOptionsMap, option, key) {\n      if (key && typeof key === 'string') {\n        labelOptionsMap.set(option[key], option);\n      }\n    };\n    function dig(optionList) {\n      var isChildren = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      // for loop to speed up collection speed\n      for (var i = 0; i < optionList.length; i += 1) {\n        var option = optionList[i];\n        if (!option[fieldNames.options] || isChildren) {\n          valueOptions.set(option[fieldNames.value], option);\n          setLabelOptions(labelOptions, option, fieldNames.label);\n          // https://github.com/ant-design/ant-design/issues/35304\n          setLabelOptions(labelOptions, option, optionFilterProp);\n          setLabelOptions(labelOptions, option, optionLabelProp);\n        } else {\n          dig(option[fieldNames.options], true);\n        }\n      }\n    }\n    dig(mergedOptions);\n    return {\n      options: mergedOptions,\n      valueOptions: valueOptions,\n      labelOptions: labelOptions\n    };\n  }, [options, children, fieldNames, optionFilterProp, optionLabelProp]);\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,qBAAqB,QAAQ,qBAAqB;;AAE3D;AACA;AACA;AACA;AACA,eAAe,SAASC,UAAUA,CAACC,OAAO,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,eAAe,EAAE;EACnG,OAAOP,KAAK,CAACQ,OAAO,CAAC,YAAY;IAC/B,IAAIC,aAAa,GAAGN,OAAO;IAC3B,IAAIO,cAAc,GAAG,CAACP,OAAO;IAC7B,IAAIO,cAAc,EAAE;MAClBD,aAAa,GAAGR,qBAAqB,CAACG,QAAQ,CAAC;IACjD;IACA,IAAIO,YAAY,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC5B,IAAIC,YAAY,GAAG,IAAID,GAAG,CAAC,CAAC;IAC5B,IAAIE,eAAe,GAAG,SAASA,eAAeA,CAACC,eAAe,EAAEC,MAAM,EAAEC,GAAG,EAAE;MAC3E,IAAIA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;QAClCF,eAAe,CAACG,GAAG,CAACF,MAAM,CAACC,GAAG,CAAC,EAAED,MAAM,CAAC;MAC1C;IACF,CAAC;IACD,SAASG,GAAGA,CAACC,UAAU,EAAE;MACvB,IAAIC,UAAU,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;MAC1F;MACA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,UAAU,CAACG,MAAM,EAAEE,CAAC,IAAI,CAAC,EAAE;QAC7C,IAAIT,MAAM,GAAGI,UAAU,CAACK,CAAC,CAAC;QAC1B,IAAI,CAACT,MAAM,CAACX,UAAU,CAACF,OAAO,CAAC,IAAIkB,UAAU,EAAE;UAC7CV,YAAY,CAACO,GAAG,CAACF,MAAM,CAACX,UAAU,CAACqB,KAAK,CAAC,EAAEV,MAAM,CAAC;UAClDF,eAAe,CAACD,YAAY,EAAEG,MAAM,EAAEX,UAAU,CAACsB,KAAK,CAAC;UACvD;UACAb,eAAe,CAACD,YAAY,EAAEG,MAAM,EAAEV,gBAAgB,CAAC;UACvDQ,eAAe,CAACD,YAAY,EAAEG,MAAM,EAAET,eAAe,CAAC;QACxD,CAAC,MAAM;UACLY,GAAG,CAACH,MAAM,CAACX,UAAU,CAACF,OAAO,CAAC,EAAE,IAAI,CAAC;QACvC;MACF;IACF;IACAgB,GAAG,CAACV,aAAa,CAAC;IAClB,OAAO;MACLN,OAAO,EAAEM,aAAa;MACtBE,YAAY,EAAEA,YAAY;MAC1BE,YAAY,EAAEA;IAChB,CAAC;EACH,CAAC,EAAE,CAACV,OAAO,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,eAAe,CAAC,CAAC;AACxE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}