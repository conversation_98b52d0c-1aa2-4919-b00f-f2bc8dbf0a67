// Final comprehensive test for Primary Kiswahili Medium level
const http = require('http');

// Test registration first
function testRegistration() {
  return new Promise((resolve, reject) => {
    const testData = JSON.stringify({
      firstName: "Fatuma",
      lastName: "<PERSON><PERSON><PERSON><PERSON>", 
      username: "fatuma_kiswahili_final",
      email: "<EMAIL>",
      school: "<PERSON>le ya Msingi Kibada",
      level: "primary_kiswahili",
      class: "4",
      phoneNumber: "0754123458",
      password: "test123"
    });

    const options = {
      hostname: 'localhost',
      port: 5000,
      path: '/api/users/register',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(testData)
      }
    };

    console.log('📝 Step 1: Testing Registration...');
    console.log('User data:', JSON.parse(testData));

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          if (res.statusCode === 200) {
            console.log('✅ Registration successful!');
            resolve(JSON.parse(testData));
          } else if (res.statusCode === 409) {
            console.log('⚠️ User already exists, proceeding with login test...');
            resolve(JSON.parse(testData));
          } else {
            console.log('❌ Registration failed:', response);
            reject(new Error('Registration failed'));
          }
        } catch (error) {
          console.log('Raw response:', data);
          reject(error);
        }
      });
    });

    req.on('error', reject);
    req.write(testData);
    req.end();
  });
}

// Test login
function testLogin(userData) {
  return new Promise((resolve, reject) => {
    const loginData = JSON.stringify({
      username: userData.username,
      password: userData.password
    });

    const options = {
      hostname: 'localhost',
      port: 5000,
      path: '/api/users/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(loginData)
      }
    };

    console.log('\n🔐 Step 2: Testing Login...');

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          if (res.statusCode === 200 && response.success) {
            console.log('✅ Login successful!');
            console.log('👤 User Info:');
            console.log(`   Name: ${response.data.firstName} ${response.data.lastName}`);
            console.log(`   Username: ${response.data.username}`);
            console.log(`   Level: ${response.data.level}`);
            console.log(`   Class: ${response.data.class}`);
            console.log(`   School: ${response.data.school}`);
            
            // Verify the level
            if (response.data.level === 'primary_kiswahili') {
              console.log('\n🎉 SUCCESS: Level verification passed!');
              console.log('✅ Primary Kiswahili Medium level is correctly stored and retrieved');
              resolve(response.data);
            } else {
              console.log('\n❌ Level verification failed!');
              console.log(`Expected: primary_kiswahili, Got: ${response.data.level}`);
              reject(new Error('Level verification failed'));
            }
          } else {
            console.log('❌ Login failed:', response);
            reject(new Error('Login failed'));
          }
        } catch (error) {
          console.log('Raw response:', data);
          reject(error);
        }
      });
    });

    req.on('error', reject);
    req.write(loginData);
    req.end();
  });
}

// Run comprehensive test
async function runComprehensiveTest() {
  console.log('🚀 Starting Comprehensive Primary Kiswahili Medium Test');
  console.log('='.repeat(60));
  
  try {
    // Step 1: Registration
    const userData = await testRegistration();
    
    // Step 2: Login
    const loginResponse = await testLogin(userData);
    
    // Step 3: Summary
    console.log('\n' + '='.repeat(60));
    console.log('🎉 ALL TESTS PASSED SUCCESSFULLY!');
    console.log('');
    console.log('✅ Primary Kiswahili Medium level implementation is working correctly:');
    console.log('   • Registration accepts primary_kiswahili level');
    console.log('   • Level is correctly stored in database');
    console.log('   • Login retrieves correct level information');
    console.log('   • User can access the system with new level');
    console.log('');
    console.log('🇹🇿 Ready for Kiswahili language education!');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
  }
}

// Run the test
runComprehensiveTest();
