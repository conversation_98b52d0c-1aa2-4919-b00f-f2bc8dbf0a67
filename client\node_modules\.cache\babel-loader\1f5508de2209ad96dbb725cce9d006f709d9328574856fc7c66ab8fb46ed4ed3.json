{"ast": null, "code": "import { textEllipsis } from '../../style';\nconst getVerticalInlineStyle = token => {\n  const {\n    componentCls,\n    itemHeight,\n    itemMarginInline,\n    padding,\n    menuArrowSize,\n    marginXS,\n    itemMarginBlock\n  } = token;\n  const paddingWithArrow = padding + menuArrowSize + marginXS;\n  return {\n    [\"\".concat(componentCls, \"-item\")]: {\n      position: 'relative',\n      overflow: 'hidden'\n    },\n    [\"\".concat(componentCls, \"-item, \").concat(componentCls, \"-submenu-title\")]: {\n      height: itemHeight,\n      lineHeight: \"\".concat(itemHeight, \"px\"),\n      paddingInline: padding,\n      overflow: 'hidden',\n      textOverflow: 'ellipsis',\n      marginInline: itemMarginInline,\n      marginBlock: itemMarginBlock,\n      width: \"calc(100% - \".concat(itemMarginInline * 2, \"px)\")\n    },\n    [\"> \".concat(componentCls, \"-item,\\n            > \").concat(componentCls, \"-submenu > \").concat(componentCls, \"-submenu-title\")]: {\n      height: itemHeight,\n      lineHeight: \"\".concat(itemHeight, \"px\")\n    },\n    [\"\".concat(componentCls, \"-item-group-list \").concat(componentCls, \"-submenu-title,\\n            \").concat(componentCls, \"-submenu-title\")]: {\n      paddingInlineEnd: paddingWithArrow\n    }\n  };\n};\nconst getVerticalStyle = token => {\n  const {\n    componentCls,\n    iconCls,\n    itemHeight,\n    colorTextLightSolid,\n    dropdownWidth,\n    controlHeightLG,\n    motionDurationMid,\n    motionEaseOut,\n    paddingXL,\n    itemMarginInline,\n    fontSizeLG,\n    motionDurationSlow,\n    paddingXS,\n    boxShadowSecondary,\n    collapsedWidth,\n    collapsedIconSize\n  } = token;\n  const inlineItemStyle = {\n    height: itemHeight,\n    lineHeight: \"\".concat(itemHeight, \"px\"),\n    listStylePosition: 'inside',\n    listStyleType: 'disc'\n  };\n  return [{\n    [componentCls]: {\n      [\"&-inline, &-vertical\"]: Object.assign({\n        [\"&\".concat(componentCls, \"-root\")]: {\n          boxShadow: 'none'\n        }\n      }, getVerticalInlineStyle(token))\n    },\n    [\"\".concat(componentCls, \"-submenu-popup\")]: {\n      [\"\".concat(componentCls, \"-vertical\")]: Object.assign(Object.assign({}, getVerticalInlineStyle(token)), {\n        boxShadow: boxShadowSecondary\n      })\n    }\n  },\n  // Vertical only\n  {\n    [\"\".concat(componentCls, \"-submenu-popup \").concat(componentCls, \"-vertical\").concat(componentCls, \"-sub\")]: {\n      minWidth: dropdownWidth,\n      maxHeight: \"calc(100vh - \".concat(controlHeightLG * 2.5, \"px)\"),\n      padding: '0',\n      overflow: 'hidden',\n      borderInlineEnd: 0,\n      // https://github.com/ant-design/ant-design/issues/22244\n      // https://github.com/ant-design/ant-design/issues/26812\n      \"&:not([class*='-active'])\": {\n        overflowX: 'hidden',\n        overflowY: 'auto'\n      }\n    }\n  },\n  // Inline Only\n  {\n    [\"\".concat(componentCls, \"-inline\")]: {\n      width: '100%',\n      // Motion enhance for first level\n      [\"&\".concat(componentCls, \"-root\")]: {\n        [\"\".concat(componentCls, \"-item, \").concat(componentCls, \"-submenu-title\")]: {\n          display: 'flex',\n          alignItems: 'center',\n          transition: [\"border-color \".concat(motionDurationSlow), \"background \".concat(motionDurationSlow), \"padding \".concat(motionDurationMid, \" \").concat(motionEaseOut)].join(','),\n          [\"> \".concat(componentCls, \"-title-content\")]: {\n            flex: 'auto',\n            minWidth: 0,\n            overflow: 'hidden',\n            textOverflow: 'ellipsis'\n          },\n          '> *': {\n            flex: 'none'\n          }\n        }\n      },\n      // >>>>> Sub\n      [\"\".concat(componentCls, \"-sub\").concat(componentCls, \"-inline\")]: {\n        padding: 0,\n        border: 0,\n        borderRadius: 0,\n        boxShadow: 'none',\n        [\"& > \".concat(componentCls, \"-submenu > \").concat(componentCls, \"-submenu-title\")]: inlineItemStyle,\n        [\"& \".concat(componentCls, \"-item-group-title\")]: {\n          paddingInlineStart: paddingXL\n        }\n      },\n      // >>>>> Item\n      [\"\".concat(componentCls, \"-item\")]: inlineItemStyle\n    }\n  },\n  // Inline Collapse Only\n  {\n    [\"\".concat(componentCls, \"-inline-collapsed\")]: {\n      width: collapsedWidth,\n      [\"&\".concat(componentCls, \"-root\")]: {\n        [\"\".concat(componentCls, \"-item, \").concat(componentCls, \"-submenu \").concat(componentCls, \"-submenu-title\")]: {\n          [\"> \".concat(componentCls, \"-inline-collapsed-noicon\")]: {\n            fontSize: fontSizeLG,\n            textAlign: 'center'\n          }\n        }\n      },\n      [\"> \".concat(componentCls, \"-item,\\n          > \").concat(componentCls, \"-item-group > \").concat(componentCls, \"-item-group-list > \").concat(componentCls, \"-item,\\n          > \").concat(componentCls, \"-item-group > \").concat(componentCls, \"-item-group-list > \").concat(componentCls, \"-submenu > \").concat(componentCls, \"-submenu-title,\\n          > \").concat(componentCls, \"-submenu > \").concat(componentCls, \"-submenu-title\")]: {\n        insetInlineStart: 0,\n        paddingInline: \"calc(50% - \".concat(fontSizeLG / 2, \"px - \").concat(itemMarginInline, \"px)\"),\n        textOverflow: 'clip',\n        [\"\\n            \".concat(componentCls, \"-submenu-arrow,\\n            \").concat(componentCls, \"-submenu-expand-icon\\n          \")]: {\n          opacity: 0\n        },\n        [\"\".concat(componentCls, \"-item-icon, \").concat(iconCls)]: {\n          margin: 0,\n          fontSize: collapsedIconSize,\n          lineHeight: \"\".concat(itemHeight, \"px\"),\n          '+ span': {\n            display: 'inline-block',\n            opacity: 0\n          }\n        }\n      },\n      [\"\".concat(componentCls, \"-item-icon, \").concat(iconCls)]: {\n        display: 'inline-block'\n      },\n      '&-tooltip': {\n        pointerEvents: 'none',\n        [\"\".concat(componentCls, \"-item-icon, \").concat(iconCls)]: {\n          display: 'none'\n        },\n        'a, a:hover': {\n          color: colorTextLightSolid\n        }\n      },\n      [\"\".concat(componentCls, \"-item-group-title\")]: Object.assign(Object.assign({}, textEllipsis), {\n        paddingInline: paddingXS\n      })\n    }\n  }];\n};\nexport default getVerticalStyle;", "map": {"version": 3, "names": ["textEllipsis", "getVerticalInlineStyle", "token", "componentCls", "itemHeight", "itemMarginInline", "padding", "menuArrowSize", "marginXS", "itemMarginBlock", "paddingWithArrow", "concat", "position", "overflow", "height", "lineHeight", "paddingInline", "textOverflow", "marginInline", "marginBlock", "width", "paddingInlineEnd", "getVerticalStyle", "iconCls", "colorTextLightSolid", "dropdownWidth", "controlHeightLG", "motionDurationMid", "motionEaseOut", "paddingXL", "fontSizeLG", "motionDurationSlow", "paddingXS", "boxShadowSecondary", "collapsedWidth", "collapsedIconSize", "inlineItemStyle", "listStylePosition", "listStyleType", "Object", "assign", "boxShadow", "min<PERSON><PERSON><PERSON>", "maxHeight", "borderInlineEnd", "overflowX", "overflowY", "display", "alignItems", "transition", "join", "flex", "border", "borderRadius", "paddingInlineStart", "fontSize", "textAlign", "insetInlineStart", "opacity", "margin", "pointerEvents", "color"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/menu/style/vertical.js"], "sourcesContent": ["import { textEllipsis } from '../../style';\nconst getVerticalInlineStyle = token => {\n  const {\n    componentCls,\n    itemHeight,\n    itemMarginInline,\n    padding,\n    menuArrowSize,\n    marginXS,\n    itemMarginBlock\n  } = token;\n  const paddingWithArrow = padding + menuArrowSize + marginXS;\n  return {\n    [`${componentCls}-item`]: {\n      position: 'relative',\n      overflow: 'hidden'\n    },\n    [`${componentCls}-item, ${componentCls}-submenu-title`]: {\n      height: itemHeight,\n      lineHeight: `${itemHeight}px`,\n      paddingInline: padding,\n      overflow: 'hidden',\n      textOverflow: 'ellipsis',\n      marginInline: itemMarginInline,\n      marginBlock: itemMarginBlock,\n      width: `calc(100% - ${itemMarginInline * 2}px)`\n    },\n    [`> ${componentCls}-item,\n            > ${componentCls}-submenu > ${componentCls}-submenu-title`]: {\n      height: itemHeight,\n      lineHeight: `${itemHeight}px`\n    },\n    [`${componentCls}-item-group-list ${componentCls}-submenu-title,\n            ${componentCls}-submenu-title`]: {\n      paddingInlineEnd: paddingWithArrow\n    }\n  };\n};\nconst getVerticalStyle = token => {\n  const {\n    componentCls,\n    iconCls,\n    itemHeight,\n    colorTextLightSolid,\n    dropdownWidth,\n    controlHeightLG,\n    motionDurationMid,\n    motionEaseOut,\n    paddingXL,\n    itemMarginInline,\n    fontSizeLG,\n    motionDurationSlow,\n    paddingXS,\n    boxShadowSecondary,\n    collapsedWidth,\n    collapsedIconSize\n  } = token;\n  const inlineItemStyle = {\n    height: itemHeight,\n    lineHeight: `${itemHeight}px`,\n    listStylePosition: 'inside',\n    listStyleType: 'disc'\n  };\n  return [{\n    [componentCls]: {\n      [`&-inline, &-vertical`]: Object.assign({\n        [`&${componentCls}-root`]: {\n          boxShadow: 'none'\n        }\n      }, getVerticalInlineStyle(token))\n    },\n    [`${componentCls}-submenu-popup`]: {\n      [`${componentCls}-vertical`]: Object.assign(Object.assign({}, getVerticalInlineStyle(token)), {\n        boxShadow: boxShadowSecondary\n      })\n    }\n  },\n  // Vertical only\n  {\n    [`${componentCls}-submenu-popup ${componentCls}-vertical${componentCls}-sub`]: {\n      minWidth: dropdownWidth,\n      maxHeight: `calc(100vh - ${controlHeightLG * 2.5}px)`,\n      padding: '0',\n      overflow: 'hidden',\n      borderInlineEnd: 0,\n      // https://github.com/ant-design/ant-design/issues/22244\n      // https://github.com/ant-design/ant-design/issues/26812\n      \"&:not([class*='-active'])\": {\n        overflowX: 'hidden',\n        overflowY: 'auto'\n      }\n    }\n  },\n  // Inline Only\n  {\n    [`${componentCls}-inline`]: {\n      width: '100%',\n      // Motion enhance for first level\n      [`&${componentCls}-root`]: {\n        [`${componentCls}-item, ${componentCls}-submenu-title`]: {\n          display: 'flex',\n          alignItems: 'center',\n          transition: [`border-color ${motionDurationSlow}`, `background ${motionDurationSlow}`, `padding ${motionDurationMid} ${motionEaseOut}`].join(','),\n          [`> ${componentCls}-title-content`]: {\n            flex: 'auto',\n            minWidth: 0,\n            overflow: 'hidden',\n            textOverflow: 'ellipsis'\n          },\n          '> *': {\n            flex: 'none'\n          }\n        }\n      },\n      // >>>>> Sub\n      [`${componentCls}-sub${componentCls}-inline`]: {\n        padding: 0,\n        border: 0,\n        borderRadius: 0,\n        boxShadow: 'none',\n        [`& > ${componentCls}-submenu > ${componentCls}-submenu-title`]: inlineItemStyle,\n        [`& ${componentCls}-item-group-title`]: {\n          paddingInlineStart: paddingXL\n        }\n      },\n      // >>>>> Item\n      [`${componentCls}-item`]: inlineItemStyle\n    }\n  },\n  // Inline Collapse Only\n  {\n    [`${componentCls}-inline-collapsed`]: {\n      width: collapsedWidth,\n      [`&${componentCls}-root`]: {\n        [`${componentCls}-item, ${componentCls}-submenu ${componentCls}-submenu-title`]: {\n          [`> ${componentCls}-inline-collapsed-noicon`]: {\n            fontSize: fontSizeLG,\n            textAlign: 'center'\n          }\n        }\n      },\n      [`> ${componentCls}-item,\n          > ${componentCls}-item-group > ${componentCls}-item-group-list > ${componentCls}-item,\n          > ${componentCls}-item-group > ${componentCls}-item-group-list > ${componentCls}-submenu > ${componentCls}-submenu-title,\n          > ${componentCls}-submenu > ${componentCls}-submenu-title`]: {\n        insetInlineStart: 0,\n        paddingInline: `calc(50% - ${fontSizeLG / 2}px - ${itemMarginInline}px)`,\n        textOverflow: 'clip',\n        [`\n            ${componentCls}-submenu-arrow,\n            ${componentCls}-submenu-expand-icon\n          `]: {\n          opacity: 0\n        },\n        [`${componentCls}-item-icon, ${iconCls}`]: {\n          margin: 0,\n          fontSize: collapsedIconSize,\n          lineHeight: `${itemHeight}px`,\n          '+ span': {\n            display: 'inline-block',\n            opacity: 0\n          }\n        }\n      },\n      [`${componentCls}-item-icon, ${iconCls}`]: {\n        display: 'inline-block'\n      },\n      '&-tooltip': {\n        pointerEvents: 'none',\n        [`${componentCls}-item-icon, ${iconCls}`]: {\n          display: 'none'\n        },\n        'a, a:hover': {\n          color: colorTextLightSolid\n        }\n      },\n      [`${componentCls}-item-group-title`]: Object.assign(Object.assign({}, textEllipsis), {\n        paddingInline: paddingXS\n      })\n    }\n  }];\n};\nexport default getVerticalStyle;"], "mappings": "AAAA,SAASA,YAAY,QAAQ,aAAa;AAC1C,MAAMC,sBAAsB,GAAGC,KAAK,IAAI;EACtC,MAAM;IACJC,YAAY;IACZC,UAAU;IACVC,gBAAgB;IAChBC,OAAO;IACPC,aAAa;IACbC,QAAQ;IACRC;EACF,CAAC,GAAGP,KAAK;EACT,MAAMQ,gBAAgB,GAAGJ,OAAO,GAAGC,aAAa,GAAGC,QAAQ;EAC3D,OAAO;IACL,IAAAG,MAAA,CAAIR,YAAY,aAAU;MACxBS,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE;IACZ,CAAC;IACD,IAAAF,MAAA,CAAIR,YAAY,aAAAQ,MAAA,CAAUR,YAAY,sBAAmB;MACvDW,MAAM,EAAEV,UAAU;MAClBW,UAAU,KAAAJ,MAAA,CAAKP,UAAU,OAAI;MAC7BY,aAAa,EAAEV,OAAO;MACtBO,QAAQ,EAAE,QAAQ;MAClBI,YAAY,EAAE,UAAU;MACxBC,YAAY,EAAEb,gBAAgB;MAC9Bc,WAAW,EAAEV,eAAe;MAC5BW,KAAK,iBAAAT,MAAA,CAAiBN,gBAAgB,GAAG,CAAC;IAC5C,CAAC;IACD,MAAAM,MAAA,CAAMR,YAAY,4BAAAQ,MAAA,CACNR,YAAY,iBAAAQ,MAAA,CAAcR,YAAY,sBAAmB;MACnEW,MAAM,EAAEV,UAAU;MAClBW,UAAU,KAAAJ,MAAA,CAAKP,UAAU;IAC3B,CAAC;IACD,IAAAO,MAAA,CAAIR,YAAY,uBAAAQ,MAAA,CAAoBR,YAAY,mCAAAQ,MAAA,CACtCR,YAAY,sBAAmB;MACvCkB,gBAAgB,EAAEX;IACpB;EACF,CAAC;AACH,CAAC;AACD,MAAMY,gBAAgB,GAAGpB,KAAK,IAAI;EAChC,MAAM;IACJC,YAAY;IACZoB,OAAO;IACPnB,UAAU;IACVoB,mBAAmB;IACnBC,aAAa;IACbC,eAAe;IACfC,iBAAiB;IACjBC,aAAa;IACbC,SAAS;IACTxB,gBAAgB;IAChByB,UAAU;IACVC,kBAAkB;IAClBC,SAAS;IACTC,kBAAkB;IAClBC,cAAc;IACdC;EACF,CAAC,GAAGjC,KAAK;EACT,MAAMkC,eAAe,GAAG;IACtBtB,MAAM,EAAEV,UAAU;IAClBW,UAAU,KAAAJ,MAAA,CAAKP,UAAU,OAAI;IAC7BiC,iBAAiB,EAAE,QAAQ;IAC3BC,aAAa,EAAE;EACjB,CAAC;EACD,OAAO,CAAC;IACN,CAACnC,YAAY,GAAG;MACd,0BAA0BoC,MAAM,CAACC,MAAM,CAAC;QACtC,KAAA7B,MAAA,CAAKR,YAAY,aAAU;UACzBsC,SAAS,EAAE;QACb;MACF,CAAC,EAAExC,sBAAsB,CAACC,KAAK,CAAC;IAClC,CAAC;IACD,IAAAS,MAAA,CAAIR,YAAY,sBAAmB;MACjC,IAAAQ,MAAA,CAAIR,YAAY,iBAAcoC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEvC,sBAAsB,CAACC,KAAK,CAAC,CAAC,EAAE;QAC5FuC,SAAS,EAAER;MACb,CAAC;IACH;EACF,CAAC;EACD;EACA;IACE,IAAAtB,MAAA,CAAIR,YAAY,qBAAAQ,MAAA,CAAkBR,YAAY,eAAAQ,MAAA,CAAYR,YAAY,YAAS;MAC7EuC,QAAQ,EAAEjB,aAAa;MACvBkB,SAAS,kBAAAhC,MAAA,CAAkBe,eAAe,GAAG,GAAG,QAAK;MACrDpB,OAAO,EAAE,GAAG;MACZO,QAAQ,EAAE,QAAQ;MAClB+B,eAAe,EAAE,CAAC;MAClB;MACA;MACA,2BAA2B,EAAE;QAC3BC,SAAS,EAAE,QAAQ;QACnBC,SAAS,EAAE;MACb;IACF;EACF,CAAC;EACD;EACA;IACE,IAAAnC,MAAA,CAAIR,YAAY,eAAY;MAC1BiB,KAAK,EAAE,MAAM;MACb;MACA,KAAAT,MAAA,CAAKR,YAAY,aAAU;QACzB,IAAAQ,MAAA,CAAIR,YAAY,aAAAQ,MAAA,CAAUR,YAAY,sBAAmB;UACvD4C,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,UAAU,EAAE,iBAAAtC,MAAA,CAAiBoB,kBAAkB,iBAAApB,MAAA,CAAkBoB,kBAAkB,cAAApB,MAAA,CAAegB,iBAAiB,OAAAhB,MAAA,CAAIiB,aAAa,EAAG,CAACsB,IAAI,CAAC,GAAG,CAAC;UACjJ,MAAAvC,MAAA,CAAMR,YAAY,sBAAmB;YACnCgD,IAAI,EAAE,MAAM;YACZT,QAAQ,EAAE,CAAC;YACX7B,QAAQ,EAAE,QAAQ;YAClBI,YAAY,EAAE;UAChB,CAAC;UACD,KAAK,EAAE;YACLkC,IAAI,EAAE;UACR;QACF;MACF,CAAC;MACD;MACA,IAAAxC,MAAA,CAAIR,YAAY,UAAAQ,MAAA,CAAOR,YAAY,eAAY;QAC7CG,OAAO,EAAE,CAAC;QACV8C,MAAM,EAAE,CAAC;QACTC,YAAY,EAAE,CAAC;QACfZ,SAAS,EAAE,MAAM;QACjB,QAAA9B,MAAA,CAAQR,YAAY,iBAAAQ,MAAA,CAAcR,YAAY,sBAAmBiC,eAAe;QAChF,MAAAzB,MAAA,CAAMR,YAAY,yBAAsB;UACtCmD,kBAAkB,EAAEzB;QACtB;MACF,CAAC;MACD;MACA,IAAAlB,MAAA,CAAIR,YAAY,aAAUiC;IAC5B;EACF,CAAC;EACD;EACA;IACE,IAAAzB,MAAA,CAAIR,YAAY,yBAAsB;MACpCiB,KAAK,EAAEc,cAAc;MACrB,KAAAvB,MAAA,CAAKR,YAAY,aAAU;QACzB,IAAAQ,MAAA,CAAIR,YAAY,aAAAQ,MAAA,CAAUR,YAAY,eAAAQ,MAAA,CAAYR,YAAY,sBAAmB;UAC/E,MAAAQ,MAAA,CAAMR,YAAY,gCAA6B;YAC7CoD,QAAQ,EAAEzB,UAAU;YACpB0B,SAAS,EAAE;UACb;QACF;MACF,CAAC;MACD,MAAA7C,MAAA,CAAMR,YAAY,0BAAAQ,MAAA,CACVR,YAAY,oBAAAQ,MAAA,CAAiBR,YAAY,yBAAAQ,MAAA,CAAsBR,YAAY,0BAAAQ,MAAA,CAC3ER,YAAY,oBAAAQ,MAAA,CAAiBR,YAAY,yBAAAQ,MAAA,CAAsBR,YAAY,iBAAAQ,MAAA,CAAcR,YAAY,mCAAAQ,MAAA,CACrGR,YAAY,iBAAAQ,MAAA,CAAcR,YAAY,sBAAmB;QAC/DsD,gBAAgB,EAAE,CAAC;QACnBzC,aAAa,gBAAAL,MAAA,CAAgBmB,UAAU,GAAG,CAAC,WAAAnB,MAAA,CAAQN,gBAAgB,QAAK;QACxEY,YAAY,EAAE,MAAM;QACpB,kBAAAN,MAAA,CACMR,YAAY,mCAAAQ,MAAA,CACZR,YAAY,wCACZ;UACJuD,OAAO,EAAE;QACX,CAAC;QACD,IAAA/C,MAAA,CAAIR,YAAY,kBAAAQ,MAAA,CAAeY,OAAO,IAAK;UACzCoC,MAAM,EAAE,CAAC;UACTJ,QAAQ,EAAEpB,iBAAiB;UAC3BpB,UAAU,KAAAJ,MAAA,CAAKP,UAAU,OAAI;UAC7B,QAAQ,EAAE;YACR2C,OAAO,EAAE,cAAc;YACvBW,OAAO,EAAE;UACX;QACF;MACF,CAAC;MACD,IAAA/C,MAAA,CAAIR,YAAY,kBAAAQ,MAAA,CAAeY,OAAO,IAAK;QACzCwB,OAAO,EAAE;MACX,CAAC;MACD,WAAW,EAAE;QACXa,aAAa,EAAE,MAAM;QACrB,IAAAjD,MAAA,CAAIR,YAAY,kBAAAQ,MAAA,CAAeY,OAAO,IAAK;UACzCwB,OAAO,EAAE;QACX,CAAC;QACD,YAAY,EAAE;UACZc,KAAK,EAAErC;QACT;MACF,CAAC;MACD,IAAAb,MAAA,CAAIR,YAAY,yBAAsBoC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAExC,YAAY,CAAC,EAAE;QACnFgB,aAAa,EAAEgB;MACjB,CAAC;IACH;EACF,CAAC,CAAC;AACJ,CAAC;AACD,eAAeV,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}