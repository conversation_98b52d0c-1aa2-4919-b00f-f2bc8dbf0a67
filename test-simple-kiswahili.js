// Simple test for Primary Kiswahili Medium
const http = require('http');

// Test registration
function testRegistration() {
  return new Promise((resolve, reject) => {
    const testData = JSON.stringify({
      firstName: "<PERSON><PERSON><PERSON><PERSON>",
      lastName: "Kiswahili",
      username: "test_kiswahili_simple",
      email: "<EMAIL>",
      school: "<PERSON>le ya Msingi",
      level: "primary_kiswahili",
      class: "5",
      phoneNumber: "0754123462",
      password: "test123"
    });

    const options = {
      hostname: 'localhost',
      port: 5000,
      path: '/api/users/register',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(testData)
      }
    };

    console.log('📝 Testing Primary Kiswahili Medium Registration...');

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        console.log(`Status: ${res.statusCode}`);
        console.log(`Response: ${data}`);
        
        try {
          const response = JSON.parse(data);
          if (res.statusCode === 200 || res.statusCode === 409) {
            console.log('✅ Registration endpoint working!');
            resolve(true);
          } else {
            console.log('❌ Registration failed');
            resolve(false);
          }
        } catch (error) {
          console.log('Response parsing error:', error.message);
          resolve(false);
        }
      });
    });

    req.on('error', (error) => {
      console.log('❌ Request failed:', error.message);
      resolve(false);
    });

    req.write(testData);
    req.end();
  });
}

// Test login
function testLogin() {
  return new Promise((resolve, reject) => {
    const loginData = JSON.stringify({
      email: "test_kiswahili_simple", // The login route expects this field to contain username or email
      password: "test123"
    });

    const options = {
      hostname: 'localhost',
      port: 5000,
      path: '/api/users/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(loginData)
      }
    };

    console.log('\n🔐 Testing Login...');

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        console.log(`Status: ${res.statusCode}`);
        console.log(`Response: ${data.substring(0, 200)}...`);
        
        try {
          const response = JSON.parse(data);
          if (res.statusCode === 200 && response.success) {
            console.log('✅ Login successful!');

            // Check both possible response structures
            const userLevel = response.response?.level || response.data?.user?.level;
            console.log(`   User level found: ${userLevel}`);

            if (userLevel === 'primary_kiswahili') {
              console.log('✅ Level verification passed!');
              resolve(true);
            } else {
              console.log('⚠️ Level verification issue - expected: primary_kiswahili, got:', userLevel);
              resolve(false);
            }
          } else {
            console.log('❌ Login failed');
            resolve(false);
          }
        } catch (error) {
          console.log('Response parsing error:', error.message);
          resolve(false);
        }
      });
    });

    req.on('error', (error) => {
      console.log('❌ Request failed:', error.message);
      resolve(false);
    });

    req.write(loginData);
    req.end();
  });
}

// Test server health
function testServerHealth() {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 5000,
      path: '/api/health',
      method: 'GET'
    };

    console.log('🏥 Testing Server Health...');

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        console.log(`Status: ${res.statusCode}`);
        if (res.statusCode === 200) {
          console.log('✅ Server is healthy!');
          resolve(true);
        } else {
          console.log('⚠️ Server health check failed');
          resolve(false);
        }
      });
    });

    req.on('error', (error) => {
      console.log('❌ Server not responding:', error.message);
      resolve(false);
    });

    req.setTimeout(5000, () => {
      console.log('⚠️ Server health check timeout');
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

// Run simple test
async function runSimpleTest() {
  console.log('🧪 SIMPLE PRIMARY KISWAHILI MEDIUM TEST');
  console.log('='.repeat(50));

  try {
    // Test server health
    const serverHealthy = await testServerHealth();
    if (!serverHealthy) {
      console.log('❌ Cannot proceed - server not healthy');
      return;
    }

    // Test registration
    const registrationWorking = await testRegistration();
    
    // Test login
    const loginWorking = await testLogin();

    // Summary
    console.log('\n' + '='.repeat(50));
    console.log('📊 TEST RESULTS:');
    console.log(`   Server Health: ${serverHealthy ? '✅' : '❌'}`);
    console.log(`   Registration: ${registrationWorking ? '✅' : '❌'}`);
    console.log(`   Login & Level: ${loginWorking ? '✅' : '❌'}`);
    
    if (serverHealthy && registrationWorking && loginWorking) {
      console.log('\n🎉 ALL TESTS PASSED!');
      console.log('🇹🇿 Primary Kiswahili Medium is working correctly!');
      console.log('\n📋 Ready to test in browser:');
      console.log('   1. Visit: http://localhost:3000/register');
      console.log('   2. Select: "Elimu ya Msingi - Kiswahili (Madarasa 1-7)"');
      console.log('   3. Complete registration and login');
      console.log('   4. Experience full Kiswahili interface');
    } else {
      console.log('\n⚠️ Some tests failed - check server and database');
    }

  } catch (error) {
    console.error('\n❌ Test error:', error.message);
  }
}

runSimpleTest();
