{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\VideoLessons\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useMemo } from \"react\";\nimport \"./index.css\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getStudyMaterial } from \"../../../apicalls/study\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { primarySubjects, primaryKiswahiliSubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects.jsx\";\nimport { useLanguage } from \"../../../contexts/LanguageContext\";\n\n// Temporary fix: Use simple text/symbols instead of React Icons to avoid chunk loading issues\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst IconComponents = {\n  FaPlayCircle: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '24px'\n    },\n    children: \"\\u25B6\\uFE0F\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 23\n  }, this),\n  FaGraduationCap: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '24px'\n    },\n    children: \"\\uD83C\\uDF93\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 26\n  }, this),\n  FaTimes: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\u2715\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 18\n  }, this),\n  FaExpand: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\u26F6\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 19\n  }, this),\n  FaCompress: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\u26F6\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 21\n  }, this),\n  TbVideo: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '24px'\n    },\n    children: \"\\uD83D\\uDCF9\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 18\n  }, this),\n  TbFilter: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\uD83D\\uDD0D\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 19\n  }, this),\n  TbSortAscending: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\u2191\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 26\n  }, this),\n  TbSearch: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\uD83D\\uDD0D\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 19\n  }, this),\n  TbX: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '16px'\n    },\n    children: \"\\u2715\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 14\n  }, this),\n  TbDownload: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\u21BB\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 21\n  }, this),\n  TbAlertTriangle: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '24px',\n      color: '#ff6b6b'\n    },\n    children: \"\\u26A0\\uFE0F\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 26\n  }, this),\n  TbInfoCircle: () => /*#__PURE__*/_jsxDEV(\"span\", {\n    style: {\n      fontSize: '18px'\n    },\n    children: \"\\u2139\\uFE0F\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 23\n  }, this)\n};\n\n// Destructure for easy use\nconst {\n  FaPlayCircle,\n  FaGraduationCap,\n  FaTimes,\n  FaExpand,\n  FaCompress,\n  TbVideo,\n  TbFilter,\n  TbSortAscending,\n  TbSearch,\n  TbX,\n  TbDownload,\n  TbAlertTriangle,\n  TbInfoCircle\n} = IconComponents;\nfunction VideoLessons() {\n  _s();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    t,\n    isKiswahili,\n    getClassName,\n    getSubjectName\n  } = useLanguage();\n  const dispatch = useDispatch();\n\n  // State management\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [selectedLevel, setSelectedLevel] = useState((user === null || user === void 0 ? void 0 : user.level) || \"primary\");\n  const [selectedClass, setSelectedClass] = useState((user === null || user === void 0 ? void 0 : user.class) || \"all\");\n  const [selectedSubject, setSelectedSubject] = useState(\"all\");\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sortBy, setSortBy] = useState(\"newest\");\n\n  // Video player state\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [showVideoIndices, setShowVideoIndices] = useState([]);\n  const [isVideoExpanded, setIsVideoExpanded] = useState(false);\n  const [videoError, setVideoError] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n\n  // Comments state\n  const [comments, setComments] = useState([]);\n  const [newComment, setNewComment] = useState(\"\");\n  const [replyingTo, setReplyingTo] = useState(null);\n  const [replyText, setReplyText] = useState(\"\");\n  const [showComments, setShowComments] = useState(true);\n  const [commentsExpanded, setCommentsExpanded] = useState(false);\n\n  // Available classes based on level\n  const availableClasses = useMemo(() => {\n    if (selectedLevel === \"primary\" || selectedLevel === \"primary_kiswahili\") return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n    if (selectedLevel === \"secondary\") return [\"1\", \"2\", \"3\", \"4\"];\n    if (selectedLevel === \"advance\") return [\"5\", \"6\"];\n    return [];\n  }, [selectedLevel]);\n\n  // Available subjects based on level\n  const availableSubjects = useMemo(() => {\n    if (selectedLevel === \"primary\") return primarySubjects;\n    if (selectedLevel === \"primary_kiswahili\") return primaryKiswahiliSubjects;\n    if (selectedLevel === \"secondary\") return secondarySubjects;\n    if (selectedLevel === \"advance\") return advanceSubjects;\n    return [];\n  }, [selectedLevel]);\n\n  // Fetch videos\n  const fetchVideos = useCallback(async () => {\n    try {\n      var _response$data;\n      setLoading(true);\n      setError(null);\n      dispatch(ShowLoading());\n      const filters = {\n        level: selectedLevel,\n        className: \"all\",\n        // Get all classes for the level\n        subject: \"all\",\n        // Get all subjects for the level\n        content: \"videos\"\n      };\n      const response = await getStudyMaterial(filters);\n      if (response !== null && response !== void 0 && (_response$data = response.data) !== null && _response$data !== void 0 && _response$data.success) {\n        setVideos(response.data.data || []);\n      } else {\n        var _response$data2;\n        setError((response === null || response === void 0 ? void 0 : (_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.message) || \"Failed to fetch videos\");\n        setVideos([]);\n      }\n    } catch (error) {\n      console.error(\"❌ Error fetching videos:\", error);\n      setError(\"Failed to load videos. Please try again.\");\n      setVideos([]);\n    } finally {\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  }, [selectedLevel, dispatch]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n    let filtered = videos;\n\n    // Apply level filter\n    filtered = filtered.filter(video => video.level === selectedLevel);\n\n    // Apply class filter\n    if (selectedClass !== \"all\") {\n      filtered = filtered.filter(video => {\n        // Check both className and class fields for compatibility\n        const videoClass = video.className || video.class;\n        return videoClass === selectedClass;\n      });\n    }\n\n    // Apply subject filter\n    if (selectedSubject !== \"all\") {\n      filtered = filtered.filter(video => video.subject === selectedSubject);\n    }\n\n    // Apply search filter\n    if (searchTerm.trim()) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(video => {\n        var _video$title, _video$subject, _video$topic;\n        return ((_video$title = video.title) === null || _video$title === void 0 ? void 0 : _video$title.toLowerCase().includes(searchLower)) || ((_video$subject = video.subject) === null || _video$subject === void 0 ? void 0 : _video$subject.toLowerCase().includes(searchLower)) || ((_video$topic = video.topic) === null || _video$topic === void 0 ? void 0 : _video$topic.toLowerCase().includes(searchLower));\n      });\n    }\n\n    // Apply sorting\n    const sorted = [...filtered].sort((a, b) => {\n      switch (sortBy) {\n        case \"newest\":\n          return new Date(b.createdAt || 0) - new Date(a.createdAt || 0);\n        case \"oldest\":\n          return new Date(a.createdAt || 0) - new Date(b.createdAt || 0);\n        case \"title\":\n          return (a.title || \"\").localeCompare(b.title || \"\");\n        case \"subject\":\n          return (a.subject || \"\").localeCompare(b.subject || \"\");\n        default:\n          return 0;\n      }\n    });\n    console.log('✅ Final filtered videos:', sorted.length);\n    if (sorted.length > 0) {\n      console.log('📹 Sample filtered video:', sorted[0]);\n    }\n    return sorted;\n  }, [videos, searchTerm, sortBy, selectedLevel, selectedClass, selectedSubject]);\n\n  // Video handlers\n  const handleShowVideo = async index => {\n    const video = filteredAndSortedVideos[index];\n    setCurrentVideoIndex(index);\n    setShowVideoIndices([index]);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video !== null && video !== void 0 && video.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        video.signedVideoUrl = signedUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n  const handleHideVideo = () => {\n    setShowVideoIndices([]);\n    setCurrentVideoIndex(null);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n    if (videoRef) {\n      videoRef.pause();\n    }\n  };\n  const toggleVideoExpansion = () => {\n    setIsVideoExpanded(!isVideoExpanded);\n  };\n\n  // Get signed URL for S3 videos to ensure access\n  const getSignedVideoUrl = async videoUrl => {\n    if (!videoUrl) return videoUrl;\n\n    // For AWS S3 URLs, get signed URL from backend\n    if (videoUrl.includes('amazonaws.com') || videoUrl.includes('s3.')) {\n      try {\n        const response = await fetch(`http://localhost:5000/api/study/video-signed-url?videoUrl=${encodeURIComponent(videoUrl)}`, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json'\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        if (data.success && data.signedUrl) {\n          console.log('✅ Got signed URL for S3 video');\n          return data.signedUrl;\n        } else {\n          console.warn('⚠️ Invalid response from signed URL endpoint:', data);\n          return videoUrl;\n        }\n      } catch (error) {\n        console.error('❌ Error getting signed URL:', error);\n        return videoUrl;\n      }\n    }\n    return videoUrl;\n  };\n\n  // Get thumbnail URL\n  const getThumbnailUrl = video => {\n    if (video.thumbnail) {\n      return video.thumbnail;\n    }\n    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n      let videoId = video.videoID;\n      if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n        const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n        videoId = match ? match[1] : videoId;\n      }\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n    return '/api/placeholder/400/225';\n  };\n\n  // Effects\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n  useEffect(() => {\n    if (user !== null && user !== void 0 && user.level) {\n      setSelectedLevel(user.level);\n    }\n    if (user !== null && user !== void 0 && user.class) {\n      setSelectedClass(user.class);\n    }\n  }, [user]);\n\n  // Clear search and refresh\n  const handleClearSearch = () => {\n    setSearchTerm(\"\");\n  };\n  const handleRefresh = () => {\n    // Only refresh data, don't clear filters or search\n    fetchVideos();\n  };\n  const handleClearAll = () => {\n    setSearchTerm(\"\");\n    setSelectedSubject(\"all\");\n    setSelectedClass(\"all\");\n    fetchVideos();\n  };\n\n  // Comment functions\n  const handleAddComment = () => {\n    if (newComment.trim()) {\n      var _user$name, _user$name$charAt;\n      const comment = {\n        id: Date.now(),\n        text: newComment,\n        author: (user === null || user === void 0 ? void 0 : user.name) || \"Anonymous\",\n        avatar: (user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : (_user$name$charAt = _user$name.charAt(0)) === null || _user$name$charAt === void 0 ? void 0 : _user$name$charAt.toUpperCase()) || \"A\",\n        timestamp: new Date().toISOString(),\n        replies: [],\n        likes: 0,\n        liked: false\n      };\n      setComments([comment, ...comments]); // Add new comments at the top\n      setNewComment(\"\");\n    }\n  };\n  const handleAddReply = commentId => {\n    if (replyText.trim()) {\n      var _user$name2, _user$name2$charAt;\n      const reply = {\n        id: Date.now(),\n        text: replyText,\n        author: (user === null || user === void 0 ? void 0 : user.name) || \"Anonymous\",\n        avatar: (user === null || user === void 0 ? void 0 : (_user$name2 = user.name) === null || _user$name2 === void 0 ? void 0 : (_user$name2$charAt = _user$name2.charAt(0)) === null || _user$name2$charAt === void 0 ? void 0 : _user$name2$charAt.toUpperCase()) || \"A\",\n        timestamp: new Date().toISOString(),\n        likes: 0,\n        liked: false\n      };\n      setComments(comments.map(comment => comment.id === commentId ? {\n        ...comment,\n        replies: [...comment.replies, reply]\n      } : comment));\n      setReplyText(\"\");\n      setReplyingTo(null);\n    }\n  };\n  const handleLikeComment = (commentId, isReply = false, parentId = null) => {\n    if (isReply) {\n      setComments(comments.map(comment => comment.id === parentId ? {\n        ...comment,\n        replies: comment.replies.map(reply => reply.id === commentId ? {\n          ...reply,\n          liked: !reply.liked,\n          likes: reply.liked ? reply.likes - 1 : reply.likes + 1\n        } : reply)\n      } : comment));\n    } else {\n      setComments(comments.map(comment => comment.id === commentId ? {\n        ...comment,\n        liked: !comment.liked,\n        likes: comment.liked ? comment.likes - 1 : comment.likes + 1\n      } : comment));\n    }\n  };\n  const formatTimeAgo = timestamp => {\n    const now = new Date();\n    const time = new Date(timestamp);\n    const diffInSeconds = Math.floor((now - time) / 1000);\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;\n    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;\n    return time.toLocaleDateString();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"video-lessons-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-lessons-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-main\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"header-icon\",\n            children: /*#__PURE__*/_jsxDEV(TbVideo, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"header-text\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              children: \"Video Lessons\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Watch educational videos to enhance your learning\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"level-display\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"current-level\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"level-label\",\n              children: \"Level:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"level-value\",\n              children: selectedLevel.charAt(0).toUpperCase() + selectedLevel.slice(1)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"current-class\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"class-label\",\n              children: \"Your Class:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"class-value\",\n              children: (user === null || user === void 0 ? void 0 : user.level) === 'primary' ? `Class ${(user === null || user === void 0 ? void 0 : user.class) || 'N/A'}` : (user === null || user === void 0 ? void 0 : user.level) === 'secondary' ? `Form ${(user === null || user === void 0 ? void 0 : user.class) || 'N/A'}` : (user === null || user === void 0 ? void 0 : user.level) === 'advance' ? `Form ${(user === null || user === void 0 ? void 0 : user.class) || 'N/A'}` : 'Not Set'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 378,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-lessons-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"video-controls\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"controls-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"control-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"control-label\",\n              children: [/*#__PURE__*/_jsxDEV(TbFilter, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 17\n              }, this), isKiswahili ? 'Chuja kwa Darasa' : 'Filter by Class']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedClass,\n              onChange: e => setSelectedClass(e.target.value),\n              className: \"control-select class-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: \"All Classes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 17\n              }, this), availableClasses.map(cls => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: cls,\n                children: selectedLevel === 'primary' ? `Class ${cls}` : `Form ${cls}`\n              }, cls, false, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"control-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"control-label\",\n              children: [/*#__PURE__*/_jsxDEV(TbFilter, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 17\n              }, this), \"Subject\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedSubject,\n              onChange: e => setSelectedSubject(e.target.value),\n              className: \"control-select subject-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: \"All Subjects\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 17\n              }, this), availableSubjects.map(subject => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: subject,\n                children: subject\n              }, subject, false, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"control-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"control-label\",\n              children: [/*#__PURE__*/_jsxDEV(TbSortAscending, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 456,\n                columnNumber: 17\n              }, this), \"Sort\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 455,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: sortBy,\n              onChange: e => setSortBy(e.target.value),\n              className: \"control-select sort-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"newest\",\n                children: \"Newest First\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"oldest\",\n                children: \"Oldest First\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"title\",\n                children: \"Title A-Z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"subject\",\n                children: \"Subject A-Z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-container\",\n            children: [/*#__PURE__*/_jsxDEV(TbSearch, {\n              className: \"search-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search videos by title, subject, or topic...\",\n              value: searchTerm,\n              onChange: e => setSearchTerm(e.target.value),\n              className: \"search-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 15\n            }, this), searchTerm && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleClearSearch,\n              className: \"clear-search-btn\",\n              children: [/*#__PURE__*/_jsxDEV(TbX, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 485,\n                columnNumber: 19\n              }, this), \"Clear Search\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleRefresh,\n            className: \"refresh-btn\",\n            children: [/*#__PURE__*/_jsxDEV(TbDownload, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 15\n            }, this), \"Refresh All\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 411,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 501,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading videos...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 502,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 500,\n        columnNumber: 11\n      }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-state\",\n        children: [/*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n          className: \"error-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 506,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Error Loading Videos\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchVideos,\n          className: \"retry-btn\",\n          children: \"Try Again\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 505,\n        columnNumber: 11\n      }, this) : filteredAndSortedVideos.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"videos-grid\",\n        children: filteredAndSortedVideos.map((video, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"video-card\",\n          onClick: () => handleShowVideo(index),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-card-thumbnail\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: getThumbnailUrl(video),\n              alt: video.title,\n              className: \"thumbnail-image\",\n              onError: e => {\n                // Fallback logic for failed thumbnails\n                if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                  // For YouTube videos, try different quality thumbnails\n                  let videoId = video.videoID;\n                  if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n                    const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                    videoId = match ? match[1] : videoId;\n                  }\n                  const fallbacks = [`https://img.youtube.com/vi/${videoId}/hqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`, `https://img.youtube.com/vi/${videoId}/default.jpg`, '/api/placeholder/320/180'];\n                  const currentSrc = e.target.src;\n                  const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n                  if (currentIndex < fallbacks.length - 1) {\n                    e.target.src = fallbacks[currentIndex + 1];\n                  }\n                } else {\n                  e.target.src = '/api/placeholder/320/180';\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"play-overlay\",\n              children: /*#__PURE__*/_jsxDEV(FaPlayCircle, {\n                className: \"play-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 551,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-duration\",\n              children: video.duration || \"Video\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 19\n            }, this), video.subtitles && video.subtitles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"subtitle-badge\",\n              children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 558,\n                columnNumber: 23\n              }, this), \"CC\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-card-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"video-title\",\n              children: video.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 565,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-meta\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"video-subject\",\n                children: video.subject\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 567,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"video-class\",\n                children: selectedLevel === 'primary' ? `Class ${video.className || video.class}` : `Form ${video.className || video.class}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 566,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-tags\",\n              children: [video.topic && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"topic-tag\",\n                children: video.topic\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 573,\n                columnNumber: 37\n              }, this), video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"shared-tag\",\n                children: [\"Shared from \", selectedLevel === 'primary' ? `Class ${video.sharedFromClass}` : `Form ${video.sharedFromClass}`]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 575,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 564,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 516,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 514,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: [/*#__PURE__*/_jsxDEV(FaGraduationCap, {\n          className: \"empty-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 586,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"No Videos Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 587,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No video lessons are available for your current selection.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 588,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"suggestion\",\n          children: \"Try selecting a different class or subject.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 589,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 585,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 409,\n      columnNumber: 7\n    }, this), showVideoIndices.length > 0 && currentVideoIndex !== null && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `video-overlay ${isVideoExpanded ? 'expanded' : ''}`,\n      onClick: e => {\n        if (e.target === e.currentTarget) handleHideVideo();\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `video-modal ${isVideoExpanded ? 'expanded' : ''}`,\n        children: ((_user$name3, _user$name3$charAt) => {\n          const video = filteredAndSortedVideos[currentVideoIndex];\n          if (!video) return /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"Video not found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 602,\n            columnNumber: 34\n          }, this);\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"video-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"video-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"video-title\",\n                  children: video.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 608,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-meta\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-subject\",\n                    children: video.subject\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 610,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-class\",\n                    children: [\"Class \", video.className]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 611,\n                    columnNumber: 25\n                  }, this), video.level && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"video-level\",\n                    children: video.level\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 612,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 609,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 607,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-controls\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"control-btn add-comment-btn\",\n                  onClick: () => {\n                    setCommentsExpanded(!commentsExpanded);\n                    if (!commentsExpanded && !isVideoExpanded) {\n                      toggleVideoExpansion();\n                    }\n                  },\n                  title: \"Add Comment\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"btn-icon\",\n                    children: \"\\uD83D\\uDCAC\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 626,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"btn-text\",\n                    children: \"Comment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 627,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 616,\n                  columnNumber: 23\n                }, this), isVideoExpanded && commentsExpanded && /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"control-btn close-comment-btn\",\n                  onClick: () => {\n                    setCommentsExpanded(false);\n                    toggleVideoExpansion();\n                  },\n                  title: \"Close Comments\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"btn-icon\",\n                    children: \"\\u2715\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 638,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"btn-text\",\n                    children: \"Close\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 639,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 630,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"control-btn close-btn\",\n                  onClick: handleHideVideo,\n                  title: \"Close video\",\n                  children: /*#__PURE__*/_jsxDEV(FaTimes, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 647,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 642,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 615,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `video-main-layout ${isVideoExpanded ? 'expanded-layout' : 'normal-layout'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"video-container\",\n                children: video.videoUrl ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    padding: '15px',\n                    background: '#000',\n                    borderRadius: '8px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"video\", {\n                    ref: ref => setVideoRef(ref),\n                    controls: true,\n                    autoPlay: true,\n                    playsInline: true,\n                    preload: \"metadata\",\n                    width: \"100%\",\n                    height: \"400\",\n                    poster: getThumbnailUrl(video),\n                    style: {\n                      width: '100%',\n                      height: '400px',\n                      backgroundColor: '#000'\n                    },\n                    onError: e => {\n                      setVideoError(`Failed to load video: ${video.title}. Please try refreshing the page.`);\n                    },\n                    onCanPlay: () => {\n                      setVideoError(null);\n                    },\n                    onLoadStart: () => {\n                      console.log('🎬 Video loading started');\n                    },\n                    crossOrigin: \"anonymous\",\n                    children: [/*#__PURE__*/_jsxDEV(\"source\", {\n                      src: video.signedVideoUrl || video.videoUrl,\n                      type: \"video/mp4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 684,\n                      columnNumber: 29\n                    }, this), video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => /*#__PURE__*/_jsxDEV(\"track\", {\n                      kind: \"subtitles\",\n                      src: subtitle.url,\n                      srcLang: subtitle.language,\n                      label: subtitle.languageName,\n                      default: subtitle.isDefault || index === 0\n                    }, `${subtitle.language}-${index}`, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 688,\n                      columnNumber: 31\n                    }, this)), \"Your browser does not support the video tag.\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 658,\n                    columnNumber: 27\n                  }, this), video.subtitles && video.subtitles.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"subtitle-indicator\",\n                    children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {\n                      className: \"subtitle-icon\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 704,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [\"Subtitles available in \", video.subtitles.length, \" language(s)\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 705,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 703,\n                    columnNumber: 29\n                  }, this), videoError && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"video-error-overlay\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"error-content\",\n                      children: [/*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n                        className: \"error-icon\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 713,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: videoError\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 714,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => setVideoError(null),\n                        className: \"dismiss-error-btn\",\n                        children: \"Dismiss\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 715,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 712,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 711,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 657,\n                  columnNumber: 23\n                }, this) : video.videoID ?\n                /*#__PURE__*/\n                // Fallback to YouTube embed if no videoUrl\n                _jsxDEV(\"iframe\", {\n                  src: `https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`,\n                  title: video.title,\n                  frameBorder: \"0\",\n                  allowFullScreen: true,\n                  className: \"video-iframe\",\n                  onLoad: () => console.log('✅ YouTube iframe loaded')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 724,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"video-error\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"error-icon\",\n                    children: \"\\u26A0\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 734,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    children: \"Video Unavailable\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 735,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: videoError || \"This video cannot be played at the moment.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 736,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"error-actions\",\n                    children: /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: video.signedVideoUrl || video.videoUrl,\n                      target: \"_blank\",\n                      rel: \"noopener noreferrer\",\n                      className: \"external-link-btn\",\n                      children: \"\\uD83D\\uDCF1 Open in New Tab\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 738,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 737,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 733,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 655,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `comments-section-below ${isVideoExpanded ? 'expanded-comments' : 'normal-comments'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"comments-count-header\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"comments-count-display\",\n                    children: [/*#__PURE__*/_jsxDEV(TbInfoCircle, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 756,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [comments.length, \" \", comments.length === 1 ? 'comment' : 'comments']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 757,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 755,\n                    columnNumber: 25\n                  }, this), !isVideoExpanded || !commentsExpanded ? /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      setCommentsExpanded(true);\n                      if (!isVideoExpanded) {\n                        toggleVideoExpansion();\n                      }\n                    },\n                    className: \"view-comments-btn\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"btn-icon\",\n                      children: \"\\uD83D\\uDC41\\uFE0F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 769,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"btn-text\",\n                      children: \"View\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 770,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 760,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setCommentsExpanded(false),\n                    className: \"comments-toggle-btn\",\n                    children: \"\\u25BC Minimize\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 773,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 754,\n                  columnNumber: 23\n                }, this), isVideoExpanded && commentsExpanded && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"comments-content maximized\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"add-comment\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"comment-input-container\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"user-avatar\",\n                        children: (user === null || user === void 0 ? void 0 : (_user$name3 = user.name) === null || _user$name3 === void 0 ? void 0 : (_user$name3$charAt = _user$name3.charAt(0)) === null || _user$name3$charAt === void 0 ? void 0 : _user$name3$charAt.toUpperCase()) || \"A\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 788,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"comment-input-wrapper\",\n                        children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n                          value: newComment,\n                          onChange: e => setNewComment(e.target.value),\n                          placeholder: \"Share your thoughts about this video...\",\n                          className: \"comment-input\",\n                          rows: \"3\",\n                          autoFocus: true\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 792,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: handleAddComment,\n                          className: \"comment-submit-btn\",\n                          disabled: !newComment.trim(),\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            children: \"\\uD83D\\uDCAC\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 805,\n                            columnNumber: 37\n                          }, this), \" Post Comment\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 800,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 791,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 787,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 786,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"comments-list\",\n                    children: comments.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"no-comments\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"no-comments-icon\",\n                        children: \"\\uD83D\\uDCAC\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 815,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"No comments yet. Be the first to share your thoughts!\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 816,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 814,\n                      columnNumber: 27\n                    }, this) : comments.map(comment => {\n                      var _comment$author, _comment$author$charA, _user$name4, _user$name4$charAt;\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"comment\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"comment-main\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"comment-avatar\",\n                            children: comment.avatar || ((_comment$author = comment.author) === null || _comment$author === void 0 ? void 0 : (_comment$author$charA = _comment$author.charAt(0)) === null || _comment$author$charA === void 0 ? void 0 : _comment$author$charA.toUpperCase()) || \"A\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 822,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"comment-content\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"comment-header\",\n                              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"comment-author\",\n                                children: comment.author\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 827,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"comment-time\",\n                                children: formatTimeAgo(comment.timestamp)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 828,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 826,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"comment-text\",\n                              children: comment.text\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 832,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"comment-actions\",\n                              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                                onClick: () => handleLikeComment(comment.id),\n                                className: `like-btn ${comment.liked ? 'liked' : ''}`,\n                                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                  children: comment.liked ? '❤️' : '🤍'\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 838,\n                                  columnNumber: 39\n                                }, this), comment.likes > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"like-count\",\n                                  children: comment.likes\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 839,\n                                  columnNumber: 61\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 834,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                                onClick: () => setReplyingTo(replyingTo === comment.id ? null : comment.id),\n                                className: \"reply-btn\",\n                                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                  children: \"\\uD83D\\uDCAC\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 845,\n                                  columnNumber: 39\n                                }, this), \" Reply\"]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 841,\n                                columnNumber: 37\n                              }, this), comment.replies.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"replies-count\",\n                                children: [comment.replies.length, \" \", comment.replies.length === 1 ? 'reply' : 'replies']\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 848,\n                                columnNumber: 39\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 833,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 825,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 821,\n                          columnNumber: 31\n                        }, this), replyingTo === comment.id && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"reply-input-container\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"reply-input-wrapper\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"user-avatar small\",\n                              children: (user === null || user === void 0 ? void 0 : (_user$name4 = user.name) === null || _user$name4 === void 0 ? void 0 : (_user$name4$charAt = _user$name4.charAt(0)) === null || _user$name4$charAt === void 0 ? void 0 : _user$name4$charAt.toUpperCase()) || \"A\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 860,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"reply-input-content\",\n                              children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n                                value: replyText,\n                                onChange: e => setReplyText(e.target.value),\n                                placeholder: `Reply to ${comment.author}...`,\n                                className: \"reply-input\",\n                                rows: \"2\",\n                                autoFocus: true\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 864,\n                                columnNumber: 39\n                              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"reply-actions\",\n                                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                                  onClick: () => handleAddReply(comment.id),\n                                  className: \"reply-submit-btn\",\n                                  disabled: !replyText.trim(),\n                                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                    children: \"\\uD83D\\uDCAC\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 878,\n                                    columnNumber: 43\n                                  }, this), \" Reply\"]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 873,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                                  onClick: () => {\n                                    setReplyingTo(null);\n                                    setReplyText(\"\");\n                                  },\n                                  className: \"reply-cancel-btn\",\n                                  children: \"Cancel\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 880,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 872,\n                                columnNumber: 39\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 863,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 859,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 858,\n                          columnNumber: 33\n                        }, this), comment.replies.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"replies\",\n                          children: comment.replies.map(reply => {\n                            var _reply$author, _reply$author$charAt;\n                            return /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"reply\",\n                              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"reply-main\",\n                                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"reply-avatar\",\n                                  children: reply.avatar || ((_reply$author = reply.author) === null || _reply$author === void 0 ? void 0 : (_reply$author$charAt = _reply$author.charAt(0)) === null || _reply$author$charAt === void 0 ? void 0 : _reply$author$charAt.toUpperCase()) || \"A\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 901,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"reply-content\",\n                                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: \"reply-header\",\n                                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                      className: \"reply-author\",\n                                      children: reply.author\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 906,\n                                      columnNumber: 45\n                                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                      className: \"reply-time\",\n                                      children: formatTimeAgo(reply.timestamp)\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 907,\n                                      columnNumber: 45\n                                    }, this)]\n                                  }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 905,\n                                    columnNumber: 43\n                                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: \"reply-text\",\n                                    children: reply.text\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 911,\n                                    columnNumber: 43\n                                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: \"reply-actions\",\n                                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                                      onClick: () => handleLikeComment(reply.id, true, comment.id),\n                                      className: `like-btn small ${reply.liked ? 'liked' : ''}`,\n                                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                        children: reply.liked ? '❤️' : '🤍'\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 917,\n                                        columnNumber: 47\n                                      }, this), reply.likes > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                                        className: \"like-count\",\n                                        children: reply.likes\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 918,\n                                        columnNumber: 67\n                                      }, this)]\n                                    }, void 0, true, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 913,\n                                      columnNumber: 45\n                                    }, this)\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 912,\n                                    columnNumber: 43\n                                  }, this)]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 904,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 900,\n                                columnNumber: 39\n                              }, this)\n                            }, reply.id, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 899,\n                              columnNumber: 37\n                            }, this);\n                          })\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 897,\n                          columnNumber: 33\n                        }, this)]\n                      }, comment.id, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 820,\n                        columnNumber: 29\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 812,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 784,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 752,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 653,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 605,\n            columnNumber: 17\n          }, this);\n        })()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 599,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 596,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 376,\n    columnNumber: 5\n  }, this);\n}\n_s(VideoLessons, \"/jXursBioI9U/+PUrUN2ryIFbCw=\", false, function () {\n  return [useSelector, useLanguage, useDispatch];\n});\n_c = VideoLessons;\nexport default VideoLessons;\nvar _c;\n$RefreshReg$(_c, \"VideoLessons\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "motion", "AnimatePresence", "getStudyMaterial", "useDispatch", "useSelector", "HideLoading", "ShowLoading", "primarySubjects", "primaryKiswahiliSubjects", "secondarySubjects", "advanceSubjects", "useLanguage", "jsxDEV", "_jsxDEV", "IconComponents", "FaPlayCircle", "style", "fontSize", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "FaGraduationCap", "FaTimes", "FaExpand", "FaCompress", "TbVideo", "Tb<PERSON><PERSON>er", "TbSortAscending", "TbSearch", "TbX", "TbDownload", "TbAlertTriangle", "color", "TbInfoCircle", "VideoLessons", "_s", "user", "state", "t", "isKiswahili", "getClassName", "getSubjectName", "dispatch", "videos", "setVideos", "loading", "setLoading", "error", "setError", "selectedLevel", "setSelectedLevel", "level", "selectedClass", "setSelectedClass", "class", "selectedSubject", "setSelectedSubject", "searchTerm", "setSearchTerm", "sortBy", "setSortBy", "currentVideoIndex", "setCurrentVideoIndex", "showVideoIndices", "setShowVideoIndices", "isVideoExpanded", "setIsVideoExpanded", "videoError", "setVideoError", "videoRef", "setVideoRef", "comments", "setComments", "newComment", "setNewComment", "replyingTo", "setReplyingTo", "replyText", "setReplyText", "showComments", "setShowComments", "commentsExpanded", "setCommentsExpanded", "availableClasses", "availableSubjects", "fetchVideos", "_response$data", "filters", "className", "subject", "content", "response", "data", "success", "_response$data2", "message", "console", "filteredAndSortedVideos", "filtered", "filter", "video", "videoClass", "trim", "searchLower", "toLowerCase", "_video$title", "_video$subject", "_video$topic", "title", "includes", "topic", "sorted", "sort", "a", "b", "Date", "createdAt", "localeCompare", "log", "length", "handleShowVideo", "index", "videoUrl", "signedUrl", "getSignedVideoUrl", "signedVideoUrl", "warn", "handleHideVideo", "pause", "toggleVideoExpansion", "fetch", "encodeURIComponent", "method", "headers", "ok", "Error", "status", "json", "getThumbnailUrl", "thumbnail", "videoID", "videoId", "match", "handleClearSearch", "handleRefresh", "handleClearAll", "handleAddComment", "_user$name", "_user$name$charAt", "comment", "id", "now", "text", "author", "name", "avatar", "char<PERSON>t", "toUpperCase", "timestamp", "toISOString", "replies", "likes", "liked", "handleAddReply", "commentId", "_user$name2", "_user$name2$charAt", "reply", "map", "handleLikeComment", "isReply", "parentId", "formatTimeAgo", "time", "diffInSeconds", "Math", "floor", "toLocaleDateString", "slice", "value", "onChange", "e", "target", "cls", "type", "placeholder", "onClick", "src", "alt", "onError", "fallbacks", "currentSrc", "currentIndex", "findIndex", "url", "split", "pop", "duration", "subtitles", "sharedFromClass", "currentTarget", "_user$name3", "_user$name3$charAt", "padding", "background", "borderRadius", "ref", "controls", "autoPlay", "playsInline", "preload", "width", "height", "poster", "backgroundColor", "onCanPlay", "onLoadStart", "crossOrigin", "subtitle", "kind", "srcLang", "language", "label", "languageName", "default", "isDefault", "frameBorder", "allowFullScreen", "onLoad", "href", "rel", "rows", "autoFocus", "disabled", "_comment$author", "_comment$author$charA", "_user$name4", "_user$name4$charAt", "_reply$author", "_reply$author$charAt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/VideoLessons/index.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo } from \"react\";\nimport \"./index.css\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getStudyMaterial } from \"../../../apicalls/study\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { primarySubjects, primaryKiswahiliSubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects.jsx\";\nimport { useLanguage } from \"../../../contexts/LanguageContext\";\n\n// Temporary fix: Use simple text/symbols instead of React Icons to avoid chunk loading issues\nconst IconComponents = {\n  FaPlayCircle: () => <span style={{fontSize: '24px'}}>▶️</span>,\n  FaGraduationCap: () => <span style={{fontSize: '24px'}}>🎓</span>,\n  FaTimes: () => <span style={{fontSize: '18px'}}>✕</span>,\n  FaExpand: () => <span style={{fontSize: '18px'}}>⛶</span>,\n  FaCompress: () => <span style={{fontSize: '18px'}}>⛶</span>,\n  TbVideo: () => <span style={{fontSize: '24px'}}>📹</span>,\n  TbFilter: () => <span style={{fontSize: '18px'}}>🔍</span>,\n  TbSortAscending: () => <span style={{fontSize: '18px'}}>↑</span>,\n  TbSearch: () => <span style={{fontSize: '18px'}}>🔍</span>,\n  TbX: () => <span style={{fontSize: '16px'}}>✕</span>,\n  TbDownload: () => <span style={{fontSize: '18px'}}>↻</span>,\n  TbAlertTriangle: () => <span style={{fontSize: '24px', color: '#ff6b6b'}}>⚠️</span>,\n  TbInfoCircle: () => <span style={{fontSize: '18px'}}>ℹ️</span>\n};\n\n// Destructure for easy use\nconst {\n  FaPlayCircle,\n  FaGraduationCap,\n  FaTimes,\n  FaExpand,\n  FaCompress,\n  TbVideo,\n  TbFilter,\n  TbSortAscending,\n  TbSearch,\n  TbX,\n  TbDownload,\n  TbAlertTriangle,\n  TbInfoCircle\n} = IconComponents;\n\nfunction VideoLessons() {\n  const { user } = useSelector((state) => state.user);\n  const { t, isKiswahili, getClassName, getSubjectName } = useLanguage();\n  const dispatch = useDispatch();\n\n  // State management\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [selectedLevel, setSelectedLevel] = useState(user?.level || \"primary\");\n  const [selectedClass, setSelectedClass] = useState(user?.class || \"all\");\n  const [selectedSubject, setSelectedSubject] = useState(\"all\");\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sortBy, setSortBy] = useState(\"newest\");\n\n  // Video player state\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [showVideoIndices, setShowVideoIndices] = useState([]);\n  const [isVideoExpanded, setIsVideoExpanded] = useState(false);\n  const [videoError, setVideoError] = useState(null);\n  const [videoRef, setVideoRef] = useState(null);\n\n  // Comments state\n  const [comments, setComments] = useState([]);\n  const [newComment, setNewComment] = useState(\"\");\n  const [replyingTo, setReplyingTo] = useState(null);\n  const [replyText, setReplyText] = useState(\"\");\n  const [showComments, setShowComments] = useState(true);\n  const [commentsExpanded, setCommentsExpanded] = useState(false);\n\n  // Available classes based on level\n  const availableClasses = useMemo(() => {\n    if (selectedLevel === \"primary\" || selectedLevel === \"primary_kiswahili\") return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n    if (selectedLevel === \"secondary\") return [\"1\", \"2\", \"3\", \"4\"];\n    if (selectedLevel === \"advance\") return [\"5\", \"6\"];\n    return [];\n  }, [selectedLevel]);\n\n  // Available subjects based on level\n  const availableSubjects = useMemo(() => {\n    if (selectedLevel === \"primary\") return primarySubjects;\n    if (selectedLevel === \"primary_kiswahili\") return primaryKiswahiliSubjects;\n    if (selectedLevel === \"secondary\") return secondarySubjects;\n    if (selectedLevel === \"advance\") return advanceSubjects;\n    return [];\n  }, [selectedLevel]);\n\n  // Fetch videos\n  const fetchVideos = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      dispatch(ShowLoading());\n\n      const filters = {\n        level: selectedLevel,\n        className: \"all\", // Get all classes for the level\n        subject: \"all\", // Get all subjects for the level\n        content: \"videos\"\n      };\n\n      const response = await getStudyMaterial(filters);\n\n      if (response?.data?.success) {\n        setVideos(response.data.data || []);\n      } else {\n        setError(response?.data?.message || \"Failed to fetch videos\");\n        setVideos([]);\n      }\n    } catch (error) {\n      console.error(\"❌ Error fetching videos:\", error);\n      setError(\"Failed to load videos. Please try again.\");\n      setVideos([]);\n    } finally {\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  }, [selectedLevel, dispatch]);\n\n  // Filter and sort videos\n  const filteredAndSortedVideos = useMemo(() => {\n\n\n    let filtered = videos;\n\n    // Apply level filter\n    filtered = filtered.filter(video => video.level === selectedLevel);\n\n    // Apply class filter\n    if (selectedClass !== \"all\") {\n      filtered = filtered.filter(video => {\n        // Check both className and class fields for compatibility\n        const videoClass = video.className || video.class;\n        return videoClass === selectedClass;\n      });\n    }\n\n    // Apply subject filter\n    if (selectedSubject !== \"all\") {\n      filtered = filtered.filter(video => video.subject === selectedSubject);\n    }\n\n    // Apply search filter\n    if (searchTerm.trim()) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(video =>\n        video.title?.toLowerCase().includes(searchLower) ||\n        video.subject?.toLowerCase().includes(searchLower) ||\n        video.topic?.toLowerCase().includes(searchLower)\n      );\n    }\n\n    // Apply sorting\n    const sorted = [...filtered].sort((a, b) => {\n      switch (sortBy) {\n        case \"newest\":\n          return new Date(b.createdAt || 0) - new Date(a.createdAt || 0);\n        case \"oldest\":\n          return new Date(a.createdAt || 0) - new Date(b.createdAt || 0);\n        case \"title\":\n          return (a.title || \"\").localeCompare(b.title || \"\");\n        case \"subject\":\n          return (a.subject || \"\").localeCompare(b.subject || \"\");\n        default:\n          return 0;\n      }\n    });\n\n    console.log('✅ Final filtered videos:', sorted.length);\n    if (sorted.length > 0) {\n      console.log('📹 Sample filtered video:', sorted[0]);\n    }\n\n    return sorted;\n  }, [videos, searchTerm, sortBy, selectedLevel, selectedClass, selectedSubject]);\n\n  // Video handlers\n  const handleShowVideo = async (index) => {\n    const video = filteredAndSortedVideos[index];\n\n    setCurrentVideoIndex(index);\n    setShowVideoIndices([index]);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video?.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        video.signedVideoUrl = signedUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  const handleHideVideo = () => {\n    setShowVideoIndices([]);\n    setCurrentVideoIndex(null);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n    if (videoRef) {\n      videoRef.pause();\n    }\n  };\n\n  const toggleVideoExpansion = () => {\n    setIsVideoExpanded(!isVideoExpanded);\n  };\n\n  // Get signed URL for S3 videos to ensure access\n  const getSignedVideoUrl = async (videoUrl) => {\n    if (!videoUrl) return videoUrl;\n\n    // For AWS S3 URLs, get signed URL from backend\n    if (videoUrl.includes('amazonaws.com') || videoUrl.includes('s3.')) {\n      try {\n        const response = await fetch(`http://localhost:5000/api/study/video-signed-url?videoUrl=${encodeURIComponent(videoUrl)}`, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json',\n          }\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const data = await response.json();\n\n        if (data.success && data.signedUrl) {\n          console.log('✅ Got signed URL for S3 video');\n          return data.signedUrl;\n        } else {\n          console.warn('⚠️ Invalid response from signed URL endpoint:', data);\n          return videoUrl;\n        }\n      } catch (error) {\n        console.error('❌ Error getting signed URL:', error);\n        return videoUrl;\n      }\n    }\n\n    return videoUrl;\n  };\n\n  // Get thumbnail URL\n  const getThumbnailUrl = (video) => {\n    if (video.thumbnail) {\n      return video.thumbnail;\n    }\n    \n    if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n      let videoId = video.videoID;\n      if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n        const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n        videoId = match ? match[1] : videoId;\n      }\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n    \n    return '/api/placeholder/400/225';\n  };\n\n  // Effects\n  useEffect(() => {\n    fetchVideos();\n  }, [fetchVideos]);\n\n  useEffect(() => {\n    if (user?.level) {\n      setSelectedLevel(user.level);\n    }\n    if (user?.class) {\n      setSelectedClass(user.class);\n    }\n  }, [user]);\n\n  // Clear search and refresh\n  const handleClearSearch = () => {\n    setSearchTerm(\"\");\n  };\n\n  const handleRefresh = () => {\n    // Only refresh data, don't clear filters or search\n    fetchVideos();\n  };\n\n  const handleClearAll = () => {\n    setSearchTerm(\"\");\n    setSelectedSubject(\"all\");\n    setSelectedClass(\"all\");\n    fetchVideos();\n  };\n\n  // Comment functions\n  const handleAddComment = () => {\n    if (newComment.trim()) {\n      const comment = {\n        id: Date.now(),\n        text: newComment,\n        author: user?.name || \"Anonymous\",\n        avatar: user?.name?.charAt(0)?.toUpperCase() || \"A\",\n        timestamp: new Date().toISOString(),\n        replies: [],\n        likes: 0,\n        liked: false\n      };\n      setComments([comment, ...comments]); // Add new comments at the top\n      setNewComment(\"\");\n    }\n  };\n\n  const handleAddReply = (commentId) => {\n    if (replyText.trim()) {\n      const reply = {\n        id: Date.now(),\n        text: replyText,\n        author: user?.name || \"Anonymous\",\n        avatar: user?.name?.charAt(0)?.toUpperCase() || \"A\",\n        timestamp: new Date().toISOString(),\n        likes: 0,\n        liked: false\n      };\n\n      setComments(comments.map(comment =>\n        comment.id === commentId\n          ? { ...comment, replies: [...comment.replies, reply] }\n          : comment\n      ));\n      setReplyText(\"\");\n      setReplyingTo(null);\n    }\n  };\n\n  const handleLikeComment = (commentId, isReply = false, parentId = null) => {\n    if (isReply) {\n      setComments(comments.map(comment =>\n        comment.id === parentId\n          ? {\n              ...comment,\n              replies: comment.replies.map(reply =>\n                reply.id === commentId\n                  ? { ...reply, liked: !reply.liked, likes: reply.liked ? reply.likes - 1 : reply.likes + 1 }\n                  : reply\n              )\n            }\n          : comment\n      ));\n    } else {\n      setComments(comments.map(comment =>\n        comment.id === commentId\n          ? { ...comment, liked: !comment.liked, likes: comment.liked ? comment.likes - 1 : comment.likes + 1 }\n          : comment\n      ));\n    }\n  };\n\n  const formatTimeAgo = (timestamp) => {\n    const now = new Date();\n    const time = new Date(timestamp);\n    const diffInSeconds = Math.floor((now - time) / 1000);\n\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;\n    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;\n    return time.toLocaleDateString();\n  };\n\n  return (\n    <div className=\"video-lessons-container\">\n      {/* Enhanced Header with Level Display */}\n      <div className=\"video-lessons-header\">\n        <div className=\"header-content\">\n          <div className=\"header-main\">\n            <div className=\"header-icon\">\n              <TbVideo />\n            </div>\n            <div className=\"header-text\">\n              <h1>Video Lessons</h1>\n              <p>Watch educational videos to enhance your learning</p>\n            </div>\n          </div>\n\n          {/* Level and Class Display */}\n          <div className=\"level-display\">\n            <div className=\"current-level\">\n              <span className=\"level-label\">Level:</span>\n              <span className=\"level-value\">{selectedLevel.charAt(0).toUpperCase() + selectedLevel.slice(1)}</span>\n            </div>\n            <div className=\"current-class\">\n              <span className=\"class-label\">Your Class:</span>\n              <span className=\"class-value\">\n                {user?.level === 'primary' ? `Class ${user?.class || 'N/A'}` :\n                 user?.level === 'secondary' ? `Form ${user?.class || 'N/A'}` :\n                 user?.level === 'advance' ? `Form ${user?.class || 'N/A'}` :\n                 'Not Set'}\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"video-lessons-content\">\n        {/* Enhanced Filters and Controls */}\n        <div className=\"video-controls\">\n          <div className=\"controls-row\">\n            {/* Class Filter */}\n            <div className=\"control-group\">\n              <label className=\"control-label\">\n                <TbFilter />\n                {isKiswahili ? 'Chuja kwa Darasa' : 'Filter by Class'}\n              </label>\n              <select\n                value={selectedClass}\n                onChange={(e) => setSelectedClass(e.target.value)}\n                className=\"control-select class-select\"\n              >\n                <option value=\"all\">All Classes</option>\n                {availableClasses.map((cls) => (\n                  <option key={cls} value={cls}>\n                    {selectedLevel === 'primary' ? `Class ${cls}` : `Form ${cls}`}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Subject Filter */}\n            <div className=\"control-group\">\n              <label className=\"control-label\">\n                <TbFilter />\n                Subject\n              </label>\n              <select\n                value={selectedSubject}\n                onChange={(e) => setSelectedSubject(e.target.value)}\n                className=\"control-select subject-select\"\n              >\n                <option value=\"all\">All Subjects</option>\n                {availableSubjects.map((subject) => (\n                  <option key={subject} value={subject}>\n                    {subject}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Sort */}\n            <div className=\"control-group\">\n              <label className=\"control-label\">\n                <TbSortAscending />\n                Sort\n              </label>\n              <select\n                value={sortBy}\n                onChange={(e) => setSortBy(e.target.value)}\n                className=\"control-select sort-select\"\n              >\n                <option value=\"newest\">Newest First</option>\n                <option value=\"oldest\">Oldest First</option>\n                <option value=\"title\">Title A-Z</option>\n                <option value=\"subject\">Subject A-Z</option>\n              </select>\n            </div>\n          </div>\n\n          {/* Search Row */}\n          <div className=\"search-row\">\n            <div className=\"search-container\">\n              <TbSearch className=\"search-icon\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search videos by title, subject, or topic...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"search-input\"\n              />\n              {searchTerm && (\n                <button onClick={handleClearSearch} className=\"clear-search-btn\">\n                  <TbX />\n                  Clear Search\n                </button>\n              )}\n            </div>\n\n            <button onClick={handleRefresh} className=\"refresh-btn\">\n              <TbDownload />\n              Refresh All\n            </button>\n          </div>\n        </div>\n\n        {/* Loading State */}\n        {loading ? (\n          <div className=\"loading-state\">\n            <div className=\"loading-spinner\"></div>\n            <p>Loading videos...</p>\n          </div>\n        ) : error ? (\n          <div className=\"error-state\">\n            <TbAlertTriangle className=\"error-icon\" />\n            <h3>Error Loading Videos</h3>\n            <p>{error}</p>\n            <button onClick={fetchVideos} className=\"retry-btn\">\n              Try Again\n            </button>\n          </div>\n        ) : filteredAndSortedVideos.length > 0 ? (\n          <div className=\"videos-grid\">\n            {filteredAndSortedVideos.map((video, index) => (\n              <div key={index} className=\"video-card\" onClick={() => handleShowVideo(index)}>\n                <div className=\"video-card-thumbnail\">\n                  <img\n                    src={getThumbnailUrl(video)}\n                    alt={video.title}\n                    className=\"thumbnail-image\"\n                    onError={(e) => {\n                      // Fallback logic for failed thumbnails\n                      if (video.videoID && !video.videoID.includes('amazonaws.com')) {\n                        // For YouTube videos, try different quality thumbnails\n                        let videoId = video.videoID;\n                        if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n                          const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                          videoId = match ? match[1] : videoId;\n                        }\n\n                        const fallbacks = [\n                          `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,\n                          `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`,\n                          `https://img.youtube.com/vi/${videoId}/default.jpg`,\n                          '/api/placeholder/320/180'\n                        ];\n\n                        const currentSrc = e.target.src;\n                        const currentIndex = fallbacks.findIndex(url => currentSrc.includes(url.split('/').pop()));\n\n                        if (currentIndex < fallbacks.length - 1) {\n                          e.target.src = fallbacks[currentIndex + 1];\n                        }\n                      } else {\n                        e.target.src = '/api/placeholder/320/180';\n                      }\n                    }}\n                  />\n                  <div className=\"play-overlay\">\n                    <FaPlayCircle className=\"play-icon\" />\n                  </div>\n                  <div className=\"video-duration\">\n                    {video.duration || \"Video\"}\n                  </div>\n                  {video.subtitles && video.subtitles.length > 0 && (\n                    <div className=\"subtitle-badge\">\n                      <TbInfoCircle />\n                      CC\n                    </div>\n                  )}\n                </div>\n\n                <div className=\"video-card-content\">\n                  <h3 className=\"video-title\">{video.title}</h3>\n                  <div className=\"video-meta\">\n                    <span className=\"video-subject\">{video.subject}</span>\n                    <span className=\"video-class\">\n                      {selectedLevel === 'primary' ? `Class ${video.className || video.class}` : `Form ${video.className || video.class}`}\n                    </span>\n                  </div>\n                  <div className=\"video-tags\">\n                    {video.topic && <span className=\"topic-tag\">{video.topic}</span>}\n                    {video.sharedFromClass && video.sharedFromClass !== (video.className || video.class) && (\n                      <span className=\"shared-tag\">\n                        Shared from {selectedLevel === 'primary' ? `Class ${video.sharedFromClass}` : `Form ${video.sharedFromClass}`}\n                      </span>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"empty-state\">\n            <FaGraduationCap className=\"empty-icon\" />\n            <h3>No Videos Found</h3>\n            <p>No video lessons are available for your current selection.</p>\n            <p className=\"suggestion\">Try selecting a different class or subject.</p>\n          </div>\n        )}\n      </div>\n\n      {/* Enhanced Video Display */}\n      {showVideoIndices.length > 0 && currentVideoIndex !== null && (\n        <div className={`video-overlay ${isVideoExpanded ? 'expanded' : ''}`} onClick={(e) => {\n          if (e.target === e.currentTarget) handleHideVideo();\n        }}>\n          <div className={`video-modal ${isVideoExpanded ? 'expanded' : ''}`}>\n            {(() => {\n              const video = filteredAndSortedVideos[currentVideoIndex];\n              if (!video) return <div>Video not found</div>;\n\n              return (\n                <div className=\"video-content\">\n                  <div className=\"video-header\">\n                    <div className=\"video-info\">\n                      <h3 className=\"video-title\">{video.title}</h3>\n                      <div className=\"video-meta\">\n                        <span className=\"video-subject\">{video.subject}</span>\n                        <span className=\"video-class\">Class {video.className}</span>\n                        {video.level && <span className=\"video-level\">{video.level}</span>}\n                      </div>\n                    </div>\n                    <div className=\"video-controls\">\n                      <button\n                        className=\"control-btn add-comment-btn\"\n                        onClick={() => {\n                          setCommentsExpanded(!commentsExpanded);\n                          if (!commentsExpanded && !isVideoExpanded) {\n                            toggleVideoExpansion();\n                          }\n                        }}\n                        title=\"Add Comment\"\n                      >\n                        <span className=\"btn-icon\">💬</span>\n                        <span className=\"btn-text\">Comment</span>\n                      </button>\n                      {(isVideoExpanded && commentsExpanded) && (\n                        <button\n                          className=\"control-btn close-comment-btn\"\n                          onClick={() => {\n                            setCommentsExpanded(false);\n                            toggleVideoExpansion();\n                          }}\n                          title=\"Close Comments\"\n                        >\n                          <span className=\"btn-icon\">✕</span>\n                          <span className=\"btn-text\">Close</span>\n                        </button>\n                      )}\n                      <button\n                        className=\"control-btn close-btn\"\n                        onClick={handleHideVideo}\n                        title=\"Close video\"\n                      >\n                        <FaTimes />\n                      </button>\n                    </div>\n                  </div>\n\n                  {/* Video and Comments Layout */}\n                  <div className={`video-main-layout ${isVideoExpanded ? 'expanded-layout' : 'normal-layout'}`}>\n                    {/* Video Container */}\n                    <div className=\"video-container\">\n                    {video.videoUrl ? (\n                      <div style={{ padding: '15px', background: '#000', borderRadius: '8px' }}>\n                          <video\n                            ref={(ref) => setVideoRef(ref)}\n                            controls\n                            autoPlay\n                            playsInline\n                            preload=\"metadata\"\n                            width=\"100%\"\n                            height=\"400\"\n                            poster={getThumbnailUrl(video)}\n                            style={{\n                              width: '100%',\n                              height: '400px',\n                              backgroundColor: '#000'\n                            }}\n                            onError={(e) => {\n                              setVideoError(`Failed to load video: ${video.title}. Please try refreshing the page.`);\n                            }}\n                            onCanPlay={() => {\n                              setVideoError(null);\n                            }}\n                            onLoadStart={() => {\n                              console.log('🎬 Video loading started');\n                            }}\n                            crossOrigin=\"anonymous\"\n                          >\n                            {/* Use signed URL if available, otherwise use original URL */}\n                            <source src={video.signedVideoUrl || video.videoUrl} type=\"video/mp4\" />\n\n                            {/* Add subtitle tracks if available */}\n                            {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => (\n                              <track\n                                key={`${subtitle.language}-${index}`}\n                                kind=\"subtitles\"\n                                src={subtitle.url}\n                                srcLang={subtitle.language}\n                                label={subtitle.languageName}\n                                default={subtitle.isDefault || index === 0}\n                              />\n                            ))}\n\n                            Your browser does not support the video tag.\n                          </video>\n\n                          {/* Subtitle indicator */}\n                          {video.subtitles && video.subtitles.length > 0 && (\n                            <div className=\"subtitle-indicator\">\n                              <TbInfoCircle className=\"subtitle-icon\" />\n                              <span>Subtitles available in {video.subtitles.length} language(s)</span>\n                            </div>\n                          )}\n\n                          {/* Video error display */}\n                          {videoError && (\n                            <div className=\"video-error-overlay\">\n                              <div className=\"error-content\">\n                                <TbAlertTriangle className=\"error-icon\" />\n                                <p>{videoError}</p>\n                                <button onClick={() => setVideoError(null)} className=\"dismiss-error-btn\">\n                                  Dismiss\n                                </button>\n                              </div>\n                            </div>\n                          )}\n                        </div>\n                    ) : video.videoID ? (\n                      // Fallback to YouTube embed if no videoUrl\n                      <iframe\n                        src={`https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`}\n                        title={video.title}\n                        frameBorder=\"0\"\n                        allowFullScreen\n                        className=\"video-iframe\"\n                        onLoad={() => console.log('✅ YouTube iframe loaded')}\n                      ></iframe>\n                    ) : (\n                      <div className=\"video-error\">\n                        <div className=\"error-icon\">⚠️</div>\n                        <h3>Video Unavailable</h3>\n                        <p>{videoError || \"This video cannot be played at the moment.\"}</p>\n                        <div className=\"error-actions\">\n                          <a\n                            href={video.signedVideoUrl || video.videoUrl}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"external-link-btn\"\n                          >\n                            📱 Open in New Tab\n                          </a>\n                        </div>\n                      </div>\n                    )}\n                    </div>\n\n                    {/* Comments Section - Always visible */}\n                    <div className={`comments-section-below ${isVideoExpanded ? 'expanded-comments' : 'normal-comments'}`}>\n                      {/* Comments Count - Always visible at top */}\n                      <div className=\"comments-count-header\">\n                        <div className=\"comments-count-display\">\n                          <TbInfoCircle />\n                          <span>{comments.length} {comments.length === 1 ? 'comment' : 'comments'}</span>\n                        </div>\n                        {!isVideoExpanded || !commentsExpanded ? (\n                          <button\n                            onClick={() => {\n                              setCommentsExpanded(true);\n                              if (!isVideoExpanded) {\n                                toggleVideoExpansion();\n                              }\n                            }}\n                            className=\"view-comments-btn\"\n                          >\n                            <span className=\"btn-icon\">👁️</span>\n                            <span className=\"btn-text\">View</span>\n                          </button>\n                        ) : (\n                          <button\n                            onClick={() => setCommentsExpanded(false)}\n                            className=\"comments-toggle-btn\"\n                          >\n                            ▼ Minimize\n                          </button>\n                        )}\n                      </div>\n\n                      {/* Comments Content - Show when expanded */}\n                      {(isVideoExpanded && commentsExpanded) && (\n                        <div className=\"comments-content maximized\">\n                            {/* Add Comment */}\n                            <div className=\"add-comment\">\n                              <div className=\"comment-input-container\">\n                                <div className=\"user-avatar\">\n                                  {user?.name?.charAt(0)?.toUpperCase() || \"A\"}\n                                </div>\n                                <div className=\"comment-input-wrapper\">\n                                  <textarea\n                                    value={newComment}\n                                    onChange={(e) => setNewComment(e.target.value)}\n                                    placeholder=\"Share your thoughts about this video...\"\n                                    className=\"comment-input\"\n                                    rows=\"3\"\n                                    autoFocus\n                                  />\n                                  <button\n                                    onClick={handleAddComment}\n                                    className=\"comment-submit-btn\"\n                                    disabled={!newComment.trim()}\n                                  >\n                                    <span>💬</span> Post Comment\n                                  </button>\n                                </div>\n                              </div>\n                            </div>\n\n                      {/* Comments List */}\n                      <div className=\"comments-list\">\n                        {comments.length === 0 ? (\n                          <div className=\"no-comments\">\n                            <div className=\"no-comments-icon\">💬</div>\n                            <p>No comments yet. Be the first to share your thoughts!</p>\n                          </div>\n                        ) : (\n                          comments.map((comment) => (\n                            <div key={comment.id} className=\"comment\">\n                              <div className=\"comment-main\">\n                                <div className=\"comment-avatar\">\n                                  {comment.avatar || comment.author?.charAt(0)?.toUpperCase() || \"A\"}\n                                </div>\n                                <div className=\"comment-content\">\n                                  <div className=\"comment-header\">\n                                    <span className=\"comment-author\">{comment.author}</span>\n                                    <span className=\"comment-time\">\n                                      {formatTimeAgo(comment.timestamp)}\n                                    </span>\n                                  </div>\n                                  <div className=\"comment-text\">{comment.text}</div>\n                                  <div className=\"comment-actions\">\n                                    <button\n                                      onClick={() => handleLikeComment(comment.id)}\n                                      className={`like-btn ${comment.liked ? 'liked' : ''}`}\n                                    >\n                                      <span>{comment.liked ? '❤️' : '🤍'}</span>\n                                      {comment.likes > 0 && <span className=\"like-count\">{comment.likes}</span>}\n                                    </button>\n                                    <button\n                                      onClick={() => setReplyingTo(replyingTo === comment.id ? null : comment.id)}\n                                      className=\"reply-btn\"\n                                    >\n                                      <span>💬</span> Reply\n                                    </button>\n                                    {comment.replies.length > 0 && (\n                                      <span className=\"replies-count\">\n                                        {comment.replies.length} {comment.replies.length === 1 ? 'reply' : 'replies'}\n                                      </span>\n                                    )}\n                                  </div>\n                                </div>\n                              </div>\n\n                              {/* Reply Input */}\n                              {replyingTo === comment.id && (\n                                <div className=\"reply-input-container\">\n                                  <div className=\"reply-input-wrapper\">\n                                    <div className=\"user-avatar small\">\n                                      {user?.name?.charAt(0)?.toUpperCase() || \"A\"}\n                                    </div>\n                                    <div className=\"reply-input-content\">\n                                      <textarea\n                                        value={replyText}\n                                        onChange={(e) => setReplyText(e.target.value)}\n                                        placeholder={`Reply to ${comment.author}...`}\n                                        className=\"reply-input\"\n                                        rows=\"2\"\n                                        autoFocus\n                                      />\n                                      <div className=\"reply-actions\">\n                                        <button\n                                          onClick={() => handleAddReply(comment.id)}\n                                          className=\"reply-submit-btn\"\n                                          disabled={!replyText.trim()}\n                                        >\n                                          <span>💬</span> Reply\n                                        </button>\n                                        <button\n                                          onClick={() => {\n                                            setReplyingTo(null);\n                                            setReplyText(\"\");\n                                          }}\n                                          className=\"reply-cancel-btn\"\n                                        >\n                                          Cancel\n                                        </button>\n                                      </div>\n                                    </div>\n                                  </div>\n                                </div>\n                              )}\n\n                              {/* Replies */}\n                              {comment.replies.length > 0 && (\n                                <div className=\"replies\">\n                                  {comment.replies.map((reply) => (\n                                    <div key={reply.id} className=\"reply\">\n                                      <div className=\"reply-main\">\n                                        <div className=\"reply-avatar\">\n                                          {reply.avatar || reply.author?.charAt(0)?.toUpperCase() || \"A\"}\n                                        </div>\n                                        <div className=\"reply-content\">\n                                          <div className=\"reply-header\">\n                                            <span className=\"reply-author\">{reply.author}</span>\n                                            <span className=\"reply-time\">\n                                              {formatTimeAgo(reply.timestamp)}\n                                            </span>\n                                          </div>\n                                          <div className=\"reply-text\">{reply.text}</div>\n                                          <div className=\"reply-actions\">\n                                            <button\n                                              onClick={() => handleLikeComment(reply.id, true, comment.id)}\n                                              className={`like-btn small ${reply.liked ? 'liked' : ''}`}\n                                            >\n                                              <span>{reply.liked ? '❤️' : '🤍'}</span>\n                                              {reply.likes > 0 && <span className=\"like-count\">{reply.likes}</span>}\n                                            </button>\n                                          </div>\n                                        </div>\n                                      </div>\n                                    </div>\n                                  ))}\n                                </div>\n                              )}\n                            </div>\n                          ))\n                        )}\n                      </div>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                  {/* End of video-main-layout */}\n                </div>\n              );\n            })()}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport default VideoLessons;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACxE,OAAO,aAAa;AACpB,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,eAAe,EAAEC,wBAAwB,EAAEC,iBAAiB,EAAEC,eAAe,QAAQ,4BAA4B;AAC1H,SAASC,WAAW,QAAQ,mCAAmC;;AAE/D;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,cAAc,GAAG;EACrBC,YAAY,EAAEA,CAAA,kBAAMF,OAAA;IAAMG,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAC9DC,eAAe,EAAEA,CAAA,kBAAMV,OAAA;IAAMG,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACjEE,OAAO,EAAEA,CAAA,kBAAMX,OAAA;IAAMG,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACxDG,QAAQ,EAAEA,CAAA,kBAAMZ,OAAA;IAAMG,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACzDI,UAAU,EAAEA,CAAA,kBAAMb,OAAA;IAAMG,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAC3DK,OAAO,EAAEA,CAAA,kBAAMd,OAAA;IAAMG,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACzDM,QAAQ,EAAEA,CAAA,kBAAMf,OAAA;IAAMG,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAC1DO,eAAe,EAAEA,CAAA,kBAAMhB,OAAA;IAAMG,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAChEQ,QAAQ,EAAEA,CAAA,kBAAMjB,OAAA;IAAMG,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAC1DS,GAAG,EAAEA,CAAA,kBAAMlB,OAAA;IAAMG,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACpDU,UAAU,EAAEA,CAAA,kBAAMnB,OAAA;IAAMG,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EAC3DW,eAAe,EAAEA,CAAA,kBAAMpB,OAAA;IAAMG,KAAK,EAAE;MAACC,QAAQ,EAAE,MAAM;MAAEiB,KAAK,EAAE;IAAS,CAAE;IAAAhB,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;EACnFa,YAAY,EAAEA,CAAA,kBAAMtB,OAAA;IAAMG,KAAK,EAAE;MAACC,QAAQ,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAC;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM;AAC/D,CAAC;;AAED;AACA,MAAM;EACJP,YAAY;EACZQ,eAAe;EACfC,OAAO;EACPC,QAAQ;EACRC,UAAU;EACVC,OAAO;EACPC,QAAQ;EACRC,eAAe;EACfC,QAAQ;EACRC,GAAG;EACHC,UAAU;EACVC,eAAe;EACfE;AACF,CAAC,GAAGrB,cAAc;AAElB,SAASsB,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAM;IAAEC;EAAK,CAAC,GAAGlC,WAAW,CAAEmC,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE,CAAC;IAAEC,WAAW;IAAEC,YAAY;IAAEC;EAAe,CAAC,GAAGhC,WAAW,CAAC,CAAC;EACtE,MAAMiC,QAAQ,GAAGzC,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAAC0C,MAAM,EAAEC,SAAS,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACmD,OAAO,EAAEC,UAAU,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqD,KAAK,EAAEC,QAAQ,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACuD,aAAa,EAAEC,gBAAgB,CAAC,GAAGxD,QAAQ,CAAC,CAAA0C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,KAAK,KAAI,SAAS,CAAC;EAC5E,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG3D,QAAQ,CAAC,CAAA0C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,KAAK,KAAI,KAAK,CAAC;EACxE,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC+D,UAAU,EAAEC,aAAa,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiE,MAAM,EAAEC,SAAS,CAAC,GAAGlE,QAAQ,CAAC,QAAQ,CAAC;;EAE9C;EACA,MAAM,CAACmE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpE,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACqE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtE,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACuE,eAAe,EAAEC,kBAAkB,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACyE,UAAU,EAAEC,aAAa,CAAC,GAAG1E,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC2E,QAAQ,EAAEC,WAAW,CAAC,GAAG5E,QAAQ,CAAC,IAAI,CAAC;;EAE9C;EACA,MAAM,CAAC6E,QAAQ,EAAEC,WAAW,CAAC,GAAG9E,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC+E,UAAU,EAAEC,aAAa,CAAC,GAAGhF,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiF,UAAU,EAAEC,aAAa,CAAC,GAAGlF,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACmF,SAAS,EAAEC,YAAY,CAAC,GAAGpF,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACqF,YAAY,EAAEC,eAAe,CAAC,GAAGtF,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACuF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxF,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAMyF,gBAAgB,GAAGtF,OAAO,CAAC,MAAM;IACrC,IAAIoD,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,mBAAmB,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IACpH,IAAIA,aAAa,KAAK,WAAW,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAC9D,IAAIA,aAAa,KAAK,SAAS,EAAE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;IAClD,OAAO,EAAE;EACX,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAMmC,iBAAiB,GAAGvF,OAAO,CAAC,MAAM;IACtC,IAAIoD,aAAa,KAAK,SAAS,EAAE,OAAO5C,eAAe;IACvD,IAAI4C,aAAa,KAAK,mBAAmB,EAAE,OAAO3C,wBAAwB;IAC1E,IAAI2C,aAAa,KAAK,WAAW,EAAE,OAAO1C,iBAAiB;IAC3D,IAAI0C,aAAa,KAAK,SAAS,EAAE,OAAOzC,eAAe;IACvD,OAAO,EAAE;EACX,CAAC,EAAE,CAACyC,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAMoC,WAAW,GAAGzF,WAAW,CAAC,YAAY;IAC1C,IAAI;MAAA,IAAA0F,cAAA;MACFxC,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MACdN,QAAQ,CAACtC,WAAW,CAAC,CAAC,CAAC;MAEvB,MAAMmF,OAAO,GAAG;QACdpC,KAAK,EAAEF,aAAa;QACpBuC,SAAS,EAAE,KAAK;QAAE;QAClBC,OAAO,EAAE,KAAK;QAAE;QAChBC,OAAO,EAAE;MACX,CAAC;MAED,MAAMC,QAAQ,GAAG,MAAM3F,gBAAgB,CAACuF,OAAO,CAAC;MAEhD,IAAII,QAAQ,aAARA,QAAQ,gBAAAL,cAAA,GAARK,QAAQ,CAAEC,IAAI,cAAAN,cAAA,eAAdA,cAAA,CAAgBO,OAAO,EAAE;QAC3BjD,SAAS,CAAC+C,QAAQ,CAACC,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;MACrC,CAAC,MAAM;QAAA,IAAAE,eAAA;QACL9C,QAAQ,CAAC,CAAA2C,QAAQ,aAARA,QAAQ,wBAAAG,eAAA,GAARH,QAAQ,CAAEC,IAAI,cAAAE,eAAA,uBAAdA,eAAA,CAAgBC,OAAO,KAAI,wBAAwB,CAAC;QAC7DnD,SAAS,CAAC,EAAE,CAAC;MACf;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdiD,OAAO,CAACjD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDC,QAAQ,CAAC,0CAA0C,CAAC;MACpDJ,SAAS,CAAC,EAAE,CAAC;IACf,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;MACjBJ,QAAQ,CAACvC,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAAC8C,aAAa,EAAEP,QAAQ,CAAC,CAAC;;EAE7B;EACA,MAAMuD,uBAAuB,GAAGpG,OAAO,CAAC,MAAM;IAG5C,IAAIqG,QAAQ,GAAGvD,MAAM;;IAErB;IACAuD,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACjD,KAAK,KAAKF,aAAa,CAAC;;IAElE;IACA,IAAIG,aAAa,KAAK,KAAK,EAAE;MAC3B8C,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,KAAK,IAAI;QAClC;QACA,MAAMC,UAAU,GAAGD,KAAK,CAACZ,SAAS,IAAIY,KAAK,CAAC9C,KAAK;QACjD,OAAO+C,UAAU,KAAKjD,aAAa;MACrC,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIG,eAAe,KAAK,KAAK,EAAE;MAC7B2C,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACX,OAAO,KAAKlC,eAAe,CAAC;IACxE;;IAEA;IACA,IAAIE,UAAU,CAAC6C,IAAI,CAAC,CAAC,EAAE;MACrB,MAAMC,WAAW,GAAG9C,UAAU,CAAC+C,WAAW,CAAC,CAAC;MAC5CN,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,KAAK;QAAA,IAAAK,YAAA,EAAAC,cAAA,EAAAC,YAAA;QAAA,OAC9B,EAAAF,YAAA,GAAAL,KAAK,CAACQ,KAAK,cAAAH,YAAA,uBAAXA,YAAA,CAAaD,WAAW,CAAC,CAAC,CAACK,QAAQ,CAACN,WAAW,CAAC,OAAAG,cAAA,GAChDN,KAAK,CAACX,OAAO,cAAAiB,cAAA,uBAAbA,cAAA,CAAeF,WAAW,CAAC,CAAC,CAACK,QAAQ,CAACN,WAAW,CAAC,OAAAI,YAAA,GAClDP,KAAK,CAACU,KAAK,cAAAH,YAAA,uBAAXA,YAAA,CAAaH,WAAW,CAAC,CAAC,CAACK,QAAQ,CAACN,WAAW,CAAC;MAAA,CAClD,CAAC;IACH;;IAEA;IACA,MAAMQ,MAAM,GAAG,CAAC,GAAGb,QAAQ,CAAC,CAACc,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC1C,QAAQvD,MAAM;QACZ,KAAK,QAAQ;UACX,OAAO,IAAIwD,IAAI,CAACD,CAAC,CAACE,SAAS,IAAI,CAAC,CAAC,GAAG,IAAID,IAAI,CAACF,CAAC,CAACG,SAAS,IAAI,CAAC,CAAC;QAChE,KAAK,QAAQ;UACX,OAAO,IAAID,IAAI,CAACF,CAAC,CAACG,SAAS,IAAI,CAAC,CAAC,GAAG,IAAID,IAAI,CAACD,CAAC,CAACE,SAAS,IAAI,CAAC,CAAC;QAChE,KAAK,OAAO;UACV,OAAO,CAACH,CAAC,CAACL,KAAK,IAAI,EAAE,EAAES,aAAa,CAACH,CAAC,CAACN,KAAK,IAAI,EAAE,CAAC;QACrD,KAAK,SAAS;UACZ,OAAO,CAACK,CAAC,CAACxB,OAAO,IAAI,EAAE,EAAE4B,aAAa,CAACH,CAAC,CAACzB,OAAO,IAAI,EAAE,CAAC;QACzD;UACE,OAAO,CAAC;MACZ;IACF,CAAC,CAAC;IAEFO,OAAO,CAACsB,GAAG,CAAC,0BAA0B,EAAEP,MAAM,CAACQ,MAAM,CAAC;IACtD,IAAIR,MAAM,CAACQ,MAAM,GAAG,CAAC,EAAE;MACrBvB,OAAO,CAACsB,GAAG,CAAC,2BAA2B,EAAEP,MAAM,CAAC,CAAC,CAAC,CAAC;IACrD;IAEA,OAAOA,MAAM;EACf,CAAC,EAAE,CAACpE,MAAM,EAAEc,UAAU,EAAEE,MAAM,EAAEV,aAAa,EAAEG,aAAa,EAAEG,eAAe,CAAC,CAAC;;EAE/E;EACA,MAAMiE,eAAe,GAAG,MAAOC,KAAK,IAAK;IACvC,MAAMrB,KAAK,GAAGH,uBAAuB,CAACwB,KAAK,CAAC;IAE5C3D,oBAAoB,CAAC2D,KAAK,CAAC;IAC3BzD,mBAAmB,CAAC,CAACyD,KAAK,CAAC,CAAC;IAC5BvD,kBAAkB,CAAC,KAAK,CAAC;IACzBE,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACA,IAAIgC,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEsB,QAAQ,KAAKtB,KAAK,CAACsB,QAAQ,CAACb,QAAQ,CAAC,eAAe,CAAC,IAAIT,KAAK,CAACsB,QAAQ,CAACb,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MACnG,IAAI;QACF,MAAMc,SAAS,GAAG,MAAMC,iBAAiB,CAACxB,KAAK,CAACsB,QAAQ,CAAC;QACzDtB,KAAK,CAACyB,cAAc,GAAGF,SAAS;MAClC,CAAC,CAAC,OAAO5E,KAAK,EAAE;QACdiD,OAAO,CAAC8B,IAAI,CAAC,8CAA8C,CAAC;QAC5D1B,KAAK,CAACyB,cAAc,GAAGzB,KAAK,CAACsB,QAAQ;MACvC;IACF;EACF,CAAC;EAED,MAAMK,eAAe,GAAGA,CAAA,KAAM;IAC5B/D,mBAAmB,CAAC,EAAE,CAAC;IACvBF,oBAAoB,CAAC,IAAI,CAAC;IAC1BI,kBAAkB,CAAC,KAAK,CAAC;IACzBE,aAAa,CAAC,IAAI,CAAC;IACnB,IAAIC,QAAQ,EAAE;MACZA,QAAQ,CAAC2D,KAAK,CAAC,CAAC;IAClB;EACF,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC/D,kBAAkB,CAAC,CAACD,eAAe,CAAC;EACtC,CAAC;;EAED;EACA,MAAM2D,iBAAiB,GAAG,MAAOF,QAAQ,IAAK;IAC5C,IAAI,CAACA,QAAQ,EAAE,OAAOA,QAAQ;;IAE9B;IACA,IAAIA,QAAQ,CAACb,QAAQ,CAAC,eAAe,CAAC,IAAIa,QAAQ,CAACb,QAAQ,CAAC,KAAK,CAAC,EAAE;MAClE,IAAI;QACF,MAAMlB,QAAQ,GAAG,MAAMuC,KAAK,CAAE,6DAA4DC,kBAAkB,CAACT,QAAQ,CAAE,EAAC,EAAE;UACxHU,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAI,CAAC1C,QAAQ,CAAC2C,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAE,uBAAsB5C,QAAQ,CAAC6C,MAAO,EAAC,CAAC;QAC3D;QAEA,MAAM5C,IAAI,GAAG,MAAMD,QAAQ,CAAC8C,IAAI,CAAC,CAAC;QAElC,IAAI7C,IAAI,CAACC,OAAO,IAAID,IAAI,CAAC+B,SAAS,EAAE;UAClC3B,OAAO,CAACsB,GAAG,CAAC,+BAA+B,CAAC;UAC5C,OAAO1B,IAAI,CAAC+B,SAAS;QACvB,CAAC,MAAM;UACL3B,OAAO,CAAC8B,IAAI,CAAC,+CAA+C,EAAElC,IAAI,CAAC;UACnE,OAAO8B,QAAQ;QACjB;MACF,CAAC,CAAC,OAAO3E,KAAK,EAAE;QACdiD,OAAO,CAACjD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,OAAO2E,QAAQ;MACjB;IACF;IAEA,OAAOA,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMgB,eAAe,GAAItC,KAAK,IAAK;IACjC,IAAIA,KAAK,CAACuC,SAAS,EAAE;MACnB,OAAOvC,KAAK,CAACuC,SAAS;IACxB;IAEA,IAAIvC,KAAK,CAACwC,OAAO,IAAI,CAACxC,KAAK,CAACwC,OAAO,CAAC/B,QAAQ,CAAC,eAAe,CAAC,EAAE;MAC7D,IAAIgC,OAAO,GAAGzC,KAAK,CAACwC,OAAO;MAC3B,IAAIC,OAAO,CAAChC,QAAQ,CAAC,aAAa,CAAC,IAAIgC,OAAO,CAAChC,QAAQ,CAAC,UAAU,CAAC,EAAE;QACnE,MAAMiC,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;QACjFD,OAAO,GAAGC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,OAAO;MACtC;MACA,OAAQ,8BAA6BA,OAAQ,oBAAmB;IAClE;IAEA,OAAO,0BAA0B;EACnC,CAAC;;EAED;EACAlJ,SAAS,CAAC,MAAM;IACd0F,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;EAEjB1F,SAAS,CAAC,MAAM;IACd,IAAIyC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEe,KAAK,EAAE;MACfD,gBAAgB,CAACd,IAAI,CAACe,KAAK,CAAC;IAC9B;IACA,IAAIf,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEkB,KAAK,EAAE;MACfD,gBAAgB,CAACjB,IAAI,CAACkB,KAAK,CAAC;IAC9B;EACF,CAAC,EAAE,CAAClB,IAAI,CAAC,CAAC;;EAEV;EACA,MAAM2G,iBAAiB,GAAGA,CAAA,KAAM;IAC9BrF,aAAa,CAAC,EAAE,CAAC;EACnB,CAAC;EAED,MAAMsF,aAAa,GAAGA,CAAA,KAAM;IAC1B;IACA3D,WAAW,CAAC,CAAC;EACf,CAAC;EAED,MAAM4D,cAAc,GAAGA,CAAA,KAAM;IAC3BvF,aAAa,CAAC,EAAE,CAAC;IACjBF,kBAAkB,CAAC,KAAK,CAAC;IACzBH,gBAAgB,CAAC,KAAK,CAAC;IACvBgC,WAAW,CAAC,CAAC;EACf,CAAC;;EAED;EACA,MAAM6D,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIzE,UAAU,CAAC6B,IAAI,CAAC,CAAC,EAAE;MAAA,IAAA6C,UAAA,EAAAC,iBAAA;MACrB,MAAMC,OAAO,GAAG;QACdC,EAAE,EAAEnC,IAAI,CAACoC,GAAG,CAAC,CAAC;QACdC,IAAI,EAAE/E,UAAU;QAChBgF,MAAM,EAAE,CAAArH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsH,IAAI,KAAI,WAAW;QACjCC,MAAM,EAAE,CAAAvH,IAAI,aAAJA,IAAI,wBAAA+G,UAAA,GAAJ/G,IAAI,CAAEsH,IAAI,cAAAP,UAAA,wBAAAC,iBAAA,GAAVD,UAAA,CAAYS,MAAM,CAAC,CAAC,CAAC,cAAAR,iBAAA,uBAArBA,iBAAA,CAAuBS,WAAW,CAAC,CAAC,KAAI,GAAG;QACnDC,SAAS,EAAE,IAAI3C,IAAI,CAAC,CAAC,CAAC4C,WAAW,CAAC,CAAC;QACnCC,OAAO,EAAE,EAAE;QACXC,KAAK,EAAE,CAAC;QACRC,KAAK,EAAE;MACT,CAAC;MACD1F,WAAW,CAAC,CAAC6E,OAAO,EAAE,GAAG9E,QAAQ,CAAC,CAAC,CAAC,CAAC;MACrCG,aAAa,CAAC,EAAE,CAAC;IACnB;EACF,CAAC;EAED,MAAMyF,cAAc,GAAIC,SAAS,IAAK;IACpC,IAAIvF,SAAS,CAACyB,IAAI,CAAC,CAAC,EAAE;MAAA,IAAA+D,WAAA,EAAAC,kBAAA;MACpB,MAAMC,KAAK,GAAG;QACZjB,EAAE,EAAEnC,IAAI,CAACoC,GAAG,CAAC,CAAC;QACdC,IAAI,EAAE3E,SAAS;QACf4E,MAAM,EAAE,CAAArH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsH,IAAI,KAAI,WAAW;QACjCC,MAAM,EAAE,CAAAvH,IAAI,aAAJA,IAAI,wBAAAiI,WAAA,GAAJjI,IAAI,CAAEsH,IAAI,cAAAW,WAAA,wBAAAC,kBAAA,GAAVD,WAAA,CAAYT,MAAM,CAAC,CAAC,CAAC,cAAAU,kBAAA,uBAArBA,kBAAA,CAAuBT,WAAW,CAAC,CAAC,KAAI,GAAG;QACnDC,SAAS,EAAE,IAAI3C,IAAI,CAAC,CAAC,CAAC4C,WAAW,CAAC,CAAC;QACnCE,KAAK,EAAE,CAAC;QACRC,KAAK,EAAE;MACT,CAAC;MAED1F,WAAW,CAACD,QAAQ,CAACiG,GAAG,CAACnB,OAAO,IAC9BA,OAAO,CAACC,EAAE,KAAKc,SAAS,GACpB;QAAE,GAAGf,OAAO;QAAEW,OAAO,EAAE,CAAC,GAAGX,OAAO,CAACW,OAAO,EAAEO,KAAK;MAAE,CAAC,GACpDlB,OACN,CAAC,CAAC;MACFvE,YAAY,CAAC,EAAE,CAAC;MAChBF,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;EAED,MAAM6F,iBAAiB,GAAGA,CAACL,SAAS,EAAEM,OAAO,GAAG,KAAK,EAAEC,QAAQ,GAAG,IAAI,KAAK;IACzE,IAAID,OAAO,EAAE;MACXlG,WAAW,CAACD,QAAQ,CAACiG,GAAG,CAACnB,OAAO,IAC9BA,OAAO,CAACC,EAAE,KAAKqB,QAAQ,GACnB;QACE,GAAGtB,OAAO;QACVW,OAAO,EAAEX,OAAO,CAACW,OAAO,CAACQ,GAAG,CAACD,KAAK,IAChCA,KAAK,CAACjB,EAAE,KAAKc,SAAS,GAClB;UAAE,GAAGG,KAAK;UAAEL,KAAK,EAAE,CAACK,KAAK,CAACL,KAAK;UAAED,KAAK,EAAEM,KAAK,CAACL,KAAK,GAAGK,KAAK,CAACN,KAAK,GAAG,CAAC,GAAGM,KAAK,CAACN,KAAK,GAAG;QAAE,CAAC,GACzFM,KACN;MACF,CAAC,GACDlB,OACN,CAAC,CAAC;IACJ,CAAC,MAAM;MACL7E,WAAW,CAACD,QAAQ,CAACiG,GAAG,CAACnB,OAAO,IAC9BA,OAAO,CAACC,EAAE,KAAKc,SAAS,GACpB;QAAE,GAAGf,OAAO;QAAEa,KAAK,EAAE,CAACb,OAAO,CAACa,KAAK;QAAED,KAAK,EAAEZ,OAAO,CAACa,KAAK,GAAGb,OAAO,CAACY,KAAK,GAAG,CAAC,GAAGZ,OAAO,CAACY,KAAK,GAAG;MAAE,CAAC,GACnGZ,OACN,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMuB,aAAa,GAAId,SAAS,IAAK;IACnC,MAAMP,GAAG,GAAG,IAAIpC,IAAI,CAAC,CAAC;IACtB,MAAM0D,IAAI,GAAG,IAAI1D,IAAI,CAAC2C,SAAS,CAAC;IAChC,MAAMgB,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACzB,GAAG,GAAGsB,IAAI,IAAI,IAAI,CAAC;IAErD,IAAIC,aAAa,GAAG,EAAE,EAAE,OAAO,UAAU;IACzC,IAAIA,aAAa,GAAG,IAAI,EAAE,OAAQ,GAAEC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAE,OAAM;IACzE,IAAIA,aAAa,GAAG,KAAK,EAAE,OAAQ,GAAEC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,IAAI,CAAE,OAAM;IAC5E,IAAIA,aAAa,GAAG,MAAM,EAAE,OAAQ,GAAEC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,KAAK,CAAE,OAAM;IAC9E,OAAOD,IAAI,CAACI,kBAAkB,CAAC,CAAC;EAClC,CAAC;EAED,oBACEtK,OAAA;IAAK6E,SAAS,EAAC,yBAAyB;IAAAxE,QAAA,gBAEtCL,OAAA;MAAK6E,SAAS,EAAC,sBAAsB;MAAAxE,QAAA,eACnCL,OAAA;QAAK6E,SAAS,EAAC,gBAAgB;QAAAxE,QAAA,gBAC7BL,OAAA;UAAK6E,SAAS,EAAC,aAAa;UAAAxE,QAAA,gBAC1BL,OAAA;YAAK6E,SAAS,EAAC,aAAa;YAAAxE,QAAA,eAC1BL,OAAA,CAACc,OAAO;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACNT,OAAA;YAAK6E,SAAS,EAAC,aAAa;YAAAxE,QAAA,gBAC1BL,OAAA;cAAAK,QAAA,EAAI;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtBT,OAAA;cAAAK,QAAA,EAAG;YAAiD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNT,OAAA;UAAK6E,SAAS,EAAC,eAAe;UAAAxE,QAAA,gBAC5BL,OAAA;YAAK6E,SAAS,EAAC,eAAe;YAAAxE,QAAA,gBAC5BL,OAAA;cAAM6E,SAAS,EAAC,aAAa;cAAAxE,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3CT,OAAA;cAAM6E,SAAS,EAAC,aAAa;cAAAxE,QAAA,EAAEiC,aAAa,CAAC2G,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG5G,aAAa,CAACiI,KAAK,CAAC,CAAC;YAAC;cAAAjK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClG,CAAC,eACNT,OAAA;YAAK6E,SAAS,EAAC,eAAe;YAAAxE,QAAA,gBAC5BL,OAAA;cAAM6E,SAAS,EAAC,aAAa;cAAAxE,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChDT,OAAA;cAAM6E,SAAS,EAAC,aAAa;cAAAxE,QAAA,EAC1B,CAAAoB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,KAAK,MAAK,SAAS,GAAI,SAAQ,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,KAAK,KAAI,KAAM,EAAC,GAC3D,CAAAlB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,KAAK,MAAK,WAAW,GAAI,QAAO,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,KAAK,KAAI,KAAM,EAAC,GAC5D,CAAAlB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,KAAK,MAAK,SAAS,GAAI,QAAO,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,KAAK,KAAI,KAAM,EAAC,GAC1D;YAAS;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENT,OAAA;MAAK6E,SAAS,EAAC,uBAAuB;MAAAxE,QAAA,gBAEpCL,OAAA;QAAK6E,SAAS,EAAC,gBAAgB;QAAAxE,QAAA,gBAC7BL,OAAA;UAAK6E,SAAS,EAAC,cAAc;UAAAxE,QAAA,gBAE3BL,OAAA;YAAK6E,SAAS,EAAC,eAAe;YAAAxE,QAAA,gBAC5BL,OAAA;cAAO6E,SAAS,EAAC,eAAe;cAAAxE,QAAA,gBAC9BL,OAAA,CAACe,QAAQ;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACXmB,WAAW,GAAG,kBAAkB,GAAG,iBAAiB;YAAA;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACRT,OAAA;cACEwK,KAAK,EAAE/H,aAAc;cACrBgI,QAAQ,EAAGC,CAAC,IAAKhI,gBAAgB,CAACgI,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAClD3F,SAAS,EAAC,6BAA6B;cAAAxE,QAAA,gBAEvCL,OAAA;gBAAQwK,KAAK,EAAC,KAAK;gBAAAnK,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACvC+D,gBAAgB,CAACqF,GAAG,CAAEe,GAAG,iBACxB5K,OAAA;gBAAkBwK,KAAK,EAAEI,GAAI;gBAAAvK,QAAA,EAC1BiC,aAAa,KAAK,SAAS,GAAI,SAAQsI,GAAI,EAAC,GAAI,QAAOA,GAAI;cAAC,GADlDA,GAAG;gBAAAtK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAER,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNT,OAAA;YAAK6E,SAAS,EAAC,eAAe;YAAAxE,QAAA,gBAC5BL,OAAA;cAAO6E,SAAS,EAAC,eAAe;cAAAxE,QAAA,gBAC9BL,OAAA,CAACe,QAAQ;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,WAEd;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRT,OAAA;cACEwK,KAAK,EAAE5H,eAAgB;cACvB6H,QAAQ,EAAGC,CAAC,IAAK7H,kBAAkB,CAAC6H,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cACpD3F,SAAS,EAAC,+BAA+B;cAAAxE,QAAA,gBAEzCL,OAAA;gBAAQwK,KAAK,EAAC,KAAK;gBAAAnK,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACxCgE,iBAAiB,CAACoF,GAAG,CAAE/E,OAAO,iBAC7B9E,OAAA;gBAAsBwK,KAAK,EAAE1F,OAAQ;gBAAAzE,QAAA,EAClCyE;cAAO,GADGA,OAAO;gBAAAxE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEZ,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNT,OAAA;YAAK6E,SAAS,EAAC,eAAe;YAAAxE,QAAA,gBAC5BL,OAAA;cAAO6E,SAAS,EAAC,eAAe;cAAAxE,QAAA,gBAC9BL,OAAA,CAACgB,eAAe;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,QAErB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRT,OAAA;cACEwK,KAAK,EAAExH,MAAO;cACdyH,QAAQ,EAAGC,CAAC,IAAKzH,SAAS,CAACyH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC3C3F,SAAS,EAAC,4BAA4B;cAAAxE,QAAA,gBAEtCL,OAAA;gBAAQwK,KAAK,EAAC,QAAQ;gBAAAnK,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CT,OAAA;gBAAQwK,KAAK,EAAC,QAAQ;gBAAAnK,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CT,OAAA;gBAAQwK,KAAK,EAAC,OAAO;gBAAAnK,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCT,OAAA;gBAAQwK,KAAK,EAAC,SAAS;gBAAAnK,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNT,OAAA;UAAK6E,SAAS,EAAC,YAAY;UAAAxE,QAAA,gBACzBL,OAAA;YAAK6E,SAAS,EAAC,kBAAkB;YAAAxE,QAAA,gBAC/BL,OAAA,CAACiB,QAAQ;cAAC4D,SAAS,EAAC;YAAa;cAAAvE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpCT,OAAA;cACE6K,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,8CAA8C;cAC1DN,KAAK,EAAE1H,UAAW;cAClB2H,QAAQ,EAAGC,CAAC,IAAK3H,aAAa,CAAC2H,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC/C3F,SAAS,EAAC;YAAc;cAAAvE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,EACDqC,UAAU,iBACT9C,OAAA;cAAQ+K,OAAO,EAAE3C,iBAAkB;cAACvD,SAAS,EAAC,kBAAkB;cAAAxE,QAAA,gBAC9DL,OAAA,CAACkB,GAAG;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAET;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENT,OAAA;YAAQ+K,OAAO,EAAE1C,aAAc;YAACxD,SAAS,EAAC,aAAa;YAAAxE,QAAA,gBACrDL,OAAA,CAACmB,UAAU;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEhB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLyB,OAAO,gBACNlC,OAAA;QAAK6E,SAAS,EAAC,eAAe;QAAAxE,QAAA,gBAC5BL,OAAA;UAAK6E,SAAS,EAAC;QAAiB;UAAAvE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvCT,OAAA;UAAAK,QAAA,EAAG;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,GACJ2B,KAAK,gBACPpC,OAAA;QAAK6E,SAAS,EAAC,aAAa;QAAAxE,QAAA,gBAC1BL,OAAA,CAACoB,eAAe;UAACyD,SAAS,EAAC;QAAY;UAAAvE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1CT,OAAA;UAAAK,QAAA,EAAI;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7BT,OAAA;UAAAK,QAAA,EAAI+B;QAAK;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdT,OAAA;UAAQ+K,OAAO,EAAErG,WAAY;UAACG,SAAS,EAAC,WAAW;UAAAxE,QAAA,EAAC;QAEpD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,GACJ6E,uBAAuB,CAACsB,MAAM,GAAG,CAAC,gBACpC5G,OAAA;QAAK6E,SAAS,EAAC,aAAa;QAAAxE,QAAA,EACzBiF,uBAAuB,CAACuE,GAAG,CAAC,CAACpE,KAAK,EAAEqB,KAAK,kBACxC9G,OAAA;UAAiB6E,SAAS,EAAC,YAAY;UAACkG,OAAO,EAAEA,CAAA,KAAMlE,eAAe,CAACC,KAAK,CAAE;UAAAzG,QAAA,gBAC5EL,OAAA;YAAK6E,SAAS,EAAC,sBAAsB;YAAAxE,QAAA,gBACnCL,OAAA;cACEgL,GAAG,EAAEjD,eAAe,CAACtC,KAAK,CAAE;cAC5BwF,GAAG,EAAExF,KAAK,CAACQ,KAAM;cACjBpB,SAAS,EAAC,iBAAiB;cAC3BqG,OAAO,EAAGR,CAAC,IAAK;gBACd;gBACA,IAAIjF,KAAK,CAACwC,OAAO,IAAI,CAACxC,KAAK,CAACwC,OAAO,CAAC/B,QAAQ,CAAC,eAAe,CAAC,EAAE;kBAC7D;kBACA,IAAIgC,OAAO,GAAGzC,KAAK,CAACwC,OAAO;kBAC3B,IAAIC,OAAO,CAAChC,QAAQ,CAAC,aAAa,CAAC,IAAIgC,OAAO,CAAChC,QAAQ,CAAC,UAAU,CAAC,EAAE;oBACnE,MAAMiC,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,oDAAoD,CAAC;oBACjFD,OAAO,GAAGC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,OAAO;kBACtC;kBAEA,MAAMiD,SAAS,GAAG,CACf,8BAA6BjD,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,gBAAe,EACpD,8BAA6BA,OAAQ,cAAa,EACnD,0BAA0B,CAC3B;kBAED,MAAMkD,UAAU,GAAGV,CAAC,CAACC,MAAM,CAACK,GAAG;kBAC/B,MAAMK,YAAY,GAAGF,SAAS,CAACG,SAAS,CAACC,GAAG,IAAIH,UAAU,CAAClF,QAAQ,CAACqF,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC;kBAE1F,IAAIJ,YAAY,GAAGF,SAAS,CAACvE,MAAM,GAAG,CAAC,EAAE;oBACvC8D,CAAC,CAACC,MAAM,CAACK,GAAG,GAAGG,SAAS,CAACE,YAAY,GAAG,CAAC,CAAC;kBAC5C;gBACF,CAAC,MAAM;kBACLX,CAAC,CAACC,MAAM,CAACK,GAAG,GAAG,0BAA0B;gBAC3C;cACF;YAAE;cAAA1K,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFT,OAAA;cAAK6E,SAAS,EAAC,cAAc;cAAAxE,QAAA,eAC3BL,OAAA,CAACE,YAAY;gBAAC2E,SAAS,EAAC;cAAW;gBAAAvE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACNT,OAAA;cAAK6E,SAAS,EAAC,gBAAgB;cAAAxE,QAAA,EAC5BoF,KAAK,CAACiG,QAAQ,IAAI;YAAO;cAAApL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,EACLgF,KAAK,CAACkG,SAAS,IAAIlG,KAAK,CAACkG,SAAS,CAAC/E,MAAM,GAAG,CAAC,iBAC5C5G,OAAA;cAAK6E,SAAS,EAAC,gBAAgB;cAAAxE,QAAA,gBAC7BL,OAAA,CAACsB,YAAY;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,MAElB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENT,OAAA;YAAK6E,SAAS,EAAC,oBAAoB;YAAAxE,QAAA,gBACjCL,OAAA;cAAI6E,SAAS,EAAC,aAAa;cAAAxE,QAAA,EAAEoF,KAAK,CAACQ;YAAK;cAAA3F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9CT,OAAA;cAAK6E,SAAS,EAAC,YAAY;cAAAxE,QAAA,gBACzBL,OAAA;gBAAM6E,SAAS,EAAC,eAAe;gBAAAxE,QAAA,EAAEoF,KAAK,CAACX;cAAO;gBAAAxE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtDT,OAAA;gBAAM6E,SAAS,EAAC,aAAa;gBAAAxE,QAAA,EAC1BiC,aAAa,KAAK,SAAS,GAAI,SAAQmD,KAAK,CAACZ,SAAS,IAAIY,KAAK,CAAC9C,KAAM,EAAC,GAAI,QAAO8C,KAAK,CAACZ,SAAS,IAAIY,KAAK,CAAC9C,KAAM;cAAC;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/G,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNT,OAAA;cAAK6E,SAAS,EAAC,YAAY;cAAAxE,QAAA,GACxBoF,KAAK,CAACU,KAAK,iBAAInG,OAAA;gBAAM6E,SAAS,EAAC,WAAW;gBAAAxE,QAAA,EAAEoF,KAAK,CAACU;cAAK;gBAAA7F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAC/DgF,KAAK,CAACmG,eAAe,IAAInG,KAAK,CAACmG,eAAe,MAAMnG,KAAK,CAACZ,SAAS,IAAIY,KAAK,CAAC9C,KAAK,CAAC,iBAClF3C,OAAA;gBAAM6E,SAAS,EAAC,YAAY;gBAAAxE,QAAA,GAAC,cACf,EAACiC,aAAa,KAAK,SAAS,GAAI,SAAQmD,KAAK,CAACmG,eAAgB,EAAC,GAAI,QAAOnG,KAAK,CAACmG,eAAgB,EAAC;cAAA;gBAAAtL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzG,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GAhEEqG,KAAK;UAAAxG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAiEV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAENT,OAAA;QAAK6E,SAAS,EAAC,aAAa;QAAAxE,QAAA,gBAC1BL,OAAA,CAACU,eAAe;UAACmE,SAAS,EAAC;QAAY;UAAAvE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1CT,OAAA;UAAAK,QAAA,EAAI;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxBT,OAAA;UAAAK,QAAA,EAAG;QAA0D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACjET,OAAA;UAAG6E,SAAS,EAAC,YAAY;UAAAxE,QAAA,EAAC;QAA2C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL2C,gBAAgB,CAACwD,MAAM,GAAG,CAAC,IAAI1D,iBAAiB,KAAK,IAAI,iBACxDlD,OAAA;MAAK6E,SAAS,EAAG,iBAAgBvB,eAAe,GAAG,UAAU,GAAG,EAAG,EAAE;MAACyH,OAAO,EAAGL,CAAC,IAAK;QACpF,IAAIA,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACmB,aAAa,EAAEzE,eAAe,CAAC,CAAC;MACrD,CAAE;MAAA/G,QAAA,eACAL,OAAA;QAAK6E,SAAS,EAAG,eAAcvB,eAAe,GAAG,UAAU,GAAG,EAAG,EAAE;QAAAjD,QAAA,EAChE,CAAC,CAAAyL,WAAA,EAAAC,kBAAA,KAAM;UACN,MAAMtG,KAAK,GAAGH,uBAAuB,CAACpC,iBAAiB,CAAC;UACxD,IAAI,CAACuC,KAAK,EAAE,oBAAOzF,OAAA;YAAAK,QAAA,EAAK;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;UAE7C,oBACET,OAAA;YAAK6E,SAAS,EAAC,eAAe;YAAAxE,QAAA,gBAC5BL,OAAA;cAAK6E,SAAS,EAAC,cAAc;cAAAxE,QAAA,gBAC3BL,OAAA;gBAAK6E,SAAS,EAAC,YAAY;gBAAAxE,QAAA,gBACzBL,OAAA;kBAAI6E,SAAS,EAAC,aAAa;kBAAAxE,QAAA,EAAEoF,KAAK,CAACQ;gBAAK;kBAAA3F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9CT,OAAA;kBAAK6E,SAAS,EAAC,YAAY;kBAAAxE,QAAA,gBACzBL,OAAA;oBAAM6E,SAAS,EAAC,eAAe;oBAAAxE,QAAA,EAAEoF,KAAK,CAACX;kBAAO;oBAAAxE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtDT,OAAA;oBAAM6E,SAAS,EAAC,aAAa;oBAAAxE,QAAA,GAAC,QAAM,EAACoF,KAAK,CAACZ,SAAS;kBAAA;oBAAAvE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAC3DgF,KAAK,CAACjD,KAAK,iBAAIxC,OAAA;oBAAM6E,SAAS,EAAC,aAAa;oBAAAxE,QAAA,EAAEoF,KAAK,CAACjD;kBAAK;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNT,OAAA;gBAAK6E,SAAS,EAAC,gBAAgB;gBAAAxE,QAAA,gBAC7BL,OAAA;kBACE6E,SAAS,EAAC,6BAA6B;kBACvCkG,OAAO,EAAEA,CAAA,KAAM;oBACbxG,mBAAmB,CAAC,CAACD,gBAAgB,CAAC;oBACtC,IAAI,CAACA,gBAAgB,IAAI,CAAChB,eAAe,EAAE;sBACzCgE,oBAAoB,CAAC,CAAC;oBACxB;kBACF,CAAE;kBACFrB,KAAK,EAAC,aAAa;kBAAA5F,QAAA,gBAEnBL,OAAA;oBAAM6E,SAAS,EAAC,UAAU;oBAAAxE,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpCT,OAAA;oBAAM6E,SAAS,EAAC,UAAU;oBAAAxE,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,EACP6C,eAAe,IAAIgB,gBAAgB,iBACnCtE,OAAA;kBACE6E,SAAS,EAAC,+BAA+B;kBACzCkG,OAAO,EAAEA,CAAA,KAAM;oBACbxG,mBAAmB,CAAC,KAAK,CAAC;oBAC1B+C,oBAAoB,CAAC,CAAC;kBACxB,CAAE;kBACFrB,KAAK,EAAC,gBAAgB;kBAAA5F,QAAA,gBAEtBL,OAAA;oBAAM6E,SAAS,EAAC,UAAU;oBAAAxE,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnCT,OAAA;oBAAM6E,SAAS,EAAC,UAAU;oBAAAxE,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CACT,eACDT,OAAA;kBACE6E,SAAS,EAAC,uBAAuB;kBACjCkG,OAAO,EAAE3D,eAAgB;kBACzBnB,KAAK,EAAC,aAAa;kBAAA5F,QAAA,eAEnBL,OAAA,CAACW,OAAO;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNT,OAAA;cAAK6E,SAAS,EAAG,qBAAoBvB,eAAe,GAAG,iBAAiB,GAAG,eAAgB,EAAE;cAAAjD,QAAA,gBAE3FL,OAAA;gBAAK6E,SAAS,EAAC,iBAAiB;gBAAAxE,QAAA,EAC/BoF,KAAK,CAACsB,QAAQ,gBACb/G,OAAA;kBAAKG,KAAK,EAAE;oBAAE6L,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,MAAM;oBAAEC,YAAY,EAAE;kBAAM,CAAE;kBAAA7L,QAAA,gBACrEL,OAAA;oBACEmM,GAAG,EAAGA,GAAG,IAAKxI,WAAW,CAACwI,GAAG,CAAE;oBAC/BC,QAAQ;oBACRC,QAAQ;oBACRC,WAAW;oBACXC,OAAO,EAAC,UAAU;oBAClBC,KAAK,EAAC,MAAM;oBACZC,MAAM,EAAC,KAAK;oBACZC,MAAM,EAAE3E,eAAe,CAACtC,KAAK,CAAE;oBAC/BtF,KAAK,EAAE;sBACLqM,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,OAAO;sBACfE,eAAe,EAAE;oBACnB,CAAE;oBACFzB,OAAO,EAAGR,CAAC,IAAK;sBACdjH,aAAa,CAAE,yBAAwBgC,KAAK,CAACQ,KAAM,mCAAkC,CAAC;oBACxF,CAAE;oBACF2G,SAAS,EAAEA,CAAA,KAAM;sBACfnJ,aAAa,CAAC,IAAI,CAAC;oBACrB,CAAE;oBACFoJ,WAAW,EAAEA,CAAA,KAAM;sBACjBxH,OAAO,CAACsB,GAAG,CAAC,0BAA0B,CAAC;oBACzC,CAAE;oBACFmG,WAAW,EAAC,WAAW;oBAAAzM,QAAA,gBAGvBL,OAAA;sBAAQgL,GAAG,EAAEvF,KAAK,CAACyB,cAAc,IAAIzB,KAAK,CAACsB,QAAS;sBAAC8D,IAAI,EAAC;oBAAW;sBAAAvK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EAGvEgF,KAAK,CAACkG,SAAS,IAAIlG,KAAK,CAACkG,SAAS,CAAC/E,MAAM,GAAG,CAAC,IAAInB,KAAK,CAACkG,SAAS,CAAC9B,GAAG,CAAC,CAACkD,QAAQ,EAAEjG,KAAK,kBACpF9G,OAAA;sBAEEgN,IAAI,EAAC,WAAW;sBAChBhC,GAAG,EAAE+B,QAAQ,CAACxB,GAAI;sBAClB0B,OAAO,EAAEF,QAAQ,CAACG,QAAS;sBAC3BC,KAAK,EAAEJ,QAAQ,CAACK,YAAa;sBAC7BC,OAAO,EAAEN,QAAQ,CAACO,SAAS,IAAIxG,KAAK,KAAK;oBAAE,GALrC,GAAEiG,QAAQ,CAACG,QAAS,IAAGpG,KAAM,EAAC;sBAAAxG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAMrC,CACF,CAAC,EAAC,8CAGL;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAGPgF,KAAK,CAACkG,SAAS,IAAIlG,KAAK,CAACkG,SAAS,CAAC/E,MAAM,GAAG,CAAC,iBAC5C5G,OAAA;oBAAK6E,SAAS,EAAC,oBAAoB;oBAAAxE,QAAA,gBACjCL,OAAA,CAACsB,YAAY;sBAACuD,SAAS,EAAC;oBAAe;sBAAAvE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC1CT,OAAA;sBAAAK,QAAA,GAAM,yBAAuB,EAACoF,KAAK,CAACkG,SAAS,CAAC/E,MAAM,EAAC,cAAY;oBAAA;sBAAAtG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrE,CACN,EAGA+C,UAAU,iBACTxD,OAAA;oBAAK6E,SAAS,EAAC,qBAAqB;oBAAAxE,QAAA,eAClCL,OAAA;sBAAK6E,SAAS,EAAC,eAAe;sBAAAxE,QAAA,gBAC5BL,OAAA,CAACoB,eAAe;wBAACyD,SAAS,EAAC;sBAAY;wBAAAvE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC1CT,OAAA;wBAAAK,QAAA,EAAImD;sBAAU;wBAAAlD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACnBT,OAAA;wBAAQ+K,OAAO,EAAEA,CAAA,KAAMtH,aAAa,CAAC,IAAI,CAAE;wBAACoB,SAAS,EAAC,mBAAmB;wBAAAxE,QAAA,EAAC;sBAE1E;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,GACNgF,KAAK,CAACwC,OAAO;gBAAA;gBACf;gBACAjI,OAAA;kBACEgL,GAAG,EAAG,iCAAgCvF,KAAK,CAACwC,OAAQ,mBAAmB;kBACvEhC,KAAK,EAAER,KAAK,CAACQ,KAAM;kBACnBsH,WAAW,EAAC,GAAG;kBACfC,eAAe;kBACf3I,SAAS,EAAC,cAAc;kBACxB4I,MAAM,EAAEA,CAAA,KAAMpI,OAAO,CAACsB,GAAG,CAAC,yBAAyB;gBAAE;kBAAArG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,gBAEVT,OAAA;kBAAK6E,SAAS,EAAC,aAAa;kBAAAxE,QAAA,gBAC1BL,OAAA;oBAAK6E,SAAS,EAAC,YAAY;oBAAAxE,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpCT,OAAA;oBAAAK,QAAA,EAAI;kBAAiB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1BT,OAAA;oBAAAK,QAAA,EAAImD,UAAU,IAAI;kBAA4C;oBAAAlD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnET,OAAA;oBAAK6E,SAAS,EAAC,eAAe;oBAAAxE,QAAA,eAC5BL,OAAA;sBACE0N,IAAI,EAAEjI,KAAK,CAACyB,cAAc,IAAIzB,KAAK,CAACsB,QAAS;sBAC7C4D,MAAM,EAAC,QAAQ;sBACfgD,GAAG,EAAC,qBAAqB;sBACzB9I,SAAS,EAAC,mBAAmB;sBAAAxE,QAAA,EAC9B;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eAGNT,OAAA;gBAAK6E,SAAS,EAAG,0BAAyBvB,eAAe,GAAG,mBAAmB,GAAG,iBAAkB,EAAE;gBAAAjD,QAAA,gBAEpGL,OAAA;kBAAK6E,SAAS,EAAC,uBAAuB;kBAAAxE,QAAA,gBACpCL,OAAA;oBAAK6E,SAAS,EAAC,wBAAwB;oBAAAxE,QAAA,gBACrCL,OAAA,CAACsB,YAAY;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAChBT,OAAA;sBAAAK,QAAA,GAAOuD,QAAQ,CAACgD,MAAM,EAAC,GAAC,EAAChD,QAAQ,CAACgD,MAAM,KAAK,CAAC,GAAG,SAAS,GAAG,UAAU;oBAAA;sBAAAtG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5E,CAAC,EACL,CAAC6C,eAAe,IAAI,CAACgB,gBAAgB,gBACpCtE,OAAA;oBACE+K,OAAO,EAAEA,CAAA,KAAM;sBACbxG,mBAAmB,CAAC,IAAI,CAAC;sBACzB,IAAI,CAACjB,eAAe,EAAE;wBACpBgE,oBAAoB,CAAC,CAAC;sBACxB;oBACF,CAAE;oBACFzC,SAAS,EAAC,mBAAmB;oBAAAxE,QAAA,gBAE7BL,OAAA;sBAAM6E,SAAS,EAAC,UAAU;sBAAAxE,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACrCT,OAAA;sBAAM6E,SAAS,EAAC,UAAU;sBAAAxE,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC,gBAETT,OAAA;oBACE+K,OAAO,EAAEA,CAAA,KAAMxG,mBAAmB,CAAC,KAAK,CAAE;oBAC1CM,SAAS,EAAC,qBAAqB;oBAAAxE,QAAA,EAChC;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,EAGJ6C,eAAe,IAAIgB,gBAAgB,iBACnCtE,OAAA;kBAAK6E,SAAS,EAAC,4BAA4B;kBAAAxE,QAAA,gBAEvCL,OAAA;oBAAK6E,SAAS,EAAC,aAAa;oBAAAxE,QAAA,eAC1BL,OAAA;sBAAK6E,SAAS,EAAC,yBAAyB;sBAAAxE,QAAA,gBACtCL,OAAA;wBAAK6E,SAAS,EAAC,aAAa;wBAAAxE,QAAA,EACzB,CAAAoB,IAAI,aAAJA,IAAI,wBAAAqK,WAAA,GAAJrK,IAAI,CAAEsH,IAAI,cAAA+C,WAAA,wBAAAC,kBAAA,GAAVD,WAAA,CAAY7C,MAAM,CAAC,CAAC,CAAC,cAAA8C,kBAAA,uBAArBA,kBAAA,CAAuB7C,WAAW,CAAC,CAAC,KAAI;sBAAG;wBAAA5I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzC,CAAC,eACNT,OAAA;wBAAK6E,SAAS,EAAC,uBAAuB;wBAAAxE,QAAA,gBACpCL,OAAA;0BACEwK,KAAK,EAAE1G,UAAW;0BAClB2G,QAAQ,EAAGC,CAAC,IAAK3G,aAAa,CAAC2G,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;0BAC/CM,WAAW,EAAC,yCAAyC;0BACrDjG,SAAS,EAAC,eAAe;0BACzB+I,IAAI,EAAC,GAAG;0BACRC,SAAS;wBAAA;0BAAAvN,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACV,CAAC,eACFT,OAAA;0BACE+K,OAAO,EAAExC,gBAAiB;0BAC1B1D,SAAS,EAAC,oBAAoB;0BAC9BiJ,QAAQ,EAAE,CAAChK,UAAU,CAAC6B,IAAI,CAAC,CAAE;0BAAAtF,QAAA,gBAE7BL,OAAA;4BAAAK,QAAA,EAAM;0BAAE;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,iBACjB;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGZT,OAAA;oBAAK6E,SAAS,EAAC,eAAe;oBAAAxE,QAAA,EAC3BuD,QAAQ,CAACgD,MAAM,KAAK,CAAC,gBACpB5G,OAAA;sBAAK6E,SAAS,EAAC,aAAa;sBAAAxE,QAAA,gBAC1BL,OAAA;wBAAK6E,SAAS,EAAC,kBAAkB;wBAAAxE,QAAA,EAAC;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC1CT,OAAA;wBAAAK,QAAA,EAAG;sBAAqD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzD,CAAC,GAENmD,QAAQ,CAACiG,GAAG,CAAEnB,OAAO;sBAAA,IAAAqF,eAAA,EAAAC,qBAAA,EAAAC,WAAA,EAAAC,kBAAA;sBAAA,oBACnBlO,OAAA;wBAAsB6E,SAAS,EAAC,SAAS;wBAAAxE,QAAA,gBACvCL,OAAA;0BAAK6E,SAAS,EAAC,cAAc;0BAAAxE,QAAA,gBAC3BL,OAAA;4BAAK6E,SAAS,EAAC,gBAAgB;4BAAAxE,QAAA,EAC5BqI,OAAO,CAACM,MAAM,MAAA+E,eAAA,GAAIrF,OAAO,CAACI,MAAM,cAAAiF,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgB9E,MAAM,CAAC,CAAC,CAAC,cAAA+E,qBAAA,uBAAzBA,qBAAA,CAA2B9E,WAAW,CAAC,CAAC,KAAI;0BAAG;4BAAA5I,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC/D,CAAC,eACNT,OAAA;4BAAK6E,SAAS,EAAC,iBAAiB;4BAAAxE,QAAA,gBAC9BL,OAAA;8BAAK6E,SAAS,EAAC,gBAAgB;8BAAAxE,QAAA,gBAC7BL,OAAA;gCAAM6E,SAAS,EAAC,gBAAgB;gCAAAxE,QAAA,EAAEqI,OAAO,CAACI;8BAAM;gCAAAxI,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC,eACxDT,OAAA;gCAAM6E,SAAS,EAAC,cAAc;gCAAAxE,QAAA,EAC3B4J,aAAa,CAACvB,OAAO,CAACS,SAAS;8BAAC;gCAAA7I,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC7B,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACJ,CAAC,eACNT,OAAA;8BAAK6E,SAAS,EAAC,cAAc;8BAAAxE,QAAA,EAAEqI,OAAO,CAACG;4BAAI;8BAAAvI,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC,eAClDT,OAAA;8BAAK6E,SAAS,EAAC,iBAAiB;8BAAAxE,QAAA,gBAC9BL,OAAA;gCACE+K,OAAO,EAAEA,CAAA,KAAMjB,iBAAiB,CAACpB,OAAO,CAACC,EAAE,CAAE;gCAC7C9D,SAAS,EAAG,YAAW6D,OAAO,CAACa,KAAK,GAAG,OAAO,GAAG,EAAG,EAAE;gCAAAlJ,QAAA,gBAEtDL,OAAA;kCAAAK,QAAA,EAAOqI,OAAO,CAACa,KAAK,GAAG,IAAI,GAAG;gCAAI;kCAAAjJ,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO,CAAC,EACzCiI,OAAO,CAACY,KAAK,GAAG,CAAC,iBAAItJ,OAAA;kCAAM6E,SAAS,EAAC,YAAY;kCAAAxE,QAAA,EAAEqI,OAAO,CAACY;gCAAK;kCAAAhJ,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAO,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACnE,CAAC,eACTT,OAAA;gCACE+K,OAAO,EAAEA,CAAA,KAAM9G,aAAa,CAACD,UAAU,KAAK0E,OAAO,CAACC,EAAE,GAAG,IAAI,GAAGD,OAAO,CAACC,EAAE,CAAE;gCAC5E9D,SAAS,EAAC,WAAW;gCAAAxE,QAAA,gBAErBL,OAAA;kCAAAK,QAAA,EAAM;gCAAE;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAM,CAAC,UACjB;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAQ,CAAC,EACRiI,OAAO,CAACW,OAAO,CAACzC,MAAM,GAAG,CAAC,iBACzB5G,OAAA;gCAAM6E,SAAS,EAAC,eAAe;gCAAAxE,QAAA,GAC5BqI,OAAO,CAACW,OAAO,CAACzC,MAAM,EAAC,GAAC,EAAC8B,OAAO,CAACW,OAAO,CAACzC,MAAM,KAAK,CAAC,GAAG,OAAO,GAAG,SAAS;8BAAA;gCAAAtG,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACxE,CACP;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACE,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,EAGLuD,UAAU,KAAK0E,OAAO,CAACC,EAAE,iBACxB3I,OAAA;0BAAK6E,SAAS,EAAC,uBAAuB;0BAAAxE,QAAA,eACpCL,OAAA;4BAAK6E,SAAS,EAAC,qBAAqB;4BAAAxE,QAAA,gBAClCL,OAAA;8BAAK6E,SAAS,EAAC,mBAAmB;8BAAAxE,QAAA,EAC/B,CAAAoB,IAAI,aAAJA,IAAI,wBAAAwM,WAAA,GAAJxM,IAAI,CAAEsH,IAAI,cAAAkF,WAAA,wBAAAC,kBAAA,GAAVD,WAAA,CAAYhF,MAAM,CAAC,CAAC,CAAC,cAAAiF,kBAAA,uBAArBA,kBAAA,CAAuBhF,WAAW,CAAC,CAAC,KAAI;4BAAG;8BAAA5I,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACzC,CAAC,eACNT,OAAA;8BAAK6E,SAAS,EAAC,qBAAqB;8BAAAxE,QAAA,gBAClCL,OAAA;gCACEwK,KAAK,EAAEtG,SAAU;gCACjBuG,QAAQ,EAAGC,CAAC,IAAKvG,YAAY,CAACuG,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gCAC9CM,WAAW,EAAG,YAAWpC,OAAO,CAACI,MAAO,KAAK;gCAC7CjE,SAAS,EAAC,aAAa;gCACvB+I,IAAI,EAAC,GAAG;gCACRC,SAAS;8BAAA;gCAAAvN,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACV,CAAC,eACFT,OAAA;gCAAK6E,SAAS,EAAC,eAAe;gCAAAxE,QAAA,gBAC5BL,OAAA;kCACE+K,OAAO,EAAEA,CAAA,KAAMvB,cAAc,CAACd,OAAO,CAACC,EAAE,CAAE;kCAC1C9D,SAAS,EAAC,kBAAkB;kCAC5BiJ,QAAQ,EAAE,CAAC5J,SAAS,CAACyB,IAAI,CAAC,CAAE;kCAAAtF,QAAA,gBAE5BL,OAAA;oCAAAK,QAAA,EAAM;kCAAE;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAM,CAAC,UACjB;gCAAA;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAQ,CAAC,eACTT,OAAA;kCACE+K,OAAO,EAAEA,CAAA,KAAM;oCACb9G,aAAa,CAAC,IAAI,CAAC;oCACnBE,YAAY,CAAC,EAAE,CAAC;kCAClB,CAAE;kCACFU,SAAS,EAAC,kBAAkB;kCAAAxE,QAAA,EAC7B;gCAED;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAQ,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACN,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CACN,EAGAiI,OAAO,CAACW,OAAO,CAACzC,MAAM,GAAG,CAAC,iBACzB5G,OAAA;0BAAK6E,SAAS,EAAC,SAAS;0BAAAxE,QAAA,EACrBqI,OAAO,CAACW,OAAO,CAACQ,GAAG,CAAED,KAAK;4BAAA,IAAAuE,aAAA,EAAAC,oBAAA;4BAAA,oBACzBpO,OAAA;8BAAoB6E,SAAS,EAAC,OAAO;8BAAAxE,QAAA,eACnCL,OAAA;gCAAK6E,SAAS,EAAC,YAAY;gCAAAxE,QAAA,gBACzBL,OAAA;kCAAK6E,SAAS,EAAC,cAAc;kCAAAxE,QAAA,EAC1BuJ,KAAK,CAACZ,MAAM,MAAAmF,aAAA,GAAIvE,KAAK,CAACd,MAAM,cAAAqF,aAAA,wBAAAC,oBAAA,GAAZD,aAAA,CAAclF,MAAM,CAAC,CAAC,CAAC,cAAAmF,oBAAA,uBAAvBA,oBAAA,CAAyBlF,WAAW,CAAC,CAAC,KAAI;gCAAG;kCAAA5I,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC3D,CAAC,eACNT,OAAA;kCAAK6E,SAAS,EAAC,eAAe;kCAAAxE,QAAA,gBAC5BL,OAAA;oCAAK6E,SAAS,EAAC,cAAc;oCAAAxE,QAAA,gBAC3BL,OAAA;sCAAM6E,SAAS,EAAC,cAAc;sCAAAxE,QAAA,EAAEuJ,KAAK,CAACd;oCAAM;sCAAAxI,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAO,CAAC,eACpDT,OAAA;sCAAM6E,SAAS,EAAC,YAAY;sCAAAxE,QAAA,EACzB4J,aAAa,CAACL,KAAK,CAACT,SAAS;oCAAC;sCAAA7I,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAC3B,CAAC;kCAAA;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACJ,CAAC,eACNT,OAAA;oCAAK6E,SAAS,EAAC,YAAY;oCAAAxE,QAAA,EAAEuJ,KAAK,CAACf;kCAAI;oCAAAvI,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAM,CAAC,eAC9CT,OAAA;oCAAK6E,SAAS,EAAC,eAAe;oCAAAxE,QAAA,eAC5BL,OAAA;sCACE+K,OAAO,EAAEA,CAAA,KAAMjB,iBAAiB,CAACF,KAAK,CAACjB,EAAE,EAAE,IAAI,EAAED,OAAO,CAACC,EAAE,CAAE;sCAC7D9D,SAAS,EAAG,kBAAiB+E,KAAK,CAACL,KAAK,GAAG,OAAO,GAAG,EAAG,EAAE;sCAAAlJ,QAAA,gBAE1DL,OAAA;wCAAAK,QAAA,EAAOuJ,KAAK,CAACL,KAAK,GAAG,IAAI,GAAG;sCAAI;wCAAAjJ,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OAAO,CAAC,EACvCmJ,KAAK,CAACN,KAAK,GAAG,CAAC,iBAAItJ,OAAA;wCAAM6E,SAAS,EAAC,YAAY;wCAAAxE,QAAA,EAAEuJ,KAAK,CAACN;sCAAK;wCAAAhJ,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OAAO,CAAC;oCAAA;sCAAAH,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAC/D;kCAAC;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACN,CAAC;gCAAA;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACH,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACH;4BAAC,GAvBEmJ,KAAK,CAACjB,EAAE;8BAAArI,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAwBb,CAAC;0BAAA,CACP;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CACN;sBAAA,GA1GOiI,OAAO,CAACC,EAAE;wBAAArI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OA2Gf,CAAC;oBAAA,CACP;kBACF;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEH,CAAC;QAEV,CAAC,EAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACe,EAAA,CAp4BQD,YAAY;EAAA,QACFhC,WAAW,EAC6BO,WAAW,EACnDR,WAAW;AAAA;AAAA+O,EAAA,GAHrB9M,YAAY;AAs4BrB,eAAeA,YAAY;AAAC,IAAA8M,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}