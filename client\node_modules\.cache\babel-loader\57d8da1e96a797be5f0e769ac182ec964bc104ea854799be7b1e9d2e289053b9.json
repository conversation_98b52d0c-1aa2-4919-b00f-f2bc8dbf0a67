{"ast": null, "code": "import { clearFix, textEllipsis } from '../../style';\nconst genListStyle = token => {\n  const {\n    componentCls,\n    antCls,\n    iconCls,\n    fontSize,\n    lineHeight\n  } = token;\n  const itemCls = \"\".concat(componentCls, \"-list-item\");\n  const actionsCls = \"\".concat(itemCls, \"-actions\");\n  const actionCls = \"\".concat(itemCls, \"-action\");\n  const listItemHeightSM = Math.round(fontSize * lineHeight);\n  return {\n    [\"\".concat(componentCls, \"-wrapper\")]: {\n      [\"\".concat(componentCls, \"-list\")]: Object.assign(Object.assign({}, clearFix()), {\n        lineHeight: token.lineHeight,\n        [itemCls]: {\n          position: 'relative',\n          height: token.lineHeight * fontSize,\n          marginTop: token.marginXS,\n          fontSize,\n          display: 'flex',\n          alignItems: 'center',\n          transition: \"background-color \".concat(token.motionDurationSlow),\n          '&:hover': {\n            backgroundColor: token.controlItemBgHover\n          },\n          [\"\".concat(itemCls, \"-name\")]: Object.assign(Object.assign({}, textEllipsis), {\n            padding: \"0 \".concat(token.paddingXS, \"px\"),\n            lineHeight,\n            flex: 'auto',\n            transition: \"all \".concat(token.motionDurationSlow)\n          }),\n          [actionsCls]: {\n            [actionCls]: {\n              opacity: 0\n            },\n            [\"\".concat(actionCls).concat(antCls, \"-btn-sm\")]: {\n              height: listItemHeightSM,\n              border: 0,\n              lineHeight: 1,\n              // FIXME: should not override small button\n              '> span': {\n                transform: 'scale(1)'\n              }\n            },\n            [\"\\n              \".concat(actionCls, \":focus,\\n              &.picture \").concat(actionCls, \"\\n            \")]: {\n              opacity: 1\n            },\n            [iconCls]: {\n              color: token.actionsColor,\n              transition: \"all \".concat(token.motionDurationSlow)\n            },\n            [\"&:hover \".concat(iconCls)]: {\n              color: token.colorText\n            }\n          },\n          [\"\".concat(componentCls, \"-icon \").concat(iconCls)]: {\n            color: token.colorTextDescription,\n            fontSize\n          },\n          [\"\".concat(itemCls, \"-progress\")]: {\n            position: 'absolute',\n            bottom: -token.uploadProgressOffset,\n            width: '100%',\n            paddingInlineStart: fontSize + token.paddingXS,\n            fontSize,\n            lineHeight: 0,\n            pointerEvents: 'none',\n            '> div': {\n              margin: 0\n            }\n          }\n        },\n        [\"\".concat(itemCls, \":hover \").concat(actionCls)]: {\n          opacity: 1,\n          color: token.colorText\n        },\n        [\"\".concat(itemCls, \"-error\")]: {\n          color: token.colorError,\n          [\"\".concat(itemCls, \"-name, \").concat(componentCls, \"-icon \").concat(iconCls)]: {\n            color: token.colorError\n          },\n          [actionsCls]: {\n            [\"\".concat(iconCls, \", \").concat(iconCls, \":hover\")]: {\n              color: token.colorError\n            },\n            [actionCls]: {\n              opacity: 1\n            }\n          }\n        },\n        [\"\".concat(componentCls, \"-list-item-container\")]: {\n          transition: \"opacity \".concat(token.motionDurationSlow, \", height \").concat(token.motionDurationSlow),\n          // For smooth removing animation\n          '&::before': {\n            display: 'table',\n            width: 0,\n            height: 0,\n            content: '\"\"'\n          }\n        }\n      })\n    }\n  };\n};\nexport default genListStyle;", "map": {"version": 3, "names": ["clearFix", "textEllipsis", "genListStyle", "token", "componentCls", "antCls", "iconCls", "fontSize", "lineHeight", "itemCls", "concat", "actionsCls", "actionCls", "listItemHeightSM", "Math", "round", "Object", "assign", "position", "height", "marginTop", "marginXS", "display", "alignItems", "transition", "motionDurationSlow", "backgroundColor", "controlItemBgHover", "padding", "paddingXS", "flex", "opacity", "border", "transform", "color", "actionsColor", "colorText", "colorTextDescription", "bottom", "uploadProgressOffset", "width", "paddingInlineStart", "pointerEvents", "margin", "colorError", "content"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/upload/style/list.js"], "sourcesContent": ["import { clearFix, textEllipsis } from '../../style';\nconst genListStyle = token => {\n  const {\n    componentCls,\n    antCls,\n    iconCls,\n    fontSize,\n    lineHeight\n  } = token;\n  const itemCls = `${componentCls}-list-item`;\n  const actionsCls = `${itemCls}-actions`;\n  const actionCls = `${itemCls}-action`;\n  const listItemHeightSM = Math.round(fontSize * lineHeight);\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-list`]: Object.assign(Object.assign({}, clearFix()), {\n        lineHeight: token.lineHeight,\n        [itemCls]: {\n          position: 'relative',\n          height: token.lineHeight * fontSize,\n          marginTop: token.marginXS,\n          fontSize,\n          display: 'flex',\n          alignItems: 'center',\n          transition: `background-color ${token.motionDurationSlow}`,\n          '&:hover': {\n            backgroundColor: token.controlItemBgHover\n          },\n          [`${itemCls}-name`]: Object.assign(Object.assign({}, textEllipsis), {\n            padding: `0 ${token.paddingXS}px`,\n            lineHeight,\n            flex: 'auto',\n            transition: `all ${token.motionDurationSlow}`\n          }),\n          [actionsCls]: {\n            [actionCls]: {\n              opacity: 0\n            },\n            [`${actionCls}${antCls}-btn-sm`]: {\n              height: listItemHeightSM,\n              border: 0,\n              lineHeight: 1,\n              // FIXME: should not override small button\n              '> span': {\n                transform: 'scale(1)'\n              }\n            },\n            [`\n              ${actionCls}:focus,\n              &.picture ${actionCls}\n            `]: {\n              opacity: 1\n            },\n            [iconCls]: {\n              color: token.actionsColor,\n              transition: `all ${token.motionDurationSlow}`\n            },\n            [`&:hover ${iconCls}`]: {\n              color: token.colorText\n            }\n          },\n          [`${componentCls}-icon ${iconCls}`]: {\n            color: token.colorTextDescription,\n            fontSize\n          },\n          [`${itemCls}-progress`]: {\n            position: 'absolute',\n            bottom: -token.uploadProgressOffset,\n            width: '100%',\n            paddingInlineStart: fontSize + token.paddingXS,\n            fontSize,\n            lineHeight: 0,\n            pointerEvents: 'none',\n            '> div': {\n              margin: 0\n            }\n          }\n        },\n        [`${itemCls}:hover ${actionCls}`]: {\n          opacity: 1,\n          color: token.colorText\n        },\n        [`${itemCls}-error`]: {\n          color: token.colorError,\n          [`${itemCls}-name, ${componentCls}-icon ${iconCls}`]: {\n            color: token.colorError\n          },\n          [actionsCls]: {\n            [`${iconCls}, ${iconCls}:hover`]: {\n              color: token.colorError\n            },\n            [actionCls]: {\n              opacity: 1\n            }\n          }\n        },\n        [`${componentCls}-list-item-container`]: {\n          transition: `opacity ${token.motionDurationSlow}, height ${token.motionDurationSlow}`,\n          // For smooth removing animation\n          '&::before': {\n            display: 'table',\n            width: 0,\n            height: 0,\n            content: '\"\"'\n          }\n        }\n      })\n    }\n  };\n};\nexport default genListStyle;"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,YAAY,QAAQ,aAAa;AACpD,MAAMC,YAAY,GAAGC,KAAK,IAAI;EAC5B,MAAM;IACJC,YAAY;IACZC,MAAM;IACNC,OAAO;IACPC,QAAQ;IACRC;EACF,CAAC,GAAGL,KAAK;EACT,MAAMM,OAAO,MAAAC,MAAA,CAAMN,YAAY,eAAY;EAC3C,MAAMO,UAAU,MAAAD,MAAA,CAAMD,OAAO,aAAU;EACvC,MAAMG,SAAS,MAAAF,MAAA,CAAMD,OAAO,YAAS;EACrC,MAAMI,gBAAgB,GAAGC,IAAI,CAACC,KAAK,CAACR,QAAQ,GAAGC,UAAU,CAAC;EAC1D,OAAO;IACL,IAAAE,MAAA,CAAIN,YAAY,gBAAa;MAC3B,IAAAM,MAAA,CAAIN,YAAY,aAAUY,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEjB,QAAQ,CAAC,CAAC,CAAC,EAAE;QACrEQ,UAAU,EAAEL,KAAK,CAACK,UAAU;QAC5B,CAACC,OAAO,GAAG;UACTS,QAAQ,EAAE,UAAU;UACpBC,MAAM,EAAEhB,KAAK,CAACK,UAAU,GAAGD,QAAQ;UACnCa,SAAS,EAAEjB,KAAK,CAACkB,QAAQ;UACzBd,QAAQ;UACRe,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,UAAU,sBAAAd,MAAA,CAAsBP,KAAK,CAACsB,kBAAkB,CAAE;UAC1D,SAAS,EAAE;YACTC,eAAe,EAAEvB,KAAK,CAACwB;UACzB,CAAC;UACD,IAAAjB,MAAA,CAAID,OAAO,aAAUO,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEhB,YAAY,CAAC,EAAE;YAClE2B,OAAO,OAAAlB,MAAA,CAAOP,KAAK,CAAC0B,SAAS,OAAI;YACjCrB,UAAU;YACVsB,IAAI,EAAE,MAAM;YACZN,UAAU,SAAAd,MAAA,CAASP,KAAK,CAACsB,kBAAkB;UAC7C,CAAC,CAAC;UACF,CAACd,UAAU,GAAG;YACZ,CAACC,SAAS,GAAG;cACXmB,OAAO,EAAE;YACX,CAAC;YACD,IAAArB,MAAA,CAAIE,SAAS,EAAAF,MAAA,CAAGL,MAAM,eAAY;cAChCc,MAAM,EAAEN,gBAAgB;cACxBmB,MAAM,EAAE,CAAC;cACTxB,UAAU,EAAE,CAAC;cACb;cACA,QAAQ,EAAE;gBACRyB,SAAS,EAAE;cACb;YACF,CAAC;YACD,oBAAAvB,MAAA,CACIE,SAAS,uCAAAF,MAAA,CACCE,SAAS,sBACnB;cACFmB,OAAO,EAAE;YACX,CAAC;YACD,CAACzB,OAAO,GAAG;cACT4B,KAAK,EAAE/B,KAAK,CAACgC,YAAY;cACzBX,UAAU,SAAAd,MAAA,CAASP,KAAK,CAACsB,kBAAkB;YAC7C,CAAC;YACD,YAAAf,MAAA,CAAYJ,OAAO,IAAK;cACtB4B,KAAK,EAAE/B,KAAK,CAACiC;YACf;UACF,CAAC;UACD,IAAA1B,MAAA,CAAIN,YAAY,YAAAM,MAAA,CAASJ,OAAO,IAAK;YACnC4B,KAAK,EAAE/B,KAAK,CAACkC,oBAAoB;YACjC9B;UACF,CAAC;UACD,IAAAG,MAAA,CAAID,OAAO,iBAAc;YACvBS,QAAQ,EAAE,UAAU;YACpBoB,MAAM,EAAE,CAACnC,KAAK,CAACoC,oBAAoB;YACnCC,KAAK,EAAE,MAAM;YACbC,kBAAkB,EAAElC,QAAQ,GAAGJ,KAAK,CAAC0B,SAAS;YAC9CtB,QAAQ;YACRC,UAAU,EAAE,CAAC;YACbkC,aAAa,EAAE,MAAM;YACrB,OAAO,EAAE;cACPC,MAAM,EAAE;YACV;UACF;QACF,CAAC;QACD,IAAAjC,MAAA,CAAID,OAAO,aAAAC,MAAA,CAAUE,SAAS,IAAK;UACjCmB,OAAO,EAAE,CAAC;UACVG,KAAK,EAAE/B,KAAK,CAACiC;QACf,CAAC;QACD,IAAA1B,MAAA,CAAID,OAAO,cAAW;UACpByB,KAAK,EAAE/B,KAAK,CAACyC,UAAU;UACvB,IAAAlC,MAAA,CAAID,OAAO,aAAAC,MAAA,CAAUN,YAAY,YAAAM,MAAA,CAASJ,OAAO,IAAK;YACpD4B,KAAK,EAAE/B,KAAK,CAACyC;UACf,CAAC;UACD,CAACjC,UAAU,GAAG;YACZ,IAAAD,MAAA,CAAIJ,OAAO,QAAAI,MAAA,CAAKJ,OAAO,cAAW;cAChC4B,KAAK,EAAE/B,KAAK,CAACyC;YACf,CAAC;YACD,CAAChC,SAAS,GAAG;cACXmB,OAAO,EAAE;YACX;UACF;QACF,CAAC;QACD,IAAArB,MAAA,CAAIN,YAAY,4BAAyB;UACvCoB,UAAU,aAAAd,MAAA,CAAaP,KAAK,CAACsB,kBAAkB,eAAAf,MAAA,CAAYP,KAAK,CAACsB,kBAAkB,CAAE;UACrF;UACA,WAAW,EAAE;YACXH,OAAO,EAAE,OAAO;YAChBkB,KAAK,EAAE,CAAC;YACRrB,MAAM,EAAE,CAAC;YACT0B,OAAO,EAAE;UACX;QACF;MACF,CAAC;IACH;EACF,CAAC;AACH,CAAC;AACD,eAAe3C,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}