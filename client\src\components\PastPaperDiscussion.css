/* Past Paper Discussion Component Styles */

.past-paper-discussion-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.past-paper-discussion-overlay.expanded {
  padding: 0;
}

.past-paper-discussion {
  background: white;
  border-radius: 15px;
  width: 100%;
  max-width: 800px;
  height: 600px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  overflow: hidden;
}

.past-paper-discussion.expanded {
  max-width: 100vw;
  height: 100vh;
  border-radius: 0;
}

/* Header */
.discussion-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 15px 15px 0 0;
}

.past-paper-discussion.expanded .discussion-header {
  border-radius: 0;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 15px;
  flex: 1;
}

.ai-icon {
  font-size: 24px;
  color: #ffd700;
}

.header-text {
  flex: 1;
}

.discussion-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.paper-title {
  margin: 5px 0 0 0;
  font-size: 14px;
  opacity: 0.9;
  font-weight: 400;
}

.header-controls {
  display: flex;
  gap: 10px;
}

.expand-btn,
.close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.expand-btn:hover,
.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

/* Messages Container */
.messages-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.messages-list {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* Messages */
.message {
  display: flex;
  gap: 12px;
  max-width: 85%;
}

.user-message {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.ai-message {
  align-self: flex-start;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  flex-shrink: 0;
}

.ai-message .message-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.user-message .message-avatar {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  color: white;
}

.loading-message .message-avatar {
  background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
}

.message-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.message-text {
  background: #f7fafc;
  padding: 12px 16px;
  border-radius: 18px;
  line-height: 1.5;
  word-wrap: break-word;
}

.user-message .message-text {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  color: white;
}

.ai-message .message-text {
  background: #f7fafc;
  color: #2d3748;
  border: 1px solid #e2e8f0;
}

.error-message .message-text {
  background: #fed7d7;
  color: #c53030;
  border: 1px solid #feb2b2;
}

.message-time {
  font-size: 12px;
  color: #718096;
  align-self: flex-end;
}

.user-message .message-time {
  align-self: flex-start;
}

/* Loading Animation */
.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Input Container */
.input-container {
  border-top: 1px solid #e2e8f0;
  padding: 20px;
  background: #f8fafc;
}

.input-wrapper {
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.message-input {
  flex: 1;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 14px;
  resize: none;
  outline: none;
  transition: all 0.3s ease;
  font-family: inherit;
  line-height: 1.4;
}

.message-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.message-input:disabled {
  background: #f1f5f9;
  cursor: not-allowed;
}

.send-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  width: 45px;
  height: 45px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.send-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

.send-btn:disabled {
  background: #cbd5e0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.input-actions {
  margin-top: 12px;
  display: flex;
  justify-content: center;
}

.clear-btn {
  background: transparent;
  color: #718096;
  border: 1px solid #e2e8f0;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.clear-btn:hover {
  background: #f1f5f9;
  color: #4a5568;
}

/* Paper Info */
.paper-info {
  background: #edf2f7;
  padding: 12px 20px;
  border-top: 1px solid #e2e8f0;
  display: flex;
  gap: 20px;
  font-size: 12px;
}

.info-item {
  color: #4a5568;
}

.info-item strong {
  color: #2d3748;
}

/* Responsive Design */
@media (max-width: 768px) {
  .past-paper-discussion-overlay {
    padding: 10px;
  }

  .past-paper-discussion {
    max-width: 100%;
    height: calc(100vh - 20px);
  }

  .discussion-header {
    padding: 15px;
  }

  .discussion-title {
    font-size: 16px;
  }

  .paper-title {
    font-size: 12px;
  }

  .messages-list {
    padding: 15px;
    gap: 12px;
  }

  .message {
    max-width: 95%;
  }

  .message-avatar {
    width: 35px;
    height: 35px;
    font-size: 16px;
  }

  .message-text {
    padding: 10px 14px;
    font-size: 14px;
  }

  .input-container {
    padding: 15px;
  }

  .message-input {
    padding: 10px 14px;
    font-size: 14px;
  }

  .send-btn {
    width: 40px;
    height: 40px;
  }

  .paper-info {
    padding: 10px 15px;
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .past-paper-discussion-overlay {
    padding: 0;
  }

  .past-paper-discussion {
    height: 100vh;
    border-radius: 0;
  }

  .discussion-header {
    border-radius: 0;
    padding: 12px 15px;
  }

  .header-info {
    gap: 10px;
  }

  .ai-icon {
    font-size: 20px;
  }

  .discussion-title {
    font-size: 14px;
  }

  .paper-title {
    font-size: 11px;
  }

  .expand-btn,
  .close-btn {
    width: 35px;
    height: 35px;
  }

  .messages-list {
    padding: 12px;
  }

  .message-avatar {
    width: 30px;
    height: 30px;
    font-size: 14px;
  }

  .message-text {
    padding: 8px 12px;
    font-size: 13px;
  }

  .input-container {
    padding: 12px;
  }

  .send-btn {
    width: 35px;
    height: 35px;
  }
}

/* Scrollbar Styling */
.messages-list::-webkit-scrollbar {
  width: 6px;
}

.messages-list::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.messages-list::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.messages-list::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}
