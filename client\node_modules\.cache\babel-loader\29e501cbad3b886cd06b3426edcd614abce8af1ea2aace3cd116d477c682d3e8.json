{"ast": null, "code": "const getRTLStyle = _ref => {\n  let {\n    componentCls,\n    menuArrowOffset\n  } = _ref;\n  return {\n    [\"\".concat(componentCls, \"-rtl\")]: {\n      direction: 'rtl'\n    },\n    [\"\".concat(componentCls, \"-submenu-rtl\")]: {\n      transformOrigin: '100% 0'\n    },\n    // Vertical Arrow\n    [\"\".concat(componentCls, \"-rtl\").concat(componentCls, \"-vertical,\\n    \").concat(componentCls, \"-submenu-rtl \").concat(componentCls, \"-vertical\")]: {\n      [\"\".concat(componentCls, \"-submenu-arrow\")]: {\n        '&::before': {\n          transform: \"rotate(-45deg) translateY(-\".concat(menuArrowOffset, \")\")\n        },\n        '&::after': {\n          transform: \"rotate(45deg) translateY(\".concat(menuArrowOffset, \")\")\n        }\n      }\n    }\n  };\n};\nexport default getRTLStyle;", "map": {"version": 3, "names": ["getRTLStyle", "_ref", "componentCls", "menuArrowOffset", "concat", "direction", "transform<PERSON><PERSON>in", "transform"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/menu/style/rtl.js"], "sourcesContent": ["const getRTLStyle = _ref => {\n  let {\n    componentCls,\n    menuArrowOffset\n  } = _ref;\n  return {\n    [`${componentCls}-rtl`]: {\n      direction: 'rtl'\n    },\n    [`${componentCls}-submenu-rtl`]: {\n      transformOrigin: '100% 0'\n    },\n    // Vertical Arrow\n    [`${componentCls}-rtl${componentCls}-vertical,\n    ${componentCls}-submenu-rtl ${componentCls}-vertical`]: {\n      [`${componentCls}-submenu-arrow`]: {\n        '&::before': {\n          transform: `rotate(-45deg) translateY(-${menuArrowOffset})`\n        },\n        '&::after': {\n          transform: `rotate(45deg) translateY(${menuArrowOffset})`\n        }\n      }\n    }\n  };\n};\nexport default getRTLStyle;"], "mappings": "AAAA,MAAMA,WAAW,GAAGC,IAAI,IAAI;EAC1B,IAAI;IACFC,YAAY;IACZC;EACF,CAAC,GAAGF,IAAI;EACR,OAAO;IACL,IAAAG,MAAA,CAAIF,YAAY,YAAS;MACvBG,SAAS,EAAE;IACb,CAAC;IACD,IAAAD,MAAA,CAAIF,YAAY,oBAAiB;MAC/BI,eAAe,EAAE;IACnB,CAAC;IACD;IACA,IAAAF,MAAA,CAAIF,YAAY,UAAAE,MAAA,CAAOF,YAAY,sBAAAE,MAAA,CACjCF,YAAY,mBAAAE,MAAA,CAAgBF,YAAY,iBAAc;MACtD,IAAAE,MAAA,CAAIF,YAAY,sBAAmB;QACjC,WAAW,EAAE;UACXK,SAAS,gCAAAH,MAAA,CAAgCD,eAAe;QAC1D,CAAC;QACD,UAAU,EAAE;UACVI,SAAS,8BAAAH,MAAA,CAA8BD,eAAe;QACxD;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}