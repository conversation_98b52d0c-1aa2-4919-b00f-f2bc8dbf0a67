{"ast": null, "code": "import * as React from 'react';\n\n// Use any here since we do not get the type during compilation\n\nvar SelectContext = /*#__PURE__*/React.createContext(null);\nexport default SelectContext;", "map": {"version": 3, "names": ["React", "SelectContext", "createContext"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-select/es/SelectContext.js"], "sourcesContent": ["import * as React from 'react';\n\n// Use any here since we do not get the type during compilation\n\nvar SelectContext = /*#__PURE__*/React.createContext(null);\nexport default SelectContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;;AAE9B;;AAEA,IAAIC,aAAa,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAC1D,eAAeD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}