{"ast": null, "code": "const genStatusStyle = token => {\n  const {\n    componentCls,\n    menuCls,\n    colorError,\n    colorTextLightSolid\n  } = token;\n  const itemCls = \"\".concat(menuCls, \"-item\");\n  return {\n    [\"\".concat(componentCls, \", \").concat(componentCls, \"-menu-submenu\")]: {\n      [\"\".concat(menuCls, \" \").concat(itemCls)]: {\n        [\"&\".concat(itemCls, \"-danger:not(\").concat(itemCls, \"-disabled)\")]: {\n          color: colorError,\n          '&:hover': {\n            color: colorTextLightSolid,\n            backgroundColor: colorError\n          }\n        }\n      }\n    }\n  };\n};\nexport default genStatusStyle;", "map": {"version": 3, "names": ["genStatusStyle", "token", "componentCls", "menuCls", "colorError", "colorTextLightSolid", "itemCls", "concat", "color", "backgroundColor"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/dropdown/style/status.js"], "sourcesContent": ["const genStatusStyle = token => {\n  const {\n    componentCls,\n    menuCls,\n    colorError,\n    colorTextLightSolid\n  } = token;\n  const itemCls = `${menuCls}-item`;\n  return {\n    [`${componentCls}, ${componentCls}-menu-submenu`]: {\n      [`${menuCls} ${itemCls}`]: {\n        [`&${itemCls}-danger:not(${itemCls}-disabled)`]: {\n          color: colorError,\n          '&:hover': {\n            color: colorTextLightSolid,\n            backgroundColor: colorError\n          }\n        }\n      }\n    }\n  };\n};\nexport default genStatusStyle;"], "mappings": "AAAA,MAAMA,cAAc,GAAGC,KAAK,IAAI;EAC9B,MAAM;IACJC,YAAY;IACZC,OAAO;IACPC,UAAU;IACVC;EACF,CAAC,GAAGJ,KAAK;EACT,MAAMK,OAAO,MAAAC,MAAA,CAAMJ,OAAO,UAAO;EACjC,OAAO;IACL,IAAAI,MAAA,CAAIL,YAAY,QAAAK,MAAA,CAAKL,YAAY,qBAAkB;MACjD,IAAAK,MAAA,CAAIJ,OAAO,OAAAI,MAAA,CAAID,OAAO,IAAK;QACzB,KAAAC,MAAA,CAAKD,OAAO,kBAAAC,MAAA,CAAeD,OAAO,kBAAe;UAC/CE,KAAK,EAAEJ,UAAU;UACjB,SAAS,EAAE;YACTI,KAAK,EAAEH,mBAAmB;YAC1BI,eAAe,EAAEL;UACnB;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}