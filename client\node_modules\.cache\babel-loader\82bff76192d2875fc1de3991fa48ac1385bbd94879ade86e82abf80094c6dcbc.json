{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { PreviewGroupContext } from \"../context\";\nvar uid = 0;\nexport default function useRegisterImage(canPreview, data) {\n  var _React$useState = React.useState(function () {\n      uid += 1;\n      return String(uid);\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 1),\n    id = _React$useState2[0];\n  var groupContext = React.useContext(PreviewGroupContext);\n  var registerData = {\n    data: data,\n    canPreview: canPreview\n  };\n\n  // Keep order start\n  // Resolve https://github.com/ant-design/ant-design/issues/28881\n  // Only need unRegister when component unMount\n  React.useEffect(function () {\n    if (groupContext) {\n      return groupContext.register(id, registerData);\n    }\n  }, []);\n  React.useEffect(function () {\n    if (groupContext) {\n      groupContext.register(id, registerData);\n    }\n  }, [canPreview, data]);\n  return id;\n}", "map": {"version": 3, "names": ["_slicedToArray", "React", "PreviewGroupContext", "uid", "useRegisterImage", "canPreview", "data", "_React$useState", "useState", "String", "_React$useState2", "id", "groupContext", "useContext", "registerData", "useEffect", "register"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-image/es/hooks/useRegisterImage.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { PreviewGroupContext } from \"../context\";\nvar uid = 0;\nexport default function useRegisterImage(canPreview, data) {\n  var _React$useState = React.useState(function () {\n      uid += 1;\n      return String(uid);\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 1),\n    id = _React$useState2[0];\n  var groupContext = React.useContext(PreviewGroupContext);\n  var registerData = {\n    data: data,\n    canPreview: canPreview\n  };\n\n  // Keep order start\n  // Resolve https://github.com/ant-design/ant-design/issues/28881\n  // Only need unRegister when component unMount\n  React.useEffect(function () {\n    if (groupContext) {\n      return groupContext.register(id, registerData);\n    }\n  }, []);\n  React.useEffect(function () {\n    if (groupContext) {\n      groupContext.register(id, registerData);\n    }\n  }, [canPreview, data]);\n  return id;\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,mBAAmB,QAAQ,YAAY;AAChD,IAAIC,GAAG,GAAG,CAAC;AACX,eAAe,SAASC,gBAAgBA,CAACC,UAAU,EAAEC,IAAI,EAAE;EACzD,IAAIC,eAAe,GAAGN,KAAK,CAACO,QAAQ,CAAC,YAAY;MAC7CL,GAAG,IAAI,CAAC;MACR,OAAOM,MAAM,CAACN,GAAG,CAAC;IACpB,CAAC,CAAC;IACFO,gBAAgB,GAAGV,cAAc,CAACO,eAAe,EAAE,CAAC,CAAC;IACrDI,EAAE,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EAC1B,IAAIE,YAAY,GAAGX,KAAK,CAACY,UAAU,CAACX,mBAAmB,CAAC;EACxD,IAAIY,YAAY,GAAG;IACjBR,IAAI,EAAEA,IAAI;IACVD,UAAU,EAAEA;EACd,CAAC;;EAED;EACA;EACA;EACAJ,KAAK,CAACc,SAAS,CAAC,YAAY;IAC1B,IAAIH,YAAY,EAAE;MAChB,OAAOA,YAAY,CAACI,QAAQ,CAACL,EAAE,EAAEG,YAAY,CAAC;IAChD;EACF,CAAC,EAAE,EAAE,CAAC;EACNb,KAAK,CAACc,SAAS,CAAC,YAAY;IAC1B,IAAIH,YAAY,EAAE;MAChBA,YAAY,CAACI,QAAQ,CAACL,EAAE,EAAEG,YAAY,CAAC;IACzC;EACF,CAAC,EAAE,CAACT,UAAU,EAAEC,IAAI,CAAC,CAAC;EACtB,OAAOK,EAAE;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}