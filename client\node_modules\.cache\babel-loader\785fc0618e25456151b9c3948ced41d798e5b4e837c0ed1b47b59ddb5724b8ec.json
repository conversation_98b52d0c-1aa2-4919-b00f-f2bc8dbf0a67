{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\WriteExam\\\\index.js\",\n  _s = $RefreshSig$();\nimport { message } from \"antd\";\nimport React, { useCallback, useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useNavigate, useParams } from \"react-router-dom\";\nimport { getExamById } from \"../../../apicalls/exams\";\nimport { addReport } from \"../../../apicalls/reports\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport Instructions from \"./Instructions\";\nimport Pass from \"../../../assets/pass.gif\";\nimport Fail from \"../../../assets/fail.gif\";\nimport Confetti from \"react-confetti\";\nimport useWindowSize from \"react-use/lib/useWindowSize\";\nimport PassSound from \"../../../assets/pass.mp3\";\nimport FailSound from \"../../../assets/fail.mp3\";\nimport { chatWithChatGPTToGetAns, chatWithChatGPTToExplainAns } from \"../../../apicalls/chat\";\nimport { useLanguage } from \"../../../contexts/LanguageContext\";\nimport XPResultDisplay from \"../../../components/modern/XPResultDisplay\";\nimport { extractUserResultData, safeNumber } from \"../../../utils/quizDataUtils\";\n\n// Minimal Safe Quiz Component\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MinimalQuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerSelect,\n  onNext,\n  onPrevious,\n  timeLeft,\n  examTitle\n}) => {\n  // Safety checks\n  if (!question) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Loading question...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Convert everything to safe strings\n  const questionText = question.name ? String(question.name) : 'Question text not available';\n  const answerType = question.answerType ? String(question.answerType) : 'Options';\n\n  // Process options safely\n  let options = [];\n  if (question.options) {\n    if (Array.isArray(question.options)) {\n      options = question.options.map(opt => String(opt || ''));\n    } else if (typeof question.options === 'object') {\n      options = Object.values(question.options).map(opt => String(opt || ''));\n    }\n  }\n  const formatTime = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return mins + ':' + (secs < 10 ? '0' : '') + secs;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%)'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderBottom: '1px solid #e5e7eb',\n        padding: '16px',\n        position: 'sticky',\n        top: 0,\n        zIndex: 50\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          maxWidth: '1200px',\n          margin: '0 auto',\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            style: {\n              fontSize: '18px',\n              fontWeight: '600',\n              color: '#111827',\n              margin: 0\n            },\n            children: examTitle ? String(examTitle) : 'Quiz'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              fontSize: '14px',\n              color: '#6b7280',\n              margin: 0\n            },\n            children: [\"Question \", questionIndex + 1, \" of \", totalQuestions]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: timeLeft <= 60 ? '#dc2626' : '#2563eb',\n            color: 'white',\n            padding: '12px 24px',\n            borderRadius: '12px',\n            fontFamily: 'monospace',\n            fontWeight: 'bold'\n          },\n          children: [\"TIME: \", formatTime(timeLeft)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        maxWidth: '800px',\n        margin: '0 auto',\n        padding: '32px 16px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          borderRadius: '16px',\n          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',\n          padding: '32px',\n          marginBottom: '24px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '24px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              background: '#dbeafe',\n              color: '#1e40af',\n              padding: '8px 16px',\n              borderRadius: '20px',\n              fontSize: '14px',\n              fontWeight: '600'\n            },\n            children: [\"Question \", questionIndex + 1, \" of \", totalQuestions]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '32px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              fontSize: '20px',\n              fontWeight: '500',\n              color: '#111827',\n              lineHeight: '1.6',\n              margin: 0\n            },\n            children: questionText\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), (question.image || question.imageUrl) && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '32px',\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: question.image || question.imageUrl,\n            alt: \"Question\",\n            style: {\n              maxWidth: '100%',\n              maxHeight: '400px',\n              objectFit: 'contain',\n              borderRadius: '12px',\n              border: '1px solid #e5e7eb'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '16px'\n          },\n          children: answerType === \"Options\" && options.length > 0 ? options.map((option, index) => {\n            const letter = String.fromCharCode(65 + index);\n            const isSelected = selectedAnswer === index;\n            return /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => onAnswerSelect(index),\n              style: {\n                width: '100%',\n                textAlign: 'left',\n                padding: '16px',\n                borderRadius: '12px',\n                border: isSelected ? '2px solid #2563eb' : '2px solid #e5e7eb',\n                background: isSelected ? '#eff6ff' : 'white',\n                color: isSelected ? '#1e40af' : '#111827',\n                cursor: 'pointer',\n                transition: 'all 0.3s',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '16px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '32px',\n                  height: '32px',\n                  borderRadius: '50%',\n                  background: isSelected ? '#2563eb' : '#f3f4f6',\n                  color: isSelected ? 'white' : '#6b7280',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  fontWeight: 'bold',\n                  fontSize: '14px'\n                },\n                children: letter\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  flex: 1,\n                  fontWeight: '500'\n                },\n                children: option\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 21\n              }, this), isSelected && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '24px',\n                  height: '24px',\n                  borderRadius: '50%',\n                  background: '#2563eb',\n                  color: 'white',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                },\n                children: \"\\u2713\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 23\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 19\n            }, this);\n          }) : /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                fontSize: '14px',\n                fontWeight: '500',\n                color: '#374151',\n                marginBottom: '8px'\n              },\n              children: \"Your Answer:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: selectedAnswer || '',\n              onChange: e => onAnswerSelect(e.target.value),\n              placeholder: \"Type your answer here...\",\n              style: {\n                width: '100%',\n                padding: '16px',\n                border: '2px solid #e5e7eb',\n                borderRadius: '12px',\n                fontSize: '16px',\n                outline: 'none'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onPrevious,\n          disabled: questionIndex === 0,\n          style: {\n            padding: '12px 24px',\n            borderRadius: '12px',\n            fontWeight: '600',\n            border: 'none',\n            cursor: questionIndex === 0 ? 'not-allowed' : 'pointer',\n            background: questionIndex === 0 ? '#e5e7eb' : '#4b5563',\n            color: questionIndex === 0 ? '#9ca3af' : 'white'\n          },\n          children: \"\\u2190 Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '14px',\n              color: '#6b7280',\n              marginBottom: '8px'\n            },\n            children: \"Progress\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '200px',\n              height: '8px',\n              background: '#e5e7eb',\n              borderRadius: '4px',\n              overflow: 'hidden'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                height: '100%',\n                background: '#2563eb',\n                borderRadius: '4px',\n                width: (questionIndex + 1) / totalQuestions * 100 + '%',\n                transition: 'width 0.3s'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onNext,\n          style: {\n            padding: '12px 24px',\n            borderRadius: '12px',\n            fontWeight: '600',\n            border: 'none',\n            cursor: 'pointer',\n            background: '#2563eb',\n            color: 'white'\n          },\n          children: questionIndex === totalQuestions - 1 ? 'Submit Quiz' : 'Next →'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this);\n};\n\n// Minimal Safe Review Component\n_c = MinimalQuizRenderer;\nconst MinimalReviewRenderer = ({\n  questions,\n  selectedOptions,\n  explanations,\n  fetchExplanation,\n  setView,\n  examData,\n  setSelectedQuestionIndex,\n  setSelectedOptions,\n  setResult,\n  setTimeUp,\n  setSecondsLeft,\n  setExplanations\n}) => {\n  if (!questions || !Array.isArray(questions)) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"No questions to review\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%)',\n      padding: '24px'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        maxWidth: '800px',\n        margin: '0 auto'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          marginBottom: '24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: 'white',\n            borderRadius: '16px',\n            padding: '24px',\n            boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',\n            border: '1px solid #e2e8f0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              fontSize: '24px',\n              fontWeight: 'bold',\n              color: '#2563eb',\n              margin: '0 0 8px 0'\n            },\n            children: \"Answer Review\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#64748b',\n              margin: 0\n            },\n            children: \"Review your answers and get explanations\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '16px',\n          marginBottom: '24px'\n        },\n        children: questions.map((question, index) => {\n          if (!question) return null;\n          const questionText = question.name ? String(question.name) : 'Question not available';\n          const answerType = question.answerType ? String(question.answerType) : 'Options';\n          const userAnswer = selectedOptions[index];\n          let isCorrect = false;\n          let correctAnswerText = 'Unknown';\n          let userAnswerText = 'Not answered';\n          if (answerType === \"Options\") {\n            isCorrect = question.correctOption === userAnswer;\n            if (question.options && question.correctOption !== undefined) {\n              const correctOpt = question.options[question.correctOption];\n              correctAnswerText = correctOpt ? String(correctOpt) : 'Unknown';\n            }\n            if (question.options && userAnswer !== undefined) {\n              const userOpt = question.options[userAnswer];\n              userAnswerText = userOpt ? String(userOpt) : 'Not answered';\n            }\n          } else {\n            isCorrect = question.correctAnswer === userAnswer;\n            correctAnswerText = question.correctAnswer ? String(question.correctAnswer) : 'Unknown';\n            userAnswerText = userAnswer ? String(userAnswer) : 'Not answered';\n          }\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              borderRadius: '12px',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\n              border: '2px solid ' + (isCorrect ? '#10b981' : '#ef4444'),\n              padding: '16px',\n              background: isCorrect ? '#f0fdf4' : '#fef2f2'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '12px'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'flex-start',\n                  gap: '12px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    width: '32px',\n                    height: '32px',\n                    borderRadius: '8px',\n                    background: '#2563eb',\n                    color: 'white',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    fontWeight: 'bold',\n                    fontSize: '14px',\n                    flexShrink: 0,\n                    marginTop: '4px'\n                  },\n                  children: index + 1\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    flex: 1\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      color: '#1e293b',\n                      fontWeight: '500',\n                      lineHeight: '1.6',\n                      margin: 0\n                    },\n                    children: questionText\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 381,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '8px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: '14px',\n                  fontWeight: '600',\n                  color: '#64748b'\n                },\n                children: \"Your Answer: \"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontWeight: '500',\n                  color: isCorrect ? '#059669' : '#dc2626'\n                },\n                children: userAnswerText\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  marginLeft: '12px',\n                  fontSize: '18px',\n                  color: isCorrect ? '#10b981' : '#ef4444'\n                },\n                children: isCorrect ? '✓' : '✗'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 17\n            }, this), !isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '8px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontSize: '14px',\n                  fontWeight: '600',\n                  color: '#64748b'\n                },\n                children: \"Correct Answer: \"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontWeight: '500',\n                  color: '#059669'\n                },\n                children: correctAnswerText\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  marginLeft: '12px',\n                  fontSize: '18px',\n                  color: '#10b981'\n                },\n                children: \"\\u2713\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 19\n            }, this), !isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '8px'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  fetchExplanation(questionText, correctAnswerText, userAnswerText, question.image || question.imageUrl || '');\n                },\n                style: {\n                  padding: '8px 16px',\n                  background: '#2563eb',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '8px',\n                  fontSize: '14px',\n                  fontWeight: '500',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '8px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\uD83D\\uDCA1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Get Explanation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 19\n            }, this), explanations[questionText] && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '12px',\n                padding: '12px',\n                background: 'white',\n                borderRadius: '8px',\n                borderLeft: '4px solid #2563eb',\n                boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',\n                border: '1px solid #e5e7eb'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  marginBottom: '8px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '18px',\n                    marginRight: '8px'\n                  },\n                  children: \"\\uD83D\\uDCA1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 464,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                  style: {\n                    fontWeight: 'bold',\n                    color: '#1f2937',\n                    fontSize: '16px',\n                    margin: 0\n                  },\n                  children: \"Explanation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 465,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 21\n              }, this), (question.image || question.imageUrl) && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '12px',\n                  padding: '8px',\n                  background: '#f9fafb',\n                  borderRadius: '6px',\n                  border: '1px solid #e5e7eb'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginBottom: '4px'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: '#374151',\n                      fontSize: '14px',\n                      fontWeight: '500'\n                    },\n                    children: \"\\uD83D\\uDCCA Reference Diagram:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 480,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    textAlign: 'center'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: question.image || question.imageUrl,\n                    alt: \"Question diagram\",\n                    style: {\n                      maxWidth: '100%',\n                      maxHeight: '200px',\n                      objectFit: 'contain',\n                      borderRadius: '6px',\n                      border: '1px solid #d1d5db'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 485,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 484,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '14px',\n                  color: '#1f2937',\n                  lineHeight: '1.6',\n                  background: '#f9fafb',\n                  padding: '8px',\n                  borderRadius: '6px'\n                },\n                children: explanations[questionText] ? String(explanations[questionText]) : ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 500,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 19\n            }, this)]\n          }, question._id || index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: window.innerWidth < 640 ? 'column' : 'row',\n          gap: '16px',\n          justifyContent: 'center',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setView(\"result\"),\n          style: {\n            padding: '16px 32px',\n            background: '#4b5563',\n            color: 'white',\n            border: 'none',\n            borderRadius: '12px',\n            fontWeight: 'bold',\n            cursor: 'pointer',\n            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'\n          },\n          children: \"\\u2190 Back to Results\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 525,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            setView(\"instructions\");\n            setSelectedQuestionIndex(0);\n            setSelectedOptions({});\n            setResult({});\n            setTimeUp(false);\n            setSecondsLeft((examData === null || examData === void 0 ? void 0 : examData.duration) || 0);\n            setExplanations({});\n          },\n          style: {\n            padding: '16px 32px',\n            background: '#059669',\n            color: 'white',\n            border: 'none',\n            borderRadius: '12px',\n            fontWeight: 'bold',\n            cursor: 'pointer',\n            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'\n          },\n          children: \"\\uD83D\\uDD04 Retake Quiz\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 541,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 518,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 302,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 301,\n    columnNumber: 5\n  }, this);\n};\n_c2 = MinimalReviewRenderer;\nfunction WriteExam() {\n  _s();\n  var _result$correctAnswer, _result$correctAnswer2;\n  const {\n    isKiswahili\n  } = useLanguage();\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);\n  const [selectedOptions, setSelectedOptions] = useState({});\n  const [result, setResult] = useState({});\n  const params = useParams();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const [view, setView] = useState(\"instructions\");\n  const [secondsLeft, setSecondsLeft] = useState(0);\n  const [timeUp, setTimeUp] = useState(false);\n  const [intervalId, setIntervalId] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [startTime, setStartTime] = useState(null);\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    width,\n    height\n  } = useWindowSize();\n  const [explanations, setExplanations] = useState({});\n  const getExamData = useCallback(async () => {\n    try {\n      setIsLoading(true);\n      dispatch(ShowLoading());\n      console.log(\"Fetching exam data for ID:\", params.id);\n      const response = await getExamById({\n        examId: params.id\n      });\n      console.log(\"Exam API Response:\", response);\n      dispatch(HideLoading());\n      setIsLoading(false);\n      if (response.success) {\n        const examData = response.data;\n\n        // Check different possible question locations\n        let questions = [];\n        if (examData !== null && examData !== void 0 && examData.questions && Array.isArray(examData.questions)) {\n          questions = examData.questions;\n        } else if (examData !== null && examData !== void 0 && examData.question && Array.isArray(examData.question)) {\n          questions = examData.question;\n        } else if (examData && Array.isArray(examData)) {\n          questions = examData;\n        }\n        console.log(\"Exam Data:\", examData);\n        console.log(\"Questions found:\", questions.length);\n        console.log(\"Exam Data structure:\", Object.keys(examData || {}));\n        setQuestions(questions);\n        setExamData(examData);\n        setSecondsLeft((examData === null || examData === void 0 ? void 0 : examData.duration) || 0);\n        if (questions.length === 0) {\n          console.warn(\"No questions found in exam data\");\n          console.log(\"Full response for debugging:\", response);\n          message.warning(\"This exam has no questions. Please contact your instructor.\");\n        }\n      } else {\n        console.error(\"API Error:\", response.message);\n        console.log(\"Full error response:\", response);\n        message.error(response.message || \"Failed to load exam data\");\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      setIsLoading(false);\n      console.error(\"Exception in getExamData:\", error);\n      message.error(error.message || \"Failed to load exam. Please try again.\");\n    }\n  }, [params.id, dispatch]);\n  const checkFreeTextAnswers = async payload => {\n    if (!payload.length) return [];\n    const {\n      data\n    } = await chatWithChatGPTToGetAns(payload);\n    return data;\n  };\n  const calculateResult = useCallback(async () => {\n    try {\n      // Check if user is available\n      if (!user || !user._id) {\n        message.error(\"User not found. Please log in again.\");\n        navigate(\"/login\");\n        return;\n      }\n      dispatch(ShowLoading());\n      const freeTextPayload = [];\n      const indexMap = [];\n      questions.forEach((q, idx) => {\n        if (q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          indexMap.push(idx);\n          freeTextPayload.push({\n            question: q.name,\n            expectedAnswer: q.correctAnswer || q.correctOption,\n            userAnswer: selectedOptions[idx] || \"\"\n          });\n        }\n      });\n      const gptResults = await checkFreeTextAnswers(freeTextPayload);\n      const gptMap = {};\n      gptResults.forEach(r => {\n        if (r.result && typeof r.result.isCorrect === \"boolean\") {\n          gptMap[r.question] = r.result;\n        } else if (typeof r.isCorrect === \"boolean\") {\n          gptMap[r.question] = {\n            isCorrect: r.isCorrect,\n            reason: r.reason || \"\"\n          };\n        }\n      });\n      const correctAnswers = [];\n      const wrongAnswers = [];\n      const wrongPayload = [];\n      questions.forEach((q, idx) => {\n        const userAnswerKey = selectedOptions[idx] || \"\";\n        if (q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          const {\n            isCorrect = false,\n            reason = \"\"\n          } = gptMap[q.name] || {};\n          const enriched = {\n            ...q,\n            userAnswer: userAnswerKey,\n            reason\n          };\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n            wrongPayload.push({\n              question: q.name,\n              expectedAnswer: q.correctAnswer || q.correctOption,\n              userAnswer: userAnswerKey\n            });\n          }\n        } else if (q.answerType === \"Options\") {\n          const correctKey = q.correctOption;\n          const correctValue = q.options && q.options[correctKey] || correctKey;\n          const userValue = q.options && q.options[userAnswerKey] || userAnswerKey || \"\";\n          const isCorrect = correctKey === userAnswerKey;\n          const enriched = {\n            ...q,\n            userAnswer: userAnswerKey\n          };\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n            wrongPayload.push({\n              question: q.name,\n              expectedAnswer: correctValue,\n              userAnswer: userValue\n            });\n          }\n        }\n      });\n\n      // Calculate time spent\n      const timeSpent = startTime ? Math.floor((Date.now() - startTime) / 1000) : 0;\n      const totalTimeAllowed = ((examData === null || examData === void 0 ? void 0 : examData.duration) || 0) * 60; // Convert minutes to seconds\n\n      // Calculate score and points\n      const totalQuestions = questions.length;\n      const correctCount = correctAnswers.length;\n      const scorePercentage = Math.round(correctCount / totalQuestions * 100);\n      const points = correctCount * 10; // 10 points per correct answer\n\n      // Determine pass/fail based on percentage\n      const passingPercentage = examData.passingMarks || 70; // Default 70%\n      const verdict = scorePercentage >= passingPercentage ? \"Pass\" : \"Fail\";\n      const tempResult = {\n        correctAnswers: correctAnswers || [],\n        wrongAnswers: wrongAnswers || [],\n        verdict: verdict || \"Fail\",\n        score: scorePercentage,\n        points: points,\n        totalQuestions: totalQuestions,\n        timeSpent: timeSpent,\n        totalTimeAllowed: totalTimeAllowed\n      };\n      setResult(tempResult);\n      const response = await addReport({\n        exam: params.id,\n        result: tempResult,\n        user: user._id\n      });\n      if (response.success) {\n        // Include XP data in the result\n        const resultWithXP = {\n          ...tempResult,\n          xpData: response.xpData\n        };\n        setResult(resultWithXP);\n        setView(\"result\");\n        window.scrollTo(0, 0);\n        new Audio(verdict === \"Pass\" ? PassSound : FailSound).play();\n      } else {\n        message.error(response.message);\n      }\n      dispatch(HideLoading());\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  }, [questions, selectedOptions, examData, params.id, user, navigate, dispatch]);\n  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {\n    try {\n      dispatch(ShowLoading());\n      const response = await chatWithChatGPTToExplainAns({\n        question,\n        expectedAnswer,\n        userAnswer,\n        imageUrl\n      });\n      dispatch(HideLoading());\n      if (response.success) {\n        setExplanations(prev => ({\n          ...prev,\n          [question]: response.explanation\n        }));\n      } else {\n        message.error(response.error || \"Failed to fetch explanation.\");\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n  const startTimer = () => {\n    const totalSeconds = (examData === null || examData === void 0 ? void 0 : examData.duration) || 0; // Duration is already in seconds\n    setSecondsLeft(totalSeconds);\n    setStartTime(Date.now()); // Record start time for XP calculation\n\n    const newIntervalId = setInterval(() => {\n      setSecondsLeft(prevSeconds => {\n        if (prevSeconds > 0) {\n          return prevSeconds - 1;\n        } else {\n          setTimeUp(true);\n          return 0;\n        }\n      });\n    }, 1000);\n    setIntervalId(newIntervalId);\n  };\n  useEffect(() => {\n    if (timeUp && view === \"questions\") {\n      clearInterval(intervalId);\n      calculateResult();\n    }\n  }, [timeUp, view, intervalId, calculateResult]);\n  useEffect(() => {\n    console.log(\"WriteExam useEffect - params.id:\", params.id);\n    if (params.id) {\n      getExamData();\n    } else {\n      console.error(\"No exam ID provided in URL parameters\");\n      message.error(\"Invalid exam ID. Please select a quiz from the list.\");\n      navigate('/user/quiz');\n    }\n  }, [params.id, getExamData, navigate]);\n  useEffect(() => {\n    return () => {\n      if (intervalId) {\n        clearInterval(intervalId);\n      }\n    };\n  }, [intervalId]);\n\n  // Add fullscreen class for all quiz views (instructions, questions, results)\n  useEffect(() => {\n    if (view === \"instructions\" || view === \"questions\" || view === \"result\") {\n      document.body.classList.add(\"quiz-fullscreen\");\n    } else {\n      document.body.classList.remove(\"quiz-fullscreen\");\n    }\n\n    // Cleanup on unmount\n    return () => {\n      document.body.classList.remove(\"quiz-fullscreen\");\n    };\n  }, [view]);\n\n  // Repair function for fixing orphaned questions\n  const repairExamQuestions = async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await fetch('/api/exams/repair-exam-questions', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify({\n          examId: params.id\n        })\n      });\n      const data = await response.json();\n      if (data.success) {\n        message.success(data.message);\n        // Reload the exam data\n        getExamData();\n      } else {\n        message.error(data.message);\n      }\n    } catch (error) {\n      message.error(\"Failed to repair exam questions\");\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n\n  // Check if user is authenticated\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex justify-center items-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl border border-blue-100 p-12 text-center max-w-md mx-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-10 h-10 text-white\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 891,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 890,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 889,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 mb-4\",\n          children: \"Authentication Required\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 894,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-8\",\n          children: \"Please log in to access the exam and start your learning journey.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 895,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"w-full px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-indigo-700 transform hover:scale-105 transition-all duration-300 shadow-lg\",\n          onClick: () => navigate(\"/login\"),\n          children: \"Go to Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 896,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 888,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 887,\n      columnNumber: 7\n    }, this);\n  }\n  return examData ? /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\",\n    children: [view === \"instructions\" && /*#__PURE__*/_jsxDEV(Instructions, {\n      examData: examData,\n      setView: setView,\n      startTimer: startTimer,\n      questions: questions\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 911,\n      columnNumber: 9\n    }, this), view === \"questions\" && (isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-blue-200 max-w-lg mx-4 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-24 h-24 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg animate-pulse\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-12 h-12 text-white animate-spin\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n              className: \"opacity-25\",\n              cx: \"12\",\n              cy: \"12\",\n              r: \"10\",\n              stroke: \"currentColor\",\n              strokeWidth: \"4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 925,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              className: \"opacity-75\",\n              fill: \"currentColor\",\n              d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 926,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 924,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 923,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-2xl font-bold text-blue-800 mb-4\",\n          children: \"Loading Quiz...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 929,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-blue-600 text-lg\",\n          children: \"Please wait while we prepare your questions.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 930,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 922,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 921,\n      columnNumber: 11\n    }, this) : questions.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-amber-50 via-white to-orange-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-amber-200 max-w-lg mx-4 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-24 h-24 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-12 h-12 text-white\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 940,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 939,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 938,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-2xl font-bold text-amber-800 mb-4\",\n          children: \"No Questions Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 943,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-amber-700 mb-6 text-lg leading-relaxed\",\n          children: \"This exam appears to have no questions. This could be due to:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 944,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"text-left text-amber-700 mb-8 space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2022 Questions not properly linked to this exam\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 948,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2022 Database connection issues\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 949,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2022 Exam configuration problems\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 950,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 947,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: repairExamQuestions,\n            className: \"w-full px-8 py-4 bg-gradient-to-r from-amber-500 to-orange-500 text-white rounded-xl font-bold text-lg hover:from-amber-600 hover:to-orange-600 transform hover:scale-105 transition-all duration-300 shadow-lg\",\n            children: \"\\uD83D\\uDD27 Repair Questions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 953,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              console.log(\"Retrying exam data fetch...\");\n              getExamData();\n            },\n            className: \"w-full px-8 py-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl font-bold text-lg hover:from-blue-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-lg\",\n            children: \"\\uD83D\\uDD04 Retry Loading\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 959,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => navigate('/user/quiz'),\n            className: \"w-full px-8 py-4 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-xl font-bold text-lg hover:from-gray-600 hover:to-gray-700 transform hover:scale-105 transition-all duration-300 shadow-lg\",\n            children: \"\\u2190 Back to Quiz List\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 968,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 952,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 937,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 936,\n      columnNumber: 11\n    }, this) : /*#__PURE__*/_jsxDEV(MinimalQuizRenderer, {\n      question: questions[selectedQuestionIndex],\n      questionIndex: selectedQuestionIndex,\n      totalQuestions: questions.length,\n      selectedAnswer: selectedOptions[selectedQuestionIndex],\n      onAnswerSelect: answer => setSelectedOptions({\n        ...selectedOptions,\n        [selectedQuestionIndex]: answer\n      }),\n      onNext: () => {\n        if (selectedQuestionIndex === questions.length - 1) {\n          calculateResult();\n        } else {\n          setSelectedQuestionIndex(selectedQuestionIndex + 1);\n        }\n      },\n      onPrevious: () => {\n        if (selectedQuestionIndex > 0) {\n          setSelectedQuestionIndex(selectedQuestionIndex - 1);\n        }\n      },\n      timeLeft: secondsLeft,\n      examTitle: (examData === null || examData === void 0 ? void 0 : examData.name) || \"Quiz\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 978,\n      columnNumber: 11\n    }, this)), view === \"result\" && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-8\",\n      children: [result.verdict === \"Pass\" && /*#__PURE__*/_jsxDEV(Confetti, {\n        width: width,\n        height: height\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1004,\n        columnNumber: 41\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white/95 backdrop-blur-md rounded-2xl shadow-xl border border-slate-200/50 overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `px-8 py-10 text-center relative ${result.verdict === \"Pass\" ? \"bg-gradient-to-br from-emerald-500/10 via-green-500/5 to-teal-500/10\" : \"bg-gradient-to-br from-amber-500/10 via-orange-500/5 to-red-500/10\"}`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-20 h-20 mx-auto mb-6 rounded-2xl flex items-center justify-center shadow-lg ${result.verdict === \"Pass\" ? \"bg-gradient-to-br from-emerald-500 to-green-600\" : \"bg-gradient-to-br from-amber-500 to-orange-600\"}`,\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: result.verdict === \"Pass\" ? Pass : Fail,\n                  alt: result.verdict,\n                  className: \"w-12 h-12 object-contain\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1020,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1015,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n                className: `text-4xl font-black mb-4 tracking-tight ${result.verdict === \"Pass\" ? \"text-emerald-700\" : \"text-amber-700\"}`,\n                children: result.verdict === \"Pass\" ? \"Excellent Work!\" : \"Keep Pushing!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1026,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xl text-slate-600 font-medium max-w-md mx-auto leading-relaxed\",\n                children: result.verdict === \"Pass\" ? \"You've mastered this exam with flying colors!\" : \"Every challenge makes you stronger. Try again!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1031,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1014,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1009,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"group relative overflow-hidden bg-gradient-to-br from-blue-500/5 to-indigo-500/10 rounded-2xl border border-blue-200/50 p-6 hover:shadow-lg transition-all duration-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1044,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-4xl font-black text-blue-600 mb-2 tracking-tight\",\n                    children: [Math.round((((_result$correctAnswer = result.correctAnswers) === null || _result$correctAnswer === void 0 ? void 0 : _result$correctAnswer.length) || 0) / questions.length * 100), \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1046,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-bold text-blue-700/80 uppercase tracking-wider\",\n                    children: \"Your Score\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1049,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1045,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1043,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"group relative overflow-hidden bg-gradient-to-br from-emerald-500/5 to-green-500/10 rounded-2xl border border-emerald-200/50 p-6 hover:shadow-lg transition-all duration-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1055,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-4xl font-black text-emerald-600 mb-2 tracking-tight\",\n                    children: [Array.isArray(result.correctAnswers) ? result.correctAnswers.length : 0, \"/\", questions.length]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1057,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-bold text-emerald-700/80 uppercase tracking-wider\",\n                    children: \"Correct\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1060,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1056,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1054,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `group relative overflow-hidden rounded-2xl border p-6 hover:shadow-lg transition-all duration-300 ${result.verdict === \"Pass\" ? \"bg-gradient-to-br from-emerald-500/5 to-green-500/10 border-emerald-200/50\" : \"bg-gradient-to-br from-amber-500/5 to-orange-500/10 border-amber-200/50\"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-100 transition-opacity duration-300 ${result.verdict === \"Pass\" ? \"from-emerald-500/5\" : \"from-amber-500/5\"} to-transparent`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1070,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `text-4xl font-black mb-2 tracking-tight ${result.verdict === \"Pass\" ? \"text-emerald-600\" : \"text-amber-600\"}`,\n                    children: result.verdict === \"Pass\" ? \"PASS\" : \"RETRY\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1074,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `text-sm font-bold uppercase tracking-wider ${result.verdict === \"Pass\" ? \"text-emerald-700/80\" : \"text-amber-700/80\"}`,\n                    children: result.verdict === \"Pass\" ? \"Success!\" : `Need ${examData.passingMarks}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1079,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1073,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1065,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1041,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-8\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative bg-slate-100 rounded-2xl p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-bold text-slate-700 mb-1\",\n                    children: \"Performance Overview\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1092,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-slate-500\",\n                    children: \"Your achievement level\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1093,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1091,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full bg-slate-200 rounded-full h-4 shadow-inner overflow-hidden\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `h-full rounded-full transition-all duration-1000 shadow-sm relative overflow-hidden ${result.verdict === \"Pass\" ? \"bg-gradient-to-r from-emerald-500 via-green-500 to-teal-500\" : \"bg-gradient-to-r from-amber-500 via-orange-500 to-red-500\"}`,\n                      style: {\n                        width: `${(((_result$correctAnswer2 = result.correctAnswers) === null || _result$correctAnswer2 === void 0 ? void 0 : _result$correctAnswer2.length) || 0) / questions.length * 100}%`\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1105,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1097,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1096,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center mt-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs font-medium text-slate-500\",\n                      children: \"0%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1109,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-lg font-black tracking-tight ${result.verdict === \"Pass\" ? \"text-emerald-600\" : \"text-amber-600\"}`,\n                      children: [Math.round((Array.isArray(result.correctAnswers) ? result.correctAnswers.length : 0) / questions.length * 100), \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1110,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs font-medium text-slate-500\",\n                      children: \"100%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1115,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1108,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1095,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1090,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1089,\n              columnNumber: 17\n            }, this), result.xpData && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-8\",\n              children: /*#__PURE__*/_jsxDEV(XPResultDisplay, {\n                xpData: result.xpData\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1124,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1123,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n                onClick: () => setView(\"review\"),\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1134,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"relative\",\n                  children: \"Review Answers\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1135,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1130,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1129,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1040,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1007,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1006,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1003,\n      columnNumber: 9\n    }, this), view === \"review\" && /*#__PURE__*/_jsxDEV(MinimalReviewRenderer, {\n      questions: questions,\n      selectedOptions: selectedOptions,\n      explanations: explanations,\n      fetchExplanation: fetchExplanation,\n      setView: setView,\n      examData: examData,\n      setSelectedQuestionIndex: setSelectedQuestionIndex,\n      setSelectedOptions: setSelectedOptions,\n      setResult: setResult,\n      setTimeUp: setTimeUp,\n      setSecondsLeft: setSecondsLeft,\n      setExplanations: setExplanations\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1147,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 908,\n    columnNumber: 5\n  }, this) : null;\n}\n_s(WriteExam, \"n1FkA+GsYDjNAQEE1Mlqb8iggg8=\", false, function () {\n  return [useLanguage, useParams, useDispatch, useNavigate, useSelector, useWindowSize];\n});\n_c3 = WriteExam;\nexport default WriteExam;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"MinimalQuizRenderer\");\n$RefreshReg$(_c2, \"MinimalReviewRenderer\");\n$RefreshReg$(_c3, \"WriteExam\");", "map": {"version": 3, "names": ["message", "React", "useCallback", "useEffect", "useState", "useDispatch", "useSelector", "useNavigate", "useParams", "getExamById", "addReport", "HideLoading", "ShowLoading", "Instructions", "Pass", "Fail", "Confetti", "useWindowSize", "PassSound", "FailSound", "chatWithChatGPTToGetAns", "chatWithChatGPTToExplainAns", "useLanguage", "XPResultDisplay", "extractUserResultData", "safeNumber", "jsxDEV", "_jsxDEV", "MinimalQuiz<PERSON><PERSON><PERSON>", "question", "questionIndex", "totalQuestions", "<PERSON><PERSON><PERSON><PERSON>", "onAnswerSelect", "onNext", "onPrevious", "timeLeft", "examTitle", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "questionText", "name", "String", "answerType", "options", "Array", "isArray", "map", "opt", "Object", "values", "formatTime", "seconds", "mins", "Math", "floor", "secs", "style", "minHeight", "background", "borderBottom", "padding", "position", "top", "zIndex", "max<PERSON><PERSON><PERSON>", "margin", "display", "justifyContent", "alignItems", "fontSize", "fontWeight", "color", "borderRadius", "fontFamily", "boxShadow", "marginBottom", "lineHeight", "image", "imageUrl", "textAlign", "src", "alt", "maxHeight", "objectFit", "border", "flexDirection", "gap", "length", "option", "index", "letter", "fromCharCode", "isSelected", "onClick", "width", "cursor", "transition", "height", "flex", "type", "value", "onChange", "e", "target", "placeholder", "outline", "disabled", "overflow", "_c", "MinimalReview<PERSON><PERSON><PERSON>", "questions", "selectedOptions", "explanations", "fetchExplanation", "<PERSON><PERSON><PERSON><PERSON>", "examData", "setSelectedQuestionIndex", "setSelectedOptions", "setResult", "setTimeUp", "setSecondsLeft", "setExplanations", "userAnswer", "isCorrect", "correctAnswerText", "userAnswerText", "correctOption", "undefined", "correctOpt", "userOpt", "<PERSON><PERSON><PERSON><PERSON>", "flexShrink", "marginTop", "marginLeft", "borderLeft", "marginRight", "_id", "window", "innerWidth", "duration", "_c2", "WriteExam", "_s", "_result$correctAnswer", "_result$correctAnswer2", "isKiswahili", "setExamData", "setQuestions", "selectedQuestionIndex", "result", "params", "dispatch", "navigate", "view", "secondsLeft", "timeUp", "intervalId", "setIntervalId", "isLoading", "setIsLoading", "startTime", "setStartTime", "user", "state", "getExamData", "console", "log", "id", "response", "examId", "success", "data", "keys", "warn", "warning", "error", "checkFreeTextAnswers", "payload", "calculateResult", "freeTextPayload", "indexMap", "for<PERSON>ach", "q", "idx", "push", "expectedAnswer", "gptResults", "gptMap", "r", "reason", "correctAnswers", "wrongAnswers", "wrongPayload", "userAnswerKey", "enriched", "<PERSON><PERSON><PERSON>", "correctValue", "userValue", "timeSpent", "Date", "now", "totalTimeAllowed", "correctCount", "scorePercentage", "round", "points", "passingPercentage", "passingMarks", "verdict", "tempResult", "score", "exam", "resultWithXP", "xpData", "scrollTo", "Audio", "play", "prev", "explanation", "startTimer", "totalSeconds", "newIntervalId", "setInterval", "prevSeconds", "clearInterval", "document", "body", "classList", "add", "remove", "repairExamQuestions", "fetch", "method", "headers", "localStorage", "getItem", "JSON", "stringify", "json", "className", "fill", "viewBox", "fillRule", "d", "clipRule", "cx", "cy", "stroke", "strokeWidth", "answer", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/WriteExam/index.js"], "sourcesContent": ["import { message } from \"antd\";\r\nimport React, { useCallback, useEffect, useState } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { useNavigate, useParams } from \"react-router-dom\";\r\nimport { getExamById } from \"../../../apicalls/exams\";\r\nimport { addReport } from \"../../../apicalls/reports\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport Instructions from \"./Instructions\";\r\nimport Pass from \"../../../assets/pass.gif\";\r\nimport Fail from \"../../../assets/fail.gif\";\r\nimport Confetti from \"react-confetti\";\r\nimport useWindowSize from \"react-use/lib/useWindowSize\";\r\nimport PassSound from \"../../../assets/pass.mp3\";\r\nimport FailSound from \"../../../assets/fail.mp3\";\r\nimport { chatWithChatGPTToGetAns, chatWithChatGPTToExplainAns } from \"../../../apicalls/chat\";\r\nimport { useLanguage } from \"../../../contexts/LanguageContext\";\r\nimport XPResultDisplay from \"../../../components/modern/XPResultDisplay\";\r\nimport { extractUserResultData, safeNumber } from \"../../../utils/quizDataUtils\";\r\n\r\n// Minimal Safe Quiz Component\r\nconst MinimalQuizRenderer = ({ question, questionIndex, totalQuestions, selectedAnswer, onAnswerSelect, onNext, onPrevious, timeLeft, examTitle }) => {\r\n  // Safety checks\r\n  if (!question) {\r\n    return <div>Loading question...</div>;\r\n  }\r\n\r\n  // Convert everything to safe strings\r\n  const questionText = question.name ? String(question.name) : 'Question text not available';\r\n  const answerType = question.answerType ? String(question.answerType) : 'Options';\r\n\r\n  // Process options safely\r\n  let options = [];\r\n  if (question.options) {\r\n    if (Array.isArray(question.options)) {\r\n      options = question.options.map(opt => String(opt || ''));\r\n    } else if (typeof question.options === 'object') {\r\n      options = Object.values(question.options).map(opt => String(opt || ''));\r\n    }\r\n  }\r\n\r\n  const formatTime = (seconds) => {\r\n    const mins = Math.floor(seconds / 60);\r\n    const secs = seconds % 60;\r\n    return mins + ':' + (secs < 10 ? '0' : '') + secs;\r\n  };\r\n\r\n  return (\r\n    <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%)' }}>\r\n      {/* Simple Header */}\r\n      <div style={{\r\n        background: 'white',\r\n        borderBottom: '1px solid #e5e7eb',\r\n        padding: '16px',\r\n        position: 'sticky',\r\n        top: 0,\r\n        zIndex: 50\r\n      }}>\r\n        <div style={{ maxWidth: '1200px', margin: '0 auto', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\r\n          <div>\r\n            <h1 style={{ fontSize: '18px', fontWeight: '600', color: '#111827', margin: 0 }}>\r\n              {examTitle ? String(examTitle) : 'Quiz'}\r\n            </h1>\r\n            <p style={{ fontSize: '14px', color: '#6b7280', margin: 0 }}>\r\n              Question {questionIndex + 1} of {totalQuestions}\r\n            </p>\r\n          </div>\r\n\r\n          <div style={{\r\n            background: timeLeft <= 60 ? '#dc2626' : '#2563eb',\r\n            color: 'white',\r\n            padding: '12px 24px',\r\n            borderRadius: '12px',\r\n            fontFamily: 'monospace',\r\n            fontWeight: 'bold'\r\n          }}>\r\n            TIME: {formatTime(timeLeft)}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Question Content */}\r\n      <div style={{ maxWidth: '800px', margin: '0 auto', padding: '32px 16px' }}>\r\n        <div style={{\r\n          background: 'white',\r\n          borderRadius: '16px',\r\n          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',\r\n          padding: '32px',\r\n          marginBottom: '24px'\r\n        }}>\r\n          {/* Question Number Badge */}\r\n          <div style={{ marginBottom: '24px' }}>\r\n            <span style={{\r\n              background: '#dbeafe',\r\n              color: '#1e40af',\r\n              padding: '8px 16px',\r\n              borderRadius: '20px',\r\n              fontSize: '14px',\r\n              fontWeight: '600'\r\n            }}>\r\n              Question {questionIndex + 1} of {totalQuestions}\r\n            </span>\r\n          </div>\r\n\r\n          {/* Question Text */}\r\n          <div style={{ marginBottom: '32px' }}>\r\n            <h2 style={{\r\n              fontSize: '20px',\r\n              fontWeight: '500',\r\n              color: '#111827',\r\n              lineHeight: '1.6',\r\n              margin: 0\r\n            }}>\r\n              {questionText}\r\n            </h2>\r\n          </div>\r\n\r\n          {/* Image */}\r\n          {(question.image || question.imageUrl) && (\r\n            <div style={{ marginBottom: '32px', textAlign: 'center' }}>\r\n              <img\r\n                src={question.image || question.imageUrl}\r\n                alt=\"Question\"\r\n                style={{\r\n                  maxWidth: '100%',\r\n                  maxHeight: '400px',\r\n                  objectFit: 'contain',\r\n                  borderRadius: '12px',\r\n                  border: '1px solid #e5e7eb'\r\n                }}\r\n              />\r\n            </div>\r\n          )}\r\n\r\n          {/* Answer Options */}\r\n          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>\r\n            {answerType === \"Options\" && options.length > 0 ? (\r\n              options.map((option, index) => {\r\n                const letter = String.fromCharCode(65 + index);\r\n                const isSelected = selectedAnswer === index;\r\n\r\n                return (\r\n                  <button\r\n                    key={index}\r\n                    onClick={() => onAnswerSelect(index)}\r\n                    style={{\r\n                      width: '100%',\r\n                      textAlign: 'left',\r\n                      padding: '16px',\r\n                      borderRadius: '12px',\r\n                      border: isSelected ? '2px solid #2563eb' : '2px solid #e5e7eb',\r\n                      background: isSelected ? '#eff6ff' : 'white',\r\n                      color: isSelected ? '#1e40af' : '#111827',\r\n                      cursor: 'pointer',\r\n                      transition: 'all 0.3s',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      gap: '16px'\r\n                    }}\r\n                  >\r\n                    <div style={{\r\n                      width: '32px',\r\n                      height: '32px',\r\n                      borderRadius: '50%',\r\n                      background: isSelected ? '#2563eb' : '#f3f4f6',\r\n                      color: isSelected ? 'white' : '#6b7280',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      justifyContent: 'center',\r\n                      fontWeight: 'bold',\r\n                      fontSize: '14px'\r\n                    }}>\r\n                      {letter}\r\n                    </div>\r\n                    <span style={{ flex: 1, fontWeight: '500' }}>{option}</span>\r\n                    {isSelected && (\r\n                      <div style={{\r\n                        width: '24px',\r\n                        height: '24px',\r\n                        borderRadius: '50%',\r\n                        background: '#2563eb',\r\n                        color: 'white',\r\n                        display: 'flex',\r\n                        alignItems: 'center',\r\n                        justifyContent: 'center'\r\n                      }}>\r\n                        ✓\r\n                      </div>\r\n                    )}\r\n                  </button>\r\n                );\r\n              })\r\n            ) : (\r\n              <div>\r\n                <label style={{\r\n                  display: 'block',\r\n                  fontSize: '14px',\r\n                  fontWeight: '500',\r\n                  color: '#374151',\r\n                  marginBottom: '8px'\r\n                }}>\r\n                  Your Answer:\r\n                </label>\r\n                <input\r\n                  type=\"text\"\r\n                  value={selectedAnswer || ''}\r\n                  onChange={(e) => onAnswerSelect(e.target.value)}\r\n                  placeholder=\"Type your answer here...\"\r\n                  style={{\r\n                    width: '100%',\r\n                    padding: '16px',\r\n                    border: '2px solid #e5e7eb',\r\n                    borderRadius: '12px',\r\n                    fontSize: '16px',\r\n                    outline: 'none'\r\n                  }}\r\n                />\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Navigation */}\r\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\r\n          <button\r\n            onClick={onPrevious}\r\n            disabled={questionIndex === 0}\r\n            style={{\r\n              padding: '12px 24px',\r\n              borderRadius: '12px',\r\n              fontWeight: '600',\r\n              border: 'none',\r\n              cursor: questionIndex === 0 ? 'not-allowed' : 'pointer',\r\n              background: questionIndex === 0 ? '#e5e7eb' : '#4b5563',\r\n              color: questionIndex === 0 ? '#9ca3af' : 'white'\r\n            }}\r\n          >\r\n            ← Previous\r\n          </button>\r\n\r\n          <div style={{ textAlign: 'center' }}>\r\n            <div style={{ fontSize: '14px', color: '#6b7280', marginBottom: '8px' }}>Progress</div>\r\n            <div style={{\r\n              width: '200px',\r\n              height: '8px',\r\n              background: '#e5e7eb',\r\n              borderRadius: '4px',\r\n              overflow: 'hidden'\r\n            }}>\r\n              <div\r\n                style={{\r\n                  height: '100%',\r\n                  background: '#2563eb',\r\n                  borderRadius: '4px',\r\n                  width: ((questionIndex + 1) / totalQuestions) * 100 + '%',\r\n                  transition: 'width 0.3s'\r\n                }}\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <button\r\n            onClick={onNext}\r\n            style={{\r\n              padding: '12px 24px',\r\n              borderRadius: '12px',\r\n              fontWeight: '600',\r\n              border: 'none',\r\n              cursor: 'pointer',\r\n              background: '#2563eb',\r\n              color: 'white'\r\n            }}\r\n          >\r\n            {questionIndex === totalQuestions - 1 ? 'Submit Quiz' : 'Next →'}\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Minimal Safe Review Component\r\nconst MinimalReviewRenderer = ({\r\n  questions,\r\n  selectedOptions,\r\n  explanations,\r\n  fetchExplanation,\r\n  setView,\r\n  examData,\r\n  setSelectedQuestionIndex,\r\n  setSelectedOptions,\r\n  setResult,\r\n  setTimeUp,\r\n  setSecondsLeft,\r\n  setExplanations\r\n}) => {\r\n  if (!questions || !Array.isArray(questions)) {\r\n    return <div>No questions to review</div>;\r\n  }\r\n\r\n  return (\r\n    <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%)', padding: '24px' }}>\r\n      <div style={{ maxWidth: '800px', margin: '0 auto' }}>\r\n        {/* Header */}\r\n        <div style={{ textAlign: 'center', marginBottom: '24px' }}>\r\n          <div style={{\r\n            background: 'white',\r\n            borderRadius: '16px',\r\n            padding: '24px',\r\n            boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',\r\n            border: '1px solid #e2e8f0'\r\n          }}>\r\n            <h2 style={{ fontSize: '24px', fontWeight: 'bold', color: '#2563eb', margin: '0 0 8px 0' }}>\r\n              Answer Review\r\n            </h2>\r\n            <p style={{ color: '#64748b', margin: 0 }}>Review your answers and get explanations</p>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Questions */}\r\n        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px', marginBottom: '24px' }}>\r\n          {questions.map((question, index) => {\r\n            if (!question) return null;\r\n\r\n            const questionText = question.name ? String(question.name) : 'Question not available';\r\n            const answerType = question.answerType ? String(question.answerType) : 'Options';\r\n            const userAnswer = selectedOptions[index];\r\n\r\n            let isCorrect = false;\r\n            let correctAnswerText = 'Unknown';\r\n            let userAnswerText = 'Not answered';\r\n\r\n            if (answerType === \"Options\") {\r\n              isCorrect = question.correctOption === userAnswer;\r\n\r\n              if (question.options && question.correctOption !== undefined) {\r\n                const correctOpt = question.options[question.correctOption];\r\n                correctAnswerText = correctOpt ? String(correctOpt) : 'Unknown';\r\n              }\r\n\r\n              if (question.options && userAnswer !== undefined) {\r\n                const userOpt = question.options[userAnswer];\r\n                userAnswerText = userOpt ? String(userOpt) : 'Not answered';\r\n              }\r\n            } else {\r\n              isCorrect = question.correctAnswer === userAnswer;\r\n              correctAnswerText = question.correctAnswer ? String(question.correctAnswer) : 'Unknown';\r\n              userAnswerText = userAnswer ? String(userAnswer) : 'Not answered';\r\n            }\r\n\r\n            return (\r\n              <div\r\n                key={question._id || index}\r\n                style={{\r\n                  borderRadius: '12px',\r\n                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\r\n                  border: '2px solid ' + (isCorrect ? '#10b981' : '#ef4444'),\r\n                  padding: '16px',\r\n                  background: isCorrect ? '#f0fdf4' : '#fef2f2'\r\n                }}\r\n              >\r\n                {/* Question */}\r\n                <div style={{ marginBottom: '12px' }}>\r\n                  <div style={{ display: 'flex', alignItems: 'flex-start', gap: '12px' }}>\r\n                    <div style={{\r\n                      width: '32px',\r\n                      height: '32px',\r\n                      borderRadius: '8px',\r\n                      background: '#2563eb',\r\n                      color: 'white',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      justifyContent: 'center',\r\n                      fontWeight: 'bold',\r\n                      fontSize: '14px',\r\n                      flexShrink: 0,\r\n                      marginTop: '4px'\r\n                    }}>\r\n                      {index + 1}\r\n                    </div>\r\n                    <div style={{ flex: 1 }}>\r\n                      <p style={{\r\n                        color: '#1e293b',\r\n                        fontWeight: '500',\r\n                        lineHeight: '1.6',\r\n                        margin: 0\r\n                      }}>\r\n                        {questionText}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Your Answer */}\r\n                <div style={{ marginBottom: '8px' }}>\r\n                  <span style={{ fontSize: '14px', fontWeight: '600', color: '#64748b' }}>Your Answer: </span>\r\n                  <span style={{\r\n                    fontWeight: '500',\r\n                    color: isCorrect ? '#059669' : '#dc2626'\r\n                  }}>\r\n                    {userAnswerText}\r\n                  </span>\r\n                  <span style={{\r\n                    marginLeft: '12px',\r\n                    fontSize: '18px',\r\n                    color: isCorrect ? '#10b981' : '#ef4444'\r\n                  }}>\r\n                    {isCorrect ? '✓' : '✗'}\r\n                  </span>\r\n                </div>\r\n\r\n                {/* Correct Answer */}\r\n                {!isCorrect && (\r\n                  <div style={{ marginBottom: '8px' }}>\r\n                    <span style={{ fontSize: '14px', fontWeight: '600', color: '#64748b' }}>Correct Answer: </span>\r\n                    <span style={{ fontWeight: '500', color: '#059669' }}>{correctAnswerText}</span>\r\n                    <span style={{ marginLeft: '12px', fontSize: '18px', color: '#10b981' }}>✓</span>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Explanation Button */}\r\n                {!isCorrect && (\r\n                  <div style={{ marginTop: '8px' }}>\r\n                    <button\r\n                      onClick={() => {\r\n                        fetchExplanation(\r\n                          questionText,\r\n                          correctAnswerText,\r\n                          userAnswerText,\r\n                          question.image || question.imageUrl || ''\r\n                        );\r\n                      }}\r\n                      style={{\r\n                        padding: '8px 16px',\r\n                        background: '#2563eb',\r\n                        color: 'white',\r\n                        border: 'none',\r\n                        borderRadius: '8px',\r\n                        fontSize: '14px',\r\n                        fontWeight: '500',\r\n                        cursor: 'pointer',\r\n                        display: 'flex',\r\n                        alignItems: 'center',\r\n                        gap: '8px'\r\n                      }}\r\n                    >\r\n                      <span>💡</span>\r\n                      <span>Get Explanation</span>\r\n                    </button>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Explanation */}\r\n                {explanations[questionText] && (\r\n                  <div style={{\r\n                    marginTop: '12px',\r\n                    padding: '12px',\r\n                    background: 'white',\r\n                    borderRadius: '8px',\r\n                    borderLeft: '4px solid #2563eb',\r\n                    boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',\r\n                    border: '1px solid #e5e7eb'\r\n                  }}>\r\n                    <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>\r\n                      <span style={{ fontSize: '18px', marginRight: '8px' }}>💡</span>\r\n                      <h6 style={{ fontWeight: 'bold', color: '#1f2937', fontSize: '16px', margin: 0 }}>\r\n                        Explanation\r\n                      </h6>\r\n                    </div>\r\n\r\n                    {/* Image */}\r\n                    {(question.image || question.imageUrl) && (\r\n                      <div style={{\r\n                        marginBottom: '12px',\r\n                        padding: '8px',\r\n                        background: '#f9fafb',\r\n                        borderRadius: '6px',\r\n                        border: '1px solid #e5e7eb'\r\n                      }}>\r\n                        <div style={{ marginBottom: '4px' }}>\r\n                          <span style={{ color: '#374151', fontSize: '14px', fontWeight: '500' }}>\r\n                            📊 Reference Diagram:\r\n                          </span>\r\n                        </div>\r\n                        <div style={{ textAlign: 'center' }}>\r\n                          <img\r\n                            src={question.image || question.imageUrl}\r\n                            alt=\"Question diagram\"\r\n                            style={{\r\n                              maxWidth: '100%',\r\n                              maxHeight: '200px',\r\n                              objectFit: 'contain',\r\n                              borderRadius: '6px',\r\n                              border: '1px solid #d1d5db'\r\n                            }}\r\n                          />\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n\r\n                    <div style={{\r\n                      fontSize: '14px',\r\n                      color: '#1f2937',\r\n                      lineHeight: '1.6',\r\n                      background: '#f9fafb',\r\n                      padding: '8px',\r\n                      borderRadius: '6px'\r\n                    }}>\r\n                      {explanations[questionText] ? String(explanations[questionText]) : ''}\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            );\r\n          })}\r\n        </div>\r\n\r\n        {/* Navigation */}\r\n        <div style={{\r\n          display: 'flex',\r\n          flexDirection: window.innerWidth < 640 ? 'column' : 'row',\r\n          gap: '16px',\r\n          justifyContent: 'center',\r\n          alignItems: 'center'\r\n        }}>\r\n          <button\r\n            onClick={() => setView(\"result\")}\r\n            style={{\r\n              padding: '16px 32px',\r\n              background: '#4b5563',\r\n              color: 'white',\r\n              border: 'none',\r\n              borderRadius: '12px',\r\n              fontWeight: 'bold',\r\n              cursor: 'pointer',\r\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'\r\n            }}\r\n          >\r\n            ← Back to Results\r\n          </button>\r\n\r\n          <button\r\n            onClick={() => {\r\n              setView(\"instructions\");\r\n              setSelectedQuestionIndex(0);\r\n              setSelectedOptions({});\r\n              setResult({});\r\n              setTimeUp(false);\r\n              setSecondsLeft(examData?.duration || 0);\r\n              setExplanations({});\r\n            }}\r\n            style={{\r\n              padding: '16px 32px',\r\n              background: '#059669',\r\n              color: 'white',\r\n              border: 'none',\r\n              borderRadius: '12px',\r\n              fontWeight: 'bold',\r\n              cursor: 'pointer',\r\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'\r\n            }}\r\n          >\r\n            🔄 Retake Quiz\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nfunction WriteExam() {\r\n  const { isKiswahili } = useLanguage();\r\n  const [examData, setExamData] = useState(null);\r\n  const [questions, setQuestions] = useState([]);\r\n  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);\r\n  const [selectedOptions, setSelectedOptions] = useState({});\r\n  const [result, setResult] = useState({});\r\n  const params = useParams();\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const [view, setView] = useState(\"instructions\");\r\n  const [secondsLeft, setSecondsLeft] = useState(0);\r\n  const [timeUp, setTimeUp] = useState(false);\r\n  const [intervalId, setIntervalId] = useState(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [startTime, setStartTime] = useState(null);\r\n  const { user } = useSelector((state) => state.user);\r\n\r\n  const { width, height } = useWindowSize();\r\n  const [explanations, setExplanations] = useState({});\r\n\r\n  const getExamData = useCallback(async () => {\r\n    try {\r\n      setIsLoading(true);\r\n      dispatch(ShowLoading());\r\n      console.log(\"Fetching exam data for ID:\", params.id);\r\n\r\n      const response = await getExamById({ examId: params.id });\r\n      console.log(\"Exam API Response:\", response);\r\n\r\n      dispatch(HideLoading());\r\n      setIsLoading(false);\r\n\r\n      if (response.success) {\r\n        const examData = response.data;\r\n\r\n        // Check different possible question locations\r\n        let questions = [];\r\n        if (examData?.questions && Array.isArray(examData.questions)) {\r\n          questions = examData.questions;\r\n        } else if (examData?.question && Array.isArray(examData.question)) {\r\n          questions = examData.question;\r\n        } else if (examData && Array.isArray(examData)) {\r\n          questions = examData;\r\n        }\r\n\r\n        console.log(\"Exam Data:\", examData);\r\n        console.log(\"Questions found:\", questions.length);\r\n        console.log(\"Exam Data structure:\", Object.keys(examData || {}));\r\n\r\n        setQuestions(questions);\r\n        setExamData(examData);\r\n        setSecondsLeft(examData?.duration || 0);\r\n\r\n        if (questions.length === 0) {\r\n          console.warn(\"No questions found in exam data\");\r\n          console.log(\"Full response for debugging:\", response);\r\n          message.warning(\"This exam has no questions. Please contact your instructor.\");\r\n        }\r\n      } else {\r\n        console.error(\"API Error:\", response.message);\r\n        console.log(\"Full error response:\", response);\r\n        message.error(response.message || \"Failed to load exam data\");\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      setIsLoading(false);\r\n      console.error(\"Exception in getExamData:\", error);\r\n      message.error(error.message || \"Failed to load exam. Please try again.\");\r\n    }\r\n  }, [params.id, dispatch]);\r\n\r\n  const checkFreeTextAnswers = async (payload) => {\r\n    if (!payload.length) return [];\r\n    const { data } = await chatWithChatGPTToGetAns(payload);\r\n    return data;\r\n  };\r\n\r\n  const calculateResult = useCallback(async () => {\r\n    try {\r\n      // Check if user is available\r\n      if (!user || !user._id) {\r\n        message.error(\"User not found. Please log in again.\");\r\n        navigate(\"/login\");\r\n        return;\r\n      }\r\n\r\n      dispatch(ShowLoading());\r\n\r\n      const freeTextPayload = [];\r\n      const indexMap = [];\r\n\r\n      questions.forEach((q, idx) => {\r\n        if (q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\r\n          indexMap.push(idx);\r\n          freeTextPayload.push({\r\n            question: q.name,\r\n            expectedAnswer: q.correctAnswer || q.correctOption,\r\n            userAnswer: selectedOptions[idx] || \"\",\r\n          });\r\n        }\r\n      });\r\n\r\n      const gptResults = await checkFreeTextAnswers(freeTextPayload);\r\n      const gptMap = {};\r\n\r\n      gptResults.forEach((r) => {\r\n        if (r.result && typeof r.result.isCorrect === \"boolean\") {\r\n          gptMap[r.question] = r.result;\r\n        } else if (typeof r.isCorrect === \"boolean\") {\r\n          gptMap[r.question] = { isCorrect: r.isCorrect, reason: r.reason || \"\" };\r\n        }\r\n      });\r\n\r\n      const correctAnswers = [];\r\n      const wrongAnswers = [];\r\n      const wrongPayload = [];\r\n\r\n      questions.forEach((q, idx) => {\r\n        const userAnswerKey = selectedOptions[idx] || \"\";\r\n\r\n        if (q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\r\n          const { isCorrect = false, reason = \"\" } = gptMap[q.name] || {};\r\n          const enriched = { ...q, userAnswer: userAnswerKey, reason };\r\n\r\n          if (isCorrect) {\r\n            correctAnswers.push(enriched);\r\n          } else {\r\n            wrongAnswers.push(enriched);\r\n            wrongPayload.push({\r\n              question: q.name,\r\n              expectedAnswer: q.correctAnswer || q.correctOption,\r\n              userAnswer: userAnswerKey,\r\n            });\r\n          }\r\n        } else if (q.answerType === \"Options\") {\r\n          const correctKey = q.correctOption;\r\n          const correctValue = (q.options && q.options[correctKey]) || correctKey;\r\n          const userValue = (q.options && q.options[userAnswerKey]) || userAnswerKey || \"\";\r\n\r\n          const isCorrect = correctKey === userAnswerKey;\r\n          const enriched = { ...q, userAnswer: userAnswerKey };\r\n\r\n          if (isCorrect) {\r\n            correctAnswers.push(enriched);\r\n          } else {\r\n            wrongAnswers.push(enriched);\r\n            wrongPayload.push({\r\n              question: q.name,\r\n              expectedAnswer: correctValue,\r\n              userAnswer: userValue,\r\n            });\r\n          }\r\n        }\r\n      });\r\n\r\n      // Calculate time spent\r\n      const timeSpent = startTime ? Math.floor((Date.now() - startTime) / 1000) : 0;\r\n      const totalTimeAllowed = (examData?.duration || 0) * 60; // Convert minutes to seconds\r\n\r\n      // Calculate score and points\r\n      const totalQuestions = questions.length;\r\n      const correctCount = correctAnswers.length;\r\n      const scorePercentage = Math.round((correctCount / totalQuestions) * 100);\r\n      const points = correctCount * 10; // 10 points per correct answer\r\n\r\n      // Determine pass/fail based on percentage\r\n      const passingPercentage = examData.passingMarks || 70; // Default 70%\r\n      const verdict = scorePercentage >= passingPercentage ? \"Pass\" : \"Fail\";\r\n\r\n      const tempResult = {\r\n        correctAnswers: correctAnswers || [],\r\n        wrongAnswers: wrongAnswers || [],\r\n        verdict: verdict || \"Fail\",\r\n        score: scorePercentage,\r\n        points: points,\r\n        totalQuestions: totalQuestions,\r\n        timeSpent: timeSpent,\r\n        totalTimeAllowed: totalTimeAllowed\r\n      };\r\n\r\n      setResult(tempResult);\r\n\r\n      const response = await addReport({\r\n        exam: params.id,\r\n        result: tempResult,\r\n        user: user._id,\r\n      });\r\n\r\n      if (response.success) {\r\n        // Include XP data in the result\r\n        const resultWithXP = {\r\n          ...tempResult,\r\n          xpData: response.xpData\r\n        };\r\n        setResult(resultWithXP);\r\n\r\n        setView(\"result\");\r\n        window.scrollTo(0, 0);\r\n        new Audio(verdict === \"Pass\" ? PassSound : FailSound).play();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  }, [questions, selectedOptions, examData, params.id, user, navigate, dispatch]);\r\n\r\n  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await chatWithChatGPTToExplainAns({ question, expectedAnswer, userAnswer, imageUrl });\r\n      dispatch(HideLoading());\r\n\r\n      if (response.success) {\r\n        setExplanations((prev) => ({ ...prev, [question]: response.explanation }));\r\n      } else {\r\n        message.error(response.error || \"Failed to fetch explanation.\");\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const startTimer = () => {\r\n    const totalSeconds = examData?.duration || 0; // Duration is already in seconds\r\n    setSecondsLeft(totalSeconds);\r\n    setStartTime(Date.now()); // Record start time for XP calculation\r\n\r\n    const newIntervalId = setInterval(() => {\r\n      setSecondsLeft((prevSeconds) => {\r\n        if (prevSeconds > 0) {\r\n          return prevSeconds - 1;\r\n        } else {\r\n          setTimeUp(true);\r\n          return 0;\r\n        }\r\n      });\r\n    }, 1000);\r\n    setIntervalId(newIntervalId);\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (timeUp && view === \"questions\") {\r\n      clearInterval(intervalId);\r\n      calculateResult();\r\n    }\r\n  }, [timeUp, view, intervalId, calculateResult]);\r\n\r\n  useEffect(() => {\r\n    console.log(\"WriteExam useEffect - params.id:\", params.id);\r\n    if (params.id) {\r\n      getExamData();\r\n    } else {\r\n      console.error(\"No exam ID provided in URL parameters\");\r\n      message.error(\"Invalid exam ID. Please select a quiz from the list.\");\r\n      navigate('/user/quiz');\r\n    }\r\n  }, [params.id, getExamData, navigate]);\r\n\r\n  useEffect(() => {\r\n    return () => {\r\n      if (intervalId) {\r\n        clearInterval(intervalId);\r\n      }\r\n    };\r\n  }, [intervalId]);\r\n\r\n  // Add fullscreen class for all quiz views (instructions, questions, results)\r\n  useEffect(() => {\r\n    if (view === \"instructions\" || view === \"questions\" || view === \"result\") {\r\n      document.body.classList.add(\"quiz-fullscreen\");\r\n    } else {\r\n      document.body.classList.remove(\"quiz-fullscreen\");\r\n    }\r\n\r\n    // Cleanup on unmount\r\n    return () => {\r\n      document.body.classList.remove(\"quiz-fullscreen\");\r\n    };\r\n  }, [view]);\r\n\r\n  // Repair function for fixing orphaned questions\r\n  const repairExamQuestions = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await fetch('/api/exams/repair-exam-questions', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\r\n        },\r\n        body: JSON.stringify({ examId: params.id })\r\n      });\r\n\r\n      const data = await response.json();\r\n      if (data.success) {\r\n        message.success(data.message);\r\n        // Reload the exam data\r\n        getExamData();\r\n      } else {\r\n        message.error(data.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(\"Failed to repair exam questions\");\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  // Check if user is authenticated\r\n  if (!user) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex justify-center items-center\">\r\n        <div className=\"bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl border border-blue-100 p-12 text-center max-w-md mx-4\">\r\n          <div className=\"w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6\">\r\n            <svg className=\"w-10 h-10 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n              <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z\" clipRule=\"evenodd\" />\r\n            </svg>\r\n          </div>\r\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">Authentication Required</h2>\r\n          <p className=\"text-gray-600 mb-8\">Please log in to access the exam and start your learning journey.</p>\r\n          <button\r\n            className=\"w-full px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-indigo-700 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n            onClick={() => navigate(\"/login\")}\r\n          >\r\n            Go to Login\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return examData ? (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\r\n\r\n      {view === \"instructions\" && (\r\n        <Instructions\r\n          examData={examData}\r\n          setView={setView}\r\n          startTimer={startTimer}\r\n          questions={questions}\r\n        />\r\n      )}\r\n\r\n      {view === \"questions\" && (\r\n        isLoading ? (\r\n          <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center\">\r\n            <div className=\"bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-blue-200 max-w-lg mx-4 text-center\">\r\n              <div className=\"w-24 h-24 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg animate-pulse\">\r\n                <svg className=\"w-12 h-12 text-white animate-spin\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                  <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                  <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                </svg>\r\n              </div>\r\n              <h3 className=\"text-2xl font-bold text-blue-800 mb-4\">Loading Quiz...</h3>\r\n              <p className=\"text-blue-600 text-lg\">\r\n                Please wait while we prepare your questions.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        ) : questions.length === 0 ? (\r\n          <div className=\"min-h-screen bg-gradient-to-br from-amber-50 via-white to-orange-50 flex items-center justify-center\">\r\n            <div className=\"bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-amber-200 max-w-lg mx-4 text-center\">\r\n              <div className=\"w-24 h-24 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg\">\r\n                <svg className=\"w-12 h-12 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                  <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\r\n                </svg>\r\n              </div>\r\n              <h3 className=\"text-2xl font-bold text-amber-800 mb-4\">No Questions Found</h3>\r\n              <p className=\"text-amber-700 mb-6 text-lg leading-relaxed\">\r\n                This exam appears to have no questions. This could be due to:\r\n              </p>\r\n              <ul className=\"text-left text-amber-700 mb-8 space-y-2\">\r\n                <li>• Questions not properly linked to this exam</li>\r\n                <li>• Database connection issues</li>\r\n                <li>• Exam configuration problems</li>\r\n              </ul>\r\n              <div className=\"space-y-3\">\r\n                <button\r\n                  onClick={repairExamQuestions}\r\n                  className=\"w-full px-8 py-4 bg-gradient-to-r from-amber-500 to-orange-500 text-white rounded-xl font-bold text-lg hover:from-amber-600 hover:to-orange-600 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n                >\r\n                  🔧 Repair Questions\r\n                </button>\r\n                <button\r\n                  onClick={() => {\r\n                    console.log(\"Retrying exam data fetch...\");\r\n                    getExamData();\r\n                  }}\r\n                  className=\"w-full px-8 py-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl font-bold text-lg hover:from-blue-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n                >\r\n                  🔄 Retry Loading\r\n                </button>\r\n                <button\r\n                  onClick={() => navigate('/user/quiz')}\r\n                  className=\"w-full px-8 py-4 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-xl font-bold text-lg hover:from-gray-600 hover:to-gray-700 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n                >\r\n                  ← Back to Quiz List\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          <MinimalQuizRenderer\r\n            question={questions[selectedQuestionIndex]}\r\n            questionIndex={selectedQuestionIndex}\r\n            totalQuestions={questions.length}\r\n            selectedAnswer={selectedOptions[selectedQuestionIndex]}\r\n            onAnswerSelect={(answer) => setSelectedOptions({...selectedOptions, [selectedQuestionIndex]: answer})}\r\n            onNext={() => {\r\n              if (selectedQuestionIndex === questions.length - 1) {\r\n                calculateResult();\r\n              } else {\r\n                setSelectedQuestionIndex(selectedQuestionIndex + 1);\r\n              }\r\n            }}\r\n            onPrevious={() => {\r\n              if (selectedQuestionIndex > 0) {\r\n                setSelectedQuestionIndex(selectedQuestionIndex - 1);\r\n              }\r\n            }}\r\n            timeLeft={secondsLeft}\r\n            examTitle={examData?.name || \"Quiz\"}\r\n          />\r\n        )\r\n      )}\r\n\r\n      {view === \"result\" && (\r\n        <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-8\">\r\n          {result.verdict === \"Pass\" && <Confetti width={width} height={height} />}\r\n\r\n          <div className=\"max-w-4xl mx-auto px-4\">\r\n            <div className=\"bg-white/95 backdrop-blur-md rounded-2xl shadow-xl border border-slate-200/50 overflow-hidden\">\r\n              {/* Modern Header */}\r\n              <div className={`px-8 py-10 text-center relative ${\r\n                result.verdict === \"Pass\"\r\n                  ? \"bg-gradient-to-br from-emerald-500/10 via-green-500/5 to-teal-500/10\"\r\n                  : \"bg-gradient-to-br from-amber-500/10 via-orange-500/5 to-red-500/10\"\r\n              }`}>\r\n                <div className=\"relative\">\r\n                  <div className={`w-20 h-20 mx-auto mb-6 rounded-2xl flex items-center justify-center shadow-lg ${\r\n                    result.verdict === \"Pass\"\r\n                      ? \"bg-gradient-to-br from-emerald-500 to-green-600\"\r\n                      : \"bg-gradient-to-br from-amber-500 to-orange-600\"\r\n                  }`}>\r\n                    <img\r\n                      src={result.verdict === \"Pass\" ? Pass : Fail}\r\n                      alt={result.verdict}\r\n                      className=\"w-12 h-12 object-contain\"\r\n                    />\r\n                  </div>\r\n                  <h1 className={`text-4xl font-black mb-4 tracking-tight ${\r\n                    result.verdict === \"Pass\" ? \"text-emerald-700\" : \"text-amber-700\"\r\n                  }`}>\r\n                    {result.verdict === \"Pass\" ? \"Excellent Work!\" : \"Keep Pushing!\"}\r\n                  </h1>\r\n                  <p className=\"text-xl text-slate-600 font-medium max-w-md mx-auto leading-relaxed\">\r\n                    {result.verdict === \"Pass\"\r\n                      ? \"You've mastered this exam with flying colors!\"\r\n                      : \"Every challenge makes you stronger. Try again!\"}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Modern Statistics Cards */}\r\n              <div className=\"p-8\">\r\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\r\n                  {/* Score Card */}\r\n                  <div className=\"group relative overflow-hidden bg-gradient-to-br from-blue-500/5 to-indigo-500/10 rounded-2xl border border-blue-200/50 p-6 hover:shadow-lg transition-all duration-300\">\r\n                    <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                    <div className=\"relative text-center\">\r\n                      <div className=\"text-4xl font-black text-blue-600 mb-2 tracking-tight\">\r\n                        {Math.round(((result.correctAnswers?.length || 0) / questions.length) * 100)}%\r\n                      </div>\r\n                      <div className=\"text-sm font-bold text-blue-700/80 uppercase tracking-wider\">Your Score</div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Correct vs Total */}\r\n                  <div className=\"group relative overflow-hidden bg-gradient-to-br from-emerald-500/5 to-green-500/10 rounded-2xl border border-emerald-200/50 p-6 hover:shadow-lg transition-all duration-300\">\r\n                    <div className=\"absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                    <div className=\"relative text-center\">\r\n                      <div className=\"text-4xl font-black text-emerald-600 mb-2 tracking-tight\">\r\n                        {Array.isArray(result.correctAnswers) ? result.correctAnswers.length : 0}/{questions.length}\r\n                      </div>\r\n                      <div className=\"text-sm font-bold text-emerald-700/80 uppercase tracking-wider\">Correct</div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Pass Status */}\r\n                  <div className={`group relative overflow-hidden rounded-2xl border p-6 hover:shadow-lg transition-all duration-300 ${\r\n                    result.verdict === \"Pass\"\r\n                      ? \"bg-gradient-to-br from-emerald-500/5 to-green-500/10 border-emerald-200/50\"\r\n                      : \"bg-gradient-to-br from-amber-500/5 to-orange-500/10 border-amber-200/50\"\r\n                  }`}>\r\n                    <div className={`absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-100 transition-opacity duration-300 ${\r\n                      result.verdict === \"Pass\" ? \"from-emerald-500/5\" : \"from-amber-500/5\"\r\n                    } to-transparent`}></div>\r\n                    <div className=\"relative text-center\">\r\n                      <div className={`text-4xl font-black mb-2 tracking-tight ${\r\n                        result.verdict === \"Pass\" ? \"text-emerald-600\" : \"text-amber-600\"\r\n                      }`}>\r\n                        {result.verdict === \"Pass\" ? \"PASS\" : \"RETRY\"}\r\n                      </div>\r\n                      <div className={`text-sm font-bold uppercase tracking-wider ${\r\n                        result.verdict === \"Pass\" ? \"text-emerald-700/80\" : \"text-amber-700/80\"\r\n                      }`}>\r\n                        {result.verdict === \"Pass\" ? \"Success!\" : `Need ${examData.passingMarks}`}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Modern Progress Visualization */}\r\n                <div className=\"mb-8\">\r\n                  <div className=\"relative bg-slate-100 rounded-2xl p-6\">\r\n                    <div className=\"text-center mb-4\">\r\n                      <h3 className=\"text-lg font-bold text-slate-700 mb-1\">Performance Overview</h3>\r\n                      <p className=\"text-sm text-slate-500\">Your achievement level</p>\r\n                    </div>\r\n                    <div className=\"relative\">\r\n                      <div className=\"w-full bg-slate-200 rounded-full h-4 shadow-inner overflow-hidden\">\r\n                        <div\r\n                          className={`h-full rounded-full transition-all duration-1000 shadow-sm relative overflow-hidden ${\r\n                            result.verdict === \"Pass\"\r\n                              ? \"bg-gradient-to-r from-emerald-500 via-green-500 to-teal-500\"\r\n                              : \"bg-gradient-to-r from-amber-500 via-orange-500 to-red-500\"\r\n                          }`}\r\n                          style={{ width: `${((result.correctAnswers?.length || 0) / questions.length) * 100}%` }}\r\n                        >\r\n                          <div className=\"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent\"></div>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"flex justify-between items-center mt-3\">\r\n                        <span className=\"text-xs font-medium text-slate-500\">0%</span>\r\n                        <span className={`text-lg font-black tracking-tight ${\r\n                          result.verdict === \"Pass\" ? \"text-emerald-600\" : \"text-amber-600\"\r\n                        }`}>\r\n                          {Math.round(((Array.isArray(result.correctAnswers) ? result.correctAnswers.length : 0) / questions.length) * 100)}%\r\n                        </span>\r\n                        <span className=\"text-xs font-medium text-slate-500\">100%</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* XP Display */}\r\n                {result.xpData && (\r\n                  <div className=\"mb-8\">\r\n                    <XPResultDisplay xpData={result.xpData} />\r\n                  </div>\r\n                )}\r\n\r\n                {/* Modern Action Buttons */}\r\n                <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\r\n                  <button\r\n                    className=\"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\r\n                    onClick={() => setView(\"review\")}\r\n                  >\r\n                    <div className=\"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                    <span className=\"relative\">Review Answers</span>\r\n                  </button>\r\n\r\n\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {view === \"review\" && (\r\n        <MinimalReviewRenderer\r\n          questions={questions}\r\n          selectedOptions={selectedOptions}\r\n          explanations={explanations}\r\n          fetchExplanation={fetchExplanation}\r\n          setView={setView}\r\n          examData={examData}\r\n          setSelectedQuestionIndex={setSelectedQuestionIndex}\r\n          setSelectedOptions={setSelectedOptions}\r\n          setResult={setResult}\r\n          setTimeUp={setTimeUp}\r\n          setSecondsLeft={setSecondsLeft}\r\n          setExplanations={setExplanations}\r\n        />\r\n      )}\r\n    </div>\r\n  ) : null;\r\n}\r\n\r\nexport default WriteExam;"], "mappings": ";;AAAA,SAASA,OAAO,QAAQ,MAAM;AAC9B,OAAOC,KAAK,IAAIC,WAAW,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC/D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,SAAS,QAAQ,2BAA2B;AACrD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAOC,SAAS,MAAM,0BAA0B;AAChD,SAASC,uBAAuB,EAAEC,2BAA2B,QAAQ,wBAAwB;AAC7F,SAASC,WAAW,QAAQ,mCAAmC;AAC/D,OAAOC,eAAe,MAAM,4CAA4C;AACxE,SAASC,qBAAqB,EAAEC,UAAU,QAAQ,8BAA8B;;AAEhF;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,mBAAmB,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,aAAa;EAAEC,cAAc;EAAEC,cAAc;EAAEC,cAAc;EAAEC,MAAM;EAAEC,UAAU;EAAEC,QAAQ;EAAEC;AAAU,CAAC,KAAK;EACpJ;EACA,IAAI,CAACR,QAAQ,EAAE;IACb,oBAAOF,OAAA;MAAAW,QAAA,EAAK;IAAmB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACvC;;EAEA;EACA,MAAMC,YAAY,GAAGd,QAAQ,CAACe,IAAI,GAAGC,MAAM,CAAChB,QAAQ,CAACe,IAAI,CAAC,GAAG,6BAA6B;EAC1F,MAAME,UAAU,GAAGjB,QAAQ,CAACiB,UAAU,GAAGD,MAAM,CAAChB,QAAQ,CAACiB,UAAU,CAAC,GAAG,SAAS;;EAEhF;EACA,IAAIC,OAAO,GAAG,EAAE;EAChB,IAAIlB,QAAQ,CAACkB,OAAO,EAAE;IACpB,IAAIC,KAAK,CAACC,OAAO,CAACpB,QAAQ,CAACkB,OAAO,CAAC,EAAE;MACnCA,OAAO,GAAGlB,QAAQ,CAACkB,OAAO,CAACG,GAAG,CAACC,GAAG,IAAIN,MAAM,CAACM,GAAG,IAAI,EAAE,CAAC,CAAC;IAC1D,CAAC,MAAM,IAAI,OAAOtB,QAAQ,CAACkB,OAAO,KAAK,QAAQ,EAAE;MAC/CA,OAAO,GAAGK,MAAM,CAACC,MAAM,CAACxB,QAAQ,CAACkB,OAAO,CAAC,CAACG,GAAG,CAACC,GAAG,IAAIN,MAAM,CAACM,GAAG,IAAI,EAAE,CAAC,CAAC;IACzE;EACF;EAEA,MAAMG,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACrC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,OAAOC,IAAI,GAAG,GAAG,IAAIG,IAAI,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,GAAGA,IAAI;EACnD,CAAC;EAED,oBACEhC,OAAA;IAAKiC,KAAK,EAAE;MAAEC,SAAS,EAAE,OAAO;MAAEC,UAAU,EAAE;IAAoD,CAAE;IAAAxB,QAAA,gBAElGX,OAAA;MAAKiC,KAAK,EAAE;QACVE,UAAU,EAAE,OAAO;QACnBC,YAAY,EAAE,mBAAmB;QACjCC,OAAO,EAAE,MAAM;QACfC,QAAQ,EAAE,QAAQ;QAClBC,GAAG,EAAE,CAAC;QACNC,MAAM,EAAE;MACV,CAAE;MAAA7B,QAAA,eACAX,OAAA;QAAKiC,KAAK,EAAE;UAAEQ,QAAQ,EAAE,QAAQ;UAAEC,MAAM,EAAE,QAAQ;UAAEC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAlC,QAAA,gBAC3HX,OAAA;UAAAW,QAAA,gBACEX,OAAA;YAAIiC,KAAK,EAAE;cAAEa,QAAQ,EAAE,MAAM;cAAEC,UAAU,EAAE,KAAK;cAAEC,KAAK,EAAE,SAAS;cAAEN,MAAM,EAAE;YAAE,CAAE;YAAA/B,QAAA,EAC7ED,SAAS,GAAGQ,MAAM,CAACR,SAAS,CAAC,GAAG;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACLf,OAAA;YAAGiC,KAAK,EAAE;cAAEa,QAAQ,EAAE,MAAM;cAAEE,KAAK,EAAE,SAAS;cAAEN,MAAM,EAAE;YAAE,CAAE;YAAA/B,QAAA,GAAC,WAClD,EAACR,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;UAAA;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENf,OAAA;UAAKiC,KAAK,EAAE;YACVE,UAAU,EAAE1B,QAAQ,IAAI,EAAE,GAAG,SAAS,GAAG,SAAS;YAClDuC,KAAK,EAAE,OAAO;YACdX,OAAO,EAAE,WAAW;YACpBY,YAAY,EAAE,MAAM;YACpBC,UAAU,EAAE,WAAW;YACvBH,UAAU,EAAE;UACd,CAAE;UAAApC,QAAA,GAAC,QACK,EAACgB,UAAU,CAAClB,QAAQ,CAAC;QAAA;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNf,OAAA;MAAKiC,KAAK,EAAE;QAAEQ,QAAQ,EAAE,OAAO;QAAEC,MAAM,EAAE,QAAQ;QAAEL,OAAO,EAAE;MAAY,CAAE;MAAA1B,QAAA,gBACxEX,OAAA;QAAKiC,KAAK,EAAE;UACVE,UAAU,EAAE,OAAO;UACnBc,YAAY,EAAE,MAAM;UACpBE,SAAS,EAAE,uCAAuC;UAClDd,OAAO,EAAE,MAAM;UACfe,YAAY,EAAE;QAChB,CAAE;QAAAzC,QAAA,gBAEAX,OAAA;UAAKiC,KAAK,EAAE;YAAEmB,YAAY,EAAE;UAAO,CAAE;UAAAzC,QAAA,eACnCX,OAAA;YAAMiC,KAAK,EAAE;cACXE,UAAU,EAAE,SAAS;cACrBa,KAAK,EAAE,SAAS;cAChBX,OAAO,EAAE,UAAU;cACnBY,YAAY,EAAE,MAAM;cACpBH,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE;YACd,CAAE;YAAApC,QAAA,GAAC,WACQ,EAACR,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;UAAA;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGNf,OAAA;UAAKiC,KAAK,EAAE;YAAEmB,YAAY,EAAE;UAAO,CAAE;UAAAzC,QAAA,eACnCX,OAAA;YAAIiC,KAAK,EAAE;cACTa,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE,KAAK;cACjBC,KAAK,EAAE,SAAS;cAChBK,UAAU,EAAE,KAAK;cACjBX,MAAM,EAAE;YACV,CAAE;YAAA/B,QAAA,EACCK;UAAY;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,EAGL,CAACb,QAAQ,CAACoD,KAAK,IAAIpD,QAAQ,CAACqD,QAAQ,kBACnCvD,OAAA;UAAKiC,KAAK,EAAE;YAAEmB,YAAY,EAAE,MAAM;YAAEI,SAAS,EAAE;UAAS,CAAE;UAAA7C,QAAA,eACxDX,OAAA;YACEyD,GAAG,EAAEvD,QAAQ,CAACoD,KAAK,IAAIpD,QAAQ,CAACqD,QAAS;YACzCG,GAAG,EAAC,UAAU;YACdzB,KAAK,EAAE;cACLQ,QAAQ,EAAE,MAAM;cAChBkB,SAAS,EAAE,OAAO;cAClBC,SAAS,EAAE,SAAS;cACpBX,YAAY,EAAE,MAAM;cACpBY,MAAM,EAAE;YACV;UAAE;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAGDf,OAAA;UAAKiC,KAAK,EAAE;YAAEU,OAAO,EAAE,MAAM;YAAEmB,aAAa,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAO,CAAE;UAAApD,QAAA,EACnEQ,UAAU,KAAK,SAAS,IAAIC,OAAO,CAAC4C,MAAM,GAAG,CAAC,GAC7C5C,OAAO,CAACG,GAAG,CAAC,CAAC0C,MAAM,EAAEC,KAAK,KAAK;YAC7B,MAAMC,MAAM,GAAGjD,MAAM,CAACkD,YAAY,CAAC,EAAE,GAAGF,KAAK,CAAC;YAC9C,MAAMG,UAAU,GAAGhE,cAAc,KAAK6D,KAAK;YAE3C,oBACElE,OAAA;cAEEsE,OAAO,EAAEA,CAAA,KAAMhE,cAAc,CAAC4D,KAAK,CAAE;cACrCjC,KAAK,EAAE;gBACLsC,KAAK,EAAE,MAAM;gBACbf,SAAS,EAAE,MAAM;gBACjBnB,OAAO,EAAE,MAAM;gBACfY,YAAY,EAAE,MAAM;gBACpBY,MAAM,EAAEQ,UAAU,GAAG,mBAAmB,GAAG,mBAAmB;gBAC9DlC,UAAU,EAAEkC,UAAU,GAAG,SAAS,GAAG,OAAO;gBAC5CrB,KAAK,EAAEqB,UAAU,GAAG,SAAS,GAAG,SAAS;gBACzCG,MAAM,EAAE,SAAS;gBACjBC,UAAU,EAAE,UAAU;gBACtB9B,OAAO,EAAE,MAAM;gBACfE,UAAU,EAAE,QAAQ;gBACpBkB,GAAG,EAAE;cACP,CAAE;cAAApD,QAAA,gBAEFX,OAAA;gBAAKiC,KAAK,EAAE;kBACVsC,KAAK,EAAE,MAAM;kBACbG,MAAM,EAAE,MAAM;kBACdzB,YAAY,EAAE,KAAK;kBACnBd,UAAU,EAAEkC,UAAU,GAAG,SAAS,GAAG,SAAS;kBAC9CrB,KAAK,EAAEqB,UAAU,GAAG,OAAO,GAAG,SAAS;kBACvC1B,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBD,cAAc,EAAE,QAAQ;kBACxBG,UAAU,EAAE,MAAM;kBAClBD,QAAQ,EAAE;gBACZ,CAAE;gBAAAnC,QAAA,EACCwD;cAAM;gBAAAvD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNf,OAAA;gBAAMiC,KAAK,EAAE;kBAAE0C,IAAI,EAAE,CAAC;kBAAE5B,UAAU,EAAE;gBAAM,CAAE;gBAAApC,QAAA,EAAEsD;cAAM;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAC3DsD,UAAU,iBACTrE,OAAA;gBAAKiC,KAAK,EAAE;kBACVsC,KAAK,EAAE,MAAM;kBACbG,MAAM,EAAE,MAAM;kBACdzB,YAAY,EAAE,KAAK;kBACnBd,UAAU,EAAE,SAAS;kBACrBa,KAAK,EAAE,OAAO;kBACdL,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBD,cAAc,EAAE;gBAClB,CAAE;gBAAAjC,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA,GA7CImD,KAAK;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8CJ,CAAC;UAEb,CAAC,CAAC,gBAEFf,OAAA;YAAAW,QAAA,gBACEX,OAAA;cAAOiC,KAAK,EAAE;gBACZU,OAAO,EAAE,OAAO;gBAChBG,QAAQ,EAAE,MAAM;gBAChBC,UAAU,EAAE,KAAK;gBACjBC,KAAK,EAAE,SAAS;gBAChBI,YAAY,EAAE;cAChB,CAAE;cAAAzC,QAAA,EAAC;YAEH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRf,OAAA;cACE4E,IAAI,EAAC,MAAM;cACXC,KAAK,EAAExE,cAAc,IAAI,EAAG;cAC5ByE,QAAQ,EAAGC,CAAC,IAAKzE,cAAc,CAACyE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAChDI,WAAW,EAAC,0BAA0B;cACtChD,KAAK,EAAE;gBACLsC,KAAK,EAAE,MAAM;gBACblC,OAAO,EAAE,MAAM;gBACfwB,MAAM,EAAE,mBAAmB;gBAC3BZ,YAAY,EAAE,MAAM;gBACpBH,QAAQ,EAAE,MAAM;gBAChBoC,OAAO,EAAE;cACX;YAAE;cAAAtE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNf,OAAA;QAAKiC,KAAK,EAAE;UAAEU,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAlC,QAAA,gBACrFX,OAAA;UACEsE,OAAO,EAAE9D,UAAW;UACpB2E,QAAQ,EAAEhF,aAAa,KAAK,CAAE;UAC9B8B,KAAK,EAAE;YACLI,OAAO,EAAE,WAAW;YACpBY,YAAY,EAAE,MAAM;YACpBF,UAAU,EAAE,KAAK;YACjBc,MAAM,EAAE,MAAM;YACdW,MAAM,EAAErE,aAAa,KAAK,CAAC,GAAG,aAAa,GAAG,SAAS;YACvDgC,UAAU,EAAEhC,aAAa,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;YACvD6C,KAAK,EAAE7C,aAAa,KAAK,CAAC,GAAG,SAAS,GAAG;UAC3C,CAAE;UAAAQ,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETf,OAAA;UAAKiC,KAAK,EAAE;YAAEuB,SAAS,EAAE;UAAS,CAAE;UAAA7C,QAAA,gBAClCX,OAAA;YAAKiC,KAAK,EAAE;cAAEa,QAAQ,EAAE,MAAM;cAAEE,KAAK,EAAE,SAAS;cAAEI,YAAY,EAAE;YAAM,CAAE;YAAAzC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvFf,OAAA;YAAKiC,KAAK,EAAE;cACVsC,KAAK,EAAE,OAAO;cACdG,MAAM,EAAE,KAAK;cACbvC,UAAU,EAAE,SAAS;cACrBc,YAAY,EAAE,KAAK;cACnBmC,QAAQ,EAAE;YACZ,CAAE;YAAAzE,QAAA,eACAX,OAAA;cACEiC,KAAK,EAAE;gBACLyC,MAAM,EAAE,MAAM;gBACdvC,UAAU,EAAE,SAAS;gBACrBc,YAAY,EAAE,KAAK;gBACnBsB,KAAK,EAAG,CAACpE,aAAa,GAAG,CAAC,IAAIC,cAAc,GAAI,GAAG,GAAG,GAAG;gBACzDqE,UAAU,EAAE;cACd;YAAE;cAAA7D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENf,OAAA;UACEsE,OAAO,EAAE/D,MAAO;UAChB0B,KAAK,EAAE;YACLI,OAAO,EAAE,WAAW;YACpBY,YAAY,EAAE,MAAM;YACpBF,UAAU,EAAE,KAAK;YACjBc,MAAM,EAAE,MAAM;YACdW,MAAM,EAAE,SAAS;YACjBrC,UAAU,EAAE,SAAS;YACrBa,KAAK,EAAE;UACT,CAAE;UAAArC,QAAA,EAEDR,aAAa,KAAKC,cAAc,GAAG,CAAC,GAAG,aAAa,GAAG;QAAQ;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAsE,EAAA,GApQMpF,mBAAmB;AAqQzB,MAAMqF,qBAAqB,GAAGA,CAAC;EAC7BC,SAAS;EACTC,eAAe;EACfC,YAAY;EACZC,gBAAgB;EAChBC,OAAO;EACPC,QAAQ;EACRC,wBAAwB;EACxBC,kBAAkB;EAClBC,SAAS;EACTC,SAAS;EACTC,cAAc;EACdC;AACF,CAAC,KAAK;EACJ,IAAI,CAACX,SAAS,IAAI,CAAClE,KAAK,CAACC,OAAO,CAACiE,SAAS,CAAC,EAAE;IAC3C,oBAAOvF,OAAA;MAAAW,QAAA,EAAK;IAAsB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAC1C;EAEA,oBACEf,OAAA;IAAKiC,KAAK,EAAE;MAAEC,SAAS,EAAE,OAAO;MAAEC,UAAU,EAAE,mDAAmD;MAAEE,OAAO,EAAE;IAAO,CAAE;IAAA1B,QAAA,eACnHX,OAAA;MAAKiC,KAAK,EAAE;QAAEQ,QAAQ,EAAE,OAAO;QAAEC,MAAM,EAAE;MAAS,CAAE;MAAA/B,QAAA,gBAElDX,OAAA;QAAKiC,KAAK,EAAE;UAAEuB,SAAS,EAAE,QAAQ;UAAEJ,YAAY,EAAE;QAAO,CAAE;QAAAzC,QAAA,eACxDX,OAAA;UAAKiC,KAAK,EAAE;YACVE,UAAU,EAAE,OAAO;YACnBc,YAAY,EAAE,MAAM;YACpBZ,OAAO,EAAE,MAAM;YACfc,SAAS,EAAE,qCAAqC;YAChDU,MAAM,EAAE;UACV,CAAE;UAAAlD,QAAA,gBACAX,OAAA;YAAIiC,KAAK,EAAE;cAAEa,QAAQ,EAAE,MAAM;cAAEC,UAAU,EAAE,MAAM;cAAEC,KAAK,EAAE,SAAS;cAAEN,MAAM,EAAE;YAAY,CAAE;YAAA/B,QAAA,EAAC;UAE5F;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLf,OAAA;YAAGiC,KAAK,EAAE;cAAEe,KAAK,EAAE,SAAS;cAAEN,MAAM,EAAE;YAAE,CAAE;YAAA/B,QAAA,EAAC;UAAwC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNf,OAAA;QAAKiC,KAAK,EAAE;UAAEU,OAAO,EAAE,MAAM;UAAEmB,aAAa,EAAE,QAAQ;UAAEC,GAAG,EAAE,MAAM;UAAEX,YAAY,EAAE;QAAO,CAAE;QAAAzC,QAAA,EACzF4E,SAAS,CAAChE,GAAG,CAAC,CAACrB,QAAQ,EAAEgE,KAAK,KAAK;UAClC,IAAI,CAAChE,QAAQ,EAAE,OAAO,IAAI;UAE1B,MAAMc,YAAY,GAAGd,QAAQ,CAACe,IAAI,GAAGC,MAAM,CAAChB,QAAQ,CAACe,IAAI,CAAC,GAAG,wBAAwB;UACrF,MAAME,UAAU,GAAGjB,QAAQ,CAACiB,UAAU,GAAGD,MAAM,CAAChB,QAAQ,CAACiB,UAAU,CAAC,GAAG,SAAS;UAChF,MAAMgF,UAAU,GAAGX,eAAe,CAACtB,KAAK,CAAC;UAEzC,IAAIkC,SAAS,GAAG,KAAK;UACrB,IAAIC,iBAAiB,GAAG,SAAS;UACjC,IAAIC,cAAc,GAAG,cAAc;UAEnC,IAAInF,UAAU,KAAK,SAAS,EAAE;YAC5BiF,SAAS,GAAGlG,QAAQ,CAACqG,aAAa,KAAKJ,UAAU;YAEjD,IAAIjG,QAAQ,CAACkB,OAAO,IAAIlB,QAAQ,CAACqG,aAAa,KAAKC,SAAS,EAAE;cAC5D,MAAMC,UAAU,GAAGvG,QAAQ,CAACkB,OAAO,CAAClB,QAAQ,CAACqG,aAAa,CAAC;cAC3DF,iBAAiB,GAAGI,UAAU,GAAGvF,MAAM,CAACuF,UAAU,CAAC,GAAG,SAAS;YACjE;YAEA,IAAIvG,QAAQ,CAACkB,OAAO,IAAI+E,UAAU,KAAKK,SAAS,EAAE;cAChD,MAAME,OAAO,GAAGxG,QAAQ,CAACkB,OAAO,CAAC+E,UAAU,CAAC;cAC5CG,cAAc,GAAGI,OAAO,GAAGxF,MAAM,CAACwF,OAAO,CAAC,GAAG,cAAc;YAC7D;UACF,CAAC,MAAM;YACLN,SAAS,GAAGlG,QAAQ,CAACyG,aAAa,KAAKR,UAAU;YACjDE,iBAAiB,GAAGnG,QAAQ,CAACyG,aAAa,GAAGzF,MAAM,CAAChB,QAAQ,CAACyG,aAAa,CAAC,GAAG,SAAS;YACvFL,cAAc,GAAGH,UAAU,GAAGjF,MAAM,CAACiF,UAAU,CAAC,GAAG,cAAc;UACnE;UAEA,oBACEnG,OAAA;YAEEiC,KAAK,EAAE;cACLgB,YAAY,EAAE,MAAM;cACpBE,SAAS,EAAE,mCAAmC;cAC9CU,MAAM,EAAE,YAAY,IAAIuC,SAAS,GAAG,SAAS,GAAG,SAAS,CAAC;cAC1D/D,OAAO,EAAE,MAAM;cACfF,UAAU,EAAEiE,SAAS,GAAG,SAAS,GAAG;YACtC,CAAE;YAAAzF,QAAA,gBAGFX,OAAA;cAAKiC,KAAK,EAAE;gBAAEmB,YAAY,EAAE;cAAO,CAAE;cAAAzC,QAAA,eACnCX,OAAA;gBAAKiC,KAAK,EAAE;kBAAEU,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE,YAAY;kBAAEkB,GAAG,EAAE;gBAAO,CAAE;gBAAApD,QAAA,gBACrEX,OAAA;kBAAKiC,KAAK,EAAE;oBACVsC,KAAK,EAAE,MAAM;oBACbG,MAAM,EAAE,MAAM;oBACdzB,YAAY,EAAE,KAAK;oBACnBd,UAAU,EAAE,SAAS;oBACrBa,KAAK,EAAE,OAAO;oBACdL,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE,QAAQ;oBACxBG,UAAU,EAAE,MAAM;oBAClBD,QAAQ,EAAE,MAAM;oBAChB8D,UAAU,EAAE,CAAC;oBACbC,SAAS,EAAE;kBACb,CAAE;kBAAAlG,QAAA,EACCuD,KAAK,GAAG;gBAAC;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eACNf,OAAA;kBAAKiC,KAAK,EAAE;oBAAE0C,IAAI,EAAE;kBAAE,CAAE;kBAAAhE,QAAA,eACtBX,OAAA;oBAAGiC,KAAK,EAAE;sBACRe,KAAK,EAAE,SAAS;sBAChBD,UAAU,EAAE,KAAK;sBACjBM,UAAU,EAAE,KAAK;sBACjBX,MAAM,EAAE;oBACV,CAAE;oBAAA/B,QAAA,EACCK;kBAAY;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNf,OAAA;cAAKiC,KAAK,EAAE;gBAAEmB,YAAY,EAAE;cAAM,CAAE;cAAAzC,QAAA,gBAClCX,OAAA;gBAAMiC,KAAK,EAAE;kBAAEa,QAAQ,EAAE,MAAM;kBAAEC,UAAU,EAAE,KAAK;kBAAEC,KAAK,EAAE;gBAAU,CAAE;gBAAArC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5Ff,OAAA;gBAAMiC,KAAK,EAAE;kBACXc,UAAU,EAAE,KAAK;kBACjBC,KAAK,EAAEoD,SAAS,GAAG,SAAS,GAAG;gBACjC,CAAE;gBAAAzF,QAAA,EACC2F;cAAc;gBAAA1F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACPf,OAAA;gBAAMiC,KAAK,EAAE;kBACX6E,UAAU,EAAE,MAAM;kBAClBhE,QAAQ,EAAE,MAAM;kBAChBE,KAAK,EAAEoD,SAAS,GAAG,SAAS,GAAG;gBACjC,CAAE;gBAAAzF,QAAA,EACCyF,SAAS,GAAG,GAAG,GAAG;cAAG;gBAAAxF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EAGL,CAACqF,SAAS,iBACTpG,OAAA;cAAKiC,KAAK,EAAE;gBAAEmB,YAAY,EAAE;cAAM,CAAE;cAAAzC,QAAA,gBAClCX,OAAA;gBAAMiC,KAAK,EAAE;kBAAEa,QAAQ,EAAE,MAAM;kBAAEC,UAAU,EAAE,KAAK;kBAAEC,KAAK,EAAE;gBAAU,CAAE;gBAAArC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/Ff,OAAA;gBAAMiC,KAAK,EAAE;kBAAEc,UAAU,EAAE,KAAK;kBAAEC,KAAK,EAAE;gBAAU,CAAE;gBAAArC,QAAA,EAAE0F;cAAiB;gBAAAzF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChFf,OAAA;gBAAMiC,KAAK,EAAE;kBAAE6E,UAAU,EAAE,MAAM;kBAAEhE,QAAQ,EAAE,MAAM;kBAAEE,KAAK,EAAE;gBAAU,CAAE;gBAAArC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CACN,EAGA,CAACqF,SAAS,iBACTpG,OAAA;cAAKiC,KAAK,EAAE;gBAAE4E,SAAS,EAAE;cAAM,CAAE;cAAAlG,QAAA,eAC/BX,OAAA;gBACEsE,OAAO,EAAEA,CAAA,KAAM;kBACboB,gBAAgB,CACd1E,YAAY,EACZqF,iBAAiB,EACjBC,cAAc,EACdpG,QAAQ,CAACoD,KAAK,IAAIpD,QAAQ,CAACqD,QAAQ,IAAI,EACzC,CAAC;gBACH,CAAE;gBACFtB,KAAK,EAAE;kBACLI,OAAO,EAAE,UAAU;kBACnBF,UAAU,EAAE,SAAS;kBACrBa,KAAK,EAAE,OAAO;kBACda,MAAM,EAAE,MAAM;kBACdZ,YAAY,EAAE,KAAK;kBACnBH,QAAQ,EAAE,MAAM;kBAChBC,UAAU,EAAE,KAAK;kBACjByB,MAAM,EAAE,SAAS;kBACjB7B,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBkB,GAAG,EAAE;gBACP,CAAE;gBAAApD,QAAA,gBAEFX,OAAA;kBAAAW,QAAA,EAAM;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACff,OAAA;kBAAAW,QAAA,EAAM;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN,EAGA0E,YAAY,CAACzE,YAAY,CAAC,iBACzBhB,OAAA;cAAKiC,KAAK,EAAE;gBACV4E,SAAS,EAAE,MAAM;gBACjBxE,OAAO,EAAE,MAAM;gBACfF,UAAU,EAAE,OAAO;gBACnBc,YAAY,EAAE,KAAK;gBACnB8D,UAAU,EAAE,mBAAmB;gBAC/B5D,SAAS,EAAE,gCAAgC;gBAC3CU,MAAM,EAAE;cACV,CAAE;cAAAlD,QAAA,gBACAX,OAAA;gBAAKiC,KAAK,EAAE;kBAAEU,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE,QAAQ;kBAAEO,YAAY,EAAE;gBAAM,CAAE;gBAAAzC,QAAA,gBACzEX,OAAA;kBAAMiC,KAAK,EAAE;oBAAEa,QAAQ,EAAE,MAAM;oBAAEkE,WAAW,EAAE;kBAAM,CAAE;kBAAArG,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAChEf,OAAA;kBAAIiC,KAAK,EAAE;oBAAEc,UAAU,EAAE,MAAM;oBAAEC,KAAK,EAAE,SAAS;oBAAEF,QAAQ,EAAE,MAAM;oBAAEJ,MAAM,EAAE;kBAAE,CAAE;kBAAA/B,QAAA,EAAC;gBAElF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,EAGL,CAACb,QAAQ,CAACoD,KAAK,IAAIpD,QAAQ,CAACqD,QAAQ,kBACnCvD,OAAA;gBAAKiC,KAAK,EAAE;kBACVmB,YAAY,EAAE,MAAM;kBACpBf,OAAO,EAAE,KAAK;kBACdF,UAAU,EAAE,SAAS;kBACrBc,YAAY,EAAE,KAAK;kBACnBY,MAAM,EAAE;gBACV,CAAE;gBAAAlD,QAAA,gBACAX,OAAA;kBAAKiC,KAAK,EAAE;oBAAEmB,YAAY,EAAE;kBAAM,CAAE;kBAAAzC,QAAA,eAClCX,OAAA;oBAAMiC,KAAK,EAAE;sBAAEe,KAAK,EAAE,SAAS;sBAAEF,QAAQ,EAAE,MAAM;sBAAEC,UAAU,EAAE;oBAAM,CAAE;oBAAApC,QAAA,EAAC;kBAExE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNf,OAAA;kBAAKiC,KAAK,EAAE;oBAAEuB,SAAS,EAAE;kBAAS,CAAE;kBAAA7C,QAAA,eAClCX,OAAA;oBACEyD,GAAG,EAAEvD,QAAQ,CAACoD,KAAK,IAAIpD,QAAQ,CAACqD,QAAS;oBACzCG,GAAG,EAAC,kBAAkB;oBACtBzB,KAAK,EAAE;sBACLQ,QAAQ,EAAE,MAAM;sBAChBkB,SAAS,EAAE,OAAO;sBAClBC,SAAS,EAAE,SAAS;sBACpBX,YAAY,EAAE,KAAK;sBACnBY,MAAM,EAAE;oBACV;kBAAE;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eAEDf,OAAA;gBAAKiC,KAAK,EAAE;kBACVa,QAAQ,EAAE,MAAM;kBAChBE,KAAK,EAAE,SAAS;kBAChBK,UAAU,EAAE,KAAK;kBACjBlB,UAAU,EAAE,SAAS;kBACrBE,OAAO,EAAE,KAAK;kBACdY,YAAY,EAAE;gBAChB,CAAE;gBAAAtC,QAAA,EACC8E,YAAY,CAACzE,YAAY,CAAC,GAAGE,MAAM,CAACuE,YAAY,CAACzE,YAAY,CAAC,CAAC,GAAG;cAAE;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA,GA/JIb,QAAQ,CAAC+G,GAAG,IAAI/C,KAAK;YAAAtD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgKvB,CAAC;QAEV,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNf,OAAA;QAAKiC,KAAK,EAAE;UACVU,OAAO,EAAE,MAAM;UACfmB,aAAa,EAAEoD,MAAM,CAACC,UAAU,GAAG,GAAG,GAAG,QAAQ,GAAG,KAAK;UACzDpD,GAAG,EAAE,MAAM;UACXnB,cAAc,EAAE,QAAQ;UACxBC,UAAU,EAAE;QACd,CAAE;QAAAlC,QAAA,gBACAX,OAAA;UACEsE,OAAO,EAAEA,CAAA,KAAMqB,OAAO,CAAC,QAAQ,CAAE;UACjC1D,KAAK,EAAE;YACLI,OAAO,EAAE,WAAW;YACpBF,UAAU,EAAE,SAAS;YACrBa,KAAK,EAAE,OAAO;YACda,MAAM,EAAE,MAAM;YACdZ,YAAY,EAAE,MAAM;YACpBF,UAAU,EAAE,MAAM;YAClByB,MAAM,EAAE,SAAS;YACjBrB,SAAS,EAAE;UACb,CAAE;UAAAxC,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETf,OAAA;UACEsE,OAAO,EAAEA,CAAA,KAAM;YACbqB,OAAO,CAAC,cAAc,CAAC;YACvBE,wBAAwB,CAAC,CAAC,CAAC;YAC3BC,kBAAkB,CAAC,CAAC,CAAC,CAAC;YACtBC,SAAS,CAAC,CAAC,CAAC,CAAC;YACbC,SAAS,CAAC,KAAK,CAAC;YAChBC,cAAc,CAAC,CAAAL,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEwB,QAAQ,KAAI,CAAC,CAAC;YACvClB,eAAe,CAAC,CAAC,CAAC,CAAC;UACrB,CAAE;UACFjE,KAAK,EAAE;YACLI,OAAO,EAAE,WAAW;YACpBF,UAAU,EAAE,SAAS;YACrBa,KAAK,EAAE,OAAO;YACda,MAAM,EAAE,MAAM;YACdZ,YAAY,EAAE,MAAM;YACpBF,UAAU,EAAE,MAAM;YAClByB,MAAM,EAAE,SAAS;YACjBrB,SAAS,EAAE;UACb,CAAE;UAAAxC,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACsG,GAAA,GA9RI/B,qBAAqB;AAgS3B,SAASgC,SAASA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EACnB,MAAM;IAAEC;EAAY,CAAC,GAAG/H,WAAW,CAAC,CAAC;EACrC,MAAM,CAACiG,QAAQ,EAAE+B,WAAW,CAAC,GAAGlJ,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAAC8G,SAAS,EAAEqC,YAAY,CAAC,GAAGnJ,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACoJ,qBAAqB,EAAEhC,wBAAwB,CAAC,GAAGpH,QAAQ,CAAC,CAAC,CAAC;EACrE,MAAM,CAAC+G,eAAe,EAAEM,kBAAkB,CAAC,GAAGrH,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACqJ,MAAM,EAAE/B,SAAS,CAAC,GAAGtH,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAMsJ,MAAM,GAAGlJ,SAAS,CAAC,CAAC;EAC1B,MAAMmJ,QAAQ,GAAGtJ,WAAW,CAAC,CAAC;EAC9B,MAAMuJ,QAAQ,GAAGrJ,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACsJ,IAAI,EAAEvC,OAAO,CAAC,GAAGlH,QAAQ,CAAC,cAAc,CAAC;EAChD,MAAM,CAAC0J,WAAW,EAAElC,cAAc,CAAC,GAAGxH,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC2J,MAAM,EAAEpC,SAAS,CAAC,GAAGvH,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC4J,UAAU,EAAEC,aAAa,CAAC,GAAG7J,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC8J,SAAS,EAAEC,YAAY,CAAC,GAAG/J,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACgK,SAAS,EAAEC,YAAY,CAAC,GAAGjK,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM;IAAEkK;EAAK,CAAC,GAAGhK,WAAW,CAAEiK,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EAEnD,MAAM;IAAEpE,KAAK;IAAEG;EAAO,CAAC,GAAGpF,aAAa,CAAC,CAAC;EACzC,MAAM,CAACmG,YAAY,EAAES,eAAe,CAAC,GAAGzH,QAAQ,CAAC,CAAC,CAAC,CAAC;EAEpD,MAAMoK,WAAW,GAAGtK,WAAW,CAAC,YAAY;IAC1C,IAAI;MACFiK,YAAY,CAAC,IAAI,CAAC;MAClBR,QAAQ,CAAC/I,WAAW,CAAC,CAAC,CAAC;MACvB6J,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEhB,MAAM,CAACiB,EAAE,CAAC;MAEpD,MAAMC,QAAQ,GAAG,MAAMnK,WAAW,CAAC;QAAEoK,MAAM,EAAEnB,MAAM,CAACiB;MAAG,CAAC,CAAC;MACzDF,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEE,QAAQ,CAAC;MAE3CjB,QAAQ,CAAChJ,WAAW,CAAC,CAAC,CAAC;MACvBwJ,YAAY,CAAC,KAAK,CAAC;MAEnB,IAAIS,QAAQ,CAACE,OAAO,EAAE;QACpB,MAAMvD,QAAQ,GAAGqD,QAAQ,CAACG,IAAI;;QAE9B;QACA,IAAI7D,SAAS,GAAG,EAAE;QAClB,IAAIK,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEL,SAAS,IAAIlE,KAAK,CAACC,OAAO,CAACsE,QAAQ,CAACL,SAAS,CAAC,EAAE;UAC5DA,SAAS,GAAGK,QAAQ,CAACL,SAAS;QAChC,CAAC,MAAM,IAAIK,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAE1F,QAAQ,IAAImB,KAAK,CAACC,OAAO,CAACsE,QAAQ,CAAC1F,QAAQ,CAAC,EAAE;UACjEqF,SAAS,GAAGK,QAAQ,CAAC1F,QAAQ;QAC/B,CAAC,MAAM,IAAI0F,QAAQ,IAAIvE,KAAK,CAACC,OAAO,CAACsE,QAAQ,CAAC,EAAE;UAC9CL,SAAS,GAAGK,QAAQ;QACtB;QAEAkD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEnD,QAAQ,CAAC;QACnCkD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAExD,SAAS,CAACvB,MAAM,CAAC;QACjD8E,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEtH,MAAM,CAAC4H,IAAI,CAACzD,QAAQ,IAAI,CAAC,CAAC,CAAC,CAAC;QAEhEgC,YAAY,CAACrC,SAAS,CAAC;QACvBoC,WAAW,CAAC/B,QAAQ,CAAC;QACrBK,cAAc,CAAC,CAAAL,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEwB,QAAQ,KAAI,CAAC,CAAC;QAEvC,IAAI7B,SAAS,CAACvB,MAAM,KAAK,CAAC,EAAE;UAC1B8E,OAAO,CAACQ,IAAI,CAAC,iCAAiC,CAAC;UAC/CR,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEE,QAAQ,CAAC;UACrD5K,OAAO,CAACkL,OAAO,CAAC,6DAA6D,CAAC;QAChF;MACF,CAAC,MAAM;QACLT,OAAO,CAACU,KAAK,CAAC,YAAY,EAAEP,QAAQ,CAAC5K,OAAO,CAAC;QAC7CyK,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEE,QAAQ,CAAC;QAC7C5K,OAAO,CAACmL,KAAK,CAACP,QAAQ,CAAC5K,OAAO,IAAI,0BAA0B,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOmL,KAAK,EAAE;MACdxB,QAAQ,CAAChJ,WAAW,CAAC,CAAC,CAAC;MACvBwJ,YAAY,CAAC,KAAK,CAAC;MACnBM,OAAO,CAACU,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDnL,OAAO,CAACmL,KAAK,CAACA,KAAK,CAACnL,OAAO,IAAI,wCAAwC,CAAC;IAC1E;EACF,CAAC,EAAE,CAAC0J,MAAM,CAACiB,EAAE,EAAEhB,QAAQ,CAAC,CAAC;EAEzB,MAAMyB,oBAAoB,GAAG,MAAOC,OAAO,IAAK;IAC9C,IAAI,CAACA,OAAO,CAAC1F,MAAM,EAAE,OAAO,EAAE;IAC9B,MAAM;MAAEoF;IAAK,CAAC,GAAG,MAAM3J,uBAAuB,CAACiK,OAAO,CAAC;IACvD,OAAON,IAAI;EACb,CAAC;EAED,MAAMO,eAAe,GAAGpL,WAAW,CAAC,YAAY;IAC9C,IAAI;MACF;MACA,IAAI,CAACoK,IAAI,IAAI,CAACA,IAAI,CAAC1B,GAAG,EAAE;QACtB5I,OAAO,CAACmL,KAAK,CAAC,sCAAsC,CAAC;QACrDvB,QAAQ,CAAC,QAAQ,CAAC;QAClB;MACF;MAEAD,QAAQ,CAAC/I,WAAW,CAAC,CAAC,CAAC;MAEvB,MAAM2K,eAAe,GAAG,EAAE;MAC1B,MAAMC,QAAQ,GAAG,EAAE;MAEnBtE,SAAS,CAACuE,OAAO,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAK;QAC5B,IAAID,CAAC,CAAC5I,UAAU,KAAK,WAAW,IAAI4I,CAAC,CAAC5I,UAAU,KAAK,mBAAmB,EAAE;UACxE0I,QAAQ,CAACI,IAAI,CAACD,GAAG,CAAC;UAClBJ,eAAe,CAACK,IAAI,CAAC;YACnB/J,QAAQ,EAAE6J,CAAC,CAAC9I,IAAI;YAChBiJ,cAAc,EAAEH,CAAC,CAACpD,aAAa,IAAIoD,CAAC,CAACxD,aAAa;YAClDJ,UAAU,EAAEX,eAAe,CAACwE,GAAG,CAAC,IAAI;UACtC,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MAEF,MAAMG,UAAU,GAAG,MAAMV,oBAAoB,CAACG,eAAe,CAAC;MAC9D,MAAMQ,MAAM,GAAG,CAAC,CAAC;MAEjBD,UAAU,CAACL,OAAO,CAAEO,CAAC,IAAK;QACxB,IAAIA,CAAC,CAACvC,MAAM,IAAI,OAAOuC,CAAC,CAACvC,MAAM,CAAC1B,SAAS,KAAK,SAAS,EAAE;UACvDgE,MAAM,CAACC,CAAC,CAACnK,QAAQ,CAAC,GAAGmK,CAAC,CAACvC,MAAM;QAC/B,CAAC,MAAM,IAAI,OAAOuC,CAAC,CAACjE,SAAS,KAAK,SAAS,EAAE;UAC3CgE,MAAM,CAACC,CAAC,CAACnK,QAAQ,CAAC,GAAG;YAAEkG,SAAS,EAAEiE,CAAC,CAACjE,SAAS;YAAEkE,MAAM,EAAED,CAAC,CAACC,MAAM,IAAI;UAAG,CAAC;QACzE;MACF,CAAC,CAAC;MAEF,MAAMC,cAAc,GAAG,EAAE;MACzB,MAAMC,YAAY,GAAG,EAAE;MACvB,MAAMC,YAAY,GAAG,EAAE;MAEvBlF,SAAS,CAACuE,OAAO,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAK;QAC5B,MAAMU,aAAa,GAAGlF,eAAe,CAACwE,GAAG,CAAC,IAAI,EAAE;QAEhD,IAAID,CAAC,CAAC5I,UAAU,KAAK,WAAW,IAAI4I,CAAC,CAAC5I,UAAU,KAAK,mBAAmB,EAAE;UACxE,MAAM;YAAEiF,SAAS,GAAG,KAAK;YAAEkE,MAAM,GAAG;UAAG,CAAC,GAAGF,MAAM,CAACL,CAAC,CAAC9I,IAAI,CAAC,IAAI,CAAC,CAAC;UAC/D,MAAM0J,QAAQ,GAAG;YAAE,GAAGZ,CAAC;YAAE5D,UAAU,EAAEuE,aAAa;YAAEJ;UAAO,CAAC;UAE5D,IAAIlE,SAAS,EAAE;YACbmE,cAAc,CAACN,IAAI,CAACU,QAAQ,CAAC;UAC/B,CAAC,MAAM;YACLH,YAAY,CAACP,IAAI,CAACU,QAAQ,CAAC;YAC3BF,YAAY,CAACR,IAAI,CAAC;cAChB/J,QAAQ,EAAE6J,CAAC,CAAC9I,IAAI;cAChBiJ,cAAc,EAAEH,CAAC,CAACpD,aAAa,IAAIoD,CAAC,CAACxD,aAAa;cAClDJ,UAAU,EAAEuE;YACd,CAAC,CAAC;UACJ;QACF,CAAC,MAAM,IAAIX,CAAC,CAAC5I,UAAU,KAAK,SAAS,EAAE;UACrC,MAAMyJ,UAAU,GAAGb,CAAC,CAACxD,aAAa;UAClC,MAAMsE,YAAY,GAAId,CAAC,CAAC3I,OAAO,IAAI2I,CAAC,CAAC3I,OAAO,CAACwJ,UAAU,CAAC,IAAKA,UAAU;UACvE,MAAME,SAAS,GAAIf,CAAC,CAAC3I,OAAO,IAAI2I,CAAC,CAAC3I,OAAO,CAACsJ,aAAa,CAAC,IAAKA,aAAa,IAAI,EAAE;UAEhF,MAAMtE,SAAS,GAAGwE,UAAU,KAAKF,aAAa;UAC9C,MAAMC,QAAQ,GAAG;YAAE,GAAGZ,CAAC;YAAE5D,UAAU,EAAEuE;UAAc,CAAC;UAEpD,IAAItE,SAAS,EAAE;YACbmE,cAAc,CAACN,IAAI,CAACU,QAAQ,CAAC;UAC/B,CAAC,MAAM;YACLH,YAAY,CAACP,IAAI,CAACU,QAAQ,CAAC;YAC3BF,YAAY,CAACR,IAAI,CAAC;cAChB/J,QAAQ,EAAE6J,CAAC,CAAC9I,IAAI;cAChBiJ,cAAc,EAAEW,YAAY;cAC5B1E,UAAU,EAAE2E;YACd,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;;MAEF;MACA,MAAMC,SAAS,GAAGtC,SAAS,GAAG3G,IAAI,CAACC,KAAK,CAAC,CAACiJ,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGxC,SAAS,IAAI,IAAI,CAAC,GAAG,CAAC;MAC7E,MAAMyC,gBAAgB,GAAG,CAAC,CAAAtF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEwB,QAAQ,KAAI,CAAC,IAAI,EAAE,CAAC,CAAC;;MAEzD;MACA,MAAMhH,cAAc,GAAGmF,SAAS,CAACvB,MAAM;MACvC,MAAMmH,YAAY,GAAGZ,cAAc,CAACvG,MAAM;MAC1C,MAAMoH,eAAe,GAAGtJ,IAAI,CAACuJ,KAAK,CAAEF,YAAY,GAAG/K,cAAc,GAAI,GAAG,CAAC;MACzE,MAAMkL,MAAM,GAAGH,YAAY,GAAG,EAAE,CAAC,CAAC;;MAElC;MACA,MAAMI,iBAAiB,GAAG3F,QAAQ,CAAC4F,YAAY,IAAI,EAAE,CAAC,CAAC;MACvD,MAAMC,OAAO,GAAGL,eAAe,IAAIG,iBAAiB,GAAG,MAAM,GAAG,MAAM;MAEtE,MAAMG,UAAU,GAAG;QACjBnB,cAAc,EAAEA,cAAc,IAAI,EAAE;QACpCC,YAAY,EAAEA,YAAY,IAAI,EAAE;QAChCiB,OAAO,EAAEA,OAAO,IAAI,MAAM;QAC1BE,KAAK,EAAEP,eAAe;QACtBE,MAAM,EAAEA,MAAM;QACdlL,cAAc,EAAEA,cAAc;QAC9B2K,SAAS,EAAEA,SAAS;QACpBG,gBAAgB,EAAEA;MACpB,CAAC;MAEDnF,SAAS,CAAC2F,UAAU,CAAC;MAErB,MAAMzC,QAAQ,GAAG,MAAMlK,SAAS,CAAC;QAC/B6M,IAAI,EAAE7D,MAAM,CAACiB,EAAE;QACflB,MAAM,EAAE4D,UAAU;QAClB/C,IAAI,EAAEA,IAAI,CAAC1B;MACb,CAAC,CAAC;MAEF,IAAIgC,QAAQ,CAACE,OAAO,EAAE;QACpB;QACA,MAAM0C,YAAY,GAAG;UACnB,GAAGH,UAAU;UACbI,MAAM,EAAE7C,QAAQ,CAAC6C;QACnB,CAAC;QACD/F,SAAS,CAAC8F,YAAY,CAAC;QAEvBlG,OAAO,CAAC,QAAQ,CAAC;QACjBuB,MAAM,CAAC6E,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;QACrB,IAAIC,KAAK,CAACP,OAAO,KAAK,MAAM,GAAGlM,SAAS,GAAGC,SAAS,CAAC,CAACyM,IAAI,CAAC,CAAC;MAC9D,CAAC,MAAM;QACL5N,OAAO,CAACmL,KAAK,CAACP,QAAQ,CAAC5K,OAAO,CAAC;MACjC;MACA2J,QAAQ,CAAChJ,WAAW,CAAC,CAAC,CAAC;IAEzB,CAAC,CAAC,OAAOwK,KAAK,EAAE;MACdxB,QAAQ,CAAChJ,WAAW,CAAC,CAAC,CAAC;MACvBX,OAAO,CAACmL,KAAK,CAACA,KAAK,CAACnL,OAAO,CAAC;IAC9B;EACF,CAAC,EAAE,CAACkH,SAAS,EAAEC,eAAe,EAAEI,QAAQ,EAAEmC,MAAM,CAACiB,EAAE,EAAEL,IAAI,EAAEV,QAAQ,EAAED,QAAQ,CAAC,CAAC;EAE/E,MAAMtC,gBAAgB,GAAG,MAAAA,CAAOxF,QAAQ,EAAEgK,cAAc,EAAE/D,UAAU,EAAE5C,QAAQ,KAAK;IACjF,IAAI;MACFyE,QAAQ,CAAC/I,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMgK,QAAQ,GAAG,MAAMvJ,2BAA2B,CAAC;QAAEQ,QAAQ;QAAEgK,cAAc;QAAE/D,UAAU;QAAE5C;MAAS,CAAC,CAAC;MACtGyE,QAAQ,CAAChJ,WAAW,CAAC,CAAC,CAAC;MAEvB,IAAIiK,QAAQ,CAACE,OAAO,EAAE;QACpBjD,eAAe,CAAEgG,IAAI,KAAM;UAAE,GAAGA,IAAI;UAAE,CAAChM,QAAQ,GAAG+I,QAAQ,CAACkD;QAAY,CAAC,CAAC,CAAC;MAC5E,CAAC,MAAM;QACL9N,OAAO,CAACmL,KAAK,CAACP,QAAQ,CAACO,KAAK,IAAI,8BAA8B,CAAC;MACjE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdxB,QAAQ,CAAChJ,WAAW,CAAC,CAAC,CAAC;MACvBX,OAAO,CAACmL,KAAK,CAACA,KAAK,CAACnL,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAM+N,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAMC,YAAY,GAAG,CAAAzG,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEwB,QAAQ,KAAI,CAAC,CAAC,CAAC;IAC9CnB,cAAc,CAACoG,YAAY,CAAC;IAC5B3D,YAAY,CAACsC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE1B,MAAMqB,aAAa,GAAGC,WAAW,CAAC,MAAM;MACtCtG,cAAc,CAAEuG,WAAW,IAAK;QAC9B,IAAIA,WAAW,GAAG,CAAC,EAAE;UACnB,OAAOA,WAAW,GAAG,CAAC;QACxB,CAAC,MAAM;UACLxG,SAAS,CAAC,IAAI,CAAC;UACf,OAAO,CAAC;QACV;MACF,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;IACRsC,aAAa,CAACgE,aAAa,CAAC;EAC9B,CAAC;EAED9N,SAAS,CAAC,MAAM;IACd,IAAI4J,MAAM,IAAIF,IAAI,KAAK,WAAW,EAAE;MAClCuE,aAAa,CAACpE,UAAU,CAAC;MACzBsB,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAACvB,MAAM,EAAEF,IAAI,EAAEG,UAAU,EAAEsB,eAAe,CAAC,CAAC;EAE/CnL,SAAS,CAAC,MAAM;IACdsK,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEhB,MAAM,CAACiB,EAAE,CAAC;IAC1D,IAAIjB,MAAM,CAACiB,EAAE,EAAE;MACbH,WAAW,CAAC,CAAC;IACf,CAAC,MAAM;MACLC,OAAO,CAACU,KAAK,CAAC,uCAAuC,CAAC;MACtDnL,OAAO,CAACmL,KAAK,CAAC,sDAAsD,CAAC;MACrEvB,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC,EAAE,CAACF,MAAM,CAACiB,EAAE,EAAEH,WAAW,EAAEZ,QAAQ,CAAC,CAAC;EAEtCzJ,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAI6J,UAAU,EAAE;QACdoE,aAAa,CAACpE,UAAU,CAAC;MAC3B;IACF,CAAC;EACH,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;;EAEhB;EACA7J,SAAS,CAAC,MAAM;IACd,IAAI0J,IAAI,KAAK,cAAc,IAAIA,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,QAAQ,EAAE;MACxEwE,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAChD,CAAC,MAAM;MACLH,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,iBAAiB,CAAC;IACnD;;IAEA;IACA,OAAO,MAAM;MACXJ,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,iBAAiB,CAAC;IACnD,CAAC;EACH,CAAC,EAAE,CAAC5E,IAAI,CAAC,CAAC;;EAEV;EACA,MAAM6E,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF/E,QAAQ,CAAC/I,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMgK,QAAQ,GAAG,MAAM+D,KAAK,CAAC,kCAAkC,EAAE;QAC/DC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAG,UAASC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAE;QAC3D,CAAC;QACDT,IAAI,EAAEU,IAAI,CAACC,SAAS,CAAC;UAAEpE,MAAM,EAAEnB,MAAM,CAACiB;QAAG,CAAC;MAC5C,CAAC,CAAC;MAEF,MAAMI,IAAI,GAAG,MAAMH,QAAQ,CAACsE,IAAI,CAAC,CAAC;MAClC,IAAInE,IAAI,CAACD,OAAO,EAAE;QAChB9K,OAAO,CAAC8K,OAAO,CAACC,IAAI,CAAC/K,OAAO,CAAC;QAC7B;QACAwK,WAAW,CAAC,CAAC;MACf,CAAC,MAAM;QACLxK,OAAO,CAACmL,KAAK,CAACJ,IAAI,CAAC/K,OAAO,CAAC;MAC7B;IACF,CAAC,CAAC,OAAOmL,KAAK,EAAE;MACdnL,OAAO,CAACmL,KAAK,CAAC,iCAAiC,CAAC;IAClD,CAAC,SAAS;MACRxB,QAAQ,CAAChJ,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;;EAED;EACA,IAAI,CAAC2J,IAAI,EAAE;IACT,oBACE3I,OAAA;MAAKwN,SAAS,EAAC,qGAAqG;MAAA7M,QAAA,eAClHX,OAAA;QAAKwN,SAAS,EAAC,2GAA2G;QAAA7M,QAAA,gBACxHX,OAAA;UAAKwN,SAAS,EAAC,kFAAkF;UAAA7M,QAAA,eAC/FX,OAAA;YAAKwN,SAAS,EAAC,sBAAsB;YAACC,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAA/M,QAAA,eAC3EX,OAAA;cAAM2N,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,8JAA8J;cAACC,QAAQ,EAAC;YAAS;cAAAjN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5M;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNf,OAAA;UAAIwN,SAAS,EAAC,uCAAuC;UAAA7M,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClFf,OAAA;UAAGwN,SAAS,EAAC,oBAAoB;UAAA7M,QAAA,EAAC;QAAiE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACvGf,OAAA;UACEwN,SAAS,EAAC,mNAAmN;UAC7NlJ,OAAO,EAAEA,CAAA,KAAM2D,QAAQ,CAAC,QAAQ,CAAE;UAAAtH,QAAA,EACnC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,OAAO6E,QAAQ,gBACb5F,OAAA;IAAKwN,SAAS,EAAC,oEAAoE;IAAA7M,QAAA,GAEhFuH,IAAI,KAAK,cAAc,iBACtBlI,OAAA,CAACd,YAAY;MACX0G,QAAQ,EAAEA,QAAS;MACnBD,OAAO,EAAEA,OAAQ;MACjByG,UAAU,EAAEA,UAAW;MACvB7G,SAAS,EAAEA;IAAU;MAAA3E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CACF,EAEAmH,IAAI,KAAK,WAAW,KACnBK,SAAS,gBACPvI,OAAA;MAAKwN,SAAS,EAAC,qGAAqG;MAAA7M,QAAA,eAClHX,OAAA;QAAKwN,SAAS,EAAC,2GAA2G;QAAA7M,QAAA,gBACxHX,OAAA;UAAKwN,SAAS,EAAC,2IAA2I;UAAA7M,QAAA,eACxJX,OAAA;YAAKwN,SAAS,EAAC,mCAAmC;YAACC,IAAI,EAAC,MAAM;YAACC,OAAO,EAAC,WAAW;YAAA/M,QAAA,gBAChFX,OAAA;cAAQwN,SAAS,EAAC,YAAY;cAACM,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,IAAI;cAAC1D,CAAC,EAAC,IAAI;cAAC2D,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC;YAAG;cAAArN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eACrGf,OAAA;cAAMwN,SAAS,EAAC,YAAY;cAACC,IAAI,EAAC,cAAc;cAACG,CAAC,EAAC;YAAiH;cAAAhN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNf,OAAA;UAAIwN,SAAS,EAAC,uCAAuC;UAAA7M,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1Ef,OAAA;UAAGwN,SAAS,EAAC,uBAAuB;UAAA7M,QAAA,EAAC;QAErC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,GACJwE,SAAS,CAACvB,MAAM,KAAK,CAAC,gBACxBhE,OAAA;MAAKwN,SAAS,EAAC,sGAAsG;MAAA7M,QAAA,eACnHX,OAAA;QAAKwN,SAAS,EAAC,4GAA4G;QAAA7M,QAAA,gBACzHX,OAAA;UAAKwN,SAAS,EAAC,8HAA8H;UAAA7M,QAAA,eAC3IX,OAAA;YAAKwN,SAAS,EAAC,sBAAsB;YAACC,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAA/M,QAAA,eAC3EX,OAAA;cAAM2N,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,mNAAmN;cAACC,QAAQ,EAAC;YAAS;cAAAjN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNf,OAAA;UAAIwN,SAAS,EAAC,wCAAwC;UAAA7M,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9Ef,OAAA;UAAGwN,SAAS,EAAC,6CAA6C;UAAA7M,QAAA,EAAC;QAE3D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJf,OAAA;UAAIwN,SAAS,EAAC,yCAAyC;UAAA7M,QAAA,gBACrDX,OAAA;YAAAW,QAAA,EAAI;UAA4C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrDf,OAAA;YAAAW,QAAA,EAAI;UAA4B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrCf,OAAA;YAAAW,QAAA,EAAI;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACLf,OAAA;UAAKwN,SAAS,EAAC,WAAW;UAAA7M,QAAA,gBACxBX,OAAA;YACEsE,OAAO,EAAEyI,mBAAoB;YAC7BS,SAAS,EAAC,iNAAiN;YAAA7M,QAAA,EAC5N;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTf,OAAA;YACEsE,OAAO,EAAEA,CAAA,KAAM;cACbwE,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;cAC1CF,WAAW,CAAC,CAAC;YACf,CAAE;YACF2E,SAAS,EAAC,2MAA2M;YAAA7M,QAAA,EACtN;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTf,OAAA;YACEsE,OAAO,EAAEA,CAAA,KAAM2D,QAAQ,CAAC,YAAY,CAAE;YACtCuF,SAAS,EAAC,2MAA2M;YAAA7M,QAAA,EACtN;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAENf,OAAA,CAACC,mBAAmB;MAClBC,QAAQ,EAAEqF,SAAS,CAACsC,qBAAqB,CAAE;MAC3C1H,aAAa,EAAE0H,qBAAsB;MACrCzH,cAAc,EAAEmF,SAAS,CAACvB,MAAO;MACjC3D,cAAc,EAAEmF,eAAe,CAACqC,qBAAqB,CAAE;MACvDvH,cAAc,EAAG4N,MAAM,IAAKpI,kBAAkB,CAAC;QAAC,GAAGN,eAAe;QAAE,CAACqC,qBAAqB,GAAGqG;MAAM,CAAC,CAAE;MACtG3N,MAAM,EAAEA,CAAA,KAAM;QACZ,IAAIsH,qBAAqB,KAAKtC,SAAS,CAACvB,MAAM,GAAG,CAAC,EAAE;UAClD2F,eAAe,CAAC,CAAC;QACnB,CAAC,MAAM;UACL9D,wBAAwB,CAACgC,qBAAqB,GAAG,CAAC,CAAC;QACrD;MACF,CAAE;MACFrH,UAAU,EAAEA,CAAA,KAAM;QAChB,IAAIqH,qBAAqB,GAAG,CAAC,EAAE;UAC7BhC,wBAAwB,CAACgC,qBAAqB,GAAG,CAAC,CAAC;QACrD;MACF,CAAE;MACFpH,QAAQ,EAAE0H,WAAY;MACtBzH,SAAS,EAAE,CAAAkF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE3E,IAAI,KAAI;IAAO;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CACF,CACF,EAEAmH,IAAI,KAAK,QAAQ,iBAChBlI,OAAA;MAAKwN,SAAS,EAAC,6EAA6E;MAAA7M,QAAA,GACzFmH,MAAM,CAAC2D,OAAO,KAAK,MAAM,iBAAIzL,OAAA,CAACX,QAAQ;QAACkF,KAAK,EAAEA,KAAM;QAACG,MAAM,EAAEA;MAAO;QAAA9D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAExEf,OAAA;QAAKwN,SAAS,EAAC,wBAAwB;QAAA7M,QAAA,eACrCX,OAAA;UAAKwN,SAAS,EAAC,+FAA+F;UAAA7M,QAAA,gBAE5GX,OAAA;YAAKwN,SAAS,EAAG,mCACf1F,MAAM,CAAC2D,OAAO,KAAK,MAAM,GACrB,sEAAsE,GACtE,oEACL,EAAE;YAAA9K,QAAA,eACDX,OAAA;cAAKwN,SAAS,EAAC,UAAU;cAAA7M,QAAA,gBACvBX,OAAA;gBAAKwN,SAAS,EAAG,iFACf1F,MAAM,CAAC2D,OAAO,KAAK,MAAM,GACrB,iDAAiD,GACjD,gDACL,EAAE;gBAAA9K,QAAA,eACDX,OAAA;kBACEyD,GAAG,EAAEqE,MAAM,CAAC2D,OAAO,KAAK,MAAM,GAAGtM,IAAI,GAAGC,IAAK;kBAC7CsE,GAAG,EAAEoE,MAAM,CAAC2D,OAAQ;kBACpB+B,SAAS,EAAC;gBAA0B;kBAAA5M,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNf,OAAA;gBAAIwN,SAAS,EAAG,2CACd1F,MAAM,CAAC2D,OAAO,KAAK,MAAM,GAAG,kBAAkB,GAAG,gBAClD,EAAE;gBAAA9K,QAAA,EACAmH,MAAM,CAAC2D,OAAO,KAAK,MAAM,GAAG,iBAAiB,GAAG;cAAe;gBAAA7K,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACLf,OAAA;gBAAGwN,SAAS,EAAC,qEAAqE;gBAAA7M,QAAA,EAC/EmH,MAAM,CAAC2D,OAAO,KAAK,MAAM,GACtB,+CAA+C,GAC/C;cAAgD;gBAAA7K,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNf,OAAA;YAAKwN,SAAS,EAAC,KAAK;YAAA7M,QAAA,gBAClBX,OAAA;cAAKwN,SAAS,EAAC,4CAA4C;cAAA7M,QAAA,gBAEzDX,OAAA;gBAAKwN,SAAS,EAAC,yKAAyK;gBAAA7M,QAAA,gBACtLX,OAAA;kBAAKwN,SAAS,EAAC;gBAAqI;kBAAA5M,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3Jf,OAAA;kBAAKwN,SAAS,EAAC,sBAAsB;kBAAA7M,QAAA,gBACnCX,OAAA;oBAAKwN,SAAS,EAAC,uDAAuD;oBAAA7M,QAAA,GACnEmB,IAAI,CAACuJ,KAAK,CAAE,CAAC,EAAA7D,qBAAA,GAAAM,MAAM,CAACyC,cAAc,cAAA/C,qBAAA,uBAArBA,qBAAA,CAAuBxD,MAAM,KAAI,CAAC,IAAIuB,SAAS,CAACvB,MAAM,GAAI,GAAG,CAAC,EAAC,GAC/E;kBAAA;oBAAApD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNf,OAAA;oBAAKwN,SAAS,EAAC,6DAA6D;oBAAA7M,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNf,OAAA;gBAAKwN,SAAS,EAAC,8KAA8K;gBAAA7M,QAAA,gBAC3LX,OAAA;kBAAKwN,SAAS,EAAC;gBAAwI;kBAAA5M,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9Jf,OAAA;kBAAKwN,SAAS,EAAC,sBAAsB;kBAAA7M,QAAA,gBACnCX,OAAA;oBAAKwN,SAAS,EAAC,0DAA0D;oBAAA7M,QAAA,GACtEU,KAAK,CAACC,OAAO,CAACwG,MAAM,CAACyC,cAAc,CAAC,GAAGzC,MAAM,CAACyC,cAAc,CAACvG,MAAM,GAAG,CAAC,EAAC,GAAC,EAACuB,SAAS,CAACvB,MAAM;kBAAA;oBAAApD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxF,CAAC,eACNf,OAAA;oBAAKwN,SAAS,EAAC,gEAAgE;oBAAA7M,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNf,OAAA;gBAAKwN,SAAS,EAAG,qGACf1F,MAAM,CAAC2D,OAAO,KAAK,MAAM,GACrB,4EAA4E,GAC5E,yEACL,EAAE;gBAAA9K,QAAA,gBACDX,OAAA;kBAAKwN,SAAS,EAAG,wGACf1F,MAAM,CAAC2D,OAAO,KAAK,MAAM,GAAG,oBAAoB,GAAG,kBACpD;gBAAiB;kBAAA7K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzBf,OAAA;kBAAKwN,SAAS,EAAC,sBAAsB;kBAAA7M,QAAA,gBACnCX,OAAA;oBAAKwN,SAAS,EAAG,2CACf1F,MAAM,CAAC2D,OAAO,KAAK,MAAM,GAAG,kBAAkB,GAAG,gBAClD,EAAE;oBAAA9K,QAAA,EACAmH,MAAM,CAAC2D,OAAO,KAAK,MAAM,GAAG,MAAM,GAAG;kBAAO;oBAAA7K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC,eACNf,OAAA;oBAAKwN,SAAS,EAAG,8CACf1F,MAAM,CAAC2D,OAAO,KAAK,MAAM,GAAG,qBAAqB,GAAG,mBACrD,EAAE;oBAAA9K,QAAA,EACAmH,MAAM,CAAC2D,OAAO,KAAK,MAAM,GAAG,UAAU,GAAI,QAAO7F,QAAQ,CAAC4F,YAAa;kBAAC;oBAAA5K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNf,OAAA;cAAKwN,SAAS,EAAC,MAAM;cAAA7M,QAAA,eACnBX,OAAA;gBAAKwN,SAAS,EAAC,uCAAuC;gBAAA7M,QAAA,gBACpDX,OAAA;kBAAKwN,SAAS,EAAC,kBAAkB;kBAAA7M,QAAA,gBAC/BX,OAAA;oBAAIwN,SAAS,EAAC,uCAAuC;oBAAA7M,QAAA,EAAC;kBAAoB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/Ef,OAAA;oBAAGwN,SAAS,EAAC,wBAAwB;oBAAA7M,QAAA,EAAC;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC,eACNf,OAAA;kBAAKwN,SAAS,EAAC,UAAU;kBAAA7M,QAAA,gBACvBX,OAAA;oBAAKwN,SAAS,EAAC,mEAAmE;oBAAA7M,QAAA,eAChFX,OAAA;sBACEwN,SAAS,EAAG,uFACV1F,MAAM,CAAC2D,OAAO,KAAK,MAAM,GACrB,6DAA6D,GAC7D,2DACL,EAAE;sBACHxJ,KAAK,EAAE;wBAAEsC,KAAK,EAAG,GAAG,CAAC,EAAAkD,sBAAA,GAAAK,MAAM,CAACyC,cAAc,cAAA9C,sBAAA,uBAArBA,sBAAA,CAAuBzD,MAAM,KAAI,CAAC,IAAIuB,SAAS,CAACvB,MAAM,GAAI,GAAI;sBAAG,CAAE;sBAAArD,QAAA,eAExFX,OAAA;wBAAKwN,SAAS,EAAC;sBAAgE;wBAAA5M,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNf,OAAA;oBAAKwN,SAAS,EAAC,wCAAwC;oBAAA7M,QAAA,gBACrDX,OAAA;sBAAMwN,SAAS,EAAC,oCAAoC;sBAAA7M,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC9Df,OAAA;sBAAMwN,SAAS,EAAG,qCAChB1F,MAAM,CAAC2D,OAAO,KAAK,MAAM,GAAG,kBAAkB,GAAG,gBAClD,EAAE;sBAAA9K,QAAA,GACAmB,IAAI,CAACuJ,KAAK,CAAE,CAAChK,KAAK,CAACC,OAAO,CAACwG,MAAM,CAACyC,cAAc,CAAC,GAAGzC,MAAM,CAACyC,cAAc,CAACvG,MAAM,GAAG,CAAC,IAAIuB,SAAS,CAACvB,MAAM,GAAI,GAAG,CAAC,EAAC,GACpH;oBAAA;sBAAApD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACPf,OAAA;sBAAMwN,SAAS,EAAC,oCAAoC;sBAAA7M,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGL+G,MAAM,CAACgE,MAAM,iBACZ9L,OAAA;cAAKwN,SAAS,EAAC,MAAM;cAAA7M,QAAA,eACnBX,OAAA,CAACJ,eAAe;gBAACkM,MAAM,EAAEhE,MAAM,CAACgE;cAAO;gBAAAlL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CACN,eAGDf,OAAA;cAAKwN,SAAS,EAAC,gDAAgD;cAAA7M,QAAA,eAC7DX,OAAA;gBACEwN,SAAS,EAAC,sPAAsP;gBAChQlJ,OAAO,EAAEA,CAAA,KAAMqB,OAAO,CAAC,QAAQ,CAAE;gBAAAhF,QAAA,gBAEjCX,OAAA;kBAAKwN,SAAS,EAAC;gBAAkI;kBAAA5M,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxJf,OAAA;kBAAMwN,SAAS,EAAC,UAAU;kBAAA7M,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEAmH,IAAI,KAAK,QAAQ,iBAChBlI,OAAA,CAACsF,qBAAqB;MACpBC,SAAS,EAAEA,SAAU;MACrBC,eAAe,EAAEA,eAAgB;MACjCC,YAAY,EAAEA,YAAa;MAC3BC,gBAAgB,EAAEA,gBAAiB;MACnCC,OAAO,EAAEA,OAAQ;MACjBC,QAAQ,EAAEA,QAAS;MACnBC,wBAAwB,EAAEA,wBAAyB;MACnDC,kBAAkB,EAAEA,kBAAmB;MACvCC,SAAS,EAAEA,SAAU;MACrBC,SAAS,EAAEA,SAAU;MACrBC,cAAc,EAAEA,cAAe;MAC/BC,eAAe,EAAEA;IAAgB;MAAAtF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC,GACJ,IAAI;AACV;AAACwG,EAAA,CAllBQD,SAAS;EAAA,QACQ3H,WAAW,EAMpBd,SAAS,EACPH,WAAW,EACXE,WAAW,EAOXD,WAAW,EAEFW,aAAa;AAAA;AAAA6O,GAAA,GAlBhC7G,SAAS;AAolBlB,eAAeA,SAAS;AAAC,IAAAjC,EAAA,EAAAgC,GAAA,EAAA8G,GAAA;AAAAC,YAAA,CAAA/I,EAAA;AAAA+I,YAAA,CAAA/G,GAAA;AAAA+G,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}