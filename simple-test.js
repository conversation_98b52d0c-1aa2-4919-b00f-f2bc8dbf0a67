// Simple test for Primary Kiswahili Medium level
const http = require('http');

const testData = JSON.stringify({
  firstName: "Am<PERSON>",
  lastName: "<PERSON><PERSON><PERSON><PERSON>", 
  username: "amina_kiswahili_test",
  email: "<EMAIL>",
  school: "<PERSON>le ya Msingi Kibada",
  level: "primary_kiswahili",
  class: "3",
  phoneNumber: "0754123457",
  password: "test123"
});

const options = {
  hostname: 'localhost',
  port: 5000,
  path: '/api/users/register',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(testData)
  }
};

console.log('🧪 Testing Primary Kiswahili Medium Registration...');
console.log('Test data:', JSON.parse(testData));

const req = http.request(options, (res) => {
  console.log(`\n📊 Status Code: ${res.statusCode}`);
  console.log(`📋 Headers:`, res.headers);

  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    console.log('\n📝 Response Body:');
    try {
      const response = JSON.parse(data);
      console.log(JSON.stringify(response, null, 2));
      
      if (res.statusCode === 200) {
        console.log('\n✅ SUCCESS: Primary Kiswahili Medium registration works!');
      } else if (res.statusCode === 409) {
        console.log('\n⚠️ User already exists (this is expected if running test multiple times)');
      } else {
        console.log('\n❌ Registration failed');
      }
    } catch (error) {
      console.log('Raw response:', data);
    }
  });
});

req.on('error', (error) => {
  console.error('❌ Request failed:', error.message);
});

req.write(testData);
req.end();
