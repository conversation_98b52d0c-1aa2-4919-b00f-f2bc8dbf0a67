{"ast": null, "code": "import warning from \"rc-util/es/warning\";\nfunction removeFromCheckedKeys(halfCheckedKeys, checkedKeys) {\n  var filteredKeys = new Set();\n  halfCheckedKeys.forEach(function (key) {\n    if (!checkedKeys.has(key)) {\n      filteredKeys.add(key);\n    }\n  });\n  return filteredKeys;\n}\nexport function isCheckDisabled(node) {\n  var _ref = node || {},\n    disabled = _ref.disabled,\n    disableCheckbox = _ref.disableCheckbox,\n    checkable = _ref.checkable;\n  return !!(disabled || disableCheckbox) || checkable === false;\n}\n// Fill miss keys\nfunction fillConductCheck(keys, levelEntities, maxLevel, syntheticGetCheckDisabled) {\n  var checkedKeys = new Set(keys);\n  var halfCheckedKeys = new Set();\n  // Add checked keys top to bottom\n  for (var level = 0; level <= maxLevel; level += 1) {\n    var entities = levelEntities.get(level) || new Set();\n    entities.forEach(function (entity) {\n      var key = entity.key,\n        node = entity.node,\n        _entity$children = entity.children,\n        children = _entity$children === void 0 ? [] : _entity$children;\n      if (checkedKeys.has(key) && !syntheticGetCheckDisabled(node)) {\n        children.filter(function (childEntity) {\n          return !syntheticGetCheckDisabled(childEntity.node);\n        }).forEach(function (childEntity) {\n          checkedKeys.add(childEntity.key);\n        });\n      }\n    });\n  }\n  // Add checked keys from bottom to top\n  var visitedKeys = new Set();\n  for (var _level = maxLevel; _level >= 0; _level -= 1) {\n    var _entities = levelEntities.get(_level) || new Set();\n    _entities.forEach(function (entity) {\n      var parent = entity.parent,\n        node = entity.node;\n      // Skip if no need to check\n      if (syntheticGetCheckDisabled(node) || !entity.parent || visitedKeys.has(entity.parent.key)) {\n        return;\n      }\n      // Skip if parent is disabled\n      if (syntheticGetCheckDisabled(entity.parent.node)) {\n        visitedKeys.add(parent.key);\n        return;\n      }\n      var allChecked = true;\n      var partialChecked = false;\n      (parent.children || []).filter(function (childEntity) {\n        return !syntheticGetCheckDisabled(childEntity.node);\n      }).forEach(function (_ref2) {\n        var key = _ref2.key;\n        var checked = checkedKeys.has(key);\n        if (allChecked && !checked) {\n          allChecked = false;\n        }\n        if (!partialChecked && (checked || halfCheckedKeys.has(key))) {\n          partialChecked = true;\n        }\n      });\n      if (allChecked) {\n        checkedKeys.add(parent.key);\n      }\n      if (partialChecked) {\n        halfCheckedKeys.add(parent.key);\n      }\n      visitedKeys.add(parent.key);\n    });\n  }\n  return {\n    checkedKeys: Array.from(checkedKeys),\n    halfCheckedKeys: Array.from(removeFromCheckedKeys(halfCheckedKeys, checkedKeys))\n  };\n}\n// Remove useless key\nfunction cleanConductCheck(keys, halfKeys, levelEntities, maxLevel, syntheticGetCheckDisabled) {\n  var checkedKeys = new Set(keys);\n  var halfCheckedKeys = new Set(halfKeys);\n  // Remove checked keys from top to bottom\n  for (var level = 0; level <= maxLevel; level += 1) {\n    var entities = levelEntities.get(level) || new Set();\n    entities.forEach(function (entity) {\n      var key = entity.key,\n        node = entity.node,\n        _entity$children2 = entity.children,\n        children = _entity$children2 === void 0 ? [] : _entity$children2;\n      if (!checkedKeys.has(key) && !halfCheckedKeys.has(key) && !syntheticGetCheckDisabled(node)) {\n        children.filter(function (childEntity) {\n          return !syntheticGetCheckDisabled(childEntity.node);\n        }).forEach(function (childEntity) {\n          checkedKeys.delete(childEntity.key);\n        });\n      }\n    });\n  }\n  // Remove checked keys form bottom to top\n  halfCheckedKeys = new Set();\n  var visitedKeys = new Set();\n  for (var _level2 = maxLevel; _level2 >= 0; _level2 -= 1) {\n    var _entities2 = levelEntities.get(_level2) || new Set();\n    _entities2.forEach(function (entity) {\n      var parent = entity.parent,\n        node = entity.node;\n      // Skip if no need to check\n      if (syntheticGetCheckDisabled(node) || !entity.parent || visitedKeys.has(entity.parent.key)) {\n        return;\n      }\n      // Skip if parent is disabled\n      if (syntheticGetCheckDisabled(entity.parent.node)) {\n        visitedKeys.add(parent.key);\n        return;\n      }\n      var allChecked = true;\n      var partialChecked = false;\n      (parent.children || []).filter(function (childEntity) {\n        return !syntheticGetCheckDisabled(childEntity.node);\n      }).forEach(function (_ref3) {\n        var key = _ref3.key;\n        var checked = checkedKeys.has(key);\n        if (allChecked && !checked) {\n          allChecked = false;\n        }\n        if (!partialChecked && (checked || halfCheckedKeys.has(key))) {\n          partialChecked = true;\n        }\n      });\n      if (!allChecked) {\n        checkedKeys.delete(parent.key);\n      }\n      if (partialChecked) {\n        halfCheckedKeys.add(parent.key);\n      }\n      visitedKeys.add(parent.key);\n    });\n  }\n  return {\n    checkedKeys: Array.from(checkedKeys),\n    halfCheckedKeys: Array.from(removeFromCheckedKeys(halfCheckedKeys, checkedKeys))\n  };\n}\n/**\n * Conduct with keys.\n * @param keyList current key list\n * @param keyEntities key - dataEntity map\n * @param mode `fill` to fill missing key, `clean` to remove useless key\n */\nexport function conductCheck(keyList, checked, keyEntities, getCheckDisabled) {\n  var warningMissKeys = [];\n  var syntheticGetCheckDisabled;\n  if (getCheckDisabled) {\n    syntheticGetCheckDisabled = getCheckDisabled;\n  } else {\n    syntheticGetCheckDisabled = isCheckDisabled;\n  }\n  // We only handle exist keys\n  var keys = new Set(keyList.filter(function (key) {\n    var hasEntity = !!keyEntities[key];\n    if (!hasEntity) {\n      warningMissKeys.push(key);\n    }\n    return hasEntity;\n  }));\n  var levelEntities = new Map();\n  var maxLevel = 0;\n  // Convert entities by level for calculation\n  Object.keys(keyEntities).forEach(function (key) {\n    var entity = keyEntities[key];\n    var level = entity.level;\n    var levelSet = levelEntities.get(level);\n    if (!levelSet) {\n      levelSet = new Set();\n      levelEntities.set(level, levelSet);\n    }\n    levelSet.add(entity);\n    maxLevel = Math.max(maxLevel, level);\n  });\n  warning(!warningMissKeys.length, \"Tree missing follow keys: \".concat(warningMissKeys.slice(0, 100).map(function (key) {\n    return \"'\".concat(key, \"'\");\n  }).join(', ')));\n  var result;\n  if (checked === true) {\n    result = fillConductCheck(keys, levelEntities, maxLevel, syntheticGetCheckDisabled);\n  } else {\n    result = cleanConductCheck(keys, checked.halfCheckedKeys, levelEntities, maxLevel, syntheticGetCheckDisabled);\n  }\n  return result;\n}", "map": {"version": 3, "names": ["warning", "removeFromCheckedKeys", "halfC<PERSON>cked<PERSON>eys", "checked<PERSON>eys", "filtered<PERSON>eys", "Set", "for<PERSON>ach", "key", "has", "add", "isCheckDisabled", "node", "_ref", "disabled", "disableCheckbox", "checkable", "fillConductCheck", "keys", "levelEntities", "maxLevel", "syntheticGetCheckDisabled", "level", "entities", "get", "entity", "_entity$children", "children", "filter", "childEntity", "visitedKeys", "_level", "_entities", "parent", "allChecked", "partialChecked", "_ref2", "checked", "Array", "from", "cleanConductCheck", "halfKeys", "_entity$children2", "delete", "_level2", "_entities2", "_ref3", "conduct<PERSON>heck", "keyList", "keyEntities", "getCheckDisabled", "warningMiss<PERSON>eys", "hasEntity", "push", "Map", "Object", "levelSet", "set", "Math", "max", "length", "concat", "slice", "map", "join", "result"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-tree/es/utils/conductUtil.js"], "sourcesContent": ["import warning from \"rc-util/es/warning\";\nfunction removeFromCheckedKeys(halfCheckedKeys, checkedKeys) {\n  var filteredKeys = new Set();\n  halfCheckedKeys.forEach(function (key) {\n    if (!checkedKeys.has(key)) {\n      filteredKeys.add(key);\n    }\n  });\n  return filteredKeys;\n}\nexport function isCheckDisabled(node) {\n  var _ref = node || {},\n    disabled = _ref.disabled,\n    disableCheckbox = _ref.disableCheckbox,\n    checkable = _ref.checkable;\n  return !!(disabled || disableCheckbox) || checkable === false;\n}\n// Fill miss keys\nfunction fillConductCheck(keys, levelEntities, maxLevel, syntheticGetCheckDisabled) {\n  var checkedKeys = new Set(keys);\n  var halfCheckedKeys = new Set();\n  // Add checked keys top to bottom\n  for (var level = 0; level <= maxLevel; level += 1) {\n    var entities = levelEntities.get(level) || new Set();\n    entities.forEach(function (entity) {\n      var key = entity.key,\n        node = entity.node,\n        _entity$children = entity.children,\n        children = _entity$children === void 0 ? [] : _entity$children;\n      if (checkedKeys.has(key) && !syntheticGetCheckDisabled(node)) {\n        children.filter(function (childEntity) {\n          return !syntheticGetCheckDisabled(childEntity.node);\n        }).forEach(function (childEntity) {\n          checkedKeys.add(childEntity.key);\n        });\n      }\n    });\n  }\n  // Add checked keys from bottom to top\n  var visitedKeys = new Set();\n  for (var _level = maxLevel; _level >= 0; _level -= 1) {\n    var _entities = levelEntities.get(_level) || new Set();\n    _entities.forEach(function (entity) {\n      var parent = entity.parent,\n        node = entity.node;\n      // Skip if no need to check\n      if (syntheticGetCheckDisabled(node) || !entity.parent || visitedKeys.has(entity.parent.key)) {\n        return;\n      }\n      // Skip if parent is disabled\n      if (syntheticGetCheckDisabled(entity.parent.node)) {\n        visitedKeys.add(parent.key);\n        return;\n      }\n      var allChecked = true;\n      var partialChecked = false;\n      (parent.children || []).filter(function (childEntity) {\n        return !syntheticGetCheckDisabled(childEntity.node);\n      }).forEach(function (_ref2) {\n        var key = _ref2.key;\n        var checked = checkedKeys.has(key);\n        if (allChecked && !checked) {\n          allChecked = false;\n        }\n        if (!partialChecked && (checked || halfCheckedKeys.has(key))) {\n          partialChecked = true;\n        }\n      });\n      if (allChecked) {\n        checkedKeys.add(parent.key);\n      }\n      if (partialChecked) {\n        halfCheckedKeys.add(parent.key);\n      }\n      visitedKeys.add(parent.key);\n    });\n  }\n  return {\n    checkedKeys: Array.from(checkedKeys),\n    halfCheckedKeys: Array.from(removeFromCheckedKeys(halfCheckedKeys, checkedKeys))\n  };\n}\n// Remove useless key\nfunction cleanConductCheck(keys, halfKeys, levelEntities, maxLevel, syntheticGetCheckDisabled) {\n  var checkedKeys = new Set(keys);\n  var halfCheckedKeys = new Set(halfKeys);\n  // Remove checked keys from top to bottom\n  for (var level = 0; level <= maxLevel; level += 1) {\n    var entities = levelEntities.get(level) || new Set();\n    entities.forEach(function (entity) {\n      var key = entity.key,\n        node = entity.node,\n        _entity$children2 = entity.children,\n        children = _entity$children2 === void 0 ? [] : _entity$children2;\n      if (!checkedKeys.has(key) && !halfCheckedKeys.has(key) && !syntheticGetCheckDisabled(node)) {\n        children.filter(function (childEntity) {\n          return !syntheticGetCheckDisabled(childEntity.node);\n        }).forEach(function (childEntity) {\n          checkedKeys.delete(childEntity.key);\n        });\n      }\n    });\n  }\n  // Remove checked keys form bottom to top\n  halfCheckedKeys = new Set();\n  var visitedKeys = new Set();\n  for (var _level2 = maxLevel; _level2 >= 0; _level2 -= 1) {\n    var _entities2 = levelEntities.get(_level2) || new Set();\n    _entities2.forEach(function (entity) {\n      var parent = entity.parent,\n        node = entity.node;\n      // Skip if no need to check\n      if (syntheticGetCheckDisabled(node) || !entity.parent || visitedKeys.has(entity.parent.key)) {\n        return;\n      }\n      // Skip if parent is disabled\n      if (syntheticGetCheckDisabled(entity.parent.node)) {\n        visitedKeys.add(parent.key);\n        return;\n      }\n      var allChecked = true;\n      var partialChecked = false;\n      (parent.children || []).filter(function (childEntity) {\n        return !syntheticGetCheckDisabled(childEntity.node);\n      }).forEach(function (_ref3) {\n        var key = _ref3.key;\n        var checked = checkedKeys.has(key);\n        if (allChecked && !checked) {\n          allChecked = false;\n        }\n        if (!partialChecked && (checked || halfCheckedKeys.has(key))) {\n          partialChecked = true;\n        }\n      });\n      if (!allChecked) {\n        checkedKeys.delete(parent.key);\n      }\n      if (partialChecked) {\n        halfCheckedKeys.add(parent.key);\n      }\n      visitedKeys.add(parent.key);\n    });\n  }\n  return {\n    checkedKeys: Array.from(checkedKeys),\n    halfCheckedKeys: Array.from(removeFromCheckedKeys(halfCheckedKeys, checkedKeys))\n  };\n}\n/**\n * Conduct with keys.\n * @param keyList current key list\n * @param keyEntities key - dataEntity map\n * @param mode `fill` to fill missing key, `clean` to remove useless key\n */\nexport function conductCheck(keyList, checked, keyEntities, getCheckDisabled) {\n  var warningMissKeys = [];\n  var syntheticGetCheckDisabled;\n  if (getCheckDisabled) {\n    syntheticGetCheckDisabled = getCheckDisabled;\n  } else {\n    syntheticGetCheckDisabled = isCheckDisabled;\n  }\n  // We only handle exist keys\n  var keys = new Set(keyList.filter(function (key) {\n    var hasEntity = !!keyEntities[key];\n    if (!hasEntity) {\n      warningMissKeys.push(key);\n    }\n    return hasEntity;\n  }));\n  var levelEntities = new Map();\n  var maxLevel = 0;\n  // Convert entities by level for calculation\n  Object.keys(keyEntities).forEach(function (key) {\n    var entity = keyEntities[key];\n    var level = entity.level;\n    var levelSet = levelEntities.get(level);\n    if (!levelSet) {\n      levelSet = new Set();\n      levelEntities.set(level, levelSet);\n    }\n    levelSet.add(entity);\n    maxLevel = Math.max(maxLevel, level);\n  });\n  warning(!warningMissKeys.length, \"Tree missing follow keys: \".concat(warningMissKeys.slice(0, 100).map(function (key) {\n    return \"'\".concat(key, \"'\");\n  }).join(', ')));\n  var result;\n  if (checked === true) {\n    result = fillConductCheck(keys, levelEntities, maxLevel, syntheticGetCheckDisabled);\n  } else {\n    result = cleanConductCheck(keys, checked.halfCheckedKeys, levelEntities, maxLevel, syntheticGetCheckDisabled);\n  }\n  return result;\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,oBAAoB;AACxC,SAASC,qBAAqBA,CAACC,eAAe,EAAEC,WAAW,EAAE;EAC3D,IAAIC,YAAY,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC5BH,eAAe,CAACI,OAAO,CAAC,UAAUC,GAAG,EAAE;IACrC,IAAI,CAACJ,WAAW,CAACK,GAAG,CAACD,GAAG,CAAC,EAAE;MACzBH,YAAY,CAACK,GAAG,CAACF,GAAG,CAAC;IACvB;EACF,CAAC,CAAC;EACF,OAAOH,YAAY;AACrB;AACA,OAAO,SAASM,eAAeA,CAACC,IAAI,EAAE;EACpC,IAAIC,IAAI,GAAGD,IAAI,IAAI,CAAC,CAAC;IACnBE,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IACxBC,eAAe,GAAGF,IAAI,CAACE,eAAe;IACtCC,SAAS,GAAGH,IAAI,CAACG,SAAS;EAC5B,OAAO,CAAC,EAAEF,QAAQ,IAAIC,eAAe,CAAC,IAAIC,SAAS,KAAK,KAAK;AAC/D;AACA;AACA,SAASC,gBAAgBA,CAACC,IAAI,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,yBAAyB,EAAE;EAClF,IAAIjB,WAAW,GAAG,IAAIE,GAAG,CAACY,IAAI,CAAC;EAC/B,IAAIf,eAAe,GAAG,IAAIG,GAAG,CAAC,CAAC;EAC/B;EACA,KAAK,IAAIgB,KAAK,GAAG,CAAC,EAAEA,KAAK,IAAIF,QAAQ,EAAEE,KAAK,IAAI,CAAC,EAAE;IACjD,IAAIC,QAAQ,GAAGJ,aAAa,CAACK,GAAG,CAACF,KAAK,CAAC,IAAI,IAAIhB,GAAG,CAAC,CAAC;IACpDiB,QAAQ,CAAChB,OAAO,CAAC,UAAUkB,MAAM,EAAE;MACjC,IAAIjB,GAAG,GAAGiB,MAAM,CAACjB,GAAG;QAClBI,IAAI,GAAGa,MAAM,CAACb,IAAI;QAClBc,gBAAgB,GAAGD,MAAM,CAACE,QAAQ;QAClCA,QAAQ,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,gBAAgB;MAChE,IAAItB,WAAW,CAACK,GAAG,CAACD,GAAG,CAAC,IAAI,CAACa,yBAAyB,CAACT,IAAI,CAAC,EAAE;QAC5De,QAAQ,CAACC,MAAM,CAAC,UAAUC,WAAW,EAAE;UACrC,OAAO,CAACR,yBAAyB,CAACQ,WAAW,CAACjB,IAAI,CAAC;QACrD,CAAC,CAAC,CAACL,OAAO,CAAC,UAAUsB,WAAW,EAAE;UAChCzB,WAAW,CAACM,GAAG,CAACmB,WAAW,CAACrB,GAAG,CAAC;QAClC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;EACA;EACA,IAAIsB,WAAW,GAAG,IAAIxB,GAAG,CAAC,CAAC;EAC3B,KAAK,IAAIyB,MAAM,GAAGX,QAAQ,EAAEW,MAAM,IAAI,CAAC,EAAEA,MAAM,IAAI,CAAC,EAAE;IACpD,IAAIC,SAAS,GAAGb,aAAa,CAACK,GAAG,CAACO,MAAM,CAAC,IAAI,IAAIzB,GAAG,CAAC,CAAC;IACtD0B,SAAS,CAACzB,OAAO,CAAC,UAAUkB,MAAM,EAAE;MAClC,IAAIQ,MAAM,GAAGR,MAAM,CAACQ,MAAM;QACxBrB,IAAI,GAAGa,MAAM,CAACb,IAAI;MACpB;MACA,IAAIS,yBAAyB,CAACT,IAAI,CAAC,IAAI,CAACa,MAAM,CAACQ,MAAM,IAAIH,WAAW,CAACrB,GAAG,CAACgB,MAAM,CAACQ,MAAM,CAACzB,GAAG,CAAC,EAAE;QAC3F;MACF;MACA;MACA,IAAIa,yBAAyB,CAACI,MAAM,CAACQ,MAAM,CAACrB,IAAI,CAAC,EAAE;QACjDkB,WAAW,CAACpB,GAAG,CAACuB,MAAM,CAACzB,GAAG,CAAC;QAC3B;MACF;MACA,IAAI0B,UAAU,GAAG,IAAI;MACrB,IAAIC,cAAc,GAAG,KAAK;MAC1B,CAACF,MAAM,CAACN,QAAQ,IAAI,EAAE,EAAEC,MAAM,CAAC,UAAUC,WAAW,EAAE;QACpD,OAAO,CAACR,yBAAyB,CAACQ,WAAW,CAACjB,IAAI,CAAC;MACrD,CAAC,CAAC,CAACL,OAAO,CAAC,UAAU6B,KAAK,EAAE;QAC1B,IAAI5B,GAAG,GAAG4B,KAAK,CAAC5B,GAAG;QACnB,IAAI6B,OAAO,GAAGjC,WAAW,CAACK,GAAG,CAACD,GAAG,CAAC;QAClC,IAAI0B,UAAU,IAAI,CAACG,OAAO,EAAE;UAC1BH,UAAU,GAAG,KAAK;QACpB;QACA,IAAI,CAACC,cAAc,KAAKE,OAAO,IAAIlC,eAAe,CAACM,GAAG,CAACD,GAAG,CAAC,CAAC,EAAE;UAC5D2B,cAAc,GAAG,IAAI;QACvB;MACF,CAAC,CAAC;MACF,IAAID,UAAU,EAAE;QACd9B,WAAW,CAACM,GAAG,CAACuB,MAAM,CAACzB,GAAG,CAAC;MAC7B;MACA,IAAI2B,cAAc,EAAE;QAClBhC,eAAe,CAACO,GAAG,CAACuB,MAAM,CAACzB,GAAG,CAAC;MACjC;MACAsB,WAAW,CAACpB,GAAG,CAACuB,MAAM,CAACzB,GAAG,CAAC;IAC7B,CAAC,CAAC;EACJ;EACA,OAAO;IACLJ,WAAW,EAAEkC,KAAK,CAACC,IAAI,CAACnC,WAAW,CAAC;IACpCD,eAAe,EAAEmC,KAAK,CAACC,IAAI,CAACrC,qBAAqB,CAACC,eAAe,EAAEC,WAAW,CAAC;EACjF,CAAC;AACH;AACA;AACA,SAASoC,iBAAiBA,CAACtB,IAAI,EAAEuB,QAAQ,EAAEtB,aAAa,EAAEC,QAAQ,EAAEC,yBAAyB,EAAE;EAC7F,IAAIjB,WAAW,GAAG,IAAIE,GAAG,CAACY,IAAI,CAAC;EAC/B,IAAIf,eAAe,GAAG,IAAIG,GAAG,CAACmC,QAAQ,CAAC;EACvC;EACA,KAAK,IAAInB,KAAK,GAAG,CAAC,EAAEA,KAAK,IAAIF,QAAQ,EAAEE,KAAK,IAAI,CAAC,EAAE;IACjD,IAAIC,QAAQ,GAAGJ,aAAa,CAACK,GAAG,CAACF,KAAK,CAAC,IAAI,IAAIhB,GAAG,CAAC,CAAC;IACpDiB,QAAQ,CAAChB,OAAO,CAAC,UAAUkB,MAAM,EAAE;MACjC,IAAIjB,GAAG,GAAGiB,MAAM,CAACjB,GAAG;QAClBI,IAAI,GAAGa,MAAM,CAACb,IAAI;QAClB8B,iBAAiB,GAAGjB,MAAM,CAACE,QAAQ;QACnCA,QAAQ,GAAGe,iBAAiB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,iBAAiB;MAClE,IAAI,CAACtC,WAAW,CAACK,GAAG,CAACD,GAAG,CAAC,IAAI,CAACL,eAAe,CAACM,GAAG,CAACD,GAAG,CAAC,IAAI,CAACa,yBAAyB,CAACT,IAAI,CAAC,EAAE;QAC1Fe,QAAQ,CAACC,MAAM,CAAC,UAAUC,WAAW,EAAE;UACrC,OAAO,CAACR,yBAAyB,CAACQ,WAAW,CAACjB,IAAI,CAAC;QACrD,CAAC,CAAC,CAACL,OAAO,CAAC,UAAUsB,WAAW,EAAE;UAChCzB,WAAW,CAACuC,MAAM,CAACd,WAAW,CAACrB,GAAG,CAAC;QACrC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;EACA;EACAL,eAAe,GAAG,IAAIG,GAAG,CAAC,CAAC;EAC3B,IAAIwB,WAAW,GAAG,IAAIxB,GAAG,CAAC,CAAC;EAC3B,KAAK,IAAIsC,OAAO,GAAGxB,QAAQ,EAAEwB,OAAO,IAAI,CAAC,EAAEA,OAAO,IAAI,CAAC,EAAE;IACvD,IAAIC,UAAU,GAAG1B,aAAa,CAACK,GAAG,CAACoB,OAAO,CAAC,IAAI,IAAItC,GAAG,CAAC,CAAC;IACxDuC,UAAU,CAACtC,OAAO,CAAC,UAAUkB,MAAM,EAAE;MACnC,IAAIQ,MAAM,GAAGR,MAAM,CAACQ,MAAM;QACxBrB,IAAI,GAAGa,MAAM,CAACb,IAAI;MACpB;MACA,IAAIS,yBAAyB,CAACT,IAAI,CAAC,IAAI,CAACa,MAAM,CAACQ,MAAM,IAAIH,WAAW,CAACrB,GAAG,CAACgB,MAAM,CAACQ,MAAM,CAACzB,GAAG,CAAC,EAAE;QAC3F;MACF;MACA;MACA,IAAIa,yBAAyB,CAACI,MAAM,CAACQ,MAAM,CAACrB,IAAI,CAAC,EAAE;QACjDkB,WAAW,CAACpB,GAAG,CAACuB,MAAM,CAACzB,GAAG,CAAC;QAC3B;MACF;MACA,IAAI0B,UAAU,GAAG,IAAI;MACrB,IAAIC,cAAc,GAAG,KAAK;MAC1B,CAACF,MAAM,CAACN,QAAQ,IAAI,EAAE,EAAEC,MAAM,CAAC,UAAUC,WAAW,EAAE;QACpD,OAAO,CAACR,yBAAyB,CAACQ,WAAW,CAACjB,IAAI,CAAC;MACrD,CAAC,CAAC,CAACL,OAAO,CAAC,UAAUuC,KAAK,EAAE;QAC1B,IAAItC,GAAG,GAAGsC,KAAK,CAACtC,GAAG;QACnB,IAAI6B,OAAO,GAAGjC,WAAW,CAACK,GAAG,CAACD,GAAG,CAAC;QAClC,IAAI0B,UAAU,IAAI,CAACG,OAAO,EAAE;UAC1BH,UAAU,GAAG,KAAK;QACpB;QACA,IAAI,CAACC,cAAc,KAAKE,OAAO,IAAIlC,eAAe,CAACM,GAAG,CAACD,GAAG,CAAC,CAAC,EAAE;UAC5D2B,cAAc,GAAG,IAAI;QACvB;MACF,CAAC,CAAC;MACF,IAAI,CAACD,UAAU,EAAE;QACf9B,WAAW,CAACuC,MAAM,CAACV,MAAM,CAACzB,GAAG,CAAC;MAChC;MACA,IAAI2B,cAAc,EAAE;QAClBhC,eAAe,CAACO,GAAG,CAACuB,MAAM,CAACzB,GAAG,CAAC;MACjC;MACAsB,WAAW,CAACpB,GAAG,CAACuB,MAAM,CAACzB,GAAG,CAAC;IAC7B,CAAC,CAAC;EACJ;EACA,OAAO;IACLJ,WAAW,EAAEkC,KAAK,CAACC,IAAI,CAACnC,WAAW,CAAC;IACpCD,eAAe,EAAEmC,KAAK,CAACC,IAAI,CAACrC,qBAAqB,CAACC,eAAe,EAAEC,WAAW,CAAC;EACjF,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS2C,YAAYA,CAACC,OAAO,EAAEX,OAAO,EAAEY,WAAW,EAAEC,gBAAgB,EAAE;EAC5E,IAAIC,eAAe,GAAG,EAAE;EACxB,IAAI9B,yBAAyB;EAC7B,IAAI6B,gBAAgB,EAAE;IACpB7B,yBAAyB,GAAG6B,gBAAgB;EAC9C,CAAC,MAAM;IACL7B,yBAAyB,GAAGV,eAAe;EAC7C;EACA;EACA,IAAIO,IAAI,GAAG,IAAIZ,GAAG,CAAC0C,OAAO,CAACpB,MAAM,CAAC,UAAUpB,GAAG,EAAE;IAC/C,IAAI4C,SAAS,GAAG,CAAC,CAACH,WAAW,CAACzC,GAAG,CAAC;IAClC,IAAI,CAAC4C,SAAS,EAAE;MACdD,eAAe,CAACE,IAAI,CAAC7C,GAAG,CAAC;IAC3B;IACA,OAAO4C,SAAS;EAClB,CAAC,CAAC,CAAC;EACH,IAAIjC,aAAa,GAAG,IAAImC,GAAG,CAAC,CAAC;EAC7B,IAAIlC,QAAQ,GAAG,CAAC;EAChB;EACAmC,MAAM,CAACrC,IAAI,CAAC+B,WAAW,CAAC,CAAC1C,OAAO,CAAC,UAAUC,GAAG,EAAE;IAC9C,IAAIiB,MAAM,GAAGwB,WAAW,CAACzC,GAAG,CAAC;IAC7B,IAAIc,KAAK,GAAGG,MAAM,CAACH,KAAK;IACxB,IAAIkC,QAAQ,GAAGrC,aAAa,CAACK,GAAG,CAACF,KAAK,CAAC;IACvC,IAAI,CAACkC,QAAQ,EAAE;MACbA,QAAQ,GAAG,IAAIlD,GAAG,CAAC,CAAC;MACpBa,aAAa,CAACsC,GAAG,CAACnC,KAAK,EAAEkC,QAAQ,CAAC;IACpC;IACAA,QAAQ,CAAC9C,GAAG,CAACe,MAAM,CAAC;IACpBL,QAAQ,GAAGsC,IAAI,CAACC,GAAG,CAACvC,QAAQ,EAAEE,KAAK,CAAC;EACtC,CAAC,CAAC;EACFrB,OAAO,CAAC,CAACkD,eAAe,CAACS,MAAM,EAAE,4BAA4B,CAACC,MAAM,CAACV,eAAe,CAACW,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAACC,GAAG,CAAC,UAAUvD,GAAG,EAAE;IACpH,OAAO,GAAG,CAACqD,MAAM,CAACrD,GAAG,EAAE,GAAG,CAAC;EAC7B,CAAC,CAAC,CAACwD,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;EACf,IAAIC,MAAM;EACV,IAAI5B,OAAO,KAAK,IAAI,EAAE;IACpB4B,MAAM,GAAGhD,gBAAgB,CAACC,IAAI,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,yBAAyB,CAAC;EACrF,CAAC,MAAM;IACL4C,MAAM,GAAGzB,iBAAiB,CAACtB,IAAI,EAAEmB,OAAO,CAAClC,eAAe,EAAEgB,aAAa,EAAEC,QAAQ,EAAEC,yBAAyB,CAAC;EAC/G;EACA,OAAO4C,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}