{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"fallback\", \"src\", \"imgRef\"],\n  _excluded2 = [\"prefixCls\", \"src\", \"alt\", \"fallback\", \"movable\", \"onClose\", \"visible\", \"icons\", \"rootClassName\", \"closeIcon\", \"getContainer\", \"current\", \"count\", \"countRender\", \"scaleStep\", \"minScale\", \"maxScale\", \"transitionName\", \"maskTransitionName\", \"imageRender\", \"imgCommonProps\", \"toolbarRender\", \"onTransform\", \"onChange\"];\nimport classnames from 'classnames';\nimport Dialog from 'rc-dialog';\nimport addEventListener from \"rc-util/es/Dom/addEventListener\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport { warning } from \"rc-util/es/warning\";\nimport React, { useContext, useEffect, useRef, useState } from 'react';\nimport { PreviewGroupContext } from \"./context\";\nimport getFixScaleEleTransPosition from \"./getFixScaleEleTransPosition\";\nimport useImageTransform from \"./hooks/useImageTransform\";\nimport useStatus from \"./hooks/useStatus\";\nimport Operations from \"./Operations\";\nimport { BASE_SCALE_RATIO, WHEEL_MAX_SCALE_RATIO } from \"./previewConfig\";\nvar PreviewImage = function PreviewImage(_ref) {\n  var fallback = _ref.fallback,\n    src = _ref.src,\n    imgRef = _ref.imgRef,\n    props = _objectWithoutProperties(_ref, _excluded);\n  var _useStatus = useStatus({\n      src: src,\n      fallback: fallback\n    }),\n    _useStatus2 = _slicedToArray(_useStatus, 2),\n    getImgRef = _useStatus2[0],\n    srcAndOnload = _useStatus2[1];\n  return /*#__PURE__*/React.createElement(\"img\", _extends({\n    ref: function ref(_ref2) {\n      imgRef.current = _ref2;\n      getImgRef(_ref2);\n    }\n  }, props, srcAndOnload));\n};\nvar Preview = function Preview(props) {\n  var prefixCls = props.prefixCls,\n    src = props.src,\n    alt = props.alt,\n    fallback = props.fallback,\n    _props$movable = props.movable,\n    movable = _props$movable === void 0 ? true : _props$movable,\n    onClose = props.onClose,\n    visible = props.visible,\n    _props$icons = props.icons,\n    icons = _props$icons === void 0 ? {} : _props$icons,\n    rootClassName = props.rootClassName,\n    closeIcon = props.closeIcon,\n    getContainer = props.getContainer,\n    _props$current = props.current,\n    current = _props$current === void 0 ? 0 : _props$current,\n    _props$count = props.count,\n    count = _props$count === void 0 ? 1 : _props$count,\n    countRender = props.countRender,\n    _props$scaleStep = props.scaleStep,\n    scaleStep = _props$scaleStep === void 0 ? 0.5 : _props$scaleStep,\n    _props$minScale = props.minScale,\n    minScale = _props$minScale === void 0 ? 1 : _props$minScale,\n    _props$maxScale = props.maxScale,\n    maxScale = _props$maxScale === void 0 ? 50 : _props$maxScale,\n    _props$transitionName = props.transitionName,\n    transitionName = _props$transitionName === void 0 ? 'zoom' : _props$transitionName,\n    _props$maskTransition = props.maskTransitionName,\n    maskTransitionName = _props$maskTransition === void 0 ? 'fade' : _props$maskTransition,\n    imageRender = props.imageRender,\n    imgCommonProps = props.imgCommonProps,\n    toolbarRender = props.toolbarRender,\n    onTransform = props.onTransform,\n    onChange = props.onChange,\n    restProps = _objectWithoutProperties(props, _excluded2);\n  var imgRef = useRef();\n  var downPositionRef = useRef({\n    deltaX: 0,\n    deltaY: 0,\n    transformX: 0,\n    transformY: 0\n  });\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    isMoving = _useState2[0],\n    setMoving = _useState2[1];\n  var groupContext = useContext(PreviewGroupContext);\n  var showLeftOrRightSwitches = groupContext && count > 1;\n  var showOperationsProgress = groupContext && count >= 1;\n  var _useImageTransform = useImageTransform(imgRef, minScale, maxScale, onTransform),\n    transform = _useImageTransform.transform,\n    resetTransform = _useImageTransform.resetTransform,\n    updateTransform = _useImageTransform.updateTransform,\n    dispatchZoomChange = _useImageTransform.dispatchZoomChange;\n  var _useState3 = useState(true),\n    _useState4 = _slicedToArray(_useState3, 2),\n    enableTransition = _useState4[0],\n    setEnableTransition = _useState4[1];\n  var rotate = transform.rotate,\n    scale = transform.scale,\n    x = transform.x,\n    y = transform.y;\n  var wrapClassName = classnames(_defineProperty({}, \"\".concat(prefixCls, \"-moving\"), isMoving));\n  useEffect(function () {\n    if (!enableTransition) {\n      setEnableTransition(true);\n    }\n  }, [enableTransition]);\n  var onAfterClose = function onAfterClose() {\n    resetTransform('close');\n  };\n  var onZoomIn = function onZoomIn() {\n    dispatchZoomChange(BASE_SCALE_RATIO + scaleStep, 'zoomIn');\n  };\n  var onZoomOut = function onZoomOut() {\n    dispatchZoomChange(BASE_SCALE_RATIO / (BASE_SCALE_RATIO + scaleStep), 'zoomOut');\n  };\n  var onRotateRight = function onRotateRight() {\n    updateTransform({\n      rotate: rotate + 90\n    }, 'rotateRight');\n  };\n  var onRotateLeft = function onRotateLeft() {\n    updateTransform({\n      rotate: rotate - 90\n    }, 'rotateLeft');\n  };\n  var onFlipX = function onFlipX() {\n    updateTransform({\n      flipX: !transform.flipX\n    }, 'flipX');\n  };\n  var onFlipY = function onFlipY() {\n    updateTransform({\n      flipY: !transform.flipY\n    }, 'flipY');\n  };\n  var onSwitchLeft = function onSwitchLeft(event) {\n    event === null || event === void 0 ? void 0 : event.preventDefault();\n    event === null || event === void 0 ? void 0 : event.stopPropagation();\n    if (current > 0) {\n      setEnableTransition(false);\n      resetTransform('prev');\n      onChange === null || onChange === void 0 ? void 0 : onChange(current - 1, current);\n    }\n  };\n  var onSwitchRight = function onSwitchRight(event) {\n    event === null || event === void 0 ? void 0 : event.preventDefault();\n    event === null || event === void 0 ? void 0 : event.stopPropagation();\n    if (current < count - 1) {\n      setEnableTransition(false);\n      resetTransform('next');\n      onChange === null || onChange === void 0 ? void 0 : onChange(current + 1, current);\n    }\n  };\n  var onMouseUp = function onMouseUp() {\n    if (visible && isMoving) {\n      setMoving(false);\n      /** No need to restore the position when the picture is not moved, So as not to interfere with the click */\n      var _downPositionRef$curr = downPositionRef.current,\n        transformX = _downPositionRef$curr.transformX,\n        transformY = _downPositionRef$curr.transformY;\n      var hasChangedPosition = x !== transformX && y !== transformY;\n      if (!hasChangedPosition) {\n        return;\n      }\n      var width = imgRef.current.offsetWidth * scale;\n      var height = imgRef.current.offsetHeight * scale;\n      // eslint-disable-next-line @typescript-eslint/no-shadow\n      var _imgRef$current$getBo = imgRef.current.getBoundingClientRect(),\n        left = _imgRef$current$getBo.left,\n        top = _imgRef$current$getBo.top;\n      var isRotate = rotate % 180 !== 0;\n      var fixState = getFixScaleEleTransPosition(isRotate ? height : width, isRotate ? width : height, left, top);\n      if (fixState) {\n        updateTransform(_objectSpread({}, fixState), 'dragRebound');\n      }\n    }\n  };\n  var onMouseDown = function onMouseDown(event) {\n    // Only allow main button\n    if (!movable || event.button !== 0) return;\n    event.preventDefault();\n    event.stopPropagation();\n    downPositionRef.current = {\n      deltaX: event.pageX - transform.x,\n      deltaY: event.pageY - transform.y,\n      transformX: transform.x,\n      transformY: transform.y\n    };\n    setMoving(true);\n  };\n  var onMouseMove = function onMouseMove(event) {\n    if (visible && isMoving) {\n      updateTransform({\n        x: event.pageX - downPositionRef.current.deltaX,\n        y: event.pageY - downPositionRef.current.deltaY\n      }, 'move');\n    }\n  };\n  var onWheel = function onWheel(event) {\n    if (!visible || event.deltaY == 0) return;\n    // Scale ratio depends on the deltaY size\n    var scaleRatio = Math.abs(event.deltaY / 100);\n    // Limit the maximum scale ratio\n    var mergedScaleRatio = Math.min(scaleRatio, WHEEL_MAX_SCALE_RATIO);\n    // Scale the ratio each time\n    var ratio = BASE_SCALE_RATIO + mergedScaleRatio * scaleStep;\n    if (event.deltaY > 0) {\n      ratio = BASE_SCALE_RATIO / ratio;\n    }\n    dispatchZoomChange(ratio, 'wheel', event.clientX, event.clientY);\n  };\n  var onKeyDown = function onKeyDown(event) {\n    if (!visible || !showLeftOrRightSwitches) return;\n    if (event.keyCode === KeyCode.LEFT) {\n      onSwitchLeft();\n    } else if (event.keyCode === KeyCode.RIGHT) {\n      onSwitchRight();\n    }\n  };\n  var onDoubleClick = function onDoubleClick(event) {\n    if (visible) {\n      if (scale !== 1) {\n        updateTransform({\n          x: 0,\n          y: 0,\n          scale: 1\n        }, 'doubleClick');\n      } else {\n        dispatchZoomChange(BASE_SCALE_RATIO + scaleStep, 'doubleClick', event.clientX, event.clientY);\n      }\n    }\n  };\n  useEffect(function () {\n    var onTopMouseUpListener;\n    var onTopMouseMoveListener;\n    var onMouseUpListener;\n    var onMouseMoveListener;\n    if (movable) {\n      onMouseUpListener = addEventListener(window, 'mouseup', onMouseUp, false);\n      onMouseMoveListener = addEventListener(window, 'mousemove', onMouseMove, false);\n      try {\n        // Resolve if in iframe lost event\n        /* istanbul ignore next */\n        if (window.top !== window.self) {\n          onTopMouseUpListener = addEventListener(window.top, 'mouseup', onMouseUp, false);\n          onTopMouseMoveListener = addEventListener(window.top, 'mousemove', onMouseMove, false);\n        }\n      } catch (error) {\n        /* istanbul ignore next */\n        warning(false, \"[rc-image] \".concat(error));\n      }\n    }\n    return function () {\n      var _onMouseUpListener, _onMouseMoveListener, _onTopMouseUpListener, _onTopMouseMoveListen;\n      (_onMouseUpListener = onMouseUpListener) === null || _onMouseUpListener === void 0 ? void 0 : _onMouseUpListener.remove();\n      (_onMouseMoveListener = onMouseMoveListener) === null || _onMouseMoveListener === void 0 ? void 0 : _onMouseMoveListener.remove();\n      /* istanbul ignore next */\n      (_onTopMouseUpListener = onTopMouseUpListener) === null || _onTopMouseUpListener === void 0 ? void 0 : _onTopMouseUpListener.remove();\n      /* istanbul ignore next */\n      (_onTopMouseMoveListen = onTopMouseMoveListener) === null || _onTopMouseMoveListen === void 0 ? void 0 : _onTopMouseMoveListen.remove();\n    };\n  }, [visible, isMoving, x, y, rotate, movable]);\n  useEffect(function () {\n    var onKeyDownListener = addEventListener(window, 'keydown', onKeyDown, false);\n    return function () {\n      onKeyDownListener.remove();\n    };\n  }, [visible, showLeftOrRightSwitches, current]);\n  var imgNode = /*#__PURE__*/React.createElement(PreviewImage, _extends({}, imgCommonProps, {\n    width: props.width,\n    height: props.height,\n    imgRef: imgRef,\n    className: \"\".concat(prefixCls, \"-img\"),\n    alt: alt,\n    style: {\n      transform: \"translate3d(\".concat(transform.x, \"px, \").concat(transform.y, \"px, 0) scale3d(\").concat(transform.flipX ? '-' : '').concat(scale, \", \").concat(transform.flipY ? '-' : '').concat(scale, \", 1) rotate(\").concat(rotate, \"deg)\"),\n      transitionDuration: !enableTransition && '0s'\n    },\n    fallback: fallback,\n    src: src,\n    onWheel: onWheel,\n    onMouseDown: onMouseDown,\n    onDoubleClick: onDoubleClick\n  }));\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Dialog, _extends({\n    transitionName: transitionName,\n    maskTransitionName: maskTransitionName,\n    closable: false,\n    keyboard: true,\n    prefixCls: prefixCls,\n    onClose: onClose,\n    visible: visible,\n    wrapClassName: wrapClassName,\n    rootClassName: rootClassName,\n    getContainer: getContainer\n  }, restProps, {\n    afterClose: onAfterClose\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-img-wrapper\")\n  }, imageRender ? imageRender(imgNode, _objectSpread({\n    transform: transform\n  }, groupContext ? {\n    current: current\n  } : {})) : imgNode)), /*#__PURE__*/React.createElement(Operations, {\n    visible: visible,\n    transform: transform,\n    maskTransitionName: maskTransitionName,\n    closeIcon: closeIcon,\n    getContainer: getContainer,\n    prefixCls: prefixCls,\n    rootClassName: rootClassName,\n    icons: icons,\n    countRender: countRender,\n    showSwitch: showLeftOrRightSwitches,\n    showProgress: showOperationsProgress,\n    current: current,\n    count: count,\n    scale: scale,\n    minScale: minScale,\n    maxScale: maxScale,\n    toolbarRender: toolbarRender,\n    onSwitchLeft: onSwitchLeft,\n    onSwitchRight: onSwitchRight,\n    onZoomIn: onZoomIn,\n    onZoomOut: onZoomOut,\n    onRotateRight: onRotateRight,\n    onRotateLeft: onRotateLeft,\n    onFlipX: onFlipX,\n    onFlipY: onFlipY,\n    onClose: onClose\n  }));\n};\nexport default Preview;", "map": {"version": 3, "names": ["_objectSpread", "_defineProperty", "_extends", "_slicedToArray", "_objectWithoutProperties", "_excluded", "_excluded2", "classnames", "Dialog", "addEventListener", "KeyCode", "warning", "React", "useContext", "useEffect", "useRef", "useState", "PreviewGroupContext", "getFixScaleEleTransPosition", "useImageTransform", "useStatus", "Operations", "BASE_SCALE_RATIO", "WHEEL_MAX_SCALE_RATIO", "PreviewImage", "_ref", "fallback", "src", "imgRef", "props", "_useStatus", "_useStatus2", "getImgRef", "srcAndOnload", "createElement", "ref", "_ref2", "current", "Preview", "prefixCls", "alt", "_props$movable", "movable", "onClose", "visible", "_props$icons", "icons", "rootClassName", "closeIcon", "getContainer", "_props$current", "_props$count", "count", "countRender", "_props$scaleStep", "scaleStep", "_props$minScale", "minScale", "_props$maxScale", "maxScale", "_props$transitionName", "transitionName", "_props$maskTransition", "maskTransitionName", "imageRender", "imgCommonProps", "toolbarRender", "onTransform", "onChange", "restProps", "downPositionRef", "deltaX", "deltaY", "transformX", "transformY", "_useState", "_useState2", "isMoving", "setMoving", "groupContext", "showLeftOrRightSwitches", "showOperationsProgress", "_useImageTransform", "transform", "resetTransform", "updateTransform", "dispatchZoomChange", "_useState3", "_useState4", "enableTransition", "setEnableTransition", "rotate", "scale", "x", "y", "wrapClassName", "concat", "onAfterClose", "onZoomIn", "onZoomOut", "onRotateRight", "onRotateLeft", "onFlipX", "flipX", "onFlipY", "flipY", "onSwitchLeft", "event", "preventDefault", "stopPropagation", "onSwitchRight", "onMouseUp", "_downPositionRef$curr", "hasChangedPosition", "width", "offsetWidth", "height", "offsetHeight", "_imgRef$current$getBo", "getBoundingClientRect", "left", "top", "isRotate", "fixState", "onMouseDown", "button", "pageX", "pageY", "onMouseMove", "onWheel", "scaleRatio", "Math", "abs", "mergedScaleRatio", "min", "ratio", "clientX", "clientY", "onKeyDown", "keyCode", "LEFT", "RIGHT", "onDoubleClick", "onTopMouseUpListener", "onTopMouseMoveListener", "onMouseUpListener", "onMouseMoveListener", "window", "self", "error", "_onMouseUpListener", "_onMouseMoveListener", "_onTopMouseUpListener", "_onTopMouseMoveListen", "remove", "onKeyDownListener", "imgNode", "className", "style", "transitionDuration", "Fragment", "closable", "keyboard", "afterClose", "showSwitch", "showProgress"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-image/es/Preview.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"fallback\", \"src\", \"imgRef\"],\n  _excluded2 = [\"prefixCls\", \"src\", \"alt\", \"fallback\", \"movable\", \"onClose\", \"visible\", \"icons\", \"rootClassName\", \"closeIcon\", \"getContainer\", \"current\", \"count\", \"countRender\", \"scaleStep\", \"minScale\", \"maxScale\", \"transitionName\", \"maskTransitionName\", \"imageRender\", \"imgCommonProps\", \"toolbarRender\", \"onTransform\", \"onChange\"];\nimport classnames from 'classnames';\nimport Dialog from 'rc-dialog';\nimport addEventListener from \"rc-util/es/Dom/addEventListener\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport { warning } from \"rc-util/es/warning\";\nimport React, { useContext, useEffect, useRef, useState } from 'react';\nimport { PreviewGroupContext } from \"./context\";\nimport getFixScaleEleTransPosition from \"./getFixScaleEleTransPosition\";\nimport useImageTransform from \"./hooks/useImageTransform\";\nimport useStatus from \"./hooks/useStatus\";\nimport Operations from \"./Operations\";\nimport { BASE_SCALE_RATIO, WHEEL_MAX_SCALE_RATIO } from \"./previewConfig\";\nvar PreviewImage = function PreviewImage(_ref) {\n  var fallback = _ref.fallback,\n    src = _ref.src,\n    imgRef = _ref.imgRef,\n    props = _objectWithoutProperties(_ref, _excluded);\n  var _useStatus = useStatus({\n      src: src,\n      fallback: fallback\n    }),\n    _useStatus2 = _slicedToArray(_useStatus, 2),\n    getImgRef = _useStatus2[0],\n    srcAndOnload = _useStatus2[1];\n  return /*#__PURE__*/React.createElement(\"img\", _extends({\n    ref: function ref(_ref2) {\n      imgRef.current = _ref2;\n      getImgRef(_ref2);\n    }\n  }, props, srcAndOnload));\n};\nvar Preview = function Preview(props) {\n  var prefixCls = props.prefixCls,\n    src = props.src,\n    alt = props.alt,\n    fallback = props.fallback,\n    _props$movable = props.movable,\n    movable = _props$movable === void 0 ? true : _props$movable,\n    onClose = props.onClose,\n    visible = props.visible,\n    _props$icons = props.icons,\n    icons = _props$icons === void 0 ? {} : _props$icons,\n    rootClassName = props.rootClassName,\n    closeIcon = props.closeIcon,\n    getContainer = props.getContainer,\n    _props$current = props.current,\n    current = _props$current === void 0 ? 0 : _props$current,\n    _props$count = props.count,\n    count = _props$count === void 0 ? 1 : _props$count,\n    countRender = props.countRender,\n    _props$scaleStep = props.scaleStep,\n    scaleStep = _props$scaleStep === void 0 ? 0.5 : _props$scaleStep,\n    _props$minScale = props.minScale,\n    minScale = _props$minScale === void 0 ? 1 : _props$minScale,\n    _props$maxScale = props.maxScale,\n    maxScale = _props$maxScale === void 0 ? 50 : _props$maxScale,\n    _props$transitionName = props.transitionName,\n    transitionName = _props$transitionName === void 0 ? 'zoom' : _props$transitionName,\n    _props$maskTransition = props.maskTransitionName,\n    maskTransitionName = _props$maskTransition === void 0 ? 'fade' : _props$maskTransition,\n    imageRender = props.imageRender,\n    imgCommonProps = props.imgCommonProps,\n    toolbarRender = props.toolbarRender,\n    onTransform = props.onTransform,\n    onChange = props.onChange,\n    restProps = _objectWithoutProperties(props, _excluded2);\n  var imgRef = useRef();\n  var downPositionRef = useRef({\n    deltaX: 0,\n    deltaY: 0,\n    transformX: 0,\n    transformY: 0\n  });\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    isMoving = _useState2[0],\n    setMoving = _useState2[1];\n  var groupContext = useContext(PreviewGroupContext);\n  var showLeftOrRightSwitches = groupContext && count > 1;\n  var showOperationsProgress = groupContext && count >= 1;\n  var _useImageTransform = useImageTransform(imgRef, minScale, maxScale, onTransform),\n    transform = _useImageTransform.transform,\n    resetTransform = _useImageTransform.resetTransform,\n    updateTransform = _useImageTransform.updateTransform,\n    dispatchZoomChange = _useImageTransform.dispatchZoomChange;\n  var _useState3 = useState(true),\n    _useState4 = _slicedToArray(_useState3, 2),\n    enableTransition = _useState4[0],\n    setEnableTransition = _useState4[1];\n  var rotate = transform.rotate,\n    scale = transform.scale,\n    x = transform.x,\n    y = transform.y;\n  var wrapClassName = classnames(_defineProperty({}, \"\".concat(prefixCls, \"-moving\"), isMoving));\n  useEffect(function () {\n    if (!enableTransition) {\n      setEnableTransition(true);\n    }\n  }, [enableTransition]);\n  var onAfterClose = function onAfterClose() {\n    resetTransform('close');\n  };\n  var onZoomIn = function onZoomIn() {\n    dispatchZoomChange(BASE_SCALE_RATIO + scaleStep, 'zoomIn');\n  };\n  var onZoomOut = function onZoomOut() {\n    dispatchZoomChange(BASE_SCALE_RATIO / (BASE_SCALE_RATIO + scaleStep), 'zoomOut');\n  };\n  var onRotateRight = function onRotateRight() {\n    updateTransform({\n      rotate: rotate + 90\n    }, 'rotateRight');\n  };\n  var onRotateLeft = function onRotateLeft() {\n    updateTransform({\n      rotate: rotate - 90\n    }, 'rotateLeft');\n  };\n  var onFlipX = function onFlipX() {\n    updateTransform({\n      flipX: !transform.flipX\n    }, 'flipX');\n  };\n  var onFlipY = function onFlipY() {\n    updateTransform({\n      flipY: !transform.flipY\n    }, 'flipY');\n  };\n  var onSwitchLeft = function onSwitchLeft(event) {\n    event === null || event === void 0 ? void 0 : event.preventDefault();\n    event === null || event === void 0 ? void 0 : event.stopPropagation();\n    if (current > 0) {\n      setEnableTransition(false);\n      resetTransform('prev');\n      onChange === null || onChange === void 0 ? void 0 : onChange(current - 1, current);\n    }\n  };\n  var onSwitchRight = function onSwitchRight(event) {\n    event === null || event === void 0 ? void 0 : event.preventDefault();\n    event === null || event === void 0 ? void 0 : event.stopPropagation();\n    if (current < count - 1) {\n      setEnableTransition(false);\n      resetTransform('next');\n      onChange === null || onChange === void 0 ? void 0 : onChange(current + 1, current);\n    }\n  };\n  var onMouseUp = function onMouseUp() {\n    if (visible && isMoving) {\n      setMoving(false);\n      /** No need to restore the position when the picture is not moved, So as not to interfere with the click */\n      var _downPositionRef$curr = downPositionRef.current,\n        transformX = _downPositionRef$curr.transformX,\n        transformY = _downPositionRef$curr.transformY;\n      var hasChangedPosition = x !== transformX && y !== transformY;\n      if (!hasChangedPosition) {\n        return;\n      }\n      var width = imgRef.current.offsetWidth * scale;\n      var height = imgRef.current.offsetHeight * scale;\n      // eslint-disable-next-line @typescript-eslint/no-shadow\n      var _imgRef$current$getBo = imgRef.current.getBoundingClientRect(),\n        left = _imgRef$current$getBo.left,\n        top = _imgRef$current$getBo.top;\n      var isRotate = rotate % 180 !== 0;\n      var fixState = getFixScaleEleTransPosition(isRotate ? height : width, isRotate ? width : height, left, top);\n      if (fixState) {\n        updateTransform(_objectSpread({}, fixState), 'dragRebound');\n      }\n    }\n  };\n  var onMouseDown = function onMouseDown(event) {\n    // Only allow main button\n    if (!movable || event.button !== 0) return;\n    event.preventDefault();\n    event.stopPropagation();\n    downPositionRef.current = {\n      deltaX: event.pageX - transform.x,\n      deltaY: event.pageY - transform.y,\n      transformX: transform.x,\n      transformY: transform.y\n    };\n    setMoving(true);\n  };\n  var onMouseMove = function onMouseMove(event) {\n    if (visible && isMoving) {\n      updateTransform({\n        x: event.pageX - downPositionRef.current.deltaX,\n        y: event.pageY - downPositionRef.current.deltaY\n      }, 'move');\n    }\n  };\n  var onWheel = function onWheel(event) {\n    if (!visible || event.deltaY == 0) return;\n    // Scale ratio depends on the deltaY size\n    var scaleRatio = Math.abs(event.deltaY / 100);\n    // Limit the maximum scale ratio\n    var mergedScaleRatio = Math.min(scaleRatio, WHEEL_MAX_SCALE_RATIO);\n    // Scale the ratio each time\n    var ratio = BASE_SCALE_RATIO + mergedScaleRatio * scaleStep;\n    if (event.deltaY > 0) {\n      ratio = BASE_SCALE_RATIO / ratio;\n    }\n    dispatchZoomChange(ratio, 'wheel', event.clientX, event.clientY);\n  };\n  var onKeyDown = function onKeyDown(event) {\n    if (!visible || !showLeftOrRightSwitches) return;\n    if (event.keyCode === KeyCode.LEFT) {\n      onSwitchLeft();\n    } else if (event.keyCode === KeyCode.RIGHT) {\n      onSwitchRight();\n    }\n  };\n  var onDoubleClick = function onDoubleClick(event) {\n    if (visible) {\n      if (scale !== 1) {\n        updateTransform({\n          x: 0,\n          y: 0,\n          scale: 1\n        }, 'doubleClick');\n      } else {\n        dispatchZoomChange(BASE_SCALE_RATIO + scaleStep, 'doubleClick', event.clientX, event.clientY);\n      }\n    }\n  };\n  useEffect(function () {\n    var onTopMouseUpListener;\n    var onTopMouseMoveListener;\n    var onMouseUpListener;\n    var onMouseMoveListener;\n    if (movable) {\n      onMouseUpListener = addEventListener(window, 'mouseup', onMouseUp, false);\n      onMouseMoveListener = addEventListener(window, 'mousemove', onMouseMove, false);\n      try {\n        // Resolve if in iframe lost event\n        /* istanbul ignore next */\n        if (window.top !== window.self) {\n          onTopMouseUpListener = addEventListener(window.top, 'mouseup', onMouseUp, false);\n          onTopMouseMoveListener = addEventListener(window.top, 'mousemove', onMouseMove, false);\n        }\n      } catch (error) {\n        /* istanbul ignore next */\n        warning(false, \"[rc-image] \".concat(error));\n      }\n    }\n    return function () {\n      var _onMouseUpListener, _onMouseMoveListener, _onTopMouseUpListener, _onTopMouseMoveListen;\n      (_onMouseUpListener = onMouseUpListener) === null || _onMouseUpListener === void 0 ? void 0 : _onMouseUpListener.remove();\n      (_onMouseMoveListener = onMouseMoveListener) === null || _onMouseMoveListener === void 0 ? void 0 : _onMouseMoveListener.remove();\n      /* istanbul ignore next */\n      (_onTopMouseUpListener = onTopMouseUpListener) === null || _onTopMouseUpListener === void 0 ? void 0 : _onTopMouseUpListener.remove();\n      /* istanbul ignore next */\n      (_onTopMouseMoveListen = onTopMouseMoveListener) === null || _onTopMouseMoveListen === void 0 ? void 0 : _onTopMouseMoveListen.remove();\n    };\n  }, [visible, isMoving, x, y, rotate, movable]);\n  useEffect(function () {\n    var onKeyDownListener = addEventListener(window, 'keydown', onKeyDown, false);\n    return function () {\n      onKeyDownListener.remove();\n    };\n  }, [visible, showLeftOrRightSwitches, current]);\n  var imgNode = /*#__PURE__*/React.createElement(PreviewImage, _extends({}, imgCommonProps, {\n    width: props.width,\n    height: props.height,\n    imgRef: imgRef,\n    className: \"\".concat(prefixCls, \"-img\"),\n    alt: alt,\n    style: {\n      transform: \"translate3d(\".concat(transform.x, \"px, \").concat(transform.y, \"px, 0) scale3d(\").concat(transform.flipX ? '-' : '').concat(scale, \", \").concat(transform.flipY ? '-' : '').concat(scale, \", 1) rotate(\").concat(rotate, \"deg)\"),\n      transitionDuration: !enableTransition && '0s'\n    },\n    fallback: fallback,\n    src: src,\n    onWheel: onWheel,\n    onMouseDown: onMouseDown,\n    onDoubleClick: onDoubleClick\n  }));\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Dialog, _extends({\n    transitionName: transitionName,\n    maskTransitionName: maskTransitionName,\n    closable: false,\n    keyboard: true,\n    prefixCls: prefixCls,\n    onClose: onClose,\n    visible: visible,\n    wrapClassName: wrapClassName,\n    rootClassName: rootClassName,\n    getContainer: getContainer\n  }, restProps, {\n    afterClose: onAfterClose\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-img-wrapper\")\n  }, imageRender ? imageRender(imgNode, _objectSpread({\n    transform: transform\n  }, groupContext ? {\n    current: current\n  } : {})) : imgNode)), /*#__PURE__*/React.createElement(Operations, {\n    visible: visible,\n    transform: transform,\n    maskTransitionName: maskTransitionName,\n    closeIcon: closeIcon,\n    getContainer: getContainer,\n    prefixCls: prefixCls,\n    rootClassName: rootClassName,\n    icons: icons,\n    countRender: countRender,\n    showSwitch: showLeftOrRightSwitches,\n    showProgress: showOperationsProgress,\n    current: current,\n    count: count,\n    scale: scale,\n    minScale: minScale,\n    maxScale: maxScale,\n    toolbarRender: toolbarRender,\n    onSwitchLeft: onSwitchLeft,\n    onSwitchRight: onSwitchRight,\n    onZoomIn: onZoomIn,\n    onZoomOut: onZoomOut,\n    onRotateRight: onRotateRight,\n    onRotateLeft: onRotateLeft,\n    onFlipX: onFlipX,\n    onFlipY: onFlipY,\n    onClose: onClose\n  }));\n};\nexport default Preview;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE,QAAQ,CAAC;EAC3CC,UAAU,GAAG,CAAC,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,eAAe,EAAE,WAAW,EAAE,cAAc,EAAE,SAAS,EAAE,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,aAAa,EAAE,gBAAgB,EAAE,eAAe,EAAE,aAAa,EAAE,UAAU,CAAC;AAC3U,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,OAAO,MAAM,oBAAoB;AACxC,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,OAAOC,KAAK,IAAIC,UAAU,EAAEC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACtE,SAASC,mBAAmB,QAAQ,WAAW;AAC/C,OAAOC,2BAA2B,MAAM,+BAA+B;AACvE,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,UAAU,MAAM,cAAc;AACrC,SAASC,gBAAgB,EAAEC,qBAAqB,QAAQ,iBAAiB;AACzE,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,IAAI,EAAE;EAC7C,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IAC1BC,GAAG,GAAGF,IAAI,CAACE,GAAG;IACdC,MAAM,GAAGH,IAAI,CAACG,MAAM;IACpBC,KAAK,GAAGzB,wBAAwB,CAACqB,IAAI,EAAEpB,SAAS,CAAC;EACnD,IAAIyB,UAAU,GAAGV,SAAS,CAAC;MACvBO,GAAG,EAAEA,GAAG;MACRD,QAAQ,EAAEA;IACZ,CAAC,CAAC;IACFK,WAAW,GAAG5B,cAAc,CAAC2B,UAAU,EAAE,CAAC,CAAC;IAC3CE,SAAS,GAAGD,WAAW,CAAC,CAAC,CAAC;IAC1BE,YAAY,GAAGF,WAAW,CAAC,CAAC,CAAC;EAC/B,OAAO,aAAanB,KAAK,CAACsB,aAAa,CAAC,KAAK,EAAEhC,QAAQ,CAAC;IACtDiC,GAAG,EAAE,SAASA,GAAGA,CAACC,KAAK,EAAE;MACvBR,MAAM,CAACS,OAAO,GAAGD,KAAK;MACtBJ,SAAS,CAACI,KAAK,CAAC;IAClB;EACF,CAAC,EAAEP,KAAK,EAAEI,YAAY,CAAC,CAAC;AAC1B,CAAC;AACD,IAAIK,OAAO,GAAG,SAASA,OAAOA,CAACT,KAAK,EAAE;EACpC,IAAIU,SAAS,GAAGV,KAAK,CAACU,SAAS;IAC7BZ,GAAG,GAAGE,KAAK,CAACF,GAAG;IACfa,GAAG,GAAGX,KAAK,CAACW,GAAG;IACfd,QAAQ,GAAGG,KAAK,CAACH,QAAQ;IACzBe,cAAc,GAAGZ,KAAK,CAACa,OAAO;IAC9BA,OAAO,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,cAAc;IAC3DE,OAAO,GAAGd,KAAK,CAACc,OAAO;IACvBC,OAAO,GAAGf,KAAK,CAACe,OAAO;IACvBC,YAAY,GAAGhB,KAAK,CAACiB,KAAK;IAC1BA,KAAK,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,YAAY;IACnDE,aAAa,GAAGlB,KAAK,CAACkB,aAAa;IACnCC,SAAS,GAAGnB,KAAK,CAACmB,SAAS;IAC3BC,YAAY,GAAGpB,KAAK,CAACoB,YAAY;IACjCC,cAAc,GAAGrB,KAAK,CAACQ,OAAO;IAC9BA,OAAO,GAAGa,cAAc,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,cAAc;IACxDC,YAAY,GAAGtB,KAAK,CAACuB,KAAK;IAC1BA,KAAK,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,YAAY;IAClDE,WAAW,GAAGxB,KAAK,CAACwB,WAAW;IAC/BC,gBAAgB,GAAGzB,KAAK,CAAC0B,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,gBAAgB;IAChEE,eAAe,GAAG3B,KAAK,CAAC4B,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,eAAe;IAC3DE,eAAe,GAAG7B,KAAK,CAAC8B,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,eAAe;IAC5DE,qBAAqB,GAAG/B,KAAK,CAACgC,cAAc;IAC5CA,cAAc,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,MAAM,GAAGA,qBAAqB;IAClFE,qBAAqB,GAAGjC,KAAK,CAACkC,kBAAkB;IAChDA,kBAAkB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,MAAM,GAAGA,qBAAqB;IACtFE,WAAW,GAAGnC,KAAK,CAACmC,WAAW;IAC/BC,cAAc,GAAGpC,KAAK,CAACoC,cAAc;IACrCC,aAAa,GAAGrC,KAAK,CAACqC,aAAa;IACnCC,WAAW,GAAGtC,KAAK,CAACsC,WAAW;IAC/BC,QAAQ,GAAGvC,KAAK,CAACuC,QAAQ;IACzBC,SAAS,GAAGjE,wBAAwB,CAACyB,KAAK,EAAEvB,UAAU,CAAC;EACzD,IAAIsB,MAAM,GAAGb,MAAM,CAAC,CAAC;EACrB,IAAIuD,eAAe,GAAGvD,MAAM,CAAC;IAC3BwD,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE,CAAC;IACTC,UAAU,EAAE,CAAC;IACbC,UAAU,EAAE;EACd,CAAC,CAAC;EACF,IAAIC,SAAS,GAAG3D,QAAQ,CAAC,KAAK,CAAC;IAC7B4D,UAAU,GAAGzE,cAAc,CAACwE,SAAS,EAAE,CAAC,CAAC;IACzCE,QAAQ,GAAGD,UAAU,CAAC,CAAC,CAAC;IACxBE,SAAS,GAAGF,UAAU,CAAC,CAAC,CAAC;EAC3B,IAAIG,YAAY,GAAGlE,UAAU,CAACI,mBAAmB,CAAC;EAClD,IAAI+D,uBAAuB,GAAGD,YAAY,IAAI3B,KAAK,GAAG,CAAC;EACvD,IAAI6B,sBAAsB,GAAGF,YAAY,IAAI3B,KAAK,IAAI,CAAC;EACvD,IAAI8B,kBAAkB,GAAG/D,iBAAiB,CAACS,MAAM,EAAE6B,QAAQ,EAAEE,QAAQ,EAAEQ,WAAW,CAAC;IACjFgB,SAAS,GAAGD,kBAAkB,CAACC,SAAS;IACxCC,cAAc,GAAGF,kBAAkB,CAACE,cAAc;IAClDC,eAAe,GAAGH,kBAAkB,CAACG,eAAe;IACpDC,kBAAkB,GAAGJ,kBAAkB,CAACI,kBAAkB;EAC5D,IAAIC,UAAU,GAAGvE,QAAQ,CAAC,IAAI,CAAC;IAC7BwE,UAAU,GAAGrF,cAAc,CAACoF,UAAU,EAAE,CAAC,CAAC;IAC1CE,gBAAgB,GAAGD,UAAU,CAAC,CAAC,CAAC;IAChCE,mBAAmB,GAAGF,UAAU,CAAC,CAAC,CAAC;EACrC,IAAIG,MAAM,GAAGR,SAAS,CAACQ,MAAM;IAC3BC,KAAK,GAAGT,SAAS,CAACS,KAAK;IACvBC,CAAC,GAAGV,SAAS,CAACU,CAAC;IACfC,CAAC,GAAGX,SAAS,CAACW,CAAC;EACjB,IAAIC,aAAa,GAAGxF,UAAU,CAACN,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC+F,MAAM,CAACzD,SAAS,EAAE,SAAS,CAAC,EAAEsC,QAAQ,CAAC,CAAC;EAC9F/D,SAAS,CAAC,YAAY;IACpB,IAAI,CAAC2E,gBAAgB,EAAE;MACrBC,mBAAmB,CAAC,IAAI,CAAC;IAC3B;EACF,CAAC,EAAE,CAACD,gBAAgB,CAAC,CAAC;EACtB,IAAIQ,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IACzCb,cAAc,CAAC,OAAO,CAAC;EACzB,CAAC;EACD,IAAIc,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;IACjCZ,kBAAkB,CAAChE,gBAAgB,GAAGiC,SAAS,EAAE,QAAQ,CAAC;EAC5D,CAAC;EACD,IAAI4C,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;IACnCb,kBAAkB,CAAChE,gBAAgB,IAAIA,gBAAgB,GAAGiC,SAAS,CAAC,EAAE,SAAS,CAAC;EAClF,CAAC;EACD,IAAI6C,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IAC3Cf,eAAe,CAAC;MACdM,MAAM,EAAEA,MAAM,GAAG;IACnB,CAAC,EAAE,aAAa,CAAC;EACnB,CAAC;EACD,IAAIU,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;IACzChB,eAAe,CAAC;MACdM,MAAM,EAAEA,MAAM,GAAG;IACnB,CAAC,EAAE,YAAY,CAAC;EAClB,CAAC;EACD,IAAIW,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;IAC/BjB,eAAe,CAAC;MACdkB,KAAK,EAAE,CAACpB,SAAS,CAACoB;IACpB,CAAC,EAAE,OAAO,CAAC;EACb,CAAC;EACD,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;IAC/BnB,eAAe,CAAC;MACdoB,KAAK,EAAE,CAACtB,SAAS,CAACsB;IACpB,CAAC,EAAE,OAAO,CAAC;EACb,CAAC;EACD,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,KAAK,EAAE;IAC9CA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACC,cAAc,CAAC,CAAC;IACpED,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACE,eAAe,CAAC,CAAC;IACrE,IAAIxE,OAAO,GAAG,CAAC,EAAE;MACfqD,mBAAmB,CAAC,KAAK,CAAC;MAC1BN,cAAc,CAAC,MAAM,CAAC;MACtBhB,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC/B,OAAO,GAAG,CAAC,EAAEA,OAAO,CAAC;IACpF;EACF,CAAC;EACD,IAAIyE,aAAa,GAAG,SAASA,aAAaA,CAACH,KAAK,EAAE;IAChDA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACC,cAAc,CAAC,CAAC;IACpED,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACE,eAAe,CAAC,CAAC;IACrE,IAAIxE,OAAO,GAAGe,KAAK,GAAG,CAAC,EAAE;MACvBsC,mBAAmB,CAAC,KAAK,CAAC;MAC1BN,cAAc,CAAC,MAAM,CAAC;MACtBhB,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC/B,OAAO,GAAG,CAAC,EAAEA,OAAO,CAAC;IACpF;EACF,CAAC;EACD,IAAI0E,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;IACnC,IAAInE,OAAO,IAAIiC,QAAQ,EAAE;MACvBC,SAAS,CAAC,KAAK,CAAC;MAChB;MACA,IAAIkC,qBAAqB,GAAG1C,eAAe,CAACjC,OAAO;QACjDoC,UAAU,GAAGuC,qBAAqB,CAACvC,UAAU;QAC7CC,UAAU,GAAGsC,qBAAqB,CAACtC,UAAU;MAC/C,IAAIuC,kBAAkB,GAAGpB,CAAC,KAAKpB,UAAU,IAAIqB,CAAC,KAAKpB,UAAU;MAC7D,IAAI,CAACuC,kBAAkB,EAAE;QACvB;MACF;MACA,IAAIC,KAAK,GAAGtF,MAAM,CAACS,OAAO,CAAC8E,WAAW,GAAGvB,KAAK;MAC9C,IAAIwB,MAAM,GAAGxF,MAAM,CAACS,OAAO,CAACgF,YAAY,GAAGzB,KAAK;MAChD;MACA,IAAI0B,qBAAqB,GAAG1F,MAAM,CAACS,OAAO,CAACkF,qBAAqB,CAAC,CAAC;QAChEC,IAAI,GAAGF,qBAAqB,CAACE,IAAI;QACjCC,GAAG,GAAGH,qBAAqB,CAACG,GAAG;MACjC,IAAIC,QAAQ,GAAG/B,MAAM,GAAG,GAAG,KAAK,CAAC;MACjC,IAAIgC,QAAQ,GAAGzG,2BAA2B,CAACwG,QAAQ,GAAGN,MAAM,GAAGF,KAAK,EAAEQ,QAAQ,GAAGR,KAAK,GAAGE,MAAM,EAAEI,IAAI,EAAEC,GAAG,CAAC;MAC3G,IAAIE,QAAQ,EAAE;QACZtC,eAAe,CAACrF,aAAa,CAAC,CAAC,CAAC,EAAE2H,QAAQ,CAAC,EAAE,aAAa,CAAC;MAC7D;IACF;EACF,CAAC;EACD,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACjB,KAAK,EAAE;IAC5C;IACA,IAAI,CAACjE,OAAO,IAAIiE,KAAK,CAACkB,MAAM,KAAK,CAAC,EAAE;IACpClB,KAAK,CAACC,cAAc,CAAC,CAAC;IACtBD,KAAK,CAACE,eAAe,CAAC,CAAC;IACvBvC,eAAe,CAACjC,OAAO,GAAG;MACxBkC,MAAM,EAAEoC,KAAK,CAACmB,KAAK,GAAG3C,SAAS,CAACU,CAAC;MACjCrB,MAAM,EAAEmC,KAAK,CAACoB,KAAK,GAAG5C,SAAS,CAACW,CAAC;MACjCrB,UAAU,EAAEU,SAAS,CAACU,CAAC;MACvBnB,UAAU,EAAES,SAAS,CAACW;IACxB,CAAC;IACDhB,SAAS,CAAC,IAAI,CAAC;EACjB,CAAC;EACD,IAAIkD,WAAW,GAAG,SAASA,WAAWA,CAACrB,KAAK,EAAE;IAC5C,IAAI/D,OAAO,IAAIiC,QAAQ,EAAE;MACvBQ,eAAe,CAAC;QACdQ,CAAC,EAAEc,KAAK,CAACmB,KAAK,GAAGxD,eAAe,CAACjC,OAAO,CAACkC,MAAM;QAC/CuB,CAAC,EAAEa,KAAK,CAACoB,KAAK,GAAGzD,eAAe,CAACjC,OAAO,CAACmC;MAC3C,CAAC,EAAE,MAAM,CAAC;IACZ;EACF,CAAC;EACD,IAAIyD,OAAO,GAAG,SAASA,OAAOA,CAACtB,KAAK,EAAE;IACpC,IAAI,CAAC/D,OAAO,IAAI+D,KAAK,CAACnC,MAAM,IAAI,CAAC,EAAE;IACnC;IACA,IAAI0D,UAAU,GAAGC,IAAI,CAACC,GAAG,CAACzB,KAAK,CAACnC,MAAM,GAAG,GAAG,CAAC;IAC7C;IACA,IAAI6D,gBAAgB,GAAGF,IAAI,CAACG,GAAG,CAACJ,UAAU,EAAE3G,qBAAqB,CAAC;IAClE;IACA,IAAIgH,KAAK,GAAGjH,gBAAgB,GAAG+G,gBAAgB,GAAG9E,SAAS;IAC3D,IAAIoD,KAAK,CAACnC,MAAM,GAAG,CAAC,EAAE;MACpB+D,KAAK,GAAGjH,gBAAgB,GAAGiH,KAAK;IAClC;IACAjD,kBAAkB,CAACiD,KAAK,EAAE,OAAO,EAAE5B,KAAK,CAAC6B,OAAO,EAAE7B,KAAK,CAAC8B,OAAO,CAAC;EAClE,CAAC;EACD,IAAIC,SAAS,GAAG,SAASA,SAASA,CAAC/B,KAAK,EAAE;IACxC,IAAI,CAAC/D,OAAO,IAAI,CAACoC,uBAAuB,EAAE;IAC1C,IAAI2B,KAAK,CAACgC,OAAO,KAAKjI,OAAO,CAACkI,IAAI,EAAE;MAClClC,YAAY,CAAC,CAAC;IAChB,CAAC,MAAM,IAAIC,KAAK,CAACgC,OAAO,KAAKjI,OAAO,CAACmI,KAAK,EAAE;MAC1C/B,aAAa,CAAC,CAAC;IACjB;EACF,CAAC;EACD,IAAIgC,aAAa,GAAG,SAASA,aAAaA,CAACnC,KAAK,EAAE;IAChD,IAAI/D,OAAO,EAAE;MACX,IAAIgD,KAAK,KAAK,CAAC,EAAE;QACfP,eAAe,CAAC;UACdQ,CAAC,EAAE,CAAC;UACJC,CAAC,EAAE,CAAC;UACJF,KAAK,EAAE;QACT,CAAC,EAAE,aAAa,CAAC;MACnB,CAAC,MAAM;QACLN,kBAAkB,CAAChE,gBAAgB,GAAGiC,SAAS,EAAE,aAAa,EAAEoD,KAAK,CAAC6B,OAAO,EAAE7B,KAAK,CAAC8B,OAAO,CAAC;MAC/F;IACF;EACF,CAAC;EACD3H,SAAS,CAAC,YAAY;IACpB,IAAIiI,oBAAoB;IACxB,IAAIC,sBAAsB;IAC1B,IAAIC,iBAAiB;IACrB,IAAIC,mBAAmB;IACvB,IAAIxG,OAAO,EAAE;MACXuG,iBAAiB,GAAGxI,gBAAgB,CAAC0I,MAAM,EAAE,SAAS,EAAEpC,SAAS,EAAE,KAAK,CAAC;MACzEmC,mBAAmB,GAAGzI,gBAAgB,CAAC0I,MAAM,EAAE,WAAW,EAAEnB,WAAW,EAAE,KAAK,CAAC;MAC/E,IAAI;QACF;QACA;QACA,IAAImB,MAAM,CAAC1B,GAAG,KAAK0B,MAAM,CAACC,IAAI,EAAE;UAC9BL,oBAAoB,GAAGtI,gBAAgB,CAAC0I,MAAM,CAAC1B,GAAG,EAAE,SAAS,EAAEV,SAAS,EAAE,KAAK,CAAC;UAChFiC,sBAAsB,GAAGvI,gBAAgB,CAAC0I,MAAM,CAAC1B,GAAG,EAAE,WAAW,EAAEO,WAAW,EAAE,KAAK,CAAC;QACxF;MACF,CAAC,CAAC,OAAOqB,KAAK,EAAE;QACd;QACA1I,OAAO,CAAC,KAAK,EAAE,aAAa,CAACqF,MAAM,CAACqD,KAAK,CAAC,CAAC;MAC7C;IACF;IACA,OAAO,YAAY;MACjB,IAAIC,kBAAkB,EAAEC,oBAAoB,EAAEC,qBAAqB,EAAEC,qBAAqB;MAC1F,CAACH,kBAAkB,GAAGL,iBAAiB,MAAM,IAAI,IAAIK,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACI,MAAM,CAAC,CAAC;MACzH,CAACH,oBAAoB,GAAGL,mBAAmB,MAAM,IAAI,IAAIK,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,oBAAoB,CAACG,MAAM,CAAC,CAAC;MACjI;MACA,CAACF,qBAAqB,GAAGT,oBAAoB,MAAM,IAAI,IAAIS,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACE,MAAM,CAAC,CAAC;MACrI;MACA,CAACD,qBAAqB,GAAGT,sBAAsB,MAAM,IAAI,IAAIS,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACC,MAAM,CAAC,CAAC;IACzI,CAAC;EACH,CAAC,EAAE,CAAC9G,OAAO,EAAEiC,QAAQ,EAAEgB,CAAC,EAAEC,CAAC,EAAEH,MAAM,EAAEjD,OAAO,CAAC,CAAC;EAC9C5B,SAAS,CAAC,YAAY;IACpB,IAAI6I,iBAAiB,GAAGlJ,gBAAgB,CAAC0I,MAAM,EAAE,SAAS,EAAET,SAAS,EAAE,KAAK,CAAC;IAC7E,OAAO,YAAY;MACjBiB,iBAAiB,CAACD,MAAM,CAAC,CAAC;IAC5B,CAAC;EACH,CAAC,EAAE,CAAC9G,OAAO,EAAEoC,uBAAuB,EAAE3C,OAAO,CAAC,CAAC;EAC/C,IAAIuH,OAAO,GAAG,aAAahJ,KAAK,CAACsB,aAAa,CAACV,YAAY,EAAEtB,QAAQ,CAAC,CAAC,CAAC,EAAE+D,cAAc,EAAE;IACxFiD,KAAK,EAAErF,KAAK,CAACqF,KAAK;IAClBE,MAAM,EAAEvF,KAAK,CAACuF,MAAM;IACpBxF,MAAM,EAAEA,MAAM;IACdiI,SAAS,EAAE,EAAE,CAAC7D,MAAM,CAACzD,SAAS,EAAE,MAAM,CAAC;IACvCC,GAAG,EAAEA,GAAG;IACRsH,KAAK,EAAE;MACL3E,SAAS,EAAE,cAAc,CAACa,MAAM,CAACb,SAAS,CAACU,CAAC,EAAE,MAAM,CAAC,CAACG,MAAM,CAACb,SAAS,CAACW,CAAC,EAAE,iBAAiB,CAAC,CAACE,MAAM,CAACb,SAAS,CAACoB,KAAK,GAAG,GAAG,GAAG,EAAE,CAAC,CAACP,MAAM,CAACJ,KAAK,EAAE,IAAI,CAAC,CAACI,MAAM,CAACb,SAAS,CAACsB,KAAK,GAAG,GAAG,GAAG,EAAE,CAAC,CAACT,MAAM,CAACJ,KAAK,EAAE,cAAc,CAAC,CAACI,MAAM,CAACL,MAAM,EAAE,MAAM,CAAC;MAC3OoE,kBAAkB,EAAE,CAACtE,gBAAgB,IAAI;IAC3C,CAAC;IACD/D,QAAQ,EAAEA,QAAQ;IAClBC,GAAG,EAAEA,GAAG;IACRsG,OAAO,EAAEA,OAAO;IAChBL,WAAW,EAAEA,WAAW;IACxBkB,aAAa,EAAEA;EACjB,CAAC,CAAC,CAAC;EACH,OAAO,aAAalI,KAAK,CAACsB,aAAa,CAACtB,KAAK,CAACoJ,QAAQ,EAAE,IAAI,EAAE,aAAapJ,KAAK,CAACsB,aAAa,CAAC1B,MAAM,EAAEN,QAAQ,CAAC;IAC9G2D,cAAc,EAAEA,cAAc;IAC9BE,kBAAkB,EAAEA,kBAAkB;IACtCkG,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE,IAAI;IACd3H,SAAS,EAAEA,SAAS;IACpBI,OAAO,EAAEA,OAAO;IAChBC,OAAO,EAAEA,OAAO;IAChBmD,aAAa,EAAEA,aAAa;IAC5BhD,aAAa,EAAEA,aAAa;IAC5BE,YAAY,EAAEA;EAChB,CAAC,EAAEoB,SAAS,EAAE;IACZ8F,UAAU,EAAElE;EACd,CAAC,CAAC,EAAE,aAAarF,KAAK,CAACsB,aAAa,CAAC,KAAK,EAAE;IAC1C2H,SAAS,EAAE,EAAE,CAAC7D,MAAM,CAACzD,SAAS,EAAE,cAAc;EAChD,CAAC,EAAEyB,WAAW,GAAGA,WAAW,CAAC4F,OAAO,EAAE5J,aAAa,CAAC;IAClDmF,SAAS,EAAEA;EACb,CAAC,EAAEJ,YAAY,GAAG;IAChB1C,OAAO,EAAEA;EACX,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGuH,OAAO,CAAC,CAAC,EAAE,aAAahJ,KAAK,CAACsB,aAAa,CAACb,UAAU,EAAE;IACjEuB,OAAO,EAAEA,OAAO;IAChBuC,SAAS,EAAEA,SAAS;IACpBpB,kBAAkB,EAAEA,kBAAkB;IACtCf,SAAS,EAAEA,SAAS;IACpBC,YAAY,EAAEA,YAAY;IAC1BV,SAAS,EAAEA,SAAS;IACpBQ,aAAa,EAAEA,aAAa;IAC5BD,KAAK,EAAEA,KAAK;IACZO,WAAW,EAAEA,WAAW;IACxB+G,UAAU,EAAEpF,uBAAuB;IACnCqF,YAAY,EAAEpF,sBAAsB;IACpC5C,OAAO,EAAEA,OAAO;IAChBe,KAAK,EAAEA,KAAK;IACZwC,KAAK,EAAEA,KAAK;IACZnC,QAAQ,EAAEA,QAAQ;IAClBE,QAAQ,EAAEA,QAAQ;IAClBO,aAAa,EAAEA,aAAa;IAC5BwC,YAAY,EAAEA,YAAY;IAC1BI,aAAa,EAAEA,aAAa;IAC5BZ,QAAQ,EAAEA,QAAQ;IAClBC,SAAS,EAAEA,SAAS;IACpBC,aAAa,EAAEA,aAAa;IAC5BC,YAAY,EAAEA,YAAY;IAC1BC,OAAO,EAAEA,OAAO;IAChBE,OAAO,EAAEA,OAAO;IAChB7D,OAAO,EAAEA;EACX,CAAC,CAAC,CAAC;AACL,CAAC;AACD,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}