{"ast": null, "code": "import * as React from 'react';\nimport Tooltip from '../../tooltip';\nconst EllipsisTooltip = _ref => {\n  let {\n    enabledEllipsis,\n    isEllipsis,\n    children,\n    tooltipProps\n  } = _ref;\n  if (!(tooltipProps === null || tooltipProps === void 0 ? void 0 : tooltipProps.title) || !enabledEllipsis) {\n    return children;\n  }\n  return /*#__PURE__*/React.createElement(Tooltip, Object.assign({\n    open: isEllipsis ? undefined : false\n  }, tooltipProps), children);\n};\nif (process.env.NODE_ENV !== 'production') {\n  EllipsisTooltip.displayName = 'EllipsisTooltip';\n}\nexport default EllipsisTooltip;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "EllipsisTooltip", "_ref", "enabledEllipsis", "isEllipsis", "children", "tooltipProps", "title", "createElement", "Object", "assign", "open", "undefined", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/typography/Base/EllipsisTooltip.js"], "sourcesContent": ["import * as React from 'react';\nimport Tooltip from '../../tooltip';\nconst EllipsisTooltip = _ref => {\n  let {\n    enabledEllipsis,\n    isEllipsis,\n    children,\n    tooltipProps\n  } = _ref;\n  if (!(tooltipProps === null || tooltipProps === void 0 ? void 0 : tooltipProps.title) || !enabledEllipsis) {\n    return children;\n  }\n  return /*#__PURE__*/React.createElement(Tooltip, Object.assign({\n    open: isEllipsis ? undefined : false\n  }, tooltipProps), children);\n};\nif (process.env.NODE_ENV !== 'production') {\n  EllipsisTooltip.displayName = 'EllipsisTooltip';\n}\nexport default EllipsisTooltip;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,eAAe;AACnC,MAAMC,eAAe,GAAGC,IAAI,IAAI;EAC9B,IAAI;IACFC,eAAe;IACfC,UAAU;IACVC,QAAQ;IACRC;EACF,CAAC,GAAGJ,IAAI;EACR,IAAI,EAAEI,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACC,KAAK,CAAC,IAAI,CAACJ,eAAe,EAAE;IACzG,OAAOE,QAAQ;EACjB;EACA,OAAO,aAAaN,KAAK,CAACS,aAAa,CAACR,OAAO,EAAES,MAAM,CAACC,MAAM,CAAC;IAC7DC,IAAI,EAAEP,UAAU,GAAGQ,SAAS,GAAG;EACjC,CAAC,EAAEN,YAAY,CAAC,EAAED,QAAQ,CAAC;AAC7B,CAAC;AACD,IAAIQ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCd,eAAe,CAACe,WAAW,GAAG,iBAAiB;AACjD;AACA,eAAef,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}