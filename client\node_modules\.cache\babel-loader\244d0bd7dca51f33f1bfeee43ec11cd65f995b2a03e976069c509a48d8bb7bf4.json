{"ast": null, "code": "import { lintWarning } from \"./utils\";\nfunction isConcatSelector(selector) {\n  var _selector$match;\n  var notContent = ((_selector$match = selector.match(/:not\\(([^)]*)\\)/)) === null || _selector$match === void 0 ? void 0 : _selector$match[1]) || '';\n\n  // split selector. e.g.\n  // `h1#a.b` => ['h1', #a', '.b']\n  var splitCells = notContent.split(/(\\[[^[]*])|(?=[.#])/).filter(function (str) {\n    return str;\n  });\n  return splitCells.length > 1;\n}\nfunction parsePath(info) {\n  return info.parentSelectors.reduce(function (prev, cur) {\n    if (!prev) {\n      return cur;\n    }\n    return cur.includes('&') ? cur.replace(/&/g, prev) : \"\".concat(prev, \" \").concat(cur);\n  }, '');\n}\nvar linter = function linter(key, value, info) {\n  var parentSelectorPath = parsePath(info);\n  var notList = parentSelectorPath.match(/:not\\([^)]*\\)/g) || [];\n  if (notList.length > 0 && notList.some(isConcatSelector)) {\n    lintWarning(\"Concat ':not' selector not support in legacy browsers.\", info);\n  }\n};\nexport default linter;", "map": {"version": 3, "names": ["lintWarning", "isConcatSelector", "selector", "_selector$match", "notContent", "match", "splitCells", "split", "filter", "str", "length", "parsePath", "info", "parentSelectors", "reduce", "prev", "cur", "includes", "replace", "concat", "linter", "key", "value", "parentSelectorPath", "notList", "some"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/@ant-design/cssinjs/es/linters/legacyNotSelectorLinter.js"], "sourcesContent": ["import { lintWarning } from \"./utils\";\nfunction isConcatSelector(selector) {\n  var _selector$match;\n  var notContent = ((_selector$match = selector.match(/:not\\(([^)]*)\\)/)) === null || _selector$match === void 0 ? void 0 : _selector$match[1]) || '';\n\n  // split selector. e.g.\n  // `h1#a.b` => ['h1', #a', '.b']\n  var splitCells = notContent.split(/(\\[[^[]*])|(?=[.#])/).filter(function (str) {\n    return str;\n  });\n  return splitCells.length > 1;\n}\nfunction parsePath(info) {\n  return info.parentSelectors.reduce(function (prev, cur) {\n    if (!prev) {\n      return cur;\n    }\n    return cur.includes('&') ? cur.replace(/&/g, prev) : \"\".concat(prev, \" \").concat(cur);\n  }, '');\n}\nvar linter = function linter(key, value, info) {\n  var parentSelectorPath = parsePath(info);\n  var notList = parentSelectorPath.match(/:not\\([^)]*\\)/g) || [];\n  if (notList.length > 0 && notList.some(isConcatSelector)) {\n    lintWarning(\"Concat ':not' selector not support in legacy browsers.\", info);\n  }\n};\nexport default linter;"], "mappings": "AAAA,SAASA,WAAW,QAAQ,SAAS;AACrC,SAASC,gBAAgBA,CAACC,QAAQ,EAAE;EAClC,IAAIC,eAAe;EACnB,IAAIC,UAAU,GAAG,CAAC,CAACD,eAAe,GAAGD,QAAQ,CAACG,KAAK,CAAC,iBAAiB,CAAC,MAAM,IAAI,IAAIF,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAAC,CAAC,CAAC,KAAK,EAAE;;EAEnJ;EACA;EACA,IAAIG,UAAU,GAAGF,UAAU,CAACG,KAAK,CAAC,qBAAqB,CAAC,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;IAC7E,OAAOA,GAAG;EACZ,CAAC,CAAC;EACF,OAAOH,UAAU,CAACI,MAAM,GAAG,CAAC;AAC9B;AACA,SAASC,SAASA,CAACC,IAAI,EAAE;EACvB,OAAOA,IAAI,CAACC,eAAe,CAACC,MAAM,CAAC,UAAUC,IAAI,EAAEC,GAAG,EAAE;IACtD,IAAI,CAACD,IAAI,EAAE;MACT,OAAOC,GAAG;IACZ;IACA,OAAOA,GAAG,CAACC,QAAQ,CAAC,GAAG,CAAC,GAAGD,GAAG,CAACE,OAAO,CAAC,IAAI,EAAEH,IAAI,CAAC,GAAG,EAAE,CAACI,MAAM,CAACJ,IAAI,EAAE,GAAG,CAAC,CAACI,MAAM,CAACH,GAAG,CAAC;EACvF,CAAC,EAAE,EAAE,CAAC;AACR;AACA,IAAII,MAAM,GAAG,SAASA,MAAMA,CAACC,GAAG,EAAEC,KAAK,EAAEV,IAAI,EAAE;EAC7C,IAAIW,kBAAkB,GAAGZ,SAAS,CAACC,IAAI,CAAC;EACxC,IAAIY,OAAO,GAAGD,kBAAkB,CAAClB,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE;EAC9D,IAAImB,OAAO,CAACd,MAAM,GAAG,CAAC,IAAIc,OAAO,CAACC,IAAI,CAACxB,gBAAgB,CAAC,EAAE;IACxDD,WAAW,CAAC,wDAAwD,EAAEY,IAAI,CAAC;EAC7E;AACF,CAAC;AACD,eAAeQ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}