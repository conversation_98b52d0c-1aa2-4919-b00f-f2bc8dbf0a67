{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nimport Col from '../grid/col';\nimport { FormContext, FormItemPrefixContext } from './context';\nimport ErrorList from './ErrorList';\nconst FormItemInput = props => {\n  const {\n    prefixCls,\n    status,\n    wrapperCol,\n    children,\n    errors,\n    warnings,\n    _internalItemRender: formItemRender,\n    extra,\n    help,\n    fieldId,\n    marginBottom,\n    onErrorVisibleChanged\n  } = props;\n  const baseClassName = \"\".concat(prefixCls, \"-item\");\n  const formContext = React.useContext(FormContext);\n  const mergedWrapperCol = wrapperCol || formContext.wrapperCol || {};\n  const className = classNames(\"\".concat(baseClassName, \"-control\"), mergedWrapperCol.className);\n  // Pass to sub FormItem should not with col info\n  const subFormContext = React.useMemo(() => Object.assign({}, formContext), [formContext]);\n  delete subFormContext.labelCol;\n  delete subFormContext.wrapperCol;\n  const inputDom = /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(baseClassName, \"-control-input\")\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(baseClassName, \"-control-input-content\")\n  }, children));\n  const formItemContext = React.useMemo(() => ({\n    prefixCls,\n    status\n  }), [prefixCls, status]);\n  const errorListDom = marginBottom !== null || errors.length || warnings.length ? /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      flexWrap: 'nowrap'\n    }\n  }, /*#__PURE__*/React.createElement(FormItemPrefixContext.Provider, {\n    value: formItemContext\n  }, /*#__PURE__*/React.createElement(ErrorList, {\n    fieldId: fieldId,\n    errors: errors,\n    warnings: warnings,\n    help: help,\n    helpStatus: status,\n    className: \"\".concat(baseClassName, \"-explain-connected\"),\n    onVisibleChanged: onErrorVisibleChanged\n  })), !!marginBottom && /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      width: 0,\n      height: marginBottom\n    }\n  })) : null;\n  const extraProps = {};\n  if (fieldId) {\n    extraProps.id = \"\".concat(fieldId, \"_extra\");\n  }\n  // If extra = 0, && will goes wrong\n  // 0&&error -> 0\n  const extraDom = extra ? /*#__PURE__*/React.createElement(\"div\", Object.assign({}, extraProps, {\n    className: \"\".concat(baseClassName, \"-extra\")\n  }), extra) : null;\n  const dom = formItemRender && formItemRender.mark === 'pro_table_render' && formItemRender.render ? formItemRender.render(props, {\n    input: inputDom,\n    errorList: errorListDom,\n    extra: extraDom\n  }) : /*#__PURE__*/React.createElement(React.Fragment, null, inputDom, errorListDom, extraDom);\n  return /*#__PURE__*/React.createElement(FormContext.Provider, {\n    value: subFormContext\n  }, /*#__PURE__*/React.createElement(Col, Object.assign({}, mergedWrapperCol, {\n    className: className\n  }), dom));\n};\nexport default FormItemInput;", "map": {"version": 3, "names": ["classNames", "React", "Col", "FormContext", "FormItemPrefixContext", "ErrorList", "FormItemInput", "props", "prefixCls", "status", "wrapperCol", "children", "errors", "warnings", "_internalItemRender", "formItemRender", "extra", "help", "fieldId", "marginBottom", "onErrorVisibleChanged", "baseClassName", "concat", "formContext", "useContext", "mergedWrapperCol", "className", "subFormContext", "useMemo", "Object", "assign", "labelCol", "inputDom", "createElement", "formItemContext", "errorListDom", "length", "style", "display", "flexWrap", "Provider", "value", "helpStatus", "onVisibleChanged", "width", "height", "extraProps", "id", "extraDom", "dom", "mark", "render", "input", "errorList", "Fragment"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/antd/es/form/FormItemInput.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nimport Col from '../grid/col';\nimport { FormContext, FormItemPrefixContext } from './context';\nimport ErrorList from './ErrorList';\nconst FormItemInput = props => {\n  const {\n    prefixCls,\n    status,\n    wrapperCol,\n    children,\n    errors,\n    warnings,\n    _internalItemRender: formItemRender,\n    extra,\n    help,\n    fieldId,\n    marginBottom,\n    onErrorVisibleChanged\n  } = props;\n  const baseClassName = `${prefixCls}-item`;\n  const formContext = React.useContext(FormContext);\n  const mergedWrapperCol = wrapperCol || formContext.wrapperCol || {};\n  const className = classNames(`${baseClassName}-control`, mergedWrapperCol.className);\n  // Pass to sub FormItem should not with col info\n  const subFormContext = React.useMemo(() => Object.assign({}, formContext), [formContext]);\n  delete subFormContext.labelCol;\n  delete subFormContext.wrapperCol;\n  const inputDom = /*#__PURE__*/React.createElement(\"div\", {\n    className: `${baseClassName}-control-input`\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: `${baseClassName}-control-input-content`\n  }, children));\n  const formItemContext = React.useMemo(() => ({\n    prefixCls,\n    status\n  }), [prefixCls, status]);\n  const errorListDom = marginBottom !== null || errors.length || warnings.length ? /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      flexWrap: 'nowrap'\n    }\n  }, /*#__PURE__*/React.createElement(FormItemPrefixContext.Provider, {\n    value: formItemContext\n  }, /*#__PURE__*/React.createElement(ErrorList, {\n    fieldId: fieldId,\n    errors: errors,\n    warnings: warnings,\n    help: help,\n    helpStatus: status,\n    className: `${baseClassName}-explain-connected`,\n    onVisibleChanged: onErrorVisibleChanged\n  })), !!marginBottom && /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      width: 0,\n      height: marginBottom\n    }\n  })) : null;\n  const extraProps = {};\n  if (fieldId) {\n    extraProps.id = `${fieldId}_extra`;\n  }\n  // If extra = 0, && will goes wrong\n  // 0&&error -> 0\n  const extraDom = extra ? /*#__PURE__*/React.createElement(\"div\", Object.assign({}, extraProps, {\n    className: `${baseClassName}-extra`\n  }), extra) : null;\n  const dom = formItemRender && formItemRender.mark === 'pro_table_render' && formItemRender.render ? formItemRender.render(props, {\n    input: inputDom,\n    errorList: errorListDom,\n    extra: extraDom\n  }) : /*#__PURE__*/React.createElement(React.Fragment, null, inputDom, errorListDom, extraDom);\n  return /*#__PURE__*/React.createElement(FormContext.Provider, {\n    value: subFormContext\n  }, /*#__PURE__*/React.createElement(Col, Object.assign({}, mergedWrapperCol, {\n    className: className\n  }), dom));\n};\nexport default FormItemInput;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,GAAG,MAAM,aAAa;AAC7B,SAASC,WAAW,EAAEC,qBAAqB,QAAQ,WAAW;AAC9D,OAAOC,SAAS,MAAM,aAAa;AACnC,MAAMC,aAAa,GAAGC,KAAK,IAAI;EAC7B,MAAM;IACJC,SAAS;IACTC,MAAM;IACNC,UAAU;IACVC,QAAQ;IACRC,MAAM;IACNC,QAAQ;IACRC,mBAAmB,EAAEC,cAAc;IACnCC,KAAK;IACLC,IAAI;IACJC,OAAO;IACPC,YAAY;IACZC;EACF,CAAC,GAAGb,KAAK;EACT,MAAMc,aAAa,MAAAC,MAAA,CAAMd,SAAS,UAAO;EACzC,MAAMe,WAAW,GAAGtB,KAAK,CAACuB,UAAU,CAACrB,WAAW,CAAC;EACjD,MAAMsB,gBAAgB,GAAGf,UAAU,IAAIa,WAAW,CAACb,UAAU,IAAI,CAAC,CAAC;EACnE,MAAMgB,SAAS,GAAG1B,UAAU,IAAAsB,MAAA,CAAID,aAAa,eAAYI,gBAAgB,CAACC,SAAS,CAAC;EACpF;EACA,MAAMC,cAAc,GAAG1B,KAAK,CAAC2B,OAAO,CAAC,MAAMC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEP,WAAW,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;EACzF,OAAOI,cAAc,CAACI,QAAQ;EAC9B,OAAOJ,cAAc,CAACjB,UAAU;EAChC,MAAMsB,QAAQ,GAAG,aAAa/B,KAAK,CAACgC,aAAa,CAAC,KAAK,EAAE;IACvDP,SAAS,KAAAJ,MAAA,CAAKD,aAAa;EAC7B,CAAC,EAAE,aAAapB,KAAK,CAACgC,aAAa,CAAC,KAAK,EAAE;IACzCP,SAAS,KAAAJ,MAAA,CAAKD,aAAa;EAC7B,CAAC,EAAEV,QAAQ,CAAC,CAAC;EACb,MAAMuB,eAAe,GAAGjC,KAAK,CAAC2B,OAAO,CAAC,OAAO;IAC3CpB,SAAS;IACTC;EACF,CAAC,CAAC,EAAE,CAACD,SAAS,EAAEC,MAAM,CAAC,CAAC;EACxB,MAAM0B,YAAY,GAAGhB,YAAY,KAAK,IAAI,IAAIP,MAAM,CAACwB,MAAM,IAAIvB,QAAQ,CAACuB,MAAM,GAAG,aAAanC,KAAK,CAACgC,aAAa,CAAC,KAAK,EAAE;IACvHI,KAAK,EAAE;MACLC,OAAO,EAAE,MAAM;MACfC,QAAQ,EAAE;IACZ;EACF,CAAC,EAAE,aAAatC,KAAK,CAACgC,aAAa,CAAC7B,qBAAqB,CAACoC,QAAQ,EAAE;IAClEC,KAAK,EAAEP;EACT,CAAC,EAAE,aAAajC,KAAK,CAACgC,aAAa,CAAC5B,SAAS,EAAE;IAC7Ca,OAAO,EAAEA,OAAO;IAChBN,MAAM,EAAEA,MAAM;IACdC,QAAQ,EAAEA,QAAQ;IAClBI,IAAI,EAAEA,IAAI;IACVyB,UAAU,EAAEjC,MAAM;IAClBiB,SAAS,KAAAJ,MAAA,CAAKD,aAAa,uBAAoB;IAC/CsB,gBAAgB,EAAEvB;EACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAACD,YAAY,IAAI,aAAalB,KAAK,CAACgC,aAAa,CAAC,KAAK,EAAE;IAC7DI,KAAK,EAAE;MACLO,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE1B;IACV;EACF,CAAC,CAAC,CAAC,GAAG,IAAI;EACV,MAAM2B,UAAU,GAAG,CAAC,CAAC;EACrB,IAAI5B,OAAO,EAAE;IACX4B,UAAU,CAACC,EAAE,MAAAzB,MAAA,CAAMJ,OAAO,WAAQ;EACpC;EACA;EACA;EACA,MAAM8B,QAAQ,GAAGhC,KAAK,GAAG,aAAaf,KAAK,CAACgC,aAAa,CAAC,KAAK,EAAEJ,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEgB,UAAU,EAAE;IAC7FpB,SAAS,KAAAJ,MAAA,CAAKD,aAAa;EAC7B,CAAC,CAAC,EAAEL,KAAK,CAAC,GAAG,IAAI;EACjB,MAAMiC,GAAG,GAAGlC,cAAc,IAAIA,cAAc,CAACmC,IAAI,KAAK,kBAAkB,IAAInC,cAAc,CAACoC,MAAM,GAAGpC,cAAc,CAACoC,MAAM,CAAC5C,KAAK,EAAE;IAC/H6C,KAAK,EAAEpB,QAAQ;IACfqB,SAAS,EAAElB,YAAY;IACvBnB,KAAK,EAAEgC;EACT,CAAC,CAAC,GAAG,aAAa/C,KAAK,CAACgC,aAAa,CAAChC,KAAK,CAACqD,QAAQ,EAAE,IAAI,EAAEtB,QAAQ,EAAEG,YAAY,EAAEa,QAAQ,CAAC;EAC7F,OAAO,aAAa/C,KAAK,CAACgC,aAAa,CAAC9B,WAAW,CAACqC,QAAQ,EAAE;IAC5DC,KAAK,EAAEd;EACT,CAAC,EAAE,aAAa1B,KAAK,CAACgC,aAAa,CAAC/B,GAAG,EAAE2B,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEL,gBAAgB,EAAE;IAC3EC,SAAS,EAAEA;EACb,CAAC,CAAC,EAAEuB,GAAG,CAAC,CAAC;AACX,CAAC;AACD,eAAe3C,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}