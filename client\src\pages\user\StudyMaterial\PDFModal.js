import { useEffect, useRef, useState } from "react";
import * as pdfjsLib from "pdfjs-dist";
import ReactModal from "react-modal";
import FloatingBrainwaveAI from "../../../components/FloatingBrainwaveAI";
import { useLanguage } from "../../../contexts/LanguageContext";
ReactModal.setAppElement('#root');

pdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@2.15.349/build/pdf.worker.min.js`;

// Add CSS for spinner animation
const spinnerStyle = `
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

// Inject the CSS
if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.textContent = spinnerStyle;
  document.head.appendChild(style);
}

const PDFModal = ({ modalIsOpen, closeModal, documentUrl }) => {
  const { isKiswahili } = useLanguage();
  const [pages, setPages] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [selectedText, setSelectedText] = useState('');
  const [showCopyButton, setShowCopyButton] = useState(false);
  const [pdfContext, setPdfContext] = useState(null);
  const [extractedText, setExtractedText] = useState('');
  const [showAIButton, setShowAIButton] = useState(false);
  const [showBrainwaveAI, setShowBrainwaveAI] = useState(false);
  const canvasRefs = useRef([]);
  const textLayerRefs = useRef([]);
  const containerRef = useRef(null);
  const renderingRefs = useRef({}); // Track rendering state per page

  // Handle text selection
  const handleTextSelection = () => {
    const selection = window.getSelection();
    const text = selection.toString().trim();

    if (text) {
      setSelectedText(text);
      setShowCopyButton(true);
      // Auto-open AI if text is selected and AI button is available
      if (showAIButton && text.length > 10) {
        setShowBrainwaveAI(true);
      }
    } else {
      setSelectedText('');
      setShowCopyButton(false);
    }
  };

  // Copy selected text to clipboard
  const copyToClipboard = async () => {
    if (selectedText) {
      try {
        await navigator.clipboard.writeText(selectedText);
        alert('Text copied to clipboard!');
        setShowCopyButton(false);
        window.getSelection().removeAllRanges();
      } catch (err) {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = selectedText;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        alert('Text copied to clipboard!');
        setShowCopyButton(false);
        window.getSelection().removeAllRanges();
      }
    }
  };

  const renderPDF = async (url) => {
    try {
      setIsLoading(true);
      setLoadingProgress(0);

      const pdf = await pdfjsLib.getDocument(url).promise;
      console.log("PDF loaded");

      setTotalPages(pdf.numPages);

      // Load pages progressively (first 3 pages immediately, then lazy load others)
      const initialPagesToLoad = Math.min(3, pdf.numPages);
      const pagesData = [];

      for (let i = 1; i <= initialPagesToLoad; i++) {
        const page = await pdf.getPage(i);
        pagesData.push(page);
        setLoadingProgress((i / pdf.numPages) * 100);
      }

      setPages(pagesData);
      setIsLoading(false);

      // Extract text from first few pages for AI context
      await extractPDFText(pdf);

      // Create PDF context for AI
      const fileName = url.split('/').pop() || 'PDF Document';
      const textContent = await extractPDFText(pdf);
      setPdfContext({
        title: fileName,
        subject: 'Study Material',
        totalPages: pdf.numPages,
        extractedText: textContent,
        url: url
      });

      setShowAIButton(true);

      // Load remaining pages in background
      if (pdf.numPages > initialPagesToLoad) {
        loadRemainingPages(pdf, initialPagesToLoad + 1, pagesData);
      }

    } catch (error) {
      console.error("Error loading PDF:", error);
      setIsLoading(false);
    }
  };

  const loadRemainingPages = async (pdf, startPage, existingPages) => {
    const updatedPages = [...existingPages];

    for (let i = startPage; i <= pdf.numPages; i++) {
      try {
        const page = await pdf.getPage(i);
        updatedPages.push(page);
        setPages([...updatedPages]);
        setLoadingProgress((i / pdf.numPages) * 100);

        // Small delay to prevent blocking the UI
        await new Promise(resolve => setTimeout(resolve, 50));
      } catch (error) {
        console.error(`Error loading page ${i}:`, error);
      }
    }
  };

  // Extract text from PDF for AI context
  const extractPDFText = async (pdf) => {
    try {
      let fullText = '';
      const maxPagesToExtract = Math.min(10, pdf.numPages); // Extract from first 10 pages for better context

      console.log(`Extracting text from ${maxPagesToExtract} pages for AI context...`);

      for (let i = 1; i <= maxPagesToExtract; i++) {
        const page = await pdf.getPage(i);
        const textContent = await page.getTextContent();

        // Better text extraction with proper spacing and formatting
        let pageText = '';
        let lastY = null;

        textContent.items.forEach((item, index) => {
          // Add line breaks for new lines (when Y position changes significantly)
          if (lastY !== null && Math.abs(lastY - item.transform[5]) > 5) {
            pageText += '\n';
          }

          // Add space before text if needed
          if (index > 0 && !pageText.endsWith(' ') && !pageText.endsWith('\n')) {
            pageText += ' ';
          }

          pageText += item.str;
          lastY = item.transform[5];
        });

        // Clean up the text
        pageText = pageText.replace(/\s+/g, ' ').trim();

        if (pageText.length > 0) {
          fullText += `=== Page ${i} ===\n${pageText}\n\n`;
        }
      }

      // Add document metadata
      const documentInfo = `Document: ${pdf.numPages} pages total, extracted content from first ${maxPagesToExtract} pages.\n\n`;
      fullText = documentInfo + fullText;

      setExtractedText(fullText);
      console.log(`Extracted ${fullText.length} characters from PDF for AI context`);
      console.log('PDF Text Preview:', fullText.substring(0, 500) + '...');
      return fullText;
    } catch (error) {
      console.warn('Could not extract text from PDF:', error);
      return 'PDF content extraction failed, but document is available for viewing.';
    }
  };

  const renderPage = async (page, index) => {
    const canvas = canvasRefs.current[index];
    const textLayer = textLayerRefs.current[index];
    if (!canvas || renderingRefs.current[index] || !containerRef.current) return;

    try {
      renderingRefs.current[index] = true;

      const viewport = page.getViewport({ scale: 1.0 });
      const containerWidth = containerRef.current.clientWidth;
      const scale = containerWidth / viewport.width;
      const scaledViewport = page.getViewport({ scale });

      const context = canvas.getContext("2d");
      canvas.height = scaledViewport.height;
      canvas.width = scaledViewport.width;
      canvas.style.width = '100%';
      canvas.style.height = 'auto';

      const renderContext = {
        canvasContext: context,
        viewport: scaledViewport,
      };

      await page.render(renderContext).promise;

      // Render text layer for selection
      if (textLayer) {
        textLayer.innerHTML = '';
        textLayer.style.width = canvas.style.width;
        textLayer.style.height = canvas.style.height;
        textLayer.style.position = 'absolute';
        textLayer.style.top = '0';
        textLayer.style.left = '0';
        textLayer.style.pointerEvents = 'auto';
        textLayer.style.userSelect = 'text';

        try {
          const textContent = await page.getTextContent();
          const textLayerDiv = textLayer;

          // Simple text rendering for selection
          textContent.items.forEach((item, itemIndex) => {
            const textSpan = document.createElement('span');
            textSpan.textContent = item.str;
            textSpan.style.position = 'absolute';
            textSpan.style.fontSize = `${item.height * scale}px`;
            textSpan.style.left = `${item.transform[4] * scale}px`;
            textSpan.style.top = `${scaledViewport.height - item.transform[5] * scale - item.height * scale}px`;
            textSpan.style.fontFamily = item.fontName || 'sans-serif';
            textSpan.style.color = 'transparent';
            textSpan.style.userSelect = 'text';
            textLayerDiv.appendChild(textSpan);
          });
        } catch (textError) {
          console.warn(`Could not render text layer for page ${index + 1}:`, textError);
        }
      }

      console.log(`Page ${index + 1} rendered`);
    } catch (error) {
      console.error(`Error rendering page ${index + 1}:`, error);
    } finally {
      renderingRefs.current[index] = false;
    }
  };

  useEffect(() => {
    if (modalIsOpen && documentUrl) {
      setPages([]);
      setTotalPages(0);
      setLoadingProgress(0);
      setSelectedText('');
      setShowCopyButton(false);
      canvasRefs.current = [];
      textLayerRefs.current = [];
      renderingRefs.current = {};
      renderPDF(documentUrl);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [modalIsOpen, documentUrl]);

  // Add event listener for text selection
  useEffect(() => {
    if (modalIsOpen) {
      document.addEventListener('selectionchange', handleTextSelection);

      // Listen for custom event to open Brainwave AI
      const handleOpenBrainwaveAI = (event) => {
        setShowBrainwaveAI(true);
      };

      window.addEventListener('openBrainwaveAI', handleOpenBrainwaveAI);

      return () => {
        document.removeEventListener('selectionchange', handleTextSelection);
        window.removeEventListener('openBrainwaveAI', handleOpenBrainwaveAI);
      };
    }
  }, [modalIsOpen]);

  // Effect to render pages when they're loaded
  useEffect(() => {
    if (pages.length > 0 && containerRef.current) {
      pages.forEach((page, index) => {
        renderPage(page, index);
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pages]);

  // Re-render pages when window is resized
  useEffect(() => {
    const handleResize = () => {
      if (pages.length > 0) {
        pages.forEach((page, index) => {
          renderPage(page, index);
        });
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [pages]);

  return (
    <ReactModal
      isOpen={modalIsOpen}
      onRequestClose={closeModal}
      contentLabel="Document Preview"
      style={{
        overlay: {
          backgroundColor: 'rgba(0, 0, 0, 0.75)'
        },
        content: {
          top: '50%',
          left: '50%',
          right: 'auto',
          bottom: 'auto',
          marginRight: '-50%',
          transform: 'translate(-50%, -50%)',
          width: '70%',
          height: '90%',
          padding: '20px',
          borderRadius: '10px',
          overflow: 'hidden',
        },
      }}
    >
      <button
        onClick={closeModal}
        style={{
          position: "absolute",
          top: "10px",
          right: "10px",
          background: "transparent",
          border: "none",
          fontSize: "20px",
          cursor: "pointer",
          zIndex: 1,
        }}
      >
        X
      </button>

      <div
        ref={containerRef}
        style={{
          height: '100%',
          overflow: 'auto',
          padding: '10px',
          scrollbarWidth: 'thin'
        }}
      >
        {isLoading && (
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '200px',
            color: '#666'
          }}>
            <div style={{
              width: '50px',
              height: '50px',
              border: '3px solid #f3f3f3',
              borderTop: '3px solid #3498db',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite',
              marginBottom: '20px'
            }}></div>
            <p>Loading PDF...</p>
            <div style={{
              width: '200px',
              height: '6px',
              backgroundColor: '#f3f3f3',
              borderRadius: '3px',
              overflow: 'hidden',
              marginTop: '10px'
            }}>
              <div style={{
                width: `${loadingProgress}%`,
                height: '100%',
                backgroundColor: '#3498db',
                transition: 'width 0.3s ease'
              }}></div>
            </div>
            <small style={{ marginTop: '5px' }}>
              {Math.round(loadingProgress)}% loaded
            </small>
          </div>
        )}

        {/* Copy Button */}
        {showCopyButton && (
          <div style={{
            position: 'fixed',
            top: '20px',
            right: '20px',
            zIndex: 1000,
            background: '#007BFF',
            color: 'white',
            padding: '10px 15px',
            borderRadius: '5px',
            cursor: 'pointer',
            boxShadow: '0 2px 10px rgba(0,0,0,0.3)',
            fontSize: '14px',
            fontWeight: 'bold'
          }} onClick={copyToClipboard}>
            📋 {isKiswahili ? 'Nakili Maandishi' : 'Copy Selected Text'}
          </div>
        )}

        {/* PDF AI Action Buttons */}
        {showAIButton && (
          <div style={{
            position: 'fixed',
            bottom: '20px',
            right: '20px',
            zIndex: 1000,
            display: 'flex',
            flexDirection: 'column',
            gap: '10px'
          }}>
            {/* Quick Ask AI Button */}
            <div style={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              color: 'white',
              padding: '12px 18px',
              borderRadius: '25px',
              cursor: 'pointer',
              boxShadow: '0 4px 15px rgba(102, 126, 234, 0.4)',
              fontSize: '14px',
              fontWeight: 'bold',
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              transition: 'all 0.3s ease'
            }}
            onClick={() => {
              setShowBrainwaveAI(true);
            }}
            onMouseEnter={(e) => {
              e.target.style.transform = 'translateY(-2px)';
              e.target.style.boxShadow = '0 6px 20px rgba(102, 126, 234, 0.6)';
            }}
            onMouseLeave={(e) => {
              e.target.style.transform = 'translateY(0)';
              e.target.style.boxShadow = '0 4px 15px rgba(102, 126, 234, 0.4)';
            }}>
              🤖 {isKiswahili ? 'Uliza AI kuhusu PDF' : 'Ask AI about PDF'}
            </div>

            {/* PDF Summary Button */}
            <div style={{
              background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
              color: 'white',
              padding: '10px 16px',
              borderRadius: '20px',
              cursor: 'pointer',
              boxShadow: '0 4px 15px rgba(16, 185, 129, 0.4)',
              fontSize: '13px',
              fontWeight: 'bold',
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              transition: 'all 0.3s ease'
            }}
            onClick={() => {
              setShowBrainwaveAI(true);
              // Auto-fill summary request
              setTimeout(() => {
                const event = new CustomEvent('autoFillAIInput', {
                  detail: {
                    text: isKiswahili ? "Nipe muhtasari wa PDF hii" : "Give me a summary of this PDF"
                  }
                });
                window.dispatchEvent(event);
              }, 500);
            }}
            onMouseEnter={(e) => {
              e.target.style.transform = 'translateY(-2px)';
              e.target.style.boxShadow = '0 6px 20px rgba(16, 185, 129, 0.6)';
            }}
            onMouseLeave={(e) => {
              e.target.style.transform = 'translateY(0)';
              e.target.style.boxShadow = '0 4px 15px rgba(16, 185, 129, 0.4)';
            }}>
              📄 {isKiswahili ? 'Muhtasari' : 'Summary'}
            </div>
          </div>
        )}

        {pages.map((page, index) => (
          <div
            key={index}
            style={{
              marginBottom: '10px',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              position: 'relative'
            }}
          >
            <div style={{ position: 'relative', display: 'inline-block' }}>
              <canvas
                ref={element => {
                  canvasRefs.current[index] = element;
                }}
                style={{
                  maxWidth: '100%',
                  height: 'auto',
                  border: '1px solid black',
                  display: 'block'
                }}
              />
              <div
                ref={element => {
                  textLayerRefs.current[index] = element;
                }}
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  pointerEvents: 'auto',
                  userSelect: 'text',
                  cursor: 'text'
                }}
              />
            </div>
          </div>
        ))}

        {totalPages > pages.length && !isLoading && (
          <div style={{
            textAlign: 'center',
            padding: '20px',
            color: '#666',
            fontStyle: 'italic'
          }}>
            Loading remaining pages... ({pages.length}/{totalPages})
          </div>
        )}
      </div>

      {/* PDF-Aware Brainwave AI */}
      {showBrainwaveAI && (
        <FloatingBrainwaveAI
          pdfContext={pdfContext}
          selectedText={selectedText}
        />
      )}
    </ReactModal>
  );
};

export default PDFModal;