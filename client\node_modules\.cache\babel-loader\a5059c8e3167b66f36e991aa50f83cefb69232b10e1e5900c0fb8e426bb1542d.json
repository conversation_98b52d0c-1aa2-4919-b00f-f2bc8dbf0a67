{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Profile\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport \"./index.css\";\nimport PageTitle from \"../../../components/PageTitle\";\nimport { getUserInfo, updateUserInfo, updateUserPhoto } from \"../../../apicalls/users\";\nimport { Form, message, Modal, Input, Button } from \"antd\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { getAllReportsForRanking, getUserRanking, getXPLeaderboard } from \"../../../apicalls/reports\";\nimport ProfilePicture from \"../../../components/common/ProfilePicture\";\nimport SubscriptionModal from \"../../../components/SubscriptionModal/SubscriptionModal\";\nimport { useLanguage } from \"../../../contexts/LanguageContext\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Profile = () => {\n  _s();\n  var _userRankingStats$use, _userRankingStats$use2, _userRankingStats$use3, _userRankingStats$use4, _userRankingStats$use5, _subscriptionData$pla, _subscriptionData$pla2, _subscriptionData$pla3, _subscriptionData$pla4;\n  const {\n    t,\n    isKiswahili,\n    getClassName\n  } = useLanguage();\n  const [userDetails, setUserDetails] = useState(null);\n  const [rankingData, setRankingData] = useState(null);\n  const [userRanking, setUserRanking] = useState(null);\n  const [userRankingStats, setUserRankingStats] = useState(null);\n  const [edit, setEdit] = useState(false);\n  const [imagePreview, setImagePreview] = useState(null);\n  const [formData, setFormData] = useState({\n    name: \"\",\n    email: \"\",\n    school: \"\",\n    level: \"\",\n    class_: \"\",\n    phoneNumber: \"\"\n  });\n  const [profileImage, setProfileImage] = useState(null);\n  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false);\n  const [showLevelChangeModal, setShowLevelChangeModal] = useState(false);\n  const [pendingLevelChange, setPendingLevelChange] = useState(null);\n  const dispatch = useDispatch();\n  const {\n    subscriptionData\n  } = useSelector(state => state.subscription);\n  const fetchReports = async () => {\n    try {\n      const response = await getAllReportsForRanking();\n      if (response.success) {\n        setRankingData(response.data);\n      } else {\n        message.error(response.message);\n      }\n      dispatch(HideLoading());\n    } catch (error) {\n      message.error(error.message);\n      dispatch(HideLoading());\n    }\n  };\n  const getUserStats = () => {\n    const Ranking = rankingData.map((user, index) => ({\n      user,\n      ranking: index + 1\n    })).filter(item => item.user.userId.includes(userDetails._id));\n    setUserRanking(Ranking);\n  };\n\n  // Fetch user ranking data from the ranking system\n  const fetchUserRankingData = async () => {\n    if (!(userDetails !== null && userDetails !== void 0 && userDetails._id)) return;\n    try {\n      dispatch(ShowLoading());\n\n      // Get user's ranking position and nearby users\n      const rankingResponse = await getUserRanking(userDetails._id, 5);\n      if (rankingResponse.success) {\n        setUserRankingStats(rankingResponse.data);\n      }\n\n      // Also get the full leaderboard to find user's position\n      const leaderboardResponse = await getXPLeaderboard({\n        limit: 1000,\n        levelFilter: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.level) || 'all'\n      });\n      if (leaderboardResponse.success) {\n        const userIndex = leaderboardResponse.data.findIndex(user => user._id === userDetails._id);\n        if (userIndex >= 0) {\n          const userWithRank = {\n            ...leaderboardResponse.data[userIndex],\n            rank: userIndex + 1,\n            totalUsers: leaderboardResponse.data.length\n          };\n          setUserRankingStats(prev => ({\n            ...prev,\n            userRank: userIndex + 1,\n            totalUsers: leaderboardResponse.data.length,\n            user: userWithRank\n          }));\n        }\n      }\n      dispatch(HideLoading());\n    } catch (error) {\n      dispatch(HideLoading());\n      console.error('Error fetching ranking data:', error);\n    }\n  };\n  useEffect(() => {\n    if (rankingData && userDetails) {\n      getUserStats();\n    }\n  }, [rankingData, userDetails]);\n  const getUserData = async () => {\n    dispatch(ShowLoading());\n    try {\n      const response = await getUserInfo();\n      if (response.success) {\n        setUserDetails(response.data);\n        setFormData({\n          name: response.data.name || \"\",\n          email: response.data.email || \"\",\n          school: response.data.school || \"\",\n          class_: response.data.class || \"\",\n          level: response.data.level || \"\",\n          phoneNumber: response.data.phoneNumber || \"\"\n        });\n        if (response.data.profileImage) {\n          setProfileImage(response.data.profileImage);\n        }\n        fetchReports();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  useEffect(() => {\n    if (localStorage.getItem(\"token\")) {\n      getUserData();\n    }\n  }, []);\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    if (name === \"phoneNumber\" && value.length > 10) return;\n    if (name === \"level\" && value !== (userDetails === null || userDetails === void 0 ? void 0 : userDetails.level) && value !== \"\") {\n      setPendingLevelChange(value);\n      setShowLevelChangeModal(true);\n      return;\n    }\n    setFormData(prev => ({\n      ...prev,\n      [name]: value,\n      ...(name === \"level\" ? {\n        class_: \"\"\n      } : {})\n    }));\n  };\n  const discardChanges = () => {\n    setFormData({\n      name: userDetails.name,\n      email: userDetails.email,\n      school: userDetails.school,\n      class_: userDetails.class,\n      level: userDetails.level,\n      phoneNumber: userDetails.phoneNumber\n    });\n    setEdit(false);\n  };\n  const handleUpdate = async ({\n    skipOTP\n  } = {}) => {\n    console.log('🔍 Current formData:', formData);\n    console.log('🔍 Current userDetails:', userDetails);\n\n    // Validation\n    if (!formData.name || formData.name.trim() === \"\") {\n      console.log('❌ Validation failed: name is empty');\n      return message.error(\"Please enter your name.\");\n    }\n    if (!formData.class_ || formData.class_.trim() === \"\") {\n      console.log('❌ Validation failed: class is empty');\n      return message.error(\"Please select a class.\");\n    }\n    if (!formData.level || formData.level.trim() === \"\") {\n      console.log('❌ Validation failed: level is empty');\n      return message.error(\"Please select a level.\");\n    }\n    // Email validation (optional - only validate if provided)\n    if (formData.email && formData.email.trim() !== \"\") {\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n      if (!emailRegex.test(formData.email)) {\n        return message.error(\"Please enter a valid email address.\");\n      }\n    }\n\n    // Since email is optional in username-based system, skip OTP verification\n    // Users can update their email directly without verification\n\n    dispatch(ShowLoading());\n    try {\n      // Prepare update payload - only include email if it has a value\n      const updatePayload = {\n        ...formData,\n        userId: userDetails._id\n      };\n\n      // Only include email if it's provided and not empty\n      if (formData.email && formData.email.trim() !== \"\") {\n        updatePayload.email = formData.email.trim();\n      } else if (userDetails !== null && userDetails !== void 0 && userDetails.email) {\n        updatePayload.email = userDetails.email;\n      }\n      console.log('📤 Sending update data:', updatePayload);\n      const response = await updateUserInfo(updatePayload);\n      console.log('📥 Server response:', response);\n      if (response.success) {\n        message.success(response.message);\n        setEdit(false);\n        getUserData();\n        if (response.levelChanged) {\n          setTimeout(() => window.location.reload(), 2000);\n        }\n      } else {\n        console.error('❌ Update failed:', response);\n        message.error(response.message || \"Failed to update profile. Please try again.\");\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('❌ Update error:', error);\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || error.message || \"An unexpected error occurred.\";\n      message.error(`Update failed: ${errorMessage}`);\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  const handleLevelChangeConfirm = () => {\n    setFormData(prev => ({\n      ...prev,\n      level: pendingLevelChange,\n      class_: \"\"\n    }));\n    setShowLevelChangeModal(false);\n    setPendingLevelChange(null);\n  };\n  const handleLevelChangeCancel = () => {\n    setShowLevelChangeModal(false);\n    setPendingLevelChange(null);\n  };\n  const handleImageChange = async e => {\n    const file = e.target.files[0];\n    if (file) {\n      // Validate file type\n      if (!file.type.startsWith('image/')) {\n        message.error('Please select a valid image file');\n        return;\n      }\n\n      // Validate file size (max 5MB)\n      if (file.size > 5 * 1024 * 1024) {\n        message.error('Image size should be less than 5MB');\n        return;\n      }\n      setProfileImage(file);\n\n      // Show preview\n      const reader = new FileReader();\n      reader.onloadend = () => setImagePreview(reader.result);\n      reader.readAsDataURL(file);\n\n      // Auto-upload the image\n      const data = new FormData();\n      data.append(\"profileImage\", file);\n      dispatch(ShowLoading());\n      try {\n        const response = await updateUserPhoto(data);\n        dispatch(HideLoading());\n        if (response.success) {\n          message.success(\"Profile picture updated successfully!\");\n          getUserData(); // Refresh user data to show new image\n        } else {\n          message.error(response.message);\n        }\n      } catch (error) {\n        dispatch(HideLoading());\n        message.error(error.message || \"Failed to update profile picture\");\n      }\n    }\n  };\n  const handleImageUpload = async () => {\n    const data = new FormData();\n    data.append(\"profileImage\", profileImage);\n    dispatch(ShowLoading());\n    try {\n      const response = await updateUserPhoto(data);\n      if (response.success) {\n        message.success(\"Photo updated successfully!\");\n        getUserData();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n\n  // Load user data on component mount\n  useEffect(() => {\n    getUserData();\n  }, []);\n\n  // Load ranking data when user details are available\n  useEffect(() => {\n    if (userDetails) {\n      fetchUserRankingData();\n    }\n  }, [userDetails]);\n\n  // Ensure formData is synchronized with userDetails\n  useEffect(() => {\n    if (userDetails) {\n      setFormData({\n        name: userDetails.name || \"\",\n        email: userDetails.email || \"\",\n        // Email is optional\n        school: userDetails.school || \"\",\n        class_: userDetails.class || \"\",\n        level: userDetails.level || \"\",\n        phoneNumber: userDetails.phoneNumber || \"\"\n      });\n    }\n  }, [userDetails]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-4xl font-bold text-gray-900 mb-2\",\n            children: \"Profile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Manage your account settings and preferences\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative mt-8 flex justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(ProfilePicture, {\n                user: userDetails,\n                size: \"3xl\",\n                showOnlineStatus: true,\n                onClick: () => document.getElementById('profileImageInput').click(),\n                className: \"hover:scale-105 transition-transform duration-200\",\n                style: {\n                  width: '120px',\n                  height: '120px',\n                  border: '4px solid #BFDBFE',\n                  boxShadow: '0 4px 12px rgba(0,0,0,0.15)'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute bottom-2 right-2 bg-blue-600 rounded-full p-2 shadow-lg cursor-pointer hover:bg-blue-700 transition-colors duration-200\",\n                onClick: () => document.getElementById('profileImageInput').click(),\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5 text-white\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 382,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M15 13a3 3 0 11-6 0 3 3 0 016 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 383,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"profileImageInput\",\n                type: \"file\",\n                accept: \"image/*\",\n                className: \"hidden\",\n                onChange: handleImageChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl shadow-xl overflow-hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col items-center mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap justify-center gap-4 text-center mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-blue-50 rounded-lg px-4 py-3 border border-blue-200 min-w-[120px]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-blue-600 font-medium\",\n                    children: \"Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 406,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.name) || 'User'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 407,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-green-50 rounded-lg px-4 py-3 border border-green-200 min-w-[120px]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-green-600 font-medium\",\n                    children: \"Username\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 410,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900 truncate max-w-[150px]\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.username) || 'username'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 411,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-purple-50 rounded-lg px-4 py-3 border border-purple-200 min-w-[120px]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-purple-600 font-medium\",\n                    children: \"Class\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.class) || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 415,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 17\n              }, this), userRankingStats && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap justify-center gap-4 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-yellow-50 rounded-lg px-4 py-3 border border-yellow-200 min-w-[120px]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-yellow-600 font-medium\",\n                    children: \"Rank\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 423,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900\",\n                    children: [\"#\", userRankingStats.userRank || 'N/A', userRankingStats.totalUsers && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm text-gray-500\",\n                      children: [\"/\", userRankingStats.totalUsers]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 427,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 424,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 422,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-orange-50 rounded-lg px-4 py-3 border border-orange-200 min-w-[120px]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-orange-600 font-medium\",\n                    children: \"Total XP\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 432,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900\",\n                    children: ((_userRankingStats$use = userRankingStats.user) === null || _userRankingStats$use === void 0 ? void 0 : (_userRankingStats$use2 = _userRankingStats$use.totalXP) === null || _userRankingStats$use2 === void 0 ? void 0 : _userRankingStats$use2.toLocaleString()) || '0'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 433,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 431,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-indigo-50 rounded-lg px-4 py-3 border border-indigo-200 min-w-[120px]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-indigo-600 font-medium\",\n                    children: \"Avg Score\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 438,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900\",\n                    children: [((_userRankingStats$use3 = userRankingStats.user) === null || _userRankingStats$use3 === void 0 ? void 0 : _userRankingStats$use3.averageScore) || '0', \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 439,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-pink-50 rounded-lg px-4 py-3 border border-pink-200 min-w-[120px]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-pink-600 font-medium\",\n                    children: \"Quizzes\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 444,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900\",\n                    children: ((_userRankingStats$use4 = userRankingStats.user) === null || _userRankingStats$use4 === void 0 ? void 0 : _userRankingStats$use4.totalQuizzesTaken) || '0'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 445,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-teal-50 rounded-lg px-4 py-3 border border-teal-200 min-w-[120px]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-teal-600 font-medium\",\n                    children: \"Streak\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-bold text-gray-900\",\n                    children: ((_userRankingStats$use5 = userRankingStats.user) === null || _userRankingStats$use5 === void 0 ? void 0 : _userRankingStats$use5.currentStreak) || '0'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 451,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 15\n            }, this), !edit ?\n            /*#__PURE__*/\n            // View Mode\n            _jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 465,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gray-50 rounded-lg border\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.name) || 'Not provided'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 466,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 464,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Username\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 471,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gray-50 rounded-lg border\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.username) || 'Not provided'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 472,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 470,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 477,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gray-50 rounded-lg border\",\n                    children: userDetails !== null && userDetails !== void 0 && userDetails.email ? /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-between\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        children: userDetails.email\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 481,\n                        columnNumber: 29\n                      }, this), userDetails.email.includes('@brainwave.temp') && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full\",\n                        children: \"Auto-generated\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 483,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 480,\n                      columnNumber: 27\n                    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-between\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-gray-500\",\n                        children: \"No email set\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 490,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: async () => {\n                          const timestamp = Date.now();\n                          const autoEmail = `${userDetails.username}.${timestamp}@brainwave.temp`;\n                          setFormData(prev => ({\n                            ...prev,\n                            email: autoEmail\n                          }));\n                          message.info('Auto-generated email created. Click \"Save Changes\" to update.');\n                        },\n                        className: \"text-xs bg-green-100 text-green-600 px-2 py-1 rounded-full hover:bg-green-200 transition-colors\",\n                        children: \"Generate Email\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 491,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 489,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 478,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 476,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"School\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 507,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gray-50 rounded-lg border\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.school) || 'Not provided'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 508,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 506,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Level\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 515,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gray-50 rounded-lg border\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.level) || 'Not provided'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 516,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 514,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Class\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 521,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gray-50 rounded-lg border\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.class) || 'Not provided'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 522,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 520,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Phone Number\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 527,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gray-50 rounded-lg border\",\n                    children: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.phoneNumber) || 'Not provided'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 528,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 17\n            }, this) :\n            /*#__PURE__*/\n            // Edit Mode\n            _jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Name *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 539,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"name\",\n                    value: formData.name,\n                    onChange: handleChange,\n                    className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                    placeholder: \"Enter your name\",\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 540,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 538,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Username\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 551,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-gray-100 rounded-lg border text-gray-600\",\n                    children: [(userDetails === null || userDetails === void 0 ? void 0 : userDetails.username) || 'Not available', /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs text-gray-500 block mt-1\",\n                      children: \"Username cannot be changed\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 554,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 552,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 550,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Email (Optional)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 558,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative\",\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"email\",\n                      name: \"email\",\n                      value: formData.email || \"\",\n                      onChange: handleChange,\n                      className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors pr-24\",\n                      placeholder: \"Enter your email (optional)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 560,\n                      columnNumber: 25\n                    }, this), (!formData.email || formData.email === '') && /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      onClick: () => {\n                        const timestamp = Date.now();\n                        const autoEmail = `${userDetails.username}.${timestamp}@brainwave.temp`;\n                        setFormData(prev => ({\n                          ...prev,\n                          email: autoEmail\n                        }));\n                        message.success('Auto-generated email created!');\n                      },\n                      className: \"absolute right-2 top-1/2 transform -translate-y-1/2 text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200 transition-colors\",\n                      children: \"Auto-Gen\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 569,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 559,\n                    columnNumber: 23\n                  }, this), formData.email && formData.email.includes('@brainwave.temp') && /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-blue-600 mt-1\",\n                    children: \"\\uD83D\\uDCE7 This is an auto-generated email. You can change it to your real email if you prefer.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 584,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 557,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"School\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 590,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    name: \"school\",\n                    value: formData.school,\n                    onChange: handleChange,\n                    className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                    placeholder: \"Enter your school\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 591,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 589,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Level *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 603,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    name: \"level\",\n                    value: formData.level,\n                    onChange: handleChange,\n                    className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                    required: true,\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Select Level\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 611,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Primary\",\n                      children: \"Primary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 612,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Secondary\",\n                      children: \"Secondary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 613,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"Advance\",\n                      children: \"Advance\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 614,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 604,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 602,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Class *\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 618,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    name: \"class_\",\n                    value: formData.class_,\n                    onChange: handleChange,\n                    className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                    required: true,\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Select Class\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 626,\n                      columnNumber: 25\n                    }, this), formData.level === \"Primary\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"1\",\n                        children: \"1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 629,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"2\",\n                        children: \"2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 630,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"3\",\n                        children: \"3\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 631,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"4\",\n                        children: \"4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 632,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"5\",\n                        children: \"5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 633,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"6\",\n                        children: \"6\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 634,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"7\",\n                        children: \"7\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 635,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true), formData.level === \"Secondary\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"1\",\n                        children: \"1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 640,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"2\",\n                        children: \"2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 641,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"3\",\n                        children: \"3\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 642,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"4\",\n                        children: \"4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 643,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true), formData.level === \"Advance\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"5\",\n                        children: \"5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 648,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"6\",\n                        children: \"6\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 649,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 619,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 617,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                    children: \"Phone Number\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 655,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"tel\",\n                    name: \"phoneNumber\",\n                    value: formData.phoneNumber,\n                    onChange: handleChange,\n                    className: \"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                    placeholder: \"Enter phone number\",\n                    maxLength: \"10\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 656,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 654,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 601,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl border border-blue-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-bold text-gray-900 mb-4 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"mr-2\",\n                  children: \"\\uD83D\\uDC8E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 673,\n                  columnNumber: 19\n                }, this), \"Subscription Plan\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 672,\n                columnNumber: 17\n              }, this), subscriptionData && subscriptionData.status === 'active' ?\n              /*#__PURE__*/\n              // Active Subscription\n              _jsxDEV(\"div\", {\n                className: \"space-y-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"text-xl font-bold text-blue-700\",\n                      children: subscriptionData.planTitle || ((_subscriptionData$pla = subscriptionData.plan) === null || _subscriptionData$pla === void 0 ? void 0 : _subscriptionData$pla.title) || 'Premium Plan'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 683,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-600 mt-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"inline-flex items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 686,\n                          columnNumber: 29\n                        }, this), \"Active Subscription\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 685,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 684,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 682,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-right\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-green-100 text-green-800 border border-green-200\",\n                      children: \"\\u2705 Active\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 692,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 691,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 681,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gradient-to-r from-blue-50 to-green-50 p-4 rounded-xl border border-blue-200\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"text-sm font-semibold text-gray-700 mb-3 flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"mr-2\",\n                      children: \"\\uD83D\\uDCC5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 701,\n                      columnNumber: 25\n                    }, this), \"Subscription Timeline\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 700,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-gray-500 uppercase tracking-wide\",\n                        children: \"Started On\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 706,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-semibold text-gray-900\",\n                        children: subscriptionData.startDate ? new Date(subscriptionData.startDate).toLocaleDateString('en-US', {\n                          weekday: 'short',\n                          year: 'numeric',\n                          month: 'short',\n                          day: 'numeric'\n                        }) : new Date(subscriptionData.createdAt).toLocaleDateString('en-US', {\n                          weekday: 'short',\n                          year: 'numeric',\n                          month: 'short',\n                          day: 'numeric'\n                        })\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 707,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 705,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-gray-500 uppercase tracking-wide\",\n                        children: \"Expires On\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 725,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-semibold text-red-600\",\n                        children: new Date(subscriptionData.endDate).toLocaleDateString('en-US', {\n                          weekday: 'short',\n                          year: 'numeric',\n                          month: 'short',\n                          day: 'numeric'\n                        })\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 726,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 724,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 704,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 699,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white p-4 rounded-xl border border-gray-200 shadow-sm\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-between\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-xs text-gray-500 uppercase tracking-wide\",\n                          children: \"Total Duration\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 743,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-lg font-bold text-gray-900\",\n                          children: [subscriptionData.duration || ((_subscriptionData$pla2 = subscriptionData.plan) === null || _subscriptionData$pla2 === void 0 ? void 0 : _subscriptionData$pla2.duration) || 1, \" month\", (subscriptionData.duration || ((_subscriptionData$pla3 = subscriptionData.plan) === null || _subscriptionData$pla3 === void 0 ? void 0 : _subscriptionData$pla3.duration) || 1) > 1 ? 's' : '']\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 744,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 742,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-blue-500\",\n                        children: /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-2xl\",\n                          children: \"\\uD83D\\uDCC6\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 749,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 748,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 741,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 740,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white p-4 rounded-xl border border-gray-200 shadow-sm\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-between\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-xs text-gray-500 uppercase tracking-wide\",\n                          children: \"Amount Paid\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 757,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-lg font-bold text-green-600\",\n                          children: [(subscriptionData.amount || subscriptionData.discountedPrice || 0).toLocaleString(), \" TZS\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 758,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 756,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-green-500\",\n                        children: /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-2xl\",\n                          children: \"\\uD83D\\uDCB0\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 763,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 762,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 755,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 754,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white p-4 rounded-xl border border-gray-200 shadow-sm\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-between\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-xs text-gray-500 uppercase tracking-wide\",\n                          children: \"Days Remaining\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 771,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-lg font-bold text-orange-600\",\n                          children: [(() => {\n                            const daysLeft = Math.max(0, Math.ceil((new Date(subscriptionData.endDate) - new Date()) / (1000 * 60 * 60 * 24)));\n                            return daysLeft;\n                          })(), \" days\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 772,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 770,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-orange-500\",\n                        children: /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-2xl\",\n                          children: \"\\u23F0\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 780,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 779,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 769,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 768,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white p-4 rounded-xl border border-gray-200 shadow-sm\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-between\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-xs text-gray-500 uppercase tracking-wide\",\n                          children: \"Total Days\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 788,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-lg font-bold text-purple-600\",\n                          children: [(subscriptionData.duration || ((_subscriptionData$pla4 = subscriptionData.plan) === null || _subscriptionData$pla4 === void 0 ? void 0 : _subscriptionData$pla4.duration) || 1) * 30, \" days\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 789,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 787,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-purple-500\",\n                        children: /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-2xl\",\n                          children: \"\\uD83D\\uDCCA\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 794,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 793,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 786,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 785,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 739,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white p-4 rounded-xl border border-gray-200\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-semibold text-gray-700\",\n                      children: \"Subscription Progress\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 803,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-500\",\n                      children: (_subscriptionData$pla5 => {\n                        const duration = subscriptionData.duration || ((_subscriptionData$pla5 = subscriptionData.plan) === null || _subscriptionData$pla5 === void 0 ? void 0 : _subscriptionData$pla5.duration) || 1;\n                        const totalDays = duration * 30;\n                        const daysLeft = Math.max(0, Math.ceil((new Date(subscriptionData.endDate) - new Date()) / (1000 * 60 * 60 * 24)));\n                        const daysUsed = totalDays - daysLeft;\n                        const percentage = Math.min(100, Math.max(0, daysUsed / totalDays * 100));\n                        return `${percentage.toFixed(1)}% completed`;\n                      })()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 804,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 802,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full bg-gray-200 rounded-full h-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-gradient-to-r from-blue-500 to-green-500 h-3 rounded-full transition-all duration-500\",\n                      style: {\n                        width: `${(_subscriptionData$pla6 => {\n                          const duration = subscriptionData.duration || ((_subscriptionData$pla6 = subscriptionData.plan) === null || _subscriptionData$pla6 === void 0 ? void 0 : _subscriptionData$pla6.duration) || 1;\n                          const totalDays = duration * 30;\n                          const daysLeft = Math.max(0, Math.ceil((new Date(subscriptionData.endDate) - new Date()) / (1000 * 60 * 60 * 24)));\n                          const daysUsed = totalDays - daysLeft;\n                          const percentage = Math.min(100, Math.max(0, daysUsed / totalDays * 100));\n                          return percentage;\n                        })()}%`\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 816,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 815,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between text-xs text-gray-500 mt-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Started\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 831,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: (() => {\n                        const daysLeft = Math.max(0, Math.ceil((new Date(subscriptionData.endDate) - new Date()) / (1000 * 60 * 60 * 24)));\n                        if (daysLeft > 7) {\n                          return `${daysLeft} days left`;\n                        } else if (daysLeft > 0) {\n                          return `⚠️ ${daysLeft} days left`;\n                        } else {\n                          return '❌ Expired';\n                        }\n                      })()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 832,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 830,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 801,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex gap-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setShowSubscriptionModal(true),\n                    className: \"px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all font-medium shadow-md\",\n                    children: \"\\uD83D\\uDE80 Upgrade Plan\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 849,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      const endDate = new Date(subscriptionData.endDate);\n                      const today = new Date();\n                      const daysLeft = Math.ceil((endDate - today) / (1000 * 60 * 60 * 24));\n                      if (daysLeft <= 7 && daysLeft > 0) {\n                        message.warning(`Your subscription expires in ${daysLeft} days. Consider renewing soon!`);\n                      } else if (daysLeft <= 0) {\n                        message.error('Your subscription has expired. Please renew to continue accessing premium features.');\n                      } else {\n                        message.info(`Your subscription is active for ${daysLeft} more days.`);\n                      }\n                    },\n                    className: \"px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium\",\n                    children: \"\\uD83D\\uDCCA Check Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 855,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 848,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 679,\n                columnNumber: 19\n              }, this) :\n              /*#__PURE__*/\n              // No Active Subscription\n              _jsxDEV(\"div\", {\n                className: \"text-center py-8\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-20 h-20 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-4xl\",\n                      children: \"\\uD83D\\uDD12\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 880,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 879,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-xl font-bold text-gray-900 mb-2\",\n                    children: \"No Active Subscription\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 882,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-600 mb-6 max-w-md mx-auto\",\n                    children: \"Unlock premium features and get unlimited access to all educational content with a subscription plan.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 883,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 878,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6 max-w-2xl mx-auto\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gradient-to-br from-blue-50 to-indigo-50 p-4 rounded-lg border border-blue-200\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-blue-600 text-2xl mb-2\",\n                      children: \"\\uD83D\\uDCDA\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 891,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"font-semibold text-gray-900 mb-1\",\n                      children: \"Unlimited Quizzes\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 892,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600\",\n                      children: \"Access all quizzes without restrictions\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 893,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 890,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gradient-to-br from-green-50 to-emerald-50 p-4 rounded-lg border border-green-200\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-green-600 text-2xl mb-2\",\n                      children: \"\\uD83E\\uDD16\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 896,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"font-semibold text-gray-900 mb-1\",\n                      children: \"AI Study Assistant\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 897,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600\",\n                      children: \"Get instant help with your studies\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 898,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 895,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gradient-to-br from-purple-50 to-violet-50 p-4 rounded-lg border border-purple-200\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-purple-600 text-2xl mb-2\",\n                      children: \"\\uD83D\\uDCCA\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 901,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"font-semibold text-gray-900 mb-1\",\n                      children: \"Progress Tracking\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 902,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600\",\n                      children: \"Monitor your learning progress\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 903,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 900,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gradient-to-br from-orange-50 to-amber-50 p-4 rounded-lg border border-orange-200\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-orange-600 text-2xl mb-2\",\n                      children: \"\\uD83C\\uDFC6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 906,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"font-semibold text-gray-900 mb-1\",\n                      children: \"Rankings & Badges\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 907,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600\",\n                      children: \"Compete and earn achievements\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 908,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 905,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 889,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setShowSubscriptionModal(true),\n                    className: \"px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all font-semibold text-lg shadow-lg hover:shadow-xl transform hover:scale-105\",\n                    children: \"\\uD83D\\uDE80 Choose Subscription Plan\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 914,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500\",\n                    children: \"Plans start from as low as 13,000 TZS per month\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 920,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 913,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 877,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 671,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-8 flex justify-center gap-4\",\n              children: !edit ? /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  // Ensure formData is properly initialized with current user data\n                  setFormData({\n                    name: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.name) || \"\",\n                    email: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.email) || \"\",\n                    school: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.school) || \"\",\n                    class_: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.class) || \"\",\n                    level: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.level) || \"\",\n                    phoneNumber: (userDetails === null || userDetails === void 0 ? void 0 : userDetails.phoneNumber) || \"\"\n                  });\n                  setEdit(true);\n                },\n                className: \"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium\",\n                children: \"Edit Profile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 931,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: discardChanges,\n                  className: \"px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-200 font-medium\",\n                  children: \"Cancel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 950,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleUpdate,\n                  className: \"px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 font-medium\",\n                  children: \"Save Changes\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 956,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    console.log('🔍 Debug - Current formData:', formData);\n                    console.log('🔍 Debug - Current userDetails:', userDetails);\n                    alert(`FormData: ${JSON.stringify(formData, null, 2)}`);\n                  },\n                  className: \"px-4 py-2 bg-gray-400 text-white rounded-lg hover:bg-gray-500 transition-colors duration-200 font-medium text-sm\",\n                  children: \"Debug\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 963,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 929,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 355,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 354,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      type: \"file\",\n      id: \"profileImageInput\",\n      accept: \"image/*\",\n      onChange: handleImageChange,\n      style: {\n        display: 'none'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 982,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"Confirm Level Change\",\n      open: showLevelChangeModal,\n      onOk: handleLevelChangeConfirm,\n      onCancel: () => {\n        setShowLevelChangeModal(false);\n        setPendingLevelChange(null);\n      },\n      okText: \"Confirm\",\n      cancelText: \"Cancel\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Are you sure you want to change your level to \", /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: pendingLevelChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1003,\n          columnNumber: 57\n        }, this), \"?\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1002,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-orange-600 text-sm mt-2\",\n        children: \"Note: Changing your level will reset your class selection and you'll only have access to content for the new level.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1005,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 991,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SubscriptionModal, {\n      isOpen: showSubscriptionModal,\n      onClose: () => setShowSubscriptionModal(false),\n      onSuccess: () => {\n        setShowSubscriptionModal(false);\n        // Refresh user data to show updated subscription\n        getUserData();\n        message.success('Subscription updated successfully!');\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1011,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 353,\n    columnNumber: 5\n  }, this);\n};\n_s(Profile, \"K6wOWmPzQJromzUcVfhkHABnG3Q=\", false, function () {\n  return [useLanguage, useDispatch, useSelector];\n});\n_c = Profile;\nexport default Profile;\nvar _c;\n$RefreshReg$(_c, \"Profile\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Page<PERSON><PERSON>le", "getUserInfo", "updateUserInfo", "updateUserPhoto", "Form", "message", "Modal", "Input", "<PERSON><PERSON>", "useDispatch", "useSelector", "HideLoading", "ShowLoading", "getAllReportsForRanking", "getUserRanking", "getXPLeaderboard", "ProfilePicture", "SubscriptionModal", "useLanguage", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Profile", "_s", "_userRankingStats$use", "_userRankingStats$use2", "_userRankingStats$use3", "_userRankingStats$use4", "_userRankingStats$use5", "_subscriptionData$pla", "_subscriptionData$pla2", "_subscriptionData$pla3", "_subscriptionData$pla4", "t", "isKiswahili", "getClassName", "userDetails", "setUserDetails", "rankingData", "setRankingData", "userRanking", "setUserRanking", "userRankingStats", "setUserRankingStats", "edit", "setEdit", "imagePreview", "setImagePreview", "formData", "setFormData", "name", "email", "school", "level", "class_", "phoneNumber", "profileImage", "setProfileImage", "showSubscriptionModal", "setShowSubscriptionModal", "showLevelChangeModal", "setShowLevelChangeModal", "pendingLevelChange", "setPendingLevelChange", "dispatch", "subscriptionData", "state", "subscription", "fetchReports", "response", "success", "data", "error", "getUserStats", "Ranking", "map", "user", "index", "ranking", "filter", "item", "userId", "includes", "_id", "fetchUserRankingData", "rankingResponse", "leaderboardResponse", "limit", "levelFilter", "userIndex", "findIndex", "userWithRank", "rank", "totalUsers", "length", "prev", "userRank", "console", "getUserData", "class", "localStorage", "getItem", "handleChange", "e", "value", "target", "discardChanges", "handleUpdate", "skipOTP", "log", "trim", "emailRegex", "test", "updatePayload", "levelChanged", "setTimeout", "window", "location", "reload", "_error$response", "_error$response$data", "errorMessage", "handleLevelChangeConfirm", "handleLevelChangeCancel", "handleImageChange", "file", "files", "type", "startsWith", "size", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "FormData", "append", "handleImageUpload", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "showOnlineStatus", "onClick", "document", "getElementById", "click", "style", "width", "height", "border", "boxShadow", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "id", "accept", "onChange", "username", "totalXP", "toLocaleString", "averageScore", "totalQuizzesTaken", "currentStreak", "timestamp", "Date", "now", "autoEmail", "info", "placeholder", "required", "max<PERSON><PERSON><PERSON>", "status", "planTitle", "plan", "title", "startDate", "toLocaleDateString", "weekday", "year", "month", "day", "createdAt", "endDate", "duration", "amount", "discountedPrice", "daysLeft", "Math", "max", "ceil", "_subscriptionData$pla5", "totalDays", "daysUsed", "percentage", "min", "toFixed", "_subscriptionData$pla6", "today", "warning", "alert", "JSON", "stringify", "display", "open", "onOk", "onCancel", "okText", "cancelText", "isOpen", "onClose", "onSuccess", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Profile/index.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport \"./index.css\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport {\r\n  getUserInfo,\r\n  updateUserInfo,\r\n  updateUserPhoto,\r\n} from \"../../../apicalls/users\";\r\nimport { Form, message, Modal, Input, Button } from \"antd\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { getAllReportsForRanking, getUserRanking, getXPLeaderboard } from \"../../../apicalls/reports\";\r\nimport ProfilePicture from \"../../../components/common/ProfilePicture\";\r\nimport SubscriptionModal from \"../../../components/SubscriptionModal/SubscriptionModal\";\r\nimport { useLanguage } from \"../../../contexts/LanguageContext\";\r\n\r\nconst Profile = () => {\r\n  const { t, isKiswa<PERSON><PERSON>, getClassName } = useLanguage();\r\n  const [userDetails, setUserDetails] = useState(null);\r\n  const [rankingData, setRankingData] = useState(null);\r\n  const [userRanking, setUserRanking] = useState(null);\r\n  const [userRankingStats, setUserRankingStats] = useState(null);\r\n  const [edit, setEdit] = useState(false);\r\n  const [imagePreview, setImagePreview] = useState(null);\r\n  const [formData, setFormData] = useState({\r\n    name: \"\",\r\n    email: \"\",\r\n    school: \"\",\r\n    level: \"\",\r\n    class_: \"\",\r\n    phoneNumber: \"\",\r\n  });\r\n  const [profileImage, setProfileImage] = useState(null);\r\n  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false);\r\n  const [showLevelChangeModal, setShowLevelChangeModal] = useState(false);\r\n  const [pendingLevelChange, setPendingLevelChange] = useState(null);\r\n  const dispatch = useDispatch();\r\n  const { subscriptionData } = useSelector((state) => state.subscription);\r\n\r\n  const fetchReports = async () => {\r\n    try {\r\n      const response = await getAllReportsForRanking();\r\n      if (response.success) {\r\n        setRankingData(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      message.error(error.message);\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  const getUserStats = () => {\r\n    const Ranking = rankingData\r\n      .map((user, index) => ({\r\n        user,\r\n        ranking: index + 1,\r\n      }))\r\n      .filter((item) => item.user.userId.includes(userDetails._id));\r\n    setUserRanking(Ranking);\r\n  };\r\n\r\n  // Fetch user ranking data from the ranking system\r\n  const fetchUserRankingData = async () => {\r\n    if (!userDetails?._id) return;\r\n\r\n    try {\r\n      dispatch(ShowLoading());\r\n\r\n      // Get user's ranking position and nearby users\r\n      const rankingResponse = await getUserRanking(userDetails._id, 5);\r\n\r\n      if (rankingResponse.success) {\r\n        setUserRankingStats(rankingResponse.data);\r\n      }\r\n\r\n      // Also get the full leaderboard to find user's position\r\n      const leaderboardResponse = await getXPLeaderboard({\r\n        limit: 1000,\r\n        levelFilter: userDetails?.level || 'all'\r\n      });\r\n\r\n      if (leaderboardResponse.success) {\r\n        const userIndex = leaderboardResponse.data.findIndex(user => user._id === userDetails._id);\r\n        if (userIndex >= 0) {\r\n          const userWithRank = {\r\n            ...leaderboardResponse.data[userIndex],\r\n            rank: userIndex + 1,\r\n            totalUsers: leaderboardResponse.data.length\r\n          };\r\n          setUserRankingStats(prev => ({\r\n            ...prev,\r\n            userRank: userIndex + 1,\r\n            totalUsers: leaderboardResponse.data.length,\r\n            user: userWithRank\r\n          }));\r\n        }\r\n      }\r\n\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      console.error('Error fetching ranking data:', error);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (rankingData && userDetails) {\r\n      getUserStats();\r\n    }\r\n  }, [rankingData, userDetails]);\r\n\r\n  const getUserData = async () => {\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await getUserInfo();\r\n      if (response.success) {\r\n        setUserDetails(response.data);\r\n        setFormData({\r\n          name: response.data.name || \"\",\r\n          email: response.data.email || \"\",\r\n          school: response.data.school || \"\",\r\n          class_: response.data.class || \"\",\r\n          level: response.data.level || \"\",\r\n          phoneNumber: response.data.phoneNumber || \"\",\r\n        });\r\n        if (response.data.profileImage) {\r\n          setProfileImage(response.data.profileImage);\r\n        }\r\n        fetchReports();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (localStorage.getItem(\"token\")) {\r\n      getUserData();\r\n    }\r\n  }, []);\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    if (name === \"phoneNumber\" && value.length > 10) return;\r\n    if (name === \"level\" && value !== userDetails?.level && value !== \"\") {\r\n      setPendingLevelChange(value);\r\n      setShowLevelChangeModal(true);\r\n      return;\r\n    }\r\n    setFormData((prev) => ({\r\n      ...prev,\r\n      [name]: value,\r\n      ...(name === \"level\" ? { class_: \"\" } : {}),\r\n    }));\r\n  };\r\n\r\n  const discardChanges = () => {\r\n    setFormData({\r\n      name: userDetails.name,\r\n      email: userDetails.email,\r\n      school: userDetails.school,\r\n      class_: userDetails.class,\r\n      level: userDetails.level,\r\n      phoneNumber: userDetails.phoneNumber,\r\n    });\r\n    setEdit(false);\r\n  };\r\n\r\n\r\n\r\n  const handleUpdate = async ({ skipOTP } = {}) => {\r\n    console.log('🔍 Current formData:', formData);\r\n    console.log('🔍 Current userDetails:', userDetails);\r\n\r\n    // Validation\r\n    if (!formData.name || formData.name.trim() === \"\") {\r\n      console.log('❌ Validation failed: name is empty');\r\n      return message.error(\"Please enter your name.\");\r\n    }\r\n    if (!formData.class_ || formData.class_.trim() === \"\") {\r\n      console.log('❌ Validation failed: class is empty');\r\n      return message.error(\"Please select a class.\");\r\n    }\r\n    if (!formData.level || formData.level.trim() === \"\") {\r\n      console.log('❌ Validation failed: level is empty');\r\n      return message.error(\"Please select a level.\");\r\n    }\r\n    // Email validation (optional - only validate if provided)\r\n    if (formData.email && formData.email.trim() !== \"\") {\r\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n      if (!emailRegex.test(formData.email)) {\r\n        return message.error(\"Please enter a valid email address.\");\r\n      }\r\n    }\r\n\r\n    // Since email is optional in username-based system, skip OTP verification\r\n    // Users can update their email directly without verification\r\n\r\n    dispatch(ShowLoading());\r\n    try {\r\n      // Prepare update payload - only include email if it has a value\r\n      const updatePayload = {\r\n        ...formData,\r\n        userId: userDetails._id,\r\n      };\r\n\r\n      // Only include email if it's provided and not empty\r\n      if (formData.email && formData.email.trim() !== \"\") {\r\n        updatePayload.email = formData.email.trim();\r\n      } else if (userDetails?.email) {\r\n        updatePayload.email = userDetails.email;\r\n      }\r\n\r\n      console.log('📤 Sending update data:', updatePayload);\r\n\r\n      const response = await updateUserInfo(updatePayload);\r\n\r\n      console.log('📥 Server response:', response);\r\n\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        setEdit(false);\r\n        getUserData();\r\n        if (response.levelChanged) {\r\n          setTimeout(() => window.location.reload(), 2000);\r\n        }\r\n      } else {\r\n        console.error('❌ Update failed:', response);\r\n        message.error(response.message || \"Failed to update profile. Please try again.\");\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Update error:', error);\r\n      const errorMessage = error.response?.data?.message || error.message || \"An unexpected error occurred.\";\r\n      message.error(`Update failed: ${errorMessage}`);\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  const handleLevelChangeConfirm = () => {\r\n    setFormData((prev) => ({\r\n      ...prev,\r\n      level: pendingLevelChange,\r\n      class_: \"\",\r\n    }));\r\n    setShowLevelChangeModal(false);\r\n    setPendingLevelChange(null);\r\n  };\r\n\r\n  const handleLevelChangeCancel = () => {\r\n    setShowLevelChangeModal(false);\r\n    setPendingLevelChange(null);\r\n  };\r\n\r\n  const handleImageChange = async (e) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      // Validate file type\r\n      if (!file.type.startsWith('image/')) {\r\n        message.error('Please select a valid image file');\r\n        return;\r\n      }\r\n\r\n      // Validate file size (max 5MB)\r\n      if (file.size > 5 * 1024 * 1024) {\r\n        message.error('Image size should be less than 5MB');\r\n        return;\r\n      }\r\n\r\n      setProfileImage(file);\r\n\r\n      // Show preview\r\n      const reader = new FileReader();\r\n      reader.onloadend = () => setImagePreview(reader.result);\r\n      reader.readAsDataURL(file);\r\n\r\n      // Auto-upload the image\r\n      const data = new FormData();\r\n      data.append(\"profileImage\", file);\r\n      dispatch(ShowLoading());\r\n\r\n      try {\r\n        const response = await updateUserPhoto(data);\r\n        dispatch(HideLoading());\r\n        if (response.success) {\r\n          message.success(\"Profile picture updated successfully!\");\r\n          getUserData(); // Refresh user data to show new image\r\n        } else {\r\n          message.error(response.message);\r\n        }\r\n      } catch (error) {\r\n        dispatch(HideLoading());\r\n        message.error(error.message || \"Failed to update profile picture\");\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleImageUpload = async () => {\r\n    const data = new FormData();\r\n    data.append(\"profileImage\", profileImage);\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await updateUserPhoto(data);\r\n      if (response.success) {\r\n        message.success(\"Photo updated successfully!\");\r\n        getUserData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n\r\n\r\n  // Load user data on component mount\r\n  useEffect(() => {\r\n    getUserData();\r\n  }, []);\r\n\r\n  // Load ranking data when user details are available\r\n  useEffect(() => {\r\n    if (userDetails) {\r\n      fetchUserRankingData();\r\n    }\r\n  }, [userDetails]);\r\n\r\n  // Ensure formData is synchronized with userDetails\r\n  useEffect(() => {\r\n    if (userDetails) {\r\n      setFormData({\r\n        name: userDetails.name || \"\",\r\n        email: userDetails.email || \"\", // Email is optional\r\n        school: userDetails.school || \"\",\r\n        class_: userDetails.class || \"\",\r\n        level: userDetails.level || \"\",\r\n        phoneNumber: userDetails.phoneNumber || \"\",\r\n      });\r\n    }\r\n  }, [userDetails]);\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\r\n      <div className=\"container mx-auto px-4 py-8\">\r\n        <div className=\"max-w-4xl mx-auto\">\r\n          {/* Header */}\r\n          <div className=\"text-center mb-8\">\r\n            <h1 className=\"text-4xl font-bold text-gray-900 mb-2\">Profile</h1>\r\n            <p className=\"text-gray-600\">Manage your account settings and preferences</p>\r\n\r\n            {/* Profile Picture with Online Status - Centered Below Header */}\r\n            <div className=\"relative mt-8 flex justify-center\">\r\n              <div className=\"relative\">\r\n                <ProfilePicture\r\n                  user={userDetails}\r\n                  size=\"3xl\"\r\n                  showOnlineStatus={true}\r\n                  onClick={() => document.getElementById('profileImageInput').click()}\r\n                  className=\"hover:scale-105 transition-transform duration-200\"\r\n                  style={{\r\n                    width: '120px',\r\n                    height: '120px',\r\n                    border: '4px solid #BFDBFE',\r\n                    boxShadow: '0 4px 12px rgba(0,0,0,0.15)'\r\n                  }}\r\n                />\r\n\r\n                {/* Camera Icon Overlay */}\r\n                <div className=\"absolute bottom-2 right-2 bg-blue-600 rounded-full p-2 shadow-lg cursor-pointer hover:bg-blue-700 transition-colors duration-200\"\r\n                     onClick={() => document.getElementById('profileImageInput').click()}>\r\n                  <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z\" />\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 13a3 3 0 11-6 0 3 3 0 016 0z\" />\r\n                  </svg>\r\n                </div>\r\n\r\n                {/* Hidden File Input */}\r\n                <input\r\n                  id=\"profileImageInput\"\r\n                  type=\"file\"\r\n                  accept=\"image/*\"\r\n                  className=\"hidden\"\r\n                  onChange={handleImageChange}\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Profile Content */}\r\n          <div className=\"bg-white rounded-2xl shadow-xl overflow-hidden\">\r\n            <div className=\"p-8\">\r\n              <div className=\"flex flex-col items-center mb-8\">\r\n                {/* User Info - Horizontal Layout */}\r\n                <div className=\"flex flex-wrap justify-center gap-4 text-center mb-6\">\r\n                  <div className=\"bg-blue-50 rounded-lg px-4 py-3 border border-blue-200 min-w-[120px]\">\r\n                    <p className=\"text-sm text-blue-600 font-medium\">Name</p>\r\n                    <p className=\"text-lg font-bold text-gray-900\">{userDetails?.name || 'User'}</p>\r\n                  </div>\r\n                  <div className=\"bg-green-50 rounded-lg px-4 py-3 border border-green-200 min-w-[120px]\">\r\n                    <p className=\"text-sm text-green-600 font-medium\">Username</p>\r\n                    <p className=\"text-lg font-bold text-gray-900 truncate max-w-[150px]\">{userDetails?.username || 'username'}</p>\r\n                  </div>\r\n                  <div className=\"bg-purple-50 rounded-lg px-4 py-3 border border-purple-200 min-w-[120px]\">\r\n                    <p className=\"text-sm text-purple-600 font-medium\">Class</p>\r\n                    <p className=\"text-lg font-bold text-gray-900\">{userDetails?.class || 'N/A'}</p>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Ranking Stats - Horizontal Layout */}\r\n                {userRankingStats && (\r\n                  <div className=\"flex flex-wrap justify-center gap-4 text-center\">\r\n                    <div className=\"bg-yellow-50 rounded-lg px-4 py-3 border border-yellow-200 min-w-[120px]\">\r\n                      <p className=\"text-sm text-yellow-600 font-medium\">Rank</p>\r\n                      <p className=\"text-lg font-bold text-gray-900\">\r\n                        #{userRankingStats.userRank || 'N/A'}\r\n                        {userRankingStats.totalUsers && (\r\n                          <span className=\"text-sm text-gray-500\">/{userRankingStats.totalUsers}</span>\r\n                        )}\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"bg-orange-50 rounded-lg px-4 py-3 border border-orange-200 min-w-[120px]\">\r\n                      <p className=\"text-sm text-orange-600 font-medium\">Total XP</p>\r\n                      <p className=\"text-lg font-bold text-gray-900\">\r\n                        {userRankingStats.user?.totalXP?.toLocaleString() || '0'}\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"bg-indigo-50 rounded-lg px-4 py-3 border border-indigo-200 min-w-[120px]\">\r\n                      <p className=\"text-sm text-indigo-600 font-medium\">Avg Score</p>\r\n                      <p className=\"text-lg font-bold text-gray-900\">\r\n                        {userRankingStats.user?.averageScore || '0'}%\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"bg-pink-50 rounded-lg px-4 py-3 border border-pink-200 min-w-[120px]\">\r\n                      <p className=\"text-sm text-pink-600 font-medium\">Quizzes</p>\r\n                      <p className=\"text-lg font-bold text-gray-900\">\r\n                        {userRankingStats.user?.totalQuizzesTaken || '0'}\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"bg-teal-50 rounded-lg px-4 py-3 border border-teal-200 min-w-[120px]\">\r\n                      <p className=\"text-sm text-teal-600 font-medium\">Streak</p>\r\n                      <p className=\"text-lg font-bold text-gray-900\">\r\n                        {userRankingStats.user?.currentStreak || '0'}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              {/* Profile Details */}\r\n              {!edit ? (\r\n                // View Mode\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                  <div className=\"space-y-4\">\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Name</label>\r\n                      <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                        {userDetails?.name || 'Not provided'}\r\n                      </div>\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Username</label>\r\n                      <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                        {userDetails?.username || 'Not provided'}\r\n                      </div>\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Email</label>\r\n                      <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                        {userDetails?.email ? (\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <span>{userDetails.email}</span>\r\n                            {userDetails.email.includes('@brainwave.temp') && (\r\n                              <span className=\"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full\">\r\n                                Auto-generated\r\n                              </span>\r\n                            )}\r\n                          </div>\r\n                        ) : (\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <span className=\"text-gray-500\">No email set</span>\r\n                            <button\r\n                              onClick={async () => {\r\n                                const timestamp = Date.now();\r\n                                const autoEmail = `${userDetails.username}.${timestamp}@brainwave.temp`;\r\n                                setFormData(prev => ({ ...prev, email: autoEmail }));\r\n                                message.info('Auto-generated email created. Click \"Save Changes\" to update.');\r\n                              }}\r\n                              className=\"text-xs bg-green-100 text-green-600 px-2 py-1 rounded-full hover:bg-green-200 transition-colors\"\r\n                            >\r\n                              Generate Email\r\n                            </button>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">School</label>\r\n                      <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                        {userDetails?.school || 'Not provided'}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"space-y-4\">\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Level</label>\r\n                      <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                        {userDetails?.level || 'Not provided'}\r\n                      </div>\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Class</label>\r\n                      <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                        {userDetails?.class || 'Not provided'}\r\n                      </div>\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Phone Number</label>\r\n                      <div className=\"p-3 bg-gray-50 rounded-lg border\">\r\n                        {userDetails?.phoneNumber || 'Not provided'}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                // Edit Mode\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                  <div className=\"space-y-4\">\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Name *</label>\r\n                      <input\r\n                        type=\"text\"\r\n                        name=\"name\"\r\n                        value={formData.name}\r\n                        onChange={handleChange}\r\n                        className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\r\n                        placeholder=\"Enter your name\"\r\n                        required\r\n                      />\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Username</label>\r\n                      <div className=\"p-3 bg-gray-100 rounded-lg border text-gray-600\">\r\n                        {userDetails?.username || 'Not available'}\r\n                        <span className=\"text-xs text-gray-500 block mt-1\">Username cannot be changed</span>\r\n                      </div>\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Email (Optional)</label>\r\n                      <div className=\"relative\">\r\n                        <input\r\n                          type=\"email\"\r\n                          name=\"email\"\r\n                          value={formData.email || \"\"}\r\n                          onChange={handleChange}\r\n                          className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors pr-24\"\r\n                          placeholder=\"Enter your email (optional)\"\r\n                        />\r\n                        {(!formData.email || formData.email === '') && (\r\n                          <button\r\n                            type=\"button\"\r\n                            onClick={() => {\r\n                              const timestamp = Date.now();\r\n                              const autoEmail = `${userDetails.username}.${timestamp}@brainwave.temp`;\r\n                              setFormData(prev => ({ ...prev, email: autoEmail }));\r\n                              message.success('Auto-generated email created!');\r\n                            }}\r\n                            className=\"absolute right-2 top-1/2 transform -translate-y-1/2 text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200 transition-colors\"\r\n                          >\r\n                            Auto-Gen\r\n                          </button>\r\n                        )}\r\n                      </div>\r\n                      {formData.email && formData.email.includes('@brainwave.temp') && (\r\n                        <p className=\"text-xs text-blue-600 mt-1\">\r\n                          📧 This is an auto-generated email. You can change it to your real email if you prefer.\r\n                        </p>\r\n                      )}\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">School</label>\r\n                      <input\r\n                        type=\"text\"\r\n                        name=\"school\"\r\n                        value={formData.school}\r\n                        onChange={handleChange}\r\n                        className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\r\n                        placeholder=\"Enter your school\"\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"space-y-4\">\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Level *</label>\r\n                      <select\r\n                        name=\"level\"\r\n                        value={formData.level}\r\n                        onChange={handleChange}\r\n                        className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\r\n                        required\r\n                      >\r\n                        <option value=\"\">Select Level</option>\r\n                        <option value=\"Primary\">Primary</option>\r\n                        <option value=\"Secondary\">Secondary</option>\r\n                        <option value=\"Advance\">Advance</option>\r\n                      </select>\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Class *</label>\r\n                      <select\r\n                        name=\"class_\"\r\n                        value={formData.class_}\r\n                        onChange={handleChange}\r\n                        className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\r\n                        required\r\n                      >\r\n                        <option value=\"\">Select Class</option>\r\n                        {formData.level === \"Primary\" && (\r\n                          <>\r\n                            <option value=\"1\">1</option>\r\n                            <option value=\"2\">2</option>\r\n                            <option value=\"3\">3</option>\r\n                            <option value=\"4\">4</option>\r\n                            <option value=\"5\">5</option>\r\n                            <option value=\"6\">6</option>\r\n                            <option value=\"7\">7</option>\r\n                          </>\r\n                        )}\r\n                        {formData.level === \"Secondary\" && (\r\n                          <>\r\n                            <option value=\"1\">1</option>\r\n                            <option value=\"2\">2</option>\r\n                            <option value=\"3\">3</option>\r\n                            <option value=\"4\">4</option>\r\n                          </>\r\n                        )}\r\n                        {formData.level === \"Advance\" && (\r\n                          <>\r\n                            <option value=\"5\">5</option>\r\n                            <option value=\"6\">6</option>\r\n                          </>\r\n                        )}\r\n                      </select>\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Phone Number</label>\r\n                      <input\r\n                        type=\"tel\"\r\n                        name=\"phoneNumber\"\r\n                        value={formData.phoneNumber}\r\n                        onChange={handleChange}\r\n                        className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\"\r\n                        placeholder=\"Enter phone number\"\r\n                        maxLength=\"10\"\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* Subscription Section */}\r\n              <div className=\"mt-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl border border-blue-200\">\r\n                <h3 className=\"text-xl font-bold text-gray-900 mb-4 flex items-center\">\r\n                  <span className=\"mr-2\">💎</span>\r\n                  Subscription Plan\r\n                </h3>\r\n\r\n                {subscriptionData && subscriptionData.status === 'active' ? (\r\n                  // Active Subscription\r\n                  <div className=\"space-y-6\">\r\n                    {/* Plan Header */}\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <div>\r\n                        <h4 className=\"text-xl font-bold text-blue-700\">{subscriptionData.planTitle || subscriptionData.plan?.title || 'Premium Plan'}</h4>\r\n                        <p className=\"text-gray-600 mt-1\">\r\n                          <span className=\"inline-flex items-center\">\r\n                            <span className=\"w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse\"></span>\r\n                            Active Subscription\r\n                          </span>\r\n                        </p>\r\n                      </div>\r\n                      <div className=\"text-right\">\r\n                        <span className=\"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-green-100 text-green-800 border border-green-200\">\r\n                          ✅ Active\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Subscription Timeline */}\r\n                    <div className=\"bg-gradient-to-r from-blue-50 to-green-50 p-4 rounded-xl border border-blue-200\">\r\n                      <h5 className=\"text-sm font-semibold text-gray-700 mb-3 flex items-center\">\r\n                        <span className=\"mr-2\">📅</span>\r\n                        Subscription Timeline\r\n                      </h5>\r\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                        <div>\r\n                          <p className=\"text-xs text-gray-500 uppercase tracking-wide\">Started On</p>\r\n                          <p className=\"text-sm font-semibold text-gray-900\">\r\n                            {subscriptionData.startDate ?\r\n                              new Date(subscriptionData.startDate).toLocaleDateString('en-US', {\r\n                                weekday: 'short',\r\n                                year: 'numeric',\r\n                                month: 'short',\r\n                                day: 'numeric'\r\n                              }) :\r\n                              new Date(subscriptionData.createdAt).toLocaleDateString('en-US', {\r\n                                weekday: 'short',\r\n                                year: 'numeric',\r\n                                month: 'short',\r\n                                day: 'numeric'\r\n                              })\r\n                            }\r\n                          </p>\r\n                        </div>\r\n                        <div>\r\n                          <p className=\"text-xs text-gray-500 uppercase tracking-wide\">Expires On</p>\r\n                          <p className=\"text-sm font-semibold text-red-600\">\r\n                            {new Date(subscriptionData.endDate).toLocaleDateString('en-US', {\r\n                              weekday: 'short',\r\n                              year: 'numeric',\r\n                              month: 'short',\r\n                              day: 'numeric'\r\n                            })}\r\n                          </p>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Plan Statistics Grid */}\r\n                    <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\r\n                      <div className=\"bg-white p-4 rounded-xl border border-gray-200 shadow-sm\">\r\n                        <div className=\"flex items-center justify-between\">\r\n                          <div>\r\n                            <p className=\"text-xs text-gray-500 uppercase tracking-wide\">Total Duration</p>\r\n                            <p className=\"text-lg font-bold text-gray-900\">\r\n                              {subscriptionData.duration || subscriptionData.plan?.duration || 1} month{(subscriptionData.duration || subscriptionData.plan?.duration || 1) > 1 ? 's' : ''}\r\n                            </p>\r\n                          </div>\r\n                          <div className=\"text-blue-500\">\r\n                            <span className=\"text-2xl\">📆</span>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div className=\"bg-white p-4 rounded-xl border border-gray-200 shadow-sm\">\r\n                        <div className=\"flex items-center justify-between\">\r\n                          <div>\r\n                            <p className=\"text-xs text-gray-500 uppercase tracking-wide\">Amount Paid</p>\r\n                            <p className=\"text-lg font-bold text-green-600\">\r\n                              {(subscriptionData.amount || subscriptionData.discountedPrice || 0).toLocaleString()} TZS\r\n                            </p>\r\n                          </div>\r\n                          <div className=\"text-green-500\">\r\n                            <span className=\"text-2xl\">💰</span>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div className=\"bg-white p-4 rounded-xl border border-gray-200 shadow-sm\">\r\n                        <div className=\"flex items-center justify-between\">\r\n                          <div>\r\n                            <p className=\"text-xs text-gray-500 uppercase tracking-wide\">Days Remaining</p>\r\n                            <p className=\"text-lg font-bold text-orange-600\">\r\n                              {(() => {\r\n                                const daysLeft = Math.max(0, Math.ceil((new Date(subscriptionData.endDate) - new Date()) / (1000 * 60 * 60 * 24)));\r\n                                return daysLeft;\r\n                              })()} days\r\n                            </p>\r\n                          </div>\r\n                          <div className=\"text-orange-500\">\r\n                            <span className=\"text-2xl\">⏰</span>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div className=\"bg-white p-4 rounded-xl border border-gray-200 shadow-sm\">\r\n                        <div className=\"flex items-center justify-between\">\r\n                          <div>\r\n                            <p className=\"text-xs text-gray-500 uppercase tracking-wide\">Total Days</p>\r\n                            <p className=\"text-lg font-bold text-purple-600\">\r\n                              {(subscriptionData.duration || subscriptionData.plan?.duration || 1) * 30} days\r\n                            </p>\r\n                          </div>\r\n                          <div className=\"text-purple-500\">\r\n                            <span className=\"text-2xl\">📊</span>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Progress Bar */}\r\n                    <div className=\"bg-white p-4 rounded-xl border border-gray-200\">\r\n                      <div className=\"flex items-center justify-between mb-2\">\r\n                        <p className=\"text-sm font-semibold text-gray-700\">Subscription Progress</p>\r\n                        <p className=\"text-xs text-gray-500\">\r\n                          {(() => {\r\n                            const duration = subscriptionData.duration || subscriptionData.plan?.duration || 1;\r\n                            const totalDays = duration * 30;\r\n                            const daysLeft = Math.max(0, Math.ceil((new Date(subscriptionData.endDate) - new Date()) / (1000 * 60 * 60 * 24)));\r\n                            const daysUsed = totalDays - daysLeft;\r\n                            const percentage = Math.min(100, Math.max(0, (daysUsed / totalDays) * 100));\r\n                            return `${percentage.toFixed(1)}% completed`;\r\n                          })()}\r\n                        </p>\r\n                      </div>\r\n                      <div className=\"w-full bg-gray-200 rounded-full h-3\">\r\n                        <div\r\n                          className=\"bg-gradient-to-r from-blue-500 to-green-500 h-3 rounded-full transition-all duration-500\"\r\n                          style={{\r\n                            width: `${(() => {\r\n                              const duration = subscriptionData.duration || subscriptionData.plan?.duration || 1;\r\n                              const totalDays = duration * 30;\r\n                              const daysLeft = Math.max(0, Math.ceil((new Date(subscriptionData.endDate) - new Date()) / (1000 * 60 * 60 * 24)));\r\n                              const daysUsed = totalDays - daysLeft;\r\n                              const percentage = Math.min(100, Math.max(0, (daysUsed / totalDays) * 100));\r\n                              return percentage;\r\n                            })()}%`\r\n                          }}\r\n                        ></div>\r\n                      </div>\r\n                      <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\r\n                        <span>Started</span>\r\n                        <span>\r\n                          {(() => {\r\n                            const daysLeft = Math.max(0, Math.ceil((new Date(subscriptionData.endDate) - new Date()) / (1000 * 60 * 60 * 24)));\r\n                            if (daysLeft > 7) {\r\n                              return `${daysLeft} days left`;\r\n                            } else if (daysLeft > 0) {\r\n                              return `⚠️ ${daysLeft} days left`;\r\n                            } else {\r\n                              return '❌ Expired';\r\n                            }\r\n                          })()}\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Action Buttons */}\r\n                    <div className=\"flex gap-3\">\r\n                      <button\r\n                        onClick={() => setShowSubscriptionModal(true)}\r\n                        className=\"px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all font-medium shadow-md\"\r\n                      >\r\n                        🚀 Upgrade Plan\r\n                      </button>\r\n                      <button\r\n                        onClick={() => {\r\n                          const endDate = new Date(subscriptionData.endDate);\r\n                          const today = new Date();\r\n                          const daysLeft = Math.ceil((endDate - today) / (1000 * 60 * 60 * 24));\r\n\r\n                          if (daysLeft <= 7 && daysLeft > 0) {\r\n                            message.warning(`Your subscription expires in ${daysLeft} days. Consider renewing soon!`);\r\n                          } else if (daysLeft <= 0) {\r\n                            message.error('Your subscription has expired. Please renew to continue accessing premium features.');\r\n                          } else {\r\n                            message.info(`Your subscription is active for ${daysLeft} more days.`);\r\n                          }\r\n                        }}\r\n                        className=\"px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium\"\r\n                      >\r\n                        📊 Check Status\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                ) : (\r\n                  // No Active Subscription\r\n                  <div className=\"text-center py-8\">\r\n                    <div className=\"mb-6\">\r\n                      <div className=\"w-20 h-20 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4\">\r\n                        <span className=\"text-4xl\">🔒</span>\r\n                      </div>\r\n                      <h4 className=\"text-xl font-bold text-gray-900 mb-2\">No Active Subscription</h4>\r\n                      <p className=\"text-gray-600 mb-6 max-w-md mx-auto\">\r\n                        Unlock premium features and get unlimited access to all educational content with a subscription plan.\r\n                      </p>\r\n                    </div>\r\n\r\n                    {/* Premium Features Preview */}\r\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6 max-w-2xl mx-auto\">\r\n                      <div className=\"bg-gradient-to-br from-blue-50 to-indigo-50 p-4 rounded-lg border border-blue-200\">\r\n                        <div className=\"text-blue-600 text-2xl mb-2\">📚</div>\r\n                        <h5 className=\"font-semibold text-gray-900 mb-1\">Unlimited Quizzes</h5>\r\n                        <p className=\"text-sm text-gray-600\">Access all quizzes without restrictions</p>\r\n                      </div>\r\n                      <div className=\"bg-gradient-to-br from-green-50 to-emerald-50 p-4 rounded-lg border border-green-200\">\r\n                        <div className=\"text-green-600 text-2xl mb-2\">🤖</div>\r\n                        <h5 className=\"font-semibold text-gray-900 mb-1\">AI Study Assistant</h5>\r\n                        <p className=\"text-sm text-gray-600\">Get instant help with your studies</p>\r\n                      </div>\r\n                      <div className=\"bg-gradient-to-br from-purple-50 to-violet-50 p-4 rounded-lg border border-purple-200\">\r\n                        <div className=\"text-purple-600 text-2xl mb-2\">📊</div>\r\n                        <h5 className=\"font-semibold text-gray-900 mb-1\">Progress Tracking</h5>\r\n                        <p className=\"text-sm text-gray-600\">Monitor your learning progress</p>\r\n                      </div>\r\n                      <div className=\"bg-gradient-to-br from-orange-50 to-amber-50 p-4 rounded-lg border border-orange-200\">\r\n                        <div className=\"text-orange-600 text-2xl mb-2\">🏆</div>\r\n                        <h5 className=\"font-semibold text-gray-900 mb-1\">Rankings & Badges</h5>\r\n                        <p className=\"text-sm text-gray-600\">Compete and earn achievements</p>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Call to Action */}\r\n                    <div className=\"space-y-3\">\r\n                      <button\r\n                        onClick={() => setShowSubscriptionModal(true)}\r\n                        className=\"px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all font-semibold text-lg shadow-lg hover:shadow-xl transform hover:scale-105\"\r\n                      >\r\n                        🚀 Choose Subscription Plan\r\n                      </button>\r\n                      <p className=\"text-xs text-gray-500\">\r\n                        Plans start from as low as 13,000 TZS per month\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              {/* Action Buttons */}\r\n              <div className=\"mt-8 flex justify-center gap-4\">\r\n                {!edit ? (\r\n                  <button\r\n                    onClick={() => {\r\n                      // Ensure formData is properly initialized with current user data\r\n                      setFormData({\r\n                        name: userDetails?.name || \"\",\r\n                        email: userDetails?.email || \"\",\r\n                        school: userDetails?.school || \"\",\r\n                        class_: userDetails?.class || \"\",\r\n                        level: userDetails?.level || \"\",\r\n                        phoneNumber: userDetails?.phoneNumber || \"\",\r\n                      });\r\n                      setEdit(true);\r\n                    }}\r\n                    className=\"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium\"\r\n                  >\r\n                    Edit Profile\r\n                  </button>\r\n                ) : (\r\n                  <>\r\n                    <button\r\n                      onClick={discardChanges}\r\n                      className=\"px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-200 font-medium\"\r\n                    >\r\n                      Cancel\r\n                    </button>\r\n                    <button\r\n                      onClick={handleUpdate}\r\n                      className=\"px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 font-medium\"\r\n                    >\r\n                      Save Changes\r\n                    </button>\r\n                    {/* Debug button - remove in production */}\r\n                    <button\r\n                      onClick={() => {\r\n                        console.log('🔍 Debug - Current formData:', formData);\r\n                        console.log('🔍 Debug - Current userDetails:', userDetails);\r\n                        alert(`FormData: ${JSON.stringify(formData, null, 2)}`);\r\n                      }}\r\n                      className=\"px-4 py-2 bg-gray-400 text-white rounded-lg hover:bg-gray-500 transition-colors duration-200 font-medium text-sm\"\r\n                    >\r\n                      Debug\r\n                    </button>\r\n                  </>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Hidden file input for profile image upload */}\r\n      <input\r\n        type=\"file\"\r\n        id=\"profileImageInput\"\r\n        accept=\"image/*\"\r\n        onChange={handleImageChange}\r\n        style={{ display: 'none' }}\r\n      />\r\n\r\n      {/* Level Change Confirmation Modal */}\r\n      <Modal\r\n        title=\"Confirm Level Change\"\r\n        open={showLevelChangeModal}\r\n        onOk={handleLevelChangeConfirm}\r\n        onCancel={() => {\r\n          setShowLevelChangeModal(false);\r\n          setPendingLevelChange(null);\r\n        }}\r\n        okText=\"Confirm\"\r\n        cancelText=\"Cancel\"\r\n      >\r\n        <p>\r\n          Are you sure you want to change your level to <strong>{pendingLevelChange}</strong>?\r\n        </p>\r\n        <p className=\"text-orange-600 text-sm mt-2\">\r\n          Note: Changing your level will reset your class selection and you'll only have access to content for the new level.\r\n        </p>\r\n      </Modal>\r\n\r\n      {/* Subscription Modal */}\r\n      <SubscriptionModal\r\n        isOpen={showSubscriptionModal}\r\n        onClose={() => setShowSubscriptionModal(false)}\r\n        onSuccess={() => {\r\n          setShowSubscriptionModal(false);\r\n          // Refresh user data to show updated subscription\r\n          getUserData();\r\n          message.success('Subscription updated successfully!');\r\n        }}\r\n      />\r\n\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Profile;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAO,aAAa;AACpB,OAAOC,SAAS,MAAM,+BAA+B;AACrD,SACEC,WAAW,EACXC,cAAc,EACdC,eAAe,QACV,yBAAyB;AAChC,SAASC,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,MAAM,QAAQ,MAAM;AAC1D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,uBAAuB,EAAEC,cAAc,EAAEC,gBAAgB,QAAQ,2BAA2B;AACrG,OAAOC,cAAc,MAAM,2CAA2C;AACtE,OAAOC,iBAAiB,MAAM,yDAAyD;AACvF,SAASC,WAAW,QAAQ,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEhE,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACpB,MAAM;IAAEC,CAAC;IAAEC,WAAW;IAAEC;EAAa,CAAC,GAAGlB,WAAW,CAAC,CAAC;EACtD,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACwC,WAAW,EAAEC,cAAc,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC0C,WAAW,EAAEC,cAAc,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC4C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC8C,IAAI,EAAEC,OAAO,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACkD,QAAQ,EAAEC,WAAW,CAAC,GAAGnD,QAAQ,CAAC;IACvCoD,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG3D,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC4D,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAAC8D,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACgE,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGjE,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAMkE,QAAQ,GAAGxD,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEyD;EAAiB,CAAC,GAAGxD,WAAW,CAAEyD,KAAK,IAAKA,KAAK,CAACC,YAAY,CAAC;EAEvE,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMzD,uBAAuB,CAAC,CAAC;MAChD,IAAIyD,QAAQ,CAACC,OAAO,EAAE;QACpB/B,cAAc,CAAC8B,QAAQ,CAACE,IAAI,CAAC;MAC/B,CAAC,MAAM;QACLnE,OAAO,CAACoE,KAAK,CAACH,QAAQ,CAACjE,OAAO,CAAC;MACjC;MACA4D,QAAQ,CAACtD,WAAW,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,OAAO8D,KAAK,EAAE;MACdpE,OAAO,CAACoE,KAAK,CAACA,KAAK,CAACpE,OAAO,CAAC;MAC5B4D,QAAQ,CAACtD,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAM+D,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,OAAO,GAAGpC,WAAW,CACxBqC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,MAAM;MACrBD,IAAI;MACJE,OAAO,EAAED,KAAK,GAAG;IACnB,CAAC,CAAC,CAAC,CACFE,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACJ,IAAI,CAACK,MAAM,CAACC,QAAQ,CAAC9C,WAAW,CAAC+C,GAAG,CAAC,CAAC;IAC/D1C,cAAc,CAACiC,OAAO,CAAC;EACzB,CAAC;;EAED;EACA,MAAMU,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,EAAChD,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAE+C,GAAG,GAAE;IAEvB,IAAI;MACFnB,QAAQ,CAACrD,WAAW,CAAC,CAAC,CAAC;;MAEvB;MACA,MAAM0E,eAAe,GAAG,MAAMxE,cAAc,CAACuB,WAAW,CAAC+C,GAAG,EAAE,CAAC,CAAC;MAEhE,IAAIE,eAAe,CAACf,OAAO,EAAE;QAC3B3B,mBAAmB,CAAC0C,eAAe,CAACd,IAAI,CAAC;MAC3C;;MAEA;MACA,MAAMe,mBAAmB,GAAG,MAAMxE,gBAAgB,CAAC;QACjDyE,KAAK,EAAE,IAAI;QACXC,WAAW,EAAE,CAAApD,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEiB,KAAK,KAAI;MACrC,CAAC,CAAC;MAEF,IAAIiC,mBAAmB,CAAChB,OAAO,EAAE;QAC/B,MAAMmB,SAAS,GAAGH,mBAAmB,CAACf,IAAI,CAACmB,SAAS,CAACd,IAAI,IAAIA,IAAI,CAACO,GAAG,KAAK/C,WAAW,CAAC+C,GAAG,CAAC;QAC1F,IAAIM,SAAS,IAAI,CAAC,EAAE;UAClB,MAAME,YAAY,GAAG;YACnB,GAAGL,mBAAmB,CAACf,IAAI,CAACkB,SAAS,CAAC;YACtCG,IAAI,EAAEH,SAAS,GAAG,CAAC;YACnBI,UAAU,EAAEP,mBAAmB,CAACf,IAAI,CAACuB;UACvC,CAAC;UACDnD,mBAAmB,CAACoD,IAAI,KAAK;YAC3B,GAAGA,IAAI;YACPC,QAAQ,EAAEP,SAAS,GAAG,CAAC;YACvBI,UAAU,EAAEP,mBAAmB,CAACf,IAAI,CAACuB,MAAM;YAC3ClB,IAAI,EAAEe;UACR,CAAC,CAAC,CAAC;QACL;MACF;MAEA3B,QAAQ,CAACtD,WAAW,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,OAAO8D,KAAK,EAAE;MACdR,QAAQ,CAACtD,WAAW,CAAC,CAAC,CAAC;MACvBuF,OAAO,CAACzB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EAED3E,SAAS,CAAC,MAAM;IACd,IAAIyC,WAAW,IAAIF,WAAW,EAAE;MAC9BqC,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACnC,WAAW,EAAEF,WAAW,CAAC,CAAC;EAE9B,MAAM8D,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9BlC,QAAQ,CAACrD,WAAW,CAAC,CAAC,CAAC;IACvB,IAAI;MACF,MAAM0D,QAAQ,GAAG,MAAMrE,WAAW,CAAC,CAAC;MACpC,IAAIqE,QAAQ,CAACC,OAAO,EAAE;QACpBjC,cAAc,CAACgC,QAAQ,CAACE,IAAI,CAAC;QAC7BtB,WAAW,CAAC;UACVC,IAAI,EAAEmB,QAAQ,CAACE,IAAI,CAACrB,IAAI,IAAI,EAAE;UAC9BC,KAAK,EAAEkB,QAAQ,CAACE,IAAI,CAACpB,KAAK,IAAI,EAAE;UAChCC,MAAM,EAAEiB,QAAQ,CAACE,IAAI,CAACnB,MAAM,IAAI,EAAE;UAClCE,MAAM,EAAEe,QAAQ,CAACE,IAAI,CAAC4B,KAAK,IAAI,EAAE;UACjC9C,KAAK,EAAEgB,QAAQ,CAACE,IAAI,CAAClB,KAAK,IAAI,EAAE;UAChCE,WAAW,EAAEc,QAAQ,CAACE,IAAI,CAAChB,WAAW,IAAI;QAC5C,CAAC,CAAC;QACF,IAAIc,QAAQ,CAACE,IAAI,CAACf,YAAY,EAAE;UAC9BC,eAAe,CAACY,QAAQ,CAACE,IAAI,CAACf,YAAY,CAAC;QAC7C;QACAY,YAAY,CAAC,CAAC;MAChB,CAAC,MAAM;QACLhE,OAAO,CAACoE,KAAK,CAACH,QAAQ,CAACjE,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOoE,KAAK,EAAE;MACdpE,OAAO,CAACoE,KAAK,CAACA,KAAK,CAACpE,OAAO,CAAC;IAC9B,CAAC,SAAS;MACR4D,QAAQ,CAACtD,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAEDb,SAAS,CAAC,MAAM;IACd,IAAIuG,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;MACjCH,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAErD,IAAI;MAAEsD;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChC,IAAIvD,IAAI,KAAK,aAAa,IAAIsD,KAAK,CAACV,MAAM,GAAG,EAAE,EAAE;IACjD,IAAI5C,IAAI,KAAK,OAAO,IAAIsD,KAAK,MAAKpE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEiB,KAAK,KAAImD,KAAK,KAAK,EAAE,EAAE;MACpEzC,qBAAqB,CAACyC,KAAK,CAAC;MAC5B3C,uBAAuB,CAAC,IAAI,CAAC;MAC7B;IACF;IACAZ,WAAW,CAAE8C,IAAI,KAAM;MACrB,GAAGA,IAAI;MACP,CAAC7C,IAAI,GAAGsD,KAAK;MACb,IAAItD,IAAI,KAAK,OAAO,GAAG;QAAEI,MAAM,EAAE;MAAG,CAAC,GAAG,CAAC,CAAC;IAC5C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMoD,cAAc,GAAGA,CAAA,KAAM;IAC3BzD,WAAW,CAAC;MACVC,IAAI,EAAEd,WAAW,CAACc,IAAI;MACtBC,KAAK,EAAEf,WAAW,CAACe,KAAK;MACxBC,MAAM,EAAEhB,WAAW,CAACgB,MAAM;MAC1BE,MAAM,EAAElB,WAAW,CAAC+D,KAAK;MACzB9C,KAAK,EAAEjB,WAAW,CAACiB,KAAK;MACxBE,WAAW,EAAEnB,WAAW,CAACmB;IAC3B,CAAC,CAAC;IACFV,OAAO,CAAC,KAAK,CAAC;EAChB,CAAC;EAID,MAAM8D,YAAY,GAAG,MAAAA,CAAO;IAAEC;EAAQ,CAAC,GAAG,CAAC,CAAC,KAAK;IAC/CX,OAAO,CAACY,GAAG,CAAC,sBAAsB,EAAE7D,QAAQ,CAAC;IAC7CiD,OAAO,CAACY,GAAG,CAAC,yBAAyB,EAAEzE,WAAW,CAAC;;IAEnD;IACA,IAAI,CAACY,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAAC4D,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjDb,OAAO,CAACY,GAAG,CAAC,oCAAoC,CAAC;MACjD,OAAOzG,OAAO,CAACoE,KAAK,CAAC,yBAAyB,CAAC;IACjD;IACA,IAAI,CAACxB,QAAQ,CAACM,MAAM,IAAIN,QAAQ,CAACM,MAAM,CAACwD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACrDb,OAAO,CAACY,GAAG,CAAC,qCAAqC,CAAC;MAClD,OAAOzG,OAAO,CAACoE,KAAK,CAAC,wBAAwB,CAAC;IAChD;IACA,IAAI,CAACxB,QAAQ,CAACK,KAAK,IAAIL,QAAQ,CAACK,KAAK,CAACyD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACnDb,OAAO,CAACY,GAAG,CAAC,qCAAqC,CAAC;MAClD,OAAOzG,OAAO,CAACoE,KAAK,CAAC,wBAAwB,CAAC;IAChD;IACA;IACA,IAAIxB,QAAQ,CAACG,KAAK,IAAIH,QAAQ,CAACG,KAAK,CAAC2D,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAClD,MAAMC,UAAU,GAAG,4BAA4B;MAC/C,IAAI,CAACA,UAAU,CAACC,IAAI,CAAChE,QAAQ,CAACG,KAAK,CAAC,EAAE;QACpC,OAAO/C,OAAO,CAACoE,KAAK,CAAC,qCAAqC,CAAC;MAC7D;IACF;;IAEA;IACA;;IAEAR,QAAQ,CAACrD,WAAW,CAAC,CAAC,CAAC;IACvB,IAAI;MACF;MACA,MAAMsG,aAAa,GAAG;QACpB,GAAGjE,QAAQ;QACXiC,MAAM,EAAE7C,WAAW,CAAC+C;MACtB,CAAC;;MAED;MACA,IAAInC,QAAQ,CAACG,KAAK,IAAIH,QAAQ,CAACG,KAAK,CAAC2D,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAClDG,aAAa,CAAC9D,KAAK,GAAGH,QAAQ,CAACG,KAAK,CAAC2D,IAAI,CAAC,CAAC;MAC7C,CAAC,MAAM,IAAI1E,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEe,KAAK,EAAE;QAC7B8D,aAAa,CAAC9D,KAAK,GAAGf,WAAW,CAACe,KAAK;MACzC;MAEA8C,OAAO,CAACY,GAAG,CAAC,yBAAyB,EAAEI,aAAa,CAAC;MAErD,MAAM5C,QAAQ,GAAG,MAAMpE,cAAc,CAACgH,aAAa,CAAC;MAEpDhB,OAAO,CAACY,GAAG,CAAC,qBAAqB,EAAExC,QAAQ,CAAC;MAE5C,IAAIA,QAAQ,CAACC,OAAO,EAAE;QACpBlE,OAAO,CAACkE,OAAO,CAACD,QAAQ,CAACjE,OAAO,CAAC;QACjCyC,OAAO,CAAC,KAAK,CAAC;QACdqD,WAAW,CAAC,CAAC;QACb,IAAI7B,QAAQ,CAAC6C,YAAY,EAAE;UACzBC,UAAU,CAAC,MAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC;QAClD;MACF,CAAC,MAAM;QACLrB,OAAO,CAACzB,KAAK,CAAC,kBAAkB,EAAEH,QAAQ,CAAC;QAC3CjE,OAAO,CAACoE,KAAK,CAACH,QAAQ,CAACjE,OAAO,IAAI,6CAA6C,CAAC;MAClF;IACF,CAAC,CAAC,OAAOoE,KAAK,EAAE;MAAA,IAAA+C,eAAA,EAAAC,oBAAA;MACdvB,OAAO,CAACzB,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvC,MAAMiD,YAAY,GAAG,EAAAF,eAAA,GAAA/C,KAAK,CAACH,QAAQ,cAAAkD,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBhD,IAAI,cAAAiD,oBAAA,uBAApBA,oBAAA,CAAsBpH,OAAO,KAAIoE,KAAK,CAACpE,OAAO,IAAI,+BAA+B;MACtGA,OAAO,CAACoE,KAAK,CAAE,kBAAiBiD,YAAa,EAAC,CAAC;IACjD,CAAC,SAAS;MACRzD,QAAQ,CAACtD,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAMgH,wBAAwB,GAAGA,CAAA,KAAM;IACrCzE,WAAW,CAAE8C,IAAI,KAAM;MACrB,GAAGA,IAAI;MACP1C,KAAK,EAAES,kBAAkB;MACzBR,MAAM,EAAE;IACV,CAAC,CAAC,CAAC;IACHO,uBAAuB,CAAC,KAAK,CAAC;IAC9BE,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAM4D,uBAAuB,GAAGA,CAAA,KAAM;IACpC9D,uBAAuB,CAAC,KAAK,CAAC;IAC9BE,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAM6D,iBAAiB,GAAG,MAAOrB,CAAC,IAAK;IACrC,MAAMsB,IAAI,GAAGtB,CAAC,CAACE,MAAM,CAACqB,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAID,IAAI,EAAE;MACR;MACA,IAAI,CAACA,IAAI,CAACE,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;QACnC5H,OAAO,CAACoE,KAAK,CAAC,kCAAkC,CAAC;QACjD;MACF;;MAEA;MACA,IAAIqD,IAAI,CAACI,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;QAC/B7H,OAAO,CAACoE,KAAK,CAAC,oCAAoC,CAAC;QACnD;MACF;MAEAf,eAAe,CAACoE,IAAI,CAAC;;MAErB;MACA,MAAMK,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAMrF,eAAe,CAACmF,MAAM,CAACG,MAAM,CAAC;MACvDH,MAAM,CAACI,aAAa,CAACT,IAAI,CAAC;;MAE1B;MACA,MAAMtD,IAAI,GAAG,IAAIgE,QAAQ,CAAC,CAAC;MAC3BhE,IAAI,CAACiE,MAAM,CAAC,cAAc,EAAEX,IAAI,CAAC;MACjC7D,QAAQ,CAACrD,WAAW,CAAC,CAAC,CAAC;MAEvB,IAAI;QACF,MAAM0D,QAAQ,GAAG,MAAMnE,eAAe,CAACqE,IAAI,CAAC;QAC5CP,QAAQ,CAACtD,WAAW,CAAC,CAAC,CAAC;QACvB,IAAI2D,QAAQ,CAACC,OAAO,EAAE;UACpBlE,OAAO,CAACkE,OAAO,CAAC,uCAAuC,CAAC;UACxD4B,WAAW,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,MAAM;UACL9F,OAAO,CAACoE,KAAK,CAACH,QAAQ,CAACjE,OAAO,CAAC;QACjC;MACF,CAAC,CAAC,OAAOoE,KAAK,EAAE;QACdR,QAAQ,CAACtD,WAAW,CAAC,CAAC,CAAC;QACvBN,OAAO,CAACoE,KAAK,CAACA,KAAK,CAACpE,OAAO,IAAI,kCAAkC,CAAC;MACpE;IACF;EACF,CAAC;EAED,MAAMqI,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,MAAMlE,IAAI,GAAG,IAAIgE,QAAQ,CAAC,CAAC;IAC3BhE,IAAI,CAACiE,MAAM,CAAC,cAAc,EAAEhF,YAAY,CAAC;IACzCQ,QAAQ,CAACrD,WAAW,CAAC,CAAC,CAAC;IACvB,IAAI;MACF,MAAM0D,QAAQ,GAAG,MAAMnE,eAAe,CAACqE,IAAI,CAAC;MAC5C,IAAIF,QAAQ,CAACC,OAAO,EAAE;QACpBlE,OAAO,CAACkE,OAAO,CAAC,6BAA6B,CAAC;QAC9C4B,WAAW,CAAC,CAAC;MACf,CAAC,MAAM;QACL9F,OAAO,CAACoE,KAAK,CAACH,QAAQ,CAACjE,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOoE,KAAK,EAAE;MACdpE,OAAO,CAACoE,KAAK,CAACA,KAAK,CAACpE,OAAO,CAAC;IAC9B,CAAC,SAAS;MACR4D,QAAQ,CAACtD,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;;EAID;EACAb,SAAS,CAAC,MAAM;IACdqG,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;;EAEN;EACArG,SAAS,CAAC,MAAM;IACd,IAAIuC,WAAW,EAAE;MACfgD,oBAAoB,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,CAAChD,WAAW,CAAC,CAAC;;EAEjB;EACAvC,SAAS,CAAC,MAAM;IACd,IAAIuC,WAAW,EAAE;MACfa,WAAW,CAAC;QACVC,IAAI,EAAEd,WAAW,CAACc,IAAI,IAAI,EAAE;QAC5BC,KAAK,EAAEf,WAAW,CAACe,KAAK,IAAI,EAAE;QAAE;QAChCC,MAAM,EAAEhB,WAAW,CAACgB,MAAM,IAAI,EAAE;QAChCE,MAAM,EAAElB,WAAW,CAAC+D,KAAK,IAAI,EAAE;QAC/B9C,KAAK,EAAEjB,WAAW,CAACiB,KAAK,IAAI,EAAE;QAC9BE,WAAW,EAAEnB,WAAW,CAACmB,WAAW,IAAI;MAC1C,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACnB,WAAW,CAAC,CAAC;EAEjB,oBACEjB,OAAA;IAAKuH,SAAS,EAAC,oEAAoE;IAAAC,QAAA,gBACjFxH,OAAA;MAAKuH,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1CxH,OAAA;QAAKuH,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAEhCxH,OAAA;UAAKuH,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BxH,OAAA;YAAIuH,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClE5H,OAAA;YAAGuH,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAA4C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAG7E5H,OAAA;YAAKuH,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAChDxH,OAAA;cAAKuH,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBxH,OAAA,CAACJ,cAAc;gBACb6D,IAAI,EAAExC,WAAY;gBAClB6F,IAAI,EAAC,KAAK;gBACVe,gBAAgB,EAAE,IAAK;gBACvBC,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,cAAc,CAAC,mBAAmB,CAAC,CAACC,KAAK,CAAC,CAAE;gBACpEV,SAAS,EAAC,mDAAmD;gBAC7DW,KAAK,EAAE;kBACLC,KAAK,EAAE,OAAO;kBACdC,MAAM,EAAE,OAAO;kBACfC,MAAM,EAAE,mBAAmB;kBAC3BC,SAAS,EAAE;gBACb;cAAE;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGF5H,OAAA;gBAAKuH,SAAS,EAAC,kIAAkI;gBAC5IO,OAAO,EAAEA,CAAA,KAAMC,QAAQ,CAACC,cAAc,CAAC,mBAAmB,CAAC,CAACC,KAAK,CAAC,CAAE;gBAAAT,QAAA,eACvExH,OAAA;kBAAKuH,SAAS,EAAC,oBAAoB;kBAACgB,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAjB,QAAA,gBACvFxH,OAAA;oBAAM0I,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAkK;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC1O5H,OAAA;oBAAM0I,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAkC;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN5H,OAAA;gBACE8I,EAAE,EAAC,mBAAmB;gBACtBlC,IAAI,EAAC,MAAM;gBACXmC,MAAM,EAAC,SAAS;gBAChBxB,SAAS,EAAC,QAAQ;gBAClByB,QAAQ,EAAEvC;cAAkB;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN5H,OAAA;UAAKuH,SAAS,EAAC,gDAAgD;UAAAC,QAAA,eAC7DxH,OAAA;YAAKuH,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBxH,OAAA;cAAKuH,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAE9CxH,OAAA;gBAAKuH,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,gBACnExH,OAAA;kBAAKuH,SAAS,EAAC,sEAAsE;kBAAAC,QAAA,gBACnFxH,OAAA;oBAAGuH,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACzD5H,OAAA;oBAAGuH,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAE,CAAAvG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEc,IAAI,KAAI;kBAAM;oBAAA0F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC,eACN5H,OAAA;kBAAKuH,SAAS,EAAC,wEAAwE;kBAAAC,QAAA,gBACrFxH,OAAA;oBAAGuH,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC9D5H,OAAA;oBAAGuH,SAAS,EAAC,wDAAwD;oBAAAC,QAAA,EAAE,CAAAvG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEgI,QAAQ,KAAI;kBAAU;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5G,CAAC,eACN5H,OAAA;kBAAKuH,SAAS,EAAC,0EAA0E;kBAAAC,QAAA,gBACvFxH,OAAA;oBAAGuH,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC5D5H,OAAA;oBAAGuH,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAE,CAAAvG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE+D,KAAK,KAAI;kBAAK;oBAAAyC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGLrG,gBAAgB,iBACfvB,OAAA;gBAAKuH,SAAS,EAAC,iDAAiD;gBAAAC,QAAA,gBAC9DxH,OAAA;kBAAKuH,SAAS,EAAC,0EAA0E;kBAAAC,QAAA,gBACvFxH,OAAA;oBAAGuH,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC3D5H,OAAA;oBAAGuH,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,GAAC,GAC5C,EAACjG,gBAAgB,CAACsD,QAAQ,IAAI,KAAK,EACnCtD,gBAAgB,CAACmD,UAAU,iBAC1B1E,OAAA;sBAAMuH,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GAAC,GAAC,EAACjG,gBAAgB,CAACmD,UAAU;oBAAA;sBAAA+C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAC7E;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACN5H,OAAA;kBAAKuH,SAAS,EAAC,0EAA0E;kBAAAC,QAAA,gBACvFxH,OAAA;oBAAGuH,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC/D5H,OAAA;oBAAGuH,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAC3C,EAAAnH,qBAAA,GAAAkB,gBAAgB,CAACkC,IAAI,cAAApD,qBAAA,wBAAAC,sBAAA,GAArBD,qBAAA,CAAuB6I,OAAO,cAAA5I,sBAAA,uBAA9BA,sBAAA,CAAgC6I,cAAc,CAAC,CAAC,KAAI;kBAAG;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACN5H,OAAA;kBAAKuH,SAAS,EAAC,0EAA0E;kBAAAC,QAAA,gBACvFxH,OAAA;oBAAGuH,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAChE5H,OAAA;oBAAGuH,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,GAC3C,EAAAjH,sBAAA,GAAAgB,gBAAgB,CAACkC,IAAI,cAAAlD,sBAAA,uBAArBA,sBAAA,CAAuB6I,YAAY,KAAI,GAAG,EAAC,GAC9C;kBAAA;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACN5H,OAAA;kBAAKuH,SAAS,EAAC,sEAAsE;kBAAAC,QAAA,gBACnFxH,OAAA;oBAAGuH,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC5D5H,OAAA;oBAAGuH,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAC3C,EAAAhH,sBAAA,GAAAe,gBAAgB,CAACkC,IAAI,cAAAjD,sBAAA,uBAArBA,sBAAA,CAAuB6I,iBAAiB,KAAI;kBAAG;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACN5H,OAAA;kBAAKuH,SAAS,EAAC,sEAAsE;kBAAAC,QAAA,gBACnFxH,OAAA;oBAAGuH,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC3D5H,OAAA;oBAAGuH,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAC3C,EAAA/G,sBAAA,GAAAc,gBAAgB,CAACkC,IAAI,cAAAhD,sBAAA,uBAArBA,sBAAA,CAAuB6I,aAAa,KAAI;kBAAG;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAGL,CAACnG,IAAI;YAAA;YACJ;YACAzB,OAAA;cAAKuH,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDxH,OAAA;gBAAKuH,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBxH,OAAA;kBAAAwH,QAAA,gBACExH,OAAA;oBAAOuH,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5E5H,OAAA;oBAAKuH,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9C,CAAAvG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEc,IAAI,KAAI;kBAAc;oBAAA0F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN5H,OAAA;kBAAAwH,QAAA,gBACExH,OAAA;oBAAOuH,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAChF5H,OAAA;oBAAKuH,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9C,CAAAvG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEgI,QAAQ,KAAI;kBAAc;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN5H,OAAA;kBAAAwH,QAAA,gBACExH,OAAA;oBAAOuH,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7E5H,OAAA;oBAAKuH,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9CvG,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEe,KAAK,gBACjBhC,OAAA;sBAAKuH,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,gBAChDxH,OAAA;wBAAAwH,QAAA,EAAOvG,WAAW,CAACe;sBAAK;wBAAAyF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,EAC/B3G,WAAW,CAACe,KAAK,CAAC+B,QAAQ,CAAC,iBAAiB,CAAC,iBAC5C/D,OAAA;wBAAMuH,SAAS,EAAC,0DAA0D;wBAAAC,QAAA,EAAC;sBAE3E;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CACP;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,gBAEN5H,OAAA;sBAAKuH,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,gBAChDxH,OAAA;wBAAMuH,SAAS,EAAC,eAAe;wBAAAC,QAAA,EAAC;sBAAY;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACnD5H,OAAA;wBACE8H,OAAO,EAAE,MAAAA,CAAA,KAAY;0BACnB,MAAMyB,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;0BAC5B,MAAMC,SAAS,GAAI,GAAEzI,WAAW,CAACgI,QAAS,IAAGM,SAAU,iBAAgB;0BACvEzH,WAAW,CAAC8C,IAAI,KAAK;4BAAE,GAAGA,IAAI;4BAAE5C,KAAK,EAAE0H;0BAAU,CAAC,CAAC,CAAC;0BACpDzK,OAAO,CAAC0K,IAAI,CAAC,+DAA+D,CAAC;wBAC/E,CAAE;wBACFpC,SAAS,EAAC,iGAAiG;wBAAAC,QAAA,EAC5G;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBACN;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN5H,OAAA;kBAAAwH,QAAA,gBACExH,OAAA;oBAAOuH,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9E5H,OAAA;oBAAKuH,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9C,CAAAvG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEgB,MAAM,KAAI;kBAAc;oBAAAwF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN5H,OAAA;gBAAKuH,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBxH,OAAA;kBAAAwH,QAAA,gBACExH,OAAA;oBAAOuH,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7E5H,OAAA;oBAAKuH,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9C,CAAAvG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEiB,KAAK,KAAI;kBAAc;oBAAAuF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN5H,OAAA;kBAAAwH,QAAA,gBACExH,OAAA;oBAAOuH,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7E5H,OAAA;oBAAKuH,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9C,CAAAvG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE+D,KAAK,KAAI;kBAAc;oBAAAyC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN5H,OAAA;kBAAAwH,QAAA,gBACExH,OAAA;oBAAOuH,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACpF5H,OAAA;oBAAKuH,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAC9C,CAAAvG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEmB,WAAW,KAAI;kBAAc;oBAAAqF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;YAAA;YAEN;YACA5H,OAAA;cAAKuH,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDxH,OAAA;gBAAKuH,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBxH,OAAA;kBAAAwH,QAAA,gBACExH,OAAA;oBAAOuH,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9E5H,OAAA;oBACE4G,IAAI,EAAC,MAAM;oBACX7E,IAAI,EAAC,MAAM;oBACXsD,KAAK,EAAExD,QAAQ,CAACE,IAAK;oBACrBiH,QAAQ,EAAE7D,YAAa;oBACvBoC,SAAS,EAAC,uHAAuH;oBACjIqC,WAAW,EAAC,iBAAiB;oBAC7BC,QAAQ;kBAAA;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN5H,OAAA;kBAAAwH,QAAA,gBACExH,OAAA;oBAAOuH,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAChF5H,OAAA;oBAAKuH,SAAS,EAAC,iDAAiD;oBAAAC,QAAA,GAC7D,CAAAvG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEgI,QAAQ,KAAI,eAAe,eACzCjJ,OAAA;sBAAMuH,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAA0B;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN5H,OAAA;kBAAAwH,QAAA,gBACExH,OAAA;oBAAOuH,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACxF5H,OAAA;oBAAKuH,SAAS,EAAC,UAAU;oBAAAC,QAAA,gBACvBxH,OAAA;sBACE4G,IAAI,EAAC,OAAO;sBACZ7E,IAAI,EAAC,OAAO;sBACZsD,KAAK,EAAExD,QAAQ,CAACG,KAAK,IAAI,EAAG;sBAC5BgH,QAAQ,EAAE7D,YAAa;sBACvBoC,SAAS,EAAC,6HAA6H;sBACvIqC,WAAW,EAAC;oBAA6B;sBAAAnC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1C,CAAC,EACD,CAAC,CAAC/F,QAAQ,CAACG,KAAK,IAAIH,QAAQ,CAACG,KAAK,KAAK,EAAE,kBACxChC,OAAA;sBACE4G,IAAI,EAAC,QAAQ;sBACbkB,OAAO,EAAEA,CAAA,KAAM;wBACb,MAAMyB,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;wBAC5B,MAAMC,SAAS,GAAI,GAAEzI,WAAW,CAACgI,QAAS,IAAGM,SAAU,iBAAgB;wBACvEzH,WAAW,CAAC8C,IAAI,KAAK;0BAAE,GAAGA,IAAI;0BAAE5C,KAAK,EAAE0H;wBAAU,CAAC,CAAC,CAAC;wBACpDzK,OAAO,CAACkE,OAAO,CAAC,+BAA+B,CAAC;sBAClD,CAAE;sBACFoE,SAAS,EAAC,6IAA6I;sBAAAC,QAAA,EACxJ;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CACT;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,EACL/F,QAAQ,CAACG,KAAK,IAAIH,QAAQ,CAACG,KAAK,CAAC+B,QAAQ,CAAC,iBAAiB,CAAC,iBAC3D/D,OAAA;oBAAGuH,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAE1C;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CACJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACN5H,OAAA;kBAAAwH,QAAA,gBACExH,OAAA;oBAAOuH,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC9E5H,OAAA;oBACE4G,IAAI,EAAC,MAAM;oBACX7E,IAAI,EAAC,QAAQ;oBACbsD,KAAK,EAAExD,QAAQ,CAACI,MAAO;oBACvB+G,QAAQ,EAAE7D,YAAa;oBACvBoC,SAAS,EAAC,uHAAuH;oBACjIqC,WAAW,EAAC;kBAAmB;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN5H,OAAA;gBAAKuH,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBxH,OAAA;kBAAAwH,QAAA,gBACExH,OAAA;oBAAOuH,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC/E5H,OAAA;oBACE+B,IAAI,EAAC,OAAO;oBACZsD,KAAK,EAAExD,QAAQ,CAACK,KAAM;oBACtB8G,QAAQ,EAAE7D,YAAa;oBACvBoC,SAAS,EAAC,uHAAuH;oBACjIsC,QAAQ;oBAAArC,QAAA,gBAERxH,OAAA;sBAAQqF,KAAK,EAAC,EAAE;sBAAAmC,QAAA,EAAC;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACtC5H,OAAA;sBAAQqF,KAAK,EAAC,SAAS;sBAAAmC,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACxC5H,OAAA;sBAAQqF,KAAK,EAAC,WAAW;sBAAAmC,QAAA,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC5C5H,OAAA;sBAAQqF,KAAK,EAAC,SAAS;sBAAAmC,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACN5H,OAAA;kBAAAwH,QAAA,gBACExH,OAAA;oBAAOuH,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC/E5H,OAAA;oBACE+B,IAAI,EAAC,QAAQ;oBACbsD,KAAK,EAAExD,QAAQ,CAACM,MAAO;oBACvB6G,QAAQ,EAAE7D,YAAa;oBACvBoC,SAAS,EAAC,uHAAuH;oBACjIsC,QAAQ;oBAAArC,QAAA,gBAERxH,OAAA;sBAAQqF,KAAK,EAAC,EAAE;sBAAAmC,QAAA,EAAC;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EACrC/F,QAAQ,CAACK,KAAK,KAAK,SAAS,iBAC3BlC,OAAA,CAAAE,SAAA;sBAAAsH,QAAA,gBACExH,OAAA;wBAAQqF,KAAK,EAAC,GAAG;wBAAAmC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5B5H,OAAA;wBAAQqF,KAAK,EAAC,GAAG;wBAAAmC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5B5H,OAAA;wBAAQqF,KAAK,EAAC,GAAG;wBAAAmC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5B5H,OAAA;wBAAQqF,KAAK,EAAC,GAAG;wBAAAmC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5B5H,OAAA;wBAAQqF,KAAK,EAAC,GAAG;wBAAAmC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5B5H,OAAA;wBAAQqF,KAAK,EAAC,GAAG;wBAAAmC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5B5H,OAAA;wBAAQqF,KAAK,EAAC,GAAG;wBAAAmC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA,eAC5B,CACH,EACA/F,QAAQ,CAACK,KAAK,KAAK,WAAW,iBAC7BlC,OAAA,CAAAE,SAAA;sBAAAsH,QAAA,gBACExH,OAAA;wBAAQqF,KAAK,EAAC,GAAG;wBAAAmC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5B5H,OAAA;wBAAQqF,KAAK,EAAC,GAAG;wBAAAmC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5B5H,OAAA;wBAAQqF,KAAK,EAAC,GAAG;wBAAAmC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5B5H,OAAA;wBAAQqF,KAAK,EAAC,GAAG;wBAAAmC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA,eAC5B,CACH,EACA/F,QAAQ,CAACK,KAAK,KAAK,SAAS,iBAC3BlC,OAAA,CAAAE,SAAA;sBAAAsH,QAAA,gBACExH,OAAA;wBAAQqF,KAAK,EAAC,GAAG;wBAAAmC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC5B5H,OAAA;wBAAQqF,KAAK,EAAC,GAAG;wBAAAmC,QAAA,EAAC;sBAAC;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA,eAC5B,CACH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACN5H,OAAA;kBAAAwH,QAAA,gBACExH,OAAA;oBAAOuH,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACpF5H,OAAA;oBACE4G,IAAI,EAAC,KAAK;oBACV7E,IAAI,EAAC,aAAa;oBAClBsD,KAAK,EAAExD,QAAQ,CAACO,WAAY;oBAC5B4G,QAAQ,EAAE7D,YAAa;oBACvBoC,SAAS,EAAC,uHAAuH;oBACjIqC,WAAW,EAAC,oBAAoB;oBAChCE,SAAS,EAAC;kBAAI;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,eAGD5H,OAAA;cAAKuH,SAAS,EAAC,wFAAwF;cAAAC,QAAA,gBACrGxH,OAAA;gBAAIuH,SAAS,EAAC,wDAAwD;gBAAAC,QAAA,gBACpExH,OAAA;kBAAMuH,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,qBAElC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EAEJ9E,gBAAgB,IAAIA,gBAAgB,CAACiH,MAAM,KAAK,QAAQ;cAAA;cACvD;cACA/J,OAAA;gBAAKuH,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBAExBxH,OAAA;kBAAKuH,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChDxH,OAAA;oBAAAwH,QAAA,gBACExH,OAAA;sBAAIuH,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAE1E,gBAAgB,CAACkH,SAAS,MAAAtJ,qBAAA,GAAIoC,gBAAgB,CAACmH,IAAI,cAAAvJ,qBAAA,uBAArBA,qBAAA,CAAuBwJ,KAAK,KAAI;oBAAc;sBAAAzC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACnI5H,OAAA;sBAAGuH,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,eAC/BxH,OAAA;wBAAMuH,SAAS,EAAC,0BAA0B;wBAAAC,QAAA,gBACxCxH,OAAA;0BAAMuH,SAAS,EAAC;wBAAsD;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,uBAEhF;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACN5H,OAAA;oBAAKuH,SAAS,EAAC,YAAY;oBAAAC,QAAA,eACzBxH,OAAA;sBAAMuH,SAAS,EAAC,yHAAyH;sBAAAC,QAAA,EAAC;oBAE1I;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN5H,OAAA;kBAAKuH,SAAS,EAAC,iFAAiF;kBAAAC,QAAA,gBAC9FxH,OAAA;oBAAIuH,SAAS,EAAC,4DAA4D;oBAAAC,QAAA,gBACxExH,OAAA;sBAAMuH,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,yBAElC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL5H,OAAA;oBAAKuH,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,gBACpDxH,OAAA;sBAAAwH,QAAA,gBACExH,OAAA;wBAAGuH,SAAS,EAAC,+CAA+C;wBAAAC,QAAA,EAAC;sBAAU;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC3E5H,OAAA;wBAAGuH,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,EAC/C1E,gBAAgB,CAACqH,SAAS,GACzB,IAAIX,IAAI,CAAC1G,gBAAgB,CAACqH,SAAS,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;0BAC/DC,OAAO,EAAE,OAAO;0BAChBC,IAAI,EAAE,SAAS;0BACfC,KAAK,EAAE,OAAO;0BACdC,GAAG,EAAE;wBACP,CAAC,CAAC,GACF,IAAIhB,IAAI,CAAC1G,gBAAgB,CAAC2H,SAAS,CAAC,CAACL,kBAAkB,CAAC,OAAO,EAAE;0BAC/DC,OAAO,EAAE,OAAO;0BAChBC,IAAI,EAAE,SAAS;0BACfC,KAAK,EAAE,OAAO;0BACdC,GAAG,EAAE;wBACP,CAAC;sBAAC;wBAAA/C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAEH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC,eACN5H,OAAA;sBAAAwH,QAAA,gBACExH,OAAA;wBAAGuH,SAAS,EAAC,+CAA+C;wBAAAC,QAAA,EAAC;sBAAU;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC3E5H,OAAA;wBAAGuH,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,EAC9C,IAAIgC,IAAI,CAAC1G,gBAAgB,CAAC4H,OAAO,CAAC,CAACN,kBAAkB,CAAC,OAAO,EAAE;0BAC9DC,OAAO,EAAE,OAAO;0BAChBC,IAAI,EAAE,SAAS;0BACfC,KAAK,EAAE,OAAO;0BACdC,GAAG,EAAE;wBACP,CAAC;sBAAC;wBAAA/C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN5H,OAAA;kBAAKuH,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,gBACpDxH,OAAA;oBAAKuH,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,eACvExH,OAAA;sBAAKuH,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,gBAChDxH,OAAA;wBAAAwH,QAAA,gBACExH,OAAA;0BAAGuH,SAAS,EAAC,+CAA+C;0BAAAC,QAAA,EAAC;wBAAc;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eAC/E5H,OAAA;0BAAGuH,SAAS,EAAC,iCAAiC;0BAAAC,QAAA,GAC3C1E,gBAAgB,CAAC6H,QAAQ,MAAAhK,sBAAA,GAAImC,gBAAgB,CAACmH,IAAI,cAAAtJ,sBAAA,uBAArBA,sBAAA,CAAuBgK,QAAQ,KAAI,CAAC,EAAC,QAAM,EAAC,CAAC7H,gBAAgB,CAAC6H,QAAQ,MAAA/J,sBAAA,GAAIkC,gBAAgB,CAACmH,IAAI,cAAArJ,sBAAA,uBAArBA,sBAAA,CAAuB+J,QAAQ,KAAI,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE;wBAAA;0BAAAlD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3J,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC,eACN5H,OAAA;wBAAKuH,SAAS,EAAC,eAAe;wBAAAC,QAAA,eAC5BxH,OAAA;0BAAMuH,SAAS,EAAC,UAAU;0BAAAC,QAAA,EAAC;wBAAE;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN5H,OAAA;oBAAKuH,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,eACvExH,OAAA;sBAAKuH,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,gBAChDxH,OAAA;wBAAAwH,QAAA,gBACExH,OAAA;0BAAGuH,SAAS,EAAC,+CAA+C;0BAAAC,QAAA,EAAC;wBAAW;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eAC5E5H,OAAA;0BAAGuH,SAAS,EAAC,kCAAkC;0BAAAC,QAAA,GAC5C,CAAC1E,gBAAgB,CAAC8H,MAAM,IAAI9H,gBAAgB,CAAC+H,eAAe,IAAI,CAAC,EAAE1B,cAAc,CAAC,CAAC,EAAC,MACvF;wBAAA;0BAAA1B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC,eACN5H,OAAA;wBAAKuH,SAAS,EAAC,gBAAgB;wBAAAC,QAAA,eAC7BxH,OAAA;0BAAMuH,SAAS,EAAC,UAAU;0BAAAC,QAAA,EAAC;wBAAE;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN5H,OAAA;oBAAKuH,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,eACvExH,OAAA;sBAAKuH,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,gBAChDxH,OAAA;wBAAAwH,QAAA,gBACExH,OAAA;0BAAGuH,SAAS,EAAC,+CAA+C;0BAAAC,QAAA,EAAC;wBAAc;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eAC/E5H,OAAA;0BAAGuH,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,GAC7C,CAAC,MAAM;4BACN,MAAMsD,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,IAAI,CAAC,CAAC,IAAIzB,IAAI,CAAC1G,gBAAgB,CAAC4H,OAAO,CAAC,GAAG,IAAIlB,IAAI,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;4BAClH,OAAOsB,QAAQ;0BACjB,CAAC,EAAE,CAAC,EAAC,OACP;wBAAA;0BAAArD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC,eACN5H,OAAA;wBAAKuH,SAAS,EAAC,iBAAiB;wBAAAC,QAAA,eAC9BxH,OAAA;0BAAMuH,SAAS,EAAC,UAAU;0BAAAC,QAAA,EAAC;wBAAC;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN5H,OAAA;oBAAKuH,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,eACvExH,OAAA;sBAAKuH,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,gBAChDxH,OAAA;wBAAAwH,QAAA,gBACExH,OAAA;0BAAGuH,SAAS,EAAC,+CAA+C;0BAAAC,QAAA,EAAC;wBAAU;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eAC3E5H,OAAA;0BAAGuH,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,GAC7C,CAAC1E,gBAAgB,CAAC6H,QAAQ,MAAA9J,sBAAA,GAAIiC,gBAAgB,CAACmH,IAAI,cAAApJ,sBAAA,uBAArBA,sBAAA,CAAuB8J,QAAQ,KAAI,CAAC,IAAI,EAAE,EAAC,OAC5E;wBAAA;0BAAAlD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC,eACN5H,OAAA;wBAAKuH,SAAS,EAAC,iBAAiB;wBAAAC,QAAA,eAC9BxH,OAAA;0BAAMuH,SAAS,EAAC,UAAU;0BAAAC,QAAA,EAAC;wBAAE;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN5H,OAAA;kBAAKuH,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,gBAC7DxH,OAAA;oBAAKuH,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrDxH,OAAA;sBAAGuH,SAAS,EAAC,qCAAqC;sBAAAC,QAAA,EAAC;oBAAqB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAC5E5H,OAAA;sBAAGuH,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EACjC,CAAC0D,sBAAA,IAAM;wBACN,MAAMP,QAAQ,GAAG7H,gBAAgB,CAAC6H,QAAQ,MAAAO,sBAAA,GAAIpI,gBAAgB,CAACmH,IAAI,cAAAiB,sBAAA,uBAArBA,sBAAA,CAAuBP,QAAQ,KAAI,CAAC;wBAClF,MAAMQ,SAAS,GAAGR,QAAQ,GAAG,EAAE;wBAC/B,MAAMG,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,IAAI,CAAC,CAAC,IAAIzB,IAAI,CAAC1G,gBAAgB,CAAC4H,OAAO,CAAC,GAAG,IAAIlB,IAAI,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;wBAClH,MAAM4B,QAAQ,GAAGD,SAAS,GAAGL,QAAQ;wBACrC,MAAMO,UAAU,GAAGN,IAAI,CAACO,GAAG,CAAC,GAAG,EAAEP,IAAI,CAACC,GAAG,CAAC,CAAC,EAAGI,QAAQ,GAAGD,SAAS,GAAI,GAAG,CAAC,CAAC;wBAC3E,OAAQ,GAAEE,UAAU,CAACE,OAAO,CAAC,CAAC,CAAE,aAAY;sBAC9C,CAAC,EAAE;oBAAC;sBAAA9D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACN5H,OAAA;oBAAKuH,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,eAClDxH,OAAA;sBACEuH,SAAS,EAAC,0FAA0F;sBACpGW,KAAK,EAAE;wBACLC,KAAK,EAAG,GAAE,CAACqD,sBAAA,IAAM;0BACf,MAAMb,QAAQ,GAAG7H,gBAAgB,CAAC6H,QAAQ,MAAAa,sBAAA,GAAI1I,gBAAgB,CAACmH,IAAI,cAAAuB,sBAAA,uBAArBA,sBAAA,CAAuBb,QAAQ,KAAI,CAAC;0BAClF,MAAMQ,SAAS,GAAGR,QAAQ,GAAG,EAAE;0BAC/B,MAAMG,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,IAAI,CAAC,CAAC,IAAIzB,IAAI,CAAC1G,gBAAgB,CAAC4H,OAAO,CAAC,GAAG,IAAIlB,IAAI,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;0BAClH,MAAM4B,QAAQ,GAAGD,SAAS,GAAGL,QAAQ;0BACrC,MAAMO,UAAU,GAAGN,IAAI,CAACO,GAAG,CAAC,GAAG,EAAEP,IAAI,CAACC,GAAG,CAAC,CAAC,EAAGI,QAAQ,GAAGD,SAAS,GAAI,GAAG,CAAC,CAAC;0BAC3E,OAAOE,UAAU;wBACnB,CAAC,EAAE,CAAE;sBACP;oBAAE;sBAAA5D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN5H,OAAA;oBAAKuH,SAAS,EAAC,iDAAiD;oBAAAC,QAAA,gBAC9DxH,OAAA;sBAAAwH,QAAA,EAAM;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACpB5H,OAAA;sBAAAwH,QAAA,EACG,CAAC,MAAM;wBACN,MAAMsD,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,IAAI,CAAC,CAAC,IAAIzB,IAAI,CAAC1G,gBAAgB,CAAC4H,OAAO,CAAC,GAAG,IAAIlB,IAAI,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;wBAClH,IAAIsB,QAAQ,GAAG,CAAC,EAAE;0BAChB,OAAQ,GAAEA,QAAS,YAAW;wBAChC,CAAC,MAAM,IAAIA,QAAQ,GAAG,CAAC,EAAE;0BACvB,OAAQ,MAAKA,QAAS,YAAW;wBACnC,CAAC,MAAM;0BACL,OAAO,WAAW;wBACpB;sBACF,CAAC,EAAE;oBAAC;sBAAArD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN5H,OAAA;kBAAKuH,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBxH,OAAA;oBACE8H,OAAO,EAAEA,CAAA,KAAMtF,wBAAwB,CAAC,IAAI,CAAE;oBAC9C+E,SAAS,EAAC,uJAAuJ;oBAAAC,QAAA,EAClK;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT5H,OAAA;oBACE8H,OAAO,EAAEA,CAAA,KAAM;sBACb,MAAM4C,OAAO,GAAG,IAAIlB,IAAI,CAAC1G,gBAAgB,CAAC4H,OAAO,CAAC;sBAClD,MAAMe,KAAK,GAAG,IAAIjC,IAAI,CAAC,CAAC;sBACxB,MAAMsB,QAAQ,GAAGC,IAAI,CAACE,IAAI,CAAC,CAACP,OAAO,GAAGe,KAAK,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;sBAErE,IAAIX,QAAQ,IAAI,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;wBACjC7L,OAAO,CAACyM,OAAO,CAAE,gCAA+BZ,QAAS,gCAA+B,CAAC;sBAC3F,CAAC,MAAM,IAAIA,QAAQ,IAAI,CAAC,EAAE;wBACxB7L,OAAO,CAACoE,KAAK,CAAC,qFAAqF,CAAC;sBACtG,CAAC,MAAM;wBACLpE,OAAO,CAAC0K,IAAI,CAAE,mCAAkCmB,QAAS,aAAY,CAAC;sBACxE;oBACF,CAAE;oBACFvD,SAAS,EAAC,gGAAgG;oBAAAC,QAAA,EAC3G;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;cAAA;cAEN;cACA5H,OAAA;gBAAKuH,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BxH,OAAA;kBAAKuH,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBxH,OAAA;oBAAKuH,SAAS,EAAC,kFAAkF;oBAAAC,QAAA,eAC/FxH,OAAA;sBAAMuH,SAAS,EAAC,UAAU;sBAAAC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC,eACN5H,OAAA;oBAAIuH,SAAS,EAAC,sCAAsC;oBAAAC,QAAA,EAAC;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChF5H,OAAA;oBAAGuH,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAEnD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eAGN5H,OAAA;kBAAKuH,SAAS,EAAC,8DAA8D;kBAAAC,QAAA,gBAC3ExH,OAAA;oBAAKuH,SAAS,EAAC,mFAAmF;oBAAAC,QAAA,gBAChGxH,OAAA;sBAAKuH,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACrD5H,OAAA;sBAAIuH,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACvE5H,OAAA;sBAAGuH,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAuC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7E,CAAC,eACN5H,OAAA;oBAAKuH,SAAS,EAAC,sFAAsF;oBAAAC,QAAA,gBACnGxH,OAAA;sBAAKuH,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACtD5H,OAAA;sBAAIuH,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAAkB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACxE5H,OAAA;sBAAGuH,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAkC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxE,CAAC,eACN5H,OAAA;oBAAKuH,SAAS,EAAC,uFAAuF;oBAAAC,QAAA,gBACpGxH,OAAA;sBAAKuH,SAAS,EAAC,+BAA+B;sBAAAC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACvD5H,OAAA;sBAAIuH,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACvE5H,OAAA;sBAAGuH,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAA8B;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE,CAAC,eACN5H,OAAA;oBAAKuH,SAAS,EAAC,sFAAsF;oBAAAC,QAAA,gBACnGxH,OAAA;sBAAKuH,SAAS,EAAC,+BAA+B;sBAAAC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACvD5H,OAAA;sBAAIuH,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAC;oBAAiB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACvE5H,OAAA;sBAAGuH,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAA6B;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN5H,OAAA;kBAAKuH,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBxH,OAAA;oBACE8H,OAAO,EAAEA,CAAA,KAAMtF,wBAAwB,CAAC,IAAI,CAAE;oBAC9C+E,SAAS,EAAC,2MAA2M;oBAAAC,QAAA,EACtN;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT5H,OAAA;oBAAGuH,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAErC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGN5H,OAAA;cAAKuH,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAC5C,CAAC/F,IAAI,gBACJzB,OAAA;gBACE8H,OAAO,EAAEA,CAAA,KAAM;kBACb;kBACAhG,WAAW,CAAC;oBACVC,IAAI,EAAE,CAAAd,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEc,IAAI,KAAI,EAAE;oBAC7BC,KAAK,EAAE,CAAAf,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEe,KAAK,KAAI,EAAE;oBAC/BC,MAAM,EAAE,CAAAhB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEgB,MAAM,KAAI,EAAE;oBACjCE,MAAM,EAAE,CAAAlB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE+D,KAAK,KAAI,EAAE;oBAChC9C,KAAK,EAAE,CAAAjB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEiB,KAAK,KAAI,EAAE;oBAC/BE,WAAW,EAAE,CAAAnB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEmB,WAAW,KAAI;kBAC3C,CAAC,CAAC;kBACFV,OAAO,CAAC,IAAI,CAAC;gBACf,CAAE;gBACF6F,SAAS,EAAC,0GAA0G;gBAAAC,QAAA,EACrH;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,gBAET5H,OAAA,CAAAE,SAAA;gBAAAsH,QAAA,gBACExH,OAAA;kBACE8H,OAAO,EAAEvC,cAAe;kBACxBgC,SAAS,EAAC,0GAA0G;kBAAAC,QAAA,EACrH;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT5H,OAAA;kBACE8H,OAAO,EAAEtC,YAAa;kBACtB+B,SAAS,EAAC,4GAA4G;kBAAAC,QAAA,EACvH;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAET5H,OAAA;kBACE8H,OAAO,EAAEA,CAAA,KAAM;oBACbhD,OAAO,CAACY,GAAG,CAAC,8BAA8B,EAAE7D,QAAQ,CAAC;oBACrDiD,OAAO,CAACY,GAAG,CAAC,iCAAiC,EAAEzE,WAAW,CAAC;oBAC3D0K,KAAK,CAAE,aAAYC,IAAI,CAACC,SAAS,CAAChK,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAE,EAAC,CAAC;kBACzD,CAAE;kBACF0F,SAAS,EAAC,kHAAkH;kBAAAC,QAAA,EAC7H;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA,eACT;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5H,OAAA;MACE4G,IAAI,EAAC,MAAM;MACXkC,EAAE,EAAC,mBAAmB;MACtBC,MAAM,EAAC,SAAS;MAChBC,QAAQ,EAAEvC,iBAAkB;MAC5ByB,KAAK,EAAE;QAAE4D,OAAO,EAAE;MAAO;IAAE;MAAArE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,eAGF5H,OAAA,CAACd,KAAK;MACJgL,KAAK,EAAC,sBAAsB;MAC5B6B,IAAI,EAAEtJ,oBAAqB;MAC3BuJ,IAAI,EAAEzF,wBAAyB;MAC/B0F,QAAQ,EAAEA,CAAA,KAAM;QACdvJ,uBAAuB,CAAC,KAAK,CAAC;QAC9BE,qBAAqB,CAAC,IAAI,CAAC;MAC7B,CAAE;MACFsJ,MAAM,EAAC,SAAS;MAChBC,UAAU,EAAC,QAAQ;MAAA3E,QAAA,gBAEnBxH,OAAA;QAAAwH,QAAA,GAAG,gDAC6C,eAAAxH,OAAA;UAAAwH,QAAA,EAAS7E;QAAkB;UAAA8E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,KACrF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJ5H,OAAA;QAAGuH,SAAS,EAAC,8BAA8B;QAAAC,QAAA,EAAC;MAE5C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGR5H,OAAA,CAACH,iBAAiB;MAChBuM,MAAM,EAAE7J,qBAAsB;MAC9B8J,OAAO,EAAEA,CAAA,KAAM7J,wBAAwB,CAAC,KAAK,CAAE;MAC/C8J,SAAS,EAAEA,CAAA,KAAM;QACf9J,wBAAwB,CAAC,KAAK,CAAC;QAC/B;QACAuC,WAAW,CAAC,CAAC;QACb9F,OAAO,CAACkE,OAAO,CAAC,oCAAoC,CAAC;MACvD;IAAE;MAAAsE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEC,CAAC;AAEV,CAAC;AAACxH,EAAA,CA/+BID,OAAO;EAAA,QAC8BL,WAAW,EAmBnCT,WAAW,EACCC,WAAW;AAAA;AAAAiN,EAAA,GArBpCpM,OAAO;AAi/Bb,eAAeA,OAAO;AAAC,IAAAoM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}