{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport useState from \"rc-util/es/hooks/useState\";\nimport * as React from 'react';\nimport { STEP_ACTIVATED, STEP_ACTIVE, STEP_NONE, STEP_PREPARE, STEP_PREPARED, STEP_START } from \"../interface\";\nimport useIsomorphicLayoutEffect from \"./useIsomorphicLayoutEffect\";\nimport useNextFrame from \"./useNextFrame\";\nvar FULL_STEP_QUEUE = [STEP_PREPARE, STEP_START, STEP_ACTIVE, STEP_ACTIVATED];\nvar SIMPLE_STEP_QUEUE = [STEP_PREPARE, STEP_PREPARED];\n\n/** Skip current step */\nexport var SkipStep = false;\n/** Current step should be update in */\nexport var DoStep = true;\nexport function isActive(step) {\n  return step === STEP_ACTIVE || step === STEP_ACTIVATED;\n}\nexport default (function (status, prepareOnly, callback) {\n  var _useState = useState(STEP_NONE),\n    _useState2 = _slicedToArray(_useState, 2),\n    step = _useState2[0],\n    setStep = _useState2[1];\n  var _useNextFrame = useNextFrame(),\n    _useNextFrame2 = _slicedToArray(_useNextFrame, 2),\n    nextFrame = _useNextFrame2[0],\n    cancelNextFrame = _useNextFrame2[1];\n  function startQueue() {\n    setStep(STEP_PREPARE, true);\n  }\n  var STEP_QUEUE = prepareOnly ? SIMPLE_STEP_QUEUE : FULL_STEP_QUEUE;\n  useIsomorphicLayoutEffect(function () {\n    if (step !== STEP_NONE && step !== STEP_ACTIVATED) {\n      var index = STEP_QUEUE.indexOf(step);\n      var nextStep = STEP_QUEUE[index + 1];\n      var result = callback(step);\n      if (result === SkipStep) {\n        // Skip when no needed\n        setStep(nextStep, true);\n      } else if (nextStep) {\n        // Do as frame for step update\n        nextFrame(function (info) {\n          function doNext() {\n            // Skip since current queue is ood\n            if (info.isCanceled()) return;\n            setStep(nextStep, true);\n          }\n          if (result === true) {\n            doNext();\n          } else {\n            // Only promise should be async\n            Promise.resolve(result).then(doNext);\n          }\n        });\n      }\n    }\n  }, [status, step]);\n  React.useEffect(function () {\n    return function () {\n      cancelNextFrame();\n    };\n  }, []);\n  return [startQueue, step];\n});", "map": {"version": 3, "names": ["_slicedToArray", "useState", "React", "STEP_ACTIVATED", "STEP_ACTIVE", "STEP_NONE", "STEP_PREPARE", "STEP_PREPARED", "STEP_START", "useIsomorphicLayoutEffect", "useNextFrame", "FULL_STEP_QUEUE", "SIMPLE_STEP_QUEUE", "SkipStep", "DoStep", "isActive", "step", "status", "prepareOnly", "callback", "_useState", "_useState2", "setStep", "_useNextFrame", "_useNextFrame2", "next<PERSON><PERSON><PERSON>", "cancelNextFrame", "startQueue", "STEP_QUEUE", "index", "indexOf", "nextStep", "result", "info", "doNext", "isCanceled", "Promise", "resolve", "then", "useEffect"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-motion/es/hooks/useStepQueue.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport useState from \"rc-util/es/hooks/useState\";\nimport * as React from 'react';\nimport { STEP_ACTIVATED, STEP_ACTIVE, STEP_NONE, STEP_PREPARE, STEP_PREPARED, STEP_START } from \"../interface\";\nimport useIsomorphicLayoutEffect from \"./useIsomorphicLayoutEffect\";\nimport useNextFrame from \"./useNextFrame\";\nvar FULL_STEP_QUEUE = [STEP_PREPARE, STEP_START, STEP_ACTIVE, STEP_ACTIVATED];\nvar SIMPLE_STEP_QUEUE = [STEP_PREPARE, STEP_PREPARED];\n\n/** Skip current step */\nexport var SkipStep = false;\n/** Current step should be update in */\nexport var DoStep = true;\nexport function isActive(step) {\n  return step === STEP_ACTIVE || step === STEP_ACTIVATED;\n}\nexport default (function (status, prepareOnly, callback) {\n  var _useState = useState(STEP_NONE),\n    _useState2 = _slicedToArray(_useState, 2),\n    step = _useState2[0],\n    setStep = _useState2[1];\n  var _useNextFrame = useNextFrame(),\n    _useNextFrame2 = _slicedToArray(_useNextFrame, 2),\n    nextFrame = _useNextFrame2[0],\n    cancelNextFrame = _useNextFrame2[1];\n  function startQueue() {\n    setStep(STEP_PREPARE, true);\n  }\n  var STEP_QUEUE = prepareOnly ? SIMPLE_STEP_QUEUE : FULL_STEP_QUEUE;\n  useIsomorphicLayoutEffect(function () {\n    if (step !== STEP_NONE && step !== STEP_ACTIVATED) {\n      var index = STEP_QUEUE.indexOf(step);\n      var nextStep = STEP_QUEUE[index + 1];\n      var result = callback(step);\n      if (result === SkipStep) {\n        // Skip when no needed\n        setStep(nextStep, true);\n      } else if (nextStep) {\n        // Do as frame for step update\n        nextFrame(function (info) {\n          function doNext() {\n            // Skip since current queue is ood\n            if (info.isCanceled()) return;\n            setStep(nextStep, true);\n          }\n          if (result === true) {\n            doNext();\n          } else {\n            // Only promise should be async\n            Promise.resolve(result).then(doNext);\n          }\n        });\n      }\n    }\n  }, [status, step]);\n  React.useEffect(function () {\n    return function () {\n      cancelNextFrame();\n    };\n  }, []);\n  return [startQueue, step];\n});"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,EAAEC,WAAW,EAAEC,SAAS,EAAEC,YAAY,EAAEC,aAAa,EAAEC,UAAU,QAAQ,cAAc;AAC9G,OAAOC,yBAAyB,MAAM,6BAA6B;AACnE,OAAOC,YAAY,MAAM,gBAAgB;AACzC,IAAIC,eAAe,GAAG,CAACL,YAAY,EAAEE,UAAU,EAAEJ,WAAW,EAAED,cAAc,CAAC;AAC7E,IAAIS,iBAAiB,GAAG,CAACN,YAAY,EAAEC,aAAa,CAAC;;AAErD;AACA,OAAO,IAAIM,QAAQ,GAAG,KAAK;AAC3B;AACA,OAAO,IAAIC,MAAM,GAAG,IAAI;AACxB,OAAO,SAASC,QAAQA,CAACC,IAAI,EAAE;EAC7B,OAAOA,IAAI,KAAKZ,WAAW,IAAIY,IAAI,KAAKb,cAAc;AACxD;AACA,gBAAgB,UAAUc,MAAM,EAAEC,WAAW,EAAEC,QAAQ,EAAE;EACvD,IAAIC,SAAS,GAAGnB,QAAQ,CAACI,SAAS,CAAC;IACjCgB,UAAU,GAAGrB,cAAc,CAACoB,SAAS,EAAE,CAAC,CAAC;IACzCJ,IAAI,GAAGK,UAAU,CAAC,CAAC,CAAC;IACpBC,OAAO,GAAGD,UAAU,CAAC,CAAC,CAAC;EACzB,IAAIE,aAAa,GAAGb,YAAY,CAAC,CAAC;IAChCc,cAAc,GAAGxB,cAAc,CAACuB,aAAa,EAAE,CAAC,CAAC;IACjDE,SAAS,GAAGD,cAAc,CAAC,CAAC,CAAC;IAC7BE,eAAe,GAAGF,cAAc,CAAC,CAAC,CAAC;EACrC,SAASG,UAAUA,CAAA,EAAG;IACpBL,OAAO,CAAChB,YAAY,EAAE,IAAI,CAAC;EAC7B;EACA,IAAIsB,UAAU,GAAGV,WAAW,GAAGN,iBAAiB,GAAGD,eAAe;EAClEF,yBAAyB,CAAC,YAAY;IACpC,IAAIO,IAAI,KAAKX,SAAS,IAAIW,IAAI,KAAKb,cAAc,EAAE;MACjD,IAAI0B,KAAK,GAAGD,UAAU,CAACE,OAAO,CAACd,IAAI,CAAC;MACpC,IAAIe,QAAQ,GAAGH,UAAU,CAACC,KAAK,GAAG,CAAC,CAAC;MACpC,IAAIG,MAAM,GAAGb,QAAQ,CAACH,IAAI,CAAC;MAC3B,IAAIgB,MAAM,KAAKnB,QAAQ,EAAE;QACvB;QACAS,OAAO,CAACS,QAAQ,EAAE,IAAI,CAAC;MACzB,CAAC,MAAM,IAAIA,QAAQ,EAAE;QACnB;QACAN,SAAS,CAAC,UAAUQ,IAAI,EAAE;UACxB,SAASC,MAAMA,CAAA,EAAG;YAChB;YACA,IAAID,IAAI,CAACE,UAAU,CAAC,CAAC,EAAE;YACvBb,OAAO,CAACS,QAAQ,EAAE,IAAI,CAAC;UACzB;UACA,IAAIC,MAAM,KAAK,IAAI,EAAE;YACnBE,MAAM,CAAC,CAAC;UACV,CAAC,MAAM;YACL;YACAE,OAAO,CAACC,OAAO,CAACL,MAAM,CAAC,CAACM,IAAI,CAACJ,MAAM,CAAC;UACtC;QACF,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EAAE,CAACjB,MAAM,EAAED,IAAI,CAAC,CAAC;EAClBd,KAAK,CAACqC,SAAS,CAAC,YAAY;IAC1B,OAAO,YAAY;MACjBb,eAAe,CAAC,CAAC;IACnB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,CAACC,UAAU,EAAEX,IAAI,CAAC;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}