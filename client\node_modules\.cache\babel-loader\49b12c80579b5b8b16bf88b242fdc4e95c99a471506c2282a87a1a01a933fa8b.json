{"ast": null, "code": "import React from 'react';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport classNames from 'classnames';\nfunction Star(props, ref) {\n  var disabled = props.disabled,\n    prefixCls = props.prefixCls,\n    character = props.character,\n    characterRender = props.characterRender,\n    index = props.index,\n    count = props.count,\n    value = props.value,\n    allowHalf = props.allowHalf,\n    focused = props.focused,\n    onHover = props.onHover,\n    onClick = props.onClick;\n  // =========================== Events ===========================\n  var onInternalHover = function onInternalHover(e) {\n    onHover(e, index);\n  };\n  var onInternalClick = function onInternalClick(e) {\n    onClick(e, index);\n  };\n  var onInternalKeyDown = function onInternalKeyDown(e) {\n    if (e.keyCode === KeyCode.ENTER) {\n      onClick(e, index);\n    }\n  };\n  // =========================== Render ===========================\n  // >>>>> ClassName\n  var starValue = index + 1;\n  var classNameList = new Set([prefixCls]);\n  // TODO: Current we just refactor from CC to FC. This logic seems can be optimized.\n  if (value === 0 && index === 0 && focused) {\n    classNameList.add(\"\".concat(prefixCls, \"-focused\"));\n  } else if (allowHalf && value + 0.5 >= starValue && value < starValue) {\n    classNameList.add(\"\".concat(prefixCls, \"-half\"));\n    classNameList.add(\"\".concat(prefixCls, \"-active\"));\n    if (focused) {\n      classNameList.add(\"\".concat(prefixCls, \"-focused\"));\n    }\n  } else {\n    if (starValue <= value) {\n      classNameList.add(\"\".concat(prefixCls, \"-full\"));\n    } else {\n      classNameList.add(\"\".concat(prefixCls, \"-zero\"));\n    }\n    if (starValue === value && focused) {\n      classNameList.add(\"\".concat(prefixCls, \"-focused\"));\n    }\n  }\n  // >>>>> Node\n  var characterNode = typeof character === 'function' ? character(props) : character;\n  var start = /*#__PURE__*/React.createElement(\"li\", {\n    className: classNames(Array.from(classNameList)),\n    ref: ref\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    onClick: disabled ? null : onInternalClick,\n    onKeyDown: disabled ? null : onInternalKeyDown,\n    onMouseMove: disabled ? null : onInternalHover,\n    role: \"radio\",\n    \"aria-checked\": value > index ? 'true' : 'false',\n    \"aria-posinset\": index + 1,\n    \"aria-setsize\": count,\n    tabIndex: disabled ? -1 : 0\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-first\")\n  }, characterNode), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-second\")\n  }, characterNode)));\n  if (characterRender) {\n    start = characterRender(start, props);\n  }\n  return start;\n}\nexport default /*#__PURE__*/React.forwardRef(Star);", "map": {"version": 3, "names": ["React", "KeyCode", "classNames", "Star", "props", "ref", "disabled", "prefixCls", "character", "character<PERSON><PERSON>", "index", "count", "value", "allowHalf", "focused", "onHover", "onClick", "onInternalHover", "e", "onInternalClick", "onInternalKeyDown", "keyCode", "ENTER", "starValue", "classNameList", "Set", "add", "concat", "characterNode", "start", "createElement", "className", "Array", "from", "onKeyDown", "onMouseMove", "role", "tabIndex", "forwardRef"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-rate/es/Star.js"], "sourcesContent": ["import React from 'react';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport classNames from 'classnames';\nfunction Star(props, ref) {\n  var disabled = props.disabled,\n    prefixCls = props.prefixCls,\n    character = props.character,\n    characterRender = props.characterRender,\n    index = props.index,\n    count = props.count,\n    value = props.value,\n    allowHalf = props.allowHalf,\n    focused = props.focused,\n    onHover = props.onHover,\n    onClick = props.onClick;\n  // =========================== Events ===========================\n  var onInternalHover = function onInternalHover(e) {\n    onHover(e, index);\n  };\n  var onInternalClick = function onInternalClick(e) {\n    onClick(e, index);\n  };\n  var onInternalKeyDown = function onInternalKeyDown(e) {\n    if (e.keyCode === KeyCode.ENTER) {\n      onClick(e, index);\n    }\n  };\n  // =========================== Render ===========================\n  // >>>>> ClassName\n  var starValue = index + 1;\n  var classNameList = new Set([prefixCls]);\n  // TODO: Current we just refactor from CC to FC. This logic seems can be optimized.\n  if (value === 0 && index === 0 && focused) {\n    classNameList.add(\"\".concat(prefixCls, \"-focused\"));\n  } else if (allowHalf && value + 0.5 >= starValue && value < starValue) {\n    classNameList.add(\"\".concat(prefixCls, \"-half\"));\n    classNameList.add(\"\".concat(prefixCls, \"-active\"));\n    if (focused) {\n      classNameList.add(\"\".concat(prefixCls, \"-focused\"));\n    }\n  } else {\n    if (starValue <= value) {\n      classNameList.add(\"\".concat(prefixCls, \"-full\"));\n    } else {\n      classNameList.add(\"\".concat(prefixCls, \"-zero\"));\n    }\n    if (starValue === value && focused) {\n      classNameList.add(\"\".concat(prefixCls, \"-focused\"));\n    }\n  }\n  // >>>>> Node\n  var characterNode = typeof character === 'function' ? character(props) : character;\n  var start = /*#__PURE__*/React.createElement(\"li\", {\n    className: classNames(Array.from(classNameList)),\n    ref: ref\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    onClick: disabled ? null : onInternalClick,\n    onKeyDown: disabled ? null : onInternalKeyDown,\n    onMouseMove: disabled ? null : onInternalHover,\n    role: \"radio\",\n    \"aria-checked\": value > index ? 'true' : 'false',\n    \"aria-posinset\": index + 1,\n    \"aria-setsize\": count,\n    tabIndex: disabled ? -1 : 0\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-first\")\n  }, characterNode), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-second\")\n  }, characterNode)));\n  if (characterRender) {\n    start = characterRender(start, props);\n  }\n  return start;\n}\nexport default /*#__PURE__*/React.forwardRef(Star);"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,IAAIA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACxB,IAAIC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;IAC3BC,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC3BC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,eAAe,GAAGL,KAAK,CAACK,eAAe;IACvCC,KAAK,GAAGN,KAAK,CAACM,KAAK;IACnBC,KAAK,GAAGP,KAAK,CAACO,KAAK;IACnBC,KAAK,GAAGR,KAAK,CAACQ,KAAK;IACnBC,SAAS,GAAGT,KAAK,CAACS,SAAS;IAC3BC,OAAO,GAAGV,KAAK,CAACU,OAAO;IACvBC,OAAO,GAAGX,KAAK,CAACW,OAAO;IACvBC,OAAO,GAAGZ,KAAK,CAACY,OAAO;EACzB;EACA,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,CAAC,EAAE;IAChDH,OAAO,CAACG,CAAC,EAAER,KAAK,CAAC;EACnB,CAAC;EACD,IAAIS,eAAe,GAAG,SAASA,eAAeA,CAACD,CAAC,EAAE;IAChDF,OAAO,CAACE,CAAC,EAAER,KAAK,CAAC;EACnB,CAAC;EACD,IAAIU,iBAAiB,GAAG,SAASA,iBAAiBA,CAACF,CAAC,EAAE;IACpD,IAAIA,CAAC,CAACG,OAAO,KAAKpB,OAAO,CAACqB,KAAK,EAAE;MAC/BN,OAAO,CAACE,CAAC,EAAER,KAAK,CAAC;IACnB;EACF,CAAC;EACD;EACA;EACA,IAAIa,SAAS,GAAGb,KAAK,GAAG,CAAC;EACzB,IAAIc,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAClB,SAAS,CAAC,CAAC;EACxC;EACA,IAAIK,KAAK,KAAK,CAAC,IAAIF,KAAK,KAAK,CAAC,IAAII,OAAO,EAAE;IACzCU,aAAa,CAACE,GAAG,CAAC,EAAE,CAACC,MAAM,CAACpB,SAAS,EAAE,UAAU,CAAC,CAAC;EACrD,CAAC,MAAM,IAAIM,SAAS,IAAID,KAAK,GAAG,GAAG,IAAIW,SAAS,IAAIX,KAAK,GAAGW,SAAS,EAAE;IACrEC,aAAa,CAACE,GAAG,CAAC,EAAE,CAACC,MAAM,CAACpB,SAAS,EAAE,OAAO,CAAC,CAAC;IAChDiB,aAAa,CAACE,GAAG,CAAC,EAAE,CAACC,MAAM,CAACpB,SAAS,EAAE,SAAS,CAAC,CAAC;IAClD,IAAIO,OAAO,EAAE;MACXU,aAAa,CAACE,GAAG,CAAC,EAAE,CAACC,MAAM,CAACpB,SAAS,EAAE,UAAU,CAAC,CAAC;IACrD;EACF,CAAC,MAAM;IACL,IAAIgB,SAAS,IAAIX,KAAK,EAAE;MACtBY,aAAa,CAACE,GAAG,CAAC,EAAE,CAACC,MAAM,CAACpB,SAAS,EAAE,OAAO,CAAC,CAAC;IAClD,CAAC,MAAM;MACLiB,aAAa,CAACE,GAAG,CAAC,EAAE,CAACC,MAAM,CAACpB,SAAS,EAAE,OAAO,CAAC,CAAC;IAClD;IACA,IAAIgB,SAAS,KAAKX,KAAK,IAAIE,OAAO,EAAE;MAClCU,aAAa,CAACE,GAAG,CAAC,EAAE,CAACC,MAAM,CAACpB,SAAS,EAAE,UAAU,CAAC,CAAC;IACrD;EACF;EACA;EACA,IAAIqB,aAAa,GAAG,OAAOpB,SAAS,KAAK,UAAU,GAAGA,SAAS,CAACJ,KAAK,CAAC,GAAGI,SAAS;EAClF,IAAIqB,KAAK,GAAG,aAAa7B,KAAK,CAAC8B,aAAa,CAAC,IAAI,EAAE;IACjDC,SAAS,EAAE7B,UAAU,CAAC8B,KAAK,CAACC,IAAI,CAACT,aAAa,CAAC,CAAC;IAChDnB,GAAG,EAAEA;EACP,CAAC,EAAE,aAAaL,KAAK,CAAC8B,aAAa,CAAC,KAAK,EAAE;IACzCd,OAAO,EAAEV,QAAQ,GAAG,IAAI,GAAGa,eAAe;IAC1Ce,SAAS,EAAE5B,QAAQ,GAAG,IAAI,GAAGc,iBAAiB;IAC9Ce,WAAW,EAAE7B,QAAQ,GAAG,IAAI,GAAGW,eAAe;IAC9CmB,IAAI,EAAE,OAAO;IACb,cAAc,EAAExB,KAAK,GAAGF,KAAK,GAAG,MAAM,GAAG,OAAO;IAChD,eAAe,EAAEA,KAAK,GAAG,CAAC;IAC1B,cAAc,EAAEC,KAAK;IACrB0B,QAAQ,EAAE/B,QAAQ,GAAG,CAAC,CAAC,GAAG;EAC5B,CAAC,EAAE,aAAaN,KAAK,CAAC8B,aAAa,CAAC,KAAK,EAAE;IACzCC,SAAS,EAAE,EAAE,CAACJ,MAAM,CAACpB,SAAS,EAAE,QAAQ;EAC1C,CAAC,EAAEqB,aAAa,CAAC,EAAE,aAAa5B,KAAK,CAAC8B,aAAa,CAAC,KAAK,EAAE;IACzDC,SAAS,EAAE,EAAE,CAACJ,MAAM,CAACpB,SAAS,EAAE,SAAS;EAC3C,CAAC,EAAEqB,aAAa,CAAC,CAAC,CAAC;EACnB,IAAInB,eAAe,EAAE;IACnBoB,KAAK,GAAGpB,eAAe,CAACoB,KAAK,EAAEzB,KAAK,CAAC;EACvC;EACA,OAAOyB,KAAK;AACd;AACA,eAAe,aAAa7B,KAAK,CAACsC,UAAU,CAACnC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}