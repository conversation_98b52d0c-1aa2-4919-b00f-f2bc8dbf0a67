{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"visible\", \"onVisibleChange\", \"getContainer\", \"current\", \"movable\", \"minScale\", \"maxScale\", \"countRender\", \"closeIcon\", \"onChange\", \"onTransform\", \"toolbarRender\", \"imageRender\"],\n  _excluded2 = [\"src\"];\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport * as React from 'react';\nimport { useState } from 'react';\nimport { PreviewGroupContext } from \"./context\";\nimport usePreviewItems from \"./hooks/usePreviewItems\";\nimport Preview from \"./Preview\";\nvar Group = function Group(_ref) {\n  var _mergedItems$current;\n  var _ref$previewPrefixCls = _ref.previewPrefixCls,\n    previewPrefixCls = _ref$previewPrefixCls === void 0 ? 'rc-image-preview' : _ref$previewPrefixCls,\n    children = _ref.children,\n    _ref$icons = _ref.icons,\n    icons = _ref$icons === void 0 ? {} : _ref$icons,\n    items = _ref.items,\n    preview = _ref.preview,\n    fallback = _ref.fallback;\n  var _ref2 = _typeof(preview) === 'object' ? preview : {},\n    previewVisible = _ref2.visible,\n    onVisibleChange = _ref2.onVisibleChange,\n    getContainer = _ref2.getContainer,\n    currentIndex = _ref2.current,\n    movable = _ref2.movable,\n    minScale = _ref2.minScale,\n    maxScale = _ref2.maxScale,\n    countRender = _ref2.countRender,\n    closeIcon = _ref2.closeIcon,\n    onChange = _ref2.onChange,\n    onTransform = _ref2.onTransform,\n    toolbarRender = _ref2.toolbarRender,\n    imageRender = _ref2.imageRender,\n    dialogProps = _objectWithoutProperties(_ref2, _excluded);\n\n  // ========================== Items ===========================\n  var _usePreviewItems = usePreviewItems(items),\n    _usePreviewItems2 = _slicedToArray(_usePreviewItems, 2),\n    mergedItems = _usePreviewItems2[0],\n    register = _usePreviewItems2[1];\n\n  // ========================= Preview ==========================\n  // >>> Index\n  var _useMergedState = useMergedState(0, {\n      value: currentIndex\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    current = _useMergedState2[0],\n    setCurrent = _useMergedState2[1];\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    keepOpenIndex = _useState2[0],\n    setKeepOpenIndex = _useState2[1];\n\n  // >>> Image\n  var _ref3 = ((_mergedItems$current = mergedItems[current]) === null || _mergedItems$current === void 0 ? void 0 : _mergedItems$current.data) || {},\n    src = _ref3.src,\n    imgCommonProps = _objectWithoutProperties(_ref3, _excluded2);\n  // >>> Visible\n  var _useMergedState3 = useMergedState(!!previewVisible, {\n      value: previewVisible,\n      onChange: function onChange(val, prevVal) {\n        onVisibleChange === null || onVisibleChange === void 0 ? void 0 : onVisibleChange(val, prevVal, current);\n      }\n    }),\n    _useMergedState4 = _slicedToArray(_useMergedState3, 2),\n    isShowPreview = _useMergedState4[0],\n    setShowPreview = _useMergedState4[1];\n\n  // >>> Position\n  var _useState3 = useState(null),\n    _useState4 = _slicedToArray(_useState3, 2),\n    mousePosition = _useState4[0],\n    setMousePosition = _useState4[1];\n  var onPreviewFromImage = React.useCallback(function (id, mouseX, mouseY) {\n    var index = mergedItems.findIndex(function (item) {\n      return item.id === id;\n    });\n    setShowPreview(true);\n    setMousePosition({\n      x: mouseX,\n      y: mouseY\n    });\n    setCurrent(index < 0 ? 0 : index);\n    setKeepOpenIndex(true);\n  }, [mergedItems]);\n\n  // Reset current when reopen\n  React.useEffect(function () {\n    if (isShowPreview) {\n      if (!keepOpenIndex) {\n        setCurrent(0);\n      }\n    } else {\n      setKeepOpenIndex(false);\n    }\n  }, [isShowPreview]);\n\n  // ========================== Events ==========================\n  var onInternalChange = function onInternalChange(next, prev) {\n    setCurrent(next);\n    onChange === null || onChange === void 0 ? void 0 : onChange(next, prev);\n  };\n  var onPreviewClose = function onPreviewClose() {\n    setShowPreview(false);\n    setMousePosition(null);\n  };\n\n  // ========================= Context ==========================\n  var previewGroupContext = React.useMemo(function () {\n    return {\n      register: register,\n      onPreview: onPreviewFromImage\n    };\n  }, [register, onPreviewFromImage]);\n\n  // ========================== Render ==========================\n  return /*#__PURE__*/React.createElement(PreviewGroupContext.Provider, {\n    value: previewGroupContext\n  }, children, /*#__PURE__*/React.createElement(Preview, _extends({\n    \"aria-hidden\": !isShowPreview,\n    movable: movable,\n    visible: isShowPreview,\n    prefixCls: previewPrefixCls,\n    closeIcon: closeIcon,\n    onClose: onPreviewClose,\n    mousePosition: mousePosition,\n    imgCommonProps: imgCommonProps,\n    src: src,\n    fallback: fallback,\n    icons: icons,\n    minScale: minScale,\n    maxScale: maxScale,\n    getContainer: getContainer,\n    current: current,\n    count: mergedItems.length,\n    countRender: countRender,\n    onTransform: onTransform,\n    toolbarRender: toolbarRender,\n    imageRender: imageRender,\n    onChange: onInternalChange\n  }, dialogProps)));\n};\nexport default Group;", "map": {"version": 3, "names": ["_extends", "_slicedToArray", "_typeof", "_objectWithoutProperties", "_excluded", "_excluded2", "useMergedState", "React", "useState", "PreviewGroupContext", "usePreviewItems", "Preview", "Group", "_ref", "_mergedItems$current", "_ref$previewPrefixCls", "previewPrefixCls", "children", "_ref$icons", "icons", "items", "preview", "fallback", "_ref2", "previewVisible", "visible", "onVisibleChange", "getContainer", "currentIndex", "current", "movable", "minScale", "maxScale", "countRender", "closeIcon", "onChange", "onTransform", "toolbarRender", "imageRender", "dialogProps", "_usePreviewItems", "_usePreviewItems2", "mergedItems", "register", "_useMergedState", "value", "_useMergedState2", "setCurrent", "_useState", "_useState2", "keepOpenIndex", "setKeepOpenIndex", "_ref3", "data", "src", "imgCommonProps", "_useMergedState3", "val", "prevVal", "_useMergedState4", "isShowPreview", "setShowPreview", "_useState3", "_useState4", "mousePosition", "setMousePosition", "onPreviewFromImage", "useCallback", "id", "mouseX", "mouseY", "index", "findIndex", "item", "x", "y", "useEffect", "onInternalChange", "next", "prev", "onPreviewClose", "previewGroupContext", "useMemo", "onPreview", "createElement", "Provider", "prefixCls", "onClose", "count", "length"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-image/es/PreviewGroup.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"visible\", \"onVisibleChange\", \"getContainer\", \"current\", \"movable\", \"minScale\", \"maxScale\", \"countRender\", \"closeIcon\", \"onChange\", \"onTransform\", \"toolbarRender\", \"imageRender\"],\n  _excluded2 = [\"src\"];\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport * as React from 'react';\nimport { useState } from 'react';\nimport { PreviewGroupContext } from \"./context\";\nimport usePreviewItems from \"./hooks/usePreviewItems\";\nimport Preview from \"./Preview\";\nvar Group = function Group(_ref) {\n  var _mergedItems$current;\n  var _ref$previewPrefixCls = _ref.previewPrefixCls,\n    previewPrefixCls = _ref$previewPrefixCls === void 0 ? 'rc-image-preview' : _ref$previewPrefixCls,\n    children = _ref.children,\n    _ref$icons = _ref.icons,\n    icons = _ref$icons === void 0 ? {} : _ref$icons,\n    items = _ref.items,\n    preview = _ref.preview,\n    fallback = _ref.fallback;\n  var _ref2 = _typeof(preview) === 'object' ? preview : {},\n    previewVisible = _ref2.visible,\n    onVisibleChange = _ref2.onVisibleChange,\n    getContainer = _ref2.getContainer,\n    currentIndex = _ref2.current,\n    movable = _ref2.movable,\n    minScale = _ref2.minScale,\n    maxScale = _ref2.maxScale,\n    countRender = _ref2.countRender,\n    closeIcon = _ref2.closeIcon,\n    onChange = _ref2.onChange,\n    onTransform = _ref2.onTransform,\n    toolbarRender = _ref2.toolbarRender,\n    imageRender = _ref2.imageRender,\n    dialogProps = _objectWithoutProperties(_ref2, _excluded);\n\n  // ========================== Items ===========================\n  var _usePreviewItems = usePreviewItems(items),\n    _usePreviewItems2 = _slicedToArray(_usePreviewItems, 2),\n    mergedItems = _usePreviewItems2[0],\n    register = _usePreviewItems2[1];\n\n  // ========================= Preview ==========================\n  // >>> Index\n  var _useMergedState = useMergedState(0, {\n      value: currentIndex\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    current = _useMergedState2[0],\n    setCurrent = _useMergedState2[1];\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    keepOpenIndex = _useState2[0],\n    setKeepOpenIndex = _useState2[1];\n\n  // >>> Image\n  var _ref3 = ((_mergedItems$current = mergedItems[current]) === null || _mergedItems$current === void 0 ? void 0 : _mergedItems$current.data) || {},\n    src = _ref3.src,\n    imgCommonProps = _objectWithoutProperties(_ref3, _excluded2);\n  // >>> Visible\n  var _useMergedState3 = useMergedState(!!previewVisible, {\n      value: previewVisible,\n      onChange: function onChange(val, prevVal) {\n        onVisibleChange === null || onVisibleChange === void 0 ? void 0 : onVisibleChange(val, prevVal, current);\n      }\n    }),\n    _useMergedState4 = _slicedToArray(_useMergedState3, 2),\n    isShowPreview = _useMergedState4[0],\n    setShowPreview = _useMergedState4[1];\n\n  // >>> Position\n  var _useState3 = useState(null),\n    _useState4 = _slicedToArray(_useState3, 2),\n    mousePosition = _useState4[0],\n    setMousePosition = _useState4[1];\n  var onPreviewFromImage = React.useCallback(function (id, mouseX, mouseY) {\n    var index = mergedItems.findIndex(function (item) {\n      return item.id === id;\n    });\n    setShowPreview(true);\n    setMousePosition({\n      x: mouseX,\n      y: mouseY\n    });\n    setCurrent(index < 0 ? 0 : index);\n    setKeepOpenIndex(true);\n  }, [mergedItems]);\n\n  // Reset current when reopen\n  React.useEffect(function () {\n    if (isShowPreview) {\n      if (!keepOpenIndex) {\n        setCurrent(0);\n      }\n    } else {\n      setKeepOpenIndex(false);\n    }\n  }, [isShowPreview]);\n\n  // ========================== Events ==========================\n  var onInternalChange = function onInternalChange(next, prev) {\n    setCurrent(next);\n    onChange === null || onChange === void 0 ? void 0 : onChange(next, prev);\n  };\n  var onPreviewClose = function onPreviewClose() {\n    setShowPreview(false);\n    setMousePosition(null);\n  };\n\n  // ========================= Context ==========================\n  var previewGroupContext = React.useMemo(function () {\n    return {\n      register: register,\n      onPreview: onPreviewFromImage\n    };\n  }, [register, onPreviewFromImage]);\n\n  // ========================== Render ==========================\n  return /*#__PURE__*/React.createElement(PreviewGroupContext.Provider, {\n    value: previewGroupContext\n  }, children, /*#__PURE__*/React.createElement(Preview, _extends({\n    \"aria-hidden\": !isShowPreview,\n    movable: movable,\n    visible: isShowPreview,\n    prefixCls: previewPrefixCls,\n    closeIcon: closeIcon,\n    onClose: onPreviewClose,\n    mousePosition: mousePosition,\n    imgCommonProps: imgCommonProps,\n    src: src,\n    fallback: fallback,\n    icons: icons,\n    minScale: minScale,\n    maxScale: maxScale,\n    getContainer: getContainer,\n    current: current,\n    count: mergedItems.length,\n    countRender: countRender,\n    onTransform: onTransform,\n    toolbarRender: toolbarRender,\n    imageRender: imageRender,\n    onChange: onInternalChange\n  }, dialogProps)));\n};\nexport default Group;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,SAAS,EAAE,iBAAiB,EAAE,cAAc,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,aAAa,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,eAAe,EAAE,aAAa,CAAC;EACjMC,UAAU,GAAG,CAAC,KAAK,CAAC;AACtB,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,OAAO;AAChC,SAASC,mBAAmB,QAAQ,WAAW;AAC/C,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,OAAO,MAAM,WAAW;AAC/B,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAACC,IAAI,EAAE;EAC/B,IAAIC,oBAAoB;EACxB,IAAIC,qBAAqB,GAAGF,IAAI,CAACG,gBAAgB;IAC/CA,gBAAgB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,kBAAkB,GAAGA,qBAAqB;IAChGE,QAAQ,GAAGJ,IAAI,CAACI,QAAQ;IACxBC,UAAU,GAAGL,IAAI,CAACM,KAAK;IACvBA,KAAK,GAAGD,UAAU,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,UAAU;IAC/CE,KAAK,GAAGP,IAAI,CAACO,KAAK;IAClBC,OAAO,GAAGR,IAAI,CAACQ,OAAO;IACtBC,QAAQ,GAAGT,IAAI,CAACS,QAAQ;EAC1B,IAAIC,KAAK,GAAGrB,OAAO,CAACmB,OAAO,CAAC,KAAK,QAAQ,GAAGA,OAAO,GAAG,CAAC,CAAC;IACtDG,cAAc,GAAGD,KAAK,CAACE,OAAO;IAC9BC,eAAe,GAAGH,KAAK,CAACG,eAAe;IACvCC,YAAY,GAAGJ,KAAK,CAACI,YAAY;IACjCC,YAAY,GAAGL,KAAK,CAACM,OAAO;IAC5BC,OAAO,GAAGP,KAAK,CAACO,OAAO;IACvBC,QAAQ,GAAGR,KAAK,CAACQ,QAAQ;IACzBC,QAAQ,GAAGT,KAAK,CAACS,QAAQ;IACzBC,WAAW,GAAGV,KAAK,CAACU,WAAW;IAC/BC,SAAS,GAAGX,KAAK,CAACW,SAAS;IAC3BC,QAAQ,GAAGZ,KAAK,CAACY,QAAQ;IACzBC,WAAW,GAAGb,KAAK,CAACa,WAAW;IAC/BC,aAAa,GAAGd,KAAK,CAACc,aAAa;IACnCC,WAAW,GAAGf,KAAK,CAACe,WAAW;IAC/BC,WAAW,GAAGpC,wBAAwB,CAACoB,KAAK,EAAEnB,SAAS,CAAC;;EAE1D;EACA,IAAIoC,gBAAgB,GAAG9B,eAAe,CAACU,KAAK,CAAC;IAC3CqB,iBAAiB,GAAGxC,cAAc,CAACuC,gBAAgB,EAAE,CAAC,CAAC;IACvDE,WAAW,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IAClCE,QAAQ,GAAGF,iBAAiB,CAAC,CAAC,CAAC;;EAEjC;EACA;EACA,IAAIG,eAAe,GAAGtC,cAAc,CAAC,CAAC,EAAE;MACpCuC,KAAK,EAAEjB;IACT,CAAC,CAAC;IACFkB,gBAAgB,GAAG7C,cAAc,CAAC2C,eAAe,EAAE,CAAC,CAAC;IACrDf,OAAO,GAAGiB,gBAAgB,CAAC,CAAC,CAAC;IAC7BC,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EAClC,IAAIE,SAAS,GAAGxC,QAAQ,CAAC,KAAK,CAAC;IAC7ByC,UAAU,GAAGhD,cAAc,CAAC+C,SAAS,EAAE,CAAC,CAAC;IACzCE,aAAa,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC7BE,gBAAgB,GAAGF,UAAU,CAAC,CAAC,CAAC;;EAElC;EACA,IAAIG,KAAK,GAAG,CAAC,CAACtC,oBAAoB,GAAG4B,WAAW,CAACb,OAAO,CAAC,MAAM,IAAI,IAAIf,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,oBAAoB,CAACuC,IAAI,KAAK,CAAC,CAAC;IAChJC,GAAG,GAAGF,KAAK,CAACE,GAAG;IACfC,cAAc,GAAGpD,wBAAwB,CAACiD,KAAK,EAAE/C,UAAU,CAAC;EAC9D;EACA,IAAImD,gBAAgB,GAAGlD,cAAc,CAAC,CAAC,CAACkB,cAAc,EAAE;MACpDqB,KAAK,EAAErB,cAAc;MACrBW,QAAQ,EAAE,SAASA,QAAQA,CAACsB,GAAG,EAAEC,OAAO,EAAE;QACxChC,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAAC+B,GAAG,EAAEC,OAAO,EAAE7B,OAAO,CAAC;MAC1G;IACF,CAAC,CAAC;IACF8B,gBAAgB,GAAG1D,cAAc,CAACuD,gBAAgB,EAAE,CAAC,CAAC;IACtDI,aAAa,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACnCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;;EAEtC;EACA,IAAIG,UAAU,GAAGtD,QAAQ,CAAC,IAAI,CAAC;IAC7BuD,UAAU,GAAG9D,cAAc,CAAC6D,UAAU,EAAE,CAAC,CAAC;IAC1CE,aAAa,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC7BE,gBAAgB,GAAGF,UAAU,CAAC,CAAC,CAAC;EAClC,IAAIG,kBAAkB,GAAG3D,KAAK,CAAC4D,WAAW,CAAC,UAAUC,EAAE,EAAEC,MAAM,EAAEC,MAAM,EAAE;IACvE,IAAIC,KAAK,GAAG7B,WAAW,CAAC8B,SAAS,CAAC,UAAUC,IAAI,EAAE;MAChD,OAAOA,IAAI,CAACL,EAAE,KAAKA,EAAE;IACvB,CAAC,CAAC;IACFP,cAAc,CAAC,IAAI,CAAC;IACpBI,gBAAgB,CAAC;MACfS,CAAC,EAAEL,MAAM;MACTM,CAAC,EAAEL;IACL,CAAC,CAAC;IACFvB,UAAU,CAACwB,KAAK,GAAG,CAAC,GAAG,CAAC,GAAGA,KAAK,CAAC;IACjCpB,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC,EAAE,CAACT,WAAW,CAAC,CAAC;;EAEjB;EACAnC,KAAK,CAACqE,SAAS,CAAC,YAAY;IAC1B,IAAIhB,aAAa,EAAE;MACjB,IAAI,CAACV,aAAa,EAAE;QAClBH,UAAU,CAAC,CAAC,CAAC;MACf;IACF,CAAC,MAAM;MACLI,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC,EAAE,CAACS,aAAa,CAAC,CAAC;;EAEnB;EACA,IAAIiB,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,IAAI,EAAEC,IAAI,EAAE;IAC3DhC,UAAU,CAAC+B,IAAI,CAAC;IAChB3C,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC2C,IAAI,EAAEC,IAAI,CAAC;EAC1E,CAAC;EACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;IAC7CnB,cAAc,CAAC,KAAK,CAAC;IACrBI,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;;EAED;EACA,IAAIgB,mBAAmB,GAAG1E,KAAK,CAAC2E,OAAO,CAAC,YAAY;IAClD,OAAO;MACLvC,QAAQ,EAAEA,QAAQ;MAClBwC,SAAS,EAAEjB;IACb,CAAC;EACH,CAAC,EAAE,CAACvB,QAAQ,EAAEuB,kBAAkB,CAAC,CAAC;;EAElC;EACA,OAAO,aAAa3D,KAAK,CAAC6E,aAAa,CAAC3E,mBAAmB,CAAC4E,QAAQ,EAAE;IACpExC,KAAK,EAAEoC;EACT,CAAC,EAAEhE,QAAQ,EAAE,aAAaV,KAAK,CAAC6E,aAAa,CAACzE,OAAO,EAAEX,QAAQ,CAAC;IAC9D,aAAa,EAAE,CAAC4D,aAAa;IAC7B9B,OAAO,EAAEA,OAAO;IAChBL,OAAO,EAAEmC,aAAa;IACtB0B,SAAS,EAAEtE,gBAAgB;IAC3BkB,SAAS,EAAEA,SAAS;IACpBqD,OAAO,EAAEP,cAAc;IACvBhB,aAAa,EAAEA,aAAa;IAC5BT,cAAc,EAAEA,cAAc;IAC9BD,GAAG,EAAEA,GAAG;IACRhC,QAAQ,EAAEA,QAAQ;IAClBH,KAAK,EAAEA,KAAK;IACZY,QAAQ,EAAEA,QAAQ;IAClBC,QAAQ,EAAEA,QAAQ;IAClBL,YAAY,EAAEA,YAAY;IAC1BE,OAAO,EAAEA,OAAO;IAChB2D,KAAK,EAAE9C,WAAW,CAAC+C,MAAM;IACzBxD,WAAW,EAAEA,WAAW;IACxBG,WAAW,EAAEA,WAAW;IACxBC,aAAa,EAAEA,aAAa;IAC5BC,WAAW,EAAEA,WAAW;IACxBH,QAAQ,EAAE0C;EACZ,CAAC,EAAEtC,WAAW,CAAC,CAAC,CAAC;AACnB,CAAC;AACD,eAAe3B,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}