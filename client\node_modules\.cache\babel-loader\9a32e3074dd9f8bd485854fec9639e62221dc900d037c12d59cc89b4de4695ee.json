{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nvar _excluded = [\"children\", \"locked\"];\nimport * as React from 'react';\nimport useMemo from \"rc-util/es/hooks/useMemo\";\nimport isEqual from \"rc-util/es/isEqual\";\nexport var MenuContext = /*#__PURE__*/React.createContext(null);\nfunction mergeProps(origin, target) {\n  var clone = _objectSpread({}, origin);\n  Object.keys(target).forEach(function (key) {\n    var value = target[key];\n    if (value !== undefined) {\n      clone[key] = value;\n    }\n  });\n  return clone;\n}\nexport default function InheritableContextProvider(_ref) {\n  var children = _ref.children,\n    locked = _ref.locked,\n    restProps = _objectWithoutProperties(_ref, _excluded);\n  var context = React.useContext(MenuContext);\n  var inheritableContext = useMemo(function () {\n    return mergeProps(context, restProps);\n  }, [context, restProps], function (prev, next) {\n    return !locked && (prev[0] !== next[0] || !isEqual(prev[1], next[1], true));\n  });\n  return /*#__PURE__*/React.createElement(MenuContext.Provider, {\n    value: inheritableContext\n  }, children);\n}", "map": {"version": 3, "names": ["_objectWithoutProperties", "_objectSpread", "_excluded", "React", "useMemo", "isEqual", "MenuContext", "createContext", "mergeProps", "origin", "target", "clone", "Object", "keys", "for<PERSON>ach", "key", "value", "undefined", "InheritableContextProvider", "_ref", "children", "locked", "restProps", "context", "useContext", "inheritable<PERSON><PERSON><PERSON><PERSON>", "prev", "next", "createElement", "Provider"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/node_modules/rc-menu/es/context/MenuContext.js"], "sourcesContent": ["import _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nvar _excluded = [\"children\", \"locked\"];\nimport * as React from 'react';\nimport useMemo from \"rc-util/es/hooks/useMemo\";\nimport isEqual from \"rc-util/es/isEqual\";\nexport var MenuContext = /*#__PURE__*/React.createContext(null);\nfunction mergeProps(origin, target) {\n  var clone = _objectSpread({}, origin);\n  Object.keys(target).forEach(function (key) {\n    var value = target[key];\n    if (value !== undefined) {\n      clone[key] = value;\n    }\n  });\n  return clone;\n}\nexport default function InheritableContextProvider(_ref) {\n  var children = _ref.children,\n    locked = _ref.locked,\n    restProps = _objectWithoutProperties(_ref, _excluded);\n  var context = React.useContext(MenuContext);\n  var inheritableContext = useMemo(function () {\n    return mergeProps(context, restProps);\n  }, [context, restProps], function (prev, next) {\n    return !locked && (prev[0] !== next[0] || !isEqual(prev[1], next[1], true));\n  });\n  return /*#__PURE__*/React.createElement(MenuContext.Provider, {\n    value: inheritableContext\n  }, children);\n}"], "mappings": "AAAA,OAAOA,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,aAAa,MAAM,0CAA0C;AACpE,IAAIC,SAAS,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC;AACtC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,IAAIC,WAAW,GAAG,aAAaH,KAAK,CAACI,aAAa,CAAC,IAAI,CAAC;AAC/D,SAASC,UAAUA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAClC,IAAIC,KAAK,GAAGV,aAAa,CAAC,CAAC,CAAC,EAAEQ,MAAM,CAAC;EACrCG,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC,CAACI,OAAO,CAAC,UAAUC,GAAG,EAAE;IACzC,IAAIC,KAAK,GAAGN,MAAM,CAACK,GAAG,CAAC;IACvB,IAAIC,KAAK,KAAKC,SAAS,EAAE;MACvBN,KAAK,CAACI,GAAG,CAAC,GAAGC,KAAK;IACpB;EACF,CAAC,CAAC;EACF,OAAOL,KAAK;AACd;AACA,eAAe,SAASO,0BAA0BA,CAACC,IAAI,EAAE;EACvD,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IAC1BC,MAAM,GAAGF,IAAI,CAACE,MAAM;IACpBC,SAAS,GAAGtB,wBAAwB,CAACmB,IAAI,EAAEjB,SAAS,CAAC;EACvD,IAAIqB,OAAO,GAAGpB,KAAK,CAACqB,UAAU,CAAClB,WAAW,CAAC;EAC3C,IAAImB,kBAAkB,GAAGrB,OAAO,CAAC,YAAY;IAC3C,OAAOI,UAAU,CAACe,OAAO,EAAED,SAAS,CAAC;EACvC,CAAC,EAAE,CAACC,OAAO,EAAED,SAAS,CAAC,EAAE,UAAUI,IAAI,EAAEC,IAAI,EAAE;IAC7C,OAAO,CAACN,MAAM,KAAKK,IAAI,CAAC,CAAC,CAAC,KAAKC,IAAI,CAAC,CAAC,CAAC,IAAI,CAACtB,OAAO,CAACqB,IAAI,CAAC,CAAC,CAAC,EAAEC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;EAC7E,CAAC,CAAC;EACF,OAAO,aAAaxB,KAAK,CAACyB,aAAa,CAACtB,WAAW,CAACuB,QAAQ,EAAE;IAC5Db,KAAK,EAAES;EACT,CAAC,EAAEL,QAAQ,CAAC;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}